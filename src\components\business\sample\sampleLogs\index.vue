<template>
<div>
  <div class="page-container">
    <el-card class="page-header">
      <h3>{{sampleNum}}</h3>
    </el-card>
    <el-card class="content">
      <div class="content-title">样例日志</div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column prop="foperateDescription" label="日志说明" min-width="240" show-overflow-tooltip></el-table-column>
        <el-table-column prop="foperateTime" label="操作时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="foperatePerson" label="操作人" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="foldStatus" label="前状态" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fnewStatus" label="当前状态" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fisSync" label="补偿同步结果" min-width="120" show-overflow-tooltip>
          <template slot="header">
            <span>补偿同步结果</span>
            <el-tooltip class="item" effect="dark" content="系统会在每日定时自动触发补偿任务，该字段为记录补偿同步结果" placement="top-start">
              <el-icon class="el-icon-question"></el-icon>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <!--           未同步， 成功， 失败-->
            <div>
              <span v-if="scope.row.fisSync === 1" style="color: #409eff">成功</span>
              <span v-else-if="scope.row.fisSync === 2" style="color: #F56C6C">失败</span>
              <span v-else-if="scope.row.fisSync === 0">未同步</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </el-card>
  </div>
</div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {getSampleLogs} from '../../../../api/sample/sample'
export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  mounted () {
    this.$_setTbHeight(74 + 20 + 24 + 42 + 32)
    this.sampleNum = this.$route.query.sampleNum || '111111111'
    this.getData()
  },
  data () {
    return {
      sampleNum: '',
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {res} = await awaitWrap(getSampleLogs({
        page: {
          current: this.currentPage,
          size: this.pageSize
        },
        params: {
          sampleNum: this.sampleNum
        }
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.tableData = []
        this.clearMap()
        this.totalPage = res.data.total || 0
        const rows = res.data.rows || []
        let sampleStatusText = {
          0: '待送样',
          1: '已寄送，送样中',
          2: '已到样',
          3: '暂停检测',
          4: '停止检测',
          5: '检测中',
          6: '停止检测-重送样',
          7: '停止检测-已重送样',
          8: '已发报告',
          9: '检测完成'
        }
        rows.forEach((v, i) => {
          const item = {
            fid: v.fid,
            foldStatus: sampleStatusText[v.foldStatus],
            fnewStatus: sampleStatusText[v.fnewStatus],
            foperateTime: v.foperateTime,
            foperatePerson: v.foperatePerson,
            foperateDescription: v.foperateDescription,
            fsampleNum: v.fsampleNum,
            fisSync: v.fisSync
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  padding: 10px;
  background: #f5f5f5;
  height: 100vh;
  .content {
    margin-top: 10px;
    .content-title {
      color: #409EFF;
      padding: 10px 0;
      width: 80px;
      text-align: center;
      border-bottom: 1px solid #409EFF;
    }
    .table {
      margin-top: 10px;
    }
  }
}

/deep/ .el-card__body, .el-main {
  padding: 10px !important;
}
</style>
