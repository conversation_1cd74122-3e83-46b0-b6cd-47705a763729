<template>
  <div class="page">
    <div class="search-form">
      <el-form ref="form" :model="form" label-width="80px" :inline="true" size="mini">
        <el-form-item label="物流单号">
          <el-input v-model="form.materialNumber" clearable placeholder="请输入" class="selectWidth"></el-input>
        </el-form-item>
        <el-form-item label="物流公司">
          <el-select v-model="form.logisticsCompany" multiple collapse-tags clearable placeholder="请选择" class="selectWidth">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in logisticsCompanyList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样本属性">
          <el-select v-model="form.sampleAttributes" clearable placeholder="请选择" class="selectWidth">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in sampleAttributesList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样本明细">
          <el-input v-model="form.sampleDetails" clearable placeholder="多样本使用逗号分隔" class="selectWidth"></el-input>
        </el-form-item>
        <el-form-item label="建立时间">
          <el-date-picker v-model="form.record" :default-time="['00:00:00', '23:59:59']" clearable type="datetimerange" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                          range-separator="-"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <template v-if="$setAuthority('003008001', 'buttons')">
          <el-button type="primary" size="mini" plain @click="handleQuickEntry">包裹登记</el-button>
        </template>
        <template v-if="$setAuthority('003008002', 'buttons') || true">
          <el-button :loading="exportLoading" type="primary" size="mini" plain @click="handleOpenExport">导出</el-button>
        </template>
        <template v-if="$setAuthority('003008003', 'buttons')">
          <el-button type="primary" size="mini" plain @click="handleStatisticalReport">统计报表</el-button>
        </template>
        <el-button type="primary" size="mini" plain @click="handleSearch">查询</el-button>
        <el-button size="mini" plain @click="handleReset">重置</el-button>
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          ref="table"
          class="expressDeliveryTable" size="mini"
          :height="tbHeight"
          style="width: 100%"
          @select="handleSelect"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column type="selection"></el-table-column>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="物流单号" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <template  v-if="$setAuthority('*********', 'buttons')">
                <el-button type="text" size="mini" @click="handleViewDetail(scope.row)">{{scope.row.materialNumber}}</el-button>
              </template>
              <template v-else>{{scope.row.materialNumber}}</template>
            </template>
          </el-table-column>
          <el-table-column prop="logisticsCompany" label="物流公司" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleAttributes" label="样本属性" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="packageTemperature" label="包裹温度" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="exceptionRemark" label="异常描述" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleDetails" label="样本明细" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="recordTime" label="记录建立时间" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="recordMan" label="记录人" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateMan" label="更新人" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" fixed="right" width="100">
            <template slot-scope="scope">
              <template v-if="$setAuthority('003008004', 'buttons')">
                <el-button type="text" size="mini" @click="handleEdit(scope.row.id)">修改</el-button>
              </template>
              <template v-if="$setAuthority('003008005', 'buttons')">
                <el-button type="text" size="mini" @click="handleDelete(scope.row.id)">删除</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <report-dialog
      :pvisible.sync="reportDialogVisible"
    ></report-dialog>
    <save-dialog
      :pvisible.sync="saveDialogVisible" :pid="saveDialogData.id" :ptype="saveDialogData.type"
      @saveDialogConfirmEvent="handleSaveDialogConfirm"
      @saveDialogCloseEvent="handleSaveDialogClose"
    ></save-dialog>
    <export-dialog
      :pvisible.sync="exportDialogVisible" :select-number="selectedRows.length" :all-number="totalPage"
      @dialogConfirmEvent="handleExport"
      @dialogCloseEvent="handleSaveDialogClose"
    ></export-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import reportDialog from './expressDeliveryManagementReportDialog'
import saveDialog from './expressDeliveryManagementSaveDialog'
import exportDialog from './expressDeliveryManagementExportDialog' // 快递导出弹窗
export default {
  name: 'expressDeliveryManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    reportDialog,
    saveDialog,
    exportDialog
  },
  props: {},
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  watch: {},
  computed: {},
  data () {
    return {
      exportLoading: false,
      selectedRows: [],
      form: {
        materialNumber: '',
        logisticsCompany: '',
        sampleAttributes: '',
        sampleDetails: '',
        record: []
      },
      submitForm: {
        materialNumber: '',
        logisticsCompany: '',
        sampleAttributes: '',
        sampleDetails: '',
        recordStartTime: '',
        recordEndTime: ''
      },
      logisticsCompanyList: [
        {
          value: '顺丰快递',
          label: '顺丰快递'
        },
        {
          value: '中铁快运',
          label: '中铁快运'
        },
        {
          value: '生生物流',
          label: '生生物流'
        },
        {
          value: '城市映急',
          label: '城市映急'
        },
        {
          value: '中集冷云',
          label: '中集冷云'
        },
        {
          value: '48同城',
          label: '48同城'
        },
        {
          value: '闪送',
          label: '闪送'
        },
        {
          value: '自送样',
          label: '自送样'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      sampleAttributesList: [
        {
          value: '科研',
          label: '科研'
        },
        {
          value: '临床',
          label: '临床'
        },
        {
          value: '科研+临床',
          label: '科研+临床'
        },
        {
          value: '测序工厂',
          label: '测序工厂'
        }, {
          value: '药厂',
          label: '药厂'
        }
      ],
      reportDialogVisible: false,
      reportDialogData: {},
      saveDialogVisible: false,
      saveDialogData: {},
      exportDialogVisible: false
    }
  },
  methods: {
    getData () {
      this.$ajax({
        loadingDom: '.expressDeliveryTable',
        url: '/package/get_package_samples',
        data: {
          params: {
            ...this.submitForm
          },
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.tableData = []
          this.selectedRows = []
          let data = result.data
          this.totalPage = data.total || 0
          let item = {}
          data.rows.forEach(v => {
            item = {
              id: v.id,
              materialNumber: v.materialNumber,
              logisticsCompany: v.logisticsCompany,
              sampleAttributes: v.sampleAttributes,
              packageTemperature: v.packageTemperature,
              exceptionRemark: v.exceptionRemark,
              sampleDetails: v.sampleDetails,
              recordTime: v.recordTime,
              recordMan: v.recordMan,
              updateMan: v.updateMan,
              updateTime: v.updateTime
            }
            item.realData = util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSearch () {
      let form = JSON.parse(JSON.stringify(this.form))
      this.submitForm = {
        materialNumber: form.materialNumber.trim(),
        logisticsCompany: form.logisticsCompany.toString(),
        sampleAttributes: form.sampleAttributes,
        sampleDetails: form.sampleDetails.trim(),
        recordStartTime: form.record[0] || '',
        recordEndTime: form.record[1] || ''
      }
      this.getData()
    },
    handleReset () {
      this.currentPage = 1
      this.form = {
        materialNumber: '',
        logisticsCompany: [],
        sampleAttributes: '',
        sampleDetails: '',
        record: []
      }
      this.handleSearch()
    },
    handleQuickEntry () {
      this.saveDialogData = {
        id: '',
        type: 0 // 新增
      }
      this.saveDialogVisible = true
    },
    // 打开快递导出
    handleOpenExport () {
      this.exportDialogVisible = true
    },
    handleExport (isAll) {
      console.log(isAll)
      let data = {}
      let url = ''
      let method = ''
      if (isAll === 0) {
        url = '/package/export_excel'
        method = 'get'
        data = {
          ids: this.selectedRows.map(v => v.id).join(',')
        }
      } else {
        url = '/package/export_excel_all'
        method = 'post'
        data = {
          params: {
            ...this.submitForm
          },
          page: {
            current: 1,
            size: 2147483647
          }
        }
      }
      this.exportLoading = true
      this.$ajax({
        url: url,
        method: method,
        data: data,
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    handleStatisticalReport () {
      this.reportDialogVisible = true
    },
    handleEdit (id) {
      this.saveDialogData = {
        id: id,
        type: 1 // 编辑
      }
      this.saveDialogVisible = true
    },
    handleDelete (id) {
      this.$confirm(`是否移除该数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/package/delete_package_sample',
          method: 'get',
          data: {
            id: id
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getData()
          } else {
            this.$message.error(result.message)
          }
        })
      }).catch(err => {
        console.log(err)
      })
    },
    handleViewDetail (row) {
      this.saveDialogData = {
        id: row.id,
        type: 2 // 2为查看
      }
      this.saveDialogVisible = true
    },
    handleSelectAll (selection) {
      this.selectedRows = []
      selection.forEach((v, i) => {
        this.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = this.selectedRows.findIndex(v => v.id === row.id)
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs.table.toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs.table.toggleRowSelection(row, true)
      }
    },
    handleSaveDialogConfirm () {
      this.saveDialogVisible = false
      this.getData()
    },
    handleSaveDialogClose () {
      this.saveDialogVisible = false
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    width: 100%;
    height: 100%;
    .search{
      height: 100px;
      line-height: 100px;
      /*display: flex;*/
      /*align-items: center;*/
      /*padding: 0 10px;*/
      >>>.el-form-item--mini.el-form-item{
        margin: 9px 0;
      }
      .selectWidth{
        width: 200px;
      }
    }
    .content{
      height: calc(100% - 100px);
      .buttonGroup{
        height: 40px;
        line-height: 40px;
      }
      .table{
        height: calc(100% - 50px);
      }
    }
  }
</style>
