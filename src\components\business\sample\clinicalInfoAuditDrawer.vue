<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :wrapper-closable="false"
      :before-close="handleClose"
      title="操作记录"
      size="1000px"
      @open="handleOpen"
    >
        <!--表格-->
        <el-table
          ref="table"
          :data="tableData"
          class="reservationTable"
          height="calc(100vh - 72px)"
          style="width: 100%">
          <el-table-column prop="operationName" label="操作" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operationPerson" label="操作人" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operationDate" label="日期" min-width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operationRemark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
        </el-table>
    </el-drawer>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'clinicalInfoAuditDrawer',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleBasicId: {
      type: Number,
      required: false
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 打开回调
    handleOpen () {
      this.getData()
    },
    // 获取审核记录
    getData () {
      this.$ajax({
        url: '/sample/basic/get_operation_record',
        method: 'get',
        data: {
          sampleBasicId: this.sampleBasicId
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = []
          res.data.forEach(v => {
            let item = {
              operationName: v.operationName,
              operationPerson: v.operationPerson,
              operationDate: v.operationDate,
              operationRemark: v.operationRemark
            }
            this.tableData.push(item)
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
