<template>
    <div :class="{'has-logo':showLogo}" :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg }">
        <logo v-if="showLogo" :collapse="isCollapse" />
        <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
            <el-menu
              :default-active="activeMenu"
              :collapse="isCollapse"
              router
              :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg"
              :text-color="settings.sideTheme === 'theme-dark' ? variables.menuText : 'rgba(0,0,0,.65)'"
              :unique-opened="true"
              :active-text-color="settings.theme"
              :collapse-transition="false"
              mode="vertical">
              <navigation-menu v-for="(item, index) in menuLists" :pmenu="item" :key="index"/>
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import navigationMenu from './navigationMenu'
import variables from '@/style/variables.scss'
// import constants from 'Constants'

export default {
  components: { navigationMenu, Logo },
  created () {
    // this.menuLists = constants.MENU_LISTS
  },
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebar']),
    activeMenu () {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo () {
      return this.$store.state.settings.sidebarLogo
    },
    menuLists () {
      return this.$store.getters.menuList
    },
    variables () {
      return variables
    },
    isCollapse () {
      return !this.sidebar.opened
    }
  }
}
</script>
