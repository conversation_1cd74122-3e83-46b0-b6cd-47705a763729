<template>
  <div>
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="120px" size="mini"
        style="display: flex; justify-content: space-between" @submit.native.prevent
        @keyup.enter.native.prevent="handleSearch">
        <div style="display: flex; flex-wrap: wrap">
          <!-- <el-form-item label="样本癌种类型" prop="cancerTypeName">
            <el-input
              v-model.trim="form.cancerTypeName"
              clearable
              placeholder="请输入样本癌种类型"
            ></el-input>
          </el-form-item>
          <el-form-item label="癌种简称" prop="cancerShortName">
            <el-input
              v-model.trim="form.cancerShortName"
              clearable
              placeholder="请输入癌种简称"
            ></el-input>
          </el-form-item>
          <el-form-item label="关联产品名称" prop="productName">
            <el-input
              v-model.trim="form.productName"
              clearable
              placeholder="请输入关联产品名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model.trim="form.status" clearable placeholder="请选择状态">
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item> -->
        </div>
      </el-form>
    </div>
    <!--按钮组-->
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('002022001', 'buttons')" type="primary" size="mini" @click="handleAdd">新增</el-button>
      <el-button v-if="$setAuthority('002022003', 'buttons')" type="primary" plain size="mini" @click="handleCoreProbeConfig">核心探针配置表</el-button>
    </div>
    <!--表格-->
    <el-table ref="table" :data="tableData" class="table" border :height="tbHeight" :row-style="handleRowStyle"
      @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll"
      @sort-change="handleSortChange">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="cancerTypeName" label="样本癌种类型" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="cancerShortName" label="癌种简称" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="configTypeText" label="癌种配置类型" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="productName" label="关联产品名称" min-width="280" show-overflow-tooltip></el-table-column>
      <el-table-column prop="statusText" label="状态" min-width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'" size="mini">
            {{ scope.row.statusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="creatorName" label="创建人" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="120" show-overflow-tooltip
        ></el-table-column>
      <el-table-column prop="updateName" label="修改人" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="updateTime" label="修改时间" min-width="120" show-overflow-tooltip
        ></el-table-column>
      <el-table-column label="操作" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="$setAuthority('002022002', 'buttons')" type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :total="totalPage"
      style="background-color: #ffffff; width: 350px" layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange" @current-change="handleCurrentChange">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh" />
      </button>
    </el-pagination>

    <!-- 编辑弹窗 -->
    <sample-cancer-type-edit-dialog :pvisible.sync="editDialogInfo.visible" :title="editDialogInfo.title"
      :row-data="editDialogInfo.rowData" @dialogConfirmEvent="getData" />

    <!-- 核心探针配置弹窗 -->
    <core-probe-config-dialog :pvisible.sync="coreProbeDialogInfo.visible" :title="coreProbeDialogInfo.title"
      :cancer-type-id="coreProbeDialogInfo.cancerTypeId" />
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util, { awaitWrap } from '@/util/util'
import { getSampleCancerTypeList } from '@/api/basicDataManagement/sampleCancerTypeApi'
import SampleCancerTypeEditDialog from './sampleCancerTypeEditDialog'
import coreProbeConfigDialog from './coreProbeConfigDialog.vue'

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    SampleCancerTypeEditDialog,
    coreProbeConfigDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32 + 24, '.search-form')
    this.getData()
  },
  data () {
    return {
      form: {
        cancerTypeName: '',
        cancerShortName: '',
        productName: '',
        status: ''
      },
      submitForm: {},
      sortParams: {
        fcreateTimeOrder: null,
        fupdateTimeOrder: null
      },
      editDialogInfo: {
        visible: false,
        title: '编辑样本癌种类型配置',
        rowData: {}
      },
      coreProbeDialogInfo: {
        visible: false
      }
    }
  },
  methods: {
    async getData () {
      const { res = {} } = await awaitWrap(
        getSampleCancerTypeList({
          pagedRequest: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        })
      )
      if (res.code === this.SUCCESS_CODE) {
        this.selectedRows.clear()
        let data = res.data
        this.totalPage = data.total
        let rows = data.records || []
        this.tableData = []
        rows.forEach((row) => {
          let item = {
            id: row.fid,
            cancerTypeName: row.fsampleCancerType,
            cancerShortName: row.fcancerSortName,
            configTypeText: row.fcancerConfigType ? '专用癌种' : '通用癌种',
            configType: row.fcancerConfigType,
            productName: row.frelateProductName,
            frelateProductCode: row.frelateProductCode,
            status: row.fstate,
            statusText: row.fstate === 0 ? '启用' : '禁用',
            creator: row.fcreator,
            creatorName: row.fcreator,
            createTime: row.fcreateTime,
            updateName: row.fupdator,
            updateTime: row.fupdateTime
          }
          item.realData = util.deepCopy(item)
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    handleSearch () {
      this.submitForm = {
        cancerTypeName: this.form.cancerTypeName,
        cancerShortName: this.form.cancerShortName,
        productName: this.form.productName,
        status: this.form.status
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        cancerTypeName: '',
        cancerShortName: '',
        productName: '',
        status: ''
      }
      this.handleSearch()
    },
    // 排序变化
    handleSortChange (column) {
      if (!column.order) {
        this.sortParams.fupdateTimeOrder = null
        this.sortParams.fcreateTimeOrder = null
      }
      if (column.prop === 'createTime') {
        this.sortParams.fupdateTimeOrder = null
        this.sortParams.fcreateTimeOrder = column.order === 'ascending' ? 1 : 0
      }
      if (column.prop === 'updateTime') {
        this.sortParams.fupdateTimeOrder = column.order === 'ascending' ? 1 : 0
        this.sortParams.fcreateTimeOrder = null
      }
      this.getData()
    },
    handleAdd () {
      this.editDialogInfo.visible = true
      this.editDialogInfo.rowData = null
    },
    // 编辑
    handleEdit (row) {
      this.editDialogInfo.visible = true
      this.editDialogInfo.rowData = row.realData
    },
    handleCoreProbeConfig () {
      this.coreProbeDialogInfo.visible = true
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.operate-btns-group {
  margin-bottom: 10px;
}
</style>
