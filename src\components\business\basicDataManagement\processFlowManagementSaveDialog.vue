<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible" :close-on-click-modal="false"
      :before-close="handleClose" width="80%"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)">
      <div class="content">
        <div class="list">
          <div class="header">
            <span>工序名称</span><span>{{userProcessMap.size}}/{{processList.length}}</span>
          </div>
          <el-scrollbar class="scrollbar" style="height: 60vh;">
            <template v-for="(item, index) in processList">
              <div :key="index" :class="hasUsed(item.stepName) ? 'disableItem' : 'listItem'" @click="handlePush(item)">
                {{item.stepName}}
              </div>
            </template>
          </el-scrollbar>
        </div>
        <div class="table">
          <div class="header">
            <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="mini" class="procedureForm" inline @submit.native.prevent>
              <el-form-item :inline-message="true" label="工序流程名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入工序流程名称" style="width: 220px"></el-input>
              </el-form-item>
              <span>
                <el-form-item :inline-message="true" label="特殊标记">
                  <el-checkbox v-model="fspecialMark" :true-label="1" :false-label="0"></el-checkbox>
                </el-form-item>
              </span>
            </el-form>
          </div>
          <el-table
            :data="tableData" ref="procedureTable" border
            class="procedureTable" size="mini"
            height="60vh">
            <el-table-column label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="stepName" label="工序名称" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="stepTypeName" label="工序分类" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column prop="tatTime" label="TAT时间" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column prop="sendSampleTypeName" label="送样类型" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="sampleTypeName" label="样本类型" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="useSampleTypeName" label="工序可用样本类型" min-width="180" show-overflow-tooltip></el-table-column>
            <el-table-column label="是否继承" width="80">
              <template slot-scope="scope">
                {{scope.row.isInherit === 1 ? '是': '否' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60" fixed="right">
              <template slot-scope="scope">
                <!--<el-button type="text" size="mini" @click="handleEdit(scope.row)">配置</el-button>-->
                <el-button type="text" size="mini" @click="handleDelete(scope.row.stepName)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <process-save-dialog
      :pvisible="processSaveDialogVisible"
      :pdata="processSaveDialogData"
      @processFlowManagementProcessSaveDialogConfirmEvent = handleProcessDialogConfirm
      @processFlowManagementProcessSaveDialogCloseEvent = handleProcessDialogClose
    ></process-save-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import processSaveDialog from './processFlowManagementSaveProcessDialog'
import util from '../../../util/util'
export default {
  name: 'processFlowManagementSaveDialog',
  components: {processSaveDialog},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        if (this.pdata.fid !== null) {
          this.title = '编辑工序流程'
          // this.getData(this.pdata.fid)
        } else {
          this.title = '新增工序流程'
        }
        this.form.id = this.pdata.fid
        this.form.name = this.pdata.procedureName
        this.fspecialMark = this.pdata.fspecialMark
        this.$nextTick(() => {
          this.setSort()
          this.getProcessList()
        })
      } else {
        this.form.id = null
        this.form.name = ''
        this.fspecialMark = this.pdata.fspecialMark
        this.tableData = []
        this.userProcessMap.clear()
      }
    }
  },
  computed: {
    hasUsed () {
      return function (name) {
        return this.userProcessMap.has(name)
      }
    }
  },
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      title: '新增工序流程',
      fspecialMark: 0,
      sortable: null,
      form: {
        name: '',
        id: null
      },
      rules: {
        name: [
          {required: true, message: '请输入工序流程名称', trigger: 'blur'}
        ]
      },
      tableData: [],
      processList: [],
      userProcessMap: new Map(),
      processSaveDialogVisible: false,
      processSaveDialogData: {}
    }
  },
  methods: {
    getData (id) {
      this.$ajax({
        loadingDom: '.procedureTable',
        method: 'get',
        url: '/system/procedure/get_procedure_info',
        data: {
          fid: id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.tableData = []
          let sortTable = []
          let item = {}
          result.data.forEach(v => {
            item = {
              stepName: v.stepName,
              stepType: v.stepType,
              stepTypeName: v.stepTypeName,
              stepCode: v.stepCode,
              tatTime: v.tatTime,
              sendSampleType: v.sendSampleType,
              sendSampleTypeName: v.sendSampleTypeName,
              sampleType: v.sampleType,
              sampleTypeName: v.sampleTypeName,
              useSampleType: v.useSampleType,
              useSampleTypeName: v.useSampleTypeName,
              weights: v.weights,
              isInherit: v.isInherit
            }
            sortTable.push(item)
            this.userProcessMap.set(item.stepName, item)
          })
          // 根据weights权重字段，进行从小到大排序
          this.tableData = sortTable.sort((a, b) => a.weights - b.weights)
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getProcessList () {
      this.$ajax({
        method: 'get',
        url: '/system/procedure/list_step_data',
        data: {
          fid: this.form.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.processList = []
          this.tableData = []
          let sortTable = []
          let item = {}
          result.data.forEach(v => {
            item = {
              stepName: v.stepName,
              stepType: v.stepType,
              stepTypeName: v.stepTypeName,
              stepCode: v.stepCode,
              tatTime: v.tatTime,
              sendSampleType: v.sendSampleType,
              sendSampleTypeName: v.sendSampleTypeName,
              sampleType: v.sampleType,
              sampleTypeName: v.sampleTypeName,
              useSampleType: v.useSampleType,
              useSampleTypeName: v.useSampleTypeName,
              weights: v.weights,
              isInherit: v.isInherit
            }
            util.setDefaultEmptyValueForObject(item)
            if (v.selected === 1) {
              sortTable.push(item)
              this.userProcessMap.set(item.stepName, item)
            }
            this.processList.push(item)
          })
          // 根据weights权重字段，进行从小到大排序
          this.tableData = sortTable.sort((a, b) => a.weights - b.weights)
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handlePush (row) {
      if (!this.userProcessMap.has(row.stepName)) {
        this.tableData.push({
          stepName: row.stepName,
          stepType: row.stepType,
          stepTypeName: row.stepTypeName,
          stepCode: row.stepCode,
          tatTime: row.tatTime,
          sendSampleType: row.sendSampleType,
          sendSampleTypeName: row.sendSampleTypeName,
          sampleType: row.sampleType,
          sampleTypeName: row.sampleTypeName,
          useSampleType: row.useSampleType,
          useSampleTypeName: row.useSampleTypeName,
          weights: row.weights,
          isInherit: row.isInherit
        })
        this.userProcessMap.set(row.stepName, row)
      }
    },
    handleClose () {
      this.$emit('processFlowManagementSaveDialogCloseEvent')
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = []
          this.tableData.forEach((v, i) => {
            params.push({
              stepCode: v.stepCode,
              weights: i + 1
            })
          })
          this.loading = true
          this.$ajax({
            url: '/system/procedure/save_template_info',
            data: {
              fid: this.form.id,
              ftemplateName: this.form.name,
              params: params,
              fspecialMark: this.fspecialMark
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('修改成功')
              this.$emit('processFlowManagementSaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleEdit (row) {
      let data = JSON.parse(JSON.stringify(row))
      data.sendSampleType = data.sendSampleType ? data.sendSampleType.split(',').map(v => Number(v)) : []
      data.sampleType = data.sampleType ? data.sampleType.split(',').map(v => Number(v)) : []
      data.useSampleType = data.useSampleType ? data.useSampleType.split(',').map(v => Number(v)) : []
      this.processSaveDialogData = data
      this.processSaveDialogVisible = true
    },
    handleDelete (name) {
      this.$confirm(`是否删除该工序?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let index = this.tableData.findIndex(item => item.stepName === name)
        if (index > -1) {
          this.tableData.splice(index, 1)
          this.userProcessMap.delete(name)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleProcessDialogConfirm (newRow) {
      this.processSaveDialogVisible = false
      let index = this.tableData.findIndex(v => v.stepName === newRow.stepName)
      if (index > -1) {
        this.tableData.splice(index, 1, newRow)
      }
    },
    handleProcessDialogClose () {
      this.processSaveDialogVisible = false
    },
    setSort () {
      const el = this.$refs.procedureTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          setTimeout(() => {
            let nI = evt.newIndex
            let oI = evt.oldIndex
            let tableData = JSON.parse(JSON.stringify(this.tableData))
            tableData.splice(nI, 0, ...tableData.splice(oI, 1))
            // console.log(tableData)
            this.tableData = []
            this.$nextTick(() => {
              this.tableData.push(...tableData)
            })
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  /*>>>.el-dialog__body{*/
    /*padding: 10px 20px;*/
  /*}*/
  .content{
    display: flex;
    width: 100%;
    .list{
      width: 190px;
      border-right: 2px solid #f2f2f2;
      margin-right: 8px;
      font-size: 12px;
      color: #000000;
      .header{
        background-color: #f2f2f2;
        font-weight: 600;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 35px;
        font-size: 14px;
      }
      .listItem{
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        padding: 0 10px;
        height: 35px;
        border-top: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
      }
      .listItem:hover{
        background-color: #ecf5ff;
      }
      .disableItem{
        display: flex;
        cursor: not-allowed;
        color: #c0c4cc;
        padding: 0 10px;
        height: 35px;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #ebeef5;
        border-bottom: 1px solid #ebeef5;
      }
    }
    .table{
      max-width: calc(100% - 200px);
      flex: 1;
      >>>.el-button{
        padding: 8px 0;
      }
      .header{
        padding: 0 10px;
        font-size: 14px;
        height: 40px;
        display: flex;
        align-items: center;
        .procedureForm >>>.el-form-item{
          margin-bottom: 0;
        }
      }
    }
  }
</style>
