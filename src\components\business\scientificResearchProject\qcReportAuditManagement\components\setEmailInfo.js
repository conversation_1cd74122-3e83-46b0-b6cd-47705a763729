import constants from '../../../../../util/constants'
import util from '../../../../../util/util'
const emailContentConfigs = {
  '000': ({projectName, month, day, sampleNum, normalCase, riskCase}) => {
    return `
    老师您好！
      <div style="text-indent: 2em;">${projectName}，吉因加T7测序服务</div>
      <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例样本，质控结果已出，1/2级：${normalCase}例，3/4级：${riskCase}例。详情见附件。</div>
      <div style="text-indent: 2em;">全部样本<b>已自动流转</b>建库测序，请您知悉。</div>
     <div>祝好！</div>`
  }, // 邮件正文1
  '001': ({projectName, month, day, sampleNum, normalCase, orderCode, riskCase}) => {
    return `
    老师您好！
    <div style="text-indent: 2em;">${projectName}，吉因加T7测序服务</div>
    <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例样本，质控结果已出，1/2级：${normalCase}例，3/4级：${riskCase}例。详情见附件。</div>
    <div style="text-indent: 2em;"><b>1/2级样本已自动流转</b>，3/4级样本请确认是否继续建库测序，并点击<a style="color: #4d4bf1; text-decoration: underline;" href="${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}">
          ${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}</a>，在系统及时处理。</div>
    <div style="text-indent: 2em;">操作指引：</div>
    <img  style="width: 100%; height: auto;" src="https://cdn.geneplus.org.cn/LIMS/guide.png" alt="指引"/></br>
    祝好！`
  }, // 邮件正文2
  '010': ({projectName, month, day, sampleNum, normalCase, orderCode, riskCase}) => {
    return `
    老师您好！
    <div style="text-indent: 2em;">${projectName}，吉因加T7测序服务</div>
    <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例样本，质控结果已出，1/2级：${normalCase}例，3/4级：${riskCase}例。详情见附件。</div>
    <div style="text-indent: 2em;">请确认继续建库测序的样本，并点击<a style="color: #4d4bf1; text-decoration: underline;" href="${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}">
          ${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}</a>，在系统及时处理。</div>
    <div style="text-indent: 2em;">操作指引：</div>
    <img  style="width: 100%; height: auto;" src="https://cdn.geneplus.org.cn/LIMS/guide.png" alt="指引"/></br>
    <div>祝好！</div>`
  }, // 邮件正文3
  '011': ({projectName, month, day, sampleNum, normalCase, orderCode, riskCase}) => {
    return `
    老师您好，
    <div style="text-indent: 2em;">项目名称：${projectName}</div>
    <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例文库质检报告已出，合格：${normalCase}例，风险/不合格：${riskCase}例。详情见附件，请知悉。</div>
    <div style="text-indent: 2em;">全部文库<b>已自动流转</b>测序，请您知悉。</div>

    祝好！
    `
  }, // illuminate、MGI邮件正文1
  '100': ({projectName, month, day, sampleNum, normalCase, orderCode, riskCase}) => {
    return `
    老师您好，
    <div style="text-indent: 2em;">项目名称：${projectName}</div>
    <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例文库质检报告已出，合格：${normalCase}例，风险/不合格：${riskCase}例。详情见附件，请知悉。</div>
    <div style="text-indent: 2em;">合格文库已自动流转测序，风险/不合格文库请确认是否继续测序，并点击<a style="color: #4d4bf1; text-decoration: underline;" href="${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}">
          ${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}</a>，在系统及时处理。</div>
    <div style="text-indent: 2em;">操作指引：</div>
    <img  style="width: 100%; height: auto;" src="https://cdn.geneplus.org.cn/LIMS/guide.png" alt="指引"/></br>

    祝好！
    `
  }, // illuminate、MGI邮件正文2
  '101': ({projectName, month, day, sampleNum, normalCase, orderCode, riskCase}) => {
    return `
    老师您好，
    <div style="text-indent: 2em;">项目名称：${projectName}</div>
    <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例文库质检报告已出，合格：${normalCase}例，风险/不合格：${riskCase}例。详情见附件，请知悉。</div>
    <div style="text-indent: 2em;">请确认继续测序的文库，并点击<a style="color: #4d4bf1; text-decoration: underline;" href="${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}">
          ${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}</a>，在系统及时处理。</div>
    <div style="text-indent: 2em;">操作指引：</div>
    <img  style="width: 100%; height: auto;" src="https://cdn.geneplus.org.cn/LIMS/guide.png" alt="指引"/></br>
    祝好！
  `
  },
  // 单细胞
  '200': ({projectCode, projectName, sampleName}) => {
    return `
      老师您好！
      <div style="text-indent: 2em;">${projectCode}-${projectName}-${util.dateFormatter(new Date(), false, null, '')}，样本${sampleName} 解离报告详见附件，请查收。</div>

        <div style="text-indent: 2em;">祝好！</div>
      `
  },
  '201': ({projectCode, projectName, sampleName, orderCode}) => {
    return `老师您好，
    <div style="text-indent: 2em;">${projectCode}-${projectName}，样本${sampleName}, RNA质检报告详见附件，请查收。
请下方链接点击登录确认该送检批次的所有样本是否继续往下实验。
<a style="color: #4d4bf1; text-decoration: underline;" href="${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}">
          ${constants.IFRAME_URL + '/main/orderSearch?orderCode=' +
    (orderCode ? encodeURIComponent(util.encryptAES(orderCode)) : '')}</a>
     <div style="text-indent: 2em;">操作指引：</div>
    <img  style="width: 100%; height: auto;" src="https://cdn.geneplus.org.cn/LIMS/guide.png" alt="指引"/></br>
    祝好！
`
  },
  // illuminate、MGI邮件正文3
  // 极致废弃
  // '112': ({projectName, month, day, sampleNum, normalCase, orderCode, riskCase}) => {
  //   return `
  //   老师您好，
  //     <div style="text-indent: 2em;">项目名称：${projectName}</div>
  //     <div style="text-indent: 2em;">${month}月${day}日到样${sampleNum}例文库质检报告已出，合格：${normalCase}例，风险/不合格：${riskCase}例。详情见附件，请知悉。</div>
  //     <div style="text-indent: 2em;">全部文库<b>已自动流转</b>测序，请您知悉。</div>
  //
  //   祝好！
  //   `
  // },
  'undefined': () => {

  }
}

/**
 * 设置邮件正文
 * 组织核酸订单
 * 邮件正文1：当自动流转为”是“，且全部合格（到样例数=1/2级例数，3/4级例数为0）时使用该模板 010
 * 邮件正文2：当自动流转为”是“，且存在风险\不合格（3/4级例数不为0）时使用该模板 011
 * 邮件正文3：当自动流转为”否“时使用该模板 000
 * illumina、MGI订单
 * 邮件正文1：当自动流转为”是“，且全部合格（到样例数=合格例数，不合格例数为0）时使用该模板 110
 * 邮件正文2：当自动流转为”是“，且存在风险\不合格（风险/不合格例数不为0）时使用该模板 111
 * 邮件正文3：当自动流转为”否“时使用该模板 100
 * @returns {String} 邮件正文
 * @param emailConfig {Object} 000,001,010,100,101,110
 * @param emailContentInfo 邮件正文参数
 * @param emailConfig.emailType {Number} 邮件类型 0: 组织核酸订单 1:illuminate、MGI订单
 * @param emailConfig.autoFlow {String} 自动流转 0: 否 1: 是
 * @param emailConfig.riskCase {String} 存在风险 0: 全部合格 1: 存在风险\不合格, 2: 极致交付
 */
export function setEmailContent (emailConfig, emailContentInfo) {
  const {emailType = 0, autoFlow = 1, isSingle = false, isRnaQc = false} = emailConfig
  const isRisk = (emailContentInfo.riskCase !== 0 ? '1' : '0')
  let type = emailType + autoFlow + isRisk
  const map = {
    '000': '000', // 核酸 合格自动实验 无风险
    '010': '000', // 核酸 全部自动实验 无风险
    '011': '000', // 核酸 全部自动实验 无风险
    '001': '001', // 核酸 合格自动实验 有风险
    '020': '010', // 核酸 确认后实验 无风险
    '021': '010', // 核酸 确认后实验 有风险
    '100': '011', // illuminate、MGI 确认后实验 有风险
    '111': '011', // illuminate、MGI 确认后实验 有风险
    '110': '011', // illuminate、MGI 确认后实验 有风险
    '101': '100', // illuminate、MGI 确认后实验 有风险
    '120': '101', // illuminate、MGI 确认后实验 有风险
    '121': '101' // illuminate、MGI 确认后实验 有风险
  }
  type = map[type]
  if (isSingle) {
    type = '200'
  }
  if (isRnaQc) {
    type = '201'
  }
  return emailContentConfigs[type](emailContentInfo)
}
