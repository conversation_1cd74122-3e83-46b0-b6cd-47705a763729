<template>
  <el-dialog
    append-to-body
    title="标记数据"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @open="handleOpen">
    <div class="dialog-wrapper">
      <div>
        <el-form
          ref="form"
          :model="form"
          label-width="120px"
          label-suffix=":"
          size="mini">
          <el-form-item label="已选杂交子文库">
            {{ subOrderLength }}
          </el-form-item>
          <el-form-item label="标记类型">
            <el-radio-group v-model="form.type">
              <el-radio :label="1">数据情况</el-radio>
              <el-radio :label="2">备注</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.type === 1" label="数据情况">
            <el-input
              v-model.trim="form.dataSituation"
              :rows="3"
              placeholder="请输入"
              type="textarea"
              size="mini"
              clearable
              maxlength="100"
              autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item v-if="form.type === 2" label="备注">
            <el-input
              v-model.trim="form.remark"
              :rows="3"
              placeholder="请输入"
              type="textarea"
              size="mini"
              clearable
              autocomplete="off"
              maxlength="100"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import {awaitWrap} from '../../../../util/util'
import {saveSign} from '../../../../api/deliveryManagement'

export default {
  name: 'signDataDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    subOrderIds: {
      type: Array,
      default: () => []
    },
    subOrderLength: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false,
      form: {
        type: 1,
        dataSituation: '',
        remark: ''
      }
    }
  },
  methods: {
    handleOpen () {
      this.form = this.$options.data().form
    },
    async handleConfirm () {
      this.loading = true
      const {res} = await awaitWrap(saveSign({
        fdataRemark: this.form.type === 2 ? this.form.remark : '',
        fdataSituation: this.form.type === 1 ? this.form.dataSituation : '',
        fcosDeliverOrderIdList: this.ids,
        fcosDeliverBatchDetailIdList: this.subOrderIds
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.visible = false
        this.$emit('dialogConfirmEvent')
        this.$message.success('标记成功')
      }
      this.loading = false
    }
  }
}
</script>

<style scoped>

</style>
