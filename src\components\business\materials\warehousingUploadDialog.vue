<template>
  <div>
    <el-dialog
      :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose"
      title="入库文件上传" width="40%" @open="handleOpen">
      <el-upload
        ref="upload" :on-success="handleOnSuccess" :on-error="handleOnError" :on-change="handleChange"
        :data="uploadParams"
        :http-request="uploadFile"
        :headers="headers"
        :auto-upload="false"
        :file-list="fileList"
        :before-upload="handleBeforeUpload"
        :action="uploadUrl"
        style="text-align: center;"
        multiple
        drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">
          <el-button :download-loading="downloadLoading" type="text" size="mini" style="padding-left: 20px;" @click="handleClickDownload">文件模板下载</el-button>
        </div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util from '../../../util/util'
export default {
  name: 'warehousingUploadDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    ptype: {
      type: Number,
      default: 0,
      required: true
    }
  },
  mounted () {},
  watch: {},
  computed: {},
  data () {
    return {
      type: '',
      uploadUrl: constants.JS_CONTEXT + '/materials/upload_stock_file',
      loading: false,
      downloadLoading: false,
      fileList: [],
      headers: {
        'user-id': util.decryptBase64(util.getSessionInfo('loginId'))
      },
      uploadParams: {
        sampleBasicId: null,
        picNum: ''
      },
      fileData: new FormData(),
      warehousingSaveDialogVisible: false,
      warehousingSaveDialogData: {}
    }
  },
  methods: {
    handleOpen () {
      this.loading = false
      this.downloadLoading = false
      this.type = this.ptype
    },
    uploadFile (file) {
      this.fileData.append('file', file.file) // append增加数据
    },
    handleConfirm () {
      if (this.fileList.length > 0) {
        let message = ''
        let valid = this.fileList.every(v => {
          if (/\.(xlsx|xls)$/i.test(v.name)) {
            if (v.size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
              message = `文件: ${name} ,大小超过10M，无法上传`
              return false
            }
            return true
          } else {
            message = '只能上传Excel文件'
            return false
          }
        })
        if (!valid) {
          this.$message.error(message)
        } else {
          this.submitUpload()
        }
      } else {
        this.$message.error('请上传文件')
      }
    },
    submitUpload () {
      this.loading = true
      let data = {
        file: this.fileList.map(v => v.raw),
        sampleBasicId: this.uploadParams.sampleBasicId,
        picNum: this.uploadParams.picNum
      }
      this.$ajax({
        url: '/materials/upload_stock_file',
        data: data,
        isFormData: true
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('上传成功')
          console.log(result.data)
          this.$emit('warehousingUploadDialogConfirmEvent', result.data)
        } else {
          this.$message({
            type: 'error',
            duration: 0,
            message: result.message
          })
        }
        this.$refs.upload.clearFiles()
      }).finally(() => {
        this.loading = false
      })
    },
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data
        let fileAbsolutePaths = data.fileAbsolutePath ? data.fileAbsolutePath.split(',') : []
        let paths = data.path ? data.path.split(',') : []
        let groups = data.group ? data.group.split(',') : []
        let fileNames = data.fileNames ? data.fileNames.split(',') : []
        let tableData = []
        let item = {}
        fileAbsolutePaths.forEach((v, i) => {
          item = {
            path: paths[i],
            group: groups[i],
            fileName: fileNames[i],
            fileAbsolutePath: fileAbsolutePaths[i]
          }
          tableData.push(item)
        })
        this.$emit('warehousingUploadDialogConfirmEvent', tableData)
      } else {
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    handleOnError () {
      this.loading = false
    },
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(png|jpg|jpge|zip|rar)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过10M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传图片或压缩文件')
        return false
      }
    },
    handleChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
      // let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)
      // if (existFile) {
      //   this.$message.error('当前文件已经存在!')
      //   fileList.pop()
      // }
      this.fileList = fileList
    },
    handleClickDownload () {
      this.downloadLoading = true
      this.$ajax({
        url: '/materials/download_template',
        data: {},
        responseType: 'blob'
      }).then(result => {
        const {data, headers} = result
        let fileName = headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1')
        // let fileName = '收款单信息.xlsx'
        let blob = new Blob([data], {type: headers['content-type']})
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) // 创建下载的链接
        downloadElement.href = href
        downloadElement.download = decodeURI(fileName) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href) // 释放blob对象
      }).catch(err => {
        console.log(err.response)
        this.$message.error(err.response.data.message)
      }).finally(() => {
        this.downloadLoading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
