<template>
  <el-dialog
    title="信息变更"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="90vw"
    @open="handleOpen">
    <el-form v-if="type === '2'" :model="form" label-suffix=":" label-width="70px" inline>
      <div class="flex-wrapper">
        <el-form-item label="操作人">
          <el-select
            v-model.trim="form.operator"
            placeholder="请选择"
            size="mini"
            style="width: 100%;"/>
        </el-form-item>
        <el-form-item label="复核人">
          <el-select
            v-model.trim="form.operator"
            placeholder="请选择"
            size="mini"
            style="width: 100%;"/>
        </el-form-item>
      </div>
    </el-form>
    <vxe-table
      border
      resizable
      show-overflow
      :data="tableData"
      :edit-config="{trigger: 'click', mode: 'cell'}">
      <vxe-column field="name" title="项目名称" :edit-render="{autofocus: '.vxe-input--inner'}"></vxe-column>
      <vxe-column field="role" title="到样时间">
      </vxe-column>
      <vxe-column field="sex" title="原始样本编号"></vxe-column>
      <vxe-column field="sex2" title="吉因加编号"></vxe-column>
      <vxe-column field="num6" title="文库修饰类型" :edit-render="{}">
      </vxe-column>
      <vxe-column field="date12" title="存储位置" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="浓度(ng/ul)" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="体积(ul)" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="平均片段大小" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="文库总量(ng)" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="质检结果" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="备注" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="修改原因" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">提 交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  name: 'infoChangeDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: {
      type: String,
      default: '1' // 1: 信息变更, 2: 回填结果
    }
  },
  data () {
    return {
      form: {},
      tableData: []
    }
  }
}
</script>

<style scoped>

</style>
