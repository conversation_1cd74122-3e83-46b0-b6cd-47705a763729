<template>
  <div v-if="visible">
    <h3 v-if="imgList[index] && imgList[index][fieldKey]" class="fixed-title">{{imgList[index][fieldKey]}}</h3>
    <el-image-viewer
      :initial-index="index"
      :on-close="onClose"
      :on-switch="onSwitch"
      :url-list="urls"
    />
  </div>
</template>

<script>

// import xx form 'xxx'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'imageViewer',
  components: {
    ElImageViewer
  },
  props: {
    pvisible: Boolean,
    currentIndex: {
      type: Number,
      default: 0
    },
    fieldKey: { // 展示的字段名，默认name
      type: String,
      default: 'name'
    },
    imgList: Array // {name, url}
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
    },
    visible (newVal) {
      this.$emit('update:pvisible', newVal)
    },
    currentIndex (index) {
      this.index = index
    }
  },
  computed: {
    // 所有的地址
    urls () {
      return this.imgList.map(v => v.url)
    }
  },
  data () {
    return {
      visible: this.pvisible,
      index: this.currentIndex
    }
  },
  methods: {
    onClose () {
      this.visible = false
    },
    onSwitch (index) {
      this.index = index
    }
  }
}
</script>

<style scoped>
.fixed-title{
  position: fixed;
  left: 20px;
  top: 20px;
  color: #fff;
  z-index: 10000;
  max-width: 200px;
  border-radius: 5px;
  padding: 5px 10px;
  background: rgba(80, 82, 86, .6);
  word-break: break-all;
}
</style>
