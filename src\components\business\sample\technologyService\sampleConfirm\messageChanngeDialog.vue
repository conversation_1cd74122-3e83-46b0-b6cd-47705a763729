<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="样本信息修改"
      width="600px"
      @open="handleOpen">
      <div>
        <el-form label-width="110px" inline size="mini">
          <el-row :gutter="10">
<!--            <el-col :span="13">-->
<!--              <span>到样时间：</span>-->
<!--              <el-date-picker-->
<!--                v-model="arriveTime"-->
<!--                type="datetime"-->
<!--                size="mini"-->
<!--                placeholder="选择日期"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                style="width: 65%">-->
<!--              </el-date-picker>-->
<!--            </el-col>-->
            <el-col :span="24">
              <el-form-item label="管材类型：">
                <el-select v-model="tubularProductType" size="mini" style="width: 400px" clearable>
                  <el-option label="EDTA管" value="EDTA管"></el-option>
                  <el-option label="streck管" value="streck管"></el-option>
                  <el-option label="EP管" value="EP管"></el-option>
                  <el-option label="袋装" value="袋装"></el-option>
                  <el-option label="八联管" value="八联管"></el-option>
                  <el-option label="96孔板" value="96孔板"></el-option>
                  <el-option label="PCR管" value="PCR管"></el-option>
                  <el-option label="其他" value="其他"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
<!--            <el-col :span="10">-->
<!--              <span>管材类型：</span>-->
<!--              <el-input v-model="tubularProductType" size="mini" style="width: 60%" autocomplete="off"></el-input>-->
<!--            </el-col>-->
            <el-col :span="24" style="margin-top: 20px">
              <el-form-item label="备注信息：">
                <el-input
                  v-model.trim="remarks"
                  :rows="3"
                  type="textarea"
                  maxlength="150"
                  show-word-limit
                  size="mini"
                  style="width: 400px"
                  autocomplete="off"
                  placeholder="请填写备注信息"></el-input>
<!--                <el-input v-model.trim="remarks" type="teatarea" size="mini" placeholder="请填写备注信息" style="width: 300px" autocomplete="off" maxlength="150"></el-input>-->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'
// import util from '../../../../../util/util'
export default {
  name: 'messageChangeDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleConfirmId: Number, // id
    storageTime: String, // 到样时间
    ftubeType: String, // 管材类型
    remark: String // 备注信息
  },
  data () {
    return {
      loading: false,
      arriveTime: '', // 到样时间
      tubularProductType: '', // 管材类型
      remarks: '' // 备注信息
    }
  },
  methods: {
    handleOpen () {
      this.arriveTime = this.storageTime
      this.tubularProductType = this.ftubeType
      this.remarks = this.remark
    },
    // 点击确认
    handleConfirm () {
      this.loading = true
      this.$ajax({
        url: '/sample/confirm/modify_sample_confirm',
        data: {
          sampleConfirmId: this.sampleConfirmId,
          storageTime: this.arriveTime,
          ftubeType: this.tubularProductType,
          remark: this.remarks
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('messageChangeDialogConfirmEvent')
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
