<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :modal-append-to-body='false'
      :before-close="handleClose"
      width="800px"
      append-to-body
      @open="handleOpen">
      <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="mini"
      label-position="right"
      label-width="120px">
      <el-form-item label="医院机构名称：" prop="customerId">
        <el-select v-model.trim="form.customerId"
                   filterable
                   clearable
                   placeholder="请选择"
                   style="width: 406px"
                   @change="handleHospitalChange">
          <el-option
            :key="item.customerId"
            :label="item.name"
            :value="item.customerId"
            v-for="item in hospitalLists">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="医生：" prop="doctorId">
        <el-select v-model.trim="form.doctorId" clearable placeholder="请选择">
          <el-option
            :key="item.fid"
            :label="item.fname"
            :value="item.fid"
            v-for="item in doctors">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称：" prop="projectId">
        <el-select v-model.trim="form.projectId" filterable clearable placeholder="请选择" style="width: 406px">
          <el-option
            :key="item.projectId"
            :label="item.projectName"
            :value="item.projectId"
            v-for="item in projects">
              <div class="hidden-item">{{item.projectName}}</div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否入排：" prop="isEnrollNum">
        <el-select v-model.trim="form.isEnrollNum" clearable placeholder="请选择">
          <el-option
            :key="item"
            :label="item"
            :value="item"
            v-for="item in ['是', '否']">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
      <div slot="footer"  class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'productAddCustomerProjectDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    title: String,
    productId: Number,
    info: Object
  },
  data () {
    return {
      submitBtnLoading: false,
      projects: [], // 项目列表
      doctors: [], // 医生列表
      hospitalLists: [],
      customerList: [],
      form: {
        fid: '', // 客户产品配置fid
        productId: this.productId, // 产品id
        customerId: '', // 医院id
        doctorId: '', // 医生id
        projectId: '', // 关联项目id
        isEnrollNum: '' // 是否入排
      },
      submitForm: {},
      rules: {
        customerId: [
          { required: true, message: '请选择医院', trigger: ['change', 'blur'] }
        ],
        projectId: [
          { required: true, message: '请选择关联项目', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  methods: {
    // 打开弹窗
    handleOpen () {
      this.form.productId = this.productId
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.form.fid = null
        if (this.info.fid !== 0) {
          let info = this.info
          this.form = {
            fid: info.fid, // 客户产品配置fid
            productId: this.productId, // 产品id
            customerId: info.fcustomerId, // 医院id
            doctorId: info.fdoctorId, // 医生id
            projectId: info.fprojectId, // 关联项目id
            isEnrollNum: info.fisEnroll // 是否入排
          }
        }
        this.getAllDoctor()
        this.getAllProject()
        this.getHospitalLists()
      })
    },
    // 获取所有项目
    getAllProject () {
      this.$ajax({
        url: '/system/product/get_all_project',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.projects = []
          result.data.forEach(v => {
            let item = {
              projectId: v.projectId, // 项目id
              projectName: v.projectName // 项目名称
            }
            this.projects.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    handleHospitalChange () {
      this.form.doctorId = ''
      this.getAllDoctor()
    },
    // 获取所有医生列表
    getAllDoctor () {
      if (this.form.customerId && this.form.customerId !== '') {
        this.$ajax({
          url: '/system/product/get_doctor_by_customerCode',
          method: 'get',
          data: {
            customerId: this.form.customerId
          }
        }).then((result) => {
          if (result.code === this.SUCCESS_CODE) {
            this.doctors = []
            result.data = result.data || []
            result.data.forEach(v => {
              let item = {
                fid: v.fid, // 主键id
                fcode: v.fcode, // 医生id
                fname: v.fname // 医生名
              }
              this.doctors.push(item)
            })
          } else {
            this.$message.error(result.message)
          }
        }).catch((e) => {
          console.log(e)
        })
      } else {
        this.form.doctorId = ''
        this.doctors = []
      }
    },
    // 获取所有医院列表
    getHospitalLists () {
      this.$ajax({
        url: '/system/product/get_all_hospital',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.hospitalLists = []
          result.data = result.data || []
          result.data.forEach(v => {
            let item = {
              customerId: v.customerId, // 主键id
              customerCode: v.customerCode, // 医院编号
              name: v.name // 医院名
            }
            this.hospitalLists.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 确认提交
    handleDialogConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitForm = JSON.parse(JSON.stringify(this.form))
          this.$ajax({
            url: '/system/product/save_product_custom_config',
            method: 'post',
            data: this.submitForm
          }).then((result) => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('客户项目配置成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).catch((e) => {
            console.log(e)
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.hidden-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 600px;
}
</style>
