<template>
  <div>
    <div class="card-wrapper">

    <el-table
      :data="tableData"
      ref="table"
      class="table"
      size="mini"
      border
      height="calc(100vh - 40px - 110px - 32px - 20px - 42px - 40px - 20px)"
      style="width: 100%;"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column label="QC结果" prop="resultTitle" min-width="120px"></el-table-column>
        <el-table-column label="Case" prop="caseResult" min-width="120px"></el-table-column>
        <el-table-column label="Control" prop="controlResult" min-width="120px"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

export default {
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_qc_result_list',
        data: {
          analysisRsId: this.analysisRsId,
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.tableData = []
        rows.forEach(v => {
          let item = {
            resultTitle: v.resultTitle,
            caseResult: v.caseResult,
            controlResult: v.controlResult
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
