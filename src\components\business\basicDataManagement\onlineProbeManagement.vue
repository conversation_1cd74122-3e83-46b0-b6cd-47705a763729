<template>
  <div style="height: 100%;width: 100%;">
    <div ref="search" class="search-form">
      <!--    搜索-->
      <el-form :model="form" inline @keyup.enter.native="handleSearch">
        <div>
          <el-form-item label="探针名称">
            <el-input v-model="form.name" type="textarea" size="mini" class="input-width"
                      placeholder="请输入探针名称用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="Patient ID">
            <el-input v-model="form.patientId" type="textarea" size="mini" class="input-width"
                      placeholder="请输入Patient ID用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="样例编号">
            <el-input v-model="form.sampleNum" type="textarea" size="mini" class="input-width"
                      placeholder="请输入样例编号用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="原始编号">
            <el-input v-model.trim="form.code" size="mini" class="form-width" placeholder="请输入" clearable @keyup.enter.native="handleSearch"></el-input>
          </el-form-item>
          <el-form-item label="快捷查询">
            <el-select v-model.trim="form.quickly" size="mini" class="form-width" placeholder="请输入" clearable @keyup.enter.native="handleSearch">
              <el-option label="探针有储位信息且探针内部环节不为已签收" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="探针名称">
          <el-input v-model="form.name" type="textarea" size="mini" class="form-long-width"
                    placeholder="请输入探针名称用逗号分隔"></el-input>
        </el-form-item>
        <el-form-item label="Patient ID">
          <el-input v-model="form.patientId" type="textarea" size="mini" class="form-long-width"
                    placeholder="请输入Patient ID用逗号分隔"></el-input>
        </el-form-item>
        <el-form-item label="样例编号">
          <el-input v-model="form.sampleNum" type="textarea" size="mini" class="form-long-width"
                    placeholder="请输入样例编号用逗号分隔"></el-input>
        </el-form-item>
        <el-form-item label="原始编号">
          <el-input v-model.trim="form.code" size="mini" class="form-width" clearable placeholder="请输入"  @keyup.enter.native="handleSearch"></el-input>
        </el-form-item>
        <el-form-item label="快捷查询">
          <el-select v-model.trim="form.quickly" size="mini" class="form-width"  placeholder="请输入" clearable @keyup.enter.native="handleSearch">
            <el-option label="探针有储位信息且探针内部环节不为已签收" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="探针状态">
          <el-select v-model.trim="form.status" multiple size="mini" clearable class="form-width" placeholder="请选择">
            <el-option :key="index"
                       :value="index"
                       :label="item.name"
                       v-for="(item, index) in statusList"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="探针标签">
          <el-select v-model.trim="form.tag" multiple size="mini" clearable class="form-width" placeholder="请选择">
            <el-option :key="index"
                       :value="item.value"
                       :label="item.label"
                       v-for="(item, index) in tagList"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="探针订购时间">
          <el-date-picker v-model.trim="form.time"
                          type="daterange"
                          class="form-long-width"
                          value-format="yyyy-MM-dd"
                          size="mini"
                          clearable
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>

    <!--    操作按钮-->
    <div class="operate-btns-group">
      <!--        <el-button type="primary" size="mini" @click="handleConnectionSample">关联样本</el-button>-->
      <el-dropdown v-if="$setAuthority('002016001', 'buttons')" style="margin: 0 10px;" @command="handleCommand">
        <el-button type="primary" size="mini">
          基础数据配置<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0">癌种简称管理</el-dropdown-item>
          <el-dropdown-item :command="1">核心探针管理</el-dropdown-item>
          <el-dropdown-item :command="2">术后样本关联</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-if="$setAuthority('002016002', 'buttons')" plain type="primary" size="mini"
                 @click="handleExportProbe">探针文件上传
      </el-button>
      <el-dropdown style="margin: 0 10px;" @command="handleProbeLocationCommand">
        <el-button v-if="$setAuthority('002016003', 'buttons') || $setAuthority('002016004', 'buttons')" plain
                   type="primary" size="mini">
          探针储位<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0" v-if="$setAuthority('002016003', 'buttons')">下载</el-dropdown-item>
          <el-dropdown-item :command="1" v-if="$setAuthority('002016004', 'buttons')">上传</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-if="$setAuthority('002016007', 'buttons')" type="primary" plain size="mini"
                 @click="handleProbeOrderSell">探针订购
      </el-button>
      <el-button v-if="$setAuthority('002016008', 'buttons')" type="primary" plain size="mini"
                 @click="handleRepeatProbeOrder">探针重订购
      </el-button>
      <el-button v-if="$setAuthority('002016005', 'buttons')" type="primary" plain size="mini"
                 @click="handleProbeArrival">探针到货确认
      </el-button>
      <el-button v-if="$setAuthority('002016006', 'buttons')" type="primary" plain size="mini" @click="handleDownload">
        探针序列下载
      </el-button>
      <el-button v-if="$setAuthority('002016010', 'buttons')" type="primary" plain size="mini"
                 @click="handleProbeAbandon">探针弃用
      </el-button>
      <template v-if="$setAuthority('002016009', 'buttons')">
        <el-button v-if="downloadingExcelLoading" type="primary" plain size="mini" disabled><i
          class="el-icon-loading"></i> 正在导出
        </el-button>
        <el-dropdown v-else @command="handleExport" style="margin: 0 10px;">
          <el-button type="primary" size="mini" plain>导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
            <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <!--    表格数据展示-->
    <el-table
      :data="tableData"
      ref="table"
      :height="tbHeight"
      border
      class="table"
      size="mini"
      @select="handleSelectTable"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column type="selection" fixed="left"></el-table-column>
      <el-table-column type="index" label="序号" width="70"></el-table-column>
      <el-table-column prop="tag" min-width="140" label="探针标签" show-overflow-tooltip>
        <template slot-scope="scope">
          <div style="display: flex">
            <el-tag v-if="scope.row.realData.probeTag" size="mini">院内</el-tag>
            <el-tag v-if="scope.row.realData.isDeprecatedProbe" type="danger" size="mini">弃用</el-tag>
            <i v-if="scope.row.tag && scope.row.tag !== '-'" style="color: red; font-size: 16px;"
               class="el-icon-warning"></i>
            <div v-if="isNullTag(scope.row)">-</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="patientId" min-width="180" label="Patient ID" show-overflow-tooltip></el-table-column>
      <el-table-column prop="probeStatusText" min-width="180" label="探针内部环节" show-overflow-tooltip></el-table-column>
      <el-table-column prop="code" min-width="100" label="原始编号" show-overflow-tooltip></el-table-column>
      <el-table-column prop="cancerName" min-width="100" label="解读匹配癌种" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sampleCancerType" min-width="100" label="样本癌种类型"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="status" min-width="100" label="探针状态" show-overflow-tooltip></el-table-column>
      <el-table-column prop="name" min-width="180" label="个性化探针名称" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button type="text" @click="handleToProbeKanban(scope.row.name, scope.row.id)">{{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="coreName" min-width="100" label="核心探针名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="hybirdProbeName" min-width="180" label="混合探针名称"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="probeOrderTime" min-width="100" label="探针订购时间"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="tissueSampleCode" min-width="100" label="组织样例编号"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="bloodSampleCode" min-width="100" label="血液样例编号"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="personalSite" min-width="120" label="实际Personal位点"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="fprobeLocation1" min-width="180" label="个性化探针储位-1"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="fprobeLocation2" min-width="180" label="个性化探针储位-2"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="fpersonalProbeReactionNum" min-width="180" label="个性化探针反应总数（rxn）"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="fhybirdProbeLocation" min-width="180" label="混合探针储位"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="fhybirdProbeReactionNum" min-width="180" label="混合探针反应数（rxn）"
                       show-overflow-tooltip></el-table-column>
      <el-table-column prop="probeNum" min-width="100" label="探针数量" show-overflow-tooltip></el-table-column>
      <el-table-column prop="tillingType" min-width="100" label="Tiling类型" show-overflow-tooltip></el-table-column>
      <el-table-column prop="panelSize" min-width="100" label="Panel Size" show-overflow-tooltip></el-table-column>
      <el-table-column prop="reason" min-width="100" label="重订购原因" show-overflow-tooltip></el-table-column>
      <el-table-column prop="reasonDetail" min-width="100" label="具体原因描述" show-overflow-tooltip></el-table-column>
      <el-table-column prop="probeSupplier" min-width="100" label="探针供应商" show-overflow-tooltip></el-table-column>
      <el-table-column prop="orderNumber" min-width="100" label="订单编号" show-overflow-tooltip></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :total="totalPage"
      :current-page.sync="currentPage"
      style="background-color: #ffffff;width: 350px;"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh"/>
      </button>
    </el-pagination>
    <!--    术后样本关联-->
    <sample-link-dialog
      :pvisible.sync="sampleLinkDataInfo.visible"
      :name="sampleLinkDataInfo.name"
    ></sample-link-dialog>
    <!--    关联样本-->
    <link-sample-dialog :pvisible.sync="linkSampleDataInfo.visible" :id="linkSampleDataInfo.id"></link-sample-dialog>
    <!--    核心探针管理-->
    <cancer-name-list-dialog :pvisible.sync="cancerEditNameDataInfo.visible"/>
    <!--    癌种简称管理-->
    <core-probe-list-dialog :pvisible.sync="coreProbeDataInfo.visible"/>
    <!--    核心探针导入-->
    <export-probe-dialog
      :pvisible.sync="exportProbeDataInfo.visible"
      :id="exportProbeDataInfo.id"
      :core-name="exportProbeDataInfo.coreName"
      :name="exportProbeDataInfo.name"
      :cancer-name="exportProbeDataInfo.cancerName"
      @dialogConfirmEvent="getData"/>
    <!--     探针储位上传-->
    <upload-probe-local-dialog
      :pvisible.sync="probeUploadDataInfo.visible"
      @dialogConfirmEvent="getData"/>
    <!--    探针订购-->
    <probe-order-sell-dialog
      :pvisible.sync="probeOrderSellVisible"
      :is-repeat="isRepeat"
      :form-data="formData"
      @dialogConfirmEvent="getData"/>

      <probe-cancel-dialog
      :pvisible.sync="probeCancelDataInfoVisible"
      :ids="ids"
      :rows="rows"
      @dialogConfirmEvent="getData"/>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import linkSampleDialog from './linkSampleDialog'
import cancerNameListDialog from './cancerNameListDialog'
import coreProbeListDialog from './coreProbeListDialog'
import exportProbeDialog from './exportProbeDialog'
import UploadProbeLocalDialog from './uploadProbeLocalDialog'
import util, {setGroupData} from '../../../util/util'
import probeOrderSellDialog from './probeOrderSellDialog'
import sampleLinkDialog from './sampleLinkDialog'
import probeCancelDialog from './probeCancelDialog.vue'

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    sampleLinkDialog, // 术后样本关联
    probeOrderSellDialog,
    UploadProbeLocalDialog, // 探针储位上传
    exportProbeDialog, // 核心探针导入
    cancerNameListDialog, // 癌种简称管理
    linkSampleDialog, // 关联样本
    coreProbeListDialog, // 核心探针管理
    probeCancelDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      form: {
        code: '',
        name: '',
        status: '',
        time: '',
        tag: '',
        patientId: '',
        sampleNum: '',
        quickly: ''
      },
      formData: {},
      searchHeight: '',
      downloadingExcelLoading: false, // 导出loading
      isRepeat: false,
      tableData: [],
      probeProcess: {
        // 0：待设计 1：设计中 2：待合成 3：合成中 4：暂停中 5：待发货 6：运输中 7：待签收 8：已签收 9：已作废 10:已订购
        0: '待设计',
        1: '设计中',
        2: '待合成',
        3: '合成中',
        4: '暂停中',
        5: '待发货',
        6: '运输中',
        7: '待签收',
        8: '已签收',
        9: '已作废',
        10: '已订购'
      },
      tagList: [
        // 弃用、院内、-
        {
          label: '弃用',
          value: '弃用'
        },
        {
          label: '院内',
          value: '院内'
        },
        {
          label: '-',
          value: '-'
        }
      ],
      statusList: [
        {
          name: '探针订购',
          value: 'inSubscribe'
        },
        {
          name: '探针设计',
          value: 'inDesign'
        },
        {
          name: '探针生产',
          value: 'inProduction'
        },
        {
          name: '探针配置',
          value: 'inConfig'
        },
        {
          name: '探针使用',
          value: 'inUse'
        }
      ],
      selectedRows: new Map(),
      probeOrderSellVisible: false,
      sampleLinkDataInfo: {
        visible: false
      },
      linkSampleDataInfo: {
        id: '',
        visible: false
      },
      cancerEditNameDataInfo: {
        visible: false
      },
      coreProbeDataInfo: {
        visible: false
      },
      probeUploadDataInfo: {
        visible: false
      },
      probeCancelDataInfoVisible: false,
      ids: '',
      rows: [],
      exportProbeDataInfo: {
        visible: false,
        id: null,
        coreName: '',
        cancerName: ''
      }
    }
  },
  methods: {
    setParams () {
      this.submitForm.name = setGroupData(this.submitForm.name, ',', true)
      this.submitForm.patientId = setGroupData(this.submitForm.patientId, ',', true)
      this.submitForm.sampleNum = setGroupData(this.submitForm.sampleNum, ',', true)
      this.submitForm.time = this.submitForm.time || []
      this.submitForm.status = this.submitForm.status || []
      return {
        originNum: this.submitForm.code,
        probeName: this.submitForm.name,
        patientId: this.submitForm.patientId,
        sampleNum: this.submitForm.sampleNum,
        isFastQuery: this.submitForm.quickly,
        probeStep: this.submitForm.status.join(','),
        probeSign: this.submitForm.tag && this.submitForm.tag.join(','),
        beginOrderTime: this.submitForm.time[0],
        endOrderTime: this.submitForm.time[1]
      }
    },
    // 探针管理数据列表
    getData (quickly) {
      const params = this.setParams()
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_list',
        data: {
          ...params,
          page: this.currentPage,
          rows: this.pageSize
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          let data = result.data || {}
          this.totalPage = data.total
          let rows = data.records || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              tag: v.fremain && v.fremain < 5,
              probeTag: v.fprobeTag,
              patientId: v.fpatientId,
              code: v.foriginNum,
              cancerName: v.fcancerName,
              status: v.fprobeStep || v.fprobeStep === 0 ? this.statusList[v.fprobeStep].name : '',
              name: v.fprobeName,
              coreName: v.fcoreProbeName,
              hybirdProbeName: v.fhybirdProbeName, // 混合探针名
              personalSite: v.fpersonalSite, // fprobeOrderTime
              probeOrderTime: v.fsubscribeTime, // 探针订购时间
              tissueSampleCode: v.ftissueSampleCode, // 组织编号
              bloodSampleCode: v.fbloodSampleCode, // 血液编号
              probeLocation: v.fprobeLocation, // 探针储位
              lockTissueNum: v.flockTissueNum,
              probeNum: v.fprobeNum,
              tillingType: v.ftillingType,
              probeStatus: v.fprobeStatus,
              probeStatusText: this.probeProcess[v.fprobeStatus],
              sampleCancerType: v.fsampleCancerType,
              panelSize: v.fpanelSize,
              probeSupplier: v.fprobeSupplier,
              fprobeLocation1: v.fprobeLocation1,
              fprobeLocation2: v.fprobeLocation2,
              fpersonalProbeReactionNum: v.fpersonalProbeReactionNum,
              fhybirdProbeLocation: v.fhybirdProbeLocation,
              fhybirdProbeReactionNum: v.fhybirdProbeReactionNum,
              orderNumber: v.forderNumber,
              isDeprecatedProbe: v.fisDeprecatedProbe,
              reason: v.freorderReason,
              reasonDetail: v.ffailureReorderReason
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    isNullTag (row) {
      return !row.realData.tag && !row.realData.probeTag && !row.realData.isDeprecatedProbe
    },
    // 导出
    handleExport (command) {
      // 没有选择查询项
      if (command === 2 && this.selectedRows.size === 0) {
        this.$message.error('请选择需要导出的数据')
        return
      }
      const params = command === 1 ? this.setParams() : {ids: [...this.selectedRows.keys()].join(',')}
      if (params) {
        this.downloadExcel(params, command)
      }
    },
    // 导出Excel
    downloadExcel (params, command) {
      this.downloadingExcelLoading = true
      this.$ajax({
        url: command === 1 ? '/system/probe/export_all_probe_list' : '/system/probe/export_checked_probe_list',
        data: params,
        method: command === 1 ? 'post' : 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadingExcelLoading = false
      })
    },
    // 探针弃用
    async handleProbeAbandon () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      const rows = [...this.selectedRows.values()]
      const hasAbandon = rows.some(v => v.isDeprecatedProbe)
      // 若探针已被弃用，则提示“请选择未弃用的探针进行操作”
      if (hasAbandon) {
        await this.$confirm('请选择未弃用的探针进行操作', '探针弃用提醒', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
        return
      }
      this.ids = rows.map(v => v.id).join(',')
      this.probeCancelDataInfoVisible = true
      this.rows = rows
    },
    async handleRepeatProbeOrder () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      const row = [...this.selectedRows.values()][0]
      // 判断探针是否弃用
      if (row.isDeprecatedProbe !== 1) {
        await this.$confirm('仅支持弃用探针重订购', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
        return
      }
      this.formData = {
        // 解读匹配癌种
        readCancerSpecies: row.realData.cancerName && row.realData.cancerName.split(','),
        // 样本类型
        sampleCancerType: row.realData.sampleCancerType,
        // 核心探针名称
        coreProbeName: row.realData.coreName,
        // 探针标签
        probeTag: row.realData.probeTag || 0,
        postoperativeSampleNumber: row.realData.bloodSampleCode,
        // 锁组织编号
        lockTissueNumber: row.realData.tissueSampleCode,
        reason: row.realData.reason,
        reasonDetail: row.realData.reasonDetail
      }
      this.isRepeat = true
      this.probeOrderSellVisible = true
    },
    // 探针订购
    handleProbeOrderSell () {
      this.isRepeat = false
      this.probeOrderSellVisible = true
    },
    // 关联样本
    handleConnectionSample () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择1条数据查看关联样本')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.linkSampleDataInfo = {
        id: row.id,
        visible: true
      }
    },
    // 基础数据管理
    handleCommand (command) {
      let commandOptions = {
        0: () => this.handleCancerEditName(),
        1: () => this.handleEditCoreProbe(),
        2: () => this.handleSampleLink()
      }
      commandOptions[command]()
    },
    // 术后样本关联
    handleSampleLink () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择1条数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      this.sampleLinkDataInfo.name = rows[0].name
      this.sampleLinkDataInfo.visible = true
    },
    // 癌种简称管理
    handleCancerEditName () {
      this.cancerEditNameDataInfo.visible = true
    },
    // 核心探针管理
    handleEditCoreProbe () {
      this.coreProbeDataInfo.visible = true
    },
    // 探针储位上传
    handleProbeUpload () {
      this.probeUploadDataInfo.visible = true
    },
    // 探针文件上传
    handleExportProbe () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择1条数据上传探针文件')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (!row.coreName) {
        this.$message.error('核心探针名称不存在，请核实')
        return
      }
      this.exportProbeDataInfo = {
        visible: true,
        id: row.id,
        coreName: row.coreName,
        cancerName: row.cancerName,
        name: row.name
      }
    },
    handleProbeLocationCommand (command) {
      command === 0 ? this.handleProbeDownload() : this.handleProbeUpload()
    },
    handleProbeDownload () {
      if (this.selectedRows.size <= 0) {
        this.$message.error('请选择数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      let ids = rows.map(v => v.id).join(',')
      this.$ajax({
        url: `/system/probe/download_probe_location`,
        method: 'get',
        data: {
          ids: ids
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    // 下载
    handleDownload () {
      if (this.selectedRows.size <= 0) {
        this.$message.error('请选择数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      let ids = rows.map(v => v.id).join(',')
      this.$ajax({
        url: `/system/probe/download_probe_serial`,
        method: 'get',
        data: {
          ids: ids
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    handleToProbeKanban (name, id) {
      util.openNewPage(`/business/subpage/probeKanban?name=${name}&id=${id}`)
    },
    // 到货确认
    async handleProbeArrival () {
      if (this.selectedRows.size <= 0) {
        this.$message.error('请选择数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      let names = rows.map(v => v.name).join(',')
      await this.$confirm(`<div>请再次确认以下探针已到货：${names}</div>`, '探针到货确认', {
        message: `<div style="width: 100%; word-wrap: break-word;word-break: normal;">请再次确认以下探针已到货：${names}</div>`,
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let ids = rows.map(v => v.id).join(',')
      this.$ajax({
        url: '/system/probe/probe_arrival_confirm',
        data: {
          ids: ids
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.handleSearch()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleReset () {
      this.form = {
        code: '',
        name: '',
        status: '',
        time: '',
        patientId: '',
        sampleNum: '',
        quickly: ''
      }
      this.handleSearch()
    },
    handleSearch () {
      this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.currentPage = 1
      this.getData()
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-input--mini {
  width: 250px;
}
.input-width {
  width: 250px;
}
</style>
