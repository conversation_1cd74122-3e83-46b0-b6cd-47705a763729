<template>
  <div>
    <el-descriptions :column="3" border>
      <!--      值为0时也显示-， 故不用setDefault -->
      <el-descriptions-item label="C反应蛋白(CRP)">{{clinicInfo.crp || '-'}}</el-descriptions-item>
      <el-descriptions-item label="降钙素原(PCT)">{{clinicInfo.pct || '-'}}</el-descriptions-item>
      <el-descriptions-item label="中性粒细胞比率(N)">{{clinicInfo.n  || '-'}}</el-descriptions-item>
      <el-descriptions-item label="血液白细胞计数(WBC)">{{clinicInfo.wbc  || '-'}}</el-descriptions-item>
      <el-descriptions-item label="淋巴细胞比率(LYM)">{{clinicInfo.lym || '-'}}</el-descriptions-item>
      <el-descriptions-item label="镜检">{{clinicInfo.microscopyCheck || '-'}}</el-descriptions-item>
      <el-descriptions-item label="培养">{{clinicInfo.culture || '-'}}</el-descriptions-item>
      <el-descriptions-item label="G实验">{{clinicInfo.gExperiment || '-'}}</el-descriptions-item>
      <el-descriptions-item label="GM实验">{{clinicInfo.gmExperiment || '-'}}</el-descriptions-item>
      <el-descriptions-item label="抗原/抗体">{{clinicInfo.antibody || '-'}}</el-descriptions-item>
      <el-descriptions-item label="PCR">{{clinicInfo.pcr || '-'}}</el-descriptions-item>
      <el-descriptions-item label="其他检测">{{clinicInfo.otherChecks || '-'}}</el-descriptions-item>
      <el-descriptions-item label="临床症状">{{clinicInfo.clinicalSymptoms || '-'}}</el-descriptions-item>
      <el-descriptions-item label="临床诊断">{{clinicInfo.clinicalDiagnosis || '-'}}</el-descriptions-item>
      <el-descriptions-item label="重点关注病原">{{clinicInfo.focusPathogens || '-'}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getClinicInfo()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      clinicInfo: {}
    }
  },
  methods: {
    // 获取报告临床信息
    async getClinicInfo () {
      let {code, data} = await this.$ajax({
        url: '/read/pathogen/get_online_report_clinical_info',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.clinicInfo = {
          crp: data.fcrp,
          pct: data.fpct,
          n: data.fn,
          wbc: data.fwbc,
          lym: data.flym,
          microscopyCheck: data.fmicroscopyCheck,
          culture: data.fculture,
          gExperiment: data.fgExperiment,
          gmExperiment: data.fgmExperiment,
          antibody: data.fantibody,
          pcr: data.fpcr,
          otherChecks: data.fotherChecks,
          clinicalSymptoms: data.fclinicalSymptoms,
          clinicalDiagnosis: data.fclinicalDiagnosis,
          focusPathogens: data.ffocusPathogens
        }
      }
    }
  }
}
</script>

<style scoped></style>
