<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      append-to-body
      title="样本列表"
      width="1100px"
      @open="handleOpen">
      <div class="search">
        <el-form ref="form" inline size="mini" @keyup.enter.native="handleSearch">
          <el-form-item label="子文库编号" prop="subLibCode">
            <el-input v-model.trim="form.subLibCode" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="吉因加编号" prop="geneCode">
            <el-input v-model.trim="form.geneCode" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="原始子文库名称" prop="orginSubLibName">
            <el-input v-model.trim="form.orginSubLibName" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
        <div class="buttonGroup">
          <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
          <el-button size="mini" type="primary" plain @click="handleReset">重置</el-button>
        </div>
      </div>
        <!--表格-->
      <el-table
        ref="table"
        :data="tableData"
        :height="400"
        size="mini"
        class="reservationTable"
        style="width: 100%">
        <el-table-column label="序号" type="index" fixed="left"></el-table-column>
        <el-table-column prop="subLibCode" label="子文库编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dnaNum" label="DNA编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneCode" label="吉因加编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orginName" label="原始名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orginSubLibName" label="原始子文库名称" min-width="120" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'

export default {
  name: 'sampleListDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    orderCode: {
      type: String | Number
    },
    taskId: {
      type: String | Number
    }
  },
  data () {
    return {
      form: {
        subLibCode: '',
        geneCode: '',
        orginSubLibName: ''
      },
      submitForm: {},
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          subLibCode: '',
          geneCode: '',
          orginSubLibName: ''
        }
        this.handleSearch()
      })
    },
    // 重置
    handleReset () {
      this.form = {
        subLibCode: '',
        geneCode: '',
        orginSubLibName: ''
      }
      this.handleSearch()
    },
    // 查询
    handleSearch () {
      this.submitForm = util.deepCopy(this.form)
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    // 样本列表数据
    getData () {
      this.$ajax({
        url: '/order/delivery/get_sub_order_sample_data',
        loadingDom: '.reservationTable',
        data: {
          ...this.form,
          orderCode: this.orderCode,
          taskId: this.taskId,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = []
          this.totalPage = res.data.total
          let data = res.data || {}
          let rows = data.rows || []
          rows.forEach(v => {
            let item = {
              subLibCode: v.subLibCode,
              dnaNum: v.dnaNum,
              geneCode: v.geneCode,
              orginName: v.orginName,
              orginSubLibName: v.orginSubLibName,
              subOrderSampleId: v.subOrderSampleId
            }
            item.realDate = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-dialog__body{
    padding: 0 20px 20px !important;
  }
  >>>.el-form-item {
    margin-bottom: 0 !important;
  }
  .search {
    display: flex;
    align-items: center;
  }
  .buttonGroup{
    height: 60px;
    display: flex;
    align-items: center;
  }
</style>
