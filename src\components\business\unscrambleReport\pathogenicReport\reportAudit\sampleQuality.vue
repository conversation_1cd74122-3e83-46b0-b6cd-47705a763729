<template>
  <div>
    <el-table
      ref="table"
      :data="tableData"
      class="dataFilterTable"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @row-click="handleRowClick"
    >
      <el-table-column prop="qcItem"  label="质控指标"></el-table-column>
      <el-table-column prop="qcResult" label="质控结果"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../../../util/util'

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 获取样本质控列表
    async getData () {
      let {code, data = []} = await this.$ajax({
        url: '/read/pathogen/get_online_report_quality_result',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        data.forEach(v => {
          let item = {
            qcItem: v.fqcItem,
            qcResult: v.fqcResult,
            id: v.fid
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped></style>
