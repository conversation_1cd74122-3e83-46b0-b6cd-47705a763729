<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    v-drag-dialog
    :before-close="handleClose"
    title="样本入库"
    width="800px"
    @open="handleOpen">
    <!--    search-->
    <el-form ref="form" label-width="320px" :model="form" label-position="left" :rules="rules" prefix=":">
      <el-form-item prop="cast" label="所选样本共同拥有以下管型， 需要选择入库管型">
        <el-select v-model.trim="form.cast" size="mini" clearable filterable>
          <el-option
            v-for="item in castOptions"
            :key="item"
            :label="item"
            :value="item"/>
        </el-select>
      </el-form-item>
      <el-form-item prop="temperature" label="所选样本存储温度">
        <el-select v-model.trim="form.temperature" size="mini" clearable filterable>
          <el-option
            v-for="item in temperatureOptions"
            :key="item"
            :label="item"
            :value="item"/>
        </el-select>
      </el-form-item>
    </el-form>
    <el-table
      ref="table"
      :data="sampleTableData"
      height="340px"
      border
      size="mini"
      class="computer-table"
      style="width: 100%"
    >
      <el-table-column prop="geneSampleNum" label="吉因加编号" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sampleType" label="样本类型" min-width="120" show-overflow-tooltip></el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button :disabled="loading" size="mini" @click="handleClose">取 消</el-button>
      <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleConfirmIds: {
      type: Array,
      default: () => []
    },
    sampleTableData: {
      type: Array,
      default: () => []
    },
    temperatureOptions: {
      type: Array,
      default: () => []
    },
    castOptions: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      form: {
        cast: '',
        temperature: ''
      },
      rules: {
        cast: [{required: true, message: '请选择管型', trigger: ['blur']}],
        temperature: [{required: true, message: '请选择存储温度', trigger: ['blur']}]
      },
      loading: false,
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.form = {
        cast: '',
        temperature: ''
      }
      this.setDefaultValue(this.temperatureOptions, this.castOptions)
    },
    /**
     * 下拉单的值只有一个的时候，默认选上去
     * @param temperatureOptions
     * @param castOptions
     */
    setDefaultValue (temperatureOptions = [], castOptions = []) {
      if (temperatureOptions.length === 1) {
        this.form.temperature = temperatureOptions[0]
      }
      if (castOptions.length === 1) {
        this.form.cast = castOptions[0]
      }
    },
    //, 样本入库
    handleConfirm () {
      let params = this.sampleTableData.map(v => {
        return {
          fsampleType: v.sampleType,
          fsampleNumber: v.geneSampleNum,
          ftemperature: this.form.temperature,
          ftubeType: this.form.cast,
          fsampleAmount: v.sampleCount,
          fnotes: '',
          foriginNum: v.oldSampleName
        }
      })
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/sample/order/submit_inner_order',
            data: {
              sampleList: params,
              flab: this.sampleTableData[0].productionAreaId
            },
            loadingDom: '.table'
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success(res.message)
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
