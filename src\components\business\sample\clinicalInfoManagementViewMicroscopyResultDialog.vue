<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="镜检结果"
      width="900px"
      @open="handleOpen">
      <el-table :data="tableData" class="table" height="300px" style="width: 100%;">
        <el-table-column prop="geneSampleNum" label="样本编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleLocation" label="取样部位" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="feedBackTime" label="反馈日期" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="result" label="镜检结果" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fclinicalResult" label="大体所见" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fheEstimation" label="FFPE" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="tumorCellsRatio" label="肿瘤细胞占比(%)" width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dnaNum" label="DNA编号" width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'clinicalInfoManagementViewMicroscopyResultDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleNum: {
      type: String
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/sample/clinical/get_noun_message',
        method: 'get',
        data: {
          sampleNum: this.sampleNum
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = res.data.rows || []
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
