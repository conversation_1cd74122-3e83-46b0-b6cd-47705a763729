<template>
  <div>
    <el-container>
      <el-aside width="200px" class="aside-menu">
        <el-scrollbar class="menu-container">
          <el-menu
            :default-active="activeIndex"
            background-color="#001529"
            text-color="rgba(255,255,255,0.65)"
            active-text-color="#fff"
            @select="handleMenuSelect">
            <el-submenu v-if="$setAuthority('009')" index="statistical">
              <template slot="title">
                <icon-svg icon-class="icon-yangbenku"></icon-svg>
                <span>统计报表</span>
              </template>
              <el-menu-item
                v-if="$myresource.menus.indexOf('009008') > -1"
                index="/business/view/mrdMonitorManagement">
                <span>个性化MRD样本监控</span>
              </el-menu-item>
            </el-submenu >
            <el-submenu v-if="$setAuthority('011')" index="sampleLibrary">
              <template slot="title">
                <icon-svg icon-class="icon-yangbenku"></icon-svg>
                <span>出入库管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('011001', 'menus')" index="/business/view/containerManagement">
                <span slot="title">容器管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('011003', 'menus')" index="/business/view/sampleSearch">
                <span slot="title">样本查询</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('011004', 'menus')" index="/business/view/applicationForStorage">
                <span slot="title">出入库申请</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('011005', 'menus')" index="/business/view/turnoverLibraryManagement">
                <span slot="title">出入库管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('011006', 'menus')" index="/business/view/abnormalSampleManagement">
                <span slot="title">异常样本管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('011007', 'menus')" index="/business/view/backSampleManagement">
                <span slot="title">返样管理</span>
              </el-menu-item>
            </el-submenu >
            <el-submenu v-if="$setAuthority('003')" index="sample">
              <template slot="title">
                <icon-svg icon-class="icon-yangbenku"></icon-svg>
                <span>样本管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('003006', 'menus')" index="/business/view/patientInfoDetail">
                <span slot="title">患者信息详情</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('003005', 'menus')" index="/business/view/sample/abnormalSampleManagement">
                <span slot="title">样例异常处理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('003004', 'menus')" index="/business/view/clinicalInfoManagement">
                <span slot="title">样本信息管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('003009', 'menus')" index="/business/view/pathogenManagement">
                <span slot="title">病原样本管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('003003', 'menus')" index="/business/view/sampleSigningManagement">
                <span slot="title">样本签收管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('003008', 'menus')" index="/business/view/expressDeliveryManagement">
                <span slot="title">快递签收管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('003010', 'menus')" index="/business/view/sampleReturnManagement">
                <span slot="title">返样管理</span>
              </el-menu-item>
              <!--<el-menu-item index="/business/view/offlineDetectManagement">-->
                <!--<span slot="title">线下检测管理</span>-->
              <!--</el-menu-item>-->
              <!--<el-menu-item index="/business/view/sampleInfoManagement">-->
                <!--<span slot="title">样例信息管理</span>-->
              <!--</el-menu-item>-->
              <!--<el-menu-item index="/business/view/sampleArrivalConfirm">-->
                <!--<span slot="title">样本到样确认</span>-->
              <!--</el-menu-item>-->
            </el-submenu >
            <el-submenu v-if="$setAuthority('018')" index="qcReport">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>测序业务项目管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('018001', 'menus')" index="/business/view/technologyService/orderReview">
                <span slot="title">订单审核到样</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('018002', 'menus')" index="/business/view/technologyService/sampleConfirm">
                <span slot="title">样本管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('018003', 'menus')" index="/business/view/technologyService/sampleManagement">
                <span slot="title">样本处理</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu v-if="$setAuthority('017')" index="kfSample">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>科服质控报告管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('017001', 'menus')" index="/business/view/qcReport">
                <span slot="title">质控报告生成</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('017002', 'menus')" index="/business/view/qcReportAuditManagement">
                <span slot="title">质控报告审核</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('017003', 'menus')" index="/business/view/subOrderManagement">
                <span slot="title">子订单管理</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu v-if="$setAuthority('019')" index="kfDataDelivery">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>科服数据交付管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('019001', 'menus')" index="/business/view/suborderDataDelivery">
                <span slot="title">子订单交付管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('019002', 'menus')" index="/business/view/sampleDelivery">
                <span slot="title">样本交付查询</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu v-if="$setAuthority('020')" index="computerManagement">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>测序上机管理</span>
              </template>
<!--              <el-menu-item v-if="$setAuthority('020001', 'menus')" index="/business/view/libraryManagement">-->
<!--                <span slot="title">文库定量&片段检测</span>-->
<!--              </el-menu-item>-->
              <el-menu-item index="/business/view/scheduleTaskManagement">
                <span slot="title">排单任务管理</span>
              </el-menu-item>
              <el-menu-item index="/business/view/pooling">
                <span slot="title">pooling</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu v-if="$setAuthority('002')" index="basicData">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>系统基础数据管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('002005', 'menus')" index="/business/view/processFlowManagement">
                <span slot="title">工序流程管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('002006', 'menus')" index="/business/view/processStepManagement">
                <span slot="title">工序步骤管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('002009', 'menus')" index="/business/view/templateManagement">
                <span slot="title">报告模板管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('002010', 'menus')" index="/business/view/moduleManagement">
                <span slot="title">报告模块管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('002015', 'menus')" index="/business/view/controlStandardManagement">
                <span slot="title">对照标准管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('002016', 'menus')" index="/business/view/onlineProbeManagement">
                <span slot="title">探针在线化管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('002017', 'menus')" index="/business/view/reportNodulusManagement">
                <span slot="title">报告小结配置</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu v-if="$setAuthority('013')" index="materials">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>物料管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('013001', 'menus')" index="/business/view/materialInventoryManagement">
                <span slot="title">物料库存管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('013002', 'menus')" index="/business/view/publicityInventoryManagement">
                <span slot="title">宣传品库存管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('013003', 'menus')" index="/business/view/materialApplicationManagement">
                <span slot="title">物料申请管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('013004', 'menus')" index="/business/view/publicityApplicationManagement">
                <span slot="title">宣传品申请管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('013005', 'menus')" index="/business/view/deliveryManagement">
                <span slot="title">发货管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('013006', 'menus')" index="/business/view/grantReport">
                <span slot="title">发放报表</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu v-if="$setAuthority('015')" index="productManagement">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>产品管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('015001', 'menus')" index="/business/view/productManagement">
                <span slot="title">常规产品管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('015002', 'menus')" index="/business/view/sinkMarketProductManagement">
                <span slot="title">下沉市场产品管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('015003', 'menus')" index="/business/view/pharmaceuticalProductManagement">
                <span slot="title">药厂产品管理</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('015004', 'menus')" index="/business/view/sequenceProductManagement">
                <span slot="title">测序工厂产品管理</span>
              </el-menu-item>
            </el-submenu>
<!--            <el-submenu v-if="$setAuthority('004')" index="orderManagement">-->
<!--              <template slot="title">-->
<!--                <icon-svg icon-class="icon-basic-data-management"></icon-svg>-->
<!--                <span>订单管理</span>-->
<!--              </template>-->
<!--&lt;!&ndash;              <el-menu-item index="/business/view/orderReview" v-if="$setAuthority('004001', 'menus')">&ndash;&gt;-->
<!--&lt;!&ndash;                <span slot="title">订单审核</span>&ndash;&gt;-->
<!--&lt;!&ndash;              </el-menu-item>&ndash;&gt;-->
<!--              <el-menu-item v-if="$setAuthority('004002', 'menus')" index="/business/view/sampleAbnormal">-->
<!--                <span slot="title">异常样本</span>-->
<!--              </el-menu-item>-->
<!--              <el-menu-item v-if="$setAuthority('004003', 'menus')" index="/business/view/orderKanban">-->
<!--                <span slot="title">订单看板</span>-->
<!--              </el-menu-item>-->
<!--            </el-submenu>-->
            <el-submenu index="reportRead" v-if="$setAuthority('006')">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>报告解读</span>
              </template>
              <el-menu-item index="/business/view/cnvVerifyManagement" v-if="$setAuthority('006004', 'menus')">
                <span slot="title">胚系CNV验证</span>
              </el-menu-item>
            </el-submenu>
            <el-submenu index="sampleReceipt" v-if="$setAuthority('022')">
              <template slot="title">
                <icon-svg icon-class="icon-basic-data-management"></icon-svg>
                <span>样本签收管理</span>
              </template>
              <el-menu-item v-if="$setAuthority('022001', 'menus')" index="/business/view/samplesNotSignedFor">
                <span slot="title">样本未签收</span>
              </el-menu-item>
              <el-menu-item v-if="$setAuthority('022002', 'menus')" index="/business/view/sampleHasBeenSigned">
                <span slot="title">样本已签收</span>
              <el-menu-item v-if="$setAuthority('022002', 'menus')" index="/business/view/sampleHasBeenSigned">
                <span slot="title">签收样本信息</span>
              </el-menu-item>
              </el-menu-item>
            </el-submenu>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      <el-main class="main-container">
        <div class="path-tabs-container">
          <path-tabs />
        </div>
        <div class="content-container">
          <keep-alive>
            <router-view />
          </keep-alive>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
// import num from './components/cc'
import PathTabs from '../common/pathTabs'
import IconSvg from '../common/iconSvg'
export default {
  name: 'overview',
  components: {IconSvg, PathTabs},
  watch: {
    $route: {
      handler: function (newVal) {
        this.setActiveIndex(newVal.path, newVal.meta)
      },
      immediate: true
    }
  },
  computed: {
    activeIndex () {
      return this.$store.getters.getValue('activeIndex')
    }
  },
  data () {
    return {
    }
  },
  methods: {
    handleMenuSelect (key) {
      console.log(key, this.$route.path)
      if (key === this.$route.path) {
        this.$router.go(0)
        return
      }
      this.$router.push({path: key})
    },
    setActiveIndex (path, meta) {
      if (meta.title) {
        this.$store.commit({
          type: 'old/setValue',
          category: 'activeIndex',
          activeIndex: path
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  /deep/ .el-menu{
    border-right: none;
  }
  /deep/.el-menu-item.is-active{
    background: $color!important;
  }
.aside-menu{
  height: calc(100vh - 65px);
  background: #001529;
}
.menu-container{
  height: calc(100vh - 65px - 1px);
  z-index: 1000;
  overflow-x: hidden;
}
.main-container{
  background: $page-bg;
  .path-tabs-container{
    height: 50px;
    /*background: rgba(2, 167, 240, 0.341176470588235);*/
    background: #fff;
    padding-left: 20px;
    padding-top: 10px;
  }
  .content-container{
    padding: 0 10px;
    margin: 20px;
    overflow-y: auto;
    background: #fff;
    height: calc(100vh - 65px - 50px - 40px)
  }
}
</style>
