<template>
  <div>
    <el-dialog
      :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose" title="类别管理"
      width="30%" @open="handleOpen">
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="mini">
          <el-form-item label="物料类别" prop="name">
            <el-input v-model="form.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="desc">
            <el-input v-model="form.desc" :autosize="{minRows: 2}" type="textarea" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'categoryDetailDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {},
  props: {
    ptype: {
      type: Number,
      default: 0,
      required: true
    },
    pcategoryId: {
      type: [Number, String],
      default: null,
      required: false
    },
    pname: {
      type: String,
      default: '',
      required: true
    },
    pdesc: {
      type: String,
      default: '',
      required: false
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      type: 0,
      form: {
        fid: null,
        name: '',
        desc: ''
      },
      rules: {
        name: [
          {required: true, message: '请输入物料类别', trigger: 'blur'}
        ],
        desc: []
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.type = this.ptype
        this.form = {
          categoryId: this.pcategoryId,
          name: this.pname,
          desc: this.pdesc
        }
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/materials/add_new_category',
            data: {
              fid: this.form.categoryId,
              fname: this.form.name,
              fdescription: this.form.desc,
              ftype: this.type
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('categorySaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  .categoryDetail >>>.el-table__header .el-checkbox {
    display: none;
  }
</style>
