<template>
  <span>
    <el-tooltip class="item" effect="dark" :content="msg" placement="top-start">
      <span>{{title}}<icon-svg icon-class="icon-wenhao" style="font-size: 14px; margin-left: 3px;"></icon-svg></span>
    </el-tooltip>
  </span>
</template>

<script>
export default {
  name: 'vxe-header-tips',
  props: {
    msg: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>

</style>
