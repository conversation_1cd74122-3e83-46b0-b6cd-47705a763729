import { myAjax } from '@/util/ajax'
/**
 * 批量修改报告小结客户信息。发送一个POST请求到'/summaryReport/save_cancer_report_info_by_customer'。
 * @param {Object} data - 请求的数据对象。应确保其安全性，避免XSS攻击等。
 * @param {Object} options - 额外的请求选项，将被合并到请求配置中。
 * @returns {Promise} - 包含请求结果的Promise对象。
 */
export function saveCancerReportCustomer (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/system/summaryReport/save_cancer_report_info_by_customer',
    data: data,
    ...options
  })
}

/**
 * 导出报告结节模板的函数。
 *
 * 该函数通过发送一个POST请求到指定的URL，以下载报告结节的客户编码导入Excel模板。
 * 使用者可以传入额外的选项来定制请求。
 *
 * @param {Object} data - 请求的数据对象。
 * @param {Object} options - 可选的配置选项，默认为空对象。
 * @returns{Promise} - 返回一个Promise对象，代表请求的结果。
 */
export function downloadReportNodulesTemplate (data, options = {}) {
  // 使用myAjax函数发送POST请求来下载报告结节模板
  return myAjax({
    url: '/system/summaryReport/download_excel_template',
    method: 'get',
    responseType: 'blob',
    data: data,
    ...options
  })
}

export function deleteProduct (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/system/summaryReport/delete_cancer_config_product',
    data: data,
    ...options
  })
}
