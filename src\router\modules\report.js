// 报表和统计相关路由
export default [
  {
    path: '/business/view/mrdMonitorManagement',
    meta: {
      title: '个性化MRD样本监控'
    },
    component: () => import('@/components/business/statistical/mrdMonitorManagement/index.vue')
  },
  {
    path: '/business/view/templateManagement',
    meta: {
      title: '报告模板管理'
    },
    component: () => import('@/components/business/templateManagement/overview.vue')
  },
  {
    path: '/business/view/moduleManagement',
    meta: {
      title: '报告模块管理'
    },
    component: () => import('@/components/business/moduleManagement/overview.vue')
  },
  {
    path: '/business/view/qcReport',
    meta: {
      title: '质控报告生成'
    },
    component: () => import('@/components/business/scientificResearchProject/qcReport/qcReport.vue')
  },
  {
    path: '/business/view/subOrderManagement',
    meta: {
      title: '子订单管理'
    },
    component: () => import('@/components/business/scientificResearchProject/subOrderManagement/subOrderManagement.vue')
  },
  {
    path: '/business/view/qcReportAuditManagement',
    meta: {
      title: '质控报告审核'
    },
    component: () => import('@/components/business/scientificResearchProject/qcReportAuditManagement/qcReportAuditManagement.vue')
  },
  {
    path: '/business/view/suborderDataDelivery',
    meta: {
      title: '子订单交付管理'
    },
    component: () => import('@/components/business/scientificResearchDataDelivery/suborderDataDelivery/overview.vue')
  },
  {
    path: '/business/view/sampleDelivery',
    meta: {
      title: '样本交付查询'
    },
    component: () => import('@/components/business/scientificResearchDataDelivery/sampleDelivery/overview.vue')
  },
  {
    path: '/business/view/cnvVerifyManagement',
    meta: {
      title: '胚系CNV验证'
    },
    component: () => import('@/components/business/reportRead/cnvVerifyManagement/index.vue')
  },
  {
    path: '/business/subpage/dataBoard',
    component: () => import('@/components/business/statistical/mrdMonitorManagement/dataBoard.vue')
  },
  {
    path: '/business/sub/templateConfigPage',
    component: () => import('@/components/business/templateManagement/templateConfigPage.vue')
  },
  {
    path: '/business/unscramble/ai',
    component: () => import('@/components/business/ai/index.vue')
  },
  {
    path: '/business/unscramble/mutationAnalysis',
    component: () => import('@/components/business/ai/common/addForecastData.vue')
  },
  {
    path: '/business/unscramble/wesReport',
    component: () => import('@/components/business/unscrambleReport/unscrambleReport/wesReport.vue')
  },
  {
    path: '/business/unscramble/addForecastData',
    component: () => import('@/components/business/unscrambleReport/unscrambleReport/common/addForecastData.vue')
  },
  {
    path: '/business/unscramble/addDXData',
    component: () => import('@/components/business/unscrambleReport/unscrambleReport/common/addDXData.vue')
  },
  {
    path: '/business/unscramble/addGeneticMutation',
    component: () => import('@/components/business/unscrambleReport/unscrambleReport/common/addGeneticMutation.vue')
  },
  {
    path: '/business/unscramble/pathogenicReport',
    component: () => import('@/components/business/unscrambleReport/pathogenicReport/index.vue')
  }
]
