<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
      title="查看"
      width="1200px"
      @open="handleOpen"
    >
      <div class="wrapper">
        <pdf :url="url"></pdf>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="submitLoading" v-if="type === 1" size="mini" type="primary" @click="handleReject">驳回</el-button>
        <el-button :loading="submitLoading" v-if="type === 1" size="mini" type="primary" @click="handleConfirm">通过</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'
import constants from '../../../../../util/constants'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: {
      type: Number
    },
    reportId: {
      type: [String, Number]
    }
  },
  data () {
    return {
      url: '',
      submitLoading: false
    }
  },
  methods: {
    handleOpen () {
      this.url = constants.JS_CONTEXT + '/order/report/preview_report?reportId='
      this.url += this.reportId
    },
    // 驳回
    async handleReject () {
      this.submitLoading = true
      try {
        let { value } = await this.$prompt('', '驳回说明', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'textarea'
        })
        this.$ajax({
          url: '/order/report/audit_report_reject',
          data: {
            reportId: this.reportId,
            rejectNote: value
          },
          method: 'get'
        }).then((res) => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('审核成功')
            this.visible = false
            this.$emit('dialogConfirmEvent')
          } else {
            this.$message.error(res.message)
          }
        })
      } finally {
        this.submitLoading = false
      }
    },
    // 通过
    async handleConfirm () {
      this.submitLoading = true
      try {
        await this.$confirm('审核通过后不可修改，确定吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        this.$ajax({
          url: '/order/report/audit_report_pass',
          data: {
            reportId: this.reportId
          },
          method: 'get'
        }).then((res) => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('审核成功')
            this.visible = false
            this.$emit('dialogConfirmEvent')
          } else {
            this.$message.error(res.message)
          }
        })
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  height: 450px;
  overflow: auto;
}
</style>
