<template>
  <div>
    <el-dialog
      :title="title" :modal="false"
      :visible.sync="visible"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="40%"
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="mini" label-suffix=":">
          <el-form-item label="检查日期">
            <my-date-picker v-model="form.checkTime"></my-date-picker>
          </el-form-item>
          <el-form-item label="病灶部位" prop="checkPosition">
            <el-input v-model.trim="form.checkPosition" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="病理结果">
            <el-input v-model.trim="form.checkResult" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="信息来源">
            <el-input v-model.trim="form.sourceImgs" placeholder="请输入" style="width: calc(100% - 80px)"></el-input>
            <el-button type="primary" icon="el-icon-search">查询</el-button>
          </el-form-item>
          <el-form-item label="淋巴结转移">
            <el-select v-model="form.lymphTransfer" placeholder="请选择" style="width: 20%;">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in trueOrFalseList">
              </el-option>
            </el-select>
            <el-input v-model.number="form.lymphNum1" placeholder="请输入" style="width: 15%;"></el-input> /
            <el-input v-model.number="form.lymphNum2" placeholder="请输入" style="width: 15%;"></el-input>
            <el-input v-model.trim="form.lymphRange" placeholder="请输入" style="width: 40%;"></el-input>
          </el-form-item>
          <el-form-item label="乳腺癌分子亚型">
            <el-select v-model="form.breastCancerMoleculeSubtype" placeholder="请选择">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in breastCancerMoleculeSubtypeList">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
export default {
  name: 'clinicalInfoManagementClinicalPathologySaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    myDatePicker
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      },
      required: true
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      title: '新增病理检查',
      form: {
        clinicalPathologyId: null,
        sampleBasicId: null,
        sampleClinicalCancerInfoId: null,
        checkTime: '',
        checkYear: '',
        checkMonth: '',
        checkDay: '',
        checkPosition: '',
        checkResult: '',
        sourceImgs: '',
        lymphTransfer: '',
        lymphNum1: '',
        lymphNum2: '',
        lymphRange: '',
        breastCancerMoleculeSubtype: ''
      },
      rules: {
        checkPosition: [
          {required: true, message: '请输入病灶部位', trigger: 'blur'}
        ]
      },
      trueOrFalseList: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ],
      breastCancerMoleculeSubtypeList: [
        {
          value: 'HR+/HER2-',
          label: 'HR+/HER2-'
        },
        {
          value: 'HR+/HER2+',
          label: 'HR+/HER2+'
        },
        {
          value: 'HR+/HER2不确定',
          label: 'HR+/HER2不确定'
        },
        {
          value: 'HR-/HER2+',
          label: 'HR-/HER2+'
        },
        {
          value: 'TNBC',
          label: 'TNBC'
        },
        {
          value: '不详',
          label: '不详'
        }
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          clinicalPathologyId: null,
          sampleBasicId: null,
          sampleClinicalCancerInfoId: null,
          checkTime: '',
          checkYear: '',
          checkMonth: '',
          checkDay: '',
          checkPosition: '',
          checkResult: '',
          sourceImgs: '',
          lymphTransfer: '',
          lymphNum1: '',
          lymphNum2: '',
          lymphRange: '',
          breastCancerMoleculeSubtype: ''
        }
        this.form = Object.assign({}, this.form, this.pdata)
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let checkTime = this.form.checkTime.split('-')
          this.$ajax({
            url: '/sample/clinical/save_clinical_pathology',
            data: {
              clinicalPathologyId: this.form.clinicalPathologyId,
              sampleBasicId: this.form.sampleBasicId,
              checkYear: checkTime[0] || '',
              checkMonth: checkTime[1] || '',
              checkDay: checkTime[2] || '',
              checkPosition: this.form.checkPosition,
              checkResult: this.form.checkResult,
              sourceImgs: this.form.sourceImgs,
              lymphTransfer: this.form.lymphTransfer,
              lymphNum1: this.form.lymphNum1,
              lymphNum2: this.form.lymphNum2,
              lymphRange: this.form.lymphRange,
              breastCancerMoleculeSubtype: this.form.breastCancerMoleculeSubtype,
              sampleClinicalCancerInfoId: this.form.sampleClinicalCancerInfoId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('clinicalPathologySaveDialogConfirmEvent')
            } else {
              this.$message.errors(result.message)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
