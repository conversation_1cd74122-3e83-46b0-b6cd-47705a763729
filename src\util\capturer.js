import html2canvas from 'html2canvas'
export default class ScreenshotCapturer {
  constructor (targetElement) {
    this.target = targetElement
    this.mutationObserver = null
  }

  async capture () {
    try {
      // 1. 准备工作
      await this.prepareForCapture()

      // 2. 等待稳定状态
      await this.waitForStableDOM()

      // 3. 执行截图
      return await this.performCapture()
    } catch (error) {
      console.error('截图失败:', error)
      throw error
    }
  }

  async prepareForCapture () {
    // 确保目标元素可见
    if (!this.isElementVisible(this.target)) {
      throw new Error('目标元素不可见')
    }

    // 确保字体加载
    await document.fonts.ready

    // 确保图片加载
    await this.waitForImages()
  }

  async waitForStableDOM (timeout = 1000) {
    return new Promise((resolve, reject) => {
      let timer = null

      const observer = new MutationObserver(() => {
        // 发现DOM变化，重置等待计时器
        if (timer) clearTimeout(timer)
        timer = setTimeout(checkStability, 100)
      })

      const checkStability = () => {
        observer.disconnect()
        resolve()
      }

      // 开始观察DOM变化
      observer.observe(this.target, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true
      })

      // 设置总超时
      setTimeout(() => {
        observer.disconnect()
        resolve() // 超时后仍然继续
      }, timeout)

      // 初始检查
      timer = setTimeout(checkStability, 100)
    })
  }

  async performCapture (retries = 3) {
    try {
      // 强制布局同步
      this.target.offsetHeight // eslint-disable-line

      return await html2canvas(this.target, {})
    } catch (error) {
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 300))
        return this.performCapture(retries - 1)
      }
      throw error
    }
  }

  isElementVisible (element) {
    if (!element) return false

    const style = getComputedStyle(element)
    if (style.display === 'none' ||
        style.visibility === 'hidden' ||
        style.opacity === '0') {
      return false
    }

    const rect = element.getBoundingClientRect()
    return !(rect.width === 0 || rect.height === 0)
  }

  async waitForImages () {
    const images = this.target.querySelectorAll('img')
    await Promise.all(Array.from(images).map(img => {
      if (img.complete) return Promise.resolve()

      return new Promise(resolve => {
        img.onload = resolve
        img.onerror = resolve
        // 设置超时以防图片永远不加载
        setTimeout(resolve, 2000)
      })
    }))
  }
}
