<template>
  <div style="padding: 10px;">
    <div>
      <!--    探针信息-->
      <div class="flex">
        <div>
          <b style="font-size: 36px;">探针看板</b>
          <span style="margin-left: 30px;">个性化探针名称：{{name}}</span>
        </div>
      </div>
      <!--    看板详情-->
      <div >
        <div class="info-title margin">看板详情 (蓝-进行中 | 绿-完成 | 灰-未做)</div>
        <div class="flex" style="width: 60%;">
          <template v-for="(item, index) in modules">
            <div :key="index">
              <div class="finish-time-wrapper">
                {{item && item.finishTime}}
              </div>
              <el-button
                :type="item && status[item.stepStatus]"
                @click="handleChangeModule(item.key)">
                {{item && item.stepName}}
              </el-button>
            </div>
            <div v-if="index !== modules.length - 1" :key="index + 'line'" class="line"></div>
          </template>
        </div>
      </div>
      <!--    信息详情-->
      <div >
        <div class="info-title margin">信息详情</div>
        <components :is="module"></components>
      </div>
    </div>
  </div>
</template>

<script>
import orderProbe from './orderProbe'
import probeDesign from './probeDesign'
import probeProduction from './probeProduction'
import probeUse from './probeUse'
import probeConfig from './probeConfig'
export default {
  name: 'probeKanban',
  components: {
    orderProbe,
    probeDesign,
    probeProduction,
    probeUse,
    probeConfig
  },
  mounted () {
    this.id = this.$route.query.id
    this.name = this.$route.query.name
    this.getProbeStep()
  },
  data () {
    return {
      name: '',
      module: 'orderProbe',
      modules: [],
      status: {
        0: 'success',
        1: 'primary',
        2: 'info'
      }
    }
  },
  methods: {
    getProbeStep () {
      this.$ajax({
        url: '/system/probe/get_probe_step',
        data: {
          fid: this.id
        },
        method: 'get'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.modules = []
          let data = result.data || []
          const keys = ['orderProbe', 'probeDesign', 'probeProduction', 'probeUse', 'probeConfig']
          data.forEach((v, index) => {
            let item = {
              stepName: v.stepName,
              stepStatus: v.stepStatus,
              key: keys[index],
              finishTime: v.finishTime.slice(0, 10)
            }
            this.modules.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleChangeModule (module) {
      this.module = module
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.margin {
  margin: 20px 0;
}
.line {
  flex: 1;
  border: 1px solid #888;
  width: 50px;
  margin-top: 20px;
}
.info-title {
  padding: 10px;
  background-color: #f3f3f8;
  border: 1px solid #eee;
}
.finish-time-wrapper {
  height: 20px;
}
</style>
