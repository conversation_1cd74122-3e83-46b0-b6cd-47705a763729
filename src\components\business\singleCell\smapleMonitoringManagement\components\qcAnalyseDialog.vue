<template>
    <el-dialog
      title="质控分析"
      :visible.sync="visible"
      width="95vw"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      @opened="handleOpen"
    >
      <div class="qc-analyse-dialog">
        <el-table
          :data="tableData"
          border
          class="table"
          size="mini"
          style="width: 100%; height: 100%;"
          @expand-change="handleExpandChange"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            type="expand"
            label=""
            width="50">
            <template slot-scope="scope">
              <el-table
                :data="scope.row.sequencingDetails"
                class="sub-table"
                style="padding-left: 60px;"
                border
                size="mini">
                <el-table-column
                  prop="projectCode"
                  label="项目编号"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="projectName"
                  label="项目名称"
                  min-width="120"
                  show-overflow-tooltip
                  align="center"/>
                <el-table-column
                  prop="species"
                  label="物种"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="geneNum"
                  label="吉因加编号"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="cosSampleName"
                  label="样本原始名称"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="tissueSampleType"
                  label="组织样本类型"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="productName"
                  label="产品名称"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="libTypeSuffix"
                  label="探针"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="libNum"
                  label="文库编号"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="sequenceTime"
                  label="上机时间"
                  min-width="150"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="seqType"
                  label="测序类型"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="dataSize"
                  label="产量(G)"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="oneMismactchRate"
                  label="OneMismactchRate"
                  min-width="150"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="qualifiedRatio"
                  label="QualifiedRatio"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="q20"
                  label="Q20"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="q30"
                  label="Q30"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="volumeRatio"
                  label="volumeRatio"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="gc"
                  label="GC"
                  min-width="100"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="totalReads"
                  label="TotalReads(M)"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
                <el-table-column
                  prop="totalReadsHalf"
                  label="TotalReads(M)/2"
                  min-width="120"
                  align="center" show-overflow-tooltip/>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column
            prop="qualityAnalysisTaskNum"
            label="质控任务ID"
            min-width="120"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            prop="status"
            label="任务状态"
            min-width="100"
            align="center" show-overflow-tooltip
          >
            <template slot-scope="scope">
                <!-- 根据质控状态设置不同的文字颜色:
                     0 - 灰色(未启动)
                     1 - 蓝色(已启动)
                     2 - 绿色(已完成) -->
                <tooltips :txt-info="scope.row.statusText + ''" :class="getQcStatusClass(scope.row.qcStatus)"></tooltips>
            </template>
          </el-table-column>
          <el-table-column
            prop="allExperimentDataSize"
            label="实验文库产出数据量"
            min-width="150"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            prop="createTime"
            label="下达时间"
            min-width="150"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            prop="creator"
            label="操作人"
            min-width="100"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            prop="qualityCompleteTime"
            label="质控完成时间"
            min-width="150"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            prop="qualityResultText"
            label="质控结果"
            min-width="100"
            align="center" show-overflow-tooltip
          > <template slot-scope="scope">
                <!-- 根据质控状态设置不同的文字颜色:
                     0 - 灰色(未启动)
                     1 - 蓝色(已启动)
                     2 - 绿色(已完成) -->
                <tooltips :txt-info="scope.row.qualityResultText + ''" :class="getQcResultClass(scope.row.qualityResult)"></tooltips>
            </template>
        </el-table-column>
          <el-table-column
            prop="internalResultText"
            label="内控结果"
            min-width="100"
            align="center" show-overflow-tooltip
          >
          <template slot-scope="scope">
                <!-- 根据质控状态设置不同的文字颜色:
                     0 - 灰色(未启动)
                     1 - 蓝色(已启动)
                     2 - 绿色(已完成) -->
                <tooltips :txt-info="scope.row.internalResultText + ''" :class="getQcResultClass(scope.row.internalResult)"></tooltips>
            </template>
        </el-table-column>
          <el-table-column
            prop="isDeliver"
            label="是否交付"
            min-width="100"
            align="center" show-overflow-tooltip
          />
          <el-table-column
            prop="fileLink"
            label="详细质控结果列表"
            min-width="200"
            align="center" show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-button v-if="scope.row.realData.fileLink" type="text" @click="handleOpenLink(scope.row.realData)">下载质控结果</el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </template>

<script>
import mixins from '@/util/mixins'
import util, { awaitWrap } from '@/util/util'
import { getQcAnalyseList, getQcAnalyseDetail, downloadReportFile } from '@/api/sequencingManagement/singleCell'
import {booleanOptions, qcAnalysisStatusConfig} from '../dataFormate'
import {downloadFile, readBlob} from '../../../../../util/util'
export default {
  name: 'QcAnalyseDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    geneNum: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      taskType: qcAnalysisStatusConfig,
      qcResult: {
        // 内控结果（0不合格 1合格 2强制合格）
        0: '不合格',
        1: '合格',
        2: '强制合格'
      },
      booleanOptions: booleanOptions,
      tableData: [
      ]
    }
  },
  methods: {
    async handleOpen () {
      // 重置表单数据
      this.$refs.form && this.$refs.form.resetFields()

      // 调用接口获取数据
      const params = {
        fgeneNum: this.geneNum
      }
      const { res = {} } = await awaitWrap(getQcAnalyseList(params, {
        loadingDom: '.table'
      }))
      if (res.code === this.SUCCESS_CODE) {
        // 映射后端数据到前端数据结构
        this.tableData = (res.data || []).map(item => {
          const v = {
            id: item.fid,
            qualityAnalysisTaskNum: item.fqualityAnalysisTaskNum,
            status: item.fstatus,
            statusText: this.taskType[item.fstatus],
            geneNum: item.fgeneNum,
            allExperimentDataSize: item.fallExperimentDataSize,
            allOligoDataSize: item.fallOligoDataSize,
            qualityCompleteTime: item.fqualityCompleteTime,
            createTime: item.fcreateTime,
            creator: item.fcreator,
            internalResult: item.finternalResult,
            internalResultText: this.qcResult[item.finternalResult],
            qualityResult: item.fqualityResult,
            qualityResultText: this.qcResult[item.fqualityResult],
            isDeliver: this.booleanOptions[item.fisDeliver],
            sequencingDetails: [],
            fileLink: item.ffileLink
          }
          v.realData = JSON.parse(JSON.stringify(v))
          util.setDefaultEmptyValueForObject(v)
          return v
        })
      }
    },
    /**
     * 获取质控状态对应的样式类名
     * @param {number} status - 质控状态码
     * @returns {string} 对应的CSS类名
     * 0: 未启动 - 灰色
     * 1: 已启动 - 蓝色
     * 2: 已完成 - 绿色
     */
    getQcStatusClass (status) {
      const statusClassMap = {
        2: 'qc-status--not-started', // 未启动
        0: 'qc-status--in-progress', // 已启动
        1: 'qc-status--completed' // 已完成
      }
      return statusClassMap[status] || ''
    },
    async handleOpenLink (row) {
      const { res } = await awaitWrap(downloadReportFile({
        fcosQualityAnalysisTaskId: row.id
      }, {
        loadingDom: '.table'
      }))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
    },
    /**
     * 获取质控状态对应的样式类名
     * @param {number} status - 质控状态码
     * @returns {string} 对应的CSS类名
     */
    getQcResultClass (status) {
      const statusClassMap = {
        0: 'result-status--unfinish', // 不合格
        1: 'result-status--finish', // 合格
        2: 'result-status--finish' // 合格
      }
      return statusClassMap[status] || ''
    },
    // 展开表格
    async handleExpandChange (row, expandedRows) {
      if (expandedRows.length > 0) {
        await this.$nextTick()
        // 获取展开行的ID
        const id = row.id
        // 调用接口获取子表格数据
        const params = { fcosQualityAnalysisTaskId: id }
        const { res = {} } = await awaitWrap(getQcAnalyseDetail(params, {
          loadingDom: '.sub-table'
        }))
        if (res.code === this.SUCCESS_CODE) {
          // 映射后端数据到前端数据结构
          row.sequencingDetails = (res.data || []).map(item => {
            const v = {
              projectName: item.fprojectName,
              projectCode: item.fprojectCode,
              species: item.fspecies,
              geneNum: item.fgeneNum,
              cosSampleName: item.fcosSampleName,
              tissueSampleType: item.ftissueSampleType,
              productName: item.fproductName,
              libTypeSuffix: item.flibTypeSuffix,
              libNum: item.flibNum,
              sequenceTime: item.fsequenceTime,
              deplaneTime: item.fdeplaneTime,
              seqType: item.fseqType,
              dataSize: item.fdataSize,
              oneMismactchRate: item.foneMismactchRate,
              qualifiedRatio: item.fqualifiedRatio,
              q20: item.fq20,
              q30: item.fq30,
              volumeRatio: item.fvolumeRatio,
              gc: item.fgc,
              totalReads: item.ftotalReads,
              totalReadsHalf: item.ftotalReadsHalf
            }
            v.realData = JSON.parse(JSON.stringify(v))
            util.setDefaultEmptyValueForObject(v)
            return v
          })
        }
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
  .qc-analyse-dialog {
    max-height: 65vh;
    min-height: 30vh;
    overflow-y: auto;
  }

  .qc-status {
    &--not-started { color: #747474; }  // 未启动 - 灰色
    &--in-progress { color: #409EFF; }  // 已启动 - 蓝色
    &--completed { color: #51C86B; }    // 已完成 - 绿色
  }

  .result-status {
    &--unfinish { color: #FF4D4F; }     // 已启动 - 蓝色
    &--finish { color: #51C86B; }    // 已完成 - 绿色
  }
  </style>
