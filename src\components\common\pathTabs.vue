<template>
  <div>
    <scroll-pane :scroll-height="30">
      <div  style="display: flex;flex-wrap: nowrap;width: auto;">
        <!--<el-button-->
          <!--size="mini"-->
          <!--v-if="$setAuthority('011001', 'menus')"-->
          <!--@click="toPage(indexPath)"-->
          <!--:type="activeIndex === indexPath ? 'primary' : 'default'">-->
          <!--容器管理-->
        <!--</el-button>-->
        <template v-for="(item, index) in visitedViews">
          <el-button
            :key="item.path + index"
            :type="activeIndex === item.path ? 'primary' : 'default'"
            size="mini"
            @click="toPage(item.path)">
            {{item.title}}
            <i
              :style="{color: activeIndex === item.path ? '#fff' : '#333333'}"
              v-if="visitedViews.length > 1"
              class="el-icon-error"
              style="margin-left: 10px;"
              @click.stop="delVisitedViews(item.path, index)"></i>
          </el-button>
        </template>
      </div>
    </scroll-pane>
  </div>
</template>

<script>
// import num from './components/c
export default {
  name: 'pathTabs',
  watch: {
    $route: {
      handler: function (newVal) {
        this.addVisitedViews(newVal)
      },
      immediate: true
    }

  },
  computed: {
    visitedViews () {
      return this.$store.getters.getValue('pathTabs')
    },
    activeIndex () {
      return this.$store.getters.getValue('activeIndex')
    }
  },
  data () {
    return {
      indexPath: '/business/view/containerManagement'
    }
  },
  methods: {
    addVisitedViews (route) {
      if (route.meta.title) {
        let hasThisPath = this.visitedViews.some(item => {
          return route.path === item.path
        })
        if (!hasThisPath) {
          let paths = JSON.parse(JSON.stringify(this.visitedViews))
          let v = {
            title: route.meta.title,
            path: route.path
          }
          paths.push(v)
          this.$store.commit({
            type: 'old/setValue',
            category: 'pathTabs',
            pathTabs: paths
          })
        }
      }
    },
    toPage (path) {
      this.$router.push({path: path})
    },
    delVisitedViews (path, index) {
      let paths = JSON.parse(JSON.stringify(this.visitedViews))
      paths.splice(index, 1)
      console.log(paths)
      this.$store.commit({
        type: 'old/setValue',
        category: 'pathTabs',
        pathTabs: paths
      })
      if (path === this.activeIndex) {
        let nextPage = paths.length === 0 ? this.indexPath : paths[index] ? paths[index].path : paths[index - 1].path
        this.$router.push({path: nextPage})
      }
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-button--mini, .el-button--mini.is-round{
    padding: 7px 5px;
  }
</style>
