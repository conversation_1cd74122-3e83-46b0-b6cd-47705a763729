<template>
  <div>
    <div v-if="imgs.length > 0">
      <div v-if="type !== 1" class="btn">
        <el-upload
          ref="upload"
          :action="action"
          :data="{
            fsamplePairId: analysisRsId
          }"
          :on-success="handleOnSuccess"
          :on-error="handleOnError"
          :on-change="handleChange"
          :limit="1"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :file-list="fileList"
          class="upload-demo">
          <el-button size="mini" type="primary">编辑</el-button>
        </el-upload>
        <el-button style="margin: 0 10px;" type="primary" size="mini" @click="base64DownloadFile('', currentImg )">下载</el-button>
      </div>
      <div class="img-wrapper">
        <el-image :src="currentImg" fit="scale-down" style="width: 100%; height: 400px;"></el-image>
      </div>
    </div>
    <div v-else>
      <el-empty description="暂无图片"></el-empty>
    </div>

  </div>
</template>

<script>

import constants from '../../../../util/constants'
export default {
  props: {
    imgs: {
      type: Array
    },
    analysisRsId: {
      type: String
    },
    type: {
      type: Number,
      default: 0
    }
  },
  mounted () {
    this.currentImg = this.imgs[0]
  },
  watch: {
    imgs () {
      this.currentImg = this.imgs[0]
    }
  },
  data () {
    return {
      currentImg: '',
      action: constants.JS_CONTEXT + '/read/bigAi/save_new_pic'
    }
  },
  methods: {
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('修改成功')
        this.$emit('uploadFileSuccessEvent')
      } else {
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    handleOnError () {
      this.loading = false
    },
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(png|svg|jpg|jpeg)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过10M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传png图片')
        return false
      }
    },
    handleChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
      // let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)
      // if (existFile) {
      //   this.$message.error('当前文件已经存在!')
      //   fileList.pop()
      // }
      this.fileList = fileList
    },
    // base64文件下载下载
    base64DownloadFile (fileName, content) {
      let aLink = document.createElement('a')
      let blob = this.base64ToBlob(content) // new Blob([content]);

      let evt = document.createEvent('HTMLEvents')
      console.log('点击下载', evt)
      evt.initEvent('click', true, true) // initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = fileName
      aLink.href = URL.createObjectURL(blob)

      // aLink.dispatchEvent(evt);
      aLink.click()
    },
    // base64转blob
    base64ToBlob (code) {
      let parts = code.split(';base64,')
      let contentType = parts[0].split(':')[1]
      let raw = window.atob(parts[1])
      let rawLength = raw.length

      let uInt8Array = new Uint8Array(rawLength)

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], {type: contentType})
    }
  }
}
</script>

<style scoped lang="scss">
.img-wrapper {
  padding: 10px;
}
.btn {
  display: flex;
  align-items: center;
}
</style>
