<template>
  <el-dialog
    append-to-body
    title="云配置信息"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    @open="handleOpen">
    <div>
      <el-button v-if="$setAuthority('002018007', 'buttons')" type="primary" size="mini" style="margin-bottom: 10px" @click="handleAddConfig">新增实验室</el-button>
      <el-table
        ref="table"
        :data="tableData"
        class="table"
        size="mini"
        border
        style="width: 100%"
        height="300px"
      >
        <el-table-column type="index" prop="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编码" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="experimentRegion" label="所属实验室" min-width="120" show-overflow-tooltip></el-table-column>
        <!--        云地域-->
        <el-table-column prop="aliyunRegion" label="云地域" min-width="120" show-overflow-tooltip></el-table-column>
        <!--        ak-->
        <el-table-column prop="ak" label="ak" min-width="80" show-overflow-tooltip></el-table-column>
        <!--        sk-->
        <el-table-column prop="sk" label="sk" min-width="80" show-overflow-tooltip></el-table-column>
        <!--        云Bucket-->
        <el-table-column prop="aliyunBucket" label="云Bucket" min-width="120" show-overflow-tooltip></el-table-column>
        <!--        接收邮箱-->
        <el-table-column prop="receiveEmail" label="接收邮箱" min-width="120" show-overflow-tooltip></el-table-column>
        <!--        抄送邮箱-->
        <el-table-column prop="ccEmail" label="抄送邮箱" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="saleEmail" label="销售邮箱" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deliverWay" label="交付方式" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="updateTime" fixed="right" label="操作" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="link" style="margin-right: 10px" @click="handleSetConfig(scope.row)">编辑</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">关  闭</el-button>
    </span>
    <set-aliyun-config-dialog
      :pvisible.sync="aliyunConfigDialogVisible"
      :form-data="formData"
      @dialogConfirmEvent="getData"></set-aliyun-config-dialog>
    <add-lab-config-dialog
      :pvisible.sync="labConfigDialogVisible"
      :experiment-region-list="experimentRegionList"
      :project-code="projectCode"
      @dialogConfirmEvent="getData"></add-lab-config-dialog>
  </el-dialog>
</template>
<script>
import mixins from '../../../../../util/mixins'
import {getDeliveryAliyunConfigList} from '../../../../../api/deliveryManagement'
import util, {awaitWrap} from '../../../../../util/util'
import AddLabConfigDialog from './addLabConfigDialog.vue'

/**
 * 云配置信息弹窗
 */
export default {
  name: 'aliyunConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    AddLabConfigDialog,
    setAliyunConfigDialog: () => import('./setAliyunConfigDialog')
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    auditStatus: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      aliyunConfigDialogVisible: false, // 云配置信息对话框的可见性状态
      labConfigDialogVisible: false, // 实验室配置对话框的可见性状态
      experimentRegionList: [], // 实验室列表
      formData: {}, // 用于存储选中行的数据
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.getData() // 获取云配置信息列表数据
    },
    handleAddConfig () {
      this.experimentRegionList = this.tableData.map(v => v.experimentRegion)
      this.labConfigDialogVisible = true
    },
    // 设置云配置信息
    handleSetConfig (row) {
      this.aliyunConfigDialogVisible = true // 打开云配置信息对话框
      this.formData = row.realData // 设置对话框中的数据为选中行的数据
    },
    async getData () {
      const {res} = await awaitWrap(getDeliveryAliyunConfigList({
        fprojectCode: this.projectCode
      }, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.tableData = []
        data.forEach((v) => {
          const item = {
            id: v.fid,
            projectCode: v.fprojectCode,
            projectName: v.fprojectName,
            experimentRegion: v.fexperimentRegion,
            aliyunRegion: v.faliyunCloudRegion,
            deliverType: v.fdeliverType,
            ak: v.fakValue,
            sk: v.fskValue,
            aliyunBucket: v.faliyunCloudBucket,
            receiveEmail: v.fsendEmail,
            ccEmail: v.fccEmail,
            saleEmail: v.fsaleEmail,
            deliverWay: v.fdeliveryMethod,
            ffinishFlag: v.ffinishFlag,
            fkuaquAkValue: v.fkuaquAkValue,
            fkuaquSkValue: v.fkuaquSkValue,
            fkuaquBucket: v.fkuaquBucket,
            fkehuBucket: v.fkehuBucket,
            fkuaquEndpoint: v.fkuaquEndpoint
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>
