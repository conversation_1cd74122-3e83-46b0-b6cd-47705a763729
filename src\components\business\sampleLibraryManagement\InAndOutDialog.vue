<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="75%"
      @open="resetData">
      <div
        v-loading="loading"
        element-loading-text="正在上传文件"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)">
        <div class="form-container">
          <div class="form-item">
            <label>模板示例</label>
            <el-button type="primary" size="mini" @click="handleDownloadTemplate">下载模板</el-button>
          </div>
          <div class="form-item">
            <label>导入申请单</label>
            <div style="display: flex;">
              <el-upload
                ref="upload"
                :on-success="handleOnSuccess"
                :on-error="handleOnError"
                :data="uploadParams"
                :auto-upload="true"
                :limit="1"
                :file-list="fileList"
                :headers="headers"
                :before-upload="handleBeforeUpload"
                :action="uploadUrl">
                <el-button size="mini" type="primary">点击上传</el-button>
              </el-upload>
              <div v-if="orderType === 2" style="margin-left: 20px;">
                <el-input
                  v-model.trim="inputSampleCodes"
                  size="mini"
                  clearable
                  placeholder="请输入样本编号，逗号隔开"
                  style="width: 300px;"></el-input>
                <el-button size="mini" type="primary" @click="handleImportSampleCode">导入</el-button>
              </div>
            </div>
          </div>
          <div class="form-item">
            <label>所属实验室：</label>
            <el-select size="mini" v-model.trim="lab">
              <el-option v-for="(v, k) in regionObj" :key="k" :label="v" :value="+k"></el-option>
            </el-select>
          </div>
        </div>
        <div>
<!--          <h4>清单</h4>-->
          <div v-if="orderType === 1 && showModifyTemperature">
<!--            <el-alert-->
<!--                title="由于所选样本存在多个存储温度，需要您手动为样本设置温度，请在下面选择框选择温度。"-->
<!--                type="warning"-->
<!--                style="margin: 5px 0"/>-->
<!--            <el-button type="primary" size="mini" @click="handleGetSampleTemperature" :loading="getTemperatureLoading">获取温度</el-button>-->
            <label>存储温度：</label>
            <el-select v-model.trim="temperature" size="mini" style="width: 150px;" :loading="getTemperatureLoading">
              <template v-for="item in temperatureList">
                <el-option :key="item" :label="item" :value="item"></el-option>
              </template>
            </el-select>
            <el-button type="primary" size="mini" @click="handleChooseTemperature">确定选择</el-button>
          </div>
          <el-table
            ref="innerTable"
            :data="tableData"
            v-if="orderType === 1"
            style="width: 100%"
            height="300"
            @select="handleSelect"
            @select-all="handleSelectAll"
            @row-click="(row) => {handleRowClick(row, 'innerTable')}">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本编号" width="220" prop="sampleCode"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本类型" min-width="180" prop="sampleType"></el-table-column>
            <el-table-column show-overflow-tooltip label="存储温度（℃）" width="150" prop="temperature"></el-table-column>
            <el-table-column show-overflow-tooltip label="管型" width="100" prop="pipe"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本量" width="100" prop="sampleAmount"></el-table-column>
            <el-table-column show-overflow-tooltip label="备注" min-width="100" prop="notes"></el-table-column>
          </el-table>
          <el-table
            ref="outTable"
            :data="tableData"
            v-if="orderType === 2"
            style="width: 100%"
            height="300"
            @select="handleSelect"
            @select-all="handleSelectAll"
            @row-click="(row) => {handleRowClick(row, 'outTable')}">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本状态" width="100" prop="sampleStatus"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本编号" width="180" prop="sampleCode"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本类型" min-width="100" prop="sampleType"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本量" width="100" prop="sampleAmount"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本存放位置" min-width="180" prop="samplePosition"></el-table-column>
            <el-table-column show-overflow-tooltip label="备注" min-width="180" prop="notes"></el-table-column>
            <el-table-column show-overflow-tooltip label="到样备注" min-width="180" prop="confirmNotes"></el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
            :loading="submitBtnLoading"
            size="mini"
            type="primary"
            @click="handleDialogSubmit">提交</el-button>
      </div>
      <el-dialog
        :close-on-click-modal="false"
        :visible.sync="showSubmitSuccessDialogVisible"
        title="提示"
        append-to-body
        width="500px">
        <div v-if="showSubmitSuccessDialogVisible" class="success-dialog">
          <p>操作成功！</p>
          <p>本次申请{{type === 'in' ? '入库' : '出库'}}{{selectedRows.size}}个样本</p>
        </div>
        <div style="display: flex;justify-content: center">
          <el-button size="mini" @click="handleCloseSuccessDialog">关闭</el-button>
          <el-button size="mini" :loading="downloading" @click="handleDownloadOrder" type="primary">下载申请单</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util from '../../../util/util'
export default {
  name: 'InAndOutDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: { // in || out 区分出入库
      type: String
    }
  },
  watch: {
    type: function (newVal) {
      switch (newVal) {
        case 'in':
          this.orderType = 1
          this.uploadUrl = constants.JS_CONTEXT + '/sample/order/import_inner_order'
          this.title = '入库申请'
          break
        case 'out':
          this.orderType = 2
          this.title = '出库申请'
          this.uploadUrl = constants.JS_CONTEXT + '/sample/order/import_out_order'
          break
        default:
          this.orderType = ''
          this.uploadUrl = ''
          this.title = ''
      }
      this.$nextTick(this.addTableScrollEvent)
      // this.addTableScrollEvent()
    }
  },
  computed: {
    tableData () {
      return this.allTableData.slice(0, this.currentPage * this.pageSize)
    }
  },
  data () {
    let loginId = util.getSessionInfo('loginId')
    let id = loginId ? util.decryptBase64(loginId) : '1'
    return {
      orderType: '',
      title: '', // dialog标题
      uploadUrl: '',
      headers: {
        'user-id': id
      },
      uploadParams: {},
      fileList: [],
      lab: '',
      loading: false,
      downloading: false,
      submitBtnLoading: false,
      showSubmitSuccessDialogVisible: false,
      // tableData: [],
      allTableData: [],
      inputSampleCodes: '',
      selectedRows: new Map(),
      orderNum: '',
      currentPage: 1,
      pageSize: 50,
      currentSampleType: '', // 当前选择的样本类型
      getTemperatureLoading: false,
      showModifyTemperature: false,
      temperatureList: [
        '4℃', '20℃', '-20℃', '-80℃', '常温'
      ],
      temperature: '',
      regionObj: constants.REGION_OBJ
    }
  },
  methods: {
    // 为表格添加前端分页
    addTableScrollEvent () {
      if (!this.orderType) return
      let tableName = this.orderType === 1 ? 'innerTable' : 'outTable'
      let table = this.$refs[`${tableName}`].$el.querySelector('.el-table__body-wrapper')
      table.addEventListener('scroll', () => {
        let scrollTop = table.scrollTop
        let scrollHeight = table.scrollHeight
        let clientHeight = table.clientHeight
        if (scrollHeight - scrollTop - clientHeight < 1) {
          // this.handleGetMoreSample()
          this.currentPage++
          this.$nextTick(() => { this.selectRows(tableName) })
        }
      })
    },
    // 判断选择的是不是存在多个样本类型
    hasChooseMulSampleType () {
      return new Promise(resolve => {
        const sampleTypeSet = new Set()
        this.selectedRows.forEach(v => {
          sampleTypeSet.add(v.sampleType)
        })
        resolve([...sampleTypeSet])
      })
    },
    // // 反选
    // selectRows (tableName) {
    //   let data = tableName + 'TableAllData'
    //   let select = tableName + 'TableSelection'
    //   let table = tableName + 'Table'
    //   this[data].forEach(v => {
    //     this.$refs[table].toggleRowSelection(v, false)
    //   })
    //   this[data].forEach(v => {
    //     if (this[select].has(v.id)) {
    //       this.$refs[table].toggleRowSelection(v, true)
    //     }
    //   })
    // },
    // 重置基本信息
    resetData () {
      this.lab = ''
      this.loading = false
      this.allTableData = []
      this.inputSampleCodes = ''
      this.selectedRows.clear()
      this.currentSampleType = ''
      this.showModifyTemperature = false
      // this.temperatureList = []
      this.temperature = ''
    },
    // 下载模板
    handleDownloadTemplate () {
      let form = document.createElement('form')
      form.action = constants.JS_CONTEXT + '/sample/order/download_order_template'
      form.method = 'get'
      form.id = 'form'
      let submitData = {
        orderType: this.orderType
      }
      for (let key in submitData) {
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = submitData[key]
        form.appendChild(input)
      }
      document.body.appendChild(form)
      form.submit()
      form.parentNode.removeChild(form)
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data || []
        let tableDataMap = new Map()
        let hasEmptyTemperature = false // 是否存在一个数据没有空温度
        data.forEach(v => {
          if (this.orderType === 1) {
            let item = {
              id: v.fid,
              sampleCode: v.fsampleNumber,
              sampleType: v.fsampleType,
              temperature: v.ftemperature,
              pipe: v.ftubeType,
              sampleAmount: v.fsampleAmount,
              confirmNotes: v.fconfirmNotes,
              notes: v.fnotes
            }
            hasEmptyTemperature = hasEmptyTemperature || !v.ftemperature
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            tableDataMap.set(item.sampleCode, item)
          } else if (this.orderType === 2) {
            let item = {
              id: v.fid,
              sampleCode: v.fsampleNumber,
              sampleStatus: v.fsampleStatus,
              sampleType: v.fsampleType,
              sampleAmount: v.fsampleAmount,
              samplePosition: v.fsamplePlace,
              confirmNotes: v.fconfirmNotes,
              isPreciousSample: v.fisPreciousSample,
              notes: v.fnotes
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            tableDataMap.set(item.sampleCode, item)
          }
        })
        this.allTableData = [...tableDataMap.values()]
        this.showModifyTemperature = this.showModifyTemperature || hasEmptyTemperature
        this.$nextTick(() => {
          this.selectRows()
        })
      } else {
        this.$showErrorDialog({
          tableData: res.data || []
        })
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 8) {
          this.loading = false
          this.$message.error('文件大小超过限制，无法上传')
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    },
    // 导入样本编号
    handleImportSampleCode () {
      if (!this.inputSampleCodes) {
        this.$message.error('样本编号不能为空')
        return
      }
      let codes = this.inputSampleCodes.replace(/\s+/g, ',').replace(/，/g, ',')
      this.$ajax({
        url: '/sample/get_sample_by_fsampleNumber',
        method: 'get',
        data: {
          fsampleNumbers: codes
        },
        loadingDom: '.form-container'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          let tableDataMap = new Map()
          this.allTableData.forEach(item => {
            tableDataMap.set(item.sampleCode, item)
          })
          // this.tableData = []
          data.forEach(v => {
            let item = {
              id: v.fid,
              sampleCode: v.fsampleNumber,
              sampleStatus: v.fsampleStatus,
              sampleType: v.fsampleType,
              sampleAmount: v.fsampleAmount,
              samplePosition: v.fsamplePlace,
              isPreciousSample: v.fisPreciousSample,
              confirmNotes: v.fconfirmNotes,
              notes: v.fnotes
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            tableDataMap.set(item.sampleCode, item)
          })
          this.allTableData = [...tableDataMap.values()]
          this.inputSampleCodes = ''
          this.$nextTick(() => {
            this.selectRows()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取温度
    async handleGetSampleTemperature () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择样本')
        return
      }
      const mulType = await this.hasChooseMulSampleType()
      if (mulType.length > 1) {
        this.$message.error('只能选择同一种样本类型进行操作')
        return
      }
      this.getSampleTypeTemperature(mulType[0])
    },
    // 获取温度
    async getSampleTypeTemperature (type) {
      this.currentSampleType = type
      this.getTemperatureLoading = true
      try {
        const { code, data, message } = await this.$ajax({
          url: '/sample/sample_type/get_temperature_by_sample_type',
          method: 'get',
          data: {
            sampleType: type
          }
        })
        code === this.SUCCESS_CODE ? this.temperatureList = data || [] : this.$message.error(message)
      } finally {
        this.getTemperatureLoading = false
      }
    },
    // 选择温度
    async handleChooseTemperature () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择样本')
        return
      }
      if (!this.temperature) {
        this.$message.error('请选择温度')
        return
      }
      // const mulType = await this.hasChooseMulSampleType()
      // if (mulType.length > 1) {
      //   this.$message.error('只能选择同一种样本类型进行操作')
      //   return
      // }
      // if (mulType[0] !== this.currentSampleType) {
      //   this.$alert(`当前温度列表是${this.currentSampleType}的温度，您选中的是样本类型是${mulType[0]}，请重新获取温度进行操作`, '提示', {type: 'error'})
      //   return
      // }
      await this.$confirm(`确定将所选的的样本的存储温度设置为${this.temperature}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.allTableData.forEach(v => {
        if (this.selectedRows.has(v.sampleCode)) {
          v.temperature = this.temperature
        }
      })
    },
    // 点击表格行
    handleRowClick (row, table) {
      this.$refs[table].toggleRowSelection(row, !this.selectedRows.has(row.sampleCode))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.sampleCode) ? this.selectedRows.delete(row.sampleCode) : this.selectedRows.set(row.sampleCode, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      if (selection.length > 0) {
        this.allTableData.forEach((row) => {
          this.selectedRows.set(row.sampleCode, row)
        })
      }
    },
    // 反选表格
    selectRows () {
      if (!this.orderType) return
      let sub = this.orderType === 1 ? 'inner' : 'out'
      this.selectedRows.forEach(v => {
        this.$refs[sub + 'Table'].toggleRowSelection(v, false)
      })
      this.tableData.forEach(v => {
        if (this.selectedRows.has(v.subId)) {
          this.$refs[sub + 'Table'].toggleRowSelection(v, true)
        }
      })
    },
    // 提交
    async handleDialogSubmit () {
      if (this.selectedRows.size === 0) {
        this.$message.error('未选择样本')
        return
      }
      if (!this.lab) {
        this.$message.error('请选择实验室')
        return
      }
      let url = ''
      let data = { // 提交选择的实验室
        flab: this.lab
      }
      let selectedRows = [...this.selectedRows.values()]
      if (this.orderType === 1) {
        if (selectedRows.some(v => !v.temperature)) {
          this.$message.error('选中的样本中存在未设置温度的样本，请检查！')
          return
        }
        url = '/sample/order/submit_inner_order'
        data.sampleList = []
        selectedRows.forEach(item => {
          let v = {
            fsampleNumber: item.sampleCode,
            fsampleType: item.sampleType,
            ftemperature: item.temperature,
            ftubeType: item.pipe,
            fsampleAmount: item.sampleAmount,
            fnotes: item.notes
          }
          data.sampleList.push(v)
        })
      } else if (this.orderType === 2) {
        url = '/sample/order/submit_out_order'
        let codes = []
        selectedRows.forEach(item => {
          let v = {
            fsampleNumber: item.sampleCode,
            fnotes: item.notes
          }
          codes.push(v)
        })
        data.sampleInfoList = codes
        // 判断出库的是否是珍贵样本
        const preciousList = selectedRows.filter(v => v.isPreciousSample === 1)
        if (preciousList.length > 0) {
          const message =
            `${preciousList.map(v => v.sampleCode).join(',')}
             <br/>
             为珍贵样本，请确认是否出库操作？`
          await this.$confirm(message, '珍贵样本提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        }
      }
      this.submitBtnLoading = true
      this.$ajax({
        url: url,
        data: data
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.orderNum = res.data
          this.showSubmitSuccessDialogVisible = true
        } else {
          this.$showErrorDialog({tableData: res.data})
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    },
    // 下载申请单
    handleDownloadOrder () {
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${this.orderNum}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        }).finally(() => {
          this.downloading = false
        })
      })
    },
    // 关闭
    handleCloseSuccessDialog () {
      this.showSubmitSuccessDialogVisible = false
      this.handleClose()
    }
  }
}
</script>

<style scoped lang="scss">
.form-container{
  .form-item{
    display: flex;
    align-items: start;
    margin-bottom: 20px;
    label{
      margin-right: 30px;
      display: block;
      width: 7em;
    }
  }
}
  .success-dialog{
    font-size: 16px;
    color: #000;
    font-weight: 600;
    text-align: center;
    line-height: 2;
    margin-bottom: 20px;
  }
</style>
