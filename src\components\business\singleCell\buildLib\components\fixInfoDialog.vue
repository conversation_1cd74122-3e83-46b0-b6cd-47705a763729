<template>
    <el-dialog
      title="信息修改"
      :visible.sync="visible"
      width="1000px"
      :close-on-click-modal="false"
      :before-close="handleClose"
      class="sample-modify-dialog">

      <el-form :model="validSamples" ref="sampleForm" :rules="rules">
        <el-table
          :data="validSamples"
          border
          size="mini"
          height="300"
          class="sample-table">
          <el-table-column prop="fgeneNum" label="吉因加编号" min-width="120"></el-table-column>
          <el-table-column prop="fcosSampleName" label="原始样本名称" min-width="150"></el-table-column>
          <el-table-column prop="flibType" label="文库类型" min-width="100"></el-table-column>
          <el-table-column label="文库浓度" min-width="120">
            <template slot-scope="scope">
              <el-form-item :prop="scope.$index + '.flibConcentration'" :rules="rules.flibConcentration">
                <el-input
                  v-model="scope.row.flibConcentration"
                  placeholder="请输入"
                  size="mini"
                  >
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="出库体积" min-width="120">
            <template slot-scope="scope">
              <el-form-item :prop="scope.$index + '.fcyclizationOutgoningVolume'" :rules="rules.fcyclizationOutgoningVolume">
                <el-input
                  v-model="scope.row.fcyclizationOutgoningVolume"
                  placeholder="请输入"
                  size="mini"
                 >
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="环化备注" min-width="150">
            <template slot-scope="scope">
              <el-form-item :prop="scope.$index + '.fcyclizationRemark'" :rules="rules.fcyclizationRemark">
                <el-input
                  v-model="scope.row.fcyclizationRemark"
                  placeholder="请输入"
                  size="mini"
                  maxlength="20"
                  show-word-limit>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini">确认</el-button>
      </span>
    </el-dialog>
  </template>

<script>
import mixins from '@/util/mixins'
import { awaitWrap } from '../../../../../util/util'
import {fixInfoApi} from '../../../../../api/sequencingManagement/singleCell'

export default {
  name: 'SampleInfoModifyDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    // 选中的样本数组
    samples: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    samples: {
      handler (newSamples) {
        this.processSelectedSamples(newSamples)
      },
      immediate: true,
      deep: true
    }
  },
  data () {
    const validateFlibConcentration = (rule, value, callback) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('文库浓度不能为空'))
        return
      }
      const numValue = Number(value)
      if (isNaN(numValue)) {
        callback(new Error('请输入数字'))
        return
      }
      const strValue = String(value)
      if (strValue.includes('.') && strValue.split('.')[1].length > 1) {
        callback(new Error('小数点后最多1位'))
        return
      }
      callback()
    }
    const validateFcyclizationOutgoningVolume = (rule, value, callback) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('出库体积不能为空'))
        return
      }
      const numValue = Number(value)
      if (isNaN(numValue)) {
        callback(new Error('请输入数字'))
        return
      }
      const strValue = String(value)
      if (strValue.includes('.') && strValue.split('.')[1].length > 2) {
        callback(new Error('小数点后最多2位'))
        return
      }
      callback()
    }
    const validateFcyclizationRemark = (rule, value, callback) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('环化备注不能为空'))
        return
      }
      if (value.length > 20) {
        callback(new Error('不能超过20个字符'))
        return
      }
      callback()
    }

    return {
      validSamples: [], // 符合条件的样本（达标为否且文库类型为oligo）
      nonOligoSamples: [], // 非oligo文库的样本
      internalSamples: [], // 用于内部处理的样本数据
      rules: {
        flibConcentration: [
          { required: true, message: '请输入文库浓度', trigger: 'blur' },
          { validator: validateFlibConcentration, trigger: 'blur' }
        ],
        fcyclizationOutgoningVolume: [
          { required: true, message: '请输入出库体积', trigger: 'blur' },
          { validator: validateFcyclizationOutgoningVolume, trigger: 'blur' }
        ],
        fcyclizationRemark: [
          { required: true, message: '请输入环化备注', trigger: 'blur' },
          { validator: validateFcyclizationRemark, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    // 处理选中的样本，筛选出符合条件的样本
    processSelectedSamples (samples) {
      // 筛选出文库类型为oligo的样本
      this.validSamples = JSON.parse(JSON.stringify(samples.map(v => v.realData)))
    },

    // 验证所有字段是否已填写
    validateAllFields () {
      return new Promise((resolve) => {
        this.$refs.sampleForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 处理关闭对话框
    handleClose () {
      this.visible = false
      // 不保存修改
      this.$refs.sampleForm && this.$refs.sampleForm.resetFields()
    },

    // 处理确认修改
    async handleConfirm () {
      if (this.validSamples.length === 0) {
        this.handleClose()
        return
      }

      // 验证所有字段
      const valid = await this.validateAllFields()
      if (valid) {
        const params = this.validSamples.map(v => {
          return {
            fnewLibId: v.fid,
            flibConcentration: v.flibConcentration,
            fcyclizationRemark: v.fcyclizationRemark,
            fcyclizationOutgoningVolume: v.fcyclizationOutgoningVolume
          }
        })
        // 保存修改
        const {res} = await awaitWrap(fixInfoApi({
          ftsNewLibReqs: params
        }))
        if (res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.$emit('dialogConfirmEvent')
          this.handleClose()
        }
      } else {
        this.$message.error('请填写完整有效的信息')
      }
    }
  }
}
</script>

  <style scoped lang="scss">
  .sample-modify-dialog {
    max-height: 90vh;
  }

  .sample-modify-dialog >>> .el-dialog {
    margin-top: 5vh !important;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    border-radius: 4px;
  }

  .sample-modify-dialog >>> .el-dialog__body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    max-height: calc(90vh - 150px);
  }

  .sample-modify-dialog >>> .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #EBEEF5;
  }

  .sample-modify-dialog >>> .el-dialog__footer {
    padding: 10px 20px;
    border-top: 1px solid #EBEEF5;
  }

  .error-message {
    margin-bottom: 15px;
  }

  .sample-table {
    margin-top: 15px;
  }

  .sample-table >>> .el-input-group__append {
    padding: 0 10px;
  }

  /* 表单元素样式 */
  .no-margin.el-form-item {
    margin-bottom: 0;
  }

  /* 确保所有表单元素统一大小 */
  .sample-modify-dialog >>> .el-input,
  .sample-modify-dialog >>> .el-select,
  .sample-modify-dialog >>> .el-button {
    font-size: 12px;
  }

  /* 表单项样式优化 */
  .sample-modify-dialog >>> .el-input__inner,
  .sample-modify-dialog >>> .el-textarea__inner {
    border-radius: 3px;
    border-color: #DCDFE6;
  }

  /* 显示表单错误信息的样式 */
  .sample-modify-dialog >>> .el-form-item__error {
    position: absolute;
    top: 100%;
    left: 0;
    font-size: 12px;
    z-index: 2;
    background-color: #fff;
    padding: 2px 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  }

  /deep/ .el-form-item {
    margin: 10px 0;

}

  /deep/ .is-error {
      margin: 0 0 20px 0 !important;
    }
  </style>
