<template>
  <div>
    <el-dialog
      v-drag-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="快递信息"
      width="700px"
      @open="handleOpen">
      <div v-loading="expressLoading">
        <div style="margin-bottom: 20px; color: #3c3838; font-size: 16px; padding: 0 20px;">快递单号：{{expressCode}}</div>
        <div class="content">
          <div v-if="info.length > 0">
            <el-timeline :reverse="true">
              <el-timeline-item
                :key="index"
                :timestamp="activity.acceptTime"
                v-for="(activity, index) in info">
                <div style="color: #3c3838; font-size: 14px;">【{{activity.acceptAddress}}】{{activity.remark}}</div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else>
            <el-empty description="暂无物流信息"></el-empty>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'

export default {
  name: 'expressInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    /**
     * 快递单号
     * */
    code: {
      type: String
    }
  },
  data () {
    return {
      expressLoading: false,
      expressCode: '', // 运单号
      info: []
    }
  },
  methods: {
    handleOpen () {
      this.expressCode = this.code
      this.getData()
    },
    // 获取快递物流信息
    getData () {
      this.expressLoading = true
      this.$ajax({
        url: '/order/get_express_detail_info',
        method: 'get',
        data: {
          fexpressCode: this.code
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.info = JSON.parse(res.data) || []
        }
      }).finally(() => {
        this.expressLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  height: 300px;
  padding: 0 20px;
  overflow: auto;
}
</style>
