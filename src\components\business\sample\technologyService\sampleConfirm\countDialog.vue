<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="数据统计"
      width="800px"
      @open="handleOpen"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="90px"
        label-suffix=":"
        size="mini"
        inline
      >
        <el-form-item label="起止时间" prop="time">
          <el-date-picker
            v-model="form.time"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import mixins from '../../../../../util/mixins'
import util from '../../../../../util/util'

export default {
  name: 'countDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    pvisible: {
      type: Boolean
    }
  },
  data () {
    return {
      loading: false,
      tableData: [],
      form: {
        time: []
      },
      rules: {
        time: [{
          required: true, message: '请选择日期', trigger: ['change', 'blur']
        }]
      }
    }
  },
  methods: {
    handleOpen () {
      this.time = []
    },

    // 下载记录
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.time = this.form.time || []
          this.loading = true
          this.$ajax({
            url: '/sample/confirm/cos_data_statistic',
            method: 'post',
            data: {
              timeStart: this.form.time[0],
              timeEnd: this.form.time[1]
            },
            responseType: 'blob'
          }).then(res => {
            util.readBlob(res.data).then(() => {
              util.downloadFile(res, true)
            }).catch(msg => {
              this.$message.error(msg)
            })
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
