<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="编辑匹配癌种"
      width="35%">
      <div>
        <div class="buttonGroup">
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAdd">新增</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete">删除</el-button>
        </div>
        <div class="table">
          <el-table
            ref="table"
            :data="tableData"
            size="mini"
            height="200"
            style="width: 100%"
            @select="handleSelect"
            @select-all="handleSelectAll"
            @row-click="handleRowClick">
            <el-table-column type="selection" width="45"></el-table-column>
            <el-table-column prop="cancer" label="癌种"></el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <select-cancer-dialog
      :pvisible="selectCancerDialogVisible"
      @selectCancerDialogCloseEvent="handleSelectCancerDialogClose"
      @selectCancerDialogConfirmEvent="handleSelectCancerDialogConfirm"
    ></select-cancer-dialog>
  </div>
</template>

<script>
import selectCancerDialog from './common/selectCancerDialog'
export default {
  name: 'wesReportEditMatchCanterDialog',
  components: {selectCancerDialog},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.tableData = this.pdata
      } else {
        this.tableData = []
      }
    }
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      tableData: [],
      selectedRows: [],
      form: [],
      cancerRules: [
        {required: true, message: '请输入匹配癌种', trigger: 'blur'}
      ],
      selectCancerDialogVisible: false
    }
  },
  methods: {
    handleClose () {
      this.$emit('editMatchCancerDialogCloseEvent')
      this.tableData = []
      this.selectedRows = []
    },
    handleConfirm () {
      let ids = this.tableData.map(v => { return v.cancerId })
      this.loading = true
      this.$ajax({
        url: '/read/unscramble/save_check_out_gene_list_result',
        data: {
          analysisRsId: this.analysisRsId,
          cancerIds: ids.join(';')
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('editMatchCancerDialogConfirmEvent')
          this.tableData = []
          this.selectedRows = []
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleDelete () {
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择数据')
      } else {
        let ids = this.selectedRows.map(v => { return v.cancerId })
        let newTableData = []
        this.tableData.forEach(v => {
          if (ids.indexOf(v.cancerId) === -1) {
            newTableData.push(v)
          }
        })
        this.selectedRows = []
        this.tableData = newTableData
      }
    },
    handleAdd () {
      this.selectCancerDialogVisible = true
    },
    handleSelectAll (selection) {
      this.selectedRows = []
      selection.forEach((v, i) => {
        this.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = this.selectedRows.indexOf(row)
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs.table.toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs.table.toggleRowSelection(row, true)
      }
    },
    handleSelectCancerDialogClose () {
      this.selectCancerDialogVisible = false
    },
    handleSelectCancerDialogConfirm (data) {
      this.selectCancerDialogVisible = false
      this.tableData.push(data)
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-dialog__body {
    padding: 10px 20px;
  }
  .buttonGroup{
    height: 40px;
    line-height: 40px;
  }
  .table{
    >>>.el-table th{
      background-color: rgba(0, 0, 0, 0);
    }
    >>>.el-table thead{
      font-size: 14px;
      font-weight: 500;
      color: #909399;
    }
  }
</style>
