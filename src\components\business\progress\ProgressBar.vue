<template>
  <div v-if="visible" class="pb-wrap">
    <div class="pb-bar">
      <div class="pb-fill" :style="{ width: percent + '%' }"></div>
    </div>
    <div class="pb-text">
      <slot>{{ text }}</slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressBar',
  props: {
    value: { type: Number, default: 0 }, // 0~1
    visible: { type: Boolean, default: false },
    text: { type: String, default: '加载中...' }
  },
  computed: {
    percent () {
      const v = Math.max(0, Math.min(1, this.value))
      return Math.round(v * 100)
    }
  }
}
</script>

<style scoped>
.pb-wrap{
  position: relative;
  width: 100%;
  background: transparent;
  padding: 8px 0;
  color: #303133;
}
.pb-bar{
  height: 6px;
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}
.pb-fill{
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #79bbff);
  transition: width .2s ease;
}
.pb-text{
  margin-top: 6px;
  font-size: 12px;
  color: #606266;
  text-align: right;
}
</style>
