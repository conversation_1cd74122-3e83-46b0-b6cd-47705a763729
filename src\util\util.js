import CryptoJS from 'crypto-js'
import Cookies from 'js-cookie'
import route from '@/router'
import { Message } from 'element-ui'
import geneplusValidate from 'geneplus_valid'
import Decimal from 'decimal.js'

/**
 * 验证手机号码格式是否正确
 * @param rule element-ui表单验证规则
 * @param value 需要验证的值
 * @param callback 回调函数
 */
const validateElementPhone = (rule, value, callback) => {
  // if (value && !geneplusValidate.phoneValidate(value)) {
  //   callback(new Error('手机号码格式不正确'))
  // } else {
  //   callback()
  // }
  if (value) {
    let list = setGroupData(value, '、', false)
    list.forEach(phone => {
      if (phone && !geneplusValidate.phoneValidate(phone)) {
        callback(new Error('手机号码格式不正确'))
      }
    })
    callback()
  }
  callback()
}

/**
 * 计算对象中有效值的数量
 * @param obj 目标对象
 * @param excludeKeys 需要排除的键名数组
 * @returns {number} 有效值的数量
 */
export const computeObjectValidKeyNum = (obj, excludeKeys = []) => {
  const keys = Object.keys(obj)
  const hasValueNum = []
  keys.forEach(k => {
    if (!excludeKeys.includes(k)) {
      const v = obj[k]
      // v = 0视为有true
      // v = [] 视为 false
      // !!v 视为true
      const isValidValue = v === 0 || (Array.isArray(v) ? v.filter(v => v || v === 0).length > 0 : !!v)
      if (isValidValue) {
        console.log(k)
      }
      if (isValidValue) hasValueNum.push(k)
    }
  })
  return hasValueNum.length
}

/**
 * 验证多个邮箱地址格式是否正确
 * @param rule element-ui表单验证规则
 * @param value 需要验证的值(多个邮箱以顿号分隔)
 * @param callback 回调函数
 */
const validateElementEmail = (rule, value, callback) => {
  if (value) {
    let list = setGroupData(value, '、', false)
    list.forEach(email => {
      if (email && !geneplusValidate.emailValidate(email)) {
        callback(new Error('邮箱格式不正确'))
      }
    })
    callback()
  }
  callback()
}

// 设置sessionStorage
export let setSessionInfo = function (key, value) {
  let storage = window.sessionStorage
  let svalue = {
    data: value
  }
  storage.setItem(key, JSON.stringify(svalue))
}
// 获取sessionStorage
export let getSessionInfo = function (key) {
  let storage = window.sessionStorage
  let svalue = storage.getItem(key)
  let value = ''
  if (svalue !== undefined && svalue !== null && svalue !== '') {
    let tmp = JSON.parse(svalue)
    value = tmp.data
  }
  return value
}
// 移除sessionStorage
export let removeSessionInfo = function (key) {
  let storage = window.sessionStorage
  storage.removeItem(key)
}

// 设置cookie
let setCookie = function (cname, cvalue, exdays) {
  // cvalue = encodeURI(cvalue)
  let val = `${cname}=${cvalue}`
  if (exdays !== undefined && exdays !== null && exdays !== 0) {
    let d = new Date()
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000))
    let expires = 'expires=' + d.toUTCString()
    val = `${val};${expires}`
  }
  document.cookie = val + ';path=/'
}
let getCookie = function (cname) {
  let name = cname + '='
  let ca = document.cookie.split(';')
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') c = c.substring(1)
    // if (c.indexOf(name) !== -1) return decodeURI(c.substring(name.length, c.length))
    if (c.indexOf(name) !== -1) return c.substring(name.length, c.length)
  }
  return ''
}
function getTimeDiff (time, isAllTime = false) {
  let time1 = new Date(time)
  if (isAllTime) {
    let y = time1.getFullYear()
    let m = time1.getMonth() + 1
    let d = time1.getDate()
    let h = time1.getHours()
    let min = time1.getMinutes()
    let s = time1.getSeconds()
    m = m < 10 ? '0' + m : m
    d = d < 10 ? '0' + d : d
    h = h < 10 ? '0' + h : h
    min = min < 10 ? '0' + min : min
    s = s < 10 ? '0' + s : s
    return `${y}-${m}-${d} ${h}:${min}:${s}`
  } else {
    let now = new Date().getTime()
    let sub = (now - time1) / 1000
    if (sub < 60) {
      return '刚刚'
    } else if (sub < 60 * 60) {
      return Math.floor(sub / 60) + '分钟前'
    } else if (sub < 60 * 60 * 24) {
      return Math.floor(sub / (60 * 60)) + '小时前'
    } else {
      let y = time1.getFullYear()
      let m = time1.getMonth() + 1
      let d = time1.getDate()
      m = m < 10 ? '0' + m : m
      d = d < 10 ? '0' + d : d
      return `${y}-${m}-${d}`
    }
  }
}

let keyIv = 'geneplus-bms^#@!' // 必须为16位
let options = {
  iv: CryptoJS.enc.Utf8.parse(keyIv),
  mode: CryptoJS.mode.CBC,
  padding: CryptoJS.pad.ZeroPadding
}
let encryptAES = function (word) {
  let key = CryptoJS.enc.Utf8.parse(keyIv)
  let srcs = CryptoJS.enc.Utf8.parse(word)
  let encrypted = CryptoJS.AES.encrypt(srcs, key, options)
  return encrypted.toString()
}
let decryptAES = function (word) {
  let key = CryptoJS.enc.Utf8.parse(keyIv)
  let decrypt = CryptoJS.AES.decrypt(word, key, options)
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}
let encryptBase64 = function (word) {
  let wordArry = CryptoJS.enc.Utf8.parse(word)
  return CryptoJS.enc.Base64.stringify(wordArry)
}

let decryptBase64 = function (word) {
  let parsedWordArray = CryptoJS.enc.Base64.parse(word)
  return parsedWordArray.toString(CryptoJS.enc.Utf8)
}

function randomCode (num = 6) {
  let letter = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
  let text = ''
  for (let i = 0; i < num; i++) {
    text += letter[Math.floor(Math.random() * 36)]
  }
  return text
}

function getAge (birthday) {
  if (!birthday) return ''
  let currentYear = new Date().getFullYear() // 当前的年份
  let calculationYear = new Date(birthday).getFullYear() // 计算的年份
  const wholeTime = currentYear + birthday.substring(4) // 周岁时间
  const calculationAge = currentYear - calculationYear // 按照年份计算的年龄
  // 判断是否过了生日
  if (new Date().getTime() > new Date(wholeTime).getTime()) {
    return calculationAge
  } else {
    return calculationAge - 1
  }
}
/**
 * 异步函数的错误处理包装器
 * @param promise Promise对象
 * @returns {Promise<{err: null, res: *}|{err: *, res: null}>}
 */
export const awaitWrap = (promise) => {
  return promise
    .then(data => {
      return { err: null, res: data }
    })
    .catch(err => {
      return { err, res: null }
    })
}
let validatePhone = function (rule, value, callback) {
  if (value === '' || value === null || value === undefined) {
    callback()
  } else {
    let match = /(^(0[0-9]{2,3})?(-)?([2-9][0-9]{6,7})+(-[0-9]{1,4})?$)|(^((\d3)|(\d{3}-))?([149][3456789]\d{9})$)/
    if (!(match.test(value))) {
      callback(new Error('格式错误'))
    } else {
      callback()
    }
  }
}

let validateIdCard = function (rule, value, callback) {
  if (value === '' || value === null || value === undefined) {
    callback()
  } else {
    let match = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    if (!(match.test(value))) {
      callback(new Error('格式错误'))
    } else {
      callback()
    }
  }
}

let identityNumberValid = function (code) {
  let city = {
    11: '北京',
    12: '天津',
    13: '河北',
    14: '山西',
    15: '内蒙古',
    21: '辽宁',
    22: '吉林',
    23: '黑龙江',
    31: '上海',
    32: '江苏',
    33: '浙江',
    34: '安徽',
    35: '福建',
    36: '江西',
    37: '山东',
    41: '河南',
    42: '湖北',
    43: '湖南',
    44: '广东',
    45: '广西',
    46: '海南',
    50: '重庆',
    51: '四川',
    52: '贵州',
    53: '云南',
    54: '西藏',
    61: '陕西',
    62: '甘肃',
    63: '青海',
    64: '宁夏',
    65: '新疆',
    71: '台湾',
    81: '香港',
    82: '澳门',
    91: '国外 '
  }
  let tip = ''
  let pass = true

  if (!code || !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/i.test(code)) {
    tip = '身份证号格式错误'
    pass = false
  } else if (!city[code.substr(0, 2)]) {
    tip = '地址编码错误'
    pass = false
  } else {
    // 18位身份证需要验证最后一位校验位
    if (code.length === 18) {
      // ∑(ai×Wi)(mod 11)
      // 加权因子
      let factor = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ]
      // 校验位
      let parity = [ 1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2 ]
      let sum = 0
      let ai = 0
      let wi = 0
      for (let i = 0; i < 17; i++) {
        ai = code[i]
        wi = factor[i]
        sum += ai * wi
      }
      let last = parity[sum % 11] + ''
      if (last !== code[17]) {
        tip = '校验位错误'
        pass = false
      }
    }
  }
  return {
    pass: pass,
    message: tip
  }
}

// 移除登录数据
let clearLoginData = function () {
  removeSessionInfo('userInfo')
  Cookies.remove('token')
}

let openNewPage = (page) => { // 新开一个页面, page是路径
  let routeData = route.resolve({
    path: page
  })
  let p = window.open()
  if (p !== null) {
    p.location.replace(routeData.href, '_blank')
  } else {
    Message.error('页面被浏览器拦截！您可以在浏览器中选择信任该网站')
  }
}
/**
 * 深拷贝 JSON 方式 无法拷贝函数
 * @param v 深拷贝对象
 * @returns {Object} 目标对象
 */
export const deepCopy = function (v) {
  return JSON.parse(JSON.stringify(v))
}

export const setDefaultEmptyValueForObject = function (obj) {
  Object.keys(obj).forEach(function (key) {
    if (obj[key] === null || obj[key] === '' || obj[key] === undefined) {
      obj[key] = '-'
    }
  })
}
// 读取blob数据
export let readBlob = function (blob) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = () => {
      try {
        let data = JSON.parse(reader.result)
        if (data.code) {
          reject(data.message || '出现错误')
        }
      } catch (e) {
        resolve()
      }
    }
  })
}

/**
 * 下载文件
 * @param res 响应对象
 * @param isDecode 是否需要解码文件名
 * @param filename 默认文件名
 */
export const downloadFile = function (res, isDecode = false, filename) {
  if (!res.data) {
    return
  }
  let blob = new Blob([res.data])
  let contentDisposition = res.headers['content-disposition']
  console.log(contentDisposition)
  let fileName = contentDisposition ? contentDisposition.substring(contentDisposition.indexOf('=') + 1) : filename
  fileName = decodeURIComponent(fileName)
  if ('download' in document.createElement('a')) { // 不是IE浏览器
    let url = window.URL.createObjectURL(blob)
    let link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link) // 下载完成移除元素
    window.URL.revokeObjectURL(url) // 释放掉blob对象
  } else { // IE 10+
    window.navigator.msSaveBlob(blob)
  }
}

// 添加样本标签
const addSampleTag = status => {
  if (+status === 7) return {name: '风', type: 'danger'}
  if (+status === 8) return {name: '停', type: 'danger'}
  return false
}

// 根据时间数获取相应的时间
export function dateFormatter (str, hasTime = true, timeCustomization = null, connector = '-') { // 默认返回yyyy-MM-dd HH:mm:ss // 可传第二个参数false，返回yyyy-MM-dd // 传第三个参数自定义返回的时间节点
  let d = new Date(str)
  let year = d.getFullYear()
  let month = (d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : (d.getMonth() + 1)
  let day = d.getDate() < 10 ? '0' + d.getDate() : d.getDate()
  let hour = d.getHours() < 10 ? '0' + d.getHours() : d.getHours()
  let minute = d.getMinutes() < 10 ? '0' + d.getMinutes() : d.getMinutes()
  let second = d.getSeconds() < 10 ? '0' + d.getSeconds() : d.getSeconds()
  if (timeCustomization) {
    return [year, month, day].join(connector) + ' ' + timeCustomization
  }
  if (hasTime) {
    return [year, month, day].join(connector) + ' ' + [hour, minute, second].join(':')
  } else {
    return [year, month, day].join(connector)
  }
}

// 复制的方法
let copyText = function copyText (text, callback) { // text: 要复制的内容， callback: 回调
  return new Promise(resolve => {
    let tag = document.createElement('input')
    tag.setAttribute('id', 'cp_gp_input')
    tag.value = text
    document.getElementsByTagName('body')[0].appendChild(tag)
    document.getElementById('cp_gp_input').select()
    document.execCommand('copy')
    document.getElementById('cp_gp_input').remove()
    resolve()
  })
}

function closePage () {
  if (navigator.userAgent.indexOf('Firefox') !== -1 || navigator.userAgent.indexOf('Chrome') !== -1) {
    window.location.href = 'about:blank'
    window.close()
  } else {
    window.opener = null
    window.open('', '_self')
    window.close()
  }
}

export const formatYearMonth = (str) => {
  return str.replace(/^(\d{4})-(\d{2})$/, '$1年$2月')
}

/**
 * this.tableData数组中每一项的每一个属性的类型，如果是数字则该属性对应的列每一行都靠右显示，
 * 否则居中显示；如果某一项的属性为空（undefined/null）则判断下一个项中该属性的类型，直到找出
 * 有一项的该属性的类型不为空，用这个得出的属性作为这一列的每一行的居中/居右的判断依据。
 * */
export const getTypeArray = (data = []) => {
  const keys = Object.keys(data[0])

  const typeObj = keys.reduce((acc, key) => {
    let type = typeof data[0][key]
    if (!Number.isNaN(+data[0][key])) {
      type = 'number'
    } else if (type === 'object' && data[0][key] === null) {
      type = 'null'
    }

    for (let i = 1; i < data.length; i++) {
      const value = data[i][key]
      if (value !== undefined && value !== null) {
        let valueType = ''
        if (!Number.isNaN(+value)) {
          valueType = 'number'
        } else {
          valueType = typeof value
        }
        if (valueType === 'object' && value === null) {
          type = 'null'
        } else {
          type = valueType
        }
        break
      }
    }

    acc[key] = type === 'number' ? 'right' : 'center'
    return acc
  }, {})

  return typeObj
}

/**
 * 分割符统一
 * @param text 分割文本
 * @param splitChar 转换后的分割符
 * @param hasJoin splitChar是否需要拼接
 * @returns {*}
 */
export function setGroupData (text = '', splitChar = '、', hasJoin = true) {
  let groupData = text.replace(/\s+|;|；|，|,|、/g, splitChar).split(splitChar).filter(v => v)
  if (hasJoin) {
    groupData = groupData.join(splitChar)
  }
  return groupData
}

function isNumber (num) {
  return /^[0-9]+.?[0-9]*$/.test(num)
}

function add (a, b) {
  if (!isNumber(a) || !isNumber(b)) return ''
  return new Decimal(a).add(new Decimal(b)).toString()
}

function sub (a, b) {
  if (!isNumber(a) || !isNumber(b)) return ''
  return new Decimal(a).sub(new Decimal(b)).toString()
}

export function mul (a, b) {
  if (!isNumber(a) || !isNumber(b)) return ''
  return new Decimal(a).mul(new Decimal(b)).toString()
}

/**
 * 将数值限制在指定范围内
 * @param {Number} value 要限制的值
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @returns {Number} 限制后的值
 */
export function clamp (value, min, max) {
  return Math.min(Math.max(value, min), max)
}

function div (a, b) {
  if (!isNumber(a) || !isNumber(b)) return ''
  return new Decimal(a).div(new Decimal(b)).toString()
}

export default {
  setGroupData,
  isNumber,
  add,
  sub,
  mul,
  div,
  validateElementEmail,
  validateElementPhone,
  closePage,
  setSessionInfo,
  getSessionInfo,
  removeSessionInfo,
  getTimeDiff,
  setCookie,
  getCookie,
  encryptAES,
  decryptAES,
  encryptBase64,
  decryptBase64,
  randomCode,
  getAge,
  validatePhone,
  validateIdCard,
  clearLoginData,
  openNewPage,
  deepCopy,
  setDefaultEmptyValueForObject,
  readBlob,
  downloadFile,
  addSampleTag,
  identityNumberValid,
  dateFormatter,
  copyText,
  formatYearMonth,
  computeObjectValidKeyNum
}
