<template>
  <div class="tile-image-viewer">
    <!-- 图像查看器容器 -->
    <div ref="viewerContainer" class="viewer-container"></div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <div class="error-message">
        <i class="el-icon-warning"></i>
        <span>{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import OpenSeadragon from 'openseadragon'
import { fabric } from 'fabric'
import { addFabricjsOverlay } from 'openseadragon-fabricjs-overlay'
import { clamp } from '@/util/util'
import api from '@/api/onlineImage'

export default {
  name: 'TileImageViewer',
  props: {
    // 图像ID
    imageId: {
      type: String,
      required: true
    },
    // 查看模式 'view' | 'edit'
    mode: {
      type: String,
      default: 'edit'
    },
    // 初始标记数据
    markers: {
      type: Array,
      default: () => []
    },
    // 每像素微米数
    umPerPx: {
      type: Number,
      default: 0.25
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (this.imageId) {
        this.initViewer()
      }
    })
  },
  watch: {
    imageId: {
      handler (newVal) {
        if (newVal) {
          this.initViewer()
        }
      },
      immediate: true,
      deep: true
    },

    mode (newMode) {
      this.updateSelectionInteractivity()
    },

    markers: {
      handler (newMarkers) {
        if (this.isInitialized && newMarkers && newMarkers.length > 0) {
          this.updateSelectionFromMarker(newMarkers[0])
        }
      },
      deep: true
    }
  },
  computed: {
    // 计算默认选片框尺寸（像素）
    defaultSelectionSize () {
      if (!this.umPerPx) return { width: 500, height: 500 }

      // 6.5mm = 6500μm
      const sizeInMicrons = 6500
      const sizeInPixels = sizeInMicrons / this.umPerPx

      return {
        width: sizeInPixels,
        height: sizeInPixels
      }
    },

    // 计算图像中心点
    imageCenterPoint () {
      if (!this.imageInfo) {
        return { x: 0, y: 0 }
      }

      const width = this.imageInfo.image_w || this.imageInfo.width || 0
      const height = this.imageInfo.image_h || this.imageInfo.height || 0

      return {
        x: width / 2,
        y: height / 2
      }
    }
  },
  data () {
    return {
      viewer: null,
      overlay: null,
      fabricCanvas: null,
      selectionRect: null,
      imageInfo: null,
      loading: false,
      error: null,
      isInitialized: false,
      currentMarker: {
        id: 'default-selection',
        type: 'rect',
        cx: 0,
        cy: 0,
        sw: 0,
        sh: 0,
        angle: 0
      }
    }
  },
  beforeDestroy () {
    this.destroyViewer()
  },
  methods: {
    /**
     * 初始化查看器
     */
    async initViewer () {
      try {
        this.loading = true
        this.error = null

        // 销毁现有查看器
        this.destroyViewer()

        if (!this.imageId) {
          throw new Error('图像ID不能为空')
        }

        console.log('【TileImageViewer】开始初始化查看器，imageId:', this.imageId)

        // 获取图像信息
        const imageInfoRes = await api.getImageInfo(this.imageId)
        this.imageInfo = imageInfoRes.data || imageInfoRes

        if (!this.imageInfo) {
          throw new Error('无法获取图像信息')
        }

        console.log('【TileImageViewer】图像信息:', this.imageInfo)

        // 构建瓦片源配置
        const info = this.imageInfo
        const tileSource = {
          width: info.image_w || info.width,
          height: info.image_h || info.height,
          tileWidth: info.tile_size || 256,
          tileHeight: info.tile_size || 256,
          minLevel: info.minLevel || 0,
          maxLevel: info.maxLevel || Math.ceil(Math.log2(Math.max(info.image_w || info.width, info.image_h || info.height) / (info.tile_size || 256))),
          getTileUrl: (level, x, y) => api.getTileUrl(this.imageId, level, x, y)
        }

        // 创建OpenSeadragon查看器
        this.viewer = OpenSeadragon({
          element: this.$refs.viewerContainer,
          prefixUrl: 'https://openseadragon.github.io/openseadragon/images/',
          tileSources: tileSource,
          showNavigationControl: false, // 隐藏导航控制
          showZoomControl: false, // 隐藏缩放控制
          showHomeControl: false, // 隐藏主页控制
          showFullPageControl: false, // 隐藏全屏控制
          showRotationControl: false, // 隐藏旋转控制
          showSequenceControl: false, // 隐藏序列控制
          mouseNavEnabled: true,
          showNavigator: false,
          minZoomLevel: 0.1,
          maxZoomLevel: 10,
          visibilityRatio: 0.5,
          constrainDuringPan: true,
          wrapHorizontal: false,
          wrapVertical: false,
          immediateRender: false,
          blendTime: 0.1,
          alwaysBlend: false,
          autoHideControls: true,
          animationTime: 1.2,
          springStiffness: 6.5
        })

        // 等待图像加载完成
        await new Promise((resolve, reject) => {
          this.viewer.addHandler('open', () => {
            console.log('【TileImageViewer】图像加载完成')
            resolve()
          })

          this.viewer.addHandler('open-failed', (event) => {
            console.error('【TileImageViewer】图像加载失败:', event)
            reject(new Error('图像加载失败'))
          })

          // 设置超时
          setTimeout(() => {
            reject(new Error('图像加载超时'))
          }, 30000)
        })

        // 初始化Fabric.js覆盖层
        this.initFabricOverlay()

        // 创建默认选片框
        this.createDefaultSelection()

        this.isInitialized = true
        this.loading = false

        console.log('【TileImageViewer】查看器初始化完成')
      } catch (error) {
        console.error('【TileImageViewer】初始化失败:', error)
        this.error = error.message || '初始化失败'
        this.loading = false
      }
    },

    /**
     * 初始化Fabric.js覆盖层
     */
    initFabricOverlay () {
      if (!this.viewer) return

      try {
        // 创建Fabric.js覆盖层
        this.overlay = addFabricjsOverlay(this.viewer)
        this.fabricCanvas = this.overlay.fabricCanvas()

        // 配置Fabric.js画布
        this.fabricCanvas.selection = false // 禁用多选
        this.fabricCanvas.preserveObjectStacking = true

        // 监听对象修改事件
        this.fabricCanvas.on('object:modified', this.handleObjectModified)
        this.fabricCanvas.on('object:moving', this.handleObjectModified)
        this.fabricCanvas.on('object:scaling', this.handleObjectModified)
        this.fabricCanvas.on('object:rotating', this.handleObjectModified)

        console.log('【TileImageViewer】Fabric.js覆盖层初始化完成')
      } catch (error) {
        console.error('【TileImageViewer】Fabric.js覆盖层初始化失败:', error)
      }
    },

    /**
     * 创建默认选片框
     */
    createDefaultSelection () {
      if (!this.fabricCanvas || !this.imageInfo) return

      try {
        // 清除现有选片框
        if (this.selectionRect) {
          this.fabricCanvas.remove(this.selectionRect)
        }

        // 如果已有标记数据，使用第一个标记
        let marker = null
        if (this.markers && this.markers.length > 0) {
          marker = { ...this.markers[0] }
        } else {
          // 创建默认选片框
          marker = {
            id: 'default-selection',
            type: 'rect',
            cx: this.imageCenterPoint.x,
            cy: this.imageCenterPoint.y,
            sw: this.defaultSelectionSize.width,
            sh: this.defaultSelectionSize.height,
            angle: 0
          }
        }

        // 保存当前标记
        this.currentMarker = { ...marker }

        // 将图像坐标转换为视口坐标
        const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(marker.cx, marker.cy)

        // 计算视口中的宽度和高度
        const imageWidth = this.imageInfo.image_w || this.imageInfo.width
        const imageHeight = this.imageInfo.image_h || this.imageInfo.height

        const viewportWidth = marker.sw / imageWidth
        const viewportHeight = marker.sh / imageHeight

        // 将视口坐标转换为画布坐标
        const canvasPoint = this.overlay.viewportToCanvas(viewportPoint)

        // 计算画布上的宽度和高度
        // const zoom = this.viewer.viewport.getZoom()
        const containerSize = this.viewer.viewport.getContainerSize()
        const canvasWidth = viewportWidth * containerSize.x
        const canvasHeight = viewportHeight * containerSize.y

        // 创建矩形
        this.selectionRect = new fabric.Rect({
          left: canvasPoint.x - canvasWidth / 2,
          top: canvasPoint.y - canvasHeight / 2,
          width: canvasWidth,
          height: canvasHeight,
          angle: marker.angle || 0,
          fill: 'rgba(0, 168, 255, 0.1)',
          stroke: '#00a8ff',
          strokeWidth: 2,
          transparentCorners: false,
          cornerColor: '#00a8ff',
          cornerSize: 10,
          lockRotation: false,
          hasRotatingPoint: true,
          rotatingPointOffset: 40,
          centeredRotation: true,
          originX: 'center',
          originY: 'center'
        })

        // 添加到画布
        this.fabricCanvas.add(this.selectionRect)
        this.fabricCanvas.setActiveObject(this.selectionRect)

        // 更新交互性
        this.updateSelectionInteractivity()

        // 触发事件
        this.$emit('selection-change', this.currentMarker)
        this.$emit('markers-change', [this.currentMarker])

        console.log('【TileImageViewer】选片框已创建:', this.currentMarker)
      } catch (error) {
        console.error('【TileImageViewer】创建选片框失败:', error)
      }
    },

    /**
     * 更新选片框交互性
     */
    updateSelectionInteractivity () {
      if (!this.selectionRect) return

      const isEditable = this.mode === 'edit'

      this.selectionRect.set({
        selectable: isEditable,
        evented: isEditable,
        hasControls: isEditable,
        hasBorders: isEditable,
        lockMovementX: !isEditable,
        lockMovementY: !isEditable,
        lockRotation: !isEditable,
        lockScalingX: !isEditable,
        lockScalingY: !isEditable
      })

      this.fabricCanvas.renderAll()
    },

    /**
     * 从标记更新选片框
     */
    updateSelectionFromMarker (marker) {
      if (!this.fabricCanvas || !this.viewer || !marker) return

      try {
        // 保存当前标记
        this.currentMarker = { ...marker }

        // 如果没有选片框，创建一个
        if (!this.selectionRect) {
          this.createDefaultSelection()
          return
        }

        // 将图像坐标转换为视口坐标
        const viewportPoint = this.viewer.viewport.imageToViewportCoordinates(marker.cx, marker.cy)

        // 计算视口中的宽度和高度
        const imageWidth = this.imageInfo.image_w || this.imageInfo.width
        const imageHeight = this.imageInfo.image_h || this.imageInfo.height

        const viewportWidth = marker.sw / imageWidth
        const viewportHeight = marker.sh / imageHeight

        // 将视口坐标转换为画布坐标
        const canvasPoint = this.overlay.viewportToCanvas(viewportPoint)

        // 计算画布上的宽度和高度
        const zoom = this.viewer.viewport.getZoom()
        const containerSize = this.viewer.viewport.getContainerSize()
        const canvasWidth = viewportWidth * containerSize.x
        const canvasHeight = viewportHeight * containerSize.y

        // 更新矩形
        this.selectionRect.set({
          left: canvasPoint.x,
          top: canvasPoint.y,
          width: canvasWidth,
          height: canvasHeight,
          angle: marker.angle || 0,
          originX: 'center',
          originY: 'center'
        })

        this.selectionRect.setCoords()
        this.fabricCanvas.renderAll()

        console.log('【TileImageViewer】选片框已更新:', marker)
      } catch (error) {
        console.error('【TileImageViewer】更新选片框失败:', error)
      }
    },

    /**
     * 处理对象修改事件
     */
    handleObjectModified () {
      if (!this.selectionRect || !this.viewer || !this.imageInfo) return

      try {
        // 获取矩形的画布坐标
        const rect = this.selectionRect
        const center = rect.getCenterPoint()

        // 将画布坐标转换为视口坐标
        const viewportPoint = this.overlay.canvasToViewport({
          x: center.x,
          y: center.y
        })

        // 将视口坐标转换为图像坐标
        const imagePoint = this.viewer.viewport.viewportToImageCoordinates(viewportPoint.x, viewportPoint.y)

        // 计算图像坐标系中的宽度和高度
        const imageWidth = this.imageInfo.image_w || this.imageInfo.width
        const imageHeight = this.imageInfo.image_h || this.imageInfo.height

        const containerSize = this.viewer.viewport.getContainerSize()
        const zoom = this.viewer.viewport.getZoom()

        const widthInViewport = rect.getScaledWidth() / containerSize.x
        const heightInViewport = rect.getScaledHeight() / containerSize.y

        const widthInImage = widthInViewport * imageWidth
        const heightInImage = heightInViewport * imageHeight

        // 更新标记数据
        this.currentMarker = {
          id: 'default-selection',
          type: 'rect',
          cx: imagePoint.x,
          cy: imagePoint.y,
          sw: widthInImage,
          sh: heightInImage,
          angle: rect.angle % 90 // 确保角度在0-90度范围内
        }

        // 触发事件
        this.$emit('selection-change', this.currentMarker)
        this.$emit('markers-change', [this.currentMarker])

        console.log('【TileImageViewer】选片框已修改:', this.currentMarker)
      } catch (error) {
        console.error('【TileImageViewer】处理选片框修改失败:', error)
      }
    },

    /**
     * 移动选片框
     * @param {Number} dx X方向移动距离（像素）
     * @param {Number} dy Y方向移动距离（像素）
     */
    moveBy (dx, dy) {
      if (!this.selectionRect || !this.viewer) return

      try {
        // 获取当前中心点
        const center = this.selectionRect.getCenterPoint()

        // 移动矩形
        this.selectionRect.set({
          left: center.x + dx,
          top: center.y + dy
        })

        this.selectionRect.setCoords()
        this.fabricCanvas.renderAll()

        // 触发修改事件
        this.handleObjectModified()

        console.log('【TileImageViewer】选片框移动:', { dx, dy })
      } catch (error) {
        console.error('【TileImageViewer】移动选片框失败:', error)
      }
    },

    /**
     * 设置选片框角度
     * @param {Number} angle 角度（0-90度）
     */
    setAngle (angle) {
      if (!this.selectionRect) return

      try {
        // 确保角度在0-90度范围内
        const clampedAngle = clamp(angle, 0, 90)

        // 设置矩形角度
        this.selectionRect.set('angle', clampedAngle)
        this.selectionRect.setCoords()
        this.fabricCanvas.renderAll()

        // 触发修改事件
        this.handleObjectModified()

        console.log('【TileImageViewer】选片框角度设置:', clampedAngle)
      } catch (error) {
        console.error('【TileImageViewer】设置选片框角度失败:', error)
      }
    },

    /**
     * 旋转选片框
     * @param {Number} deltaAngle 旋转角度增量
     */
    rotateBy (deltaAngle) {
      if (!this.selectionRect) return

      try {
        // 计算新角度并确保在0-90度范围内
        const currentAngle = this.selectionRect.angle || 0
        const newAngle = clamp(currentAngle + deltaAngle, 0, 90)

        // 设置矩形角度
        this.selectionRect.set('angle', newAngle)
        this.selectionRect.setCoords()
        this.fabricCanvas.renderAll()

        // 触发修改事件
        this.handleObjectModified()

        console.log('【TileImageViewer】选片框旋转:', { deltaAngle, newAngle })
      } catch (error) {
        console.error('【TileImageViewer】旋转选片框失败:', error)
      }
    },

    /**
     * 重置视图
     */
    resetView () {
      if (!this.viewer) return

      try {
        this.viewer.viewport.goHome()
        console.log('【TileImageViewer】视图已重置')
      } catch (error) {
        console.error('【TileImageViewer】重置视图失败:', error)
      }
    },

    /**
     * 销毁查看器
     */
    destroyViewer () {
      try {
        // 清除Fabric.js画布
        if (this.fabricCanvas) {
          this.fabricCanvas.clear()
          this.selectionRect = null
        }

        // 销毁覆盖层
        if (this.overlay) {
          this.overlay.destroy()
          this.overlay = null
          this.fabricCanvas = null
        }

        // 销毁OpenSeadragon查看器
        if (this.viewer) {
          this.viewer.destroy()
          this.viewer = null
        }

        // 重置状态
        this.isInitialized = false
        this.imageInfo = null

        console.log('【TileImageViewer】查看器已销毁')
      } catch (error) {
        console.error('【TileImageViewer】销毁查看器失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.tile-image-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}

.viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #409eff;
}

.loading-spinner i {
  font-size: 32px;
  animation: rotate 2s linear infinite;
}

.loading-spinner span {
  font-size: 14px;
  color: #666;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #f56c6c;
  text-align: center;
  padding: 20px;
}

.error-message i {
  font-size: 32px;
}

.error-message span {
  font-size: 14px;
  max-width: 300px;
  word-wrap: break-word;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
