// 样本管理相关路由
export default [
  // 样本管理相关路由
  {
    path: '/business/view/sampleSearch',
    meta: {
      title: '样本查询'
    },
    component: () => import('@/components/business/sampleLibraryManagement/sampleSearch.vue')
  },
  {
    path: '/business/view/turnoverLibraryManagement',
    meta: {
      title: '出入库管理'
    },
    component: () => import('@/components/business/sampleLibraryManagement/turnoverLibraryManagement.vue')
  },
  {
    path: '/business/view/applicationForStorage',
    meta: {
      title: '出入库申请'
    },
    component: () => import('@/components/business/sampleLibraryManagement/applicationForStorage.vue')
  },
  {
    path: '/business/view/abnormalSampleManagement',
    meta: {
      title: '异常样本管理'
    },
    component: () => import('@/components/business/sampleLibraryManagement/abnormalSampleManagement.vue')
  },
  {
    path: '/business/view/backSampleManagement',
    meta: {
      title: '返样管理'
    },
    component: () => import('@/components/business/sampleLibraryManagement/backSampleManagement.vue')
  },
  {
    path: '/business/view/patientInfoDetail',
    meta: {
      title: '患者信息详情'
    },
    component: () => import('@/components/business/sample/patientDetail/patientInfoDetail.vue')
  },
  {
    path: '/business/view/sample/abnormalSampleManagement',
    meta: {
      title: '样例异常处理'
    },
    component: () => import('@/components/business/sample/abnormalSampleManagement.vue')
  },
  {
    path: '/business/view/clinicalInfoManagement',
    meta: {
      title: '样本信息管理'
    },
    component: () => import('@/components/business/sample/clinicalInfoManagement.vue')
  },
  {
    path: '/business/view/pathogenManagement',
    meta: {
      title: '病原样本管理'
    },
    component: () => import('@/components/business/pathogenSample/pathogenSampleManagement.vue')
  },
  {
    path: '/business/view/sampleReturnManagement',
    meta: {
      title: '返样管理'
    },
    component: () => import('@/components/business/sample/returnSampleManagement.vue')
  },
  {
    path: '/business/view/sampleSigningManagement',
    meta: {
      title: '样本签收管理'
    },
    component: () => import('@/components/business/sample/sampleSigningManagement.vue')
  },
  {
    path: '/business/view/expressDeliveryManagement',
    meta: {
      title: '快递签收管理'
    },
    component: () => import('@/components/business/sample/expressDeliveryManagement.vue')
  },
  {
    path: '/business/view/samplesNotSignedFor',
    meta: {
      title: '样本未签收'
    },
    component: () => import('@/components/business/sampleReceiptManagement/samplesNotSignedFor/overview.vue')
  },
  {
    path: '/business/view/sampleHasBeenSigned',
    meta: {
      title: '签收样本信息'
    },
    component: () => import('@/components/business/sampleReceiptManagement/sampleHasBeenSigned/overview.vue')
  },
  {
    path: '/business/view/sampleMonitoring',
    meta: {
      title: '样本监控'
    },
    component: () => import('@/components/business/dataMonitoringManagement/smapleMonitoringManagement/index.vue')
  },
  {
    path: '/business/sub/clinicalInfoManagementInfoDetail',
    component: () => import('@/components/business/sample/clinicalInfoManagementInfoDetail.vue')
  },
  {
    path: '/business/sub/pathogenInfoManagementInfoDetail',
    component: () => import('@/components/business/pathogenSample/pathogenSampleInfo.vue')
  },
  {
    path: '/business/sub/sampleLogs',
    meta: {
      title: '样例日志'
    },
    component: () => import('@/components/business/sample/sampleLogs/index.vue')
  }
]
