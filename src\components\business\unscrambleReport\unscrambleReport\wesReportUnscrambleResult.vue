<template>
  <div class="page">
    <div class="buttonGroup">
      <el-button type="primary" size="mini" @click="handleCreateReport">生成报告</el-button>
    </div>
    <div class="content">
      <el-tabs v-model="activeName" tab-position="left" style="height: 100%;" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="baseInfo">
          <div class="content baseInfo">
            <el-form ref="baseInfo" :model="baseInfo" label-width="120px" label-suffix="：">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="姓名">
                    <desensitization :info="baseInfo.name" type="name" is-detail></desensitization>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="样本编号">{{baseInfo.sampleNum}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="送检单位">{{baseInfo.hospital}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="性别">{{baseInfo.sex}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="样本类型">{{baseInfo.sampleType}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="送检医生">{{baseInfo.doctor}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="出生年月">{{baseInfo.birthday}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="样本采集时间">{{baseInfo.collectDate}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="送检项目">{{baseInfo.sendProject}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="身份证/护照">
                    <desensitization :info="baseInfo.idCard" type="idCard" is-detail></desensitization>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="样本接收日期">{{baseInfo.receiverDate}}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="联系电话">
                    <span :key="index" v-for="(item, index) in baseInfo.telephone">
                       <desensitization :info="item" type="phone" is-detail></desensitization>
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="报告日期">{{baseInfo.reportDate}}</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="临床诊断">{{baseInfo.clinicalDiagnose}}</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="治疗史">
                    <span v-html="baseInfo.drugHistory"></span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="靶向药物用药提示" name="targetedDrugMedicationTips">
          <div class="content targetedDrugMedicationTips">
            <el-row>
              <el-col :span="24">
                <div class="col">
                  <span style="font-size: 14px;">检测小结</span>
                  <el-button type="text" @click="handleEdit">[编辑]</el-button>
                </div>
              </el-col>
              <el-col :span="8" >
                <div class="col col-margin">体系突变：{{targetedDrugMedicationTips.somaticVariationCount}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">胚系突变：{{targetedDrugMedicationTips.germlineMutationCount}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">靶向药物相关突变：{{targetedDrugMedicationTips.targetDrugCount}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">TMB：{{targetedDrugMedicationTips.tmb}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">MSI：{{targetedDrugMedicationTips.msi}}</div>
              </el-col>
              <el-col :span="8" v-if="targetedDrugMedicationTips.bcl2l11">
                <div class="col col-margin">BCL2L11结果：{{targetedDrugMedicationTips.bcl2l11}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">TMS_Status：{{targetedDrugMedicationTips.tmbStatus}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">cTMB：{{targetedDrugMedicationTips.cTmb}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">TNB：{{targetedDrugMedicationTips.tnb}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">cTNB：{{targetedDrugMedicationTips.cTnb}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">HRD：{{targetedDrugMedicationTips.hrd}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">HRD(Assay)：{{targetedDrugMedicationTips.hrdAssay}}</div>
              </el-col>
              <el-col :span="8">
                <div class="col col-margin">TraceMuts：{{targetedDrugMedicationTips.ftraceMuts}}</div>
              </el-col>
              <el-col :span="24">
                <div style="font-size: 14px;" class="col">靶向药物提示</div>
              </el-col>
              <el-col :span="24">
                <div class="col-margin">
                  <el-table
                    :data="targetedDrugMedicationTips.targetHint"
                    border
                    size="mini"
                    style="width: 100%">
                    <el-table-column prop="gene" label="基因" width="180"></el-table-column>
                    <el-table-column label="变异" width="180">
                      <template slot-scope="scope">
                        <span v-html="scope.row.variation"></span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="rate" label="频率"></el-table-column>
                    <el-table-column label="提示敏感">
                      <el-table-column prop="hintSensitiveFda" label="FDA/CFDA批准">
                        <template slot-scope="scope">
                          <template v-for="(item, index) in scope.row.hintSensitiveFda">
                            <p :key="'hintSensitiveFda' + index" :class="{red: item.isRed}">{{item.drugName}}</p>
                          </template>
                        </template>
                      </el-table-column>
                      <el-table-column label="临床试验">
                        <template slot-scope="scope">
                          <template v-for="(item, index) in scope.row.hintSensitiveClinic">
                            <p :key="'hintSensitiveClinic' + index" :class="{red: item.isRed}">{{item.drugName}}</p>
                          </template>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="提示耐药或无效">
                      <el-table-column prop="hintResistance" label="FDA/CFDA批准">
                        <template slot-scope="scope">
                          <template v-for="(item, index) in scope.row.hintResistance">
                            <p :key="'hintResistance' + index" :class="{red: item.isRed}">{{item.drugName}}</p>
                          </template>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="研究结论不一致">
                      <el-table-column prop="resultNotSame" label="FDA/CFDA批准">
                        <template slot-scope="scope">
                          <template v-for="(item, index) in scope.row.resultNotSame">
                            <p :key="'resultNotSame' + index" :class="{red: item.isRed}">{{item.drugName}}</p>
                          </template>
                        </template>
                      </el-table-column>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
              <el-col :span="24">
                <div style="font-size: 14px;" class="col">药物注释</div>
              </el-col>
              <el-col :span="24">
                <div class="col-margin">
                  <el-table
                    :data="targetedDrugMedicationTips.targetNotes"
                    border
                    size="mini"
                    style="width: 100%">
                    <el-table-column prop="targetNote" label="药物注释"></el-table-column>
                  </el-table>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        <el-tab-pane label="脑胶质瘤基因检出结果" name="gliomaGeneDetectionResults">
          <div class="content">
            <el-table
              :data="gliomaGeneDetectionResultsTableData"
              border
              class="gliomaGeneDetectionResults"
              size="mini"
              height="100%"
              style="width: 100%">
              <el-table-column prop="fgene" label="分子标记基因"></el-table-column>
              <el-table-column prop="ftestResult" label="检测结果"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="基于突变的免疫指标" name="immuneIndex">
          <div class="content">
            <el-table
              :data="immuneIndexTableData"
              class="immuneIndex"
              border
              size="mini"
              height="100%"
              style="width: 100%">
              <el-table-column prop="immune" label="免疫指标"></el-table-column>
              <el-table-column label="检测结果">
                <template slot-scope="scope">
                  <span v-html="scope.row.fdetectResult"></span>
                </template>
              </el-table-column>
              <el-table-column prop="fdetectRead" label="解读结果"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="变异检测结果" name="variationDetectionResults">
          <div style="height: 50px; line-height: 50px;">
            <el-radio-group v-model="detectMutationRadio" size="mini" @change="getData">
              <el-radio-button label="体系SNV"></el-radio-button>
              <el-radio-button label="体系CNV"></el-radio-button>
              <el-radio-button label="体系SV"></el-radio-button>
              <el-radio-button label="胚系SNV"></el-radio-button>
              <el-radio-button label="胚系CNV"></el-radio-button>
            </el-radio-group>
          </div>
          <div>
            <el-table
              :data="variationDetectionResultsTableData"
              class="variationDetectionResults"
              border
              size="mini"
              height="calc(100vh - 50px - 40px - 50px - 100px - 20px)"
              style="width: 100%">
              <template v-if="detectMutationRadio === '体系SNV'">
                <el-table-column prop="gene" label="基因"></el-table-column>
                <el-table-column prop="nucleotideMutation" label="碱基"></el-table-column>
                <el-table-column prop="aminoAcidMutation" label="氨基酸"></el-table-column>
                <el-table-column prop="transcript" label="转录本"></el-table-column>
                <el-table-column prop="exinId" label="外显子功能区域"></el-table-column>
                <el-table-column prop="caseAf" label="频率"></el-table-column>
                <el-table-column prop="function" label="function"></el-table-column>
                <el-table-column prop="varType" label="varType"></el-table-column>
              </template>
              <template v-else-if="detectMutationRadio === '体系CNV'">
                <el-table-column prop="gene" label="基因"></el-table-column>
                <el-table-column prop="aminoAcidMutation" label="变异类型"></el-table-column>
                <el-table-column prop="transcript" label="转录本"></el-table-column>
                <el-table-column prop="copyNum" label="拷贝数"></el-table-column>
                <el-table-column prop="exInId" label="外显子"></el-table-column>
              </template>
              <template v-else-if="detectMutationRadio === '体系SV'">
                <el-table-column prop="gene" label="基因"></el-table-column>
                <el-table-column prop="aminoAcidMutation" label="氨基酸"></el-table-column>
                <el-table-column prop="mutationRate" label="变异频率"></el-table-column>
                <el-table-column prop="transcript" label="转录本"></el-table-column>
                <el-table-column prop="exInId" label="功能区域"></el-table-column>
                <el-table-column prop="gene1" label="基因1"></el-table-column>
                <el-table-column prop="transcript1" label="transcript1"></el-table-column>
                <el-table-column prop="exon1" label="exon1"></el-table-column>
                <el-table-column prop="maploc1" label="maploc1"></el-table-column>
                <el-table-column prop="gene2" label="gene2"></el-table-column>
                <el-table-column prop="transcript2" label="transcript2"></el-table-column>
                <el-table-column prop="exon2" label="exon2"></el-table-column>
                <el-table-column prop="maploc2" label="maploc2"></el-table-column>
              </template>
              <template v-else-if="detectMutationRadio === '胚系SNV'">
                <el-table-column prop="gene" label="基因"></el-table-column>
                <el-table-column prop="nucleotideMutation" label="碱基"></el-table-column>
                <el-table-column prop="exInId" label="功能区域"></el-table-column>
                <el-table-column prop="aminoAcidMutation" label="氨基酸"></el-table-column>
                <el-table-column prop="mutationRate" label="变异频率"></el-table-column>
                <el-table-column prop="zygosity" label="杂合性"></el-table-column>
                <!--<el-table-column prop="gene1" label="基因1"></el-table-column>-->
                <el-table-column prop="transcript" label="转录本"></el-table-column>
                <el-table-column prop="function" label="function"></el-table-column>
                <el-table-column prop="varType" label="varType"></el-table-column>
                <el-table-column prop="mutationMeaning" label="变异意义"></el-table-column>
              </template>
              <template v-else>
                <el-table-column prop="gene" label="基因"></el-table-column>
                <el-table-column prop="nucleotideMutation" label="碱基"></el-table-column>
                <el-table-column prop="exInId" label="功能区域"></el-table-column>
                <el-table-column prop="aminoAcidMutation" label="氨基酸"></el-table-column>
                <el-table-column prop="mutationRate" label="变异频率"></el-table-column>
                <el-table-column prop="zygosity" label="杂合性"></el-table-column>
                <el-table-column prop="transcript" label="转录本"></el-table-column>
              </template>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="靶向药物解析" name="targetedDrugAnalysis">
          <div style="height: 50px; line-height: 50px;">
            <el-radio-group v-model="targetedDrugAnalysisRadio" size="mini" @change="getTargetedDrugAnalysisTableData">
              <el-radio-button label="提示敏感"></el-radio-button>
              <el-radio-button label="提示耐药"></el-radio-button>
              <el-radio-button label="结论不一致"></el-radio-button>
            </el-radio-group>
          </div>
          <div>
            <el-table
              :data="targetedDrugAnalysisTableData"
              border
              class="targetedDrugAnalysis"
              size="mini"
              height="calc(100vh - 50px - 40px - 50px - 100px - 20px)"
              style="width: 100%">
                <el-table-column label="基因变异" width="180">
                  <template slot-scope="scope">
                    <span v-html="scope.row.geneMutation"></span>
                  </template>
                </el-table-column>
                <el-table-column label="靶向药物" width="180">
                  <template slot-scope="scope">
                    <span v-html="scope.row.drug"></span>
                  </template>
                </el-table-column>
                <el-table-column label="靶向药物解析">
                  <template slot-scope="scope">
                    <span v-html="scope.row.analysis"></span>
                  </template>
                </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="化疗药物解析" name="analysisOfChemotherapyDrugs">
          <div style="height: 50px; line-height: 50px;">
            <el-button size="mini" type="primary" @click="handleDelete('analysisOfChemotherapyDrugs')">删除</el-button>
          </div>
          <el-table
            ref="analysisOfChemotherapyDrugs"
            :data="analysisOfChemotherapyDrugsTableData"
            border
            class="analysisOfChemotherapyDrugs"
            height="calc(100vh - 50px - 40px - 50px - 100px - 20px)"
            size="mini"
            style="width: 100%"
            @select-all="handleSelectAll"
            @row-click="handleRowClick"
            @select="handleSelect">
            <el-table-column type="selection" width="45"></el-table-column>
            <el-table-column prop="drugName" label="药物名称"></el-table-column>
            <el-table-column prop="testProject" label="检测项目"></el-table-column>
            <el-table-column prop="drug" label="药物" min-width="180"></el-table-column>
            <el-table-column prop="gene" label="基因"></el-table-column>
            <el-table-column prop="testPoint" label="检测位点"></el-table-column>
            <el-table-column prop="level" label="等级"></el-table-column>
            <el-table-column prop="testResult" label="检测结果"></el-table-column>
            <el-table-column prop="resultDetail" label="结果解析" min-width="180"></el-table-column>
            <el-table-column prop="drugHint" label="用药提示" min-width="200"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="结构变异检测结果解析" name="analysisOfStructuralVariationDetectionResults">
          <div class="content">
            <el-table
              ref="analysisOfStructuralVariationDetectionResults"
              :data="analysisOfStructuralVariationDetectionResultsTableData"
              border
              class="analysisOfStructuralVariationDetectionResults"
              height="100%"
              size="mini"
              style="width: 100%">
              <el-table-column prop="gene" label="变异">
                <template slot-scope="scope">
                  <p v-html="scope.row.gene"></p>
                </template>
              </el-table-column>
              <el-table-column label="详细解析">
                <template slot-scope="scope">
                  <p v-html="scope.row.analysis"></p>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="遗传致病解析" name="analysisOfGeneticDisease">
          <div class="content">
            <el-table
              :data="analysisOfGeneticDiseaseTableData"
              border
              class="analysisOfGeneticDisease"
              height="100%"
              size="mini"
              style="width: 100%">
              <el-table-column prop="gene" label="基因"></el-table-column>
              <el-table-column prop="inheritance" label="遗传性肿瘤综合征/遗传方式" width="200px"></el-table-column>
              <el-table-column prop="transcript" label="转录本"></el-table-column>
              <el-table-column prop="exinId" label="功能区域"></el-table-column>
              <el-table-column prop="mutation" label="变异"></el-table-column>
              <el-table-column prop="zygosity" label="纯和/杂合"></el-table-column>
              <el-table-column prop="mutationMeaning" label="变异意义"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="遗传结果解读" name="interpretationOfGeneticResults">
          <div style="height: 50px; line-height: 50px">
            <el-button size="mini" type="primary" @click="handleEditGeneticResult">编辑</el-button>
          </div>
          <el-table
            ref="interpretationOfGeneticResults" :data="interpretationOfGeneticResultsTableData"
            border
            height="calc(100vh - 50px - 40px - 50px - 100px - 20px)"
            size="mini"
            class="interpretationOfGeneticResults"
            style="width: 100%"
            @select-all="handleSelectAll"
            @select="handleSelect"
            @row-click="handleRowClick">
            <el-table-column type="selection" width="45"></el-table-column>
            <el-table-column prop="fgene" label="基因"></el-table-column>
            <el-table-column prop="fresultRead" label="结果解读"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="遗传疾病简介" name="introductionToGeneticDiseases">
          <div class="content">
            <el-table
              :data="introductionToGeneticDiseasesTableData" border
              height="100%"
              size="mini"
              class="introductionToGeneticDiseases"
              style="width: 100%">
              <el-table-column prop="geneticCancer" label="遗传性肿瘤综合征" width="200"></el-table-column>
              <el-table-column prop="diseaseAbout" label="疾病简介"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="遗传患癌风险" name="geneticCancerRisk">
          <div class="content">
            <el-table
              :data="geneticCancerRiskTableData"
              class="geneticCancerRisk" border
              height="100%"
              size="mini"
              style="width: 100%">
              <el-table-column prop="gene" label="基因" width="100"></el-table-column>
              <el-table-column prop="tumorTypes" label="癌症类型" width="120"></el-table-column>
              <el-table-column prop="simpleRisk" label="普通人群患癌风险" width="120"></el-table-column>
              <el-table-column prop="avgAge" label="平均患病年龄" width="100"></el-table-column>
              <el-table-column label="突变携带者患癌风险1" min-width="180">
                <template slot-scope="scope">
                  <span v-html="scope.row.withMutationRisk"></span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="参考文献" name="references">
          <div style="height: 50px; line-height: 50px">
            <el-button size="mini" type="primary" @click="handleDelete('references')">删除</el-button>
          </div>
          <el-table
            ref="references"
            :data="referencesTableData"
            class="references" border
            height="calc(100vh - 50px - 40px - 50px - 100px - 20px)"
            size="mini"
            style="width: 100%"
            @select-all="handleSelectAll"
            @row-click="handleRowClick"
            @select="handleSelect">
            <el-table-column type="selection" width="45"></el-table-column>
            <el-table-column prop="referenceDoc" label="参考文献"></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <detect-result-edit-dialog
      :pvisible="detectResultEditDialogVisible" :pdata="detectResultEditDialogData"
      @detectResultEditDialogConfirmEvent="handleDetectResultEditDialogConfirm"
      @detectResultEditDialogCloseEvent="handleDetectResultEditDialogClose"
    ></detect-result-edit-dialog>
    <report-template-dialog
      :pvisible="reportTemplateDialogVisible"
      @reportTemplateDialogConfirmEvent="handleReportTemplateDialogConfirm"
      @reportTemplateDialogCloseEvent="handleReportTemplateDialogClose"
    ></report-template-dialog>
    <genetic-result-edit-dialog
      :pvisible="geneticResultEditDialogVisible"
      :pdata="geneticResultEditDialogData"
      @geneticResultEditDialogConfirmEvent="handleGeneticResultEditDialogConfirm"
      @geneticResultEditDialogCloseEvent="handleGeneticResultEditDialogClose"
    >
    </genetic-result-edit-dialog>
  </div>
</template>

<script>
import detectResultEditDialog from './wesReportUnscrambleResultDetectResultEditDialog'
import reportTemplateDialog from './wesReportUnscrambleResultTemplateDialog'
import geneticResultEditDialog from './wesReportUnscrambleResultGeneticResultEditDialog'
import util from '../../../../util/util'
export default {
  name: 'wesReportUnscrambleResult',
  components: {
    detectResultEditDialog,
    reportTemplateDialog,
    geneticResultEditDialog
  },
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      baseInfo: {},
      targetedDrugMedicationTips: {
        fid: '',
        rptTestMutaCount: '',
        TMBStatus: '',
        rptGermlineMutaCount: '',
        TMBValue: '',
        rptTargetMutaCount: '',
        MSIStatus: '',
        polyrs: '',
        tipsTableData: [],
        drugNotesTableData: []
      },
      gliomaGeneDetectionResultsTableData: [],
      immuneIndexTableData: [],
      activeName: 'baseInfo',
      detectMutationRadio: '体系SNV',
      variationDetectionResultsTableData: [],
      targetedDrugAnalysisRadio: '提示敏感',
      targetedDrugAnalysisData: {
        targetResult1: [], // 提示敏感
        targetResult2: [], // 提示耐药
        targetResult3: [] // 结论不一致
      },
      targetedDrugAnalysisTableData: [],
      analysisOfChemotherapyDrugsTableData: [],
      analysisOfGeneticDiseaseTableData: [],
      interpretationOfGeneticResultsTableData: [],
      geneticCancerRiskTableData: [],
      referencesTableData: [],
      introductionToGeneticDiseasesTableData: [],
      analysisOfStructuralVariationDetectionResultsTableData: [],
      selectedRows: [],
      detectResultEditDialogData: {},
      detectResultEditDialogVisible: false,
      reportTemplateDialogVisible: false,
      geneticResultEditDialogVisible: false,
      geneticResultEditDialogData: {}
    }
  },
  methods: {
    getData () {
      switch (this.activeName) {
        case 'baseInfo':
          this.$ajax({
            loadingDom: '.baseInfo',
            url: '/read/wesUnscramble/get_report_basic_data',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              let data = result.data.rows
              let item = {}
              if (data) {
                item = {
                  fid: data.fid,
                  name: data.name,
                  sampleNum: data.sampleNum,
                  doctor: data.doctor,
                  hospital: data.hospital,
                  sex: data.sex,
                  sampleType: data.sampleType,
                  birthday: data.birthday,
                  collectDate: data.collectDate,
                  receiverDate: data.receiverDate,
                  sendProject: data.sendProject,
                  idCard: data.idCard,
                  telephone: data.telephone && data.telephone.split(','),
                  reportDate: data.reportDate,
                  clinicalDiagnose: data.clinicalDiagnose,
                  drugHistory: data.drugHistory
                }
                util.setDefaultEmptyValueForObject(item)
                this.baseInfo = Object.assign({}, item)
              }
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'targetedDrugMedicationTips':
          this.$ajax({
            loadingDom: '.targetedDrugMedicationTips',
            url: '/read/wesUnscramble/get_TipsForTargetingDrugs_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.targetedDrugMedicationTips = result.data || {}
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'gliomaGeneDetectionResults':
          this.$ajax({
            loadingDom: '.gliomaGeneDetectionResults',
            url: '/read/wesUnscramble/get_glioma_result',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.gliomaGeneDetectionResultsTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'immuneIndex':
          this.$ajax({
            loadingDom: '.immuneIndex',
            url: '/read/wesUnscramble/get_immuneIndex_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.immuneIndexTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'variationDetectionResults':
          let url = ''
          switch (this.detectMutationRadio) {
            case '体系SNV':
              url = '/read/wesUnscramble/locus/snv'
              break
            case '体系CNV':
              url = '/read/wesUnscramble/locus/cnv'
              break
            case '体系SV':
              url = '/read/wesUnscramble/locus/sv'
              break
            case '胚系SNV':
              url = '/read/wesUnscramble/locus/hsnv'
              break
            case '胚系CNV':
              url = '/read/wesUnscramble/locus/hcnv'
              break
          }
          this.$ajax({
            loadingDom: '.variationDetectionResults',
            url: url,
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.variationDetectionResultsTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'targetedDrugAnalysis':
          this.$ajax({
            loadingDom: '.targetedDrugAnalysis',
            url: '/read/wesUnscramble/get_targetedDrugAnalysis_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.targetedDrugAnalysisData = result.data
              this.getTargetedDrugAnalysisTableData()
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'analysisOfChemotherapyDrugs':
          this.$ajax({
            loadingDom: '.analysisOfChemotherapyDrugs',
            url: '/read/wesUnscramble/get_chemotherapyDrugsAnalysis_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.analysisOfChemotherapyDrugsTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'analysisOfStructuralVariationDetectionResults':
          this.$ajax({
            loadingDom: '.analysisOfStructuralVariationDetectionResults',
            url: '/read/wesUnscramble/structure_mutation_analysis',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.analysisOfStructuralVariationDetectionResultsTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'analysisOfGeneticDisease':
          this.$ajax({
            loadingDom: '.analysisOfGeneticDisease',
            url: '/read/wesUnscramble/get_geneticPathogenicAnalysis_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.analysisOfGeneticDiseaseTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'interpretationOfGeneticResults':
          this.$ajax({
            loadingDom: '.interpretationOfGeneticResults',
            url: '/read/wesUnscramble/get_interpretationOfGeneticResults_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.interpretationOfGeneticResultsTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'introductionToGeneticDiseases':
          this.$ajax({
            loadingDom: '.introductionToGeneticDiseases',
            url: '/read/wesUnscramble/get_interpretationGeneticDiseases_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.introductionToGeneticDiseasesTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'geneticCancerRisk':
          this.$ajax({
            loadingDom: '.geneticCancerRisk',
            url: '/read/wesUnscramble/get_geneticCancerRisk_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.geneticCancerRiskTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
        case 'references':
          this.$ajax({
            loadingDom: '.references',
            url: '/read/wesUnscramble/get_references_list',
            method: 'get',
            data: {
              analysisRsId: this.analysisRsId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.referencesTableData = result.data
            } else {
              this.$message.error(result.message)
            }
          })
          break
      }
    },
    handleClick (tab, event) {
      this.selectedRows = []
      this.getData()
    },
    handleDelete (type) {
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择数据')
      } else {
        let row = this.selectedRows
        let url = ''
        let data = {}
        if (type === 'analysisOfChemotherapyDrugs') {
          let ctAnalyticalIds = row.map(v => {
            return v.ctAnalyticalId
          })
          url = '/read/wesUnscramble/remove_ct_drug'
          data = {
            analysisRsId: this.analysisRsId,
            fid: ctAnalyticalIds
          }
        } else if (type === 'references') {
          let ids = row.map(v => {
            return v.fid
          })
          url = '/read/wesUnscramble/remove_report_doc'
          data = {
            analysisRsId: this.analysisRsId,
            fid: ids
          }
        }
        this.$ajax({
          url: url,
          data: data
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.selectedRows = []
            this.getData()
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleEdit () {
      if (this.targetedDrugMedicationTips.fid !== '') {
        this.detectResultEditDialogData = {
          msi: this.targetedDrugMedicationTips.msi,
          hrd: this.targetedDrugMedicationTips.hrd
        }
        this.detectResultEditDialogVisible = true
      }
    },
    handleDetectResultEditDialogConfirm () {
      this.detectResultEditDialogVisible = false
      this.getData()
    },
    handleDetectResultEditDialogClose () {
      this.detectResultEditDialogVisible = false
    },
    handleSelectAll (selection) {
      this.selectedRows = []
      selection.forEach((v, i) => {
        this.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = -1
      if (this.activeName === 'analysisOfChemotherapyDrugs') {
        index = this.selectedRows.findIndex(v => v.ctAnalyticalId === row.ctAnalyticalId)
      } else {
        index = this.selectedRows.findIndex(v => v.fid === row.fid)
      }
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs[this.activeName].toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs[this.activeName].toggleRowSelection(row, true)
      }
    },
    getTargetedDrugAnalysisTableData () {
      this.targetedDrugAnalysisTableData = []
      switch (this.targetedDrugAnalysisRadio) {
        case '提示敏感':
          this.targetedDrugAnalysisTableData = this.targetedDrugAnalysisData.targetResult1
          break
        case '提示耐药':
          this.targetedDrugAnalysisTableData = this.targetedDrugAnalysisData.targetResult2
          break
        case '结论不一致':
          this.targetedDrugAnalysisTableData = this.targetedDrugAnalysisData.targetResult3
          break
      }
    },
    handleReportTemplateDialogConfirm () {
      this.reportTemplateDialogVisible = false
      this.getData()
    },
    handleReportTemplateDialogClose () {
      this.reportTemplateDialogVisible = false
    },
    handleCreateReport () {
      this.reportTemplateDialogVisible = true
    },
    handleEditGeneticResult () {
      if (this.selectedRows.length !== 1) {
        this.$message.error('请选择一条数据')
      } else {
        let row = this.selectedRows[0]
        this.geneticResultEditDialogData = {
          fid: row.fid,
          gene: row.fgene,
          result: row.fresultRead
        }
        this.geneticResultEditDialogVisible = true
      }
    },
    handleGeneticResultEditDialogConfirm () {
      this.geneticResultEditDialogVisible = false
      this.getData()
    },
    handleGeneticResultEditDialogClose () {
      this.geneticResultEditDialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    height: 100%;
    .buttonGroup{
      height: 50px;
      line-height: 50px;
      padding: 0 20px;
      background-color: #f2f2f2;
    }
    .content{
      height: calc(100% - 50px);
      >>>.el-tabs__item{
        text-align: center;
      }
      >>>.el-tabs__content{
        height: 100%;
        padding: 10px;
        .col{
            font-size: 13px;
            height: 40px;
            line-height: 40px;
          }
        .col-margin{
          margin: 0 10px;
        }
      }
      .red{
        color: red;
      }
      .content{
        height: calc(100vh - 50px - 40px - 100px - 20px);
        overflow: auto;
      }
    }
  }
</style>
