<template>
  <div>
    <el-table
      ref="table"
      :data="tableData"
      class="dataFilterTable"
    >
      <el-table-column prop="speciesChinese" width="180" label="病原体"/>
      <el-table-column prop="speciesDescription" label="菌种说明"/>
    </el-table>
  </div>
</template>

<script>
import util from '../../../../../util/util'

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 获取病原体解释
    async getData () {
      let {code, data = []} = await this.$ajax({
        url: '/read/tngs/pathogen/get_pathogen_explanation',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.clinicInfo = []
        data.forEach(v => {
          let item = {
            speciesChinese: v.fspeciesChinese,
            speciesDescription: v.fspeciesDescription
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped></style>
