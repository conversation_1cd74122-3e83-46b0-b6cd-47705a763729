<template>
  <div>
    <el-table
      ref="table"
      :data="tableData"
      class="dataFilterTable"
    >
      <el-table-column prop="pathogen" width="180" label="病原体"></el-table-column>
      <el-table-column prop="description" label="菌种说明">
        <template slot-scope="scope">
          <div :key="index" v-for="(item, index) in scope.row.description">{{item}}</div></template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../../../util/util'

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 获取病原体解释
    async getData () {
      let {code, data = []} = await this.$ajax({
        url: '/read/pathogen/get_pathogen_interpretation',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.clinicInfo = []
        data.forEach(v => {
          let description = v.fdescription || []
          let item = {
            pathogen: v.fpathogen,
            description: description.map(v => v.freferences)
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped></style>
