<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @opened="handleOpen">
    <div class="dialog-content">
      <el-form-item label="交付类型" prop="deliverType">
        <el-select
          v-model="form.deliverType"
          placeholder="请选择"
          size="mini"
          :loading="getDeliverTypeListLoading"
          @change="handleDeliverTypeChange">
          <el-option v-for="item in deliverTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form :model="form" ref="form" size="mini" label-suffix=":" inline :rules="rules" label-width="150px">
        <el-form-item label="云地域" prop="aliyunRegion">
          <el-input v-model.trim="form.aliyunRegion" type="textarea" autosize
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="AK" prop="ak">
          <el-input v-model.trim="form.ak" type="textarea" autosize
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="SK" prop="sk">
          <el-input v-model.trim="form.sk" type="textarea" autosize
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="云Bucket" prop="aliyunBucket">
          <el-input v-model.trim="form.aliyunBucket" type="textarea" autosize
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="接收邮箱" prop="receiveEmail">
          <el-input v-model.trim="form.receiveEmail" type="textarea" autosize disabled
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="抄送邮箱" prop="ccEmail">
          <el-input v-model.trim="form.ccEmail" type="textarea" autosize
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="销售邮箱" prop="saleEmail">
          <el-input v-model.trim="form.saleEmail" type="textarea" autosize disabled
                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="交付方式" prop="deliverWay">
<!--          <el-input v-model.trim="form.deliverWay" type="textarea" autosize-->
<!--                    class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>-->
          <el-select v-model="form.deliverWay" placeholder="请选择" size="mini" @change="handleDeliverWayChange">
            <el-option v-for="item in deliverWayList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="isShow">
          <el-form-item label="finishFlag" prop="finishFlag">
            <el-input v-model.trim="form.finishFlag" type="textarea" autosize
                       class="form-width" maxlength="500" show-word-limit clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="kuaquAk" prop="kuaquAk">
            <el-input v-model.trim="form.kuaquAk" type="textarea" autosize
                      class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="kuaquSk" prop="kuaquSk">
            <el-input v-model.trim="form.kuaquSk" type="textarea" autosize
                       class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="kuaquBucket" prop="kuaquBucket">
            <el-input v-model.trim="form.kuaquBucket" type="textarea" autosize
                       class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="kehuBucket" prop="kehuBucket">
            <el-input v-model.trim="form.kehuBucket" type="textarea" autosize
                       class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="kuaquEndpoint" prop="kuaquEndpoint">
            <el-input v-model.trim="form.kuaquEndpoint" type="textarea" autosize
                       class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'
import util, {awaitWrap} from '../../../../../util/util'
import {setDeliveryAliyunConfig, getDeliverType} from '../../../../../api/deliveryManagement'

export default {
  name: 'setDeliveryConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    isShow () {
      const ways = ['aliyun_huaiwei_OMSeast2', 'aliyun_huaiwei_OMSeast3', 'aliyun_huaiwei_OMSguangzhou', 'aliyun_huaiwei_OMSnorth4', 'hw_path_kuaqu', 'oss_path_kuaqu', 'path_tar_kuaqu', 'oss_obs', 'tos_path_kuaqu']
      return ways.includes(this.form.deliverWay)
    }
  },
  data () {
    return {
      title: '编辑云配置信息',
      loading: false,
      deliverWayList: ['aliyun_aws', 'aliyun_huaiwei_OMSeast2', 'aliyun_huaiwei_OMSeast3', 'aliyun_huaiwei_OMSguangzhou', 'aliyun_huaiwei_OMSnorth4', 'hw_dir_link', 'hw_link', 'hw_path', 'hw_path_kuaqu', 'link', 'tos_path', 'oss_path', 'path_meige', 'path_tar_kuaqu', 'oss_obs', 'tos_path_kuaqu', 'oss_path_kuaqu'],
      deliverTypeList: [],
      deliverTypeLabelObj: {
        1: '阿里云',
        2: '火山云'
      },
      getDeliverTypeListLoading: false,
      form: {
        fid: '',
        aliyunRegion: '',
        deliverType: '',
        ak: '',
        sk: '',
        aliyunBucket: '',
        receiveEmail: '',
        ccEmail: '',
        deliverWay: '',
        finishFlag: '',
        kuaquAk: '',
        kuaquSk: '',
        kuaquBucket: '',
        kehuBucket: '',
        kuaquEndpoint: ''
      },
      rules: {
        deliverType: [
          { required: true, message: '请选择交付类型', trigger: 'change' }
        ],
        aliyunRegion: [{
          required: true, message: '请输入云地域', trigger: 'blur'
        }],
        ak: [{
          required: true, message: '请输入AK', trigger: 'blur'
        }],
        sk: [{
          required: true, message: '请输入SK', trigger: 'blur'
        }],
        aliyunBucket: [
          {
            required: true, message: '请输入云Bucket', trigger: 'blur'
          }
        ],
        receiveEmail: [
          {
            required: true, message: '请输入接收邮箱', trigger: 'blur'
          },
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ],
        ccEmail: [
          {
            required: true, message: '请输入抄送邮箱', trigger: 'blur'
          },
          {
            required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']
          }
        ],
        deliverWay: [{
          required: true, message: '请选择交付方式', trigger: 'change'
        }],
        kuaquAk: [{
          required: true, message: '请输入Kuaqu AK', trigger: 'blur'
        }],
        kuaquSk: [{
          required: true, message: '请输入Kuaqu SK', trigger: 'blur'
        }],
        kuaquBucket: [{
          required: true, message: '请输入Kuaqu Bucket', trigger: 'blur'
        }],
        kehuBucket: [{
          required: true, message: '请输入客户Bucket', trigger: 'blur'
        }],
        kuaquEndpoint: [{
          required: true, message: '请输入Kuaqu Endpoint', trigger: 'blur'
        }]
      }
    }
  },
  methods: {
    handleOpen () {
      this.deliverTypeList = []
      this.getDeliverTypeList()
      this.$refs.form.resetFields()
      const formData = this.formData || {}
      this.form = {
        fid: formData.id,
        aliyunRegion: formData.aliyunRegion,
        deliverType: formData.deliverType,
        ak: formData.ak,
        sk: formData.sk,
        aliyunBucket: formData.aliyunBucket,
        receiveEmail: formData.receiveEmail,
        ccEmail: formData.ccEmail,
        companyName: formData.companyName,
        deliverWay: formData.deliverWay,
        finishFlag: formData.ffinishFlag,
        kuaquAk: formData.fkuaquAkValue,
        kuaquSk: formData.fkuaquSkValue,
        kuaquBucket: formData.fkuaquBucket,
        kehuBucket: formData.fkehuBucket,
        kuaquEndpoint: formData.fkuaquEndpoint,
        saleEmail: formData.saleEmail
      }
    },
    async getDeliverTypeList () {
      const fexperimentRegion = this.formData.experimentRegion
      this.getDeliverTypeListLoading = true
      const { res } = await awaitWrap(getDeliverType(fexperimentRegion))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.deliverTypeList = data.map(v => {
          return {
            value: v.fdeliverType,
            label: this.deliverTypeLabelObj[v.fdeliverType],
            ak: v.fakValue,
            sk: v.fskValue,
            aliyunRegion: v.faliyunCloudRegion,
            aliyunBucket: v.faliyunCloudBucket
          }
        })
      }
      this.getDeliverTypeListLoading = false
    },
    handleDeliverTypeChange (val) {
      const item = this.deliverTypeList.find(v => v.value === val)
      if (item) {
        this.form.ak = item.ak
        this.form.sk = item.sk
        this.form.aliyunRegion = item.aliyunRegion
        this.form.aliyunBucket = item.aliyunBucket
      }
    },
    handleDeliverWayChange () {
      if (this.isShow) {
        this.form.finishFlag = 'Rawdata-upload-done.flag'
      }
    },
    setParams () {
      let otherParams = {}
      if (this.isShow) {
        otherParams = {
          ffinishFlag: this.form.finishFlag,
          fkuaquAkValue: this.form.kuaquAk,
          fkuaquSkValue: this.form.kuaquSk,
          fkuaquBucket: this.form.kuaquBucket,
          fkehuBucket: this.form.kehuBucket,
          fkuaquEndpoint: this.form.kuaquEndpoint
        }
      }
      return {
        fid: this.formData.id,
        faliyunCloudRegion: this.form.aliyunRegion,
        fdeliverType: this.form.deliverType,
        faliyunCloudBucket: this.form.aliyunBucket,
        fakValue: this.form.ak,
        fskValue: this.form.sk,
        fsendEmail: this.form.receiveEmail,
        fccEmail: this.form.ccEmail,
        fsaleEmail: this.form.saleEmail,
        fdeliveryMethod: this.form.deliverWay,
        ...otherParams
      }
    },
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = this.setParams()
          this.loading = true
          let {res} = await awaitWrap(setDeliveryAliyunConfig(params))
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('设置成功')
            this.$emit('dialogConfirmEvent')
            this.visible = false
          }
          this.loading = false
        } else {
          this.$message.error('表单存在错误，请检查')
        }
      })
    }
  }
}
</script>

<style scoped>
.form-width {
  width: 200px;
}

/deep/ .el-textarea__inner {
  padding-bottom: 28px !important;
}

/deep/ .el-input__count {
  height: 20px;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
