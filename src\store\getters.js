import util from '../util/util'
import Cookies from 'js-cookie'
import store from './index'
const getters = {
  userId: state => state.user.userInfo.id,
  menuList: state => state.user.menuList,
  pathList: state => state.user.pathList,
  btnList: state => state.user.btnList,
  userInfo: state => state.user.userInfo,
  token: state => state.user.token,
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  activeIndex: state => state.tagsView.activeIndex,
  pathTabs: state => state.tagsView.pathTabs,
  uploadData: state => state.dailyReportBatchUpload.uploadData,
  sampleInfo: state => state.sequencing.sampleInfo,
  getValue: (state) => {
    return category => {
      if (category === 'userInfo') { // 用户信息
        let localData = util.getSessionInfo('userInfo')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'userInfo',
            userInfo: localData
          })
        }
        return state.old.userInfo
      } else if (category === 'menuStatus') {
        let localData = Number(Cookies.get('menuStatus'))
        if (localData && state.old.menuStatus === 0) {
          store.commit({
            type: 'old/setValue',
            category: 'menuStatus',
            menuStatus: localData
          })
        }
        return state.old.menuStatus
      } else if (category === 'activeIndex') {
        let localData = util.getSessionInfo('activeIndex')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'activeIndex',
            activeIndex: localData
          })
        }
        return state.old.activeIndex
      } else if (category === 'deviceSize') {
        return state.old.deviceSize
      } else if (category === 'pathTabs') {
        return state.old.pathTabs
      } else if (category === 'enterLibraryOrder') {
        let localData = util.getSessionInfo('enterLibraryOrder')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'enterLibraryOrder',
            enterLibraryOrder: localData
          })
        }
        return state.old.enterLibraryOrder
      } else if (category === 'containerId') {
        let localData = util.getSessionInfo('containerId')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'containerId',
            containerId: localData
          })
        }
        return state.old.containerId
      } else if (category === 'loginId') {
        let localData = util.getSessionInfo('loginId')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'loginId',
            loginId: localData
          })
        }
        return state.old.loginId
      } else if (category === 'forecastData') {
        let localData = util.getSessionInfo('forecastData')
        if (localData && state.old.forecastData === null) {
          store.commit({
            type: 'old/setValue',
            category: 'forecastData',
            forecastData: localData
          })
        }
        return state.old.forecastData
      } else if (category === 'dxData') {
        let localData = util.getSessionInfo('dxData')
        if (localData && state.old.dxData === null) {
          store.commit({
            type: 'old/setValue',
            category: 'dxData',
            dxData: localData
          })
        }
        return state.old.dxData
      } else if (category === 'mutationsData') {
        let localData = util.getSessionInfo('mutationsData')
        if (localData && state.old.mutationsData === null) {
          store.commit({
            type: 'old/setValue',
            category: 'mutationsData',
            mutationsData: localData
          })
        }
        return state.old.mutationsData
      } else if (category === 'analysisRsId') {
        let localData = util.getSessionInfo('analysisRsId')
        if (localData && state.old.analysisRsId === null) {
          store.commit({
            type: 'old/setValue',
            category: 'analysisRsId',
            analysisRsId: localData
          })
        }
        return state.old.analysisRsId
      } else if (category === 'isPreview') {
        let localData = util.getSessionInfo('isPreview')
        if (localData && state.old.isPreview === null) {
          store.commit({
            type: 'old/setValue',
            category: 'isPreview',
            isPreview: localData
          })
        }
        return state.old.isPreview
      } else if (category === 'matchCancer') {
        let localData = util.getSessionInfo('matchCancer')
        if (localData && state.old.matchCancer === null) {
          store.commit({
            type: 'old/setValue',
            category: 'matchCancer',
            matchCancer: localData
          })
        }
        return state.old.matchCancer
      } else if (category === 'libraryOperatingData') {
        let localData = util.getSessionInfo('libraryOperatingData')
        if (localData && state.old.libraryOperatingData === null) {
          store.commit({
            type: 'old/setValue',
            category: 'libraryOperatingData',
            libraryOperatingData: localData
          })
        }
        return state.old.libraryOperatingData
      } else if (category === 'clinicalInfo') {
        let localData = util.getSessionInfo('clinicalInfo')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'clinicalInfo',
            clinicalInfo: localData
          })
        }
        return state.old.clinicalInfo
      } else if (category === 'templateId') {
        let localData = util.getSessionInfo('templateId')
        if (localData) {
          store.commit({
            type: 'old/setValue',
            category: 'templateId',
            templateId: localData
          })
        }
        return state.old.templateId
      } else if (category === 'deliveryInfo') {
        let localData = util.getSessionInfo('deliveryInfo')
        if (localData && state.old.deliveryInfo.id === null) {
          store.commit({
            type: 'old/setValue',
            category: 'deliveryInfo',
            deliveryInfo: localData
          })
        }
        return state.old.deliveryInfo
      } else if (category === 'applicationInfo') {
        let localData = util.getSessionInfo('applicationInfo')
        if (localData && state.old.applicationInfo.id === null) {
          store.commit({
            type: 'old/setValue',
            category: 'applicationInfo',
            applicationInfo: localData
          })
        }
        return state.old.applicationInfo
      } else if (category === 'pathogenicType') {
        let localData = util.getSessionInfo('pathogenicType')
        if (localData && state.old.pathogenicType === null) {
          store.commit({
            type: 'old/setValue',
            category: 'pathogenicType',
            pathogenicType: localData
          })
        }
        return state.old.pathogenicType
      } else if (category === 'sampleQuality') {
        let localData = util.getSessionInfo('sampleQuality')
        if (localData && state.old.sampleQuality === null) {
          store.commit({
            type: 'old/setValue',
            category: 'sampleQuality',
            sampleQuality: localData
          })
        }
        return state.old.sampleQuality
      } else if (category === 'needUpdated') {
        let localData = util.getSessionInfo('needUpdated')
        if (localData && state.old.needUpdated === null) {
          store.commit({
            type: 'old/setValue',
            category: 'needUpdated',
            needUpdated: localData
          })
        }
        return state.old.needUpdated
      } else if (category === 'pixelRatio') {
        let localData = util.getSessionInfo('pixelRatio')
        if (localData && state.old.pixelRatio === null) {
          store.commit({
            type: 'old/setValue',
            category: 'pixelRatio',
            pixelRatio: localData
          })
        }
        return state.old.pixelRatio
      }
    }
  }
}

export default getters
