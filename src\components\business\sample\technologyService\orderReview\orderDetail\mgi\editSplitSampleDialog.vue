<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="900px"
      @open="handleOpen">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        v-if="visible"
        label-width="180px"
        size="mini"
        inline=""
        label-suffix="：">
        <el-form-item label="样本名称" prop="sampleName">
          <el-select v-model="form.sampleName" class="form-width" placeholder="请选择">
            <template v-for="item in sampleNameList">
              <el-option :key="item" :label="item" :value="item"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="子文库名称" prop="subLibraryName">
          <el-input v-model.trim="form.subLibraryName" maxlength="50" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="Index位数" prop="indexDigits">
          <el-input v-model.trim="form.indexDigits" :min="0" :max="50" type="number" class="form-width" placeholder="请输入"  @blur="handleNumBlur('indexDigits')"></el-input>
        </el-form-item>
        <el-form-item label="Index编号" prop="indexNum">
          <el-input v-model.trim="form.indexNum" maxlength="50" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="单（双）Index" prop="indexSpecies">
          <el-select v-model="form.indexSpecies" class="form-width" placeholder="请选择" @change="handleChangeIndex">
            <el-option label="单Index（indexF）" value="单Index（indexF）"></el-option>
            <el-option label="单Index（indexR）" value="单Index（indexR）"></el-option>
            <el-option label="双Index" value="双Index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.indexSpecies !== '单Index（indexR）'" label="IndexF序列5'-3’方向" prop="index1Index">
          <el-input v-model.trim="form.index1Index"  maxlength="10" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item v-if="form.indexSpecies !== '单Index（indexF）'" label="IndexR序列5'-3’方向" prop="index2Index">
          <el-input v-model.trim="form.index2Index" maxlength="16" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="数据量（G）" prop="subLibraryDateNum">
          <el-input v-model.trim="form.subLibraryDateNum" :min="0" type="number" maxlength="20" class="form-width" placeholder="请输入" @blur="handleNumBlur('subLibraryDateNum')"></el-input>
        </el-form-item>
        <el-form-item :rules="isCoreSpun ? rules.baseBalance : [{required: false}]" label="碱基平衡" prop="baseBalance">
          <el-select v-model="form.baseBalance" class="form-width" placeholder="请选择">
            <el-option label="碱基平衡" value="碱基平衡"></el-option>
            <el-option label="碱基不平衡" value="碱基不平衡"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文库类型" prop="libraryType">
          <el-select v-model="form.libraryType" class="form-width" placeholder="请选择">
            <template v-for="item in libraryTypeList">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="index类型" prop="indexType">
          <el-select v-model="form.indexType" class="form-width" placeholder="请选择">
            <el-option label="华大官方单端index（128）" value="华大官方单端index（128）"></el-option>
            <el-option label="华大官方单端index（96）" value="华大官方单端index（96）"></el-option>
            <el-option label="华大官方双端index" value="华大官方双端index"></el-option>
            <el-option label="其他（请在备注中说明）" value="其他（请在备注中说明）"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建库试剂盒" prop="dataBox">
          <el-input v-model.trim="form.dataBox" maxlength="50" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="subLibraryNotes">
          <el-input v-model.trim="form.subLibraryNotes" maxlength="150" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../../../util/mixins'

export default {
  name: `editSplitSampleDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Object | null
    },
    rowIndex: Number | null,
    sampleNameList: Array,
    isCoreSpun: Boolean // 应用类型是否是包芯上机
  },
  computed: {},
  data () {
    // 第一个验证，仅允许数字、字母、- _ 组成
    const validSubLibraryName = (rule, value, callback) => {
      let regx = /^[0-9A-Za-z_]{1,}$/
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error('仅允许数字和字母还有“_”组成'))
      }
    }
    const sameNameValid = (rule, value, callback) => {
      this.form.libraryName === value ? callback(new Error('子文库名称不能与样本名称相同')) : callback()
    }
    // 仅允许输入大写的A,T,C,G四类字母
    const indexVaild = function (rule, value, callback) {
      console.log(rule)
      let regx = /^(A|T|C|G|-)+$/g
      if (rule.field === 'index2Index' && !value) {
        callback()
        return
      }
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error('只允许输入包含ATCG四个字母的序列'))
      }
    }
    return {
      title: '',
      form: {},
      libraryTypeList: [
        {label: '全基因组文库-WGS', value: '全基因组文库-WGS'},
        {label: 'PCR-free文库', value: 'PCR-free文库'},
        {label: '全外显子文库-WES', value: '全外显子文库-WES'},
        {label: '目标区域捕获文库', value: '目标区域捕获文库'},
        {label: 'mRNA链特异性文库', value: 'mRNA链特异性文库'},
        {label: 'IncRNA链特异性文库', value: 'IncRNA链特异性文库'},
        {label: '普通转录组文库', value: '普通转录组文库'},
        {label: '宏基因组文库', value: '宏基因组文库'},
        {label: '宏转录因组文库', value: '宏转录因组文库'},
        {label: '10x3’单细胞文库', value: '10x3’单细胞文库'},
        {label: '10x5’单细胞文库', value: '10x5’单细胞文库'},
        {label: '10xATAC单细胞文库', value: '10xATAC单细胞文库'},
        {label: '甲基化文库', value: '甲基化文库'},
        {label: 'ATAC-seq文库', value: 'ATAC-seq文库'},
        {label: 'chip-seq文库', value: 'chip-seq文库'},
        {label: 'Hi-C文库', value: 'Hi-C文库'},
        {label: 'TCR文库', value: 'TCR文库'},
        {label: '其他（请在备注中说明）', value: '其他（请在备注中说明）'}
      ],
      rules: {
        sampleName: [
          {required: true, message: '请输入', trigger: 'blur'},
          {validator: validSubLibraryName, trigger: 'blur'}
        ],
        subLibraryName: [
          {required: true, message: '请输入', trigger: 'blur'},
          {validator: validSubLibraryName, trigger: 'blur'},
          {validator: sameNameValid, trigger: 'blur'}
        ],
        indexNum: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        indexSpecies: [
          {required: true, message: '请选择单（双）index', trigger: 'blur'}
        ],
        index1Index: [
          {required: true, message: '请输入', trigger: 'blur'},
          {validator: indexVaild, trigger: 'blur'}
        ],
        index2Index: [
          {required: true, message: '请输入', trigger: 'blur'},
          {validator: indexVaild, trigger: 'blur'}
        ],
        subLibraryDateNum: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        baseBalance: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        libraryType: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        indexType: [
          {required: true, message: '请选择', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      if (this.pdata) {
        this.form = {...this.pdata}
        this.title = this.form.sampleName ? '编辑拆分样本信息' : '新增拆分样本信息'
      } else {
        this.title = '新增拆分样本信息'
        this.form = {
          sampleName: '',
          subLibraryName: '',
          indexDigits: '',
          indexNum: '',
          indexSpecies: '',
          index1Index: '',
          index2Index: '',
          subLibraryDateNum: '',
          baseBalance: '',
          libraryType: '',
          indexType: '',
          dataBox: '',
          libraryNotes: ''
        }
      }
      console.log(this.pdata, this.form, 'form')
    },
    // 数字类型的输入失焦后的操作
    handleNumBlur (k) {
      let v = +this.form[k]
      if (isNaN(v) || v < 0) {
        this.form[k] = ''
      }
    },
    // 更改单(双)index
    handleChangeIndex (val) {
      if (val === '单Index（indexF）') {
        this.form.index2Index = ''
      } else if (val === '单Index（indexR）') {
        this.form.index1Index = ''
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let oldName = ''
          if (this.pdata) oldName = this.pdata.sampleName + '-' + this.pdata.subLibraryName
          this.$emit('dialogConfirmEvent', this.form, this.rowIndex, oldName)
        }
      })
    },
    async handleClose () {
      await this.$confirm(`是否确认放弃${this.pdata && this.pdata.sampleName ? '编辑' : '新增'}？确认后关闭弹窗`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
.form-width{
  width: 200px;
}
</style>
