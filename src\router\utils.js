/**
 * 路由工具函数
 */

/**
 * 将路径转换为路由路径格式
 * @param {string} path 原始路径
 * @returns {string} 格式化后的路由路径
 */
export function formatPath (path) {
  // 确保路径以/开头
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  return path
}

/**
 * 生成面包屑导航数据
 * @param {Object} route 当前路由对象
 * @param {Array} routes 路由配置数组
 * @returns {Array} 面包屑导航数据
 */
export function generateBreadcrumbs (route, routes) {
  const breadcrumbs = []
  if (!route.matched || route.matched.length === 0) {
    return breadcrumbs
  }

  route.matched.forEach(match => {
    if (match.meta && match.meta.title) {
      breadcrumbs.push({
        title: match.meta.title,
        path: match.path
      })
    }
  })

  return breadcrumbs
}

/**
 * 检查用户是否有权限访问该路由
 * @param {Object} route 路由对象
 * @param {Array} permissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasPermission (route, permissions) {
  if (!route.meta || !route.meta.permission) {
    return true
  }

  if (!permissions || permissions.length === 0) {
    return false
  }

  return permissions.some(permission => {
    return route.meta.permission === permission
  })
}

/**
 * 过滤用户有权限的路由
 * @param {Array} routes 路由配置数组
 * @param {Array} permissions 用户权限列表
 * @returns {Array} 过滤后的路由配置
 */
export function filterRoutes (routes, permissions) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(tmp, permissions)) {
      if (tmp.children) {
        tmp.children = filterRoutes(tmp.children, permissions)
      }
      res.push(tmp)
    }
  })

  return res
}
