<template>
  <el-dialog
    title="数据导入"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :width="dialogWidth"
    @open="handleOpen">
    <el-form ref="form" size="mini" :model="form" label-suffix=":" label-width="80px">
      <el-form-item label="样本导入">
        <el-upload
          ref="upload"
          :auto-upload="false"
          :file-list="fileList"
          :action="uploadUrl"
          :data="uploadParams"
          class="upload-demo"
          :on-error="handleError"
          :before-upload="handleBeforeUpload"
          :on-change="handleChange"
          :on-success="handleOnSuccess"
        >
          <el-button size="mini" icon="el-icon-upload">上传文件</el-button>
          <div slot="tip" class="el-upload__tip">支持格式：Excel文件（xlsx、xls）</div>
        </el-upload>
      </el-form-item>
      <div style="padding-left: 80px">
        <el-button type="text" size="mini" @click="handleDownload">导入模板下载</el-button>
      </div>
      <div class="tips">
        注：<br/>
        1.仅支持10ML以内的Exce格式的文件上传<br/>
        2.原始样本名称、吉因加编号，必填，组织核酸样本无原始子文库名称（可不填），重上机没有完成上
        机，可不填芯片号（需填监控截止时间）
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>

import mixins from '@/util/mixins'
import constants from '@/util/constants'
import {awaitWrap, downloadFile, readBlob} from '@/util/util'
import {downloadPersonalTemplate} from '@/api/deliveryManagement/index'
export default {
  name: 'uploadResultDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    personalizeType: {
      type: String,
      default: ''
    }
  },
  computed: {
    uploadParams () {
      return {
        personalizeType: this.personalizeType
      }
    }
  },
  data () {
    return {
      isUpload: false,
      loading: false,
      fileList: [],
      uploadUrl: constants.JS_CONTEXT + '/experiment/cos_deliver_order/import_excel',
      form: {},
      dialogWidth: '500px',
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
      this.loading = false
    },
    async handleDownload () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadPersonalTemplate())
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    handleError () {
      this.loading = false
      this.$message.error('导入失败')
    },
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.loading = false
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.loading = false
        return false
      }
    },
    async handleChange (file, fileList) {
      if (fileList.length < 2) return
      const message = '一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？'
      const {err} = await awaitWrap(this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      err ? fileList.pop() : fileList.shift()
    },
    handleOnSuccess (res) {
      if (res && res.code === this.SUCCESS_CODE) {
        this.$refs.upload.clearFiles()
        this.visible = false
        const {cosDeliverPersonalizeHeads} = res.data
        // this.$message({
        //   message: `成功导入${successCount}条，导入失败${failCount}条`,
        //   type: 'success'
        // })
        this.$emit('dialogConfirmEvent', cosDeliverPersonalizeHeads || [])
      } else {
        this.$refs.upload.clearFiles()
        if (res && res.data) {
          this.$showErrorDialog({tableData: res.data, isShowButton: false})
        } else {
          this.$message.error(res.message)
        }
      }
      this.loading = false
    },
    // 下一步
    handleConfirm () {
      const fileList = this.$refs.upload.uploadFiles || []
      if (fileList.length < 1) {
        this.$message.error('请上传任务文件')
        return
      }
      this.loading = true
      this.$refs.upload.submit()
    }
  }
}
</script>

<style scoped lang="scss">

.flex-wrapper {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
}

.tips {
  font-size: 12px;
  color: #888;
}

</style>
