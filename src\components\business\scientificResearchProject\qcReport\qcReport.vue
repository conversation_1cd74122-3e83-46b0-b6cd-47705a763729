<template>
  <div>
    <!--操作按钮区-->
    <div class="flex search-form">
      <!--操作按钮区-->
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('017001001', 'buttons')" size="mini" type="primary" @click="handleReport(1)">生成报告</el-button>
        <el-button v-if="$setAuthority('017001001', 'buttons')" size="mini" type="primary" @click="handleReport(2)">上传报告</el-button>
      </div>
      <!--查询区-->
      <div>
        <div class="flex">
          <el-select v-model.trim="searchType" style="width: 144px!important" size="mini" clearable placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in optionsList">
            </el-option>
          </el-select>
          <el-select
            v-if="searchType === 'forderType'"
            v-model="searchValue"
            filterable
            clearable
            style="width: 256px"
            collapse-tags
            class="input-width"
            size="mini"
          >
            <el-option
              :key="k"
              :label="k"
              :value="v * 1"
              v-for="(k, v) in orderTypes"></el-option>
          </el-select>
          <el-input
            v-else
            v-model.trim="searchValue"
            :disabled="!searchType"
            size="mini"
            style="width: 256px"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleSearch()"></el-input>
          <div class="btn">
            <!--            需要设置参数为空，防止按钮属性传递-->
            <el-button size="mini" type="primary" @click="handleSearch()">查询</el-button>
            <el-button size="mini" type="primary" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleAdvancedReport">高级查询</el-button>
          </div>
        </div>
      </div>
    </div>

    <!--表格-->
    <div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        height="calc(100vh - 74px - 40px - 42px - 32px)"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="sampleReportStatusText" label="样本报告状态" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="scope.row.sampleReportStatusClass">{{scope.row.sampleReportStatusText}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="subOrderReportStatusText" label="子订单报告状态" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="scope.row.subOrderReportStatusClass">{{scope.row.subOrderReportStatusText}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taskOrderNum" label="任务单号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskOrderType" label="任务单类型" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneCode" label="吉因加编号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="nucleateCode" label="核酸编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originalCode" label="原始样本名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleType" label="样本类型" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="type" label="订单类型" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="detectType" label="产品名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="subOrderCode" label="子订单编号" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderCode" label="订单编号" min-width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <!--查询抽屉-->
    <search-dialog
      ref="searchDialog"
      :pvisible.sync="searchVisible"
      @dialogConfirmEvent="handleSearch"/>
    <generate-report-dialog
      :pvisible.sync="generateReportVisible"
      :qc-result-id-list="qcResultIdList"
      :order-code="orderCode"
      :report-type="1"
      @dialogConfirmEvent="getData"/>
    <upload-report-dialog
      :pvisible.sync="uploadReportVisible"
      :qc-result-id-list="qcResultIdList"
      :order-code="orderCode"
      :report-type="1"
      @dialogConfirmEvent="getData">
    </upload-report-dialog>
  </div>
</template>

<script>

import mixins from '../../../../util/mixins'
import searchDialog from './components/searchDialog'
import util from '../../../../util/util'
import GenerateReportDialog from '../components/generateReportDialog'
import UploadReportDialog from './components/uploadReportDialog' // 查询抽屉

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    UploadReportDialog,
    GenerateReportDialog,
    searchDialog
  },
  watch: {
    searchType (newValue) {
      this.searchValue = ''
      if (!newValue) {
        this.handleSearch()
      }
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      searchVisible: false,
      generateReportVisible: false,
      uploadReportVisible: false,
      qcResultIdList: [],
      orderCode: '',
      searchType: '',
      searchValue: '',
      advanceForm: {},
      submitForm: {},
      orderTypes: {
        1: 'illumina文库订单',
        2: 'MGI文库订单',
        3: '组织或核酸样本订单',
        5: '单细胞订单'
      },
      optionsList: [
        {
          label: '任务单号',
          value: 'ftaskOrderNum'
        }, {
          label: '吉因加编号',
          value: 'fgeneCode'
        }, {
          label: '核酸编号',
          value: 'fnucleateCode'
        }, {
          label: '项目编号',
          value: 'fprojectCode'
        }, {
          label: '样本类型',
          value: 'fsampleType'
        }, {
          label: '子订单编号',
          value: 'fsubOrderCode'
        },
        // 订单类型，任务单类型，产品名称
        {
          label: '订单类型',
          value: 'forderType'
        },
        {
          label: '任务单类型',
          value: 'ftaskOrderType'
        },
        {
          label: '产品名称',
          value: 'fdetectType'
        }
      ],
      statusOptions: {
        '': {
          text: '未生成',
          class: 'not-generate'
        },
        1: {
          text: '审核通过',
          class: ''
        },
        10: {
          text: '已发送',
          class: ''
        },
        2: {
          text: '生成失败',
          class: 'fail-generate'
        },
        20: {
          text: '被驳回',
          class: 'fail-generate'
        },
        3: {
          text: '未生成',
          class: 'not-generate'
        },
        30: {
          text: '生成中',
          class: 'generate'
        },
        31: {
          text: '待审核',
          class: ''
        }
      },
      tableData: []
    }
  },
  methods: {
    // 生成报告
    // 1.在当前页面选中至少一条记录进行操作。
    // 2.所选记录的“项目编号”相同。
    // 3.所选记录的“样本报告状态”为“生成失败”或“被驳回”或“未生成”。
    // 所选记录的“子订单报告状态”为“未生成”。
    handleReport (type) {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何记录')
        return
      }
      let rows = [...this.selectedRows.values()]
      let orderCode = rows[0].orderCode
      if (rows.some(v => v.orderCode !== orderCode)) {
        this.$message.error('不同项目的样本不允许一同生成质控报告')
        return
      }
      if (rows.some(v => ![2, 20, 3].includes(v.sampleReportStatus))) {
        this.$message.error('仅允许选择样本报告状态为“生成失败”、“被驳回”、“未生成”的记录进行操作')
        return
      }
      // if (rows.some(v => v.subOrderReportStatus !== 3)) {
      //   this.$message.error('仅允许选择子订单报告状态为“未生成”的记录进行操作')
      //   return
      // }
      this.qcResultIdList = [...this.selectedRows.keys()]
      this.orderCode = orderCode
      if (type === 1) {
        this.generateReportVisible = true
        return
      }
      this.uploadReportVisible = true
    },
    // 查询
    handleSearch (data) {
      if (data) {
        this.advanceForm = data
      } else {
        this.advanceForm = this.$refs.searchDialog.setParams()
      }
      this.submitForm = util.deepCopy(this.advanceForm)
      this.submitForm.searchKey = this.searchType
      this.submitForm.searchValue = this.searchValue
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    // 重置分别重置高级和简易查询
    handleReset () {
      this.searchType = ''
      this.searchValue = ''
      this.handleSearch()
    },
    // 高级查询
    handleAdvancedReport () {
      this.searchVisible = true
    },
    // 获取表格数据
    getData () {
      let data = util.getSessionInfo('currentLab') || []
      let options = {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      }
      data = data.map(v => {
        return options[v]
      })
      this.$ajax({
        url: '/order/report/get_sample_list',
        data: {
          areaList: data,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          ...util.deepCopy(this.submitForm)
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.rows || []
          data.forEach((v) => {
            let sampleReportStatus = this.statusOptions[v.fsampleReportStatus] || {
              text: '',
              class: ''
            }
            let subOrderReportStatus = this.statusOptions[v.fsubOrderReportStatus] || {
              text: '',
              class: ''
            }
            let item = {
              sampleReportStatusText: sampleReportStatus.text,
              sampleReportStatusClass: sampleReportStatus.class,
              subOrderReportStatusText: subOrderReportStatus.text,
              subOrderReportStatusClass: subOrderReportStatus.class,
              sampleReportStatus: v.fsampleReportStatus,
              subOrderReportStatus: v.fsubOrderReportStatus,
              taskOrderNum: v.ftaskOrderNum,
              taskOrderType: v.ftaskOrderType,
              geneCode: v.fgeneCode,
              nucleateCode: v.fnucleateCode,
              originalCode: v.foriginalCode,
              sampleType: v.fsampleType,
              projectCode: v.fprojectCode,
              projectName: v.fprojectName,
              type: v.ftype,
              detectType: v.fdetectType,
              subOrderCode: v.fsubOrderCode,
              orderCode: v.forderCode,
              id: v.fqcResultId
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.handleEchoSelect()
          })
        }
      })
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row, rowIndex}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff !important', padding: 0, height: '24px'}
      }
      return {padding: 0, height: '24px'}
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  justify-content: space-between;
}
.buttonGroup{
  margin: 10px 0;
  height: 40px;
  line-height: 40px;
}
.btn {
  margin-left: 10px;
}
.not-generate {
  color: $color
}
.generate {
  color: $success-color
}
.fail-generate {
  color: $fail-color
}

</style>
