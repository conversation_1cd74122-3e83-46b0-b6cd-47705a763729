<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :modal-append-to-body='false'
      :before-close="handleClose"
      width="800px"
      append-to-body
      @opened="handleOpen">
      <el-table :data="tableData"
                border
                class="table"
                size="mini">
        <el-table-column prop="customer"  label="客户" min-width="360">
          <template slot-scope="scope">
            <div v-if="!scope.row.isFix">
              {{scope.row.customer}}
            </div>
            <div v-if="scope.row.isFix">
              <el-select-v2
                v-model.trim="scope.row.customer"
                :options="customerList"
                value-key="hospitalAndDoctor"
                label-key="hospitalAndDoctor"
                size="mini"
                clearable
                collapse-tags
                filterable
                placeholder="请选择"
                style="width: 100%"/>
<!--              <el-select v-model.trim="scope.row.customer"-->
<!--                         v-el-select-loadmore:rangeNumber="loadMore(rangeNumber)"-->
<!--                         size="mini"-->
<!--                         clearable-->
<!--                         filterable-->
<!--                         placeholder="请选择"-->
<!--                         style="width: 100%"-->
<!--              >-->
<!--                <el-option-->
<!--                  :key="index"-->
<!--                  :label="item.hospitalAndDoctor"-->
<!--                  :value="item.hospitalAndDoctor"-->
<!--                  v-for="(item, index) in customerList">-->
<!--                </el-option>-->
<!--              </el-select>-->
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reportName" label="报告模版" min-width="150">
          <template slot-scope="scope">
            <div v-if="!scope.row.isFix">
              {{scope.row.reportName}}
            </div>
            <div v-if="scope.row.isFix">
              <el-select-v2
                v-model.trim="scope.row.reportCode"
                :options="templates"
                value-key="reportCode"
                label-key="reportName"
                size="mini"
                clearable
                collapse-tags
                filterable
                placeholder="请选择"
                style="width: 100%"/>
<!--              <el-select v-model.trim="scope.row.reportCode" size="mini" clearable filterable placeholder="请选择" style="width: 100%">-->
<!--                <el-option-->
<!--                  :key="item.reportCode"-->
<!--                  :label="item.reportName"-->
<!--                  :value="item.reportCode"-->
<!--                  v-for="item in templates">-->
<!--                </el-option>-->
<!--              </el-select>-->
            </div>
          </template>
        </el-table-column>
        <el-table-column  label="操作" min-width="60">
          <template slot-scope="scope">
            <div class="icon-wrapper">
              <div style="cursor: pointer" @click="openFix(scope.row, scope.$index)">{{scope.row.isFix ? '保存' : '修改'}}</div> <span style="color: #EBEEF5">|</span>
              <div style="cursor: pointer" slot="reference" @click="handleDelete(scope.$index)">删除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-button style="margin-top: 5px; width: 100%; cursor: pointer" @click="addCustomerTemplate">+ 新增</el-button>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'productCustomerTemplateDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    title: String,
    productId: Number
  },
  directives: {
    'el-select-loadmore': {
      bind (el, binding) {
        // 获取element-ui定义好的scroll盒子
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        SELECTWRAP_DOM.addEventListener('scroll', () => {
          /**
           * scrollHeight 获取元素内容高度(只读)
           * scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
           * clientHeight 读取元素的可见高度(只读)
           * 如果元素滚动到底, 下面等式返回true, 没有则返回false:
           * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
           */
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight
          if (condition) binding.value()
        })
      }
    }
  },
  data () {
    return {
      templates: [], // 模版列表
      tableData: [], // 客户定制模版列表
      customerList: [],
      rangeNumber: 10
    }
  },
  methods: {
    async handleOpen () {
      this.tableData = []
      this.getAllTemplates()
      this.getData()
      await this.getCustomer()
    },
    loadMore (n) {
      // n是默认初始展示的条数会在渲染的时候就可以获取,具体可以打log查看
      // if(n < 8) this.rangeNumber = 10 //elementui下拉超过7条才会出滚动条,如果初始不出滚动条无法触发loadMore方法
      // eslint-disable-next-line no-return-assign
      return () => this.rangeNumber += 5 // 每次滚动到底部可以新增条数  可自定义
    },
    // 获取模版 /system/product/get_all_template
    getAllTemplates () {
      this.$ajax({
        url: '/system/product/get_all_template',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.templates = []
          result.data.forEach(v => {
            let item = {
              reportCode: v.reportCode, // 报告模版id
              reportName: v.reportName
            }
            this.templates.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // /system/product/get_combo_type_list
    // getcomboTypeList () {
    //   this.$ajax({
    //     url: '/system/product/get_combo_type_list',
    //     method: 'get'
    //   }).then((result) => {
    //     if (result.code === this.SUCCESS_CODE) {
    //       result.data = result.data || []
    //       this.templates = []
    //       result.data.forEach(v => {
    //         let item = {
    //           fcomboTypeName: v.fcomboTypeName, // 报告模版id
    //           fid: v.fid
    //         }
    //         this.templates.push(item)
    //       })
    //     } else {
    //       this.$message.error(result.message)
    //     }
    //   }).catch((e) => {
    //     console.log(e)
    //   })
    // },
    // 获取客户定制模版列表 /system/product/get_template_config_list
    getData () {
      this.$ajax({
        url: '/system/product/get_template_config_list',
        method: 'get',
        loadingDom: '.table',
        data: {
          productId: this.productId
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.tableData = []
          result.data.forEach(v => {
            let item = {
              fid: v.fid,
              reportName: v.reportName, // 报告模版名
              reportCode: v.reportCode,
              doctorName: v.doctorName, // 医生姓名
              hospitalName: v.hospitalName,
              descNum: v.descNum, // 排序号
              customer: `${v.hospitalName}/${v.doctorName}`,
              isFix: false
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 切换修改保存
    openFix (row, index) {
      let item = this.tableData[index]
      if (item.isFix === false) {
        item.isFix = true
      } else {
        let isCheck = this.checkRow(row)
        console.log(isCheck, '111111111')
        if (isCheck) {
          let flag = this.fixProductExperiment(row)
          if (flag) item.isFix = false
        }
      }
    },
    // 保存或修改实验配置参数
    fixProductExperiment (row) {
      let saveFlag = false
      console.log(row)
      this.$ajax({
        url: '/system/product/sava_product_template_config',
        method: 'post',
        data: {
          productId: this.productId,
          fid: row.fid,
          customerCode: this.customerList.find(item => item.hospitalAndDoctor === row.customer).customerCode,
          doctorCode: this.customerList.find(item => item.hospitalAndDoctor === row.customer).doctorCode,
          reportCode: row.reportCode
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存定制模板配置成功')
          saveFlag = true
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
      return saveFlag
    },
    // 校验保存数据
    checkRow (row) {
      console.log('校验中')
      // 2) 同一个产品里“样本标签”+“文库类型”+“探针”不能重复
      let list = this.tableData.filter(item =>
        item.fsampleSign + item.flibType + item.fprobe === row.fsampleSign + row.flibType + row.fprobe
      )
      if (list.length > 1) {
        this.errorMessage = '同一个产品里“样本标签”+“文库类型”+“探针”不能重复'
        this.$message.error(this.errorMessage)
        return false
      }
      return true
      // 数据量：非必填，正数，可以有小数
      // 杂交基数：非必填，正数，可以有小数
    },
    // 获取客户 /system/product/get_hospital_and_doctor
    getCustomer () {
      this.$ajax({
        url: '/system/product/get_hospital_and_doctor',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.customerList = []
          result.data.forEach(v => {
            let item = {
              customerCode: v.customerCode, // 客户id
              hospitalAndDoctor: v.hospitalAndDoctor, // 客户名
              customerId: v.customerId, // 医生id
              doctorId: v.doctorId, // 医生名
              doctorCode: v.doctorCode
            }
            this.customerList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 新增定制化模版
    addCustomerTemplate () {
      this.tableData.push(
        {
          reportName: '', // 报告模版名
          doctorName: '', // 医生姓名
          hospitalName: '',
          descNum: '', // 排序号
          customer: '',
          isFix: true
        }
      )
    },
    // 删除一行实验配置参数
    handleDelete (index) {
      console.log(index)
      if (!this.tableData[index].fid) {
        this.tableData.splice(index, 1)
      } else {
        let self = this
        this.$ajax({
          url: '/system/product/delete_product_template_config',
          method: 'get',
          data: {fid: this.tableData[index].fid}
        }).then((result) => {
          if (result.code === this.SUCCESS_CODE) {
            self.tableData.splice(index, 1)
            this.$message.success('删除产品实验参数配置成功')
          } else {
            this.$message.error(result.message)
          }
        }).catch((e) => {
          console.log(e)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.icon-wrapper {
  display: flex;
  align-content: center;
  justify-content: space-between;
  color: deepskyblue;
}
</style>
