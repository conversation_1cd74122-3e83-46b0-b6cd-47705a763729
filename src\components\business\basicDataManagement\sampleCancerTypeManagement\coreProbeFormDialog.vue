<template>
  <el-dialog :title="title" :visible.sync="visible" width="600px" append-to-body :close-on-click-modal="false" @close="handleClose" @opened="handleOpen">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="mini">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="芯片类型" prop="chipType">
            <el-input
              v-model.trim="form.chipType"
              placeholder="请输入芯片类型"
              maxlength="10"
              show-word-limit
              @blur="handleChipTypeChange">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="癌种分类" prop="cancerCategory">
            <el-input
              v-model.trim="form.cancerCategory"
              placeholder="自动生成"
              disabled>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="癌种" prop="cancerType">
            <el-input
              v-model.trim="form.cancerType"
              placeholder="请输入癌种"
              maxlength="10"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model.trim="form.remark"
              placeholder="自动生成"
              disabled>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm" :loading="loading">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { editCoreProbeConfig, getCancerCategory } from '@/api/basicDataManagement/sampleCancerTypeApi'
import { awaitWrap } from '@/util/util'
import mixins from '@/util/mixins'

export default {
  name: 'CoreProbeFormDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    pvisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增核心探针配置'
    },
    formData: {
      type: Object,
      default: null
    },
    cancerTypeId: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      form: {
        chipType: '',
        cancerCategory: '',
        cancerType: '',
        remark: ''
      },
      rules: {
        chipType: [
          { required: true, message: '请输入芯片类型', trigger: 'blur' },
          { max: 10, message: '最多输入10个字符', trigger: 'blur' },
          // 必须已m,v k开头, 后面不限制也可以不输入
          { pattern: /^[mvk][a-zA-Z]*$/, message: '必须以m,v,k开头的字母', trigger: 'blur' }
        ],
        cancerCategory: [
          { required: true, message: '请输入癌种分类', trigger: 'blur' }
        ],
        cancerType: [
          { required: true, message: '请输入癌种', trigger: 'blur' },
          { max: 10, message: '最多输入10个字符', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入备注', trigger: 'blur' }
        ]
      },
      loading: false,
      existingCategories: [] // 存储已存在的癌种分类
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()

      if (this.formData) {
        // 编辑模式
        this.form = {
          chipType: this.formData.chipType,
          cancerCategory: this.formData.cancerCategory,
          cancerType: this.formData.cancerType,
          remark: this.formData.remark
        }
      } else {
        // 新增模式
        this.form = {
          chipType: '',
          cancerCategory: '',
          cancerType: '',
          remark: ''
        }
      }
    },

    async handleChipTypeChange () {
      // 编辑模式下，不执行处理
      if (this.formData) return
      // 芯片类型变化时，自动生成癌种分类和备注
      await this.generateCancerCategory()
      this.generateRemark()
    },
    async generateCancerCategory () {
      // 校验是否为空， 格式是否正确
      if (!this.form.chipType) {
        return
      }
      const pattern = /^[mvk][a-zA-Z0-9]*$/
      if (!pattern.test(this.form.chipType)) {
        return
      }
      // 调用生成癌种分类的接口
      const params = {
        fchipType: this.form.chipType
      }

      const { res } = await awaitWrap(getCancerCategory(params))
      console.log('res', res.code, this.SUCCESS_CODE)
      if (res && res.code === this.SUCCESS_CODE) {
        this.form.cancerCategory = res.data
      }
    },
    generateRemark () {
      const cancerCategory = this.form.cancerCategory
      if (!cancerCategory) {
        this.form.remark = ''
        return
      }

      if (cancerCategory.startsWith('DT-')) {
        this.form.remark = '1021芯片'
      } else if (cancerCategory.startsWith('WES-')) {
        this.form.remark = 'WES芯片'
      } else {
        this.form.remark = ''
      }
    },

    async handleConfirm () {
      try {
        await this.$refs.form.validate()
        this.loading = true

        const params = {
          fid: this.formData ? this.formData.fid : '',
          fchipType: this.form.chipType,
          fcancerType: this.form.cancerCategory,
          fcancer: this.form.cancerType,
          fremark: this.form.remark
        }

        const { res } = await awaitWrap(editCoreProbeConfig(params))
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('dialogConfirmEvent')
          this.handleClose()
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
