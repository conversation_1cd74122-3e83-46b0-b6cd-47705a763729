<template>
  <div style="margin-bottom: 20px;">
    <div style="border: 1px solid #f2f2f2;padding: 10px;">
      <h4>{{ title }}</h4>
      <el-table
        ref="table"
        :data="tableData"
        class="dataFilterTable"
        :row-style="handleRowStyle"
      >
        <el-table-column prop="geneList" label="耐药基因" show-overflow-tooltip>
          <template slot-scope="scope">
            <i>{{ scope.row.geneList }}</i>
          </template>
        </el-table-column>
        <el-table-column prop="reads" label="序列数" show-overflow-tooltip/>
        <el-table-column prop="miniResistantDrug" label="作用抗生素" show-overflow-tooltip/>
        <el-table-column prop="machineProcessed" label="机制" show-overflow-tooltip/>
        <el-table-column prop="correspondingPathogen" label="疑似来源物种" width="140" show-overflow-tooltip/>
        <el-table-column prop="relevanceSpeciesDrug" label="物种关联置信度" width="140" show-overflow-tooltip/>
        <el-table-column prop="relevanceClinic" label="临床表型关联置信度" width="150" show-overflow-tooltip/>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String
    },
    tableData: {
      type: Array
    }
  },
  methods: {
    handleRowStyle ({row}) {
      console.log(row)
      return row.report === 'Y' ? {color: 'red'} : {}
    }
  }
}
</script>

<style scoped></style>
