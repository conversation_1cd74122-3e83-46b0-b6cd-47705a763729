<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="多次检测提醒"
      width="50%"
    >
      <div>
        以下样本与录入样本身份证/护照一致
        <el-table
          :data="tableData" height="200" size="mini"
          style="width: 100%">
          <el-table-column prop="sampleNum" label="样例编号" width="120"></el-table-column>
          <el-table-column prop="name" label="姓名" width="120"></el-table-column>
          <el-table-column prop="idcard" label="身份证/护照" width="140"></el-table-column>
          <el-table-column prop="productName" label="产品名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="projectName" label="项目名称" min-width="140" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'patientSplitSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ptableData: {
      type: Array,
      default: function () {
        return []
      },
      required: true
    }
  },
  data () {
    return {
      tableData: this.ptableData
    }
  },
  methods: {
    handleClose () {
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
