<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="核心探针管理"
      width="50%"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-button type="primary" size="mini" style="margin-bottom: 20px" @click="handleEdit(null)">新增</el-button>
      <el-table
        :data="tableData"
        border
        class="table"
        size="mini"
        height="400">
        <el-table-column prop="cancerName" min-width="100" label="匹配癌种" show-overflow-tooltip></el-table-column>
        <el-table-column prop="coreProbeName" min-width="100" label="核心探针名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="probeDesignMethod" min-width="160" label="核心探针设计方" show-overflow-tooltip></el-table-column>
        <el-table-column prop="probeSize" min-width="100" label="探针大小" show-overflow-tooltip></el-table-column>
        <el-table-column prop="probeNum" min-width="100" label="探针条数" show-overflow-tooltip></el-table-column>
        <el-table-column prop="note" min-width="180" label="说明" show-overflow-tooltip></el-table-column>
        <el-table-column width="100" label="操作">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" @click="handleEdit(scope.row)"></el-button>
            <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row.fid)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="totalPage"
        style="background-color: #ffffff;width: 350px;"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </el-dialog>
    <edit-probe-dialog :pvisible.sync="editProbeDataInfo.visible" :probe-info="editProbeDataInfo.probeInfo" @dialogConfirmEvent="getData"></edit-probe-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import editProbeDialog from './editProbeDialog'

export default {
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {
    editProbeDialog
  },
  props: {
    name: {
      type: String
    }
  },
  data () {
    return {
      tableData: [],
      editProbeDataInfo: {
        cancerInfo: {},
        visible: false
      }
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    // 关联样本列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_core_probe_config_list',
        data: {
          page: this.currentPage,
          rows: this.pageSize
        },
        method: 'get'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.totalPage = result.data.total
          let data = result.data.records || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              fid: v.fid,
              cancerName: v.fcancerName,
              coreProbeName: v.fcoreProbeName,
              probeDesignMethod: v.fprobeDesignMethod,
              probeSize: v.fprobeSize,
              probeNum: v.fprobeNum,
              note: v.fnote
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 新增或修改癌种简称
    handleEdit (row) {
      console.log(row)
      this.editProbeDataInfo = {
        probeInfo: row,
        visible: true
      }
    },
    async handleDelete (id) {
      await this.$confirm(`是否确认删除当前数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.$ajax({
        url: '/system/probe/delete_core_probe',
        data: {
          fid: id
        },
        method: 'get'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped></style>
