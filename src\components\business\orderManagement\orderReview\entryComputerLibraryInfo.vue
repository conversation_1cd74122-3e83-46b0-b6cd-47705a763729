<template>
  <div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">检测信息</p>
        </div>
      </div>
      <div class="content">
        <el-form :model="detectInfo" :rules="rules" ref="detectInfoForm" label-width="120px" size="mini">
          <el-form-item label="检测类型" prop="detectType">
            <span v-if="!editMode">{{detectInfo.detectType}}</span>
            <el-select v-model="detectInfo.detectType" v-else class="form-width" placeholder="请选择">
              <el-option
                      :key="k"
                      :label="v"
                      :value="k"
                      v-for="(v, k) in detectTypeLists">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用类型" prop="applicationType">
            <span v-if="!editMode">{{detectInfo.applicationType}}</span>
            <el-radio-group v-model="detectInfo.applicationType" v-else>
              <el-radio :key="k" :label="k" v-for="(v, k) in applicationTypeLists">{{v}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="物种信息" prop="speciesInfo">
            <span v-if="!editMode">{{detectInfo.speciesInfo}}</span>
            <el-input v-model.trim="detectInfo.speciesInfo" v-else maxlength="50" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="文库类型" prop="libraryType">
            <span v-if="!editMode">{{detectInfo.libraryType}}</span>
            <el-radio-group v-model="detectInfo.libraryType" v-else @change="handleLibraryTypeChange">
              <el-radio :key="k" :label="k" v-for="(v, k) in libraryTypeLists">{{v}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item ref="libraryDetail" label="文库详情" prop="libraryDetail">
            <span v-if="!editMode">{{detectInfo.libraryDetail.join(' / ')}}</span>
            <el-cascader v-model="detectInfo.libraryDetail" :disabled="!detectInfo.libraryType" :options="libraryDetailLists[detectInfo.libraryType]" :show-all-levels="false" v-else></el-cascader>
          </el-form-item>
          <el-form-item ref="buildLibraryMethod" v-if="detectInfo.libraryType === 'illumina文库'" label="建库方式" prop="buildLibraryMethod">
            <span v-if="!editMode">{{detectInfo.buildLibraryMethod}}</span>
            <el-radio-group v-model="detectInfo.buildLibraryMethod" v-else @change="handleBuildLibraryMethodChange">
              <el-radio label="illumina kit建库">illumina kit建库<span style="color:#1890FF;">（提示：请提供建库试剂盒，并勾选具体类型接头序列）</span></el-radio>
              <el-radio label="非illumina kit建库">非illumina kit建库<span style="color:#1890FF;">（提示：请提供接头引物序列并标明index的位置，碱基个数及序列）</span></el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="detectInfo.buildLibraryMethod === 'illumina kit建库'">
            <el-form-item ref="linkerIndex" label="接头序列" prop="linkerIndex">
              <span v-if="!editMode">{{detectInfo.linkerIndex === '其他' ? detectInfo.linkerIndexOther : detectInfo.linkerIndex}}</span>
              <template v-else>
                <el-radio-group v-model="detectInfo.linkerIndex" @change="detectInfo.linkerIndexOther === ''">
                  <el-radio label="Multiplexing adaper">Multiplexing adaper</el-radio>
                  <el-radio label="PE adapter primer（不含index序列）">PE adapter primer（不含index序列）</el-radio>
                  <el-radio label="Nextera Index-Kit adaper prime">Nextera Index-Kit adaper prime</el-radio>
                  <el-radio label="其他">其他</el-radio>
                </el-radio-group>
                <el-input
                        v-model.trim="detectInfo.linkerIndexOther"
                        v-if="detectInfo.linkerIndex === '其他'"
                        placeholder="请输入其他接头序列"
                        maxlength="200" class="form-width"></el-input>
              </template>
            </el-form-item>
            <el-form-item ref="dataBox" label="建库试剂盒" prop="dataBox">
              <span v-if="!editMode">{{detectInfo.dataBox}}</span>
              <el-input v-model.trim="detectInfo.dataBox" v-else maxlength="100" class="form-width"></el-input>
            </el-form-item>
          </template>
          <template v-if="detectInfo.buildLibraryMethod === '非illumina kit建库'">
            <el-form-item ref="indexInfo" label="序列信息" prop="indexInfo">
              <span v-if="!editMode">{{detectInfo.indexInfo}}</span>
              <el-input v-model.trim="detectInfo.indexInfo" v-else maxlength="200" class="form-width"></el-input>
            </el-form-item>
            <el-form-item ref="nucleobaseNum" label="碱基数目" prop="nucleobaseNum">
              <span v-if="!editMode">{{detectInfo.nucleobaseNum}}</span>
              <el-input v-model.trim="detectInfo.nucleobaseNum" v-else maxlength="200" class="form-width"></el-input>
            </el-form-item>
          </template>
          <el-form-item label="样本保存介质" prop="sampleStorageMedium">
            <span v-if="!editMode">{{showOtherText(detectInfo.sampleStorageMedium, detectInfo.sampleStorageMediumOther)}}</span>
            <template v-else>
              <el-radio-group v-model="detectInfo.sampleStorageMedium" @change="detectInfo.sampleStorageMediumOther = ''">
                <el-radio label="纯水"></el-radio>
                <el-radio label="TE buffer"></el-radio>
                <el-radio label="RNase水（或DEPC水）"></el-radio>
                <el-radio label="乙醇"></el-radio>
                <el-radio label="干粉"></el-radio>
                <el-radio label="其他"></el-radio>
              </el-radio-group>
              <el-input
                v-model.trim="detectInfo.sampleStorageMediumOther"
                v-if="detectInfo.sampleStorageMedium === '其他'"
                placeholder="请输入其他样本保存介质"
                maxlength="200" class="form-width"></el-input>
            </template>
          </el-form-item>
          <el-form-item ref="indexType" v-if="detectInfo.libraryType" label="接头类型" prop="indexType">
            <span v-if="!editMode">{{showOtherText(detectInfo.indexType, detectInfo.indexTypeOther)}}</span>
            <template v-else>
              <el-radio-group v-model="detectInfo.indexType" @change="detectInfo.indexTypeOther === ''">
                <template v-if="detectInfo.libraryType === 'illumina文库'">
                  <!--<el-radio label="Illumina文库index"></el-radio>-->
                  <!--<el-radio label="illumina文库转化index"></el-radio>-->
                  <el-radio label="5'端磷酸化"></el-radio>
                  <el-radio label="5'端未磷酸化"></el-radio>
                  <el-radio label="其他">其他</el-radio>
                </template>
                <template v-if="detectInfo.libraryType === 'MGI文库'">
                  <el-radio label="华大官方单端Index(128)"></el-radio>
                  <el-radio label="华大官方单端Index(96)"></el-radio>
                  <el-radio label="华大官方双端Index"></el-radio>
                  <el-radio label="其他"></el-radio>
                </template>
              </el-radio-group>
              <el-input
                      v-model.trim="detectInfo.indexTypeOther"
                      v-if="detectInfo.indexType === '其他'"
                      placeholder="请输入其他接头序列"
                      maxlength="200" class="form-width"></el-input>
              <span style="color:#1890FF;">（提示：非华大官方 Index 必须要填写文库结构）</span>
            </template>
          </el-form-item>
          <el-form-item label="特殊序列" prop="specialIndex">
            <span v-if="!editMode">{{showOtherText(detectInfo.specialIndex, detectInfo.specialIndexOther, '有')}}</span>
            <template v-else>
              <el-radio-group v-model="detectInfo.specialIndex">
                <el-radio label="无"></el-radio>
                <el-radio label="有"></el-radio>
              </el-radio-group>
              <el-input
                      v-model.trim="detectInfo.specialIndexOther"
                      v-if="detectInfo.specialIndex === '有'"
                      placeholder="请输入"
                      maxlength="100" class="form-width"></el-input>
              <span style="color:#1890FF;">（提示：文库中含有特殊插入序列，如：UID、固定碱基等，请在输入框中说明）</span>
            </template>
          </el-form-item>
          <el-form-item v-if="detectInfo.indexType === '其他'"  label="文库结构" prop="libraryStructure">
            <div class="structure-container">
              <p class="nav-bar">
                <span>单Index文库</span>
                <template v-if="editMode">
                  <el-button type="text" @click="handleShowExample(1)">查看示例</el-button>
                  <el-button type="text" @click="handleShowUploadStructureDialog(1)">上传文库结构</el-button>
                  <el-button type="text" @click="handleShowExample(1, 'write')">录入文库结构</el-button>
                </template>
              </p>
              <div class="index-content">
                <div
                  v-if="detectInfo.libraryStructure.single.img.path"
                  class="row-item">
                  <img :src="detectInfo.libraryStructure.single.img.absolutePath" style="display: block;width: 100%;height: auto" alt="">
                  <el-button v-if="editMode" type="text" @click="handleDeleteLibraryStructure(1)">删除</el-button>
                </div>
                <div
                  v-else-if="detectInfo.libraryStructure.single.input && detectInfo.libraryStructure.single.input.length > 0"
                  class="row-item">
                  <div class="index">
                    <p>5'-</p>
                    <div class="main-index">
                      <p>{{detectInfo.libraryStructure.single.input[0]}}</p>
                      <p class="bg-yellow">index1<span class="index-num">{{detectInfo.libraryStructure.single.input[1]}}</span>bp</p>
                      <p>{{detectInfo.libraryStructure.single.input[2]}}</p>
                      <p class="bg-blue">indexDNA<span class="index-num">{{detectInfo.libraryStructure.single.input[3]}}</span>bp</p>
                      <p>{{detectInfo.libraryStructure.single.input[4]}}</p>
                    </div>
                    <p>-3’</p>
                  </div>
                  <el-button v-if="editMode" type="text" @click="handleDeleteLibraryStructure(1)">删除</el-button>
              </div>
              </div>
            </div>
            <div class="structure-container">
              <p class="nav-bar">
                <span>双Index文库</span>
                <template v-if="editMode">
                  <el-button type="text" @click="handleShowExample(2)">查看示例</el-button>
                  <el-button type="text" @click="handleShowUploadStructureDialog(2)">上传文库结构</el-button>
                  <el-button type="text" @click="handleShowExample(2, 'write')">录入文库结构</el-button>
                </template>
              </p>
              <div class="index-content">
                <div
                        v-if="detectInfo.libraryStructure.double.img.path"
                        class="row-item">
                  <img :src="detectInfo.libraryStructure.double.img.absolutePath" style="display: block;width: 100%;height: auto" alt="">
                  <el-button v-if="editMode" type="text" @click="handleDeleteLibraryStructure(2)">删除</el-button>
                </div>
                <div
                  v-else-if="detectInfo.libraryStructure.double.input && detectInfo.libraryStructure.double.input.length > 0"
                  class="row-item">
                  <div class="index">
                    <p>5'-</p>
                    <div class="main-index">
                      <p>{{detectInfo.libraryStructure.double.input[0]}}</p>
                      <p class="bg-yellow">index1<span class="index-num">{{detectInfo.libraryStructure.double.input[1]}}</span>bp</p>
                      <p>{{detectInfo.libraryStructure.double.input[2]}}</p>
                      <p class="bg-blue">indexDNA<span class="index-num">{{detectInfo.libraryStructure.double.input[3]}}</span>bp</p>
                      <p>{{detectInfo.libraryStructure.double.input[4]}}</p>
                      <p class="bg-yellow">index1<span class="index-num">{{detectInfo.libraryStructure.double.input[5]}}</span>bp</p>
                      <p>{{detectInfo.libraryStructure.double.input[6]}}</p>
                    </div>
                    <p>-3’111</p>
                  </div>
                  <el-button v-if="editMode" type="text" @click="handleDeleteLibraryStructure(2)">删除</el-button>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="是否已环化" prop="isCyclization">
            <span v-if="!editMode">{{detectInfo.isCyclization}}</span>
            <el-radio-group v-model="detectInfo.isCyclization" v-else>
              <el-radio label="是"></el-radio>
              <el-radio label="否"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="测序模式" prop="sequencingMode">
            <span v-if="!editMode">{{detectInfo.sequencingMode}}</span>
            <el-radio-group v-model="detectInfo.sequencingMode" v-else>
              <el-radio label="PE100"></el-radio>
              <el-radio label="PE150"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="仪器类型" prop="instrumentType">
            <span v-if="!editMode">{{detectInfo.instrumentType}}</span>
            <el-radio-group v-model="detectInfo.instrumentType" v-else>
              <el-radio label="DNBSEQ-T7RS"></el-radio>
              <el-radio label="Gene+Seq2000"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="交付数据类型" label-width="auto" prop="deliveryDataType">
            <span v-if="!editMode">{{showOtherText(detectInfo.deliveryDataType, detectInfo.deliveryDataTypeOther)}}</span>
            <template v-else>
              <el-radio-group v-model="detectInfo.deliveryDataType">
                <el-radio label="Fsatq数据"></el-radio>
                <el-radio label="其他"></el-radio>
              </el-radio-group>
              <el-input
                      v-model.trim="detectInfo.deliveryDataTypeOther"
                      v-if="detectInfo.deliveryDataType === '其他'"
                      placeholder="请输入"
                      maxlength="50" class="form-width"></el-input>
            </template>
          </el-form-item>
          <el-form-item label="拆分需求" prop="splitDemand">
            <span v-if="!editMode">{{detectInfo.splitDemand}}</span>
            <el-radio-group v-model="detectInfo.splitDemand" v-else>
              <el-radio label="拆分"></el-radio>
              <el-radio label="不拆分"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="交付方式" prop="deliveryMethod">
            <span v-if="!editMode">{{detectInfo.deliveryMethod}}</span>
            <el-radio-group v-model="detectInfo.deliveryMethod" v-else>
              <el-radio label="硬盘交付"></el-radio>
              <el-radio label="云交付"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="检测方式" prop="isAutoDetect">
            <span v-if="!editMode">{{detectInfo.isAutoDetect}}</span>
            <el-radio-group v-model="detectInfo.isAutoDetect" v-else>
              <el-radio label="质控合格，样本可默认开始检测"></el-radio>
              <el-radio label="下单确认后才能开始检测"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">文库信息（{{totalPage}}）</p>
        </div>
      </div>
      <div class="content">
        <el-table
          ref="table"
          :data="sampleInfoTable"
          max-height="800px"
          border
          default-expand-all
          class="computer-table"
          style="width: 100%">
          <el-table-column type="expand">
            <template slot-scope="scope">
              <el-table
                :data="scope.row.childLibrary"
                style="width: 100%;"
                max-height="300px">
                <el-table-column prop="subLibraryName" label="子文库名称" min-width="180" show-overflow-tooltip></el-table-column>
                <el-table-column prop="indexDigits" label="Index位数" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="indexNum" label="Index编号" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="index1Index" label="IndexF序列" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="index2Index" label="IndexR序列" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="subLibraryDateNum" label="子文库数据量（G）" width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="subLibraryNotes" label="子文库备注" min-width="180" show-overflow-tooltip></el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column prop="geneplusNum" label="吉因加编号" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryName" label="文库名称" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <template v-if="scope.row.libraryName">
                <template v-if="scope.row.sampleTag">
                  <el-tag :type="scope.row.sampleTag.type" size="mini">{{scope.row.sampleTag.name}}</el-tag>
                </template>
                <span>{{scope.row.libraryName}}</span>
              </template>
              <template v-else>
                <span>{{scope.row.libraryName}}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="species" label="物种" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fragmentSize" label="片段大小（bp）" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryDateNum" label="文库数据量（G）" width="130" show-overflow-tooltip></el-table-column>
          <el-table-column prop="baseBalance" label="碱基均衡" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingConcentration" label="送样浓度" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingVolume" label="送样体积" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryNotes" label="文库备注" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column label="子文库数" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span style="font-weight: 600;">{{scope.row.childLibrary.length}}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
                :page-sizes="pageSizes"
                :page-size="pageSize"
                :current-page.sync="currentPage"
                :total="totalPage"
                style="background: #ffffff;"
                layout="total, sizes, prev, pager, next, jumper, slot"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <!--<edit-library-info-dialog-->
      <!--:pvisible.sync="editLibraryInfoDialogVisible"-->
      <!--:pdata="editLibraryInfoDialogData"-->
      <!--@dialogConfirmEvent="handleEditLibraryInfoDialogConfirm"/>-->
    <!--<import-library-info-dialog-->
      <!--:pvisible.sync="importLibraryInfoDialogVisible"-->
      <!--:type="1"-->
      <!--@dialogConfirmEvent="handleImportLibraryInfoDialogConfirm"/>-->
    <library-info-library-structure-example-file-dialog
      :pvisible.sync="libraryInfoLibraryStructureExampleFileDialogVisible"
      :type="libraryInfoLibraryStructureExampleFileDialogData.type"
      :pdata="libraryInfoLibraryStructureExampleFileDialogData.pdata"
      :mode="libraryInfoLibraryStructureExampleFileDialogData.mode"
      @dialogConfirmEvent="handleEditLibraryStructureConfirm"/>
    <upload-structure-img-dialog
      :pvisible.sync="uploadStructureImgDialogVisible"
      @dialogConfirmEvent="handleUploadStructureImgDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../../util/util'
import mixins from '../../../../util/mixins'
import editLibraryInfoDialog from './entryComputerLibraryInfoEditLibraryInfoDialog'
import importLibraryInfoDialog from './importLibraryInfoDialog'
import libraryInfoLibraryStructureExampleFileDialog from './entryComputerLibraryInfoLibraryStructureExampleFileDialog'
import uploadStructureImgDialog from './entryComputerLibraryInfoUploadStructureImgDialog'
export default {
  name: 'entryComputerLibraryInfo',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    editLibraryInfoDialog,
    importLibraryInfoDialog,
    libraryInfoLibraryStructureExampleFileDialog,
    uploadStructureImgDialog
  },
  props: {
    readOnly: Boolean,
    orderId: String | Number,
    pageType: String | Number,
    editMode: Boolean, // 编辑模式
    onlineForm: Object // 线上的数据，之前前端填好了的
  },
  mounted () {
    this.getDetectType()
    if (this.orderId) {
      this.getData()
    }
  },
  watch: {
    onlineForm: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.detectInfo = {...newVal}
        }
      },
      deep: true
    }
  },
  computed: {
    sampleInfoTableComputed () {
      let tableData = []
      this.sampleInfoTable.forEach((v, i) => {
        let item = {}
        if (v.childLibrary && v.childLibrary.length > 0) {
          v.childLibrary.forEach((vv, ii) => {
            item = {...v, ...vv, rowIndex: i + ';' + ii}
            tableData.push(item)
          })
        } else {
          item = {...v}
          tableData.push(item)
        }
      })
      return tableData
    },
    // 表格合并的时候那些开头的index
    childLibraryStartIndex () {
      let childNum = []
      this.sampleInfoTable.forEach(v => {
        if (v.childLibrary && v.childLibrary.length > 0) {
          childNum.push(v.childLibrary.length)
        }
      })
      console.log(childNum)
      let startIndex = [0]
      if (childNum.length > 0) {
        childNum.reduce((prev, cur, index) => {
          startIndex.push(index === 0 ? 0 : prev)
          return prev + cur
        })
      }
      return startIndex
    },
    sampleStorageMediumText () {
      let index = this.detectInfo.sampleStorageMedium.indexOf('其他')
      let r = util.deepCopy(this.detectInfo.sampleStorageMedium)
      if (index > -1) {
        r[index] = this.detectInfo.sampleStorageMediumOther
      }
      return r.join('，')
    }
  },
  data () {
    const self = this
    const linkerIndexValid = function (rule, value, callback) {
      if (value === '其他' && !self.detectInfo.linkerIndexOther) {
        callback(new Error('请输入其他接头序列'))
        return
      }
      callback()
    }
    const indexTypeVaild = function (rule, value, callback) {
      if (value === '其他' && !self.detectInfo.indexTypeOther) {
        callback(new Error('请输入接头类型'))
        return
      }
      callback()
    }
    const specialIndexVaild = function (rule, value, callback) {
      if (value === '有' && !self.detectInfo.specialIndexOther) {
        callback(new Error('请输入特殊序列说明'))
        return
      }
      callback()
    }
    const deliveryDataTypeVaild = function (rule, value, callback) {
      if (value === '其他' && !self.detectInfo.deliveryDataTypeOther) {
        callback(new Error('请输入其他交付数据类型'))
        return
      }
      callback()
    }
    return {
      realComponentTableDataName: 'sampleInfoTable',
      detectInfo: {
        detectType: '', // 检测类型
        applicationType: '', // 应用类型
        speciesInfo: '', // 物种信息
        libraryType: '',
        libraryDetail: [],
        buildLibraryMethod: '', // 建库方式
        linkerIndex: '', // 接头序列
        linkerIndexOther: '', // 接口序列其他
        dataBox: '', // 建库数据盒
        indexInfo: '', // 序列信息
        nucleobaseNum: '', // 碱基数目
        sampleStorageMedium: [], // 样本保存介质
        sampleStorageMediumOther: '', // 其他样本保存介质
        indexType: '', // 接头类型
        indexTypeOther: '', // 其他接头类型
        specialIndex: '', // 特殊序列
        specialIndexOther: '', // 特殊序列补充说明
        libraryStructure: { // 文库结构
          single: {
            img: {}, // 上传的图片地址
            input: [] // 输入的文案
          },
          double: {
            img: {}, // 上传的图片地址
            input: [] // 输入的文案
          }
        },
        isCyclization: '', // 是否已经环化
        sequencingMode: '', // 测序模式
        instrumentType: '', // 仪器类型
        deliveryDataType: '', // 交互数据类型
        deliveryDataTypeOther: '', // 交互数据类型其他
        splitDemand: '', // 拆分需求
        deliveryMethod: '', // 交付方式
        isAutoDetect: '' // 是否允许根据质控结果自动开启检测
      },
      libraryInfoLibraryStructureExampleFileDialogVisible: false,
      libraryInfoLibraryStructureExampleFileDialogData: {
        type: 1, // 1：单Index文库  2：双Index文库
        mode: 'read', // write read
        pdata: []
      },
      uploadStructureImgDialogVisible: false,
      uploadStructureImgType: 1,
      detectTypeLists: {},
      applicationTypeLists: {
        '客户自建文库包芯上机': '客户自建文库包芯上机',
        '客户自建文库散样上机': '客户自建文库散样上机'
      },
      libraryTypeLists: {
        'illumina文库': 'illumina文库',
        'MGI文库': 'MGI文库'
      },
      libraryDetailLists: {
        'illumina文库': [
          {
            value: 'DNA文库',
            label: 'DNA文库',
            children: [
              {value: '全基因组文库-WGS', label: '全基因组文库-WGS'},
              {value: '全外显子文库-WES', label: '全外显子文库-WES'},
              {value: 'PCR-free文库', label: 'PCR-free文库'},
              {value: '目标区域捕获文库', label: '目标区域捕获文库'},
              {value: '宏基因组文库', label: '宏基因组文库'},
              {value: 'TAPS文库', label: 'TAPS文库'},
              {value: 'TCP文库', label: 'TCP文库'},
              {value: '甲基化文库', label: '甲基化文库'},
              {value: 'Hi-C文库', label: 'Hi-C文库'}
            ]
          },
          {
            value: 'RNA文库',
            label: 'RNA文库',
            children: [
              {value: '普通转录组文库', label: '普通转录组文库'},
              {value: 'mRNA链特异性文库', label: 'mRNA链特异性文库'},
              {value: 'Lnc链特异性文库', label: 'Lnc链特异性文库'},
              {value: '10x_3’单细胞文库', label: '10x_3’单细胞文库'},
              {value: '10x_5’单细胞文库', label: '10x_5’单细胞文库'},
              {value: '10x_VDJ_TCR文库', label: '10x_VDJ_TCR文库'}
            ]
          }
        ],
        'MGI文库': [
          {
            value: 'DNA文库',
            label: 'DNA文库',
            children: [
              {value: '全基因组文库-WGS', label: '全基因组文库-WGS'},
              {value: '全外显子文库-WES', label: '全外显子文库-WES'},
              {value: 'PCR-free文库', label: 'PCR-free文库'},
              {value: '目标区域捕获文库', label: '目标区域捕获文库'},
              {value: '宏基因组文库', label: '宏基因组文库'},
              {value: 'TAPS文库', label: 'TAPS文库'},
              {value: 'TCP文库', label: 'TCP文库'}
            ]
          },
          {
            value: 'RNA文库',
            label: 'RNA文库',
            children: [
              {value: '普通转录组文库', label: '普通转录组文库'},
              {value: 'mRNA链特异性文库', label: 'mRNA链特异性文库'},
              {value: 'Lnc链特异性文库', label: 'Lnc链特异性文库'}
            ]
          }
        ]
      },
      sampleInfoTable: [],
      editLibraryInfoDialogData: null, // 编辑数据，新建时传null,编辑时传正常数据
      editLibraryInfoDialogVisible: false,
      importLibraryInfoDialogVisible: false,
      rules: {
        detectType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 检测类型
        applicationType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 应用类型
        speciesInfo: [
          {required: true, message: '请输入', trigger: 'blur'}
        ], // 物种信息
        libraryType: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        libraryDetail: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        buildLibraryMethod: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 建库方式
        linkerIndex: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: linkerIndexValid, trigger: 'change'}
        ], // 接头序列
        dataBox: [
          {required: true, message: '请输入', trigger: 'blur'}
        ], // 建库数据盒
        indexInfo: [
          {required: true, message: '请输入', trigger: 'blur'}
        ], // 序列信息
        nucleobaseNum: [
          {required: true, message: '请输入', trigger: 'blur'}
        ], // 碱基数目
        indexType: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: indexTypeVaild, trigger: 'change'}
        ], // 接头类型
        specialIndex: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: specialIndexVaild, trigger: 'change'}
        ], // 特殊序列
        isCyclization: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 是否已经环化
        sequencingMode: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 测序模式
        instrumentType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 仪器类型
        deliveryDataType: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: deliveryDataTypeVaild, trigger: 'change'}
        ], // 交互数据类型
        deliveryDataTypeOther: '', // 交互数据类型其他
        splitDemand: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 拆分需求
        deliveryMethod: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 交付方式
        isAutoDetect: [
          {required: true, message: '请选择', trigger: 'change'}
        ] // 是否允许根据质控结果自动开启检测
      }
    }
  },
  methods: {
    // 合并行
    tableSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex <= 7) {
        if (row.childLibrary.length) {
          let index = this.childLibraryStartIndex.indexOf(rowIndex)
          if (index > -1) {
            return {
              rowspan: row.childLibrary.length,
              colspan: 1
            }
          } else {
            return [0, 0]
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 展示其他文案，当一个文案为 '其他'时，展示另外一个字段，用于只读模式
    showOtherText (firstFiled, otherFiled, keyWord = '其他') {
      let text = ''
      text = firstFiled === keyWord ? otherFiled : firstFiled
      return text !== 0 && !text ? '-' : text
    },
    handleAdd (row, index) {
      this.editLibraryInfoDialogData = row ? {index, form: {...row}} : null
      this.editLibraryInfoDialogVisible = true
    },
    // 获取文库样本表格数据
    getData () {
      let data = {
        orderId: this.orderId,
        type: this.pageType
      }
      data.pageVO = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$ajax({
        url: '/order/get_lib_or_tissue_list',
        data: data,
        loadingDom: '.computer-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.sampleInfoTable = []
          rows.forEach((v, i) => {
            let item = {
              geneplusNum: v.geneCode,
              libraryName: v.name,
              species: v.species,
              fragmentSize: v.fragment,
              libraryDateNum: v.dataSize,
              baseBalance: v.baseBalance,
              samplingConcentration: v.concentration,
              samplingVolume: v.volume,
              libraryNotes: v.note,
              childLibrary: []
            }
            if (v.subLib && v.subLib.length > 0) {
              v.subLib.forEach((vv) => {
                let child = {
                  subLibraryName: vv.subName,
                  indexDigits: vv.indexNum,
                  indexNum: vv.indexCode,
                  index1Index: vv.index1,
                  index2Index: vv.index2,
                  subLibraryDateNum: vv.subDataSize,
                  subLibraryNotes: vv.subNote
                }
                item.childLibrary.push(child)
              })
            }
            if (!data.pageVO) {
              item.libraryFrontId = i // 前端Id,只有在编辑时有
            }
            this.sampleInfoTable.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取检测类型
    getDetectType () {
      this.$ajax({
        url: '/order/get_detect_type_list',
        method: 'get',
        data: {
          type: 2
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          console.log(res.data)
          this.detectTypeLists = {}
          res.data.forEach(v => {
            this.detectTypeLists[v] = v
          })
        } else {
          this.$message.error('获取检测类型失败，错误：' + res.message)
        }
      })
    },
    handleEditLibraryInfoDialogConfirm (data) {
      if (!data.index && data.index !== 0) {
        delete data.index
        this.libraryInfoTable.push(data.form)
      } else {
        this.$set(this.libraryInfoTable, data.index, data.form)
        // this.libraryInfoTable[data.index] = data.form
      }
    },
    // 查看文库结构示例文件
    handleShowExample (index, mode = 'read') {
      let pdata = []
      console.log(111)
      if (mode === 'write') {
        pdata = index === 1 ? this.detectInfo.libraryStructure.single.input : this.detectInfo.libraryStructure.double.input
        console.log(pdata)
      }
      this.libraryInfoLibraryStructureExampleFileDialogData = {
        type: index,
        mode: mode,
        pdata: pdata
      }
      this.libraryInfoLibraryStructureExampleFileDialogVisible = true
    },
    // 删除文库结构
    handleDeleteLibraryStructure (type) {
      console.log(343343)
      if (type === 1) {
        this.$set(this.detectInfo.libraryStructure, 'single', {img: '', input: ''})
      }
      if (type === 2) {
        this.$set(this.detectInfo.libraryStructure, 'double', {img: '', input: ''})
      }
    },
    // 展示上传结构
    handleShowUploadStructureDialog (type) {
      this.uploadStructureImgType = type
      this.uploadStructureImgDialogVisible = true
    },
    // 上传实例文件确定 data = {path, group, absolutePath}
    handleUploadStructureImgDialogConfirm (data) {
      if (data.path) {
        let key = this.uploadStructureImgType === 1 ? 'single' : 'double'
        this.$set(this.detectInfo.libraryStructure, key, {img: {...data}, input: []})
      }
    },
    handleImportLibraryInfoDialogConfirm (res) {},
    handleEditLibraryStructureConfirm ({type, input}) {
      if (type === 1) {
        this.$set(this.detectInfo.libraryStructure.single, 'input', input)
      }
      if (type === 2) {
        this.$set(this.detectInfo.libraryStructure.double, 'input', input)
      }
    },
    handleDelete (index) {
      this.libraryInfoTable.splice(index, 1)
    },
    // 文库类型变化
    handleLibraryTypeChange (val) {
      console.log(val)
      this.clearValidate(['libraryDetail', 'indexType', 'buildLibraryMethod', 'linkerIndex', 'dataBox', 'indexInfo', 'nucleobaseNum'])
      this.$nextTick(() => {
        this.detectInfo.libraryDetail = []
        this.detectInfo.indexType = ''
        this.detectInfo.indexTypeOther = ''
        this.detectInfo.buildLibraryMethod = ''
        this.detectInfo.linkerIndex = ''
        this.detectInfo.linkerIndexOther = ''
        this.detectInfo.dataBox = ''
        this.detectInfo.indexInfo = ''
        this.detectInfo.nucleobaseNum = ''
      })
    },
    // 建库方式变化
    handleBuildLibraryMethodChange () {
      this.clearValidate(['linkerIndex', 'dataBox', 'indexInfo', 'nucleobaseNum'])
      this.$nextTick(() => {
        this.detectInfo.linkerIndex = ''
        this.detectInfo.linkerIndexOther = ''
        this.detectInfo.dataBox = ''
        this.detectInfo.indexInfo = ''
        this.detectInfo.nucleobaseNum = ''
      })
    },
    // 清除部分表单的验证
    clearValidate (field = []) {
      field.forEach(v => {
        if (this.$refs[v]) {
          this.$refs[v].clearValidate()
        }
      })
    },
    validForm () {
      return new Promise((resolve, reject) => {
        this.$refs.detectInfoForm.validate(valid => {
          let msg = '验证不通过'
          valid ? resolve() : reject(msg)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .title{
    margin-right: 30px;
    font-size: 20px;
    font-weight: 600;
  }
  .form-width{
    width: 200px;
  }
  .module{
    background: #fff;
    margin: 20px 0;
    .module-title-bar{
      @extend .operateBar;
      height: 50px;
      .min-title{
        @extend .title;
        font-size: 16px;
      }
    }
    .content{
      padding: 10px 20px;
    }
  }
  // 文库结构的样式
  .structure-container{
    padding: 10px;
    font-size: 13px;
    .bg-gray{
      background: #D7D7D7;
    }
    .bg-yellow{
      background: #f59a23;
    }
    .bg-blue{
      background: #1890ff;
    }
    .nav-bar > span{
      margin-right: 20px;
    }
    .index-content{
      margin-top: 10px;
      .row-item{
        display: flex;
        .index{
          display: flex;
          color: #333;
          max-width: 100%;
          overflow-x: auto;
          margin-right: 10px;
          .main-index{
            display: flex;
            margin: 0 10px;
            @extend .bg-gray;
            p{
              padding: 0 8px;
              .index-num{
                padding: 0 3px;
                color: red;
                border-bottom: 1px solid #333;
              }
            }
          }
        }
      }
    }
  }
</style>
