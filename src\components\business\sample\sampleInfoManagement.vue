<template>
  <div>
    <div style="margin: 20px 0 0 0;border-bottom: 1px solid #ccc;">
      <el-form :model="form" size="mini" label-width="120px" inline>
        <div>
          <el-form-item label="样本编号">
            <el-input v-model="form.sampleCode" class="form-content" clearable></el-input>
          </el-form-item>
          <el-form-item label="临床科研">
            <el-select v-model="form.clinicalResearch" size="mini" class="form-content" clearable>
              <el-option label="科研" value="0"></el-option>
              <el-option label="临床" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="信息补录">
            <el-cascader
              v-model="form.infoSupplement"
              :options="infoSupplementOptions"
              :props="{ multiple: true }"
              size="mini"
              class="form-content"
              collapse-tags
              clearable></el-cascader>
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="form.name" class="form-content" clearable></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="样例编号(精确)">
            <el-input v-model="form.sampleCodeAccurate" size="mini"  class="form-content"></el-input>
          </el-form-item>
          <el-form-item label="审核时间">
            <el-date-picker
              v-model="form.verifyDate[0]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
            <span>--</span>
            <el-date-picker
              v-model="form.verifyDate[1]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="到样时间">
            <el-date-picker
              v-model="form.SampleDate[0]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
            <span>--</span>
            <el-date-picker
              v-model="form.SampleDate[1]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="原始编号">
            <el-input v-model="form.originNum" size="mini" class="form-content"></el-input>
          </el-form-item>
          <el-form-item label="身份证/护照">
            <el-input v-model="form.idCard" size="mini" class="form-content"></el-input>
          </el-form-item>
          <el-form-item label="产品/项目名称">
            <el-input v-model="form.productName" size="mini" class="form-content"></el-input>
          </el-form-item>
          <el-form-item label="送检单位">
            <el-input v-model="form.unit" size="mini" class="form-content"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="buttonGroup">
      <el-button type="text" size="mini" @click="handleShowFastEntryDialog">快速录入</el-button>
      <el-button type="text" size="mini" >补录信息</el-button>
      <el-button type="text" size="mini" >信息审核</el-button>
      <el-button type="text" size="mini" >存疑修改</el-button>
      <el-button type="text" size="mini" >查看明细</el-button>
      <el-button type="text" size="mini" >合并</el-button>
      <el-button type="text" size="mini" >导出</el-button>
    </div>
    <div>
      <el-table
        ref="table"
        :data="tableData"
        :height="tableHeight"
        class="reservationTable"
        style="width: 100%"
        @select="handleSelectTable"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45"></el-table-column>
        <el-table-column prop="patientCode" label="患者编号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleCode" label="样本编号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="infoSupplement" label="信息补录" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="idCard" label="身份证/护照" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" width="220" show-overflow-tooltip></el-table-column>
        <el-table-column prop="unit" label="送检单位" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="clinicalResearch" label="临床科研" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="产品/项目名称" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="area" label="生产片区" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originNum" label="原始编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleDate" label="到样时间" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="rejectNotes" label="驳回备注" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="verifyDate" label="审核时间" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reviewer" label="审核人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="entryPerson" label="录入人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="entryDate" label="录入时间" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="makeUpPerson" label="补录人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="makeUpDate" label="补录时间" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suspiciousPerson" label="存疑人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suspiciousNote" label="存疑备注" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modifier" label="修改者" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modifyDate" label="修改时间" width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
              :page-sizes="pageSizes"
              :page-size="pageSize"
              :current-page.sync="currentPage"
              :total="totalPage"
              layout="total, sizes, prev, pager, next, jumper, slot"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </div>
    <fast-entry-dialog :pvisible.sync="fastEntryDialogVisible" />
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import fastEntryDialog from './sampleInfoManagementFastEntryInfoDialog'
export default {
  name: 'sampleInfoManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    fastEntryDialog
  },
  beforeMount () {
    this.$_setTbHeight()
    window.addEventListener('resize', this.$_setTbHeight)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.$_setTbHeight)
    })
  },
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      fastEntryDialogVisible: false,
      tableHeight: 0,
      form: {
        sampleCode: '',
        sampleCodeAccurate: '', // 样本编号（精确）
        verifyDate: ['', ''],
        SampleDate: ['', ''],
        clinicalResearch: '', // 临床科研
        detectLink: '', // 检测环节
        infoSupplement: [], // 信息补录
        name: '',
        originNum: '', // 原始编号
        idCard: '',
        productName: '',
        unit: ''
      },
      formSubmit: {},
      infoSupplementOptions: [
        {
          label: '全选',
          value: '0',
          children: [
            {value: 1, label: '前端未补录'},
            {value: 2, label: '未补录'},
            {value: 3, label: '补录中'},
            {value: 4, label: '驳回'},
            {value: 5, label: '待审'},
            {value: 6, label: '审核通过'}
          ]
        }
      ]
    }
  },
  methods: {
    $_setTbHeight () {
      let h1 = document.documentElement.clientHeight - 1
      this.tableHeight = h1 - 64 - 50 - 20 - 20 - 51 * 3 - 45 - 42 - 20
    },
    getData () {
      this.$ajax({
        url: '/sample/basic/get_sample_basic_list',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {}
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleBasicId,
              sampleCode: v.sampleNum,
              patientCode: v.patientID,
              infoSupplement: v.makeupStatus,
              idCard: v.idcard,
              name: v.name,
              unit: v.inspectionUnit,
              clinicalResearch: '',
              productName: v.proName,
              area: '',
              originNum: v.originNum,
              sampleDate: '',
              rejectNotes: v.rejectRemark,
              verifyDate: v.auditTime,
              reviewer: v.auditor,
              entryPerson: v.creator,
              entryDate: v.createTime,
              makeUpPerson: v.makeuper,
              makeUpDate: v.makeupTime,
              modifier: v.modifier,
              modifyDate: v.updateTime
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSearch () {
      this.formSubmit = {...this.form}
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        sampleCode: '',
        productName: '',
        uploadResultDate: ['', ''],
        offlineDetectStatus: '',
        detectLink: ''
      }
      this.handleSearch()
    },
    // 点击行
    handleRowClick (row) {
      // if (!this.selectedRows.has(row.patientCode)) {
      //   this.$refs.table.clearSelection()
      //   this.selectedRows.clear()
      // }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id)
        ? this.selectedRows.delete(row.id)
        : this.selectedRows.set(row.id, row)
    },
    // 快速录入
    handleShowFastEntryDialog () {
      this.fastEntryDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
  .form-content{
    width: 150px;
  }
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }
</style>
