<template>
  <div class="page">
    <div class="buttonGroup">
      <el-button type="primary" size="mini" @click="handleOnlineReport">生成解读结果</el-button>
    </div>
    <div class="content">
      <div>
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%;" @tab-click="getData">
          <el-tab-pane label="数据筛选-D" name="reportData"></el-tab-pane>
          <el-tab-pane label="胚系变异" name="ht"></el-tab-pane>
          <el-tab-pane label="疾病背景" name="sick"></el-tab-pane>
          <el-tab-pane label="患癌风险" name="riskOfIllness"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="width: calc(100% - 40px - 140px);">
        <div class="buttonGroup" style="background-color: #ffffff;">
          <template v-if="activeName === 'reportData'">
            <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
            <!--<el-button type="primary" size="mini" @click="handleEdit">修改基因药物解析</el-button>-->
            <!--<el-button type="primary" size="mini" @click="handleCopyAndAdd">复制新增</el-button>-->
            <!--<el-button type="primary" size="mini" @click="handleBulkCopy">批量复制</el-button>-->
            <el-button-group style="margin: 0 10px;">
              <el-button size="mini" @click="handleChange(1)">上移</el-button>
              <el-button size="mini" @click="handleChange(0)">下移</el-button>
              <el-button size="mini" @click="handleDelete">删除</el-button>
              <el-button size="mini" @click="handleReturnData">撤回</el-button>
            </el-button-group>
          </template>
          <template v-else>
            <el-button type="primary" size="mini" @click="handleEditHData">修改</el-button>
            <template v-if="activeName === 'sick'">
              <el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
            </template>
          </template>
        </div>
        <div style="height: calc(100% - 50px)">
          <el-table
            :data="tableData"
            :height="'calc(100%)'"
            ref="table"
            class="dataFilterTable"
            style="width: 100%;"
            @select="handleSelect"
            @select-all="handleSelectAll"
            @row-click="handleRowClick">
            <el-table-column type="selection" width="45"></el-table-column>
            <template v-if="activeName === 'reportData'">
              <el-table-column key="reportData-callOutTime" prop="callOutTime" label="报出情况" width="120"></el-table-column>
              <el-table-column key="reportData-gene" prop="gene" label="基因"  width="140" show-overflow-tooltip></el-table-column>
              <el-table-column key="reportData-nucleotideMutation" prop="nucleotideMutation" label="碱基改变"  width="140"></el-table-column>
              <el-table-column key="reportData-aminoAcidMutation" prop="aminoAcidMutation" label="氨基酸改变"  width="140"></el-table-column>
              <el-table-column key="reportData-mutationRate" prop="mutationRate" label="突变频率"  width="100"></el-table-column>
              <el-table-column key="reportData-drugName" prop="drugName" label="药物名称"  width="180" show-overflow-tooltip></el-table-column>
              <el-table-column key="reportData-drugStatus" prop="drugStatus" label="药物状态"  width="300" show-overflow-tooltip></el-table-column>
              <el-table-column key="reportData-curativePertinence" prop="curativePertinence" label="疗效相关性"  width="160"></el-table-column>
              <el-table-column key="reportData-evidenceLevel" prop="evidenceLevel" label="证据等级" width="100"></el-table-column>
              <el-table-column key="reportData-cancerClass" prop="cancerClass" label="癌种类型"  width="120"></el-table-column>
              <el-table-column key="reportData-recordNo" prop="recordNo" label="记录编码"  width="200"></el-table-column>
              <el-table-column key="reportData-updateTime" prop="updateTime" label="最后修改时间" width="180"></el-table-column>
              <el-table-column key="reportData-nucl" prop="nucl" label="碱基（库内）" width="120"></el-table-column>
              <el-table-column key="reportData-amino" prop="amino" label="氨基酸（库内）" width="130"></el-table-column>
            </template>
            <template v-else-if="activeName === 'ht'">
              <el-table-column key="ht-fgene" prop="fgene" label="基因" width="120"></el-table-column>
              <el-table-column key="ht-nucleotideMutation" prop="nucleotideMutation" label="核苷酸改变" width="180"></el-table-column>
              <el-table-column key="ht-aminoAcidMutation" prop="aminoAcidMutation" label="氨基酸改变" width="180"></el-table-column>
              <el-table-column key="ht-mutationType" prop="mutationType" label="变异意义" width="120"></el-table-column>
              <el-table-column key="ht-relatedDisease" prop="relatedDisease" label="遗传性肿瘤综合征" width="180"></el-table-column>
              <el-table-column key="ht-geneticMode" prop="geneticMode" label="遗传方式" width="100"></el-table-column>
              <el-table-column key="ht-ffr1" prop="ffr1" label="Fr.1*" width="140"></el-table-column>
              <el-table-column key="ht-ffr2" prop="ffr2" label="Fr.2*" width="140"></el-table-column>
              <el-table-column key="ht-ffr3" prop="ffr3" label="Fr.3*" width="140"></el-table-column>
              <el-table-column key="ht-referenceSeq" prop="referenceSeq" label="参考序列" width="140"></el-table-column>
              <el-table-column key="ht-fexinId" prop="fexinId" label="功能区域"  width="180"></el-table-column>
              <el-table-column key="ht-fzygosity" prop="fzygosity" label="纯和/杂合" width="180"></el-table-column>
              <el-table-column key="ht-ffunction" prop="ffunction" label="Function" width="180"></el-table-column>
              <el-table-column key="ht-frsId" prop="frsId" label="rs号" width="180"></el-table-column>
              <el-table-column key="ht-fgeneticRate" prop="fgeneticRate" label="遗传机率" width="100"></el-table-column>
              <el-table-column key="ht-geneMutationAnalysis" prop="geneMutationAnalysis" label="基因变异解析" min-width="200" show-overflow-tooltip></el-table-column>
              <el-table-column key="ht-mutationDes" prop="mutationDes" label="变异概述" min-width="200" show-overflow-tooltip></el-table-column>
            </template>
            <template v-else-if="activeName === 'sick'">
              <el-table-column key="sick-gene" prop="gene" label="基因" width="120" show-overflow-tooltip></el-table-column>
              <el-table-column key="sick-cancerClass" prop="cancerClass" label="匹配癌种" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column key="sick-gender" prop="gender" label="性别" width="80"></el-table-column>
              <el-table-column key="sick-relatedDisease" prop="relatedDisease" label="遗传性肿瘤综合征" width="300"></el-table-column>
              <el-table-column key="sick-geneticMode" prop="geneticMode" label="遗传方式" width="100"></el-table-column>
              <el-table-column key="sick-geneBackground" prop="geneBackground" label="疾病背景" min-width="200" show-overflow-tooltip></el-table-column>
            </template>
            <template v-else>
              <el-table-column key="riskOfIllness-gene" prop="gene" label="基因" width="120"></el-table-column>
              <el-table-column key="riskOfIllness-tumorTypes" prop="tumorTypes" label="癌症类型" width="180"></el-table-column>
              <el-table-column key="riskOfIllness-cancerRisk" prop="cancerRisk" label="普通人患癌风险" min-width="180"></el-table-column>
              <el-table-column key="riskOfIllness-cancerRisk1" label="突变携带者患癌风险1" min-width="180">
                <template slot-scope="scope">
                  <span v-html="scope.row.mutantCancerRisk"></span>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import util from '../../../../util/util'
import obj from '../../../../util/mixins'
export default {
  name: 'wesReportDataFilter',
  mixins: [obj.tablePaginationCommonData],
  components: {},
  props: [],
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      activeName: 'reportData',
      selectedRows: []
    }
  },
  methods: {
    getData (tab, event) {
      // console.log(tab)
      // if (this.activeName !== tab.name) {
      //   this.activeName = tab.name
      // }
      let url = ''
      switch (this.activeName) {
        case 'reportData':
          url = '/read/wesUnscramble/get_report_data_list'
          break
        case 'ht':
          url = '/read/wesUnscramble/get_report_h_ht_list'
          break
        case 'sick':
          url = '/read/wesUnscramble/get_report_h_sick_list'
          break
        case 'riskOfIllness':
          url = '/read/wesUnscramble/get_report_h_risk_of_illness_list'
          break
      }
      this.$ajax({
        loadingDom: '.dataFilterTable',
        method: 'get',
        url: url,
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRows = []
          let data = result.data
          this.totalPage = data.total
          this.tableData = []
          data.rows.forEach(v => {
            // v.realData = JSON.parse(JSON.stringify(v))
            util.setDefaultEmptyValueForObject(v)
            this.tableData.push(v)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSelectAll (selection) {
      this.selectedRows = []
      selection.forEach((v, i) => {
        this.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = this.selectedRows.findIndex(v => v.fid === row.fid)
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs.table.toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs.table.toggleRowSelection(row, true)
      }
    },
    handleChange (type) {
      if (this.selectedRows.length === 0) {
        this.$message.error('请选择数据')
      } else if (this.selectedRows.length !== 1) {
        this.$message.error('请选择一条数据')
      } else {
        let row = JSON.parse(JSON.stringify(this.selectedRows[0]))
        if (type) {
          // 上移
          let index = this.tableData.findIndex(v => v.fid === row.fid)
          if (index !== 0) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index - 1, 0, row)
            this.$refs.table.toggleRowSelection(row, true)
          }
        } else {
          // 下移
          let index = this.tableData.findIndex(v => v.fid === row.fid)
          if (index !== this.tableData.length - 1) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index + 1, 0, row)
            this.$refs.table.toggleRowSelection(row, true)
          }
        }
      }
    },
    handleDelete () {
      let rows = this.selectedRows
      if (rows.length === 0) {
        this.$message.error('请选择数据')
      } else {
        let drugReadIds = rows.map(v => {
          return v.fid
        })
        this.$ajax({
          url: '/read/unscramble/interpretation_data_screening_delete',
          data: {
            analysisRsId: this.analysisRsId,
            drugReadIds: drugReadIds.join(',')
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getData()
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleOnlineReport () {
      this.$ajax({
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        url: '/read/wesUnscramble/create_online_result',
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('在线报告生成成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleEdit () {
      util.openNewPage('/business/unscramble/addForecastData')
    },
    handleReturnData () {},
    handleEditHData () {},
    handleSave () {
      let drugReadIds = this.tableData.map(v => { return v.fid })
      this.$ajax({
        loadingDom: '.dataFilterTable',
        url: '/read/unscramble/save_weights',
        data: {
          analysisRsId: this.analysisRsId,
          drugReadIds: drugReadIds.join(',')
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleCopyAndAdd () {},
    handleBulkCopy () {}
  }
}
</script>

<style scoped lang="scss">
  .page{
    height: 100%;
    .buttonGroup{
      background-color: #f2f2f2;
      padding: 0 20px;
      height: 50px;
      line-height: 50px;
    }
    .content{
      height: calc(100% - 50px);
      display: flex;
      >>>.el-tabs__item{
        width: 140px;
        text-align: center;
      }
    }
  }
</style>
