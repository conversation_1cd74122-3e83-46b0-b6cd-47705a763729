// 导入工具方法，用于设置对象的默认空值
import {setDefaultEmptyValueForObject, deepCopy} from '../../../../util/util'

/**
 * 定义数据状态常量，用于表示数据的状态
 * @enum {number}
 */
export const dataStatus = {
  // 未启动状态的标识
  0: '未启动',
  // 已启动状态的标识
  1: '已启动',
  // 停止检测状态的标识
  2: '停止检测'
}

export const deliverStatusConfig = {
  // 未交付：样本还在生产中，实验环节未流转至交付；
  1: '未交付',
  // 已交付：样本已流转至交付环节且交付完成时间不为空，系统自动标识该样本为已交付；
  2: '已交付',
  // 暂停：样本在生产过程中出现异常需要暂时停止交付；
  3: '暂停',
  // 终止：样本在生产过程中出现异常需要在某个环节结束生产进行交付；（一般是客户要求在某个环节结束生产进行交付）
  4: '终止'
}

/**
 * 定义了一个常量对象stepConfig，用于映射实验步骤的编号和名称。
 * 键为步骤编号，值为步骤名称。
 */
export const stepConfig = {
  //  1到样 2前处理 3提取 4质检 5文库 6建库 7杂交 8上机 9交付
  1: '到样',
  2: '前处理',
  3: '提取',
  4: '核酸质检',
  5: '文库质检',
  6: '建库',
  7: '杂交',
  8: '上机',
  9: '交付'
}

export const orderTypeConfig = {
  1: 'illumina文库订单',
  2: 'MGI文库订单',
  3: '组织核酸样本订单'
}

export const BOOLEAN_CONFIG = {
  1: '是',
  0: '否'
}

export const experimentalLinkConfig = {
  //  纯测序、建库测序、建库测序分析、提取建库测序、提取建库测序分析
  1: '纯测序',
  2: '建库测序',
  3: '建库测序分析',
  4: '提取建库测序',
  5: '提取建库测序分析'
}

export const IS_BACK_UP_CONFIG = {
  0: '正常样本',
  1: '备份样本'
}

// 预警
export const warningConfig = {
  // 1未建库预警 2未上机预警
  1: '未建库预警',
  2: '未上机预警'
}

export const notNullFields = {
//   freExperimentNum重实验次数
//   fdeliveryProblem交付问题
//   fdataNote数据备注（对应异常问题字段）
// forderNote订单备注
// fsampleNote样本备注
  freExperimentNum: '重实验次数',
  fdeliveryProblem: '交付问题',
  fdataNote: '数据备注',
  forderNote: '订单备注',
  fsampleNote: '样本备注'
}

/**
 * 根据传入的值，返回对应的实验步骤名称。
 * @param {Object} value - 包含cellValue属性的对象，cellValue应为实验步骤的编号。
 * @returns {string} - 对应编号的实验步骤名称。
 */
export const formatStep = (value) => {
  return stepConfig[value] || '-'
}

/**
 * 根据传入的值，返回对应的实验步骤名称。
 * @param {Object} value - 包含cellValue属性的对象，cellValue应为实验步骤的编号。
 * @returns {string} - 对应编号的实验步骤名称。
 */
export const formatDeliverStatus = (value) => {
  return deliverStatusConfig[value] || '-'
}

/**
 * 根据传入的值，返回对应的实验步骤名称。
 * @param {Object} value - 包含cellValue属性的对象，cellValue应为实验步骤的编号。
 * @returns {string} - 对应编号的实验步骤名称。
 */
export const formatQcStatus = (value) => {
  return dataStatus[value] || '-'
}
/**
 * 根据传入的值，返回对应的实验步骤名称。
 * @param {Object} value - 包含cellValue属性的对象，cellValue应为实验步骤的编号。
 * @returns {string} - 对应编号的实验步骤名称。
 */
export const formatOrderType = (value) => {
  return orderTypeConfig[value] || '-'
}

export const formatBoolean = (value) => {
  return BOOLEAN_CONFIG[value] || '-'
}

export const formatISBackUp = (value) => {
  return IS_BACK_UP_CONFIG[value] || '-'
}

export const formatWarning = (value) => {
  return warningConfig[value] || '-'
}

export const formatActualCycle = (hour, minute) => {
  // 如果小时和分钟都为0，返回空字符串
  if (!hour && !minute) {
    return ''
  }
  // 转数字
  hour = Number(hour)
  minute = Number(minute)
  // 如果只有分钟不为0，单独返回分钟数
  if (!hour) {
    return `${minute}分钟`
  }
  if (!minute) {
    return `${hour}小时`
  }

  // 将小时数大于等于24的情况转换为天数
  if (hour >= 24) {
    const day = Math.floor(hour / 24)
    hour %= 24
    return `${day}天${hour}小时${minute}分钟`
  }

  // 返回小时和分钟，这是最后一种情况
  return `${hour}小时${minute}分钟`
}
const colors = {
  '': '#606266',
  0: '#E6A23C',
  1: '#409EFF'
}
/**
 * 格式化输入的数据数组，为每个项目添加额外的属性和处理默认值。
 * @param {Array} data - 原始数据数组，每个元素代表一个项目。
 * @returns {Array} - 格式化后的数据数组，每个项目都包含了更多的属性和处理了默认值。
 */
export const dataFormating = (data = []) => {
  // 遍历数据数组，对每个项目进行处理
  return data.map(v => {
    // 创建一个新的对象，包含项目的原始属性
    const item = {
      fid: v.fid,
      geneCode: v.fgeneCode,
      oldSampleName: v.foldSampleName,
      currentStep: v.fcurrentStep,
      currentStepText: formatStep(v.fcurrentStep),
      deliverStatus: v.fdeliverStatus,
      deliverStatusText: formatDeliverStatus(v.fdeliverStatus),
      qcStatus: v.fqcStatus,
      qcStatusText: formatQcStatus(v.fqcStatus),
      confirmTime: v.fconfirmTime,
      orderCode: v.forderCode,
      orderId: v.forderId,
      orderType: v.forderType || '-',
      orderTypeText: formatOrderType(v.forderType),
      projectCode: v.fprojectCode,
      productType: v.fproductType,
      projectName: v.fprojectName,
      sampleType: v.fsampleType,
      productName: v.fproductName,
      libraryType: v.flibraryType,
      addTestOrderDataSize: v.faddTestOrderDataSize,
      actualCycleMinutes: v.factualCycleMinutes,
      actualCycleHour: v.factualCycleHour,
      actualCycleText: formatActualCycle(v.factualCycleHour, v.factualCycleMinutes),
      isBackUp: v.fisBackUp,
      isBackUpText: formatISBackUp(v.fisBackUp),
      instrumentType: v.finstrumentType,
      sequenceType: v.fsequenceType,
      laneTotal: v.flaneTotal,
      dataSize: v.fdataSize,
      dataSizeUnit: v.fdataSizeUnit,
      qcResult: v.fqcResult,
      outputTotal: v.foutputTotal,
      outputProportion: v.foutputProportion,
      outputProportionText: v.foutputProportion && `${v.foutputProportion}%`,
      deliveryMethod: v.fdeliveryMethod,
      qcReportTime: v.fqcReportTime,
      enableTime: v.fenableTime,
      buildLibFinishTime: v.fbuildLibFinishTime,
      sequenceTime: v.fsequenceTime,
      deplaneTime: v.fdeplaneTime,
      deliveryTime: v.fdeliveryTime,
      standardCycle: v.fstandardCycle,
      actualCycle: v.factualCycle,
      delayDays: v.fdelayDays,
      fisMergeAddTesting: v.fisMergeAddTesting,
      fisMergeAddTestingColor: colors[v.fisMergeAddTesting],
      fmergeAddTestingOutputTotal: v.fmergeAddTestingOutputTotal,
      isDelay: v.fisDelay,
      isDelayText: formatBoolean(v.fisDelay),
      reExperimentNum: v.freExperimentNum,
      flowWarning: v.fflowWarning,
      flowWarningText: formatWarning(v.fflowWarning),
      deliveryProblem: v.fdeliveryProblem,
      dataNote: v.fdataNote,
      orderNote: v.forderNote,
      sampleNote: v.fsampleNote,
      experimentalLink: v.fexperimentalLink,
      freleaseTotal: v.freleaseTotal
    }
    // 为项目创建一个深拷贝的原始数据对象，用于保留项目的初始状态
    // 创建该项目的深拷贝，用于保留原始数据不变
    item.realData = deepCopy(item)
    // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
    // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
    setDefaultEmptyValueForObject(item)
    return item
  })
}
