<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="导入到样"
      width="500px"
      @open="handleOpen">
      <div style="margin-top: 20px">
        <div class="upLoad">
          <el-upload
            ref="upload"
            :on-success="handleOnSuccess"
            :on-error="handleOnError"
            :on-change="handleChange"
            :auto-upload="false"
            :data="uploadParams"
            :limit="1"
            :file-list="fileList"
            :action="uploadUrl">
            <el-button size="mini" plain><i class="el-icon-upload"></i>上传文件</el-button>
            <span slot="tip" class="el-upload__tip" style="margin-left: 10px">仅支持Excel</span>
          </el-upload>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="dataResultVisible"
      :before-close="handleCloseDialog"
      :modal="false" title="数据错误提示"
      width="800px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      style="margin-top: 3%">
      <template>
        <el-table
          :data="tableData"
          stripe
          border
          size="mini"
          max-height="400px"
        >
          <el-table-column type="index" label="序号" width="50px">
          </el-table-column>
          <el-table-column prop="title" label="原始编号" width="220px" show-overflow-tooltip></el-table-column>
          <el-table-column prop="actualContent" label="文件实际内容" width="220px" show-overflow-tooltip></el-table-column>
          <el-table-column prop="errorReason" label="异常说明" min-width="220px" show-overflow-tooltip></el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dataResultVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../../util/mixins'
import constants from '../../../../../util/constants'
import util from '../../../../../util/util'
// import util from '../../../../../util/util'
export default {
  name: 'sampleConfirmImportDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderNumber: {
      type: String
    }
  },
  data () {
    return {
      loading: false,
      fileList: [], // 文件
      uploadParams: {},
      dataResultVisible: false,
      tableData: [],
      uploadUrl: constants.JS_CONTEXT + '/sample/confirm/import_confirm_file'
    }
  },
  methods: {
    test (row) {
      console.log(row)
    },
    // 点击确认
    handleConfirm () {
      if (this.$refs.upload.uploadFiles.length > 0) {
        let message = ''
        let valid = this.$refs.upload.uploadFiles.every(v => {
          if (/\.(xlsx|xls)$/i.test(v.name)) {
            return true
          } else {
            message = '只能上传Excel文件'
            return false
          }
        })
        if (!valid) {
          this.$message.error(message)
        } else {
          this.submitUpload()
        }
      } else {
        this.$message.error('请上传文件')
      }
    },
    handleClose () {
      if (this.loading) {
        this.$message.error('请等待文件上传完成后关闭弹窗')
        return
      }
      this.visible = false
      this.$emit('dialogCloseEvent')
    },
    submitUpload: function () {
      this.loading = true
      let data = {
        file: this.fileList.map(v => v.raw),
        t7OrderCode: this.orderNumber
      }
      this.$ajax({
        url: '/sample/confirm/import_confirm_file',
        data: data,
        isFormData: true
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.visible = false
          this.$message.success('上传成功')
          this.$emit('dialogConfirmEvent')
        } else {
          this.$refs.upload.clearFiles()
          this.fileList = []
          this.tableData = result.data || []
          this.dataResultVisible = this.tableData.length
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
      this.fileList = fileList
    },
    handleCloseDialog () {
      this.dataResultVisible = false
    },
    handleOnError () {
      this.loading = false
      this.loading = false
      this.$message.error('上传出现错误')
    },
    handleOpen () {
      this.fileList = []
    },
    handleOnSuccess () {
      this.loading = false
    },
    // 下载
    downloadCountRecords () {
      this.$ajax({
        url: '/sample/confirm/download_confirm_template',
        method: 'get',
        data: {
          t7OrderCode: this.orderNumber
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>
.download{
  font-weight: bold;
  margin-left: 15px;
}
.download:hover{
  color: #409EFF;
  cursor: pointer;
}
.upLoad{
  margin-left: 90px;
  margin-top: 20px;
}
.circle {
  width: 30px;
  height:30px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  border: 2px solid #000;
  font-size: 16px;
  font-weight: bold;
}

</style>
