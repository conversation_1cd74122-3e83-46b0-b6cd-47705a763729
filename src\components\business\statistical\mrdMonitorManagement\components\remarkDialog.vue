<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      title="备注"
      width="600px"
      @open="handleOpen">
      <el-form
        ref="form"
        size="mini"
        :model="form"
        :rules="rules"
        label-suffix="：">
        <template>
          <el-form-item  prop="remark">
            <el-input v-model.trim="form.remark" maxlength="500" type="textarea" clearable  :rows="6"/>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleNum: {
      type: String
    }
  },
  data () {
    return {
      loading: false,
      form: {
        remark: ''
      },
      rules: {
      }
    }
  },
  methods: {
    handleOpen () {
      this.getRemarks()
    },
    // 获取备注内容
    getRemarks () {
      this.$ajax({
        url: '/system/monitor/get_sample_mrd_remark',
        methods: 'get',
        data: {
          sampleNum: this.sampleNum
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.form.remark = res.data
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$ajax({
            url: '/system/monitor/save_sample_mrd_remark',
            data: {
              sampleNum: this.sampleNum,
              remark: this.form.remark
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.visible = false
              this.$message.success('保存成功')
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
