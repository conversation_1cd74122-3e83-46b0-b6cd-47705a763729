<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @open="handleOpen">
    <div class="dialog-content">
      <el-form :model="form" ref="form" size="mini" label-suffix=":" inline :rules="rules" label-width="150px">
        <div class="title">历史配置</div>
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model.trim="form.projectCode" type="textarea" autosize
                    class="form-width" maxlength="25" show-word-limit clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="文库类型" prop="libraryType">
          <el-input v-model.trim="form.libraryType" type="textarea" autosize
                    class="form-width" maxlength="25" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="交付类型" prop="deliverType">
          <el-select size="mini" v-model.trim="form.deliverType" class="form-width" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in deliverTypeList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否全保" prop="isInsurance">
          <el-select size="mini" v-model.trim="form.isInsurance" class="form-width" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in ['否', '是']" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型" prop="productType">
          <el-input v-model.trim="form.productType" type="textarea" autosize
                    class="form-width" maxlength="25" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item v-if="form.deliverType === '慢交付'" label="交付延时" prop="deliveryDelay">
          <el-input v-model.trim="form.deliveryDelay" class="form-width"  clearable placeholder="请输入">
            <template slot="append">小时</template>
          </el-input>
        </el-form-item>
        <el-form-item label="流程名称" prop="processName">
          <el-input v-model.trim="form.processName" type="textarea" autosize
                    class="form-width" maxlength="25" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="流程参数" prop="processParams">
          <el-input v-model.trim="form.processParams" type="textarea" autosize
                    class="form-width" maxlength="100" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="Q30+N标准" prop="q30NStandard">
          <el-input v-model.trim="form.q30NStandard" type="textarea" autosize
                    class="form-width" maxlength="100" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="数据产量标准（%）" prop="dataProductionStandard">
          <el-input v-model.trim="form.dataProductionStandard" type="textarea" autosize
                    class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="数据产量标准（G）" prop="dataProductionStandardG">
          <el-input v-model.trim="form.dataProductionStandardG" class="form-width" clearable
                    placeholder="请输入"></el-input>
        </el-form-item>
                <el-form-item label="公司名称" prop="companyName">
                  <el-select-v2 v-model.trim="form.companyName" class="form-width" :options="customerList" size="mini"
                                clearable filterable placeholder="请选择" style="width: 100%" @change="handleCompanyChange"/>
                </el-form-item>
        <el-form-item label="Company_English" prop="companyEnglish">
          <el-input v-model.trim="form.companyEnglish" type="textarea" autosize
                    class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <div class="title">单细胞</div>
        <el-form-item label="单细胞质控钉钉群通知webhook地址" prop="fsingleDingdingWebhook">
          <el-input v-model.trim="form.fsingleDingdingWebhook" type="textarea" autosize
                    class="form-width" maxlength="200" show-word-limit clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="@通知人员手机号" prop="fsingleDingdingPhone">
          <el-input v-model.trim="form.fsingleDingdingPhone" type="textarea" autosize
                    class="form-width" maxlength="100" show-word-limit clearable placeholder="请输入, 多个手机号以','隔开"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'
import util, {awaitWrap, setGroupData} from '../../../../../util/util'
import {setDeliveryConfig} from '../../../../../api/deliveryManagement'
import {getCustomerList} from '../../../../../api/system/productManagementApi'

export default {
  name: 'setDeliveryConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      title: '新增交付配置',
      loading: false,
      form: {
        fid: '',
        projectCode: '',
        productType: '',
        libraryType: '',
        isInsurance: '',
        deliveryDelay: 240,
        deliverType: '',
        processName: '',
        processParams: '',
        fsingleDingdingWebhook: '',
        fsingleDingdingPhone: '',
        cleanProcessParams: '',
        q30NStandard: '',
        dataProductionStandard: '',
        dataProductionStandardG: '',
        companyName: '',
        companyEnglish: ''
      },
      customerList: [],
      deliverTypeList: ['极致交付', '普通交付', '先下先交', '不交付', '慢交付'],
      rules: {
        projectCode: [{
          required: true, message: '请输入项目编码', trigger: 'blur'
        }],
        deliveryDelay: [
          {
            required: true, message: '请输入交付延时', trigger: 'blur'
          },
          {pattern: /^(0|[1-9][0-9]{0,3})$/, message: '请输入0-9999的整数', trigger: 'change'}
        ],
        deliverType: [{
          required: true, message: '请输入交付类型', trigger: 'blur'
        }],
        processName: [{
          required: true, message: '请输入流程名称', trigger: 'blur'
        }],
        processParams: [{
          required: true, message: '请输入流程参数', trigger: 'blur'
        }],
        q30NStandard: [{
          required: true, message: '请输入Q30+N标准', trigger: 'blur'
        }],
        dataProductionStandard: [
          {pattern: /^(0|[1-9][0-9]{0,3})(\.\d{1,2})?$/, message: '请输入0-9999的数字，最多两位小数', trigger: 'change'}
        ],
        dataProductionStandardG: [
          {pattern: /^(0|[1-9][0-9]{0,3})$/, message: '请输入0-9999的整数', trigger: 'change'}
        ],
        fsingleDingdingPhone: [
          {validator: util.validateElementPhone, trigger: 'change'}
        ],
        companyName: [{
          required: true, message: '请输入公司名称', trigger: 'blur'
        }],
        companyEnglish: [
          {required: true, message: '请输入Company_English', trigger: 'blur'},
          {
            validator (rule, value, callback) {
              // eslint-disable-next-line no-useless-escape
              const specialCharPattern = /[()（）{}\/\\:*?"<>| ]/ // 特殊字符正则表达式
              if (specialCharPattern.test(value)) {
                callback(new Error('填写数据存在特殊字符，请检查！'))
              } else {
                callback() // 验证通过
              }
            }
          }
        ]

      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        const formData = this.formData || {}
        this.title = '新增交付配置'
        if (formData.id) {
          this.title = '编辑交付配置'
        }
        this.getCustomer()
        this.form = {
          fid: formData.id,
          projectCode: formData.projectCode,
          productType: formData.productType,
          libraryType: formData.libraryType,
          deliverType: formData.deliverType,
          processName: formData.processName,
          deliveryDelay: formData.deliveryDelay || 240,
          processParams: formData.processParams,
          fsingleDingdingWebhook: formData.fsingleDingdingWebhook || '',
          cleanProcessParams: formData.cleanProcessParams,
          q30NStandard: formData.q30NStandard,
          isInsurance: formData.isInsurance,
          fsingleDingdingPhone: formData.fsingleDingdingPhone || '',
          dataProductionStandard: formData.dataProductionStandard,
          dataProductionStandardG: formData.dataProductionStandardG,
          companyName: formData.companyName,
          companyEnglish: formData.companyEnglish
        }
      })
    },
    async getCustomer () {
      const {res} = await awaitWrap(getCustomerList({}))
      if (res && res.code === this.SUCCESS_CODE) {
        this.customerList = res.data.map(v => {
          return {
            label: v.name,
            value: v.name
          }
        })
      }
    },
    handleCompanyChange () {
      this.form.companyEnglish = ''
    },
    setParams () {
      return {
        fid: this.form.fid,
        fprojectCode: this.form.projectCode,
        fslowDeliverDelay: this.form.deliverType === '慢交付' ? this.form.deliveryDelay : '',
        fproductType: this.form.productType,
        flibraryType: this.form.libraryType,
        fdeliverType: this.form.deliverType,
        fprocessName: this.form.processName,
        fprocessParams: this.form.processParams,
        fsingleDingdingWebhook: this.form.fsingleDingdingWebhook,
        fsingleDingdingPhone: setGroupData(this.form.fsingleDingdingPhone, ',', true),
        fcleanProcessParams: this.form.cleanProcessParams,
        fq30NStandard: this.form.q30NStandard,
        fisInsurance: this.form.isInsurance,
        fdataProductionStandard: this.form.dataProductionStandard,
        fdataProductionStandardG: this.form.dataProductionStandardG,
        fcompanyName: this.form.companyName,
        fcompanyEnglish: this.form.companyEnglish
      }
    },
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = this.setParams()
          this.loading = true
          let {res} = await awaitWrap(setDeliveryConfig(params))
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('设置成功')
            this.$emit('dialogConfirmEvent')
            this.visible = false
          }
          this.loading = false
        } else {
          this.$message.error('表单存在错误，请检查')
        }
      })
    }
  }
}
</script>

<style scoped>
.form-width {
  width: 200px;
}
/deep/ .el-textarea__inner {
  padding-bottom: 28px!important;
}
/deep/ .el-input__count {
  height: 20px;
}
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

  .title {
    height: 20px;
    line-height: 20px;
    padding-left: 5px;
    border-left: 4px solid #539fff;
    margin: 10px 0;
  }
</style>
