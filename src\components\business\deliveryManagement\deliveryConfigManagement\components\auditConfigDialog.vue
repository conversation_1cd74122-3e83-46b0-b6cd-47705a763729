<script>
import mixins from '../../../../../util/mixins'
import {awaitWrap, setDefaultEmptyValueForObject} from '../../../../../util/util'
import {auditDeliveryConfig, getDeliveryConfigDetail} from '../../../../../api/deliveryManagement'

export default {
  name: 'auditConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    fid: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false,
      configList: []
    }
  },
  methods: {
    handleOpen () {
      this.getDetailInfo()
    },
    // 获取交付配置详情
    async getDetailInfo () {
      const {res} = await awaitWrap(getDeliveryConfigDetail({
        fprojectCode: this.projectCode,
        fprojectDeliverConfigId: this.fid
      }, {loadingDom: '.tabs'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.configList = []
        data.forEach(v => {
          const item = {
            id: v.fid,
            projectCode: v.fprojectCode,
            productType: v.fproductType,
            libraryType: v.flibraryType,
            deliverType: v.fdeliverType,
            projectName: v.fprojectName,
            creator: v.fcreator,
            createTime: v.fcreateTime,
            updator: v.fupdator,
            deliveryDelay: v.fslowDeliverDelay,
            processName: v.fprocessName,
            processParams: v.fprocessParams,
            cleanProcessParams: v.fcleanProcessParams,
            q30NStandard: v.fq30NStandard,
            isDelete: v.fisDelete,
            updateTime: v.fupdateTime,
            auditStatusText: v.fisVerify ? '已审核' : '未审核',
            dataProductionStandard: v.fdataProductionStandard,
            dataProductionStandardG: v.fdataProductionStandardG,
            experimentRegion: v.fexperimentRegion,
            aliyunRegion: v.faliyunCloudRegion,
            ak: v.fakValue,
            sk: v.fskValue,
            aliyunBucket: v.faliyunCloudBucket,
            receiveEmail: v.fsendEmail,
            ccEmail: v.fccEmail,
            deliverWay: v.fdeliveryMethod,
            companyName: v.fcompanyName,
            companyEnglish: v.fcompanyEnglish,
            finishFlag: v.ffinishFlag,
            kuaquAk: v.fkuaquAkValue,
            kuaquSk: v.fkuaquSkValue,
            kuaquBucket: v.fkuaquBucket,
            kehuBucket: v.fkehuBucket,
            kuaquEndpoint: v.fkuaquEndpoint
          }
          item.realData = JSON.parse(JSON.stringify(item))
          setDefaultEmptyValueForObject(item)
          this.configList.push(item)
        })
      }
    },
    async handleConfirm () {
      this.loading = true
      const {res} = await awaitWrap(auditDeliveryConfig({
        fid: this.fid
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('审核成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
      this.loading = false
    }
  }
}
</script>

<template>
  <el-dialog
    append-to-body
    title="审核"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1000px"
    @opened="handleOpen">
    <el-tabs class="tabs">
      <el-tab-pane v-for="(item, index) in configList" :key="index" :label="item.experimentRegion">
        <el-descriptions class="margin-top" :column="2" size="mini" border>
          <el-descriptions-item label="项目编号">{{ item.projectName }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ item.projectCode }}</el-descriptions-item>
          <el-descriptions-item label="所属实验室">{{ item.experimentRegion }}</el-descriptions-item>
          <el-descriptions-item label="产品类型">{{ item.productType }}</el-descriptions-item>
          <el-descriptions-item label="文库类型">{{ item.libraryType }}</el-descriptions-item>
          <el-descriptions-item label="交付类型">{{ item.deliverType }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">{{ item.auditStatusText }}</el-descriptions-item>
          <el-descriptions-item label="流程名称">{{ item.processName }}</el-descriptions-item>
          <el-descriptions-item label="流程参数" :span="2">{{ item.processParams }}</el-descriptions-item>
<!--          <el-descriptions-item label="cleandata流程参数" :span="2">{{ item.cleanProcessParams }}</el-descriptions-item>-->
          <el-descriptions-item label="Q30+N标准" :span="2">{{ item.q30NStandard }}</el-descriptions-item>
          <el-descriptions-item label="数据产量标准(%)">{{ item.dataProductionStandard }}</el-descriptions-item>
          <el-descriptions-item label="数据产量标准(G)">{{ item.dataProductionStandardG }}</el-descriptions-item>
          <el-descriptions-item label="云地域" :span="2">{{ item.aliyunRegion }}</el-descriptions-item>
          <el-descriptions-item label="ak" :span="2">{{ item.ak }}</el-descriptions-item>
          <el-descriptions-item label="sk" :span="2">{{ item.sk }}</el-descriptions-item>
          <el-descriptions-item label="云Bucket" :span="2">{{ item.aliyunBucket }}</el-descriptions-item>
          <el-descriptions-item label="finishFlag" :span="2">{{ item.finishFlag }}</el-descriptions-item>
          <el-descriptions-item label="kuaquAk" :span="2">{{ item.kuaquAk }}</el-descriptions-item>
          <el-descriptions-item label="kuaquSk" :span="2">{{ item.kuaquSk }}</el-descriptions-item>
          <el-descriptions-item label="kuaquBucket" :span="2">{{ item.kuaquBucket }}</el-descriptions-item>
          <el-descriptions-item label="kehuBucket" :span="2">{{ item.kehuBucket }}</el-descriptions-item>
          <el-descriptions-item label="kuaquEndpoint" :span="2">{{ item.kuaquEndpoint }}</el-descriptions-item>
          <el-descriptions-item label="抄送邮箱" :span="2">{{ item.ccEmail }}</el-descriptions-item>
          <el-descriptions-item label="交付方式">{{ item.deliverWay }}</el-descriptions-item>
          <el-descriptions-item label="公司名称">{{ item.companyName }}</el-descriptions-item>
          <el-descriptions-item label="companyEnglish" :span="2">{{ item.companyEnglish }}</el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
    </el-tabs>

    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">通  过</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">
.tabs {
  min-height: 300px;
}
</style>
