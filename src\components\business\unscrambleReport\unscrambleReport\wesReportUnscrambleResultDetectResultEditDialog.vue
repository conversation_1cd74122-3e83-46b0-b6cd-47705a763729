<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="40%">
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="mini">
          <el-row>
           <el-col :span="12">
             <el-form-item label="MSI" prop="msi">
               <el-select v-model="form.msi" placeholder="请选择MSI">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in msiList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="HRD" prop="hrd">
               <el-select v-model="form.hrd" placeholder="请选择HRD">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in hrdList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wesReportUnscrambleResultDetectResultEditDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.form = this.pdata
      }
    }
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      loading: false,
      title: '编辑',
      visible: this.pvisible,
      form: {
        msi: '',
        hrd: ''
      },
      rules: {
        msi: [
          {required: true, message: '请选择MSI', trigger: ['blur', 'change']}
        ],
        hrd: [
          {required: true, message: '请选择HRD', trigger: ['blur', 'change']}
        ]
      },
      msiList: [
        {
          label: 'MSI-H',
          value: 'MSI-H'
        },
        {
          label: 'MSS',
          value: 'MSS'
        },
        {
          label: '-',
          value: '-'
        }
      ],
      hrdList: [
        {
          label: '阴性',
          value: '阴性'
        },
        {
          label: '阳性',
          value: '阳性'
        }
      ]
    }
  },
  methods: {
    handleClose () {
      this.$emit('detectResultEditDialogCloseEvent')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/read/wesUnscramble/edit_target_hint',
            data: {
              analysisRsId: this.analysisRsId,
              msi: this.form.msi === '-' ? '' : this.form.msi,
              hrd: this.form.hrd
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('修改成功！')
              this.$emit('detectResultEditDialogConfirmEvent')
              this.$refs.form.resetFields()
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  .el-select{
    width: 100%;
  }
</style>
