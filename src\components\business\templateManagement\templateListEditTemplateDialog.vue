<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="800px"
      @open="handleOpen">
      <div v-if="visible" style="height: 400px;overflow-y: auto;padding: 0 20px;" class="warp">
        <div class="container">
          <div class="container-header">基本信息</div>
          <el-form
                  :model="baseInfo"
                  :rules="rules"
                  ref="baseInfoForm"
                  size="mini"
                  label-width="120px"
                  class="form-container">
            <el-form-item label="模板编码" prop="templateCode" class="items-50">
              <el-input v-model="baseInfo.templateCode"></el-input>
            </el-form-item>
            <el-form-item label="模板名称" prop="templateName" class="items-50">
              <el-input v-model="baseInfo.templateName"></el-input>
            </el-form-item>
            <el-form-item label="文件类型" prop="fileType" class="items-50">
              <!--<el-input v-model="baseInfo.fileType"></el-input>-->
              <el-select v-model="baseInfo.fileType" placeholder="请选择文件类型">
                <template>
                  <el-option  v-for="item in fileTypeLists" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item v-if="baseInfo.fileType === 1" label="模板上传" prop="name" class="items-50">
              <el-upload
                ref="upload"
                :action="actionUrl"
                :auto-upload="false"
                :limit="1">
                <el-button size="mini" >点击上传</el-button>
                <div slot="tip" v-if="fileName" class="el-upload__tip">线上文件：{{fileName}}</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="模板分类" prop="name" class="items-50">
              <el-cascader
                      :options="templateCategoryLists"
                      v-model="baseInfo.templateType"
                      v-formLoading="categoryLoading"
                      :disabled="categoryLoading"
                      :show-all-levels="false"
                      :props="templateCategoryProp"
                      style="width: 100%;"
                      collapse-tags
                      filterable
                      clearable></el-cascader>
            </el-form-item>
            <el-form-item label="模板说明" prop="name" class="items-50">
              <el-input v-model="baseInfo.templateDirections" type="textarea"  placeholder="请输入"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="container">
          <div class="container-header">模板配置信息</div>
          <el-form
            :model="templateConfigInfo"
            ref="templateConfigForm"
            size="mini"
            label-width="120px"
            class="form-container">
            <el-form-item label="送检项目" prop="project" class="items-50">
              <el-input v-model="templateConfigInfo.project"></el-input>
            </el-form-item>
            <el-form-item label="NCCN配置" prop="NccnConfig" class="items-50">
              <el-select v-model="templateConfigInfo.NccnConfig" v-formLoading="NccnLoading">
                <template >
                  <el-option v-for="item in NccnConfigLists" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="化疗配置" prop="chemotherapyConfig" class="items-50">
              <el-select v-model="templateConfigInfo.chemotherapyConfig" v-formLoading="chemotherapyLoading">
                <template>
                  <el-option  v-for="item in chemotherapyConfigLists" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="PARP配置" prop="ParpConfig" class="items-50">
              <el-select v-model="templateConfigInfo.ParpConfig" v-formLoading="ParpLoading">
                <template>
                  <el-option v-for="item in ParpConfigLists" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="是否药厂报告" prop="isPharmaceuticalFactoryReport" class="items-50">
              <el-select v-model="templateConfigInfo.isPharmaceuticalFactoryReport">
                <el-option :value="0" label="是"></el-option>
                <el-option :value="1" label="否"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="证据等级配置" prop="evidenceLevelConfig" class="items-50">
              <el-select v-model="templateConfigInfo.evidenceLevelConfig">
                <el-option :value="0" label="报出"></el-option>
                <el-option :value="1" label="不报出"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="预后指标编码" prop="prognosticTargetCode" class="items-50">
              <el-input v-model="templateConfigInfo.prognosticTargetCode" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="NCCN解析" prop="versionNum" class="items-50">
              <el-select v-model="templateConfigInfo.versionNum" v-formLoading="ParpLoading">
                <template>
                  <el-option v-for="(item, index) in versionNums" :key="index" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="检测小结" prop="name" class="items-100">
              <el-input v-model="templateConfigInfo.detectSummary" type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="体细胞变异说明" prop="somaticVariationDescription" class="items-100">
              <el-input v-model="templateConfigInfo.somaticVariationDescription" type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="胚细胞变异说明" prop="germlineVariationDescription" class="items-100">
              <el-input v-model="templateConfigInfo.germlineVariationDescription" type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="检测方法说明" prop="detectMethod" class="items-100">
              <el-input v-model="templateConfigInfo.detectMethod" type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="核心基因配置" prop="coreMarkerGene" class="items-100">
              <el-input v-model.trim="templateConfigInfo.coreMarkerGene" clearable type="textarea" placeholder="多个基因用顿号或逗号隔开，HRR基因的输入形式为“HRR:gene1+gene2+gene3+...”"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="container">
          <div class="container-header">参考文献</div>
          <div>
            <el-button size="mini" @click="handleClearAllReference">全部清空</el-button>
            <el-button size="mini" type="primary" @click="showGetReferences = true">匹配</el-button>
          </div>
          <div style="display: flex;align-items: start;margin-top: 10px;">
            <!--<el-input v-model="templateConfigInfo.references" disabled type="textarea"></el-input>-->
            <div style="width: 100%;">
              <div :key="item.fid" v-for="(item, index) in templateConfigInfo.references" class="references-list">
                <p>{{item.fname}}</p>
                <div class="icon">
                  <i class="el-icon-delete" @click="handleDeleteReferences(index)"></i>
                </div>
              </div>
            </div>
            <!--<el-button size="mini" type="primary" style="margin-left: 10px;" @click="showGetReferences = true">匹配</el-button>-->
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
      <el-dialog
          :visible.sync="showGetReferences"
          :close-on-click-modal="false"
          :before-close="handleGetReferencesClose"
          title="匹配文献"
          width="500px"
          append-to-body>
        <div class="references-path">
          <p>文献ID:</p>
          <div style="display: flex;align-items: start;">
            <el-input v-model="referencesIdInput" type="textarea" placeholder="请输入文献ID， 多个id已逗号分隔"></el-input>
            <el-button size="mini" type="primary" style="margin-left: 10px;" @click="handleSearchReferences">文献匹配</el-button>
          </div>
        </div>
        <div class="references-path">
          <p>参考文献</p>
          <el-table ref="referenceTable" :data="referencesLists" class="referenceTable" style="width: 100%" border>
            <el-table-column type="selection" width="45"></el-table-column>
            <el-table-column prop="fname" label="文献" min-width="180"></el-table-column>
          </el-table>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="handleGetReferencesClose">取 消</el-button>
          <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleConfirmReferences">确认</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
// import util from '../../../util/util'
// import Cookies from 'js-cookie'
export default {
  name: 'templateListEditTemplateDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    title: String,
    templateInfo: Object | null
  },
  data () {
    return {
      submitBtnLoading: false, // 提交按钮
      baseInfo: { // 基础信息
        templateCode: '',
        templateName: '',
        fileType: '',
        templateFile: '',
        templateDirections: '',
        templateType: '' // 模板分类
      },
      templateConfigInfo: { // 模板配置信息
        project: '', // 送检项目
        NccnConfig: '', // Nccn配置
        versionNum: '', // NCCN版本號
        chemotherapyConfig: '', // 化疗配置
        ParpConfig: '', // PARP配置
        isPharmaceuticalFactoryReport: '', // 是否药厂报告
        evidenceLevelConfig: '', // 证据等级配置
        detectSummary: '', // 检测小结
        somaticVariationDescription: '', // 体细胞变异说明
        germlineVariationDescription: '', // 胚系变异说明
        detectMethod: '', // 检测方法说明
        coreMarkerGene: '', // 核心基因配置
        prognosticTargetCode: '',
        references: [] // 参考文献
      },
      NccnConfigLists: [], // Nccn配置选项
      NccnLoading: false,
      VersionLoading: false,
      chemotherapyConfigLists: [], // 化疗配置选项
      chemotherapyLoading: false,
      ParpConfigLists: [], // Parp配置选项
      ParpLoading: false,
      fileTypeLists: [
        {label: '完整文件', value: 1},
        {label: 'word模块组合', value: 2},
        {label: 'VM模块组合', value: 3}
      ],
      templateCategoryLists: [], // 模板分类
      categoryLoading: false, // 模板分类
      templateCategoryProp: {
        checkStrictly: true,
        value: 'fid',
        label: 'fcategoryName'
      },
      versionNums: [],
      actionUrl: constants.JS_CONTEXT + '',
      uploadParams: {}, // 上传文件的数据
      fileName: '', // 文件名字
      hasUploadFile: false,
      fileList: [], // 文件列表
      showGetReferences: false,
      referencesIdInput: '', // 输入的匹配参考文献的Id
      referencesLists: [], // 参考文献列表
      referencesSelectRows: new Map(), // 选中文献项
      rules: {
        templateCode: [
          { required: true, message: '请输入模板编码', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        templateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        fileType: [
          { required: true, message: '请选择文件类型', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.initData()
      let isNew = this.title === '新建模板'
      let data = this.templateInfo || {}
      this.fileName = isNew ? '' : data.fileName
      let baseInfo = isNew
        ? { // 基础信息
          id: '',
          templateCode: '',
          templateName: '',
          fileType: '',
          templateFile: '',
          templateDirections: '',
          templateType: '' // 模板分类
        }
        : {
          id: data.id,
          templateCode: data.templateCode,
          templateName: data.templateName,
          fileType: data.fileType,
          // templateFile: '',
          templateDirections: data.templateDirections,
          templateType: data.categoryArr // 模板分类
        }
      let templateConfigInfo = isNew
        ? { // 模板配置信息
          project: '', // 送检项目
          NccnConfig: '', // Nccn配置
          versionNum: '',
          chemotherapyConfig: '', // 化疗配置
          ParpConfig: '', // PARP配置
          isPharmaceuticalFactoryReport: '', // 是否药厂报告
          evidenceLevelConfig: '', // 证据等级配置
          prognosticTargetCode: '',
          detectSummary: '', // 检测小结
          somaticVariationDescription: '', // 体细胞变异说明
          germlineVariationDescription: '', // 胚系变异说明
          detectMethod: '', // 检测方法说明
          coreMarkerGene: '', // 核心基因配置
          references: [] // 参考文献
        }
        : {
          project: data.project, // 送检项目
          NccnConfig: data.NccnConfig, // Nccn配置
          versionNum: data.versionNum, // NCCN解析
          chemotherapyConfig: data.chemotherapyConfig, // 化疗配置
          ParpConfig: data.ParpConfig, // PARP配置
          isPharmaceuticalFactoryReport: data.isPharmaceuticalFactoryReport, // 是否药厂报告
          evidenceLevelConfig: data.evidenceLevelConfig, // 证据等级配置
          prognosticTargetCode: data.prognosticTargetCode,
          detectSummary: data.detectSummary, // 检测小结
          somaticVariationDescription: data.somaticVariationDescription, // 体细胞变异说明
          germlineVariationDescription: data.germlineVariationDescription, // 胚系变异说明
          detectMethod: data.detectMethod, // 检测方法说明
          coreMarkerGene: data.coreMarkerGene, // 核心基因配置
          references: data.references // 参考文献
        }
      this.baseInfo = baseInfo
      this.templateConfigInfo = templateConfigInfo
      this.$nextTick(() => {
        this.$refs.baseInfoForm.clearValidate()
      })
    },
    initData () {
      this.getTemplateCategoryLists()
      this.getCtConfig()
      this.getNccnConfigOptions()
      this.getVersionNums()
      this.getParpConfig()
    },
    // 获取模板分类列表
    getTemplateCategoryLists () {
      this.categoryLoading = true
      this.$ajax({
        url: '/system/template/get_category_tree',
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.templateCategoryLists = res.data || []
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.categoryLoading = false
      })
    },
    // 全部删除
    handleClearAllReference () {
      if (this.templateConfigInfo.references.length === 0) return
      this.$confirm('确定清除全部参考文献吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.templateConfigInfo.references = []
      })
    },
    // 删除某一条
    handleDeleteReferences (index) {
      this.templateConfigInfo.references.splice(index, 1)
    },
    // 关闭匹配参考文献弹窗
    handleGetReferencesClose () {
      this.referencesIdInput = ''
      this.referencesLists = []
      this.showGetReferences = false
      this.referencesSelectRows.clear()
    },
    // 匹配文献
    handleSearchReferences () {
      if (!this.referencesIdInput) {
        this.$message.error('请输入文献ID')
        return
      }
      // let inputIds = this.referencesIdInput.replace(/\s+|，/g, ',').split(',').filter(v => { return v })
      this.$ajax({
        url: '/system/template/search_report_doc',
        data: {
          docIds: this.referencesIdInput
        },
        method: 'get',
        loadingDom: '.referenceTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.referencesLists = []
          this.referencesSelectRows.clear()
          let data = res.data || []
          data.forEach(v => {
            let item = {
              fid: v.fid,
              fname: v.fname
            }
            this.referencesLists.push(item)
            this.referencesSelectRows.set(item.fid, item)
          })
          this.$nextTick(() => {
            this.$refs.referenceTable.toggleAllSelection()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleConfirmReferences () {
      let referencesMap = new Map()
      this.templateConfigInfo.references.forEach(item => {
        referencesMap.set(item.fid, item)
      })
      this.referencesSelectRows.forEach((value, key) => {
        console.log(key, value)
        referencesMap.set(key, value)
      })
      this.templateConfigInfo.references = [...referencesMap.values()]
      this.handleGetReferencesClose()
    },
    // 获取NCCN配置
    getNccnConfigOptions () {
      this.NccnLoading = true
      this.$ajax({
        url: '/system/template/nccn_config',
        data: {},
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.NccnConfigLists = []
          data.forEach(item => {
            let v = {
              label: item,
              value: item
            }
            this.NccnConfigLists.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.NccnLoading = false
      })
    },
    getVersionNums () {
      this.VersionLoading = true
      this.$ajax({
        url: '/system/template/get_version_num_list',
        data: {},
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.versionNums = []
          data.forEach(item => {
            let v = {
              label: item,
              value: item
            }
            this.versionNums.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.VersionLoading = false
      })
    },
    // 获取化疗配置
    getCtConfig () {
      this.chemotherapyLoading = true
      this.$ajax({
        url: '/system/template/ct_config',
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.chemotherapyConfigLists = []
          data.forEach(item => {
            let v = {
              label: item,
              value: item
            }
            this.chemotherapyConfigLists.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.chemotherapyLoading = false
      })
    },
    // 获取Parp
    getParpConfig () {
      this.ParpLoading = true
      this.$ajax({
        url: '/system/template/parp_config',
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.ParpConfigLists = []
          data.forEach(item => {
            let v = {
              label: item,
              value: item
            }
            this.ParpConfigLists.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.ParpLoading = false
      })
    },
    // 保存
    handleDialogConfirm () {
      this.$refs.baseInfoForm.validate(valid => {
        if (valid) {
          let file = ''
          if (this.$refs.upload && this.$refs.upload.uploadFiles.length > 0) {
            file = this.$refs.upload.uploadFiles[0].raw
          }
          // this.$refs.upload.submit()
          let refsIds = this.templateConfigInfo.references.map(v => {
            return v.fid
          })
          let type = this.baseInfo.templateType.length > 0 ? this.baseInfo.templateType[this.baseInfo.templateType.length - 1] : ''
          this.submitBtnLoading = true
          let templateInfo = this.templateInfo || {}
          this.$fileAjax({
            url: '/system/template/create_or_update_template',
            data: {
              templateFlie: file,
              reportTemplateId: this.baseInfo.id || '',
              oldTemplateId: templateInfo.oldTemplateId || '', // 老模板的id，复制文件时使用
              reportCode: this.baseInfo.templateCode,
              reportName: this.baseInfo.templateName,
              fileType: this.baseInfo.fileType,
              templateDetail: this.baseInfo.templateDirections,
              templateType: type,
              sendTestProject: this.templateConfigInfo.project,
              fnccnTargetTypeCode: this.templateConfigInfo.NccnConfig,
              fversionNum: this.templateConfigInfo.versionNum,
              fctDrugTypeCode: this.templateConfigInfo.chemotherapyConfig,
              fparpConfigTypeCode: this.templateConfigInfo.ParpConfig,
              medicineReport: this.templateConfigInfo.isPharmaceuticalFactoryReport,
              levelOut: this.templateConfigInfo.evidenceLevelConfig,
              testRsSummary: this.templateConfigInfo.detectSummary,
              testRsSummaryT: this.templateConfigInfo.somaticVariationDescription,
              testRsSummaryP: this.templateConfigInfo.germlineVariationDescription,
              testDetail: this.templateConfigInfo.detectMethod,
              coreMarkerGene: this.templateConfigInfo.coreMarkerGene,
              frLibraryId: refsIds.toString(),
              fprognosticTargetCode: this.templateConfigInfo.prognosticTargetCode
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success('成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.submitBtnLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .container-header{
    line-height: 40px;
    padding-left: 20px;
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
    border-bottom: 1px solid #b9b9b9;
  }
  .form-container{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .items-50{
    width: 50%!important;
  }
  .items-100{
    width: 100%;
  }
  .references-list{
    display: flex;
    align-items: start;
    flex-wrap: nowrap;
    font-size: 15px;
    margin-bottom: 5px;
    border-bottom: 1px solid #f2f2f2;
    &:hover{
      & .icon .el-icon-delete{
        display: block;
      }
    }
    & > p {
      width: 100%;
    }
    .icon{
      margin-left: 20px;
      color: red;
      flex-shrink: 0;
      margin-top: 5px;
      width: 1.5em;
      height: 1.5em;
      .el-icon-delete{
        display: none;
        cursor: pointer;
      }
    }
  }
  .references-path{
    margin-bottom: 20px;
    & > p{
      margin-bottom: 10px;
      font-weight: 600;
      color: #000;
    }
  }
</style>
