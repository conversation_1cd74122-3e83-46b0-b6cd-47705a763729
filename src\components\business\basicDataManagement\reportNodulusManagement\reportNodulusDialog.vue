<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="97%"
      top="5vh"
      @open="handleOpen">
      <div class="wrapper dialog-wrapper">
        <!--        癌种列表-->
        <div v-if="type !== 2" class="category">
          <div
            :id="item"
            :key="index"
            :class="{'category-item-active': item === cancer}"
            v-for="(item, index) in cancers"
            class="category-item"
            @click="handleChangeCancer(item)">
            <tooltips :txt-info="item"></tooltips>
          </div>
        </div>
        <!--        报告配置-->
        <div class="info">
          <el-collapse v-model="activeNames" style="width: 100%" clearable size="mini">
            <el-collapse-item title="前置条件(条件关系为且)" name="1">
              <!--              前置条件-->
              <div class="precondition">
                <!--                客戶-->
                <div class="precondition-left">
                  <div id="info" class="title precondition-left-item">客户</div>
                  <div :key="index"
                       v-for="(item, index) in customerList"
                       style="display: flex;justify-content: space-between;"
                       class="precondition-left-item">
                    <div>
                      {{item.customData}}
                    </div>
                    <div v-if="type !== 0">
                      <i style="color: red" class="el-icon-delete" @click="handleDeleteCustomer(index)"></i>
                    </div>
                  </div>
                  <div v-if="type !== 0">
                    <el-button :loading="loadingCustom"
                               v-if="showMore"
                               icon="el-icon-plus"
                               size="mini"
                               class="precondition-left-item more"
                               @click="handleCustomer"></el-button>
                    <div v-else>
                      <el-select v-model.trim="customCode"
                                 v-lazyLoad="lazyOption"
                                 :remote-method="handleSearchCustomer"
                                 size="mini"
                                 filterable
                                 remote
                                 clearable
                                 style="width: 100%"
                                 placeholder="请选择（按客户编码搜索）"
                                 @visible-change="handleVisible">
                        <el-option
                          :key="index + '客户'"
                          :label="item.customData"
                          :value="item.customCode"
                          v-for="(item, index) in customeres">
                        </el-option>
                      </el-select>
                      <el-button
                        size="mini"
                        icon="el-icon-check"
                        class="precondition-left-item more"
                        @click="handleSave"></el-button>
                    </div>
                  </div>
                </div>
                <!--                癌种-->
                <div class="precondition-right">
                  <div class="title">癌种(条件关系为或)</div>
                  <div class="precondition-right-item">
                    二级癌种：
                    <el-cascader
                      v-model="carcinomaTwo"
                      :disabled="disabled"
                      :options="conditionalOneList"
                      :props="props"
                      :show-all-levels="false"
                      clearable
                      filterable
                      collapse-tags
                      style="width: 80%"
                      size="mini"
                    ></el-cascader>
                  </div>
                  <div class="precondition-right-item">
                    二级癌种为空时，一级癌种
                    <el-select v-model="carcinomaOne" :disabled="disabled" size="mini" clearable filterable placeholder="请选择">
                      <el-option
                        :key="index + '一级癌种'"
                        :label="item.label"
                        :value="item.value"
                        v-for="(item, index) in conditionalOneList">
                      </el-option>
                    </el-select>
                    且解读匹配癌种为：
                    <el-select v-model="carcinomaThree" :disabled="disabled" size="mini" clearable multiple filterable placeholder="请选择">
                      <el-option
                        :key="index + '解读癌种'"
                        :label="item.label"
                        :value="item.value"
                        v-for="(item, index) in conditionalCancers">
                      </el-option>
                    </el-select>
                  </div>
                  <div class="precondition-right-item">
                    一，二级癌种都为空时，则检查解读匹配癌种：
                    <el-select v-model="carcinomaFour"
                               :disabled="disabled"
                               size="mini"
                               clearable
                               multiple
                               filterable
                               placeholder="请选择">
                      <el-option
                        :key="index + '匹配癌种'"
                        :label="item.label"
                        :value="item.value"
                        v-for="(item, index) in conditionalCancers">
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </div>
              <div class="report-precondition">
                报告模版配置: 科服流量报告小结模板.docx
              </div>
            </el-collapse-item>

            <el-collapse-item title="癌种模块配置" name="2">
              <div style="padding: 10px">
                <el-table
                  :data="cancerTableData"
                  ref="table"
                  border
                  class="table"
                  size="mini"
                  height="150px">
                  <el-table-column prop="cancer" label="癌种" show-overflow-tooltip>
                  </el-table-column>
                  <el-table-column prop="basic" label="基本信息" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.basic" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="targetDrug" label="靶向用药提示" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.targetDrug" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="otherDrug" label="其他药物提示" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.otherDrug" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="endocrineTherapy" label="内分泌治疗" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.endocrineTherapy" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="antiAngiogenesisDrug" label="抗血管药物生成提示" width="140px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.antiAngiogenesisDrug" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="免疫治疗药物提示"  show-overflow-tooltip>
                    <el-table-column
                      prop="immunityTherapyTmb"
                      label="TMB"
                      width="120">
                      <template slot-scope="scope">
                        <el-select v-model="scope.row.immunityTherapyTmb" size="mini" :disabled="disabled">
                          <el-option
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                            v-for="(item, index) in options"
                          ></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="immunityTherapyMsi"
                      label="MSI"
                      width="120px">
                      <template slot-scope="scope">
                        <el-select v-model="scope.row.immunityTherapyMsi"  size="mini" :disabled="disabled">
                          <el-option
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                            v-for="(item, index) in options"
                          ></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="immunityTherapyData"
                      label="免疫影疗效影响因素"
                      width="140px">
                      <template slot-scope="scope">
                        <el-select v-model="scope.row.immunityTherapyData" size="mini" :disabled="disabled">
                          <el-option
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                            v-for="(item, index) in options"
                          ></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column prop="tumorDynamicMonitor" label="肿瘤动态监测分子指标结果提示" width="200px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.tumorDynamicMonitor" :disabled="disabled" clearable size="mini" @change="handleChange(scope.$index)">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="parpDrugTargetResult" label="PARP抑制剂靶向药物相关标志物检测结果及用药提示" width="320px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.drugTargetResult" :disabled="disabled" clearable size="mini" @change="handleChange(scope.$index)">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="drugTargetResult" label="靶向药物相关标志物检测结果及用药提示" width="240px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.parpDrugTargetResult" :disabled="disabled" clearable size="mini" @change="handleChange(scope.$index)">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="hrdDetectionResult" label="同源重组修复缺陷（HRD）状态检测结果" width="240px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.hrdDetectionResult" :disabled="disabled" clearable size="mini" @change="handleChange(scope.$index)">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="chemotherapy" label="化疗" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.chemotherapy" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="prognosis" label="预后" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.prognosis" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="genetic" label="遗传" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.genetic" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="说明" width="120px" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.description" size="mini" :disabled="disabled">
                        <el-option
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          v-for="(item, index) in options"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-collapse-item>

            <el-collapse-item title="产品模块配置" name="3">
              <div style="padding: 10px">
                <div style="margin: 5px 0">
                  <el-input
                    v-model.trim="productName"
                    size="mini"
                    style="width: 160px"
                    clearable
                    placeholder="请输入产品名称"
                    @keyup.enter.native="getProductInfo"></el-input>
                  <el-input
                    v-model.trim="productCode"
                    size="mini"
                    style="width: 160px"
                    clearable
                    placeholder="请输入产品编码"
                    @keyup.enter.native="getProductInfo"></el-input>
                  <el-button type="primary" size="mini" @click="getProductInfo">搜索</el-button>
                </div>
                <vxe-table
                  ref="tableRef"
                  border
                  resizable
                  height="300px"
                  keep-source
                  show-overflow
                  :data="productTableData"
                  size="mini"
                  :valid-config="{msgMode: 'full'}"
                  :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
                  @edit-actived="handleEdit"
                >
                  <vxe-column v-if="!disabled" title="操作" fixed="left" width="180">
                    <template #default="{ row, rowIndex }">
                      <el-button v-if="rowIndex === productTableData.length - 1" type="text" size="mini" @click="handAddProduct">新增</el-button>
                      <el-button type="text" size="mini" @click="handDeleteProduct(rowIndex)">删除</el-button>
                      <el-button v-if="row.isFix" type="text" size="mini" @click="handSaveProduct(row, rowIndex)">保存</el-button>
                    </template>
                  </vxe-column>
                  <vxe-table-column field="proCode" fixed="left" title="产品编码" width="120"
                                    :edit-render="{name: '$select', optionProps: {label: 'label', value: 'label'},
                                     options: productNames, props: {clearable: true, disabled: disabled || isProductCodeDisabled, filterable: true},
                                      events: {change: handleProductChange, clear: handleClear}}"></vxe-table-column>
                  <vxe-table-column field="proName" fixed="left" title="产品名称" width="200"
                                    :edit-render="{name: '$select', optionProps: {label: 'value', value: 'value'},
                                     options: productNames, props: {clearable: true, disabled: disabled || isProductCodeDisabled, filterable: true},
                                      events: {change: handleProductNameChange, clear: handleClear}}"></vxe-table-column>
                  <vxe-table-column field="basic" title="基本信息" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="targetDrug" title="靶向用药提示" width="120" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="otherDrug" title="其他药物提示" width="120" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="endocrineTherapy" title="内分泌治疗" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="antiAngiogenesisDrug" title="抗血管药物生成提示" width="180" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-colgroup title="免疫治疗药物提示">
                    <vxe-table-column field="immunityTherapyTmb" title="TMB" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                    <vxe-table-column field="immunityTherapyMsi" title="MSI" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                    <vxe-table-column field="immunityTherapyData" title="免疫影疗效影响因素" width="150" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  </vxe-colgroup>
                  <vxe-table-column field="tumorDynamicMonitor" title="肿瘤动态监测分子指标结果提示" width="240" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="parpDrugTargetResult" title="PARP抑制剂靶向药物相关标志物检测结果及用药提示" width="330" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="drugTargetResult" title="靶向药物相关标志物检测结果及用药提示" width="320" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="hrdDetectionResult" title="同源重组修复缺陷（HRD）状态检测结果" width="280" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="chemotherapy" title="化疗" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="prognosis" title="预后" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="genetic" title="遗传" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>
                  <vxe-table-column field="description" title="说明" width="100" :edit-render="{name: '$select', options: options, props: {clearable: true, disabled: disabled}, events: {change: handleChange}}"></vxe-table-column>

                </vxe-table>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button v-if="type !== 0" :loading="loading" type="primary"  size="mini"  @click="handleConfirm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import Constants from '../../../../util/constants'
import {awaitWrap} from '../../../../util/util'
import {deleteProduct} from '../../../../api/system/reportNodulesApi'
export default {
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    type: {
      type: Number
    },
    cancerName: {
      type: String
    }
  },
  data () {
    return {
      title: '报告小结配置修改',
      titles: { // 0: 查看詳情 1：配置 2：複製新增
        0: '查看',
        1: '报告小结配置修改',
        2: '复制新增报告小结配置'
      },
      lazyOption: {
        loadData: this.getCustomerInfo,
        distance: 20,
        scrollBody: '.el-scrollbar__wrap', // 为el-select 滚动容器的DOM元素的class选择器
        callback: (fn) => {
          // 这里是在组件销毁前, 移除监听事件.
          this.$once('hook:beforeDestroy', () => fn())
        }
      },
      productName: '',
      productCode: '',
      productNames: [],
      newCancerName: '',
      customerList: [],
      action: Constants.JS_CONTEXT + '/system/summaryReport/upload_template',
      disabled: false,
      cancers: [],
      cancerTableData: [],
      activeNames: ['1', '2', '3'],
      productTableData: [],
      loading: false,
      loadingCustom: false,
      cancer: '', // 新增癌种
      cancerInfo: {}, // 癌种配置信息
      carcinomaOne: '',
      carcinomaTwo: '',
      carcinomaThree: '',
      carcinomaFour: '',
      isProductCodeDisabled: true,
      options: [
        {label: '否', value: 1},
        {label: '是', value: 0}
      ],
      conditionalTwoList: [], // 多级癌种列表
      conditionalOneList: [],
      conditionalCancers: [], // 解读匹配癌种列表
      downloadLoading: false,
      props: { multiple: true, emitPath: false }, // 级联选择器参数
      customCode: '',
      showMore: true, // 展示新增用户按钮
      customeres: [] // 客户列表
    }
  },
  methods: {
    async handleOpen () {
      this.getCustomerInfo() // 获取所有客户列表
      await this.getCancer() // 获取癌种分类列表
      this.handleInit() // 初始化页面
      this.$nextTick(() => {
        document.getElementById(this.cancer).scrollIntoView(false)
        document.getElementById('info').scrollIntoView(false)
      }) // 重置滚动条
      this.getCancerInfo() // 获取报告小结配置
      this.getProductInfo() // 获取产品配置
      this.getCancerList(1) // 获取一级癌种列表
      this.getCancerList(0) // 获取解读匹配癌种列表
      this.getAllProductName() // 获取所有产品名称
    },
    async handleClose () {
      if (this.type === 2) {
        await this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.handleDelete()
      }
      this.$emit('dialogCloseEvent')
      this.visible = false
    },

    /** ***********页面操作******************/
    // 保存癌种报告小结信息
    async handleConfirm () {
      this.carcinomaTwo = this.carcinomaTwo || []
      let {code, message} = await this.$ajax({
        url: '/system/summaryReport/save_cancer_report_info',
        data: {
          id: this.id,
          conditionalOne: this.carcinomaTwo.join(','),
          conditionalTwo: JSON.stringify({
            cancer: this.carcinomaOne,
            readCancer: this.carcinomaThree
          }),
          conditionThreeList: this.carcinomaFour,
          fcustomerCode: this.customerList.map(v => v.customCode).join(','),
          ...this.cancerTableData[0]
        }
      })
      if (code === this.SUCCESS_CODE) {
        this.$message.success('保存成功')
        if (this.type === 2) {
          this.visible = false
          this.$emit('dialogConfirmEvent')
        }
      } else {
        this.$message.error(message)
      }
    },
    // 删除癌种
    async handleDelete () {
      let {code, message} = await this.$ajax({
        url: '/system/summaryReport/delete_cancer_report_info',
        data: {
          cancer: this.cancerName
        }
      })
      if (code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
      } else {
        this.$message.error(message)
      }
    },

    /** ***********客户相关操作******************/
    // 新增客户
    handleCustomer () {
      this.showMore = false
    },
    // 删除客户
    handleDeleteCustomer (index) {
      this.customerList.splice(index, 1)
    },
    // 保存客户
    handleSave () {
      if (!this.customCode) {
        this.$message.error('请选择客户保存')
        return
      }
      let customer = this.customeres.find(v => v.customCode === this.customCode)
      let include = this.customerList.some(v => v.customCode === this.customCode)
      if (include) {
        this.$message.error(customer.customData + '已存在客户列表中')
        return
      }
      this.showMore = true
      this.customerList.push(customer)
      this.customCode = ''
    },
    // 切换癌种
    async handleChangeCancer (cancer) {
      if (this.type !== 0) {
        await this.$confirm(`切换癌种后，当前癌种的修改不会保存，是否继续切换。`, '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
      this.cancer = cancer
      document.getElementById('info').scrollIntoView(false)
      this.getCancerInfo()
      this.getProductInfo()
    },
    handleVisible (visible) {
      if (visible) this.handleSearchCustomer()
    },
    // 远程搜索客户信息
    handleSearchCustomer (customCode) {
      this.currentPage = 1
      this.customeres = []
      this.getCustomerInfo(customCode)
    },

    /** ***********产品相关操作******************/
    // 新增产品
    handAddProduct () {
      this.productTableData.push({
        id: '',
        basic: '',
        targetDrug: '',
        otherDrug: '',
        endocrineTherapy: '',
        antiAngiogenesisDrug: '',
        immunityTherapyTmb: '',
        immunityTherapyMsi: '',
        immunityTherapyData: '',
        chemotherapy: '',
        prognosis: '',
        genetic: '',
        description: '',
        proName: '',
        proCode: '',
        tumorDynamicMonitor: 1,
        drugTargetResult: 1,
        parpDrugTargetResult: 1,
        hrdDetectionResult: 1,
        isFix: true,
        showProductName: true
      })
    },
    handleEdit ({ rowIndex, row }) {
      this.isProductCodeDisabled = !row.showProductName
    },
    /**
     * 删除对应索引的产品报告小结数据
     * @param index 索引
     */
    async handDeleteProduct (index) {
      if (!this.productTableData[index].showProductName) {
        await this.$confirm(`确定删除产品配置: ${this.productTableData[index].proName}`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
      const {res} = await awaitWrap(deleteProduct({
        productConfigId: this.productTableData[index].id
      }, {
        method: 'get'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
        this.productTableData.splice(index, 1)
      }
    },
    /**
     * 修改判断产品配置项是否修改的参数
     * @param index 索引
     */
    handleChange ({row}) {
      row.isFix = true
    },
    /**
     * 保存对应产品
     * @param product 保存的产品
     * @param index 索引
     */
    async handSaveProduct (product, index) {
      if (!product.proName) {
        this.$message.error('请选择产品名称')
        return
      }
      this.loading = true
      try {
        let {code, message} = await this.$ajax({
          url: '/system/summaryReport/save_product_report_info',
          data: {
            cancer: this.cancer,
            ...this.productTableData[index]
          }
        })
        if (code === this.SUCCESS_CODE) {
          this.productTableData[index].isFix = false
          this.productTableData[index].showProductName = false
          this.$message.success('保存成功')
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loading = false
      }
    },

    // 根据type初始化弹窗
    handleInit () {
      this.disabled = this.type === 0
      this.type !== 2
        ? this.title = this.titles[this.type]
        : this.title = this.titles[this.type] + '-' + this.cancerName
      this.currentPage = 1
      this.showMore = true
      this.productCode = ''
      this.productName = ''
      this.cancerName ? this.cancer = this.cancerName : this.cancer = this.cancers[0]
      this.activeNames = ['1', '2', '3']
    },
    // 获取用户信息, 分页展示，多了就让用户远程搜索
    async getCustomerInfo (customCode = '') {
      this.loadingCustom = true
      try {
        let {code, data = {}} = await this.$ajax({
          url: '/system/summaryReport/get_custom_info_by_code',
          data: {
            fcustomerCode: customCode,
            page: {
              current: this.currentPage,
              size: 10000
            }
          },
          loadingDom: '.precondition-left'
        })
        if (code === this.SUCCESS_CODE) {
          this.currentPage += 1
          let result = data.records || []
          this.customeres = [...this.customeres, ...result]
        }
      } finally {
        this.loadingCustom = false
      }
    },
    // 获取癌种列表
    async getCancer () {
      let {code, data} = await this.$ajax({
        url: '/system/summaryReport/get_cancer_report_infos',
        data: {
          page: {
            current: 1,
            size: 10000
          }
        },
        loadingDom: '.dialog-wrapper'
      })
      if (code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.cancers = []
        rows.forEach(v => {
          this.cancers.push(v.cancer)
        })
      }
    },
    // 获取对应癌种信息
    async getCancerInfo () {
      let {code, data} = await this.$ajax({
        url: '/system/summaryReport/get_cancer_report_info',
        data: {
          cancer: this.cancer
        },
        method: 'get',
        loadingDom: '.info'
      })
      if (code === this.SUCCESS_CODE) {
        this.carcinomaOne = ''
        this.carcinomaTwo = ''
        this.carcinomaThree = ''
        this.carcinomaFour = ''
        this.cancerTableData = [{
          id: data.id,
          cancer: data.cancer,
          basic: data.basic,
          targetDrug: data.targetDrug,
          otherDrug: data.otherDrug,
          endocrineTherapy: data.endocrineTherapy,
          antiAngiogenesisDrug: data.antiAngiogenesisDrug,
          immunityTherapyTmb: data.immunityTherapyTmb,
          immunityTherapyMsi: data.immunityTherapyMsi,
          immunityTherapyData: data.immunityTherapyData,
          chemotherapy: data.chemotherapy,
          prognosis: data.prognosis,
          genetic: data.genetic,
          description: data.description,
          tumorDynamicMonitor: data.tumorDynamicMonitor,
          drugTargetResult: data.drugTargetResult,
          parpDrugTargetResult: data.parpDrugTargetResult,
          hrdDetectionResult: data.hrdDetectionResult
        }]
        this.cancerInfo = {
          fcustomerCode: data.fcustomerCode,
          conditionalRelation: data.conditionalRelation,
          conditionalOne: data.conditionalOne,
          conditionalTwo: data.conditionalTwo,
          conditionThree: data.conditionThreeList
        }
        if (data.conditionalTwo) {
          let info = JSON.parse(data.conditionalTwo) || {}
          this.carcinomaOne = info.cancer
          this.carcinomaThree = info.readCancer
        }
        this.customerList = data.fcustomerCodeObj
        this.carcinomaTwo = data.conditionalOne ? data.conditionalOne.split(',') : []
        this.carcinomaFour = data.conditionThreeList
      }
    },
    handleProductChange ({row}) {
      if (row.proCode) {
        const product = this.productNames.filter(v => v.label === row.proCode)[0] || {}
        row.proName = product.productName
        row.isFix = true
      }
    },
    handleProductNameChange ({row}) {
      if (row.proName) {
        const product = this.productNames.filter(v => v.value === row.proName)[0] || {}
        row.proCode = product.label
        row.isFix = true
      }
    },
    handleClear ({row}) {
      row.proCode = ''
      row.proName = ''
    },
    // 获取产品报告小结信息
    async getProductInfo () {
      let {code, data} = await this.$ajax({
        url: '/system/summaryReport/get_product_report_info',
        data: {
          cancer: this.cancer,
          productCode: this.productCode,
          productName: this.productName
        },
        method: 'get',
        loadingDom: '.productTable'
      })
      if (code === this.SUCCESS_CODE) {
        this.productTableData = []
        data.forEach((v, index) => {
          this.productTableData.push({
            id: v.id,
            basic: v.basic,
            targetDrug: v.targetDrug,
            proCode: v.proCode,
            otherDrug: v.otherDrug,
            endocrineTherapy: v.endocrineTherapy,
            antiAngiogenesisDrug: v.antiAngiogenesisDrug,
            immunityTherapyTmb: v.immunityTherapyTmb,
            immunityTherapyMsi: v.immunityTherapyMsi,
            immunityTherapyData: v.immunityTherapyData,
            chemotherapy: v.chemotherapy,
            prognosis: v.prognosis,
            genetic: v.genetic,
            description: v.description,
            proName: v.proName,
            tumorDynamicMonitor: v.tumorDynamicMonitor,
            drugTargetResult: v.drugTargetResult,
            parpDrugTargetResult: v.parpDrugTargetResult,
            hrdDetectionResult: v.hrdDetectionResult,
            isFix: false
          })
        })
      }
    },
    /**
     * 获取指定级别的癌种
     * @param type 癌种级别
     */
    async getCancerList (type) {
      let {code, data} = await this.$ajax({
        url: '/system/summaryReport/get_list_cancers_by_cancerclass',
        data: {
          cancerclass: type
        },
        loadingDom: '.dialog-wrapper',
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        data = data || []
        let cancer = []
        data.forEach(v => {
          let item = {
            value: v.cancerTypeName,
            label: v.cancerTypeName
          }
          item.children = v.children.map(vv => {
            return {
              value: vv.cancerTypeName,
              label: vv.cancerTypeName
            }
          })
          cancer.push(item)
        })
        type ? this.conditionalOneList = cancer : this.conditionalCancers = cancer
      }
    },
    // 获取所有的产品名称
    async getAllProductName () {
      let {code, data = []} = await this.$ajax({
        url: '/system/summaryReport/get_all_product_name'
      })
      if (code === this.SUCCESS_CODE) {
        this.productNames = data.map(v => {
          return {
            label: v.productCode,
            value: v.productName,
            productName: v.productName
          }
        }) || []
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  display: flex;
  height: calc(90vh - 140px);
  .category {
    height: 100%;
    width: 240px;
    border: 1px solid #f2f2f2;
    overflow: auto;
    .category-item {
      height: 44px;
      line-height: 44px;
      padding: 5px;
      border: 1px solid #f2f2f2;
      cursor: pointer;
    }
    .category-item-active {
      color: $color;
    }
  }
  .info {
    width: 100%;
    overflow: auto;
    .precondition {
      display: flex;
      justify-content: space-between;
      line-height: 44px;
      padding: 20px;
      .title {
        background: #f2f2f2;
        font-weight: bold;
        height: 44px;
        padding: 0 10px;
      }
      .precondition-left {
        width: 30%;
        height: 288px;
        border: 1px solid #eee;
        overflow: auto;
        .precondition-left-item {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 10px;
          border-bottom: 1px solid #eee;
        }
        .more {
          cursor: pointer;
          color: $color;
          text-align: center;
          font-size: 32px;
          width: 100%;
          height: 44px;
        }
      }
      .precondition-right {
        width: 65%;
        height: 288px;
        .precondition-right-item {
          height: 88px;
        }
      }
    }
    .report-precondition {
      padding: 0 20px;
      display: flex;
    }
  }

}
/deep/ .el-collapse-item__header {
  padding: 0 10px;
  background: #ecf6ff;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 5px;
}
/deep/ .el-dialog {
  margin-top: 5vh !important;
  height: 89vh;
}

</style>
