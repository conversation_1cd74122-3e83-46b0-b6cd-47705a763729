import { myAjax } from '../../util/ajax'

/**
 * 获取探针信息详情
 * @param data
 * @param options
 * @returns {Promise<void>}
 */
export function getProbeInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '',
    data: data,
    ...options
  })
}

/**
 * 弃用探针
 * @param data
 * @param options
 * @returns {Promise<void>}
 */
export function abandonProbe (data, options = {}) {
  return myAjax({
    url: '/system/probe/deprecated_probe',
    data: data,
    ...options
  })
}
