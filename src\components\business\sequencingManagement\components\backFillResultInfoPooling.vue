<template>
  <div>
    <el-form :model="form" ref="form" size="mini" label-suffix=":" inline :rules="rules" label-width="80px">
      <el-form-item label="检测人" prop="detector">
        <el-select v-model.trim="form.detector" filterable multiple collapse-tags clearable placeholder="请选择检测人">
          <el-option v-for="(item, index) in detectorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="复核人" prop="auditor">
        <el-select v-model.trim="form.auditor" filterable clearable multiple collapse-tags placeholder="请选择复核人">
          <el-option v-for="(item, index) in auditorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <vxe-table
      ref="tableRef"
      border
      resizable
      height="300px"
      keep-source
      :data="tableData"
      size="mini"
      :row-style="rowStyle"
      show-overflow
      :edit-rules="validRules"
      :valid-config="{msgMode: 'full'}"
      :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
    >
<!--      <vxe-column type="seq" title="序号" width="60"></vxe-column>-->
      <vxe-column field="samplePosition" show-overflow title="样本孔位" width="120" :edit-render="renderConfig"/>
      <vxe-column field="originSamplePosition" show-overflow title="原始样本定位" width="120" :edit-render="renderConfig"/>

      <vxe-column field="name" show-overflow="tooltip" title="项目名称" width="120"></vxe-column>
      <vxe-column field="sampleName" show-overflow="tooltip" title="实验样本 " width="120"></vxe-column>
      <vxe-column field="geneCode" show-overflow title="核酸/吉因加编号" width="110">
        <template #default="{ row }">
          <span v-if="row.geneCode !== '查看样本详情'">{{row.geneCode}}</span>
          <div v-else class="link" @click="handleDetail(row)">{{row.geneCode}}</div>
        </template>
      </vxe-column>
      <vxe-column field="oldSampleName" show-overflow title="原始样本名称" width="110"></vxe-column>
      <vxe-column field="scheduledAmount" show-overflow title="排单数据量/G" width="120"></vxe-column>
      <vxe-column field="concentration" show-overflow title="浓度（ng/ul)" width="120"></vxe-column>
<!--      <vxe-column field="poolingMultiple" show-overflow title="混合倍数" width="120" :edit-render="renderConfig">-->
<!--      </vxe-column>-->
      <vxe-column field="poolingData" show-overflow title="混合量" width="120" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="poolingVolume" show-overflow title="混合体积" width="120" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="dilutionMultiple" show-overflow title="稀释倍数" width="120" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="dilutionConcentration" show-overflow title="稀释后浓度" width="120" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="poolingTotalData" show-overflow title="混合总量（ng）" width="130" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="poolingTotalVolume" show-overflow title="混合总体积（ul）" width="140" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="poolingName" show-overflow title="pooling文库名称" width="140" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="poolingConcentration" show-overflow title="pooling浓度" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="targetPosition" show-overflow title="目标孔位" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="targetMaterial" show-overflow title="目标耗材" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="taskCode" show-overflow title="任务单编号" width="140"></vxe-column>
    </vxe-table>
    <div class="tips">
      <span style="margin-right: 10px; color: #409EFF">样本总数: {{tableData.length}}</span>
      提交后数据如需修改，请点击【信息变更】，确认继续提交？
    </div>
  </div>
</template>

<script>
import {awaitWrap} from '../../../../util/util'
import {getTestOrCheck, saveResult} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'backFillResultDialog',
  props: {
    info: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number,
      default: null
    }
  },
  mounted () {
    this.init()
  },
  watch: {
    info: {
      handler: function () {
        this.init()
      },
      deep: true
    }
  },
  data () {
    // 混合总量、混合总体积、pooling浓度、pooling文库名称
    const validator = ({ cellValue, row }) => {
      const fields = [
        // 'poolingMultiple',
        'poolingTotalData',
        'poolingTotalVolume',
        'poolingConcentration',
        'poolingName'
      ]
      const flag = fields.some(item => !!row[item])
      if (flag && !cellValue) {
        return new Error('请输入')
      }
    }
    return {
      form: {
        detector: '', // 检测人
        auditor: '' // 审核人
      },
      detectorOptions: [],
      auditorOptions: [],
      tableData: [],
      renderConfig: {name: '$input', props: {clearable: true}},
      rules: {
        detector: [{required: true, message: '请输入检测人', trigger: 'change'}],
        auditor: [{required: true, message: '请输入复核人', trigger: 'change'}]
      },
      validRules: {
        // poolingMultiple: [
        //   {
        //     pattern: /^[0-9]\d*(\.\d)?$/, message: '请输入一位小数', trigger: 'change' },
        //   {
        //     validator: validator, trigger: 'change'
        //   }
        // ],
        poolingData: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ],
        poolingVolume: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ],
        dilutionMultiple: [
          {
            pattern: /^[0-9]\d*(\.\d)?$/, message: '请输入一位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ],
        dilutionConcentration: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ],
        poolingTotalData: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ],
        poolingTotalVolume: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ],
        poolingName: [
          {
            validator: validator, trigger: 'change'
          }
        ],
        poolingConcentration: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {
            validator: validator, trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    init () {
      this.tableData = []
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
      this.getTestOrCheckPerson(1, 'detectorOptions')
      this.getTestOrCheckPerson(2, 'auditorOptions')
      let isHeightLight = false
      let index = 0
      this.info.forEach((v, i) => {
        // 判断是否切换高亮 （pooling名称）
        if (i !== 0 && (v.fresultName !== this.info[i - 1].fresultName)) {
          isHeightLight = !isHeightLight
          index = 0
        }
        index++
        const item = {
          id: v.fid,
          name: v.fprojectName,
          sampleName: v.fsampleName,
          geneCode: this.handleSetGeneCode(v), // 吉因加编号,
          geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
          geneInfo: {
            fgeneCode: v.fgeneCode,
            fnucleateCode: v.fnucleateCode
          },
          fgeneCode: v.fgeneCode,
          oldSampleName: v.foldSampleName,
          nucleateCode: v.fnucleateCode,
          scheduledAmount: v.fsize,
          concentration: v.fconcentration || '',
          poolingMultiple: v.fmixingMultiple || '',
          poolingData: v.fmixingAmount || '',
          poolingVolume: v.fmixingVolume || '',
          dilutionMultiple: v.fdilutionMultiple || '',
          dilutionConcentration: v.fdilutionConcentration,
          poolingTotalData: v.ftotalMix || '',
          poolingTotalVolume: v.ftotalMixingVolume || '',
          poolingName: v.fresultName,
          poolingConcentration: v.fpoolingConcentration || '',
          taskCode: v.ftaskCode,
          isHeightLight: isHeightLight,
          index: index,
          samplePosition: v.fsamplePosition, // 样本孔位
          originSamplePosition: v.foriginSamplePosition, // 原始样本定位
          volume: v.fvolume, // 体积
          targetPosition: v.ftargetPosition, // 目标定位
          targetMaterial: v.ftargetMaterial // 目标耗材
        }
        this.tableData.push(item)
      })
    },
    rowStyle  ({ row, rowIndex }) {
      if (row.isHeightLight) {
        return {
          backgroundColor: '#eee'
        }
      }
      return null
    },
    async getTestOrCheckPerson (roleType, key) {
      const {res} = await awaitWrap(getTestOrCheck({roleType}))
      if (res && res.code === this.SUCCESS_CODE) {
        this[key] = []
        const data = res.data || []
        data.forEach(v => {
          const item = {
            label: v.frealName,
            value: v.fid
          }
          this[key].push(item)
        })
      }
    },
    handleValidForm () {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('表单存在错误，请检查'))
          }
        })
      })
    },
    handleValidate () {
      const $table = this.$refs.tableRef
      return new Promise(async (resolve, reject) => {
        const valid = await $table.fullValidate(true)
        if (valid) {
          this.$message.error('表格存在错误，请检查')
          reject(new Error('表格存在错误，请检查'))
        } else {
          resolve()
        }
      })
    },
    setParams () {
      const list = []
      this.tableData.forEach(v => {
        const item = {
          fid: v.id,
          fprojectName: v.name,
          fsampleName: v.sampleName,
          fgeneCode: v.fgeneCode,
          fnucleateCode: v.nucleateCode,
          foldSampleName: v.oldSampleName, // 原始样本名称
          fsize: v.scheduledAmount,
          fconcentration: v.concentration,
          // fmixingMultiple: v.poolingMultiple,
          fmixingAmount: v.poolingData,
          fmixingVolume: v.poolingVolume,
          fdilutionMultiple: v.dilutionMultiple,
          fdilutionConcentration: v.dilutionConcentration,
          ftotalMix: v.poolingTotalData,
          ftotalMixingVolume: v.poolingTotalVolume,
          fresultName: v.poolingName ? 'PL' + v.poolingName : '',
          fpoolingConcentration: v.poolingConcentration,
          ftaskCode: v.taskCode,
          fsamplePosition: v.samplePosition, // 样本孔位
          foriginSamplePosition: v.originSamplePosition, // 原始样本定位
          ftargetPosition: v.targetPosition, // 目标定位
          ftargetMaterial: v.targetMaterial // 目标耗材
        }
        list.push(item)
      })
      return {
        tsPoolingDataDTOList: list,
        ftestPerson: this.form.detector,
        fcheckPerson: this.form.auditor
      }
    },
    async handleSubmit () {
      try {
        await Promise.all([this.handleValidate(), this.handleValidForm()])
        const params = this.setParams()
        console.log(params.tsPoolingDataDTOList)
        if (params.tsPoolingDataDTOList.some(v => v.fpoolingConcentration === '0')) {
          await this.$confirm('导入结果内存在浓度为0的数据，请确认数据是否正确？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        }
        if (params.fcheckPerson.some(v => params.ftestPerson.findIndex(vv => v === vv) !== -1)) {
          this.$message.error('检测人不能与复核人相同')
          return
        }
        // 获取当前没有填写的数据
        this.tableData.filter(v => {
          const fields = [
            // 'poolingMultiple',
            'poolingTotalData',
            'poolingTotalVolume',
            'poolingConcentration',
            'poolingName'
          ]
          return fields.every(item => !!v[item])
        })
        const {res} = await awaitWrap(saveResult(params, this.type, {showErrorMessageBox: false}))
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('回填成功，请在已完成页面查看回填结果。')
          return true
        } else {
          if (res && res.data) {
            this.$showSequencingErrorDialog({tableData: res.data, isShowButton: false})
          } else {
            this.$message.error(res.message)
          }
        }
      } catch (e) {
        throw new Error(e)
      }
    }
  }
}
</script>

<style scoped>

</style>
