<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="80%"
      @open="handleOpen">
      <div class="structure-container">
        <div class="index-content">
          <div class="row-item">
            <div class="index">
              <p>5'-</p>
              <div v-if="type === 1" class="main-index">
                <p>
                  <input v-model.trim="input[0]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input1[0]}}</span>
                </p>
                <p class="bg-yellow">
                  index1
                  <input v-model.trim="input[1]" v-if="modes === 'write'" class="input-40"/>
                  <span v-if="modes === 'read'" class="index-num">{{input1[1]}}</span>
                  bp
                </p>
                <p>
                  <input v-model.trim="input[2]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input1[2]}}</span>
                </p>
                <p class="bg-blue">
                  indexDNA
                  <input v-model.trim="input[3]" v-if="modes === 'write'" class="input-40"/>
                  <span v-if="modes === 'read'" class="index-num">{{input1[3]}}</span>
                  bp
                <p>
                  <input v-model.trim="input[4]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input1[4]}}</span>
                </p>
              </div>
              <div v-if="type === 2" class="main-index">
                <!--<p>{{input2[0]}}</p>-->
                <p>
                  <input v-model.trim="input[0]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input2[0]}}</span>
                </p>
                <p class="bg-yellow">
                  index1
                  <input v-model.trim="input[1]" v-if="modes === 'write'" class="input-40"/>
                  <span v-if="modes === 'read'" class="index-num">{{input2[1]}}</span>
                  bp
                </p>
                <p>
                  <input v-model.trim="input[2]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input2[2]}}</span>
                </p>
                <p class="bg-blue">
                  indexDNA
                  <input v-model.trim="input[3]" v-if="modes === 'write'" class="input-40"/>
                  <span v-if="modes === 'read'" class="index-num">{{input2[3]}}</span>
                  bp
                </p>
                <p>
                  <input v-model.trim="input[4]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input2[4]}}</span>
                </p>
                <p class="bg-yellow">
                  indexDNA
                  <input v-model.trim="input[5]" v-if="modes === 'write'" class="input-40"/>
                  <span v-if="modes === 'read'" class="index-num">{{input2[5]}}</span>
                  bp
                </p>
                <p>
                  <input v-model.trim="input[6]" v-if="modes === 'write'" class="input-200"/>
                  <span v-if="modes === 'read'">{{input2[6]}}</span>
                </p>
              </div>
              <p>-3’</p>
            </div>
          </div>
        </div>
      </div>
      <div class="description">
        <p class="title">说明</p>
        <p>1、若为散样测序，仅支持index位数为10的文库样本，若为index位数不同文库样本需包run测序</p>
        <p>2、散样测序需求，单个子文库样本数据量不能低于6G</p>
        <p>3、不支持单/双index混合文库</p>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
export default {
  name: 'entryComputerLibraryInfoLibraryStructureExampleFileDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: Number, // 1：单Index文库  2：双Index文库
    mode: String, // write read
    pdata: Array // 已经输入的数组
  },
  data () {
    return {
      input1: ['TGTGAGCCAAGGAGTTG', '10', 'TTGTCTTCCTAAGACCGCTTGGCCTCCTACTT', '250', 'AAGTCGGATCGCCATCTCGTTC'], // 输入的文案
      input2: ['TGTGAGCCAAGGAGTTG', '10', 'TTGTCTTCCTAAGACCGCTTGGCCTCCTACTT', '250', 'AAGTCGGATCGCCATCTCGTTC', '10', 'AACTGCGACGTACTGAGA'], // 输入的文案
      input: [],
      modes: 'read',
      title: ''
    }
  },
  methods: {
    handleOpen () {
      if (this.mode === 'write') {
        this.title = '录入文库结构'
        this.input = this.pdata.length === 0 ? new Array(this[`input${this.type}`].length).fill('') : this.pdata
      }
      if (this.mode === 'read') {
        this.title = '文库填写示例'
      }
      this.$nextTick(() => {
        this.modes = this.mode
      })
    },
    handleConfirm () {
      let hasAllWrite = this.input.every(v => {
        return v && v.length < 200
      })
      if (!hasAllWrite) {
        this.$message.error('请填写完整并且每项的长度不超过200')
        return
      }
      this.$emit('dialogConfirmEvent', {
        type: this.type,
        input: this.input
      })
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
  input{
    background: transparent;
    border: none;
    &:focus{
      border: none;
      outline: none;
    }
  }
  .input-200{
    width: 200px;
  }
  .input-40{
    width: 40px;
    border-bottom: 1px solid #333;
    text-align: center;
    color: red;
  }
  // 文库结构的样式
  .structure-container{
    padding: 10px;
    font-size: 13px;
    .bg-gray{
      background: #D7D7D7;
    }
    .bg-yellow{
      background: #f59a23;
    }
    .bg-blue{
      background: #1890ff;
    }
    .index-content{
      margin-top: 10px;
      .row-item{
        display: flex;
        align-items: center;
        p{
          line-height: 25px;
          height: 25px;
        }
        .index{
          display: flex;
          color: #333;
          max-width: 100%;
          overflow-x: auto;
          margin: auto;
          .main-index{
            display: flex;
            margin: 0 10px;
            @extend .bg-gray;
            p{
              padding: 0 8px;
              .index-num{
                padding: 0 3px;
                color: red;
                border-bottom: 1px solid #333;
              }
            }
          }
        }
      }
    }
  }
  .description{
    .title{
      font-weight: 600;
    }
    p{
      line-height: 25px;
      letter-spacing: .5px;
    }
  }
</style>
