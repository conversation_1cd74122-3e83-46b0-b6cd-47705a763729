<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="拆分任务"
      width="750px">
      <el-table
        ref="splitTable"
        :data="tableData"
        class="reservationTable"
        height="400"
        style="width: 100%"
        @select="handleSelectTable"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45"></el-table-column>
        <el-table-column prop="patientCode" label="患者编号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleCode" label="样本编号" width="110" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sex" label="性别" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="idCard" label="身份证" min-width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button :loading="loading" size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
// import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  name: 'patientSplitSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    tableData: {
      type: Array,
      default () { return [] }
    }
  },
  data () {
    return {
      loading: false,
      selectedRows: new Map()
    }
  },
  methods: {
    // 点击行
    handleRowClick (row) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      let id = row.id
      if (!this.selectedRows.has(id)) {
        this.$refs.splitTable.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.splitTable.toggleRowSelection(row, !this.selectedRows.has(id))
      this.selectedRows.has(id)
        ? this.selectedRows.delete(id)
        : this.selectedRows.set(id, row)
    },
    // 确认
    handleConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择单个样例进行拆分')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.loading = true
      this.$ajax({
        url: '/sample/patient/split_patient_id',
        data: {
          patientId: row.patientCode,
          sampleNum: row.sampleCode,
          idcard: row.realData.idCard
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('拆分成功')
          this.handleClose()
          this.$emit('dialogConfirm')
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleClose () {
      this.visible = false
      this.selectedRows.clear()
    }
  }
}
</script>

<style scoped>

</style>
