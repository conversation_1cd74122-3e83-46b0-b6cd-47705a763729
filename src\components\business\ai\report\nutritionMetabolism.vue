<template>
  <div class="desc">
    <div class="card-wrapper" style="margin-top: 20px">
      <h4>营养素代谢</h4>
      <el-table
        :data="geneticMutationMap.nutrition"
        border>
        <template v-for="(item, index) in map[1]" >
          <el-table-column :prop="item.fitem" :label="item.fitem" :key="index"></el-table-column>
        </template>
      </el-table>
    </div>

    <div class="card-wrapper" style="margin-top: 20px">
      <h4>矿物质需求</h4>
      <el-table
        :data="geneticMutationMap.mineral"
        border>
        <template v-for="(item, index) in map[2]" >
          <el-table-column :prop="item.fitem" :label="item.fitem" :key="index"></el-table-column>
        </template>
      </el-table>
    </div>

    <div class="card-wrapper" style="margin-top: 20px">
      <h4>体质特征</h4>
      <el-table
        :data="geneticMutationMap.constitutive"
        border>
        <template v-for="(item, index) in map[3]" >
          <el-table-column :prop="item.fitem" :label="item.fitem" :key="index"></el-table-column>
        </template>
      </el-table>
    </div>

    <div class="card-wrapper" style="margin-top: 20px">
      <h4>代谢能力</h4>
      <el-table
        :data="geneticMutationMap.metabolic"
        border>
        <template v-for="(item, index) in map[4]" >
          <el-table-column :prop="item.fitem" :label="item.fitem" :key="index"></el-table-column>
        </template>
      </el-table>
    </div>

    <div class="card-wrapper" style="margin-top: 20px">
      <h4>环境敏感度</h4>
      <el-table
        :data="geneticMutationMap.environment"
        border>
        <template v-for="(item, index) in map[5]" >
          <el-table-column :prop="item.fitem" :label="item.fitem" :key="index"></el-table-column>
        </template>
      </el-table>
    </div>

  </div>
</template>

<script>

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      geneticMutationMap: {},
      map: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_nutrient_metabolism_list',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        this.geneticMutationMap.nutrition = this.forMateData(data[1])
        this.geneticMutationMap.mineral = this.forMateData(data[2])
        this.geneticMutationMap.constitutive = this.forMateData(data[3])
        this.geneticMutationMap.metabolic = this.forMateData(data[4])
        this.geneticMutationMap.environment = this.forMateData(data[5])
        this.map = data
      }
    },
    forMateData (data) {
      data = data || []
      let result = {}
      data.forEach(v => {
        result[v.fitem] = v.fforecastResult
      })
      return [result]
    }
  }
}
</script>

<style scoped>
/deep/ .el-descriptions-item__cell {
  width:  212px
}
</style>
