<template>
  <el-dialog
    title="测序上机配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    width="900px"
    @open="handleOpen">
    <div style="margin-bottom: 10px;">任务单号 {{taskCode}}</div>
    <div style="margin-bottom: 10px;">请配置任务:</div>
    <div class="dialog-form">
      <el-form ref="form" :model="form" inline size="mini" label-suffix=":" :rules="rules" label-width="130px">
        <el-form-item label="机器号" prop="machineNumber">
          <el-select v-model.trim="form.machineNumber" class="form-width" placeholder="请选择">
            <el-option v-for="item in matchingNumber" :key="item" :label="item" :value="item"></el-option>
          </el-select>
<!--          <el-cascader-->
<!--            v-model.trim="form.machineNumber"-->
<!--            :options="matchingNumber"-->
<!--            :show-all-levels="false"-->
<!--            collapse-tags-->
<!--            class="form-width"-->
<!--            clearable-->
<!--            size="mini"-->
<!--          ></el-cascader>-->
        </el-form-item>
        <el-form-item label="边" prop="side">
          <el-select v-model.trim="form.side" class="form-width" placeholder="请选择">
            <el-option v-for="item in sideList" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="FC号" prop="fcNumber">
          <el-input v-model.trim="form.fcNumber" maxlength="" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="测序模式" prop="sequencingPattern">
          <el-select v-model.trim="form.sequencingPattern" class="form-width" placeholder="请选择">
            <el-option v-for="item in sequenceMode" :key="item" :label="item" :value="item"></el-option>
          </el-select>
<!--          <el-cascader-->
<!--            v-model.trim="form.sequencingPattern"-->
<!--            :options="sequenceMode"-->
<!--            :show-all-levels="false"-->
<!--            collapse-tags-->
<!--            class="form-width"-->
<!--            clearable-->
<!--            size="mini"-->
<!--          ></el-cascader>-->
        </el-form-item>
        <el-form-item label="Barcode文件" prop="barcode">
          <el-select v-model.trim="form.barcode" class="form-width" placeholder="请选择">
            <el-option v-for="item in barcode" :key="item" :label="item" :value="item"></el-option>
          </el-select>
<!--          <el-cascader-->
<!--            v-model.trim="form.barcode"-->
<!--            :options="barcode"-->
<!--            :show-all-levels="false"-->
<!--            collapse-tags-->
<!--            class="form-width"-->
<!--            clearable-->
<!--            size="mini"-->
<!--          ></el-cascader>-->
        </el-form-item>
        <el-form-item label="芯片负压值(Mpa)" prop="chipNegativePressure">
          <el-input v-model.trim="form.chipNegativePressure" maxlength="50" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="自定义引物" prop="customPrimer">
          <el-select v-model.trim="form.customPrimer" class="form-width" placeholder="请选择">
            <el-option v-for="(item, index) in booleanList" :key="item" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="自动清洗" prop="autoClean">
          <el-select v-model.trim="form.autoClean" class="form-width" placeholder="请选择">
            <el-option v-for="(item, index) in booleanList" :key="item" :label="item" :value="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'
import {awaitWrap} from '../../../../../util/util'
import {getConfig, saveConfig} from '../../../../../api/sequencingManagement/makeDNBApi'

export default {
  name: 'setComputerConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    taskCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      form: {
        machineNumber: '',
        side: '',
        fcNumber: '',
        barcode: '',
        sequencingPattern: '',
        chipNegativePressure: '',
        customPrimer: '',
        autoClean: ''
      },
      barcode: [],
      matchingNumber: [],
      sequenceMode: [],
      sideList: ['A', 'B', 'C', 'D'],
      booleanList: ['否', '是'],
      loading: false,
      rules: {
        machineNumber: [{ required: true, message: '请选择', trigger: 'blur' }],
        side: [{ required: true, message: '请选择', trigger: 'blur' }],
        fcNumber: [{ required: true, message: '请输入', trigger: 'blur' }],
        sequencingPattern: [{ required: true, message: '请选择', trigger: 'blur' }],
        barcode: [{ required: true, message: '请选择', trigger: 'blur' }],
        chipNegativePressure: [{ required: true, message: '请选择', trigger: 'blur' }],
        customPrimer: [{ required: true, message: '请输入', trigger: 'blur' }],
        autoClean: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.getConfig()
      })
    },
    async getConfig () {
      const {res} = await awaitWrap(getConfig({
        fkeyList: ['MACHINE_NUMBER', 'SEQUENCE_MODE', 'BARCODE']
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.barcode = []
        this.matchingNumber = []
        this.sequenceMode = []
        this.handleSetGroup(data['BARCODE'] || [], this.barcode)
        this.handleSetGroup(data['MACHINE_NUMBER'] || [], this.matchingNumber)
        this.handleSetGroup(data['SEQUENCE_MODE'] || [], this.sequenceMode)
        this.barcode = [...new Set([...this.barcode])] || []
        this.matchingNumber = [...new Set([...this.matchingNumber])] || []
        this.sequenceMode = [...new Set([...this.sequenceMode])] || []
      }
    },
    handleSetGroup (data = [], option) {
      // const map = new Map()
      data.forEach(v => {
        if (v.fisDelete === 0) {
          option.push(v.fvalue)
          // map.has(v.fproductionArea)
          //   ? map.get(v.fproductionArea).push({ label: v.fvalue, value: v.fvalue })
          //   : map.set(v.fproductionArea, [{ label: v.fvalue, value: v.fvalue }])
        }
      })
      // map.forEach((list, area) => {
      //   const item = {
      //     label: area,
      //     value: area,
      //     children: list
      //   }
      //   option.push(item)
      // })
    },
    setParams () {
      return {
        ftaskCode: this.taskCode,
        fmachineNumber: this.form.machineNumber.length > 1 ? this.form.machineNumber[1] : '',
        fside: this.form.side,
        ffc: this.form.fcNumber,
        fsequenceMode: this.form.sequencingPattern.length > 1 ? this.form.sequencingPattern[1] : '',
        fbarcode: this.form.barcode.length > 1 ? this.form.barcode[1] : '',
        fchipPressureValue: this.form.chipNegativePressure,
        fcustomItem: this.form.customPrimer,
        fautomaticCleaning: this.form.autoClean
      }
    },
    async handleConfirm () {
      await this.handleValidForm()
      const params = this.setParams()
      let {res} = await awaitWrap(saveConfig(params))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('配置成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
    }
  }
}
</script>

<style scoped>
</style>
