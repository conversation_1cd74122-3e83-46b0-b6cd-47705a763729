<template>
  <div style="margin-bottom: 20px;">
    <div style="border: 1px solid #f2f2f2;padding: 10px;">
      <h4>{{title}}</h4>
      <el-table
        ref="table"
        :data="tableData"
        :row-style="handleRowStyle"
        class="dataFilterTable"
      >
        <el-table-column prop="type" label="类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="genusName" label="属" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.realData.genusName">{{scope.row.genusName}}</span>
            <br v-if="scope.row.realData.genusName && scope.row.realData.genusLatin"/>
            <i v-if="scope.row.realData.genusLatin">{{scope.row.genusLatin}}</i>
          </template>
        </el-table-column>
        <el-table-column prop="genusReadsNumber" label="序列(属)" show-overflow-tooltip></el-table-column>
        <el-table-column prop="speciesName" label="种" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.realData.speciesName">{{scope.row.speciesName}}</span>
            <br v-if="scope.row.realData.speciesName && scope.row.realData.speciesLatin"/>
            <i v-if="scope.row.realData.speciesLatin">{{scope.row.speciesLatin}}</i>
          </template>
        </el-table-column>
        <el-table-column prop="speciesReadsNumber" label="序列(种)" show-overflow-tooltip></el-table-column>
        <el-table-column prop="coverage" label="覆盖度" show-overflow-tooltip></el-table-column>
        <el-table-column prop="speciesRelativeAbundance" label="相对丰度" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String
    },
    tableData: {
      type: Array
    }
  },
  methods: {
    handleRowStyle ({row}) {
      console.log(row)
      return row.report === 'Y' ? {color: 'red'} : {}
    }
  }
}
</script>

<style scoped></style>
