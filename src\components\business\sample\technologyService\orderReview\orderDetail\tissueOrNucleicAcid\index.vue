<template>
  <div class="page">
    <nav class="operateBar bg-white model-operate" style="height: 56px;">
      <div class="operate">
        <div style="font-size: 13px;">
          <span class="title">组织或核酸{{readOnly ? '订单详情' : '订单录入'}}</span>
          <span v-if="orderCode" style="margin-left: 20px">订单编号：{{orderCode}}</span>
        </div>
        <div v-if="!isDetail">
          <template v-if="readOnly">
            <template v-if="approvalStatus === 2">
              <el-button v-if="$setAuthority('018001006', 'buttons')" size="mini" @click="handleDownload">下载到样表</el-button>
              <el-button v-if="$setAuthority('018001007', 'buttons') && !projectInfo.isAllSampleConfirm" size="mini" type="primary" @click="importDialogVisible = true">导入到样</el-button>
            </template>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <!--审核状态为未审核时展示-->
            <template v-if="approvalStatus === 1">
              <el-button v-if="$setAuthority('018001003', 'buttons')" size="mini" @click="handleReject">驳回</el-button>
              <el-button v-if="$setAuthority('018001004', 'buttons')" size="mini" type="primary" @click="handleEdit">修改</el-button>
              <el-button v-if="$setAuthority('018001005', 'buttons')" size="mini" type="primary" @click="handlePass">通过</el-button>
            </template>
            <el-button
              :loading="downloadOrderLoading"
              v-if="$setAuthority('018001001', 'buttons')"
              size="mini"
              type="primary"
              @click="handleDownloadOrder">{{downloadOrderLoading ? '正在下载' : '订单下载'}}</el-button>
          </template>
          <template v-else>
            <el-button size="mini" @click="handleBack">返回</el-button>
            <el-button size="mini" type="primary" @click="handleSubmit(1)">保存</el-button>
          </template>
        </div>
      </div>
    </nav>
    <div style="height: 55px"></div>
    <div class="info">
      <!--基本信息-->
      <entry-base-info
        ref="baseInfo"
        :read-only="readOnly"
        :project-data="projectInfo"
        :courier-data="courierInfo"
        :operate-data="operateInfo"
      />
      <!--样本信息-->
      <entry-library-info
        ref="libraryInfo"
        :read-only="readOnly"
        :order-id="orderId"
        :page-type="pagetype"
        :online-form="detectInfo"
      />
    </div>
    <!--驳回-->
    <reject-dialog
      :pvisible.sync="rejectDialogVisible"
      :order-id="orderId"
      :order-code="orderCode"
      :project-info="projectInfo"
      page-type="2"
      @dialogConfirmEvent="handleWindowClose"/>
    <sample-confirm-import-dialog
      :pvisible.sync="importDialogVisible"
      :order-number="orderCode"
      @dialogConfirmEvent="handleRefresh"/>
  </div>
</template>

<script>

// import xx form 'xxx'
import entryBaseInfo from '../enterBaseInfo'
import entryLibraryInfo from './entryLibraryInfo'
import rejectDialog from '../rejectDialog'
import sampleConfirmImportDialog from '../../../sampleConfirm/sampleConfirmImportDialog'
import util from '../../../../../../../util/util'
import constants from '../../../../../../../util/constants'
import qs from 'qs'
// import Cookies from 'js-cookie'
export default {
  name: `index`,
  components: {
    entryBaseInfo,
    entryLibraryInfo,
    rejectDialog,
    sampleConfirmImportDialog
  },
  mounted () {
    if (this.orderId) {
      this.getOrderDetail(this.orderId)
    }
  },
  watch: {
    libraryOperatingData: {
      handler: function (newVal) {
        if (newVal) {
          this.readOnly = newVal.type === 2 || newVal.type === 3
          this.isDetail = newVal.type === 3
          this.orderId = newVal.orderId
          this.orderCode = newVal.code
          this.approvalStatus = newVal.status
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    userInfo () {
      return this.$store.getters.getValue('userInfo')
    },
    libraryOperatingData () {
      let data = {}
      if (this.$route.query.orderCode) {
        let {type, orderId, orderCode, status} = this.$route.query
        type = atob(decodeURIComponent(type)) * 1
        status = atob(decodeURIComponent(status)) * 1
        let code = atob(decodeURIComponent(orderCode))
        orderId = atob(decodeURIComponent(orderId))
        data = {
          type, orderId, code, status
        }
        this.$store.commit({
          type: 'old/setValue',
          category: 'libraryOperatingData',
          libraryOperatingData: {
            type: type, // type 1编辑 2 只读
            orderId: orderId,
            status: status,
            code: code,
            name: 'lims'
          }
        })
      }
      return this.$store.getters.getValue('libraryOperatingData') || data
    }
  },
  data () {
    return {
      readOnly: true,
      importDialogVisible: false,
      downloadOrderLoading: false,
      isDetail: false,
      orderCode: '', // 订单编号
      orderId: '',
      approvalStatus: '', // 审核状态
      projectInfo: {}, // 项目信息
      courierInfo: {}, // 快递信息
      operateInfo: {},
      detectInfo: {}, // 检测信息
      source: null,
      pagetype: 3, // 1: illumina 2:mgi 3：组织核酸样本信息
      rejectDialogVisible: false // 驳回visible
    }
  },
  methods: {
    // 刷新页面
    handleRefresh () {
      this.$nextTick(() => {
        this.readOnly = true
        this.$refs.libraryInfo.getData()
        this.getOrderDetail(this.orderId)
      })
    },
    // 下载订单
    handleDownloadOrder () {
      let ids = [this.orderId]
      this.downloadOrderLoading = true
      this.$ajax({
        url: '/order/download_order',
        data: {
          orderIds: ids.toString()
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadOrderLoading = false
      })
    },
    // 下载
    handleDownload () {
      this.$ajax({
        url: '/sample/confirm/download_confirm_template',
        method: 'get',
        data: {
          t7OrderCode: this.orderCode
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.loading = false
      })
    },
    handleClose () {
      util.closePage()
    },
    // 校验
    validForm () {
      return Promise.all([this.$refs.baseInfo.validForm(), this.$refs.libraryInfo.validForm()])
    },
    // 提交 status 0 暂存草稿 1 提交
    handleSubmit (status) {
      let baseInfo = this.$refs.baseInfo
      if (status === 0) {
        if (!baseInfo.projectInfo.projectCode) {
          this.$message.error('选择项目编号后才能暂存')
          return
        }
        let data = this.setData(status)
        console.log(data, status)
        this.submit(data)
      } else if (status === 1) {
        this.validForm().then(() => {
          let data = this.setData(status)
          this.submit(data, status)
        }).catch((e) => {
          console.log(e)
          if (e) {
            this.$message.error(e)
          }
        })
      }
    },
    // 提交
    submit (data, status) {
      this.$ajax({
        url: '/order/modify_order_info',
        data: data,
        loadingDom: '.page',
        loadingObject: {
          lock: true,
          text: '正在提交',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('提交成功')
          if (status === 1) {
            // setTimeout(() => {
            //   util.closePage()

            // }, 1000)
            this.readOnly = true
            this.getOrderDetail(this.orderId)
            // setTimeout(() => {
            //   const info = document.getElementsByClassName('info')
            //   console.log(info[0], 'info')
            //   info[0].scrollIntoView()
            // }, 0)
          } else {
            // 暂存草稿成功后会返回id
            this.$store.commit({
              type: 'old/setValue',
              category: 'libraryOperatingData',
              libraryOperatingData: {
                type: 1,
                orderId: res.data.orderId
              }
            })
            this.childOrderId = res.data.orderSubId
            this.$nextTick(() => {
              this.$refs.libraryInfo.getData()
            })
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 设置提交的数据
    setData (status) {
      // 子组件的实例
      let baseInfo = this.$refs.baseInfo // 基本信息
      let libraryInfo = this.$refs.libraryInfo // 文库信息
      let p = baseInfo.projectInfo // 缩写，项目信息
      let c = baseInfo.courierInfo // 缩写，快递信息
      let detect = libraryInfo.detectInfo // 检测信息
      let sample = libraryInfo.sampleInfoTable // 寄送样本信息
      // 提交的数据
      let data = {}
      data.cosOrder = {
        fid: this.orderId,
        ftype: this.pagetype,
        fprojectCode: p.projectCode,
        fprojectName: p.projectName,
        fhospital: p.unit,
        fcustomerName: p.customer,
        fconcatPhone: p.phone,
        fsellerName: p.sale,
        fsendTime: p.sendDate,
        fsendAddr: p.address,
        femail: p.email,
        farea: p.area,
        fsellerPhone: p.salePhone,
        fsellerEmail: p.saleEmail,
        fsampleNumber: p.sampleCount,
        fexpress: c.courierCompany,
        fotherExpress: c.otherCourierCompany,
        fexpressCode: c.trackingNum,
        fotherTransport: c.otherTransportMethod,
        fisOtherTransportType: c.transportMethod === '其他' ? 1 : 0,
        ftransportType: c.transportMethod,
        fstatus: status
      }
      // 前端数据存放
      data.cosOrder.ffrontData = JSON.stringify({
        projectInfo: p,
        courierInfo: c,
        detectInfo: detect
      })
      data.cosOrderTissue = {
        fid: this.childOrderId,
        fisAutoDetect: detect.automaticDetection,
        fisAbleToUsedOut: detect.samplesAvailable,
        fnote: detect.notes,
        fdeliverType: detect.deliveryMethod,
        fdeliveryAddr: detect.hardDriveShippingAddress
      }
      let sampleMap = new Map()
      sample.forEach(v => {
        let item = {
          fid: v.fid, // 新增没有，修改的时候后台会传过来
          fcode: v.fsampleCode, // 新增没有，修改的时候后台会传过来
          ftype: this.pageType,
          fgeneCode: v.geneplusNum,
          fname: v.sampleName,
          fspecies: v.species, // 物种
          fsampleType: v.sampleType,
          ftissueSampleType: v.tissueSampleType,
          ftissuePart: v.tissuePart,
          ftissueSampleState: v.tissueSampleStatus,
          fextractType: v.extractionType,
          fnucleicAcidSampleType: v.nucleicAcidSampleType,
          fnucleicAcidSampleState: v.nucleicAcidSampleStatus,
          flibType: v.libraryType,
          fdetectType: v.detectType,
          fsequenceType: v.tactics,
          finstrumentType: v.sequencingPlatform, // 测序平台
          fdataSize: v.dataNum,
          fexperimentalLink: v.labEnvironment,
          fnote: v.notes
        }
        sampleMap.set(v.sampleName, item)
      })
      data.cosSample = [...sampleMap.values()]
      return data
    },
    // 获取订单详情
    getOrderDetail (orderId, isCopyInfo = false) {
      this.$ajax({
        url: '/order/get_order_detail',
        data: {
          orderId: orderId,
          type: this.pagetype
        },
        loadingDom: '.page'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.approvalStatus = res.data.fstatus
          let data = JSON.parse(res.data.ffrontData)
          let dataOrder = data.projectInfo // 基础信息
          let courierInfo = data.courierInfo // 快递信息
          let detectInfo = data.detectInfo // 检测信息
          this.source = res.data.fsource
          this.projectInfo = {
            projectCode: dataOrder.projectCode,
            projectName: dataOrder.projectName,
            unit: dataOrder.unit,
            customer: dataOrder.customer,
            phone: dataOrder.phone,
            sale: dataOrder.sale,
            salePhone: dataOrder.salePhone,
            saleEmail: dataOrder.saleEmail,
            sampleCount: dataOrder.sampleCount, // 样本数量
            sendDate: dataOrder.sendDate,
            address: dataOrder.address,
            email: dataOrder.email,
            area: dataOrder.area,
            fprojectManagerEmail: res.data.fprojectManagerEmail,
            isAllSampleConfirm: res.data.fisAllSampleConfirm
          }
          if (!isCopyInfo) {
            this.courierInfo = {
              courierCompany: courierInfo.courierCompany,
              otherCourierCompany: courierInfo.otherCourierCompany,
              trackingNum: courierInfo.trackingNum,
              transportMethod: courierInfo.transportMethod,
              otherTransportMethod: courierInfo.otherTransportMethod
            }
            // this.orderCode = res.data.order.fcode
          }
          this.detectInfo = {
            automaticDetection: detectInfo.automaticDetection,
            samplesAvailable: detectInfo.samplesAvailable,
            fexperimentType: res.data.fexperimentType,
            notes: detectInfo.notes,
            deliveryMethod: detectInfo.deliveryMethod,
            tactics: detectInfo.tactics,
            sequencingPlatform: detectInfo.sequencingPlatform,
            hardDriveShippingAddress: detectInfo.hardDriveShippingAddress
          }
        } else {
          this.$confirm(res.message, '提示', {
            confirmButtonText: '重新加载',
            cancelButtonText: '关闭页面',
            type: 'error'
          }).then(() => {
            this.getOrderDetail(this.orderId)
          }).catch(() => {
            util.closePage()
          })
        }
      })
    },
    // 通过审核
    handlePass () {
      this.$confirm('通过后无法撤回，确认审核通过吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/order/pass_order',
          data: {
            orderId: this.orderId
          },
          method: 'get',
          page: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$store.commit({
              type: 'old/setValue',
              category: 'libraryOperatingData',
              libraryOperatingData: {
                type: 2, // type 1编辑 2 只读
                orderId: this.orderId,
                status: 2,
                code: this.orderCode,
                name: 'lims'
              }
            })
            this.$message.success('订单审核通过，可在此页面进行到样！')
            this.getOrderDetail(this.orderId)
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 驳回
    handleReject () {
      console.log('驳回')
      this.rejectDialogVisible = true
    },
    // 关闭弹窗
    handleWindowClose () {
      setTimeout(() => {
        util.closePage()
      }, 1000)
    },
    // 修改
    handleEdit () {
      console.log(this.source)
      if (this.source !== 1) {
        this.$message.error('擎科来源的订单请在擎科系统修改！')
        return
      }
      this.$ajax({
        url: '/order/get_order_status',
        data: {
          orderId: this.orderId
        },
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          if (res.data === 1) {
            this.handleToTechnologyService()
          } else {
            this.$message.error('该订单状态已变更，请刷新页面！')
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 跳转科服
    handleToTechnologyService () {
      let orderInfo = {
        isLims: true,
        orderCode: this.libraryOperatingData.code,
        orderId: this.libraryOperatingData.orderId,
        email: `${this.userInfo.emailPrefix}@geneplus.org.cn`
      }
      window.location.href = constants.IFRAME_URL +
        '/entryTissueOrder?' +
        qs.stringify(orderInfo)
      // let tokenInfo = {
      //   token: Cookies.get('x-lims-token')
      // }
      // this.$jsonp(constants.IFRAME_URL + '/geneplus-customer-order/cos/system/add_cookie?' + qs.stringify(tokenInfo), {}).then((res) => {
      //
      // }).catch(err => {
      //   console.log(err)
      // })
    },
    // 返回
    async handleBack () {
      await this.$confirm('退出修改后，您修改的内容是不会被保存的，确定退出吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.readOnly = true
      this.getOrderDetail(this.orderId)
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-scrollbar__wrap{
  overflow-x: hidden;
}
.page{
  width: 90%;
  min-width:1000px;
  margin: auto;
  padding: 10px 20px;
}
.title{
  margin-right: 30px;
  font-size: 20px;
  font-weight: 600;
}
.model-operate {
  position: fixed;
  top: 0;
  left: 0;
  background: #fff;
  width: 100vw;
  z-index: 999;
  .operate {
    width: 90%;
    min-width:1000px;
    margin: auto;
    display: flex;
    justify-content: space-between;
  }
}
</style>
