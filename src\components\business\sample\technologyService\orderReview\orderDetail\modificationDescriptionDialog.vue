<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        v-if="visible"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template v-if="step === 1">
          <el-form-item label="修改说明" prop="notes">
            <el-input
              v-model.trim="form.notes"
              :rows="3"
              type="textarea"
              maxlength="150"
              show-word-limit
              class="form-width"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="图片说明">
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :file-list="form.picList"
              :headers="headers"
              :on-success="handleOnSuccess"
              :on-error="handleOnError"
              :on-progress="handleProgress"
              :before-upload="handleBeforeUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              multiple
              accept="image/jpg,image/jpeg,image/png"
              list-type="picture-card">
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img :src="dialogImageUrl" width="100%" alt="">
            </el-dialog>
          </el-form-item>
        </template>
        <template v-else>
          <div class="email-content">
            <el-form-item label="邮件标题" prop="emailTitle">
              <el-input
                v-model.trim="form.emailTitle"
                maxlength="150"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="邮件正文" prop="emailContent">
              <div class="form-width">
                <edit
                  :value="form.emailContent"/>
              </div>
            </el-form-item>
            <el-form-item label="附件文件">
              <el-upload
                :action="uploadUrl"
                :file-list="form.attachFile"
                :headers="headers"
                :on-remove="handleRemove"
                :on-error="handleOnError"
                class="upload-demo"
                multiple>
                <el-button size="mini" icon="el-icon-plus">添加附件</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item label="收件邮箱" prop="inbox">
              <el-input
                v-model.trim="form.inbox"
                :rows="2"
                maxlength="150"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="抄送邮箱" prop="sendEmail">
              <el-input
                v-model.trim="form.sendEmail"
                maxlength="150"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
          </div>
        </template>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="onProgress" v-if="step === 1" size="mini" type="primary" @click="handleNext">下一步</el-button>
        <el-button v-else size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../../util/mixins'
import constants from '../../../../../../util/constants'
import Cookies from 'js-cookie'
import util from '../../../../../../util/util'

export default {
  name: `modificationDescriptionDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    /**
     * pageType: 文库类型：1: illumina 2:mgi 3：组织核酸样本信息
     * projectInfo: 项目信息
     * */
    pageType: {
      type: Number || ''
    },
    projectInfo: {
      type: Object || {}
    }
  },
  computed: {},
  data () {
    return {
      title: '修改',
      step: 1, // 步骤
      form: {
        notes: '', // 修改说明
        picList: [], // 图片数明
        emailTitle: '', // 邮件标题
        emailContent: '', // 邮件正文
        attachFile: [], // 附件文件
        inbox: '', // 收件邮箱
        sendEmail: '' // 寄件邮箱
      },
      headers: {
        token: Cookies.get('token')
      },
      uploadUrl: constants.JS_CONTEXT + '/covid/manage/transport/upload_image',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false,
      linkUrl: 'http://*************:9900/entryIlluminaLibraryOrder',
      rules: {
        notes: [
          {required: true, message: '请输入', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      console.log(this.projectInfo, this.pageType, this.form, 'form')
      this.step = 1
      const projectInfo = this.projectInfo || {}
      const today = util.dateFormatter(new Date().getTime(), false)
      this.form = {
        notes: '', // 修改说明
        picList: [
          {
            name: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            onlineUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
          },
          {
            name: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            onlineUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
          },
          {
            name: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            onlineUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
          },
          {
            name: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            onlineUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
          },
          {
            name: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            onlineUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
          }
        ], // 图片数明
        emailTitle: `【订单修改通知】- ${projectInfo.projectCode} - ${projectInfo.projectName} - ${today}`, // 邮件标题
        emailContent: '', // 邮件正文
        attachFile: [], // 附件文件
        inbox: projectInfo.email, // 收件邮箱
        sendEmail: projectInfo.saleEmail // 寄件邮箱
      }
    },
    // 下一步
    handleNext () {
      this.title = '邮件信息确认'
      this.step = 2
      let imgList = `${this.form.picList.map(item => {
        return `<img src="${item.onlineUrl}" style="width: 100px; height: 100px; margin: 2px;" />`
      }).join('')}`
      this.form.emailContent =
      `您好，<br/>订单由于以下原因有部分内容被修改，请知悉。<br/>修改说明：${this.form.notes}
       <div style="display: flex;flex-wrap: wrap;">
          ${imgList}
        </div>
        可点击链接（科服-该订单详情网址）查看。<br/>祝好！`
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let oldName = ''
          if (this.pdata) oldName = this.pdata.sampleName + '-' + this.pdata.subLibraryName
          this.$emit('dialogConfirmEvent', this.form, this.rowIndex, oldName)
        }
      })
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      let name = file.name
      if (!/\.(jpg|png|jpeg)$/.test(name)) {
        this.$message.error('只能上传jpg、png或jpeg的图片')
        return false
      }
      if (file.size > 1024 * 1024 * 50) {
        this.$message.error('文件不能大于50M，请重新上传')
        return false
      }
      return true
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.onProgress = false
      if (res.success) {
        file.onlineUrl = res.data.url
      }
      this.form.picList = [...fileList]
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    handleRemove (file, fileList) {
      this.form.picList = [...fileList]
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.form-width{
  width: 600px;
}
.email-content {
  padding-top: 15px;
  border: 1px solid #efefef;
}
.img{
  width: 150px;
  height: 150px;
  margin: 5px;
}
</style>
