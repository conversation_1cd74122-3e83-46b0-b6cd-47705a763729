<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="快速录入"
      width="60%"
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="80px" label-position="top">
          <el-row :gutter="10">
           <el-col :span="6">
             <el-form-item label="样例编号" prop="sampleNum">
               <el-input v-model="form.sampleNum" ref="sampleNum" maxlength="9" placeholder="请输入样例编号"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="身份证/护照" prop="fastinIdcard">
               <el-input v-model="form.fastinIdcard" placeholder="请输入身份证/护照"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="姓名" prop="name">
               <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="原始编号" prop="originNum">
               <el-input v-model="form.originNum" placeholder="请输入原始编号"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="检测产品" prop="productName">
               <el-select v-model="form.productName" filterable placeholder="请选择检测产品" style="width: 100%;" clearable>
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in productNameList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="送检单位" prop="inspectionUnit">
               <el-select v-model="form.inspectionUnit" filterable placeholder="请选择送检单位" style="width: 100%;" clearable>
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in inspectionUnitList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="送检日期" prop="inspectionTime">
               <el-date-picker v-model="form.inspectionTime" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" placeholder="请选择送检日期" style="width: 100%;"></el-date-picker>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="项目名称" prop="projectName">
               <el-select v-model="form.projectName" placeholder="请选择项目名称" filterable style="width: 100%;" clearable>
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in projectList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="24">
             <el-form-item label="备注" prop="remark">
               <el-input v-model="form.remark" :autosize="{minRows: 2}" type="textarea" rows="2" placeholder="请输入备注"></el-input>
             </el-form-item>
           </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer">
        <el-button :loading="confirmLoading" size="mini" type="primary" @click="handleConfirm(1)">保存并继续添加</el-button>
        <el-button :loading="confirmLoading" size="mini" type="primary" @click="handleConfirm(0)">保存并关闭</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
    <check-dialog
      :pvisible.sync="checkData.visible"
      :ptable-data="checkData.tableData"/>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
import checkDialog from './clinicalInfoManagementFastEntryCheckDialog'
export default {
  name: 'patientSplitSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {checkDialog},
  props: {},
  data () {
    let identityNumberValidator = function (rule, value, callback) {
      if (value) {
        if (value.length === 18) {
          let result = util.identityNumberValid(value)
          if (result.pass) {
            callback()
          } else {
            callback(new Error(result.message))
          }
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      form: {
        sampleNum: '',
        fastinIdcard: '',
        name: '',
        originNum: '',
        productName: '',
        inspectionUnit: '',
        inspectionTime: '',
        projectName: '',
        remark: ''
      },
      rules: {
        sampleNum: [
          {required: true, message: '请输入样本编号', trigger: 'blur'}
        ],
        fastinIdcard: [
          {validator: identityNumberValidator}
        ],
        name: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        productName: [
          {required: true, message: '请选择检测产品', trigger: 'change'}
        ],
        inspectionUnit: [
          {required: true, message: '请选择送检单位', trigger: 'change'}
        ],
        inspectionTime: [
          {required: true, message: '请选择送检日期', trigger: 'change'}
        ]
      },
      projectList: [],
      productNameList: [],
      inspectionUnitList: [],
      confirmLoading: false,
      checkData: {
        visible: false,
        tableData: []
      }
    }
  },
  methods: {
    getCustomersList () {
      this.$ajax({
        method: 'get',
        url: '/customer/getCustomers',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.inspectionUnitList = []
          if (result.data) {
            result.data.forEach(v => {
              this.inspectionUnitList.push({
                value: v.customerId,
                label: v.name
              })
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getProjectsList () {
      this.$ajax({
        method: 'get',
        url: '/project/getAllProject',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.projectList = []
          if (result.data) {
            result.data.forEach(v => {
              this.projectList.push({
                value: v.projectId,
                label: v.projectName
              })
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getProductsList () {
      this.$ajax({
        method: 'get',
        url: '/product/getAllProduct',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.productNameList = []
          if (result.data) {
            result.data.forEach(v => {
              this.productNameList.push({
                value: v.productId,
                label: v.productName
              })
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleOpen () {
      this.getCustomersList()
      this.getProjectsList()
      this.getProductsList()
      this.$nextTick(() => {
        this.$refs.sampleNum.focus()
      })
    },
    // 确认
    handleConfirm (type) {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.projectName && this.form.productName) {
            this.$alert('检测产品与项目只能填其中一个', '错误', {type: 'error'})
            return
          }
          this.confirmLoading = true
          this.$ajax({
            url: '/sample/basic/fast_save_sample_info',
            data: {
              sampleNum: this.form.sampleNum,
              fastinIdcard: this.form.fastinIdcard,
              name: this.form.name,
              originNum: this.form.originNum,
              productName: this.form.productName,
              inspectionUnit: this.form.inspectionUnit,
              inspectionTime: this.form.inspectionTime,
              projectName: this.form.projectName,
              remark: this.form.remark
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              let data = result.data
              this.$message.success('保存成功')
              if (data && data.length > 0) {
                this.checkData.visible = true
                this.checkData.tableData = data
              }
              if (type === 1) {
                this.form = {
                  sampleNum: '',
                  fastinIdcard: '',
                  name: '',
                  originNum: '',
                  productName: '',
                  inspectionUnit: '',
                  inspectionTime: '',
                  projectName: '',
                  remark: ''
                }
                this.$refs.form.resetFields()
                this.$refs.sampleNum.focus()
              } else {
                this.handleClose()
              }
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },
    handleClose () {
      this.visible = false
      this.form = {
        sampleNum: '',
        fastinIdcard: '',
        name: '',
        originNum: '',
        productName: '',
        inspectionUnit: '',
        inspectionTime: '',
        projectName: '',
        remark: ''
      }
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style scoped>

</style>
