// 定义一个常量，用于复用render属性中相同的配置
const inputRenderConfig = {name: '$input',
  attrs: {
    maxlength: 50
  },
  props: {clearable: true},
  events: {input: 'handleSave'}}

let idCounter = 1 // 新增全局变量用于追踪递增的ID
// 定义一个函数，用于生成具有共同属性的配置对象，提高代码的可维护性
const hiddenField = [
  // 静置结合时间min
  'fstaticBindingTime',
  // 磁珠批号（大磁珠）+盒1批次
  'fmagneticBeadNumber',
  // 液滴生成异常情况说明
  'fdropletFromation',
  // cycle
  'fcycle',
  // cDNA扩增&纯化异常情况说明
  'fdescriptionOfAbnormalities',
  // 补扩cycle
  'fexpansionCycle',
  // 补扩后浓度
  'fexpansionComplementary',
  // 扩增cycle
  'famplificationCycle',
  // 试剂盒频次
  'fkitFrequency',
  // 环化投入量
  'fcyclizationEffort',
  // 环化投入体积
  'fcyclizationInputVolume',
  // 环化浓度
  'fcyclizationConcentration',
  // 环化试剂批次
  'fcycleReagentBatch',
  // 环化完成日期
  'fcyclizationCompletionTime',
  // 环化实验人
  'fcyclizationExperimenter',
  // 环化备注
  'fcyclizationRemark',
  // indexF-序列5'-3'方向
  'findexfSequence',
  // indexR-序列5'-3'方向
  'findexrSequence',
  // 文库名称
  'fcdnaCyclizationLib',
  // index类型
  'findexType'
]
const configFixTime = '20200220'

function createCommonConfig (field, title, renderConfig = null, formaterFN = null, width = 120) {
  return {
    id: idCounter++, // 修改为从0开始递增
    title: title,
    field: field,
    showOverflow: true,
    width: width,
    render: renderConfig,
    isCustomerField: true,
    formater: formaterFN,
    configFixTime,
    isShow: !hiddenField.includes(field)
  }
}

// 初始化idCounter，确保每次运行或刷新页面时ID序列重新开始
idCounter = 1
/**
 * 导出一个配置数组，用于表格设置。该数组包含多个配置对象，每个对象代表表格中的一列。
 * 这种方式有助于统一管理和创建表格列配置，提高代码的可维护性和可读性。
 *
 * @returns {Object[]} 返回一个配置对象数组，每个对象包含列的配置信息。
 */
export const tableConfig = [
  createCommonConfig('fexceptionRemark', '异常状态'),
  createCommonConfig('fproductionStatusText', '生产状态'),
  createCommonConfig('fisAddTestText', '是否已补测', inputRenderConfig),
  createCommonConfig('fcosQualityThresholdOrderType', '归属类型'),
  createCommonConfig('fisQualityText', '是否达标'),
  createCommonConfig('fdifference', '差额'),
  createCommonConfig('fproductionNumber', '生产序号<i style="color: red">*</i>'),
  createCommonConfig('fconfirmTime', '到样日期'),
  createCommonConfig('fsubOrderCode', '子订单编号'),
  createCommonConfig('fprojectCode', '项目编号'),
  createCommonConfig('fprojectName', '项目名称'),
  createCommonConfig('fcosSampleName', '样本原始名称'),
  createCommonConfig('fgeneNum', '吉因加编号<i style="color: red">*</i>'),
  createCommonConfig('fproductName', '产品类型'),
  createCommonConfig('flibNum', '文库编号'),
  createCommonConfig('flibType', '文库类型<i style="color: red">*</i>'),
  createCommonConfig('fbeforeBoardSuspensionConcentration', '上机前悬液浓度<i style="color: red">*</i>', inputRenderConfig, null, 160),
  createCommonConfig('fcellSampleVolume', '细胞上样体积μL<i style="color: red">*</i>', inputRenderConfig, null, 160),
  createCommonConfig('fcellInput', '细胞/核投入量<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fstaticBindingTime', '静置结合时间min'),
  createCommonConfig('fmagneticBeadNumber', '磁珠批号（大磁珠）+盒1批次', null, null, 200),
  createCommonConfig('fc4Version', 'C4版本<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fdropletFromation', '液滴生成异常情况说明', null, null, 200),
  createCommonConfig('fexperimenter', '实验人<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fcycle', 'cycle'),
  createCommonConfig('fcdnaConcentration', 'cDNA浓度（＞10ng/μl）<i style="color: red">*</i>', inputRenderConfig, null, 200),
  createCommonConfig('fdescriptionOfAbnormalities', 'cDNA扩增&纯化异常情况说明', null, null, 200),
  createCommonConfig('fexpansionCycle', '补扩cycle'),
  createCommonConfig('fexpansionComplementary', '补扩后浓度ng/μl'),
  createCommonConfig('fcdnaQuality', 'cDNA质量ng<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fcdnaMaxPeak', 'cDNA主峰最高80-6500<i style="color: red">*</i>', inputRenderConfig, null, 200),
  createCommonConfig('fcdnaExperimentCompletionTime', 'cDNA实验完成时间<i style="color: red">*</i>', inputRenderConfig, null, 160),
  createCommonConfig('fcdnaExperimenter', 'cDNA实验人<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('finputVolume', '投入体积μL<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('feffort', '投入量ng<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fspliceIndex', '接头index<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('famplificationCycle', '扩增cycle'),
  createCommonConfig('flibConcentration', '文库浓度<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('flibQuality', '文库质量<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('ffragmentSize', '片段大小<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fkitFrequency', '试剂盒批次'),
  createCommonConfig('flibFinishTime', '建库完成时间<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('flibExperimenter', '建库实验人<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fcyclizationConcentration', '环化浓度（＞0.9ng/μL）', null, null, 200),
  createCommonConfig('fcyclizationInputVolume', '环化投入体积μL'),
  createCommonConfig('fcyclizationEffort', '环化投入量ng'),
  createCommonConfig('fcdnaCyclizationLib', '文库名称', null, null, 200),
  createCommonConfig('fcycleReagentBatch', '环化试剂批次'),
  createCommonConfig('fcyclizationCompletionTime', '环化完成日期'),
  createCommonConfig('fcyclizationExperimenter', '环化实验人'),
  createCommonConfig('fcyclizationRemark', '环化备注'),
  createCommonConfig('fcyclizationOutgoningVolume', '出库体积<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fdataSize', '数据量<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('findexfSequence', 'indexF-序列 5’-3’方向', null, null, 200),
  createCommonConfig('findexrSequence', 'indexR-序列 5’-3’方向', null, null, 200),
  createCommonConfig('fcomputerStrategy', '上机策略<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('findexType', 'index类型'),
  createCommonConfig('flocation', '定位<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fcreator', '创建人'),
  createCommonConfig('fcreateTime', '创建时间')
]
