<template>
  <div>
    <div class="top-row">
      <h3>移库申请</h3>
      <div>
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button type="primary" size="mini" @click="handleSubmit">提交</el-button>
      </div>
    </div>
    <div class="wrap">
      <div class="title">位置选择</div>
      <box-choose ref="source" height="500px" :can-edit="false" can-choose/>
    </div>
    <div class="btn-group">
      <el-button type="primary" size="mini" @click="handleMove(1)"><i class="el-icon-caret-bottom"></i> 零散移库</el-button>
      <el-button type="primary" size="mini" @click="handleMove(2)"><i class="el-icon-caret-bottom"></i> 整盒移库</el-button>
    </div>
    <div class="wrap">
      <div class="title">目标位置</div>
      <box-choose ref="target" height="500px" :can-edit="false" @boxChange="handleResetSource"/>
    </div>
  </div>
</template>

<script>

// import xx form 'xxx'
import util, {awaitWrap} from '../../../util/util'
import {isExistPreciousSample} from '../../../api/sampleLibraryManagement/sampleSearch'
export default {
  name: 'moveStorageApplication',
  data () {
    return {
      lab: '',
      labName: '',
      samplePosMap: new Map() // 样本移动的位置记录，key是样本id，value: {initPos, endPos, fid, sampleCode}
    }
  },
  methods: {
    getBoxData () {
      const source = this.$refs.source
      const target = this.$refs.target
      return {
        source: {
          boxes: source.currentBox,
          currentChecked: source.currentChecked,
          boxSelectedId: source.boxSelectedId
        },
        target: {
          boxes: target.currentBox,
          boxSelectedId: target.boxSelectedId
        }
      }
    },
    // type 1：零散移库 2 整盒子移库
    async handleMove (type) {
      const { source, target } = this.getBoxData()
      await this.judgeCanMove(source, target, type)
      let allSampleObj = {}
      if (type === 1) {
        allSampleObj = util.deepCopy(source.currentChecked)
      } else if (type === 2) {
        source.boxes.boxes.forEach(arr => {
          arr.forEach(v => {
            if (v.sampleCode) {
              allSampleObj[v.id] = util.deepCopy(v)
            }
          })
        })
      }
      console.log(allSampleObj)
      const sourceRef = this.$refs.source
      const targetRef = this.$refs.target
      const newPos = targetRef.addSample(Object.values(allSampleObj))
      console.log(newPos)
      sourceRef.setSampleHoleOperateType(Object.keys(allSampleObj))
      sourceRef.currentChecked = {}
      sourceRef.checkAll = false
      this.lab = source.boxes.containerInfo.flabNo
      this.labName = source.boxes.containerInfo.flab
      this.setSamplePosMap(
        {pos: source.boxes.pos, list: Object.values(allSampleObj)},
        {pos: target.boxes.pos, list: newPos}
      )
    },
    setSamplePosMap (source = {}, target = {}) {
      source.list.forEach(v => {
        if (!this.samplePosMap.has(v.sampleId)) {
          this.samplePosMap.set(v.sampleId, {fid: v.sampleId, fsourcePlace: `${source.pos}-${v.id}`, sampleCode: v.sampleCode})
        }
      })
      target.list.forEach(v => {
        if (!this.samplePosMap.has(v.sampleId)) {
          this.samplePosMap.set(v.sampleId, {fid: v.sampleId, ftargetPlace: `${target.pos}-${v.id}`, sampleCode: v.sampleCode})
        } else {
          this.samplePosMap.get(v.sampleId).ftargetPlace = `${target.pos}-${v.id}`
        }
      })
    },
    // 判断是否可以移库, type 1：零散移库 2 整盒子移库
    judgeCanMove (source, target, type) {
      return new Promise(resolve => {
        if (!source.boxSelectedId) {
          this.$message.error('请选择初始盒子')
          return
        }
        if (!target.boxSelectedId) {
          this.$message.error('请选择目标盒子')
          return
        }
        if (source.boxSelectedId === target.boxSelectedId) {
          this.$message.error('初始盒子和目标盒子不能相同')
          return
        }
        if (source.boxes.containerInfo.flabNo !== target.boxes.containerInfo.flabNo) {
          this.$message.error('两个盒子所属实验室不同，无法移库')
          return
        }
        if (this.lab && this.lab !== source.boxes.containerInfo.flabNo) {
          this.$message.error(`存在${this.labName}的移库操作未提交，无法进行${source.boxes.containerInfo.flab}的移库`)
          return
        }
        // 盒型必须一致
        if (source.boxes.row !== target.boxes.row || source.boxes.column !== target.boxes.column) {
          this.$message.error('初始盒子盒型与目标盒子盒型不一致')
          return
        }
        // 存储类型必须一致
        if (source.boxes.sampleType !== target.boxes.sampleType) {
          this.$message.error(`初始盒子盒型与目标盒子存储类型不一致，目标盒子只能存储${source.boxes.sampleType}`)
          return
        }
        // 温度必须相同
        if (source.boxes.containerInfo.ftemperature !== target.boxes.containerInfo.ftemperature) {
          this.$message.error(`初始盒子盒型与目标盒子温度不一致，初始盒子需要的温度是${source.boxes.containerInfo.ftemperature}`)
          return
        }
        const sourceCheckedKeys = Object.keys(source.currentChecked)
        // 目标盒子的可用孔位数量
        const { emptyNum: targetBoxEmptyHoleNum } = this.getBoxSampleNum(target.boxes.boxes)
        // 零散移库下必须选择
        if (type === 1) {
          const l = sourceCheckedKeys.length
          if (l === 0) {
            this.$message.error('请选择要移库的样本')
            return
          }
          if (l > targetBoxEmptyHoleNum) {
            this.$message.error(`您选中了${l}个样本，但目标盒中仅有${targetBoxEmptyHoleNum}空位，无法满足要求，请处理。`)
            return
          }
        } else if (type === 2) { // 整合移库
          const { sampleNum: sourceSampleNum } = this.getBoxSampleNum(source.boxes.boxes)
          if (sourceSampleNum > targetBoxEmptyHoleNum) {
            this.$message.error(`您初始盒子中有${sourceSampleNum}个样本，但目标盒中仅有${targetBoxEmptyHoleNum}空位，无法满足要求，请处理。`)
            return
          }
        }
        resolve()
      })
    },
    // 获取盒子中有多少样本
    getBoxSampleNum (box) {
      let sampleNum = 0
      let emptyNum = 0
      box.forEach(item => {
        if (item) {
          item.forEach(v => {
            if (v.sampleCode) {
              sampleNum++
            } else {
              emptyNum++
            }
          })
        }
      })
      return {
        sampleNum,
        emptyNum
      }
    },
    // 重置目标数据
    handleResetSource () {
      const source = this.$refs.source
      source.boxChange(source.currentSelectBox)
      this.samplePosMap.clear()
    },
    handleClose () {
      window.close()
    },
    async handleJudge () {
      console.log([...this.samplePosMap.values()])
      const res = await awaitWrap(isExistPreciousSample({
        sampleList: [...this.samplePosMap.values()].map(v => v.sampleCode)
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        return res.data
      }
    },
    async handleSubmit () {
      if (this.samplePosMap.size === 0) {
        this.$message.error('未进行样本移库')
        return
      }
      const preciousList = await this.handleJudge()
      if (preciousList.length > 0) {
        const message =
          `${preciousList.join(',')}
             <br/>
             为珍贵样本，请确认是否出库操作？`
        await this.$confirm(message, '珍贵样本提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      this.submitBtnLoading = true
      this.$ajax({
        url: '/sample/order/submit_move_order',
        data: {
          flab: this.lab,
          sampleList: [...this.samplePosMap.values()]
        }
      }).then(res => {
        if (res.success) {
          this.$confirm('移库操作成功', '提示', {
            confirmButtonText: '继续操作',
            cancelButtonText: '退出',
            type: 'warning'
          }).then(() => {
            window.go(0)
          }).catch(window.close)
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.top-row{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 10px;
}
.wrap{
  border: 1px solid #EBEEF5;
  .title{
    line-height: 40px;
    background: #ccc;
    padding: 0 10px;
  }
}
  .btn-group{
    text-align: center;
    line-height: 100px;
  }
</style>
