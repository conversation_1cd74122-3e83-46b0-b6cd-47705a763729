<script>
import mixins from '../../../../../util/mixins'
import constants from '../../../../../util/constants'
import {awaitWrap, downloadFile, readBlob} from '../../../../../util/util'
import {signSample, downloadTemplate} from '../../../../../api/sequencingManagement/unTestSampleApi'
import ErrorDialog from './errorDialog.vue'
export default {
  name: 'signSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {ErrorDialog},
  props: {
    type: {
      type: Number,
      default: null
    },
    ids: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      uploadUrl: constants.JS_CONTEXT + '/experiment/wait_detect_sample/mark_complete',
      form: {
        type: '',
        fileList: []
      },
      fileList: [],
      loading: false,
      errorVisible: false,
      errorTableData: [],
      rules: {
        type: [{
          required: true,
          message: '请选择标记类型',
          trigger: 'change'
        }]
      },
      // 完成、未完成
      typeList: [{
        label: '完成',
        value: 1
      }, {
        label: '未完成',
        value: 0
      }]
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.$refs.upload.clearFiles()
    },
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.loading = false
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.loading = false
        return false
      }
    },
    // 文件改变时回调，判断文件是否替换
    async handleFileChange (file, fileList) {
      if (fileList.length < 2) return
      const { err } = await awaitWrap(this.$confirm('一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      err ? fileList.pop() : fileList.shift()
    },
    handleError () {
      this.loading = false
      this.$message.error('导入失败')
    },
    async handleOnSuccess (res) {
      this.loading = false
      this.$refs.upload.clearFiles()
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('标记成功')
        this.visible = false
        this.$emit('dialogConfirmEvent')
      } else {
        if (res && res.data) {
          this.errorVisible = true
          this.errorTableData = res.data
        } else {
          this.$message.error(res.message)
        }
      }
    },
    // 下载模版
    async handleDownload () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTemplate(this.type))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    async handleSignSample () {
      this.loading = true
      const {res} = await awaitWrap(signSample({
        fids: this.ids,
        file: new File([], ''),
        fmarkType: this.form.type
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('标记成功')
        this.visible = false
        this.$emit('dialogConfirmEvent')
      }
      this.loading = false
    },
    // 标记样本
    async handleConfirm () {
      await this.handleValidForm()
      // 判断是否是导入
      if (this.type !== 2) {
        await this.handleSignSample()
        return
      }
      const fileList = this.$refs.upload.uploadFiles || []
      if (fileList.length < 1) {
        this.$message.error('请上传任务文件')
        return
      }
      this.form.fileList = fileList
      this.loading = true
      this.$refs.upload.submit()
    }
  }
}
</script>

<template>
  <div>
    <el-dialog
      v-drag-dialog
      :close-on-click-modal="false"
      append-to-body
      :visible.sync="visible"
      :before-close="handleClose"
      title="标记样本"
      width="650px"
      @opened="handleOpen">
      <el-form ref="form" :model="form" label-position="left" label-suffix=":" :rules="rules">
        <span v-if="type !== 2">已选中{{ids.length}}个样本</span>
        <el-form-item label="请选择标记类型" prop="type">
          <el-select v-model="form.type" size="mini" clearable class="form-width" placeholder="请选择标记类型">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="type === 2">
          <el-form-item label="请导入需要标记的样本" prop="fileList">
            <el-button type="text" size="mini" @click="handleDownload">文件模板下载</el-button>
            <el-upload
              ref="upload"
              :auto-upload="false"
              :file-list="fileList"
              :action="uploadUrl"
              :data="{
              fids: [],
              fmarkType: form.type
             }"
              :before-upload="handleBeforeUpload"
              :on-change="handleFileChange"
              :on-success="handleOnSuccess"
              :on-error="handleError"
              style="text-align: center;"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
                <br/>
                仅支持 xls、xlsx，只能上传1份小于10M的文件；
              </div>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
    </el-dialog>
    <error-dialog :pvisible.sync="errorVisible" :table-data="errorTableData"></error-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
