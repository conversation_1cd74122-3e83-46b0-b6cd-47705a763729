<template>
  <div class="page-container">
    <div class="page-title">
      个性化MRD数据监控看板
    </div>

    <div class="sample-monitoring">
      <div class="module-title-bar">
        样本进度监控
      </div>
      <el-form :model="form" inline label-width="140px" size="mini">
        <el-form-item>
          <div slot="label">
            <el-dropdown @command="(command) => handleCommand(command, 1)">
                      <span class="el-dropdown-link">
                        {{ timeOneKey }}<i class="el-icon-arrow-down el-icon--right"></i>
                      </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="到样时间">到样时间</el-dropdown-item>
                <el-dropdown-item command="报告时间">报告时间</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <el-date-picker
            v-model="tableForm.reportTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            type="daterange"
            size="mini"
            class="date"
            style="width: 250px;"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产品/项目名称">
          <el-select v-model="tableForm.productName"
                     multiple
                     collapse-tags filterable size="mini" clearable
                     class="input-width">
            <el-option
              v-for="(item, index) in productList"
              :key="index"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <span class="btn">
          <el-button
            type="primary"
            size="mini"
            plain
            @click="handleSearch">
            确认
          </el-button>
          <el-button
            type="primary"
            size="mini"
            plain
            @click="handleResetForm">
            重置
          </el-button>
        </span>
        <span class="content">
          <el-table
            ref="table"
            :data="tableData"
            height="300px"
            border
            size="mini"
            :show-summary="true"
            class="computer-table"
            style="width: 100%"
          >
            <el-table-column prop="productName" label="产品/项目名称" min-width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="sampleTotalNum" label="样例总数" min-width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="pauseTestSampleNum" label="暂停检测样例数" min-width="100"
                             show-overflow-tooltip></el-table-column>
            <el-table-column prop="sentReportSampleNum" label="报告发放样例数" min-width="100"
                             show-overflow-tooltip></el-table-column>
            <el-table-column prop="signProbeSampleNum" label="探针已到货样例数" min-width="120"
                             show-overflow-tooltip></el-table-column>
            <el-table-column prop="noSignProbeSampleNum" label="探针未到货样例数" min-width="160"
                             show-overflow-tooltip></el-table-column>
            <el-table-column prop="noSubscribeProbeSampleNum" label="未订购探针样例数" min-width="160"
                             show-overflow-tooltip></el-table-column>
          </el-table>
        </span>
      </el-form>

    </div>

    <div class="sample-total">
      <div class="module-title-bar">
        送样量展示
      </div>
      <div class="content">
        <div class="search">
          <div class="title">
            条件设置
          </div>
          <el-form :model="form" label-width="140px" size="mini">
            <el-form-item>
              <div slot="label">
                <el-dropdown @command="(command) => handleCommand(command, 2)">
                      <span class="el-dropdown-link">
                        {{ timeTwoKey }}<i class="el-icon-arrow-down el-icon--right"></i>
                      </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="到样时间">到样时间</el-dropdown-item>
                    <el-dropdown-item command="报告时间">报告时间</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <el-date-picker
                v-model="form.reportTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                type="daterange"
                size="mini"
                class="date"
                style="width: 250px;"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="产品/项目名称">
              <el-select v-model="form.productName"
                         multiple
                         collapse-tags filterable size="mini" clearable
                         class="input-width">
                <el-option
                  v-for="(item, index) in productList"
                  :key="index"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="统计周期">
              <el-radio-group v-model="form.dateUnit">
                <el-radio :label="0">周统计</el-radio>
                <el-radio :label="1">月统计</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div class="btn">
            <el-button
              type="primary"
              size="mini"
              plain
              @click="handleConfirm">
              确认
            </el-button>
            <el-button
              type="primary"
              size="mini"
              plain
              @click="handleReset">
              重置
            </el-button>
          </div>
        </div>
        <div class="charts-wrapper">
          <div class="title">
            图示区
          </div>
          <div v-if="showCharts" ref="charts" class="charts"></div>
          <el-empty v-else></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import util from '../../../../util/util'
import * as echarts from 'echarts'

export default {
  mounted () {
    this.getBoardData()
    this.getProductList()
  },
  data () {
    return {
      showCharts: false,
      timeOneKey: '到样时间',
      timeTwoKey: '到样时间',
      tableData: [],
      productList: [],
      tableForm: {
        productName: [],
        reportTime: []
      },
      form: {
        dateUnit: 1,
        productName: [],
        reportTime: []
      },
      tableFormSubmit: {},
      submitForm: {}
    }
  },
  methods: {
    // 筛选表格数据
    handleSearch () {
      this.tableFormSubmit = this.forMateTableForm(this.tableForm)
      this.getBoardData()
    },
    // 重置表格搜索
    handleResetForm () {
      this.timeKey = '到样时间'
      this.tableForm = {
        dateUnit: 1,
        productName: [],
        reportTime: []
      }
      this.handleSearch()
    },
    // 获取下拉数据
    getProductList () {
      this.$ajax({
        url: '/system/monitor/get_mrd_product_name_list'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.productList = []
          data.forEach(v => {
            this.productList.push(v)
          })
        }
      })
    },
    // 获取数据
    getBoardData () {
      this.$ajax({
        url: '/system/monitor/get_progress_sample_mrd_list',
        loadingDom: '.computer-table',
        data: this.tableFormSubmit
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              noSignProbeSampleNum: v.noSignProbeSampleNum,
              noSubscribeProbeSampleNum: v.noSubscribeProbeSampleNum,
              pauseTestSampleNum: v.pauseTestSampleNum,
              productName: v.productName,
              sampleTotalNum: v.sampleTotalNum,
              sentReportSampleNum: v.sentReportSampleNum,
              signProbeSampleNum: v.signProbeSampleNum
            }
            let realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            item.realData = realData
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.$refs.table.doLayout()
          })
        } else {
          this.$message(res.message)
        }
      })
    },
    handleCommand (command, type) {
      type === 1 ? this.timeOneKey = command : this.timeTwoKey = command
    },
    handleReset () {
      this.timeKey = '到样时间'
      this.form = {
        dateUnit: 1,
        productName: [],
        reportTime: []
      }
      this.showCharts = false
    },
    formMateTimeSearch (form, timeKey) {
      const timeKeyMapping = {
        '到样时间': ['sampleConfirmStartTime', 'sampleConfirmEndTime'],
        '报告时间': ['reportStartDate', 'reportEndDate']
      }
      let timeSearch = [timeKey]
      let timeForm = {}
      const timeSearchValue = [
        form.reportTime
      ]
      timeSearch.forEach((v, index) => {
        let keys = timeKeyMapping[v]
        let value = timeSearchValue[index] || []
        if (value.length === 2) {
          timeForm[keys[0]] = timeSearchValue[index][0]
          timeForm[keys[1]] = timeSearchValue[index][1]
        }
      })
      return timeForm
    },
    forMateTableForm (form) {
      const timeForm = this.formMateTimeSearch(form, this.timeOneKey)
      const submitForm = {
        dateUnit: form.dateUnit,
        productName: form.productName.join(','),
        ...timeForm
      }
      return submitForm
    },
    forMateForm (form) {
      const timeForm = this.formMateTimeSearch(form, this.timeTwoKey)
      if (JSON.stringify(timeForm) === '{}') {
        return false
      }
      const submitForm = {
        dateUnit: form.dateUnit,
        productName: form.productName.join(','),
        ...timeForm
      }
      return submitForm
    },
    handleConfirm () {
      const submitForm = this.forMateForm(this.form)
      if (!submitForm) {
        this.$message.error('请选择时间')
        return
      }

      this.$ajax({
        url: '/system/monitor/get_amount_sample_mrd_list',
        loadingDom: '.charts-wrapper',
        data: submitForm
      }).then(async res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.showCharts = true
          await this.$nextTick()
          const myChart = echarts.init(this.$refs.charts)
          const option = {
            xAxis: {
              type: 'category',
              data: data.map(v => v.date),
              axisLine: {
                'show': false
              },
              axisLabel: {
                interval: 0, //   坐标轴刻度标签的显示间隔(在类目轴中有效哦)，默认会采用标签不重叠的方式显示标签（也就是默认会将部分文字显示不全）可以设置为0强制显示所有标签，如果设置为1，表示隔一个标签显示一个标签，如果为3，表示隔3个标签显示一个标签，以此类推
                rotate: 60 // 标签倾斜的角度，在类目轴的类目标签显示不全时可以通过旋转防止标签重叠（官方这样说的）旋转的角度是-90到90度
              }
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              left: '6%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                data: data.map(v => v.sampleAmount),
                type: 'line'
              }
            ]
          }
          option && myChart.setOption(option)
        } else {
          this.$message(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 40px);
  overflow: scroll;
  padding: 20px;

  .page-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .module-title-bar {
    font-size: 20px;
    font-weight: bold;
    margin: 20px 0;
    background: #eee;
    padding: 10px;
  }

  .sample-total {

    .title {
      font-size: 18px;
      font-weight: bold;
      margin: 0 0 20px;
    }

    .content {
      display: flex;
      height: calc(100% - 90px);

      .search {
        margin-right: 30px;
        padding-right: 30px;
        position: relative;
        width: 35%;

        .btn {
          margin-top: 80px;
          display: flex;
          justify-content: flex-end;
        }
      }

      .search::after {
        position: absolute;
        top: 0;
        right: 0;
        content: '';
        height: 100%;
        width: 0;
        border: 2px dashed #eee;
      }
      .charts-wrapper {
        flex: 1;
      }
    }

  }
}

.charts {
  margin: auto;
  width: 500px;
  height: 300px;
}

</style>
