<template>
  <div style="height: 100%;">
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini" label-suffix=":" @keyup.enter.native="handleSearch">
        <el-form-item label="申请单号">
          <el-input v-model.trim="form.applyCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="客户">
          <el-input v-model.trim="form.customer" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="是否加急">
          <el-select v-model="form.isEmergency" clearable placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in trueOrFalseList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model.trim="form.applicant" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="中心名称">
          <el-input v-model.trim="form.centerName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="所属">
          <el-select v-model="form.belong" clearable multiple placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in belongList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期">
          <el-date-picker v-model="form.applyTime" type="daterange"
                          range-separator="-" clearable
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <el-button :loading="downloadLoading" v-if="$setAuthority('013004001', 'buttons')" type="primary" size="mini" @click="handleExport">批量导出发货单</el-button>
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
      </div>
      <div class="table">
        <el-table
          ref="table"
          :data="tableData"
          size="mini"
          class="publicityApplicationManagement"
          height="calc(100vh - 74px - 40px - 83px - 42px - 32px)"
          style="width: 100%"
          @select="handleSelect"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="applyCode" label="申请单号" min-width="180">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="handleViewDetail(scope.row)">{{scope.row.applyCode}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="customer" label="客户" width="120">
            <template slot-scope="scope">
              <desensitization :info="scope.row.customer" type="name"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="applyTime" label="申请日期" width="140"></el-table-column>
          <el-table-column prop="applicant" label="申请人" width="120"></el-table-column>
          <el-table-column prop="contactPerson" label="联系人" width="120"></el-table-column>
          <el-table-column prop="centerCode" label="中心编号" width="120"></el-table-column>
          <el-table-column prop="centerName" label="中心名称" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="applyTypeText" label="申请类型" width="120"></el-table-column>
          <el-table-column prop="statusText" label="状态" width="100"></el-table-column>
          <el-table-column prop="auditor" label="审核人" width="120"></el-table-column>
          <el-table-column prop="isEmergencyText" label="加急" width="100"></el-table-column>
          <el-table-column prop="belongText" label="所属" width="120"></el-table-column>
          <el-table-column prop="expectReceiveTime" label="期望到达时间" width="140"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  name: 'publicityApplicationManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {},
  props: [],
  mounted () {
    this.handleSearch()
  },
  watch: {},
  computed: {},
  data () {
    return {
      downloadLoading: false,
      type: 1,
      form: {
        applyCode: '',
        customer: '',
        applyTime: [],
        isEmergency: '',
        applicant: '',
        belong: [],
        centerName: ''
      },
      submitForm: {
        applyCode: '',
        customer: '',
        applyTime: [],
        isEmergency: '',
        applicant: '',
        belong: [],
        centerName: ''
      },
      trueOrFalseList: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ],
      applyTypeList: [
        {
          value: 1,
          label: '物料申请'
        },
        {
          value: 2,
          label: '补发申请'
        },
        {
          value: 3,
          label: '部分发货'
        },
        {
          value: 4,
          label: '自动补发'
        }
      ],
      statusList: [
        {
          value: 0,
          label: '通过'
        },
        {
          value: 1,
          label: '待发货'
        },
        {
          value: 2,
          label: '已发货'
        },
        {
          value: 3,
          label: '已签收'
        }
      ],
      belongList: [
        {
          value: 1,
          label: '临床(物料)'
        },
        {
          value: 2,
          label: '药厂'
        },
        {
          value: 3,
          label: '渠道'
        },
        {
          value: 4,
          label: '临床(宣传品)'
        },
        {
          value: 5,
          label: '病原(物料)'
        }
      ],
      selectedRows: []
    }
  },
  methods: {
    getData () {
      let applyStartTime = ''
      let applyEndTime = ''
      if (this.submitForm.applyTime && this.submitForm.applyTime.length > 0) {
        applyStartTime = this.submitForm.applyTime[0]
        applyEndTime = this.submitForm.applyTime[1]
      }
      this.$ajax({
        loadingDom: '.publicityApplicationManagement',
        url: '/materials/get_apply_form',
        data: {
          applyCode: this.submitForm.applyCode,
          customer: this.submitForm.customer,
          applyStartTime: applyStartTime,
          applyEndTime: applyEndTime,
          isEmergency: this.submitForm.isEmergency,
          applicant: this.submitForm.applicant,
          centerName: this.submitForm.centerName,
          belong: this.submitForm.belong,
          type: this.type, // 0为物料库存, 1为宣传品库存
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.selectedRows = []
          this.tableData = []
          this.totalPage = data.total
          let item = {}
          data.rows.forEach(v => {
            item = {
              fid: v.fid,
              applyCode: v.applyCode,
              customer: v.customer,
              applicant: v.applicant,
              applyTime: v.applyTime,
              contactPerson: v.contactPerson,
              centerCode: v.centerCode,
              centerName: v.centerName,
              applyType: v.applyType,
              applyTypeText: this.getString(v.applyType, 'applyTypeList'),
              auditor: v.auditor,
              isEmergency: v.isEmergency,
              isEmergencyText: this.getString(v.isEmergency, 'trueOrFalseList'),
              status: v.status,
              statusText: this.getString(v.status, 'statusList'),
              belong: v.belong,
              belongText: this.getString(v.belong, 'belongList'),
              auditTime: v.auditTime,
              expectReceiveTime: v.expectReceiveTime
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSearch () {
      this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        applyCode: '',
        customer: '',
        applyTime: [],
        isEmergency: '',
        applicant: '',
        belong: [],
        centerName: ''
      }
      this.handleSearch()
    },
    handleSelectAll (selection) {
      this.selectedRows = []
      selection.forEach((v, i) => {
        this.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = this.selectedRows.indexOf(row)
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs.table.toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs.table.toggleRowSelection(row, true)
      }
    },
    handleExport () {
      if (this.selectedRows.length > 0) {
        let hasClinical = false // 是否有临床
        let hasOther = false // 是否有药厂与渠道
        let hasBelong = false // 所属是否一致
        this.selectedRows.reduce((pre, next) => {
          if (pre.belong !== next.belong) hasBelong = true
          return next
        }, this.selectedRows[0])
        if (hasBelong) {
          this.$message.error('所选信息必须要相同所属才能批量导出')
          return
        }
        this.selectedRows.forEach(v => {
          if (v.belong === 1 || v.belong === 4 || v.belong === 5) hasClinical = true
          if (v.belong === 2 || v.belong === 3) hasOther = true
        })
        if (hasOther && hasClinical) {
          this.$message.error('临床(宣传品)与药厂、渠道不能一起导出')
          return
        }
        let applyFormIds = this.selectedRows.map(v => v.fid)
        this.downloadLoading = true
        this.$ajax({
          url: '/materials/export_send_form',
          data: {
            applyFormIds: applyFormIds.toString(),
            type: hasClinical ? 0 : 1 // 导出发货单类型0为临床1为药厂与渠道
          },
          responseType: 'blob'
        }).then(result => {
          const {data, headers} = result
          let fileName = headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1')
          // let fileName = '收款单信息.xlsx'
          let blob = new Blob([data], {type: headers['content-type']})
          let downloadElement = document.createElement('a')
          let href = window.URL.createObjectURL(blob) // 创建下载的链接
          downloadElement.href = href
          downloadElement.download = decodeURI(fileName) // 下载后文件名
          document.body.appendChild(downloadElement)
          downloadElement.click() // 点击下载
          document.body.removeChild(downloadElement) // 下载完成移除元素
          window.URL.revokeObjectURL(href) // 释放blob对象
        }).catch(err => {
          console.log(err.response)
          this.$message.error(err.response.data.message)
        }).finally(() => {
          this.downloadLoading = false
        })
      } else {
        this.$message.error('请选择数据')
      }
    },
    getString (value, list) {
      let label = ''
      let obj = this[list].find(v => value === v.value)
      if (obj) {
        label = obj.label
      }
      return label
    },
    handleViewDetail (row) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'applicationInfo',
        applicationInfo: {
          id: row.fid,
          type: 'publicity',
          needReissue: row.applyType === 2
        }
      })
      util.openNewPage('/business/subpage/applicationDetail')
    }
  }
}
</script>

<style scoped lang="scss">
  .search{
    height: 96px;
  }
  .search >>>.el-form-item--mini{
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .content{
    height: calc(100% - 96px);
    .buttonGroup{
      height: 40px;
      line-height: 40px;
    }
    .table{
      height: calc(100% - 50px);
    }
    .table >>>.el-table__header .el-checkbox {
      display: none;
    }
  }
</style>
