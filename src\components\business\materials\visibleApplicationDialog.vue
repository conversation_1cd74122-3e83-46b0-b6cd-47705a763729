<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    title="可见申请方式"
    width="20%"
    @open="handleOpen">
    <el-table
      ref="table"
      :data="tableData"
      class="table"
      size="mini"
      style="width: 100%"
      @select="handleSelectTable"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column type="selection"></el-table-column>
      <el-table-column prop="name" label="物料编码" min-width="120" show-overflow-tooltip></el-table-column>
    </el-table>
    <span slot="footer">
      <el-button :loading="loading" size="mini" @click="handleClose">取消</el-button>
      <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import util from '../../../util/util'
import mixins from '../../../util/mixins'

export default {
  name: 'visibleApplicationDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number
    }
  },
  data () {
    return {
      tableData: [],
      loading: false,
      selectedRows: new Map()
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.getData()
      })
    },
    // 获取可见申请方式列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/materials/get_visible_type',
        method: 'get',
        data: {
          fid: this.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data.typeList
          this.selectedRows.clear()
          this.tableData = []
          let item = {}
          data.forEach(v => {
            item = {
              id: v.fid,
              name: v.fname,
              checked: v.fchecked
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.tableData.forEach(v => {
            this.$nextTick(() => {
              // 设置表格默认选择状态
              if (v.checked === 1) {
                this.$refs.table.toggleRowSelection(v, true)
                this.selectedRows.set(v.id, v)
              }
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 设置可见申请方式
    handleConfirm () {
      this.loading = true
      this.$ajax({
        loadingDom: '.table',
        url: '/materials/save_material_type',
        data: {
          fid: this.id,
          ftypeIdList: [...this.selectedRows.values()].map(v => v.id)
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped>

</style>
