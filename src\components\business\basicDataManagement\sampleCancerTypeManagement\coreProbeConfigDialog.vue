<template>
  <el-dialog :title="title" :visible.sync="visible" width="1000px" :close-on-click-modal="false" @close="handleClose"
    @open="handleOpen">
    <!-- 操作按钮 -->
    <div class="operate-btns-group" style="margin-bottom: 10px;">
      <el-button type="primary" size="mini" @click="handleAdd">新增</el-button>
    </div>

    <!-- 数据列表 -->
    <el-table ref="table" :data="tableData" border :height="400" v-loading="loading">
      <el-table-column prop="chipType" label="芯片类型" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="cancerCategory" label="癌种分类" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="cancerType" label="癌种" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页-->
    <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh" />
      </button>
    </el-pagination>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">关闭</el-button>
    </div>

    <!-- 新增/编辑表单弹窗 -->
    <core-probe-form-dialog :pvisible.sync="formDialogInfo.visible" :title="formDialogInfo.title"
      :form-data="formDialogInfo.formData" :cancer-type-id="cancerTypeId" @dialogConfirmEvent="handleSearch" />
  </el-dialog>
</template>

<script>
import { getCoreProbeConfigList } from '@/api/basicDataManagement/sampleCancerTypeApi'
import util, { awaitWrap } from '@/util/util'
import mixins from '@/util/mixins'
import CoreProbeFormDialog from './coreProbeFormDialog'

export default {
  name: 'CoreProbeConfigDialog',
  mixins: [mixins.tablePaginationCommonData, mixins.dialogBaseInfo],
  components: {
    CoreProbeFormDialog
  },
  props: {
    pvisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '核心探针配置表'
    },
    cancerTypeId: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      formDialogInfo: {
        visible: false,
        title: '',
        formData: null
      },
      tableData: []
    }
  },
  methods: {
    async handleOpen () {
      await this.handleSearch()
    },

    // 搜索
    handleSearch () {
      this.currentPage = 1
      this.getData()
    },

    async getData () {
      const { res } = await awaitWrap(getCoreProbeConfigList({
        cancerTypeId: this.cancerTypeId,
        pagedRequest: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }))
      if (res.code === this.SUCCESS_CODE) {
        const data = res.data
        this.totalPage = data.total
        this.tableData = []
        data.records.forEach(v => {
          const item = {
            chipType: v.fchipType,
            cancerCategory: v.fcancerType,
            cancerType: v.fcancer,
            remark: v.fremark,
            fid: v.fid
          }
          item.realData = util.deepCopy(item)
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        console.log(this.tableData)
      }
    },

    handleAdd () {
      this.formDialogInfo.visible = true
      this.formDialogInfo.title = '新增核心探针配置'
      this.formDialogInfo.formData = null
    },

    handleEdit (row) {
      this.formDialogInfo.visible = true
      this.formDialogInfo.title = '编辑核心探针配置'
      this.formDialogInfo.formData = row
    },

    handleFormConfirm () {
      this.getData()
    }
  }
}
</script>

<style scoped>
.operate-btns-group {
  text-align: left;
}

.dialog-footer {
  text-align: right;
}
</style>
