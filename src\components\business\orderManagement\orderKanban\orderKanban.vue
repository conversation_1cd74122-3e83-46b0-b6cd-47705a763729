<template>
  <div class="page">
    <nav class="operateBar">
      <div></div>
      <div>
        <el-select
                v-model="form.content"
                size="medium"
                placeholder="请选择"
                style="width: 150px;"
                clearable
                @clear="handleReset"
                @change="clearInput">
          <template v-for="(v, k) in searchOptions">
            <el-option :key="k" :label="v.label" :value="k"></el-option>
          </template>
        </el-select>
        <template v-if="inputFiled.indexOf(form.content) > -1">
          <el-input
                  v-model="form.input"
                  :disabled="!form.content"
                  style="width: 250px;margin: 0 20px;"
                  size="medium"
                  clearable
                  placeholder="请输入"
                  @clear="handleReset"
                  @keyup.enter.native="handleSearch"></el-input>
        </template>
        <template v-else-if="form.content === 'detectType'">
          <el-select
                  v-model="form.input"
                  style="width: 250px;margin: 0 20px;"
                  size="medium"
                  clearable
                  @clear="handleReset"
          >
            <el-option :key="v" :label="v" :value="v" v-for="v in detectTypeOptions"></el-option>
          </el-select>
        </template>
        <template v-else>
          <el-input
                  v-model="form.input"
                  :disabled="!form.content"
                  style="width: 250px;margin: 0 20px;"
                  size="medium"
                  clearable
                  placeholder="请输入"
                  @clear="handleReset"
                  @keyup.enter.native="handleSearch"></el-input>
        </template>
        <el-button size="medium" type="primary" @click="handleSearch">查询</el-button>
      </div>
    </nav>
    <div style="padding: 0 20px;">
      <el-table
              ref="table"
              :data="tableData"
              class="table"
              height="calc(100vh - 65px - 45px - 20px - 76px - 42px - 20px - 15px)"
              @select="handleSelect"
              @row-click="handleRowClick"
              @select-all="handleSelectAll">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column label="订单编号" width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <!--<el-button type="text" @click.stop="handleCheck(scope.row.realData, 2, false)">{{scope.row.orderNum}}</el-button>-->
            <span
              style="color: var(--primary-color);font-size: 14px;cursor: pointer"
              @click="handleCheck(scope.row.realData, 2, false)">{{scope.row.orderNum}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="childOrderCode" label="子订单号" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <span
              style="color: var(--primary-color);font-size: 14px;cursor: pointer"
              @click="handleShowQcDialog(scope.row.realData)">{{scope.row.childOrderCode}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sampleNum" label="样本数目" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="detectType" label="检测类型" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="extract" label="提取" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{'text-green': scope.row.extract[0] !== scope.row.extract[1]}">{{scope.row.extract.join('/')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="buildLibrary" label="建库" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{'text-green': scope.row.buildLibrary[0] !== scope.row.buildLibrary[1]}">{{scope.row.buildLibrary.join('/')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="hybridization" label="杂交" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{'text-green': scope.row.hybridization[0] !== scope.row.hybridization[1]}">{{scope.row.hybridization.join('/')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="computerTest" label="上机" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{'text-green': scope.row.computerTest[0] !== scope.row.computerTest[1]}">{{scope.row.computerTest.join('/')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="analysis" label="分析" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{'text-green': scope.row.analysis[0] !== scope.row.analysis[1]}">{{scope.row.analysis.join('/')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="util" label="送检单位" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="estimatedDeliveryDate" label="预计交付日期" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="applyDate" label="申请检测日期" width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
              :page-sizes="pageSizes"
              :page-size="pageSize"
              :current-page.sync="currentPage"
              :total="totalPage"
              style="background: #ffffff;"
              layout="total, sizes, prev, pager, next, jumper, slot"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
      </el-pagination>
    </div>
    <quality-control-results-dialog
      :pvisible.sync="qualityControlResultsDialogVisible"
      :order-id="qualityControlResultsDialogData.childOrderId"
      :type="qualityControlResultsDialogData.orderType"/>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import qualityControlResultsDialog from './qualityControlResultsDialog'
export default {
  name: 'abnormalSample',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    qualityControlResultsDialog
  },
  mounted () {
    this.getDetectType()
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        content: '',
        input: ''
      },
      qualityControlResultsDialogVisible: false,
      qualityControlResultsDialogData: {
        orderType: 0,
        childOrderId: ''
      },
      formSubmit: {},
      statusOptions: {
        0: '草稿',
        1: '待审核',
        2: '已审核',
        3: '已撤回',
        4: '驳回',
        5: '已到样',
        6: '异常',
        7: '风险检测',
        8: '停止检测',
        9: '流入检测',
        10: '核酸质控完成',
        11: '文库质控完成',
        12: '建库完成',
        13: '测序下机'
      },
      detectTypeOptions: [],
      searchOptions: {
        orderCode: {label: '订单编号'},
        subOrderCode: {label: '子订单编号'},
        libName: {label: '文库名称'},
        subLibName: {label: '子文库名称'},
        sampleName: {label: '样本名称'},
        geneCode: {label: '吉因加编号'},
        projectCode: {label: '项目编号'},
        projectName: {label: '项目名称'},
        detectType: {label: '检测类型'}
      },
      inputFiled: ['orderCode', 'subOrderCode', 'libName', 'subLibName', 'sampleName', 'geneCode', 'projectCode', 'projectName']
    }
  },
  methods: {
    // 获取检测类型
    getDetectType () {
      this.$ajax({
        url: '/order/get_detect_type_list',
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.detectTypeOptions = res.data || []
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getData () {
      let data = {
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      if (this.formSubmit.content) {
        data[this.formSubmit.content] = this.formSubmit.input
      }
      this.$ajax({
        url: '/order/get_sub_order_table_list',
        data: data,
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows
          this.tableData = []
          this.selectedRows.clear()
          rows.forEach(v => {
            let item = {
              id: v.fid,
              orderId: v.orderId,
              orderType: v.type,
              orderNum: v.orderCode,
              childOrderCode: v.subOrderCode,
              childId: v.subOrderId,
              sampleNum: v.sampleCount,
              detectType: v.detectType,
              projectCode: v.projectCode,
              projectName: v.projectName,
              extract: this.setSpecFile(v.extract, v.sampleCount),
              buildLibrary: this.setSpecFile(v.build, v.sampleCount),
              hybridization: this.setSpecFile(v.hybrid, v.sampleCount),
              computerTest: this.setSpecFile(v.upMachine, v.sampleCount),
              analysis: this.setSpecFile(v.analysis, v.sampleCount),
              // submitDate: v.,
              customerName: v.customerName,
              util: v.sendUnit,
              estimatedDeliveryDate: v.preDeliveryTime,
              applyDate: v.applyInspectionTime
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 设置 提取、建库、杂交、上机、分析的字段
    setSpecFile (value, total) {
      if (typeof value === 'number' && typeof total === 'number') {
        return [value, total]
      }
      return ['-', '-']
    },
    handleReset () {
      this.form = {
        content: '',
        input: ''
      }
      this.handleSearch()
    },
    // 下拉框发生改变时，清空input
    clearInput () {
      this.form.input = ''
    },
    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData()
    },
    // 查看
    handleCheck (row, type, editMode) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type,
          orderId: row.orderId,
          editMode: editMode
        }
      })
      let path = ''
      if (row.orderType === 1) path = `/business/sub/orderLibraryDetail?code=${row.orderNum}`
      if (row.orderType === 2) path = `/business/sub/orderTissueDetail?code=${row.orderNum}`
      if (path) util.openNewPage(path)
    },
    // 查看子订单
    handleShowQcDialog (row) {
      this.qualityControlResultsDialogData = {
        orderType: row.orderType,
        childOrderId: row.childId
      }
      this.qualityControlResultsDialogVisible = true
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.text-green{
  color: #67C23A;
}
</style>
