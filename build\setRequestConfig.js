
const path = require('path')
const util = require('./utils')
const config = require('../config/index')

exports.rewrite = function (args) {
  let mode = args[0] || 'prod'
  util.readJson(path.resolve(__dirname, '../', 'requsetConfig.json')).then(function (data) {
    let spath = path.resolve(__dirname, '../', 'requsetConfig.json')
    let tpath = path.resolve(__dirname, '../', 'dist/static/config.json')
    console.log('config配置如下：', data[mode])
    util.writeJson(spath, mode, tpath, function () {
    })
  })
}
