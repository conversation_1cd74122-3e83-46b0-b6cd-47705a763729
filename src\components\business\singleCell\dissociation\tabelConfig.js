// 定义一个常量，用于复用render属性中相同的配置
const inputRenderConfig = {name: '$input',
  attrs: {
    maxlength: 50
  },
  props: {clearable: true}
}
const selectRenderConfig = {name: '$select',
  options: [
    { label: '是', value: 1 },
    { label: '否', value: 0 }
  ],
  props: {clearable: true}
}

const testSelectRenderConfig = {name: '$select',
  options: [
    // A、B、C、D、符合、不符合
    { label: 'A', value: 'A' },
    { label: 'B', value: 'B' },
    { label: 'C', value: 'C' },
    { label: 'D', value: 'D' },
    { label: '符合', value: '符合' },
    { label: '不符合', value: '不符合' }
  ],
  props: {clearable: true}
}

let idCounter = 1 // 新增全局变量用于追踪递增的ID

// 定义一个函数，用于生成具有共同属性的配置对象，提高代码的可维护性
function createCommonConfig (field, title, renderConfig = null, formaterFN = null, width = 140) {
  return {
    id: idCounter++, // 修改为从0开始递增
    title: title,
    field: field,
    showOverflow: true,
    width: width,
    render: renderConfig,
    isCustomerField: true,
    formater: formaterFN,
    isShow: true
  }
}

// 初始化idCounter，确保每次运行或刷新页面时ID序列重新开始
idCounter = 1
/**
 * 导出一个配置数组，用于表格设置。该数组包含多个配置对象，每个对象代表表格中的一列。
 * 这种方式有助于统一管理和创建表格列配置，提高代码的可维护性和可读性。
 *
 * @returns {Object[]} 返回一个配置对象数组，每个对象包含列的配置信息。
 */
export const tableConfig = [
  createCommonConfig('fcosSampleName', '样本原始名称'),
  createCommonConfig('fproductionNumber', '生产序号<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('warningFlag', '异常状态'),
  createCommonConfig('fisBuildLibText', '样本状态'),
  createCommonConfig('fconfirmTime', '到样日期'),
  createCommonConfig('fsubOrderCode', '子订单编号'),
  createCommonConfig('fprojectCode', '项目编号'),
  createCommonConfig('fprojectName', '项目名称'),
  createCommonConfig('fsendTime', '送检日期'),
  createCommonConfig('fsampleTag', '样本标签'),
  createCommonConfig('fisStagingText', '开启状态'),
  createCommonConfig('fisBackUpText', '是否为备份'),
  createCommonConfig('fgeneNum', '吉因加编号'),
  createCommonConfig('fspeciesType', '物种类型'),
  createCommonConfig('ftissueSampleType', '组织样本类型'),
  createCommonConfig('ftissueSampleState', '组织状态'),
  createCommonConfig('fconfirmException', '到样异常'),
  createCommonConfig('fcosExceptionRemark', '到样异常描述'),
  // 是否异常
  createCommonConfig('fisSampleAnomaly', '是否异常', selectRenderConfig),
  // 样本异常描述
  createCommonConfig('fsampleAnomaly', '样本异常描述', inputRenderConfig),
  createCommonConfig('forganType', '器官类型'),
  createCommonConfig('fsamplingMethod', '取样方式'),
  createCommonConfig('fexpectedCellsNumber', '期望捕获细胞数（合同签订）', null, null, 180),
  createCommonConfig('ftranferDataSize', '测序数据量（M reads）', null, null, 180),
  createCommonConfig('fproductName', '产品名称'),
  createCommonConfig('fexperimentalLink', '实验环节'),
  createCommonConfig('fexperimentTime', '实验日期', inputRenderConfig),
  createCommonConfig('fnote', '备注'),
  createCommonConfig('fisSiteExperimentText', '是否上门实验'),
  // createCommonConfig('fprojectType', '事业部'),
  createCommonConfig('fcellSuspensionConcentration', '细胞悬液浓度<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fcellTotal', '细胞总量', inputRenderConfig),
  createCommonConfig('fcytoactive', '细胞活性%', inputRenderConfig),
  createCommonConfig('fproportionNucleatedCell', '有核细胞占比%', inputRenderConfig),
  createCommonConfig('fcoalescenceRate', '结团率%', inputRenderConfig),
  createCommonConfig('ffragmentationCondition', '碎片情况', inputRenderConfig),
  createCommonConfig('fisDieCell', '是否去死细胞', selectRenderConfig),
  createCommonConfig('fisDefragment', '是否去碎片', selectRenderConfig),
  createCommonConfig('fbeforeBoardSuspensionConcentration', '上机前-细胞悬液浓度', inputRenderConfig, null, 150),
  // createCommonConfig('fbeforeBoardCount', '上机前-细胞/细胞核总量', inputRenderConfig, null, 150),
  createCommonConfig('fbeforeBoardCount', '上机前-细胞/细胞核总量<i style="color: red">*</i>', inputRenderConfig, null, 170),
  createCommonConfig('fbeforeBoardCytocative', '上机前-细胞活性/核完整性<i style="color: red">*</i>', inputRenderConfig, null, 180),
  createCommonConfig('fbeforeBoardDiameter', '上机前-细胞直径μm', inputRenderConfig, null, 160),
  createCommonConfig('fbeforeBoardCakeRate', '上机前-结团率%<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fbeforeBoardFragmentation', '上机前碎片情况', inputRenderConfig),
  // createCommonConfig('fisSampleException', '样本状态是否异常', selectRenderConfig),
  createCommonConfig('fsampleDescribe', '样本描述', inputRenderConfig),
  createCommonConfig('ftestSummary', '检测结论<i style="color: red">*</i>', testSelectRenderConfig),
  createCommonConfig('fisEmbarkation', '是否上机<i style="color: red">*</i>', selectRenderConfig),
  createCommonConfig('fexperimenter', '实验人员<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fexperimentChecker', '实验核对人员<i style="color: red">*</i>', inputRenderConfig),
  createCommonConfig('fexperimentRemark', '实验备注', inputRenderConfig)
]
