import sampleDetailDialogPlugin from './sampleDetailDialog.vue'

const sampleDetailDialog = {}

sampleDetailDialog.install = function (Vue) {
  let Dialogcom = Vue.extend(sampleDetailDialogPlugin)
  let instance = new Dialogcom()
  Vue.prototype.$showSampleDetailDialog = function ({geneInfo = ''}) {
    instance.visible = true
    instance.geneInfo = geneInfo
  }
  instance.$mount(document.createElement('div'))
  document.body.appendChild(instance.$el)
}

export default sampleDetailDialog
