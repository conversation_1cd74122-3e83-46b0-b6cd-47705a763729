<template>
  <div>
    <div class="search-form">
      <el-form :model="form" size="mini" label-width="80px" inline style="display: flex;">
        <el-form-item label="申请单号">
          <el-input v-model.trim="formInput.order" size="mini" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model.trim="formInput.applicant" size="mini" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model.trim="formInput.time"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleResetForm">重置</el-button>
    </div>
    <el-table
      :data="tableData"
      :key="status"
      style="width: 100%;"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      class="table"
      @filter-change="handleFilterTable">
      <el-table-column type="index" width="70" label="序号"></el-table-column>
      <el-table-column
        :filters="progressFilterOptions"
        :filtered-value="orderStatusFilterValues" prop="orderStatus"
        label="单号状态"
        width="110"
        column-key="orderStatus"></el-table-column>
      <el-table-column show-overflow-tooltip label="申请单号" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="handleModifyOrder(scope.row, 'sampleSearch')">{{scope.row.orderNum}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="lab" label="所属实验室" width="120"></el-table-column>
      <el-table-column prop="applicant" label="申请人" width="100"></el-table-column>
      <el-table-column prop="applicationTime" label="申请时间" width="180"></el-table-column>
      <el-table-column prop="operator" label="操作人" width="100"></el-table-column>
      <el-table-column prop="estimatedTime" label="预计完成时间" width="180"></el-table-column>
      <el-table-column prop="completeTime" label="完成时间" width="180"></el-table-column>
      <el-table-column prop="consumingTime" label="耗时（h）" width="180"></el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <template v-if="$setAuthority('011005001', 'buttons')">
            <el-button
              :disabled="scope.row.orderStatus !== '待领取'" type="text"
              @click="handleReceive(scope.row)">领取</el-button>
          </template>
          <template>
            <el-button
              v-if="!scope.row.orderNum.includes('调度入库') && scope.row.orderStatus === '待领取'"
              type="text"
              @click="handleReject(scope.row)">驳回</el-button>
          </template>
          <template v-if="$setAuthority('011005002', 'buttons')">
            <el-button
              :disabled="scope.row.orderStatus === '待领取' || scope.row.orderStatus === '已完成' || scope.row.orderStatus === '驳回'"
              type="text"
              @click="handleShowConfirmCompleteInnerLibraryApplicationDialog(scope.row)"
            >确认完成</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <modify-order-dialog
      :pvisible="modifyOrderDialogVisible"
      :page="page"
      :order-id="currentId"
      title="订单详情"
      @dialogConfirmEvent="handleModifyConfirm"
      @dialogCloseEvent="modifyOrderDialogVisible = false"></modify-order-dialog>
    <confirm-complete-inner-library-application-dialog
      :pvisible="confirmCompleteInnerLibraryApplicationDialogVisible"
      :order-id="currentId"
      @dialogCloseEvent="confirmCompleteInnerLibraryApplicationDialogVisible = false"
      @dialogConfirmEvent="confirmCompleteDialogConfirm"
      />
    <confirm-complete-move-library-application-dialog
        :pvisible="confirmCompleteMoveLibraryApplicationDialogVisible"
        :order-id="currentId"
        @dialogCloseEvent="confirmCompleteMoveLibraryApplicationDialogVisible = false"
        @dialogConfirmEvent="confirmCompleteDialogConfirm"
    />
    <confirm-complete-outer-library-application-dialog
      :pvisible="confirmCompleteOuterLibraryApplicationDialogVisible"
      :order-id="currentId"
      @dialogCloseEvent="confirmCompleteOuterLibraryApplicationDialogVisible = false"
      @dialogConfirmEvent="confirmCompleteDialogConfirm"/>
    <confirm-complete-back-sample-application-dialog
      :pvisible.sync="confirmCompleteBackSampleApplicationDialogVisible"
      :order-id="currentId"
      @dialogConfirmEvent="handleSearch"/>
    <reject-dialog
      :pvisible.sync="rejectDialogVisible"
      :order-num="currentId"
      @dialogConfirmEvent="getData"/>
  </div>
</template>

<script>
// import num from './components/cc'
// import constants from '../../../util/constants'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import modifyOrderDialog from './modifyOrderDialog'
import confirmCompleteInnerLibraryApplicationDialog from './confirmCompleteInnerLibraryApplicationDialog'
import confirmCompleteOuterLibraryApplicationDialog from './confirmCompleteOuterLibraryApplicationDialog'
import confirmCompleteBackSampleApplicationDialog from './confirmCompleteBackSampleApplicationDialog'
import confirmCompleteMoveLibraryApplicationDialog from './confirmCompleteMoveLibraryApplicationDialog'
import rejectDialog from './turnoverLibraryManagementRejectDialog'
export default {
  name: 'inAndOutLibraryManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    modifyOrderDialog,
    confirmCompleteInnerLibraryApplicationDialog,
    confirmCompleteOuterLibraryApplicationDialog,
    confirmCompleteBackSampleApplicationDialog,
    confirmCompleteMoveLibraryApplicationDialog,
    rejectDialog
  },
  mounted () {
    this.getData()
  },
  data () {
    return {
      form: {
        order: '',
        applicant: '',
        time: []
      },
      formInput: {
        order: '',
        applicant: '',
        time: []
      },
      currentId: '',
      page: 'sampleSearch',
      status: 'in', // 出入库， in || out
      progressFilterOptions: [
        {text: '待领取', value: '待领取'},
        {text: '处理中', value: '处理中'},
        {text: '已完成', value: '已完成'},
        {text: '部分完成', value: '部分完成'}
      ],
      orderStatusFilterValues: [],
      applicationType: '', // in || out, 出入库申请类型
      inAndOutDialogVisible: false,
      modifyOrderDialogVisible: false,
      confirmCompleteInnerLibraryApplicationDialogVisible: false,
      confirmCompleteMoveLibraryApplicationDialogVisible: false,
      confirmCompleteOuterLibraryApplicationDialogVisible: false,
      confirmCompleteBackSampleApplicationDialogVisible: false,
      rejectDialogVisible: false
    }
  },
  methods: {
    getData () {
      let time = this.form.time || []
      this.$ajax({
        url: '/sample/order/get_all_order_list',
        data: {
          orderNumber: this.form.order,
          fapplicantName: this.form.applicant,
          startDate: time[0],
          endDate: time[1],
          isApply: 1,
          fsampleOrderStatus: this.orderStatusFilterValues,
          pageRequest: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.records || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              orderStatus: v.fsampleOrderStatus,
              orderNum: v.fsampleOrderNumber,
              lab: v.flab,
              applicant: v.fapplicantName,
              operator: v.foperatorName,
              applicationTime: v.fapplicantTime,
              estimatedTime: v.expectedCompleteTime,
              completeTime: v.realCompleteTime,
              consumingTime: v.realWorkHours
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.form = {...this.formInput}
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.formInput = {
        order: '',
        time: [],
        applicant: ''
      }
      this.handleSearch()
    },
    // 筛选事件触发
    handleFilterTable (val) {
      if (val.orderStatus) {
        this.orderStatusFilterValues = []
        val.orderStatus.forEach(item => {
          this.orderStatusFilterValues.push(item)
        })
        this.handleSearch()
      }
    },
    // 领取
    async handleReceive (row) {
      if (row.orderNum.indexOf('入库') > -1) {
        this.$store.commit({
          type: 'old/setValue',
          category: 'enterLibraryOrder',
          enterLibraryOrder: row.orderNum
        })
        util.openNewPage('/business/sub/enterLibraryDetail?lab=' + row.lab)
      } else if (row.orderNum.indexOf('出库') > -1 || row.orderNum.indexOf('移库') > -1) {
        const isOut = row.orderNum.indexOf('出库') > -1
        await this.$confirm(`请确定是否领取${isOut ? '出库' : '移库'}申请单！`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        const url = isOut ? '/sample/order/receive_out_order' : '/sample/order/receive_move_order'
        this.receiveOrder(url, row.orderNum)
      }
    },
    // 移库和出库的领取
    receiveOrder (url, orderNum) {
      this.$ajax({
        url,
        method: 'get',
        data: {
          orderNumber: orderNum
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.handleDownloadOrder(orderNum)
          this.handleSearch()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 显示确认完成弹窗
    handleShowConfirmCompleteInnerLibraryApplicationDialog (row) {
      this.currentId = row.orderNum
      if (row.orderNum.indexOf('入库') > -1) {
        this.confirmCompleteInnerLibraryApplicationDialogVisible = true
      } else if (row.orderNum.indexOf('出库') > -1) {
        this.confirmCompleteOuterLibraryApplicationDialogVisible = true
      } else if (row.orderNum.includes('返样')) {
        this.confirmCompleteBackSampleApplicationDialogVisible = true
      } else if (row.orderNum.indexOf('移库') > -1) {
        this.confirmCompleteMoveLibraryApplicationDialogVisible = true
      }
    },
    // 显示驳回弹窗
    handleReject (row) {
      this.currentId = row.orderNum
      this.rejectDialogVisible = true
    },
    // 下载
    handleDownloadOrder (orderNum) {
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${orderNum}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    // 显示
    handleModifyOrder (row, page) {
      this.currentId = row.orderNum
      this.page = page
      this.modifyOrderDialogVisible = true
    },
    confirmCompleteDialogConfirm () {
      this.confirmCompleteOuterLibraryApplicationDialogVisible = false
      this.confirmCompleteInnerLibraryApplicationDialogVisible = false
      this.confirmCompleteMoveLibraryApplicationDialogVisible = false
      this.handleSearch()
    },
    handleModifyConfirm () {
      this.handleSearch()
      this.modifyOrderDialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
  .form-container{
    display: flex;
    justify-content: space-between;
    margin: 20px 0 0 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #ccc;
    .form{
      display: flex;
      .form-item{
        display: flex;
        align-items: center;
        margin-right: 20px;
        label{
          width: 5em;
          flex-shrink: 0;
        }
      }
    }
  }
</style>
