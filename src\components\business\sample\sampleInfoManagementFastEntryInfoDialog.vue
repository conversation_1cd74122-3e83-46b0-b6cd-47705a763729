<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="样例信息录入"
      width="500px"
      @open="handleOpen">
      <el-form :model="form" :rules="rules" ref="ruleForm" size="mini" label-width="100px">
        <el-form-item label="样本编号" prop="sampleCode">
          <el-input v-model="form.sampleCode"></el-input>
        </el-form-item>
        <el-form-item label="身份证/护照">
          <el-input v-model="form.idCard"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="原始编号">
          <el-input v-model="form.orderNum"></el-input>
        </el-form-item>
        <el-form-item label="检测产品" prop="detectProduct">
          <el-select v-model="form.detectProduct" placeholder="请选择检测产品">
            <template v-for="item in productNameLists">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="送检单位" prop="unit">
          <el-select v-model="form.unit" placeholder="请选择送检单位">
            <template v-for="item in unitLists">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="送检日期" prop="submitDate">
          <el-date-picker v-model="form.submitDate" type="date" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-select v-model="form.productName" placeholder="请选择项目名称">
            <template v-for="item in productNameLists">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.notes" type="textarea"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSaveAndAdd">保存并继续添加</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button @click="visible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'sampleInfoManagementFastEntryInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      form: {
        sampleCode: '',
        idCard: '',
        name: '',
        originNum: '',
        detectProduct: '',
        unit: '',
        submitDate: '',
        productName: '',
        notes: ''
      },
      unitLists: [], // 送检单位列表
      detectProductLists: [], // 检测产品列表
      productNameLists: [], // 产品名称列表
      rules: {
        sampleCode: [
          { required: true, message: '请输入样本编号', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '身份证/护照不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        detectProduct: [
          { required: true, message: '请选择检测产品', trigger: 'change' }
        ],
        unit: [
          { required: true, message: '请选择送检单位', trigger: 'change' }
        ],
        submitDate: [
          { required: true, message: '请选择样本编号', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      setTimeout(() => {
        this.$refs.ruleForm.resetFields()
      })
    },
    // 保存并继续添加
    handleSaveAndAdd () {
      this.submitInfo().then(() => {
        this.$refs.ruleForm.resetFields()
      })
    },
    // 保存
    handleSave () {
      this.submitInfo().then(() => {
        this.visible = false
      })
    },
    // 提交信息
    submitInfo () {
      return new Promise((resolve) => {
        this.$ajax({
          url: '/sample/basic/fast_save_sample',
          data: {
            sampleNum: this.form.sampleCode,
            idcard: this.form.idCard,
            name: this.form.name,
            originNum: this.form.originNum,
            productIds: this.form.detectProduct,
            customerId: this.form.unit,
            inspectionTime: this.form.submitDate,
            projectId: this.form.productName,
            remark: this.form.notes
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            resolve()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
