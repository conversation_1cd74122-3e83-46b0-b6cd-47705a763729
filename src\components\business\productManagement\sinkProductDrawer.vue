<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :wrapper-closable="false"
      :size="'40%'"
      :with-header="false"
      @open="handleOpen">
      <el-form :model="form" style="padding: 20px 5px" label-position="right" label-width="120px" label-suffix=":">
        <el-form-item label="产品分类编码">
          {{form.fproductTypeCode}}
        </el-form-item>
        <el-form-item label="产品分类名称">
          {{form.fproductTypeName}}
        </el-form-item>
        <el-form-item label="产品/套餐编码">
          {{form.fproductComboType}}
        </el-form-item>
        <el-form-item label="产品/套餐名称">
          {{form.fproductComboName}}
        </el-form-item>
        <div class="title">产品</div>
        <el-form-item label="产品系列">
          {{form.fproductSeries}}
        </el-form-item>
        <el-form-item label="产品线">
          {{form.fproductLine}}
        </el-form-item>
<!--        <el-form-item label="适用客户类型">-->
<!--          <el-select size="mini" multiple v-model.trim="form.fusePeople" disabled>-->
<!--            <el-option></el-option>-->

<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="产品类型">
          <el-radio-group v-model.trim="form.fcomboType" size="mini" disabled>
            <el-radio label="0">产品</el-radio>
            <el-radio label="1">套餐</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="title">产品基本信息</div>
        <el-form-item label="是否审核">
          {{form.fisCheck}}
        </el-form-item>
        <el-form-item label="检测样本">
          {{form.ftestSampleType}}
        </el-form-item>
        <el-form-item label="基因数目">
          {{form.fgeneNum}}
        </el-form-item>
        <el-form-item label="适用人群">
          {{form.fusePeople}}
        </el-form-item>
        <el-form-item label="产品字母条码">
          {{form.fproductBarcode}}
        </el-form-item>
        <el-form-item label="生产片区">
          {{form.fprocedureArea}}
        </el-form-item>
        <el-form-item label="靶向药物数目">
          {{form.ftargetDrugNum}}
        </el-form-item>
        <el-form-item label="产品描述">
          {{form.fproductDes}}
        </el-form-item>
        <el-form-item label="癌种">
          {{form.fcancerName}}
        </el-form-item>
        <el-form-item label="备注">
          {{form.fnote}}
        </el-form-item>
        <div class="title">其他</div>
        <div style="display: flex;align-items: center;justify-content: space-around;margin: 10px 0">
          <span>创建人: {{form.fcreator}}</span>
          <span>事业部：{{form.fworkPart}}</span>
          <span>创建时间：{{form.fcreateTime}}</span>
        </div>
        <div class="button-wrapper" style=""><el-button size="mini" @click="handleClose">返回</el-button></div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'sinkProductDrawer',
  mixins: [mixins.dialogBaseInfo],
  props: {
    fid: {
      type: Number
    }
  },
  data () {
    return {
      form: {
        productCategoryCode: '',
        tableData: []
      }
    }
  },
  methods: {
    handleOpen () {
      console.log(this.fid)
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/system/otherProduct/get_downstream_market_product_detail',
        method: 'get',
        data: {
          fotherProductId: this.fid
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form = {
            fid: data.fid,
            fproductTypeCode: data.fproductTypeCode,
            fproductTypeName: data.fproductTypeName,
            fproductComboType: data.fproductComboType,
            fproductComboName: data.fproductComboName,
            fproductCode: data.fproductCode,
            fproductName: data.fproductName,
            fshortName: data.fshortName,
            fcomboType: data.fcomboType === '0' ? '0' : '1',
            fproductSeries: data.fproductSeries,
            fproductLine: data.fproductLine,
            fisCheck: data.fisCheck === 0 ? '否' : '是',
            ftestSampleType: data.ftestSampleType,
            fgeneNum: data.fgeneNum,
            fusePeople: data.fusePeople,
            fproductBarcode: data.fproductBarcode,
            fprocedureArea: data.fprocedureArea,
            ftargetDrugNum: data.ftargetDrugNum,
            fproductDes: data.fproductDes,
            fcancerName: data.fcancerName,
            fnote: data.fnote,
            fupdateNote: data.fupdateNote,
            fstate: data.fstate,
            fcreator: data.fcreator,
            fcreateTime: data.fcreateTime,
            fworkPart: data.fworkPart,
            fupdator: data.fupdator,
            fupdateTime: data.fupdateTime
          }
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    }
  }
}
</script>

<style scoped>
.title{
  margin: 20px 0 10px;
  padding: 0 10px;
  height: 35px;
  line-height: 35px;
  border-bottom: 1px solid #DCDFE6;
}

>>>.el-form-item {
  margin: 10px;
  color: #606266;
}
/*解决办法：*/
  /*1.显示滚动条：当内容超出容器的时候，可以拖动：*/
>>>.el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}

/*2.隐藏滚动条，太丑了*/
>>>.el-drawer__container ::-webkit-scrollbar {
  display: none;
}
.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 5px;
  height: 40px;
  width: 100%;
  box-shadow: 19px -2px 26px -8px rgba(49,44,44,0.75);
}
</style>
