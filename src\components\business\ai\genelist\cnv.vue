<template>
  <div>
    <div class="btn">
      <el-button size="mini" type="primary" disabled>新增基因变异与药物疗效</el-button>
      <el-button size="mini" type="primary" disabled>标记报出</el-button>
      <el-button size="mini" type="primary" disabled>取消报出</el-button>
      <el-button size="mini" type="primary" disabled>提交验证数据</el-button>
      <el-button size="mini" type="primary" disabled>查看验证详情</el-button>
      <el-button size="mini" type="primary" disabled>删除</el-button>
      <el-button size="mini" type="primary" disabled>合并添加</el-button>
      <el-button type="primary" size="mini" disabled>突变备注</el-button>
    </div>
    <div class="card-wrapper">
      <el-table
        :data="tableData" ref="table" class="table"
        size="mini"
        border
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column label="比对结果" prop="canComparison" min-width="120px"></el-table-column>
        <el-table-column label="Link_local" prop="name" min-width="120px"></el-table-column>
        <el-table-column label="Gene" prop="gene" min-width="120px"></el-table-column>
        <el-table-column label="AvgRatio" prop="avgRatio" min-width="120px"></el-table-column>
        <el-table-column label="Transcript" prop="transcript" min-width="120px"></el-table-column>
        <el-table-column key="4-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
          <template slot-scope="scope">
            <el-tooltip placement="top">
              <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
              <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

export default {
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {statusCode, data} = await this.$ajax({
        url: '/read/bigAi/get_cnv_list'
      })
      if (statusCode && statusCode === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.data = []
        rows.forEach(v => {
          let item = {
            canComparison: v.fcanComparison,
            isRead: v.fcanComparison,
            report: v.freport,
            inProductCheckRange: v.inProductCheckRange,
            dxAttr: v.fdxAttr,
            fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
            readsPlot: v.freadsPlot,
            gene: v.fgene,
            state: v.fstate,
            ratio: v.fratio,
            avgRatio: v.favgRatio,
            tumDepthOri: v.ftumDepthOri,
            qual: v.fqual,
            transcript: v.ftranscript,
            exon: v.fexon,
            lengthPercent: v.flengthPercent,
            maploc: v.fMaploc,
            chr: v.fchr,
            start: v.fstart,
            end: v.fend,
            autoInterpStatus: v.fautoInterpStatus
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.data.push(item)
        })
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
