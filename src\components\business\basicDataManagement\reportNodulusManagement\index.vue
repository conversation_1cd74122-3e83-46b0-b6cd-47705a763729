<template>
  <div>
    <!--    功能区-->
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('002017001', 'buttons')" type="primary" size="mini" @click="handleConfiguration(0)">查看详情</el-button>
      <el-button v-if="$setAuthority('002017002', 'buttons')" type="primary" plain size="mini" @click="handleConfiguration(1)">配置</el-button>
      <el-button v-if="$setAuthority('002017003', 'buttons')" type="primary" plain size="mini" @click="handleConfiguration(2)">复制新增</el-button>
      <el-button v-if="$setAuthority('002017005', 'buttons')" type="primary" plain size="mini" @click="handleConfigurationCustom">批量配置</el-button>
      <el-button v-if="$setAuthority('002017004', 'buttons')" type="primary" plain size="mini" @click="handleDelete">删除</el-button>
    </div>
    <!--    列表-->
    <el-table
      :data="tableData"
      ref="table"
      height="calc(100vh - 74px - 40px - 42px - 32px)"
      border
      class="table"
      size="mini"
      @select="handleSelectTable"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="序号" type="index" show-overflow-tooltip></el-table-column>
      <el-table-column label="癌种" prop="cancer" show-overflow-tooltip></el-table-column>
      <el-table-column label="建立者" prop="creator" show-overflow-tooltip></el-table-column>
      <el-table-column label="建立时间" prop="createTime" show-overflow-tooltip></el-table-column>
      <el-table-column label="修改者" prop="updator" show-overflow-tooltip></el-table-column>
      <el-table-column label="修改时间" prop="updateTime" show-overflow-tooltip></el-table-column>
    </el-table>
    <!--分页-->
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      style="background: #ffffff;"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
    </el-pagination>
    <report-nodulus-dialog
      :pvisible.sync="visible"
      :cancer-name="cancerName"
      :type="type"
      id="id"
      @dialogConfirmEvent="getData"
      @dialogCloseEvent="getData"></report-nodulus-dialog>
    <configuration-custom-dialog :pvisible.sync="configurationCustomVisible" @dialogConfirmEvent="getData">
    </configuration-custom-dialog>
  </div>
</template>

<script>
import util from '../../../../util/util'
import ReportNodulusDialog from './reportNodulusDialog'
import mixins from '../../../../util/mixins'
import ConfigurationCustomDialog from './configurationCustomDialog.vue'

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {ConfigurationCustomDialog, ReportNodulusDialog},
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: [], // 表格数据
      selectedRows: new Map(),
      visible: false,
      configurationCustomVisible: false,
      cancerName: '',
      type: 0, // 弹窗类型
      id: 0 // 报告小结id
    }
  },
  methods: {
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/system/summaryReport/get_cancer_report_infos',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
      })
      if (code === this.SUCCESS_CODE) {
        this.selectedRows = new Map()
        this.totalPage = data.total
        this.selectedRows = new Map()
        let rows = data.rows || []
        this.tableData = []
        rows.forEach(v => {
          let item = {
            id: v.id,
            cancer: v.cancer,
            creator: v.creator,
            createTime: v.createTime,
            updator: v.updator,
            updateTime: v.updateTime
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    /**
     * 配置報告小結
     * @param type 0: 查看詳情 1：配置 2：複製新增
     */
    async handleConfiguration (type) {
      this.cancerName = ''
      let rows = [...this.selectedRows.values()]
      this.type = type
      if (this.selectedRows.size !== 0) {
        this.id = rows[0].id
        this.cancerName = rows[0].cancer
      }
      if (type === 2) {
        if (this.selectedRows.size !== 1) {
          this.$message.error('请选择一条数据')
          return
        }
        let {value} = await this.$prompt('请输入新癌种名称', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (info) => {
            if (!info) return '请输入新癌种名称'
          }
        })
        let {code, message} = await this.$ajax({
          url: '/system/summaryReport/add_by_copy',
          data: {
            copyCancer: rows[0].cancer,
            newCancer: value
          }
        })
        if (code === this.SUCCESS_CODE) {
          this.cancerName = value
        } else {
          this.$message.error(message)
          return
        }
      }
      this.visible = true
    },
    // 批量配置客户
    handleConfigurationCustom () {
      this.configurationCustomVisible = true
    },
    // 删除癌种
    async handleDelete () {
      let rows = [...this.selectedRows.values()]
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let {cancer} = rows[0]
      await this.$confirm(`确定删除癌种配置: ${cancer}<div style="font-size: 12px; color: #888">（此操作无法撤销，在继续之前，请确保您了解其影响）?</div>`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
      let {code, message} = await this.$ajax({
        url: '/system/summaryReport/delete_cancer_report_info',
        data: {
          cancer: cancer
        }
      })
      if (code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
        this.getData()
      } else {
        this.$message.error(message)
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped></style>
