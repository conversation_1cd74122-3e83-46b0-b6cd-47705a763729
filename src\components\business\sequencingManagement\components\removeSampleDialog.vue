<template>
<div>
  <el-dialog
    v-drag-dialog
    :close-on-click-modal="false"
    :visible.sync="visible"
    :before-close="handleClose"
    title="提示"
    width="800px"
    @open="handleOpen">
    <div class="tips">
      移除样本后不可恢复，确认继续移除以下{{removeInfo.length}}个样本？
    </div>
    <div class="sample-list">
      <el-table
        ref="table"
        :data="removeInfo"
        class="table"
        size="mini"
        border
        style="width: 100%"
        height="200px"
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="实验样本 " prop="sampleName" show-overflow-tooltip></el-table-column>
        <el-table-column label="核酸/吉因加编号" prop="geneCode" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.geneCode !== '查看样本详情'">{{scope.row.geneCode}}</span>
            <div v-else class="link" @click="handleDetail(scope.row)">{{scope.row.geneCode}}</div>
          </template>
        </el-table-column>
        <el-table-column label="原始样本名称" prop="oldSampleName" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
    <el-form ref="form" :model="form" label-suffix=":" :rules="rules" label-width="80px">
      <el-form-item label="移除原因" prop="reason">
        <el-input v-model.trim="form.reason" maxlength="200" size="mini" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
  </el-dialog>
</div>
</template>

<script>
import mixins from '../../../../util/mixins'
import {awaitWrap} from '../../../../util/util'
import {removeSample} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'removeSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    removeInfo: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      loading: false,
      form: {
        reason: ''
      },
      rules: {
        reason: [
          {required: true, message: '请输入移除原因', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.tableData = []
        this.$refs.form.resetFields()
      })
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      let {res} = await awaitWrap(removeSample({
        fidList: [...this.removeInfo.map(v => v.id)],
        fremoveReason: this.form.reason
      }, this.type))
      if (res && res.code === this.SUCCESS_CODE) {
        this.visible = false
        this.$message.success('移除成功')
        this.$emit('dialogConfirmEvent')
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  margin: 3px 0;
  font-size: 12px;
  font-weight: 300;
  color: #666;
}
.sample-list {
  margin: 10px 0;
}
</style>
