<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="确认完成"
      width="80%"
      @open="handleOpen">
      <div v-loading="loading">
        <el-form
            v-if="isDispatchOrder && visible"
            ref="form"
            :model="form"
            :rules="rules"
            size="mini"
            label-position="right">
          <el-form-item label="快递单号：" prop="trackingNum">
            <el-input v-model.trim="form.trackingNum" style="width: 150px;"></el-input>
          </el-form-item>
        </el-form>
        <el-table
          :data="tableData"
          ref="sampleTable"
          style="width: 100%;"
          height="350">
          <el-table-column type="selection" width="55" :selectable="tableCanSelect"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本状态" width="100" prop="fsampleStatus"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本编号" width="180" prop="fsampleNumber"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本类型" min-width="220" prop="fsampleType"></el-table-column>
          <el-table-column show-overflow-tooltip label="管型" width="100" prop="ftubeType"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本量" width="100" prop="fsampleAmount"></el-table-column>
          <el-table-column show-overflow-tooltip label="所属实验室" width="150" prop="flab"></el-table-column>
          <el-table-column show-overflow-tooltip label="存储温度" width="100" prop="ftemperature"></el-table-column>
          <el-table-column show-overflow-tooltip label="存储位置" min-width="180" prop="fsamplePlace"></el-table-column>
          <el-table-column show-overflow-tooltip label="是否异常" width="100">
            <template slot-scope="scope">
              <span :style="{color: scope.row.fisException ? '#F56C6C' : '#67C23A'}">{{scope.row.fisException ? '是' : '否'}}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="异常备注" min-width="180" prop="fexceptionNotes">
            <template slot-scope="scope">
              <el-input
                v-model.trim="scope.row.fexceptionNotes"
                v-if="scope.row.fsampleStatus === '处理中' && scope.row.fisException === 1"
                size="mini"></el-input>
              <span v-else>{{scope.row.fexceptionNotes}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.fsampleStatus === '处理中'"
                type="text"
                @click="handleMark(scope.row)">{{scope.row.fisException ? '取消' : ''}}标记异常</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="line-height: 2.5;">
          共{{tableData.length}}条,已选{{hasChooseNum}}条
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
// import util from '../../../util/util'
// import constants from '../../../util/constants'
import mixins from '../../../util/mixins'
export default {
  name: 'confirmCompleteOuterLibraryApplicationDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: String | Number
    }
  },
  computed: {
    isDispatchOrder () {
      return this.orderId.includes('调度出库')
    }
  },
  data () {
    return {
      form: {
        trackingNum: ''
      },
      rules: {
        trackingNum: [
          {required: false, message: '请输入', trigger: 'blur'}
        ]
      },
      tableData: [],
      currentPosition: '',
      paraffinCabinetLocationLists: [], // 石蜡包埋组织位置
      selectedRows: new Map(),
      hasChooseNum: 0, // 已选行数
      loading: false
    }
  },
  methods: {
    handleOpen () {
      this.paraffinCabinetLocationLists = []
      this.hasChooseNum = 0
      this.tableData = []
      this.form = {
        trackingNum: ''
      }
      if (this.orderId) {
        this.$ajax({
          url: '/sample/order/get_sample_list_by_order_number',
          method: 'get',
          data: {
            orderNumber: this.orderId
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            let data = res.data || []
            this.tableData = []
            data.forEach(item => {
              let v = {
                ...item
              }
              this.tableData.push(v)
            })
            this.$nextTick(() => {
              this.$refs.sampleTable.clearSelection()
              this.selectedRows.clear()
              this.tableData.forEach(item => {
                if (item.fsampleStatus === '处理中') {
                  this.selectedRows.set(item.fid, item)
                  this.$refs.sampleTable.toggleRowSelection(item, true)
                }
              })
              this.hasChooseNum = this.tableData.length
              this.getParaffinCabinetLocationLists()
            })
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 可否选中
    tableCanSelect (row) {
      return row.fsampleStatus === '处理中'
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
      this.hasChooseNum = this.selectedRows.size
      this.getParaffinCabinetLocationLists()
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fid, row)
      })
      this.hasChooseNum = this.selectedRows.size
      this.getParaffinCabinetLocationLists()
    },
    // 获取石蜡柜位置列表
    getParaffinCabinetLocationLists () {
      this.paraffinCabinetLocationLists = []
      let dataSet = new Set()
      let data = [...this.selectedRows.values()]
      data.forEach(item => {
        if (item.fsamplePlace && item.fsampleType === '石蜡包埋组织') {
          dataSet.add(item.fsamplePlace)
        }
      })
      this.paraffinCabinetLocationLists.push(...dataSet)
    },
    validateForm () {
      return new Promise(resolve => {
        const hasNormal = this.tableData.some(v => !v.fisException)
        if (hasNormal && !this.form.trackingNum) {
          this.$message.error('请填写快递单号')
          return
        }
        resolve()
      })
    },
    // 确认
    async handleDialogConfirm () {
      if (this.isDispatchOrder) {
        await this.validateForm()
      }
      let data = [...this.selectedRows.values()]
      if (this.isDispatchOrder) {
        data.forEach(v => {
          v.frecipientCourierNum = this.form.trackingNum
        })
      }
      const params = {
        orderNumber: this.orderId,
        sampleInfoList: data,
        paraffinCabinetPlace: this.paraffinCabinetLocationLists
      }
      this.loading = true
      this.$ajax({
        url: '/sample/order/confirm_complete_order',
        data: params
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('提交成功')
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 输入异常弹窗关闭
    handleMark (row) {
      row.fisException = row.fisException === 1 ? 0 : 1
    }
  }
}
</script>

<style scoped>

</style>
