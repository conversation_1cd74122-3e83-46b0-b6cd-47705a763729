import { myAjax } from '@/util/ajax'

/**
 * 获取待测样本
 * @param data http://172.16.50.15:3000/repository/editor?id=82
 * @param options
 * @returns {*}
 */
export function getUnTestSampleList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/wait_detect_sample/query_data',
    data: data,
    ...options
  })
}

/**
 * 设置备注
 * @param data http://172.16.50.15:3000/repository/editor?id=82
 * @param options
 * @returns {*}
 */
export function setRemark (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/wait_detect_sample/add_remark',
    data: data,
    ...options
  })
}
/**
 * 设置备注
 * @param data http://172.16.50.15:3000/repository/editor?id=82
 * @param options
 * @returns {*}
 */
export function signSample (data, options = {}) {
  return myAjax({
    method: 'post',
    isFormData: true,
    url: '/experiment/wait_detect_sample/mark_complete',
    data: data,
    ...options
  })
}
/**
 * 设置备注
 * @param data http://172.16.50.15:3000/repository/editor?id=82
 * @param options
 * @returns {*}
 */
export function cancelSignSample (data, options = {}) {
  return myAjax({
    method: 'post',
    isFormData: true,
    url: '/experiment/wait_detect_sample/cancel_mark_complete',
    data: data,
    ...options
  })
}

/**
 * 设置备注
 * @param data http://172.16.50.15:3000/repository/editor?id=82
 * @param options
 * @returns {*}
 */
export function downInspection (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/wait_detect_sample/download_inspection',
    data: data,
    responseType: 'blob',
    ...options
  })
}

/**
 * 设置备注
 * @param data http://172.16.50.15:3000/repository/editor?id=82
 * @param options
 * @returns {*}
 */
export function downUnInspection (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/wait_detect_sample/download_no_confirm',
    data: data,
    responseType: 'blob',
    ...options
  })
}

/**
 * 下载模板
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function downloadTemplate (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/wait_detect_sample/download_mark_template',
    responseType: 'blob',
    data: data,
    ...options
  })
}

/**
 * 下载模板
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function getSeqTimes (data, options = {}) {
  return myAjax({
    url: '/experiment/wait_detect_sample/seq_times_detail',
    data: data,
    ...options
  })
}
