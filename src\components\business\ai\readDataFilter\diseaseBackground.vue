<template>
  <div>
    <div style="margin:10px;">
      <el-button type="primary" size="mini" @click="handleFixBackground">修改</el-button>
      <el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
    </div>
    <div class="card-wrapper">
      <el-table
        :data="tableData"
        ref="table"
        class="table"
        size="mini"
        border
        height="calc(100vh - 40px - 154px - 32px - 20px - 20px - 20px)"
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column label="基因名称" prop="gene" min-width="120px"></el-table-column>
        <el-table-column label="匹配癌种" prop="cancerClass" min-width="120px"></el-table-column>
        <el-table-column label="性别" prop="gender" min-width="120px"></el-table-column>
        <el-table-column label="遗传性肿瘤综合征" prop="relatedDisease" min-width="120px"></el-table-column>
        <el-table-column label="遗传方式" prop="geneticMode" min-width="120px"></el-table-column>
        <el-table-column label="疾病背景" prop="geneBackground" show-overflow-tooltip min-width="120px"></el-table-column>
      </el-table>
    </div>
    <fix-disease-background-diglog
      :pvisible.sync="diseaseVisible"
      :genetic-description-id="geneticDescriptionId"
      :sick-rids-man="sickRidsMan"
      :sick-rids-woman="sickRidsWoman"
      @dialogConfirmEvent="getData"
    ></fix-disease-background-diglog>
  </div>
</template>

<script>

import FixDiseaseBackgroundDiglog from './fixDiseaseBackgroundDiglog'
export default {
  components: {FixDiseaseBackgroundDiglog},
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: [],
      geneticDescriptionId: 0,
      diseaseVisible: false,
      selectedRows: new Map()
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_report_h_sick_list',
        loadingDom: '.table',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.selectedRows = new Map()
        this.tableData = []
        rows.forEach(v => {
          let item = {
            id: v.fid,
            gene: v.gene,
            cancerClass: v.cancerClass,
            gender: v.gender,
            relatedDisease: v.relatedDisease,
            geneticDescriptionId: v.geneticDescriptionId,
            geneticMode: v.geneticMode,
            sickRidsMan: v.sickRidsMan,
            sickRidsWoman: v.sickRidsWoman,
            geneBackground: v.geneBackground
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    },
    // 修改疾病背景
    handleFixBackground () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let data = [...this.selectedRows.values()][0]
      this.geneticDescriptionId = data.geneticDescriptionId
      this.sickRidsMan = data.sickRidsMan
      this.sickRidsWoman = data.sickRidsWoman
      this.diseaseVisible = true
    },
    // 删除疾病背景
    async handleDelete () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let geneticCancerIds = [...this.selectedRows.values()].map(v => v.id).join(',')
      let {code, message} = await this.$ajax({
        url: '/read/bigAi/delete_sick_data',
        data: {
          geneticCancerIds: geneticCancerIds,
          analysisRsId: this.analysisRsId
        }
      })
      if (code && code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
        this.getData()
      } else {
        this.$message.error(message)
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
