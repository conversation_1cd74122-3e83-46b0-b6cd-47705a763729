<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="设置层"
      width="700px"
      @open="handleOpen">
      <div class="form">
        <div class="form-item">
          <label>容器名称：</label>
          <el-input :style="{width: formWidth}" v-model="currentOperateName" disabled size="mini"></el-input>
        </div>
        <div class="form-item">
          <label>添加层数：</label>
          <el-select v-model="floorTotal" :style="{width: formWidth}" size="mini" filterable>
            <template v-for="item in maxFloorNum">
              <el-option :key="item" :label="item" :value="item"></el-option>
            </template>
          </el-select>
        </div>
        <template v-if="floorTotal">
          <template v-for="(item, index) in floorSampleTypeInput">
            <div :key="index" class="form-item">
              <label>层数范围：</label>
              <el-input :style="{width: formWidth}" v-model="item.floorNums" size="mini"></el-input>
            </div>
            <div :key="index + 'sample'" class="form-item">
              <label>样本类型：</label>
              <el-select
                v-model="item.sampleType"
                :disabled="item.floorNums.length === 0"
                :style="{width: formWidth}"
                size="mini"
                multiple
                filterable>
                <template v-for="item in sampleTypeOptions">
                  <el-option :key="item.value" :label="item.value" :value="item.value"></el-option>
                </template>
              </el-select>
            </div>
            <div :key="index + 'icon'">
              <i
                v-if="index === floorSampleTypeInput.length - 1"
                class="el-icon-plus icon"
                style="font-size: 30px;font-weight: 600;cursor: pointer;"
                @click="handleAddFloorSampleType"></i>
              <i
                v-if="floorSampleTypeInput.length !== 1"
                class="el-icon-minus icon"
                style="font-size: 30px;font-weight: 600;cursor: pointer;"
                @click="handleSpliceFloorSampleType(index)"></i>
            </div>
          </template>
        </template>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../util/mixins'
export default {
  name: 'setFloorDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    currentSetObj: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
      currentOperateName: '容器',
      formWidth: '150px',
      floorTotal: '',
      maxFloorNum: 9,
      sampleTypeOptions: [
        {value: '外周血'},
        {value: '血液'},
        {value: '石蜡'},
        {value: '肾'},
        {value: '血浆'},
        {value: '胆汁'}
      ],
      floorSampleTypeInput: [
        {floorNums: '', sampleType: []}
      ]
    }
  },
  methods: {
    handleOpen () {
      let form = this.currentSetObj.formInput
      if (form.sampleTypeInput) {
        console.log(form)
        this.floorTotal = form.content.length
        this.floorSampleTypeInput = form.sampleTypeInput
      } else {
        this.floorTotal = ''
        this.floorSampleTypeInput = [
          {floorNums: '', sampleType: []}
        ]
      }
    },
    handleAddFloorSampleType () {
      this.floorSampleTypeInput.push({floorNums: '', sampleType: []})
    },
    handleSpliceFloorSampleType (i) {
      this.floorSampleTypeInput.splice(i, 1)
    },
    // 将输入值转换为数组
    changInputToArr (input, maxNum = 9) {
      let regx = /[1-9]([\s+,，\\-]*[0-9])*$/
      if (regx.test(input)) {
        input = input.replace(/\s+/g, ',').replace(/，/, ',').replace(/(\s+-\s+)|(\s+-)|(-\s+)/, '-')
        // 转换字符串为数组,去除空项
        let f = input.split(',').filter(item => { return item })
        let floorNumsSet = new Set()
        let numCorrect = true
        for (let i = 0; i < f.length; i++) {
          if (f[i].indexOf('-') > -1) {
            let fArr = f[i].split('-').filter(item => { return item })
            if (fArr.length !== 2) {
              this.$message.error('输入格式不正确')
              numCorrect = false
              break
            } else {
              let correctNum = fArr.every(v => {
                let num = +v
                return !Number.isNaN(num) && num > 0 && num <= maxNum
              })
              if (correctNum) {
                let arr = fArr.map(v => { return +v })
                let max = Math.max(...arr)
                let min = Math.min(...arr)
                if (max <= maxNum && min > 0) {
                  let foolA = []
                  do {
                    foolA.push(min)
                    min++
                  } while (min <= max)
                  foolA.forEach(item => {
                    floorNumsSet.add(item)
                  })
                } else {
                  this.$message.error(`请确保输入的值在1-${maxNum}之间`)
                  numCorrect = false
                  break
                }
              } else {
                this.$message.error('输入格式不正确')
                numCorrect = false
                break
              }
            }
          } else {
            let num = +f[i]
            if (!Number.isNaN(num) && num > 0 && num <= maxNum) {
              floorNumsSet.add(num)
            } else {
              this.$message.error('输入格式不正确')
            }
          }
        }
        if (numCorrect) {
          return [...floorNumsSet]
        } else {
          return false
        }
      } else {
        this.$message.error('盒子序号格式不正确')
        return false
      }
    },
    handleConfirm () {
      let sampleTypeInput = []
      let inputCorrect = true
      for (let i = 0; i < this.floorSampleTypeInput.length; i++) {
        let floorNums = this.changInputToArr(this.floorSampleTypeInput[i].floorNums, this.floorTotal)
        if (!floorNums) {
          inputCorrect = false
          break
        }
        if (this.floorSampleTypeInput[i].sampleType.length === 0) {
          this.$message.error('样本类型不能为空')
          inputCorrect = false
          break
        }
        let v = {
          floorNums: floorNums,
          sampleType: this.floorSampleTypeInput[i].sampleType
        }
        sampleTypeInput.push(v)
      }
      if (inputCorrect) {
        let floorMap = new Map()
        sampleTypeInput.forEach(item => {
          item.floorNums.forEach(v => {
            let vv = {
              tag: '层',
              filed: 'floor',
              num: v,
              sampleType: item.sampleType,
              children: []
            }
            floorMap.set(v, vv)
          })
        })
        if (floorMap.size !== this.floorTotal) {
          this.$message.error('层数与设置的层数不一致')
          return
        }
        let floor = [...floorMap.values()]
        this.$emit('dialogConfirmEvent', floor, sampleTypeInput)
      }
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  .form{
    display: flex;
    flex-wrap: wrap;
    .form-item{
      margin-bottom: 20px;
      margin-right: 20px;
    }
  }
</style>
