<template>
  <div class="page">
    <div v-if="showRouter">
<!--      <my-header :current-lab-options="labOptions" :currentlab="lab"></my-header>-->
      <keep-alive>
        <router-view v-if="showRouter"/>
      </keep-alive>
    </div>
    <div v-else style="height: calc(100vh - 64px)"></div>
    <!--<aside-menu>-->
      <!--<router-view />-->
    <!--</aside-menu>-->
  </div>
</template>

<script>
// import commonHeader from '@/components/common/header'
import asideMenu from './layout/asideMenu'
import myHeader from './layout/header'
import Vue from 'vue'
import util from '../util/util'
import Cookies from 'js-cookie'
import constants from '../util/constants'

export default {
  components: {
    asideMenu,
    myHeader
  },
  mounted () {
    console.log('用户信息获取中...')
    // 下面这段在测试的时候加
    if (constants.IS_TEST) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'loginId',
        loginId: 'MTM1'
      })
    }
    if (this.$route.query.oxym) {
      Cookies.set('x-lims-token', this.$route.query.oxym)
    }
    this.getUserInfo()
  },
  computed: {
    userId () {
      return this.$store.getters.getValue('loginId')
    }
  },
  data () {
    return {
      showRouter: false,
      labOptions: [],
      lab: []
    }
  },
  methods: {
    getUserInfo () {
      // Cookies.set('x-lims-token', '1a8eea2fea5847a2ba2bdd6a99204e3b')
      this.showRouter = false
      this.$ajax({
        url: '/user/get_authority_and_name',
        data: {},
        loadingDom: '.page',
        loadingObject: {
          text: '正在获取信息',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          // util.setSessionInfo('hasLogin', true)
          let name = res.data.userName
          let id = res.data.userId
          let emailPrefix = res.data.emailPrefix
          let resources = {
            modules: [],
            menus: [],
            buttons: []
          }
          this.iterateDecorateResource(resources, res.data.resources)
          util.setSessionInfo('resource', resources)
          Vue.prototype.$myresource = resources
          let area = res.data.area || []
          this.lab = []
          this.labOptions = []
          // 用户已经设置了的实验室
          let hasSetLab = util.getSessionInfo('currentLab') || []
          let areaIds = area.filter(v => v.production_area_id) || []
          let labs = hasSetLab.filter(v => areaIds.indexOf(v)) // 清除在资源里面没有的项
          area.forEach(item => {
            let v = {
              label: item.production_area_name,
              value: item.production_area_id
            }
            this.labOptions.push(v)
            if (item.is_default === 1) this.lab.push(item.production_area_id)
          })
          util.setSessionInfo('currentLab', (hasSetLab && hasSetLab.length > 0) ? labs : this.lab)
          Cookies.set('flab', (hasSetLab && hasSetLab.length > 0) ? labs.toString() : this.lab.toString())
          Cookies.set('labOptions', JSON.stringify(this.labOptions))
          this.$store.commit({
            type: 'old/setValue',
            category: 'userInfo',
            userInfo: {
              name,
              id,
              avatar: '',
              emailPrefix: emailPrefix
            }
          })
          this.showRouter = true
          resources = this.dealResource(res.data.resources || [])
          this.$store.dispatch('setLoginData', {
            userInfo: {
              name,
              id,
              avatar: '',
              emailPrefix: emailPrefix
            },
            token: Cookies.get('x-lims-token'),
            resources
          })
        } else {
          this.$message({
            type: 'error',
            message: res.message,
            duration: 5000
          })
          // setTimeout(() => {
          //   this.$router.push({path: '/500'})
          // }, 5000)
        }
      }).catch(() => {
        // setTimeout(() => {
        //   this.$router.push({path: '/500'})
        // }, 3000)
      })
    },
    // 处理登录资源数据
    dealResource (resources) {
      let menuList = []
      const btnList = []
      const pathList = []
      this.iterateDecorateNewResource(menuList, btnList, pathList, resources)
      menuList = menuList.filter(this.judgeHasFrontModule)
      menuList = menuList.map(menu => {
        menu.children = menu.children.filter(v => {
          v.url = v.url || ''
          return v.url.includes('xfront')
        }).map(v => {
          v.url = v.url || ''
          v.url = v.url.replace('xfront', '')
          return v
        })
        menu.children = menu.children.sort((pre, next) => pre.order - next.order)
        return menu
      })
      return {
        menuList: menuList.sort((pre, next) => pre.order - next.order),
        btnList,
        pathList
      }
    },
    judgeHasFrontModule (MODULE) {
      const children = MODULE.children || []
      return children.some(v => v.url.includes('xfront'))
    },
    iterateDecorateNewResource (menuList, componentList, pathList, source = []) {
      source.forEach((v, i) => {
        let isFrontModule = true
        // 判断当前模块是否包含前端模块
        // if (v.resourceType === 'MODULE') isFrontModule = this.judgeHasFrontModule(v)
        // if (v.resourceType === 'MENU') isFrontModule = v.resourceUrl.includes('xfront')
        if (v.resourceType === 'MODULE' || v.resourceType === 'SUB-MODULE' || v.resourceType === 'MENU') {
          let url = v.resourceUrl
          const item = {
            title: v.resourceName,
            url: url,
            icon: v.resourceIconCls || 'icon-authority',
            order: v.resourceOrder,
            children: []
          }
          if (v.children && v.children.length > 0) {
            this.iterateDecorateNewResource(item.children, componentList, pathList, v.children)
          }
          if (isFrontModule) menuList.push(item)
          if (url && isFrontModule) pathList.push(url)
        } else if (v.resourceType === 'BUTTON') {
          if (isFrontModule) componentList.push(v.resourceLevel)
          if (v.children && v.children.length > 0) {
            this.iterateDecorateResource(null, componentList, pathList, v.children)
          }
        }
      })
    },
    iterateDecorateResource (
      data = {
        modules: [],
        menus: [],
        buttons: []
      },
      resources = []
    ) {
      if (resources && resources.length > 0) {
        resources.forEach(item => {
          switch (item.resourceType) {
            case 'MODULE':
              data.modules.push(item.resourceLevel)
              break
            case 'MENU':
              data.menus.push(item.resourceLevel)
              break
            case 'BUTTON':
              data.buttons.push(item.resourceLevel)
              break
          }
          if (item.children && item.children.length > 0) {
            this.iterateDecorateResource(data, item.children)
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-header {
    padding: 0;
  }
  >>>.el-main {
    padding: 0;
  }
  .aside{
    transition: width 0.28s;
  }
</style>
