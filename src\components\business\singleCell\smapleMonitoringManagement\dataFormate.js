// 导入工具方法，用于设置对象的默认空值
import {setDefaultEmptyValueForObject, deepCopy} from '../../../../util/util'
import {FIELD_MAPPING} from './tabelConfig'
export const deliverConfig = {
  // 0 未交付 1已下达 2已交付 3下达失败 4交付失败
  0: '未交付',
  1: '已下达',
  2: '已交付',
  3: '下达失败',
  4: '交付失败'
}
export const qcAnalysisStatusConfig = {
  // 0 已启动 1已完成 2未启动 3启动失败
  2: '未启动',
  0: '已启动',
  1: '已完成',
  3: '启动失败'
}

export const openStatus = {
  // 0直接开启、1暂存
  0: '直接开启',
  1: '暂存'
}

export const booleanOptions = {
  0: '否',
  1: '是'
}
export const exceptionTypeList = [
  { label: '无', value: '5' },
  { label: '暂停', value: '0' },
  { label: '终止-解离前', value: '1' },
  { label: '终止-解离后', value: '2' },
  { label: '终止-建库前', value: '3' },
  { label: '终止-建库后', value: '4' }
]
/**
 * 格式化输入的数据数组，为每个项目添加额外的属性和处理默认值。
 * @param {Array} data - 原始数据数组，每个元素代表一个项目。
 * @returns {Array} - 格式化后的数据数组，每个项目都包含了更多的属性和处理了默认值。
 */
export const dataFormating = (data = []) => {
  // 遍历数据数组，对每个项目进行处理
  return data.map(v => {
    // 创建一个新的对象，包含项目的原始属性
    const item = {
      ...v
    }

    // 将前端字段映射为后端字段
    Object.keys(FIELD_MAPPING).forEach(frontendField => {
      const backendField = FIELD_MAPPING[frontendField]
      item[frontendField] = v[backendField]
    })

    // 特殊属性转化 状态转文字
    // 交付状态转换
    item.deliverStatusText = deliverConfig[item.deliverStatus] || ''
    // 预警状态转换
    item.warningFlagText = ['暂停', '终止-解离前', '终止-解离后', '终止-建库前', '终止-建库后'][item.warningFlag] || ''
    // QC状态转换
    item.qcStatusText = qcAnalysisStatusConfig[item.qcStatus] || ''
    item.cellInput = item.cellInput ? JSON.parse(item.cellInput) : []
    item.cellInput = item.cellInput.map(v => v.cellInput).join(',')
    // 布尔值转中文
    item.isSequencing = booleanOptions[item.isSequencing]
    item.isQcStandard = booleanOptions[item.isQcStandard]
    item.openStatus = openStatus[item.openStatus]
    item.isBackup = booleanOptions[item.isBackup]
    item.isDoorExperiment = booleanOptions[item.isDoorExperiment]
    item.isDeliveryStandard = booleanOptions[item.isDeliveryStandard]
    item.fisSampleAnomaly = booleanOptions[item.fisSampleAnomaly]
    // 为项目创建一个深拷贝的原始数据对象，用于保留项目的初始状态
    // 创建该项目的深拷贝，用于保留原始数据不变
    item.realData = deepCopy(item)
    // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
    setDefaultEmptyValueForObject(item)
    return item
  })
}
