<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="前端信息反馈"
      width="600px"
      @open="handleOpen"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <!--反馈原因-->
          <el-col>
            <el-form-item prop="callbackInfo" label="异常原因：">
              <el-select v-model="form.reason" clearable size="mini">
                <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in reasons"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!--备注-->
          <el-col>
            <el-form-item prop="note" label="补充备注：">
              <el-input v-model="form.note" size="mini" maxlength="20" clearable placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'clinicalInfoManagementSaveInfoCallbackDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number
    }
  },
  data () {
    return {
      loading: false,
      reasons: [
        {
          label: '上传图片内容不清晰',
          value: '上传图片内容不清晰'
        },
        {
          label: '图片上传错误',
          value: '图片上传错误'
        },
        {
          label: '关键信息依据图片无法获取',
          value: '关键信息依据图片无法获取'
        }
      ],
      form: {
        callbackInfo: '',
        note: '',
        reason: ''
      },
      rules: {
        reason: [
          {required: true, message: '请选择异常原因', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/sample/basic/followup_error',
            data: {
              followupErrorReason: this.form.reason,
              followupErrorRemark: this.form.note,
              sampleBasicId: this.id
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('随访异常设置成功')
              this.visible = false
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
