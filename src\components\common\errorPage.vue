<template>
  <div class="page">
    <div class="content">
      <div class="img">
        <img :src="require('../../assets/404.png')" alt="错误图片">
      </div>
      <div class="text">
        <p>SORRY您没有访问的权限！</p>
        <p>请检查网络是否连接正常后再尝试，若还是不能，请联系管理员！</p>
      </div>
    </div>
  </div>
</template>

<script>
// import num from './components/cc'
import pictures from '../../util/pictures'
export default {
  name: 'page404',
  data () {
    return {
      page404: pictures.NO_PAGE
    }
  }
}
</script>

<style scoped lang="scss">
 .page{
   overflow: hidden;
   background-color: #fff;
   position: fixed;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   .content{
     margin: 50px auto auto auto;
     width: 100%;
     max-width: 960px;
     .text{
       color: #777;
       text-align: center;
       font-weight: 600;
       line-height: 1.7;
       font-size: 14px;
     }
     .img{
       width: 52rem;
       max-width: 70%;
       margin: auto;
       position: relative;
       &:after{
         content: '500';
         display: block;
         position: absolute;
         right: 0;
         top: 10%;
         font-size: 7rem;
         color: $color;
         font-weight: 500;
       }
       img{
         display: block;
         width: 100%;
         height: auto;
       }
     }
   }
 }
</style>
