<!--前三种：细菌、病毒、真菌-->
<template>
  <div>
    <el-table
      ref="table"
      border
      :data="tableData"
      style="width: 100%"
      :cell-style="handleCheckCell"
      :height="'calc(100% - 32px)'"
      class="dataFilterTable"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @row-click="handleRowClick"
      :row-class-name="tableRowClassName"
    >
      <el-table-column fixed="left" type="index" width="45"/>
      <el-table-column fixed="left" type="selection" width="45" align="center"/>
      <el-table-column fixed="left" :align="getColumnType['report']" prop="report" label="是否报出" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed="left" :align="getColumnType['confirmed']" prop="confirmed" label="报出参考" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed="left" :align="getColumnType['probe_Designed']" prop="probe_Designed" label="是否在检测范围" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed="left" :align="getColumnType['Species_TaxID']" prop="Species_TaxID" label="种TaxID" width="120"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button class="underline" type="text" @click.stop="handleShowImg(scope.row.realData.fdbSeq)">
            <div class="clamp">{{ scope.row[scope.column.property] }}</div>

          </el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="left" :align="getColumnType['Species_Name']" prop="Species_Name" label="种拉丁文名" width="120"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button class="underline" type="text" @click.stop="handleShowPathogensData(scope.row.Species_TaxID, scope.row['Species_Name'])">
            <div class="clamp">{{ scope.row['Species_Name'] }}</div>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="left" :align="getColumnType['Species_Name_CN']" prop="Species_Name_CN" label="种中文名" width="120"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="clamp" @click.stop="handleShowNotify(scope.row)">{{ scope.row['Species_Name_CN'] }}</div>
        </template>
      </el-table-column>
      <el-table-column :align="getColumnType['Genus_TaxID']" prop="Genus_TaxID" label="属TaxID" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Genus_Name']" prop="Genus_Name" label="属拉丁文名" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Genus_Name_CN']" prop="Genus_Name_CN" label="属中文名" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Type']" prop="Type" label="Type" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['All_Reads']" prop="All_Reads" label="All_Reads" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Rmdup_Reads']" prop="Rmdup_Reads" label="Rmdup_Reads" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['AllRPMCR']" prop="AllRPMCR" label="All_RPMCR" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Target_Reads']" prop="Target_Reads" label="Target_Reads" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['ftargetRmdupReads']" prop="ftargetRmdupReads" label="Target_Rmdup_Reads" width="200"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['ffunProbes']" prop="ffunProbes" label="FunProbes" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['ffunProbesRatio']" prop="ffunProbesRatio" label="FunProbes_Ratio" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['SDSMRN']" prop="SDSMRN" label="SDSMRN" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Target_RPMCR']" prop="Target_RPMCR" label="Target_RPMCR" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fadjustedTargetRpmcr']" prop="fadjustedTargetRpmcr" label="adjusted_Target_RPMCR" width="220"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Target_rRPMCR']" prop="Target_rRPMCR" label="Target_rRPMCR" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['ftargetRmdupRpmcr']" prop="ftargetRmdupRpmcr" label="Target_Rmdup_RPMCR" width="200"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['ftargetRmdupRrpmcr']" prop="ftargetRmdupRrpmcr" label="Target_Rmdup_rRPMCR" width="220"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Specific_Reads']" prop="Specific_Reads" label="Specific_Reads" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fspecificRegionRatio']" prop="fspecificRegionRatio" label="Specific_Region_Ratio" width="160"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fgi']" prop="fgi" label="GI" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fik']" prop="fik" label="IK" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['RPKM']" prop="RPKM" label="RPKM" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['logRPKM']" prop="logRPKM" label="logRPKM" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Abundance_re']" prop="Abundance_re" label="Abundance_re" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['SG']" prop="SG" label="SG" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Ave_Depth']" prop="Ave_Depth" label="Ave_Depth" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Med_Depth']" prop="Med_Depth" label="Med_Depth" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Mode_Depth']" prop="Mode_Depth" label="Mode_Depth" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['1X_Coverage']" prop="1X_Coverage" label="1X_Coverage" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['2X_Coverage']" prop="2X_Coverage" label="2X_Coverage" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['5X_Coverage']" prop="5X_Coverage" label="5X_Coverage" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['10X_Coverage']" prop="10X_Coverage" label="10X_Coverage" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['UniformityT02M']" prop="UniformityT02M" label="Uniformity(T_0.2_M)" width="170"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['UniformityT05M']" prop="UniformityT05M" label="Uniformity(T_0.5_M)" width="170"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Dispersion(CV)']" prop="Dispersion(CV)" label="Dispersion(CV)" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Zscore']" prop="Zscore" label="Zscore" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['frmdupZscore']" prop="frmdupZscore" label="Rmdup_Zscore" width="160"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['History_NTC_Info']" prop="History_NTC_Info" label="History_NTC_Info" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['FoldChange_Info']" prop="FoldChange_Info" label="FoldChange_Info" width="160"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['History_Case_Info']" prop="History_Case_Info" label="History_Case_Info" width="160"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['RUN_Case_Info']" prop="RUN_Case_Info" label="RUN_Case_Info" width="140"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Pathogenic']" prop="Pathogenic" label="高致病病原" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Conditioned']" prop="Conditioned" label="条件致病菌" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Industrial']" prop="Industrial" label="试剂工程菌" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Colonization']" prop="Colonization" label="定植菌" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Confidence_Level']" prop="Confidence_Level" label="Confidence_Level" width="150"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Concentration']" prop="Concentration" label="Concentration" width="120"
                       show-overflow-tooltip/>
      <el-table-column :align="getColumnType['freasonOfFilter']" prop="freasonOfFilter" label="Reason_of_Filter" width="180"
                       show-overflow-tooltip/>
      <el-table-column
        :align="getColumnType['ftargetAllRmdupSeq']"
        prop="ftargetAllRmdupSeq"
        label="Target/All_Rmdup_Seq"
        width="120"
        >
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">
              <div v-html="scope.row.ftargetAllRmdupSeq"></div>
            </div>

            <div class="clamp">{{scope.row.ftargetAllRmdupSeq}}</div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      :total="totalPage">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh"/>
      </button>
    </el-pagination>
    <pathogens-data-dialog
      :pvisible.sync="pathogensData.visible"
      :name="pathogensData.name"
      :tax-id="pathogensData.taxId"
    ></pathogens-data-dialog>
  </div>
</template>

<script>
import util from '@/util/util'
import mixins from '../../../../../../util/mixins'
import {getTypeArray} from '../../../../../../util/util'
import PathogensDataDialog from './pathogensDataDialog'

export default {
  name: 'Common',
  mixins: [mixins.tablePaginationCommonData],
  components: {PathogensDataDialog},
  props: {
    type: {
      type: String,
      default: '0'
    },
    refresh: {
      type: Boolean
    }
  },
  mounted () {
    // 处理拉动列名时，fix列的行位置与非fix列的行位置错位
    const table = document.querySelector('.el-table')
    table.addEventListener('click', async (e) => {
      await this.$nextTick()
      this.$refs.table.doLayout()
    })
  },
  beforeDestroy () {
    const table = document.querySelector('.el-table')
    if (table) {
      table.removeEventListener('click', async (e) => {
        await this.$nextTick()
        this.$refs.table.doLayout()
      })
    }
  },
  watch: {
    type: {
      handler () {
        this.getData()
        this.tableData = []
        this.selectedRows = []
        if (this.$refs.table) {
          this.$refs.table.bodyWrapper.scrollLeft = 0
        }
      },
      immediate: true
    },
    refresh: {
      handler (newVal) {
        if (newVal) {
          this.getData()
        }
      }
    }
  },
  data () {
    return {
      taxIds: new Set(),
      tableData: [],
      selectedRows: new Map(),
      getColumnType: [],
      pathogensData: {
        visible: false,
        taxId: '',
        name: ''
      }
    }
  },
  methods: {
    async getData () {
      const mappings = {
        '0': '0',
        '10': '10',
        '20': '20',
        '70': '40'
      }
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/get_bacterial_fungal_virus',
        data: {
          type: mappings[this.type],
          analysisId: this.$route.query.oxym,
          current: this.currentPage + '',
          size: this.pageSize + ''
        },
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        let rows = data.rows || []
        this.totalPage = data.total
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            report: v.freport,
            confirmed: v.fconfirmed,
            probe_Designed: v.fprobeDesigned,
            Species_TaxID: v.fspeciesTaxId,
            Species_Name: v.fspeciesLatin,
            Species_Name_CN: v.fspeciesChinese,
            Genus_TaxID: v.fgenusTaxid,
            Genus_Name: v.fgenusLatin,
            Genus_Name_CN: v.fgenusChinese,
            Type: v.ftypeOriginal,
            All_Reads: v.fallReads,
            Rmdup_Reads: v.frmdupReads,
            AllRPMCR: v.fallRpmcr,
            Target_Reads: v.ftargetReads,
            SDSMRN: v.fsdsmrn,
            Target_RPMCR: v.ftargetRpmcr,
            Target_rRPMCR: v.ftargetRrpmcr,
            Specific_Reads: v.fspecificReads,
            RPKM: v.frpkm,
            logRPKM: v.flogRpkm,
            Abundance_re: v.fabundanceRe,
            SG: v.fsg,
            Ave_Depth: v.faveDepth,
            Med_Depth: v.fmedDepth,
            Mode_Depth: v.fmodeDepth,
            '1X_Coverage': v.f1xCoverage,
            '2X_Coverage': v.f2xCoverage,
            '5X_Coverage': v.f5xCoverage,
            '10X_Coverage': v.f10xCoverage,
            UniformityT02M: v.funiformity02tm,
            UniformityT05M: v.funiformity05tm,
            'Dispersion(CV)': v.fdispersionCv,
            Zscore: v.fzscore,
            FoldChange_Info: v.ffoldChangeInfo,
            History_NTC_Info: v.fhistoryNtcInfo,
            History_Case_Info: v.fhistorycaseInfo,
            RUN_Case_Info: v.frunCaseInfo,
            Pathogenic: v.fpathogenic,
            Conditioned: v.fconditioned,
            Industrial: v.findustrial,
            Colonization: v.fcolonization,
            Confidence_Level: v.fconfidenceLevel,
            Concentration: v.fconcentration,
            fdbSeq: v.fdbSeq,
            ftargetRmdupReads: v.ftargetRmdupReads,
            ffunProbesRatio: v.ffunProbesRatio,
            ffunProbes: v.ffunProbes,
            fadjustedTargetRpmcr: v.fadjustedTargetRpmcr,
            ftargetRmdupRpmcr: v.ftargetRmdupRpmcr,
            ftargetRmdupRrpmcr: v.ftargetRmdupRrpmcr,
            fgi: v.fgi,
            fik: v.fik,
            frmdupZscore: v.frmdupZscore,
            freasonOfFilter: v.freasonOfFilter,
            ftargetAllRmdupSeq: v.ftargetAllRmdupSeq,
            fspecificRegionRatio: v.fspecificRegionRatio
          }
          if (item.ftargetAllRmdupSeq) {
            item.ftargetAllRmdupSeq = item.ftargetAllRmdupSeq.split('\n').join('<br/>')
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        this.getColumnType = getTypeArray(this.tableData)
      }
    },
    // 动态行样式
    handleCheckCell ({row}) {
      const id = row.id
      let rows = [...this.selectedRows.values()]
      const isSelect = rows.some(v => v.id === id)
      return isSelect ? 'background: #ecf6ff' : ''
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
      this.$emit('handleRowClick', this.selectedRows)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
      this.$emit('handleSelect', this.selectedRows)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.$emit('handleSelectAll', this.selectedRows)
    },
    // 行样式
    tableRowClassName ({row, rowIndex}) {
      const obj = {
        Y: 'red',
        L: 'blue'
      }
      if (!row.report) return ''
      return obj[row.report]
    },
    handleShowImg (dbSeq) {
      this.$emit('showImg', dbSeq)
    },
    // 显示同批次该病原数据
    handleShowPathogensData (taxId = '', name = '') {
      this.pathogensData.taxId = taxId
      this.pathogensData.name = name
      this.pathogensData.visible = true
    },
    // 获取菌种说明
    async getSpeciesExplanation (id) {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/get_species_explanation',
        method: 'get',
        data: {
          taxId: id
        }
      })
      if (code === this.SUCCESS_CODE) {
        return data.fspeciesDescription || '暂无相关菌种说明'
      }
    },
    // 对应的菌种说明
    async handleShowNotify (row) {
      if (this.taxIds.has(row.Species_TaxID)) return
      const taxId = row.Species_TaxID
      this.taxIds.add(taxId)
      const description = await this.getSpeciesExplanation(taxId)
      this.$notify({
        dangerouslyUseHTMLString: true,
        duration: 0,
        message: `
        <div>
          <div>
            <h2 style="display: inline-block">${row['Species_Name_CN']}</h2>
            <i>${row['Species_Name']}</i>
          </div>
          <div style="margin-top: 10px; padding: 10px;">
            ${description}
          </div>
        </div>
        `,
        onClose: () => {
          this.taxIds.delete(taxId)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table {
  .red {
    color: #EE3838FF;
  }

  .blue {
    color: #539fff;
  }
}
.underline {
  text-decoration: underline;
}
.clamp {
  width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.el-drawer__wrapper {
  position: fixed;
  top: 0;
  left: auto;
  right: 0;
  bottom: 0;
  overflow: hidden;
  margin: 0;
  width: 40%;
}

/deep/ .el-drawer__container {
  position: relative;
  left: auto;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
}
</style>
