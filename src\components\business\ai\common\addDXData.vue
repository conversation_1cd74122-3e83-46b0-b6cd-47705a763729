<template>
  <div class="page">
    <div class="header">
      <div class="title">
        新增DX数据
      </div>
      <div>
        <el-button :loading="saveLoading" type="primary" size="mini" @click="handleSave('0')">保存草稿</el-button>
        <el-button :loading="saveLoading" type="primary" size="mini" @click="handleSave('1')">确认提交</el-button>
      </div>
    </div>
    <div class="content">
      <div>
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%;" @tab-click="handleClick">
          <el-tab-pane label="变异概述" name="1"></el-tab-pane>
          <el-tab-pane label="实体瘤相关文献研究" name="2"></el-tab-pane>
          <el-tab-pane label="淋巴瘤类相关文件研究" name="3"></el-tab-pane>
          <el-tab-pane label="生物学详情" name="4"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="height: 100%; overflow: auto; width: calc(100% - 140px); padding-right: 9px;">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="mini" label-position="top" label-suffix="：">
          <el-row :gutter="15">
            <div v-show="activeName === '1'">
              <el-col :span="8">
                <el-form-item label="基因" prop="gGene">
                  <el-input v-model="form.gGene" placeholder="请输入基因名称"></el-input>
                </el-form-item>
              </el-col>
              <template v-if="type === 'CNV'">
                <el-col :span="8">
                  <el-form-item label="碱基">
                    <el-input v-model="form.gNucleotideMutation" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="氨基酸改变" prop="gAminoAcidMutation">
                    <el-input v-model="form.gAminoAcidMutation" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Function">
                    <el-input v-model="form.gVariationFunction" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="转录本">
                    <el-input v-model="form.gReferenceSequence" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
              </template>
              <template v-else>
                <el-col :span="8">
                  <el-form-item label="主效基因" prop="gMainGene">
                    <el-select v-model="form.gMainGene" filterable allow-create default-first-option placeholder="请选择" clearable style="width: 100%;">
                      <el-option
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        v-for="item in mainGeneList">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="融合类型" prop="gFuseType">
                    <el-input v-model="form.gFuseType" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
              </template>
              <el-col :span="8">
                <el-form-item label="外显子" prop="gExon">
                  <el-input v-model="form.gExon" placeholder="请输入外显子"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="蛋白功能影响" prop="gProteinFunctionEffect">
                  <el-select v-model="form.gProteinFunctionEffect" clearable placeholder="请选择" style="width: 100%;">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in proteinFunctionEffectList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="变异概述（中文）">
                  <el-input v-model="form.gMutationFunctionDes" :autosize="{minRows: 10}" placeholder="中文概述" type="textarea"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="变异概述（英文）">
                  <el-input v-model="form.gMutationFunctionEDes" :autosize="{minRows: 10}" placeholder="英文概述" type="textarea"></el-input>
                </el-form-item>
              </el-col>
            </div>
            <div v-show="activeName === '2'">
              <el-col :span="8">
                <el-form-item label="变异类别" prop="gMutationType">
                  <el-input v-model="form.gMutationType" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="DX属性" prop="gDxAttribute">
                  <el-select v-model="form.gDxAttribute" placeholder="请选择" style="width: 100%;">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in gDxAttributeList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="文献研究（中文）">
                  <el-input v-model="form.fliteratureResearchCn" :autosize="{minRows: 10}" placeholder="中文概述" type="textarea"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="文献研究（英文）">
                  <el-input v-model="form.fliteratureResearchEn" :autosize="{minRows: 10}" placeholder="英文概述" type="textarea"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label-width="0">
                  <div>
                    <el-button type="primary" size="mini" @click="handleGetReferences">自动获取文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(0)">新增文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(1)">修改文献</el-button>
                    <el-button type="primary" size="mini" @click="handleSelectReferences">选择文献</el-button>
                    <el-button type="primary" size="mini" @click="handleDeleteReferences">移除</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(1)">上移</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(0)">下移</el-button>
                  </div>
                  <el-table
                    :data="solidTumorTableData"
                    height="400"
                    highlight-current-row
                    style="width: 100%"
                    @current-change="handleCurrentChange">
                    <el-table-column type="index" label="#"></el-table-column>
                    <!--<el-table-column type="selection" width="45"></el-table-column>-->
                    <el-table-column prop="rIdNameCombo" label="文献名称"></el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </div>
            <div v-show="activeName === '3'">
              <el-col :span="8">
                <el-form-item label="变异类别" prop="flymMutationType">
                  <el-input v-model="form.flymMutationType" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="DX属性" prop="glymDxAttribute">
                  <el-select v-model="form.glymDxAttribute" placeholder="请选择" style="width: 100%;">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in gDxAttributeList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="文献研究（中文）">
                  <el-input v-model="form.flymLiteratureResearchCn" :autosize="{minRows: 10}" placeholder="中文概述" type="textarea"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="文献研究（英文）">
                  <el-input v-model="form.flymLiteratureResearchEn" :autosize="{minRows: 10}" placeholder="英文概述" type="textarea"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label-width="0">
                  <div>
                    <el-button type="primary" size="mini" @click="handleGetReferences">自动获取文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(0)">新增文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(1)">修改文献</el-button>
                    <el-button type="primary" size="mini" @click="handleSelectReferences">选择文献</el-button>
                    <el-button type="primary" size="mini" @click="handleDeleteReferences">移除</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(1)">上移</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(0)">下移</el-button>
                  </div>
                  <el-table
                    :data="lymphomaTableData"
                    height="400"
                    highlight-current-row
                    style="width: 100%"
                    @current-change="handleCurrentChange">
                    <el-table-column type="index" label="#"></el-table-column>
                    <!--<el-table-column type="selection" width="45"></el-table-column>-->
                    <el-table-column prop="rIdNameCombo" label="文献名称"></el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </div>
            <div v-show="activeName === '4'">
              <el-col :span="8">
                <el-form-item label="Gene1" prop="gene1">
                  <el-input v-model="form.gene1" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Pos1" prop="pos1">
                  <el-input v-model="form.pos1" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Transcript1" prop="transcript1">
                  <el-input v-model="form.transcript1" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Gene2" prop="gene2">
                  <el-input v-model="form.gene2" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Pos2" prop="pos2">
                  <el-input v-model="form.pos2" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Transcript2" prop="transcript2">
                  <el-input v-model="form.transcript2" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="FusionType" prop="fusionType">
                  <el-input v-model="form.fusionType" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="FusionInfo" prop="fusionInfo">
                  <el-input v-model="form.fusionInfo" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="DX_Level" prop="siteDXLevel">
                  <el-input v-model="form.siteDXLevel" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="H_Realtag_Y" prop="siteHRealtagY">
                  <el-input v-model="form.siteHRealtagY" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="H_Realtag_MAF" prop="siteHRealtagMaf">
                  <el-input v-model="form.siteHRealtagMaf" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="case_Realtag_Y" prop="siteCaseRealtagY">
                  <el-input v-model="form.siteCaseRealtagY" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="InReport" prop="inReport">
                  <el-input v-model="form.inReport" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Report_Count" prop="siteReportCount">
                  <el-input v-model="form.siteReportCount" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="case_Realtag_MAF" prop="siteCaseRealtagMaf">
                  <el-input v-model="form.siteCaseRealtagMaf" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="InMSK" prop="siteInMSK">
                  <el-input v-model="form.siteInMSK" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="MSK_Count" prop="siteMskCount">
                  <el-input v-model="form.siteMskCount" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="MSK_general tumor types" prop="siteMskGeneralTumorTypes">
                  <el-input v-model="form.siteMskGeneralTumorTypes" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="MSK_detailed tumor types" prop="siteMskDetailedTumorTypes">
                  <el-input v-model="form.siteMskDetailedTumorTypes" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="InCOSMIC" prop="siteInCosmic">
                  <el-input v-model="form.siteInCosmic" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Cosmic_Count" prop="siteCosmicCount">
                  <el-input v-model="form.siteCosmicCount" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Cosmic_ID" prop="siteCosmicId">
                  <el-input v-model="form.siteCosmicId" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Cosmic_site" prop="siteCosmicSite">
                  <el-input v-model="form.siteCosmicSite" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Cosmic Histology" prop="siteCosmicHistology">
                  <el-input v-model="form.siteCosmicHistology" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
            </div>
          </el-row>
        </el-form>
      </div>
    </div>
    <save-references-dialog
      :pvisible="saveReferencesDialogVisible"
      :pdata="saveReferencesDialogData"
      @saveReferencesDialogCloseEvent="handleSaveReferencesDialogClose"
      @saveReferencesDialogConfirmEvent="handleSaveReferencesDialogConfirm"
    ></save-references-dialog>
    <search-references-dialog
      :pvisible="searchReferencesDialogVisible"
      @searchReferencesDialogCloseEvent="handleSearchReferencesDialogClose"
      @searchReferencesDialogConfirmEvent="handleSearchReferencesDialogConfirm"
    ></search-references-dialog>
  </div>
</template>

<script>
import saveReferencesDialog from './saveReferencesDialog'
import searchReferencesDialog from './searchReferencesDialog'
export default {
  name: 'addDXData',
  components: {saveReferencesDialog, searchReferencesDialog},
  props: [],
  mounted () {
    this.getProteinFunctionEffectList()
    this.getData()
  },
  watch: {},
  computed: {
    dxData () {
      return this.$store.getters.getValue('dxData')
    }
  },
  data () {
    return {
      type: 'CNV',
      saveLoading: false,
      activeName: '1',
      rules: {
        gGene: [
          {required: true, message: '请输入基因', trigger: 'blur'}
        ],
        gAminoAcidMutation: [
          {required: true, message: '请输入氨基酸改变', trigger: 'blur'}
        ],
        gExon: [
          {required: true, message: '请输入外显子', trigger: 'blur'}
        ],
        gFuseType: [
          {required: true, message: '请输入融合类型', trigger: 'blur'}
        ],
        gMutationType: [
          {required: true, message: '请输入变异类别', trigger: 'blur'}
        ],
        gDxAttribute: [
          {required: true, message: '请选择DX属性', trigger: ['blur', 'change']}
        ]
      },
      solidTumorTableData: [],
      lymphomaTableData: [],
      selectedRow: '',
      form: {
        fid: '',
        type: '',
        gGene: '',
        gExon: '',
        gProteinFunctionEffect: '',
        gMainGene: '',
        gFuseType: '',
        gMutationFunctionDes: '',
        gMutationFunctionEDes: '',
        gMutationType: '',
        gDxAttribute: '',
        fliteratureResearchCn: '',
        fliteratureResearchEn: '',
        refenceIdSolidTumor: '',
        flymMutationType: '',
        glymDxAttribute: '',
        flymLiteratureResearchCn: '',
        flymLiteratureResearchEn: '',
        refenceIdLymphoma: '',
        gene1: '',
        pos1: '',
        transcript1: '',
        gene2: '',
        pos2: '',
        transcript2: '',
        fusionType: '',
        fusionInfo: '',
        siteDXLevel: '',
        siteHRealtagY: '',
        siteHRealtagMaf: '',
        siteCaseRealtagY: '',
        inReport: '',
        siteReportCount: '',
        siteCaseRealtagMaf: '',
        siteInMSK: '',
        siteMskCount: '',
        siteMskGeneralTumorTypes: '',
        siteMskDetailedTumorTypes: '',
        siteInCosmic: '',
        siteCosmicCount: '',
        siteCosmicId: '',
        siteCosmicSite: '',
        siteCosmicHistology: '',
        gNucleotideMutation: '',
        gAminoAcidMutation: '',
        gVariationFunction: '',
        gReferenceSequence: '',
        variationChr: '',
        variationStart: '',
        variationStop: '',
        variationRef: '',
        variationCall: ''
      },
      proteinFunctionEffectList: [],
      mainGeneList: [],
      gDxAttributeList: [
        {
          label: 'DX点',
          value: 'DX点'
        },
        {
          label: '非DX点',
          value: '非DX点'
        },
        {
          label: 'NA',
          value: 'NA'
        }
      ],
      saveReferencesDialogVisible: false,
      saveReferencesDialogData: {},
      searchReferencesDialogVisible: false
    }
  },
  methods: {
    getData () {
      this.form = Object.assign({}, this.form, this.dxData)
      if (this.form.gene1 || this.form.gene2) {
        this.getMainGeneList()
        this.type = 'SV'
      } else {
        this.type = 'CNV'
      }
    },
    getMainGeneList () {
      this.$ajax({
        url: '/read/unscramble/get_main_gene',
        method: 'get',
        data: {
          fgene1: this.form.gene1,
          fgene2: this.form.gene2
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.mainGeneList = []
          if (result.data) {
            this.mainGeneList.push({
              label: result.data,
              value: result.data
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getProteinFunctionEffectList () {
      this.$ajax({
        url: '/read/unscramble/get_protein_function_effect',
        method: 'get',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.proteinFunctionEffectList = []
          if (result.data) {
            result.data.forEach(v => {
              this.proteinFunctionEffectList.push({
                label: v,
                value: v
              })
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleClick () {
      this.selectedRow = ''
    },
    handleCurrentChange (val) {
      this.selectedRow = val
    },
    handleSave (type) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.refenceIdSolidTumor = this.solidTumorTableData.map(v => v.rLibraryId).join(',')
          this.form.refenceIdLymphoma = this.lymphomaTableData.map(v => v.rLibraryId).join(',')
          this.saveLoading = true
          this.$ajax({
            url: '/read/unscramble/dx_data_save_or_update',
            data: {
              ...this.form,
              flag: type
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              window.close()
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.saveLoading = false
          })
        } else {
          this.$message.error('必填项校验不通过')
        }
      })
    },
    handleGetReferences () {
      let text = ''
      switch (this.activeName) {
        case '2':
          text = this.form.fliteratureResearchCn
          break
        case '3':
          text = this.form.flymLiteratureResearchCn
          break
      }
      this.$ajax({
        loadingDom: '.referencesTable',
        url: '/read/unscramble/auto_get_literature',
        method: 'get',
        data: {
          ftext: text,
          associatedId: ''
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRow = ''
          this.solidTumorTableData = []
          this.lymphomaTableData = []
          if (this.activeName === '2') {
            this.solidTumorTableData = result.data.rows
          } else {
            this.lymphomaTableData = result.data.rows
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleEditReferences (type) {
      if (type === 0) {
        this.saveReferencesDialogData = {
          libraryId: '',
          referenceType: '',
          referenceId: '',
          referenceName: ''
        }
        this.saveReferencesDialogVisible = true
      } else {
        if (this.selectedRow) {
          this.saveReferencesDialogData = {
            libraryId: this.selectedRow.rLibraryId,
            referenceType: this.selectedRow.rClassified,
            referenceId: this.selectedRow.rId,
            referenceName: this.selectedRow.rName
          }
          this.saveReferencesDialogVisible = true
        } else {
          this.$message.error('请选择一条数据')
        }
      }
    },
    handleSelectReferences () {
      this.searchReferencesDialogVisible = true
    },
    handleDeleteReferences () {
      if (this.selectedRow) {
        if (this.activeName === '2') {
          let index = this.solidTumorTableData.findIndex(v => v.rLibraryId === this.selectedRow.rLibraryId)
          this.solidTumorTableData.splice(index, 1)
        } else {
          let index = this.lymphomaTableData.findIndex(v => v.rLibraryId === this.selectedRow.rLibraryId)
          this.lymphomaTableData.splice(index, 1)
        }
        this.selectedRow = ''
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleMove (type) {
      if (this.selectedRow) {
        let row = this.selectedRow
        if (this.activeName === '2') {
          if (type) {
            // 上移
            let index = this.solidTumorTableData.findIndex(v => v.rLibraryId === row.rLibraryId)
            if (index !== 0) {
              this.solidTumorTableData.splice(index, 1)
              this.solidTumorTableData.splice(index - 1, 0, row)
            }
          } else {
            // 下移
            let index = this.solidTumorTableData.findIndex(v => v.rLibraryId === row.rLibraryId)
            if (index !== this.solidTumorTableData.length - 1) {
              this.solidTumorTableData.splice(index, 1)
              this.solidTumorTableData.splice(index + 1, 0, row)
            }
          }
        } else {
          if (type) {
            // 上移
            let index = this.lymphomaTableData.findIndex(v => v.rLibraryId === row.rLibraryId)
            if (index !== 0) {
              this.lymphomaTableData.splice(index, 1)
              this.lymphomaTableData.splice(index - 1, 0, row)
            }
          } else {
            // 下移
            let index = this.lymphomaTableData.findIndex(v => v.rLibraryId === row.rLibraryId)
            if (index !== this.lymphomaTableData.length - 1) {
              this.lymphomaTableData.splice(index, 1)
              this.lymphomaTableData.splice(index + 1, 0, row)
            }
          }
        }
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleSaveReferencesDialogClose () {
      this.saveReferencesDialogVisible = false
    },
    handleSaveReferencesDialogConfirm () {
      this.saveReferencesDialogVisible = false
      this.handleGetReferences()
    },
    handleSearchReferencesDialogClose () {
      this.searchReferencesDialogVisible = false
    },
    handleSearchReferencesDialogConfirm (data) {
      this.searchReferencesDialogVisible = false
      if (data.length !== 0) {
        let index = -1
        switch (this.activeName) {
          case '2':
            data.forEach(v => {
              index = this.solidTumorTableData.findIndex(vv => {
                return vv.rLibraryId === v.rLibraryId
              })
              if (index === -1) {
                this.solidTumorTableData.push(v)
              }
            })
            break
          case '3':
            data.forEach(v => {
              index = this.lymphomaTableData.findIndex(vv => {
                return vv.rLibraryId === v.rLibraryId
              })
              if (index === -1) {
                this.lymphomaTableData.push(v)
              }
            })
            break
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .page {
    height: calc(100vh - 40px);

    .header {
      height: 58px;
      padding: 0 20px;
      border-bottom: 2px solid #E4E7ED;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title{
        font-size: 18px;
        font-weight: bold;
      }
    }

    .content {
      >>> .el-tabs__item {
        /*width: 140px;*/
        text-align: right;
      }

      height: calc(100% - 60px);
      display: flex;
      overflow: auto;
    }

    >>> .el-table thead {
      font-size: 14px;
      font-weight: 500;
      color: #909399;
    }

    >>> .el-table th {
      background-color: rgba(0, 0, 0, 0);
    }
  }
</style>
