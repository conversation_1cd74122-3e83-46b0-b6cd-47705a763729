<template>
  <el-dialog v-drag-dialog :close-on-click-modal="false" append-to-body :visible.sync="visible"
    :before-close="handleClose" title="添加样本" width="500px" @open="handleOpen">
    <div style="margin-bottom: 10px;">添加样本数: {{ sampleNums }}</div>
    <el-form ref="form" :model="form" size="mini" label-suffix=":" :rules="rules" label-width="80px">
      <el-radio-group v-model="form.type" style="margin: 10px 0;">
        <el-radio :label="0">添加到已有任务单</el-radio>
        <el-radio :label="1">建立新任务单</el-radio>
      </el-radio-group>
      <el-form-item v-if="!form.type" label="任务单号" prop="taskCode">
        <el-r-select v-model.trim="form.taskCode" :options="taskCodeList" clearable filterable placeholder="请选择">
        </el-r-select>
      </el-form-item>
      <el-form-item v-if="form.type" label="所属片区" prop="area">
        <el-select v-model.trim="form.area" placeholder="请选择" clearable @change="handleScheduleAreaChange">
          <el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.type" label="测序类型" prop="sequencingType">
        <el-select v-model.trim="form.sequencingType" placeholder="请选择" clearable filterable size="mini">
          <el-option v-for="item in typeOptions" :label="item.label" :value="item.value" :key="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <div class="tips">确认所选样本在同一张芯片上?</div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import Cookies from 'js-cookie'
import util, { awaitWrap } from '../../../../util/util'
import { getTaskList } from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'addSampleConfirmDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleNums: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false,
      form: {
        type: 0,
        sequencingType: '',
        taskCode: '',
        area: ''
      },
      typeOptions: [
        { label: 'PE150', value: 'PE150' },
        { label: 'PE100', value: 'PE100' }
      ],
      rules: {
        sequencingType: [{ required: true, message: '请选择测序类型', trigger: 'change' }],
        taskCode: [{ required: true, message: '请选择任务单号', trigger: 'change' }],
        area: [{ required: true, message: '请选择所属片区', trigger: 'change' }]
      },
      taskCodeList: [],
      areaList: JSON.parse(Cookies.get('labOptions') || '').filter(v => v.label !== '苏州')
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form.type = 0
        this.$refs.form.resetFields()
        this.form.area = util.getSessionInfo('scheduleArea')
        this.getTaskList()
      })
    },
    async getTaskList () {
      const params = {
        pageVO: {
          currentPage: 1,
          pageSize: 100000
        }
      }
      let { res } = await awaitWrap(getTaskList(params, { loadingDom: '.table' }))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.taskCodeList = []
        const rows = data.rows || []
        rows.forEach(v => {
          const item = {
            label: v.fcode,
            value: v.fcode // 任务单号
          }
          this.taskCodeList.push(item)
        })
      }
    },
    // 排单地区变化
    handleScheduleAreaChange () {
      util.setSessionInfo('scheduleArea', this.form.area)
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let data = {
            sequencingType: this.form.sequencingType,
            area: this.form.area
          }
          if (!this.form.type) {
            data = {
              taskCode: this.form.taskCode
            }
          }

          this.$emit('dialogConfirmEvent', data)
          this.visible = false
          this.loading = false
        } else {
          this.$message.error('表单存在错误，请检查')
        }
      })
    }
  }
}
</script>

<style scoped></style>
