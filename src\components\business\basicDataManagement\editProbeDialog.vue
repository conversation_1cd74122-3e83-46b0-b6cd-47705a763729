<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="600px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item prop="cancerName" label="匹配癌种">
          <el-select v-model="form.cancerName" size="mini" clearable filterable placeholder="请选择">
            <el-option
              :key="index"
              :value="item.cancerName"
              :label="item.cancerName"
              v-for="(item, index) in cancerList">
              <span style="float: left">{{ item.cancerName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="coreProbeName" label="核心探针名称">
          <el-input v-model.trim="form.coreProbeName" size="mini" maxlength="20" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="probeDesignMethod" label="核心探针设计方">
          <el-input v-model.trim="form.probeDesignMethod" size="mini" maxlength="20" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="probeSize" label="探针大小">
          <el-input v-model.trim="form.probeSize" size="mini" maxlength="20" clearable placeholder="请输入">
            <template slot="append">kb</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="probeNum" label="核心条数">
          <el-input v-model.trim="form.probeNum" size="mini" maxlength="20" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="note" label="说明">
          <el-input v-model.trim="form.note" type="textarea" size="mini" maxlength="100" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    probeInfo: {
      type: Object,
      required: false
    }
  },
  data () {
    return {
      title: '新增核心探针',
      loading: false,
      cancerList: [],
      form: {
        fid: '',
        cancerName: '',
        coreProbeName: '',
        probeDesignMethod: '',
        probeSize: '',
        probeNum: '',
        note: ''
      },
      rules: {
        cancerName: [
          {required: true, message: '请选择癌种', trigger: ['blur', 'change']}
        ],
        coreProbeName: [
          {required: true, message: '请输入核心探针名称', trigger: ['blur', 'change']}
        ],
        probeDesignMethod: [
          {required: true, message: '请输入核心探针设计方', trigger: ['blur', 'change']}
        ],
        probeSize: [
          {required: true, message: '请输入探针大小', trigger: ['blur', 'change']},
          {required: true, pattern: /^(\+)?\d+(\.\d+)?$/, message: '请输入数字', trigger: ['blur', 'change']}
        ],
        probeNum: [
          {required: true, message: '请输入探针条数', trigger: ['blur', 'change']},
          {required: true, pattern: /^\d{1,}$/, message: '请输入整数', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.getCancerList()
        this.$refs.form.resetFields()
        this.title = '新增核心探针'
        this.form.fid = ''
        if (this.probeInfo) {
          this.form = JSON.parse(JSON.stringify(this.probeInfo))
          this.title = '编辑核心探针'
        }
      })
    },
    getCancerList () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_cancer_sort_name_config_list',
        data: {
          probeName: this.name,
          page: 1,
          rows: 10000
        },
        method: 'get'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.totalPage = result.data.total
          let data = result.data.records || []
          this.cancerList = []
          data.forEach(v => {
            let item = {
              fid: v.fid,
              name: v.fsimpleName,
              cancerName: v.fcancerName
            }
            this.cancerList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 保存配置
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/system/probe/save_core_probe',
            data: {
              fid: this.form.fid,
              fcancerName: this.form.cancerName,
              fcoreProbeName: this.form.coreProbeName,
              fprobeDesignMethod: this.form.probeDesignMethod,
              fprobeSize: this.form.probeSize,
              fprobeNum: this.form.probeNum,
              fnote: this.form.note
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
/deep/ .el-select {
  width: 400px;
}
</style>
