<template>
  <div class="page-container">
    <el-card class="reportInfo">
      <el-row style="width: 100%;">
        <el-col :span="6">
          <div class="item">
            <el-avatar icon="el-icon-user-solid"></el-avatar>
            <span style="margin-left: 20px">患者性别:{{patientInfo.sex}}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="item">
            <div>产品名称: {{productName}}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="item">
            <div>解读匹配癌种: {{getMatchCancers}}</div>
            <i class="el-icon-edit" style="cursor: pointer;" @click="handleEditMatchCancer"></i>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="item">
            <div>样本编号: {{sampleInfo.sampleCode}}</div>
          </div>
        </el-col>
      </el-row>
      <!--标签菜单-->
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="检测基因列表" name="geneList"></el-tab-pane>
        <el-tab-pane label="解读数据筛选" name="readDataFilter"></el-tab-pane>
        <el-tab-pane label="报告审核" name="reportAudit"></el-tab-pane>
      </el-tabs>
    </el-card>
    <component :is="activeName"></component>

    <report-edit-match-cancer-dialog
      :pvisible.sync="editMatchCancerVisible"
      :pdata="editMatchCancerData"
      @editMatchCancerDialogConfirmEvent="getData"
    ></report-edit-match-cancer-dialog>
  </div>
</template>

<script>
import reportEditMatchCancerDialog from './reportEditMatchCancerDialog'
import readDataFilter from './readDataFilter/readDataFilter'
import reportAudit from './report/reportAudit'
import geneList from './genelist/geneList'

export default {
  components: {
    geneList, // 基因检测列表
    readDataFilter, // 解读数据筛选
    reportAudit, // 报告审核
    reportEditMatchCancerDialog
  },
  mounted () {
    this.init()
  },
  computed: {
    getMatchCancers () {
      let cancerName = this.matchCancerName.map(v => { return v.cancerTypeName })
      return cancerName.join(',')
    },
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      editMatchCancerVisible: false,
      editMatchCancerData: [],
      matchCancerName: [],
      activeName: 'geneList', // 组件名
      sampleInfo: {}, // 样本信息
      productName: '',
      patientInfo: {}
    }
  },
  methods: {
    init () {
      this.$store.commit({
        type: 'old/setValue',
        category: 'analysisRsId',
        analysisRsId: this.$route.query.oxym
      })
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/read/unscramble/get_sample_patient_info',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.matchCancerName = data.matchCancerName
          this.$store.commit({
            type: 'old/setValue',
            category: 'matchCancer',
            matchCancer: data.matchCancerName
          })
          this.patientInfo = { // 患者信息
            patientName: data.patientInfo.fpatientName,
            age: data.patientInfo.fage,
            sex: data.patientInfo.fsex,
            detectCancer: data.patientInfo.fdetectCancer,
            familyHistory: data.patientInfo.ffamilyHistory,
            pipelineName: data.patientInfo.fpipelineName,
            chip: data.patientInfo.fchip,
            clientName: data.patientInfo.fclientName,
            sendTime: data.patientInfo.fsendTime,
            sendUnit: data.patientInfo.fsendUnit
          }
          this.productName = data.productName
          this.sampleInfo = { // 樣本信息
            sampleCode: data.sampleInfo.fsampleCode,
            sampleType: data.sampleInfo.fsampleType,
            sampleCancerType: data.sampleInfo.sampleCancerType,
            detectCancer: data.sampleInfo.fdetectCancer,
            result: data.sampleInfo.fresult
          }
          this.isSingleSample = data.isSingleSample
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 切换组件
    handleClick () {},
    handleEditMatchCancer () {
      this.editMatchCancerData = []
      this.matchCancerName.forEach(v => {
        this.editMatchCancerData.push({cancer: v.cancerTypeName, cancerId: v.cancerTypeId})
      })
      this.editMatchCancerVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  height: calc(100vh - 40px);
  background: #eee;
  font-size: 14px;
  .reportInfo {
    margin-bottom: 10px;
    height: 110px;
    background: #fff;
    .item {
      display: flex;
      align-items: center;
      height: 40px;
    }
  }
}
</style>
