<template>
  <div style="margin-bottom: 20px;">
    <div style="border: 1px solid #f2f2f2;padding: 10px;">
      <h4>{{ title }}</h4>
      <el-table
        ref="table"
        :data="tableData"
        class="dataFilterTable"
        :row-style="handleRowStyle"
      >
        <el-table-column prop="genusName" label="属" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.realData.genusName!=='-'">{{ scope.row.genusName }}</span>
            <br v-if="scope.row.realData.genusName!=='-' && scope.row.realData.genusLatin!=='-'"/>
            <i v-if="scope.row.realData.genusLatin!=='-'">{{ scope.row.genusLatin }}</i>
            <span v-if="scope.row.realData.genusName==='-' && scope.row.realData.genusLatin==='-'">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="speciesName" :label="typeOfResult()[0]" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.realData.speciesName!=='-'">{{ scope.row.speciesName }}</span>
            <br v-if="scope.row.realData.speciesName!=='-' && scope.row.realData.speciesLatin!=='-'"/>
            <i v-if="scope.row.realData.speciesLatin!=='-'">{{ scope.row.speciesLatin }}</i>
            <span v-if="scope.row.realData.speciesName==='-' && scope.row.realData.speciesLatin==='-'">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="targetReads" :label="typeOfResult()[1]" show-overflow-tooltip/>
        <el-table-column prop="targetRPMCR" label="覆盖度" show-overflow-tooltip/>
        <el-table-column prop="abundanceRe" label="相对丰度" show-overflow-tooltip/>
        <el-table-column prop="concentration" label="估测浓度" show-overflow-tooltip/>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String
    },
    tableData: {
      type: Array
    },
    type: {
      type: String
    }
  },
  mounted () {
    this.typeOfResult()
  },
  methods: {
    handleRowStyle ({row}) {
      console.log(row)
      return row.report === 'Y' ? {color: 'red'} : {}
    },
    typeOfResult () {
      const obj = {
        bacteria: ['种/群', '序列(种/群)'],
        fungus: ['种', '序列(种)'],
        dna: ['种/型', '序列(种/型)'],
        rna: ['种/型', '序列(种/型)'],
        specialPathogenDetection: ['种/群', '序列(种/群)'],
        suspectedHumanMicrobiota: ['种/群', '序列(种/群)']
      }
      return obj[this.type]
    }
  }
}
</script>

<style scoped></style>
