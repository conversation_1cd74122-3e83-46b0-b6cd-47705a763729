<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="handleClose"
    v-drag-dialog
    title="文件上传"
    width="30%"
    @open="handleOpen"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="mini" label-suffix=":" label-position="top">
      <el-form-item label="撤回原因" prop="reason">
        <el-input v-model="form.reason" ref="materialNumber" clearable placeholder="请输入" max="20"></el-input>
      </el-form-item>
      <el-form-item label="同步删除图片" prop="isDelete">
        <el-input v-model="form.isDelete" ref="materialNumber" clearable placeholder="请输入" max="20"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      form: {
        isDelete: 0,
        reason: ''
      }
    }
  }
}
</script>

<style scoped></style>
