<template>
  <div>
    <el-dialog :visible="visible" :close-on-click-modal="false" :close-on-press-escape="false" title="导入"
               width="700px" @close="handleCloseImport">
      <el-upload ref="upload" :auto-upload="false"
                 :file-list="fileList" :action="uploadUrl"
                 :data="uploadParams"
                 :before-upload="handleBeforeUpload"
                 :on-change="handleChange"
                 :on-success="handleOnSuccess"
                 style="text-align: center;"
                 drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">
          <span>只支持excel文件</span>
          <!--<el-button size="mini" type="text" style="padding-left: 20px;" @click="handleClickDownload">导入模板下载</el-button>-->
        </div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleCloseImport">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirmImport">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import constants from '../../../../util/constants'

export default {
  name: 'wesReportDetectionOfGeneVariationImportDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.loading = false
        this.uploadParams = {
          analysisRsId: this.analysisRsId,
          type: this.pdata
        }
      }
    }
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      fileList: [],
      uploadUrl: constants.JS_CONTEXT + '/read/wesUnscramble/analysis_data_import',
      uploadParams: {
        analysisRsId: this.analysisRsId,
        type: ''
      }
    }
  },
  methods: {
    handleBeforeUpload (file) {
      if (!(/\.(xlsx|xls)$/i.test(file.name))) {
        this.$message.error(file.name + '不为Excel文件，无法上传！')
        return false
      }
    },
    handleChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
    },
    handleOnSuccess (response) {
      this.loading = false
      if (response.code === this.SUCCESS_CODE) {
        if (response.message) {
          this.$message({
            showClose: true,
            duration: 0,
            type: 'error',
            message: response.message
          })
        } else {
          this.$message.success('操作成功')
        }
        this.$refs.upload.clearFiles()
        this.$emit('importDialogConfirmEvent')
      } else {
        this.fileList.splice(0, 1)
        this.$message({
          dangerouslyUseHTMLString: true,
          message: response.message,
          type: 'error'
        })
      }
    },
    handleCloseImport () {
      this.$refs.upload.clearFiles()
      this.$emit('importDialogCloseEvent')
    },
    handleConfirmImport () {
      this.$refs.upload.submit()
      this.loading = true
    }
    // handleClickDownload () {
    //   let params = {}
    //   let realParams = {}
    //   params.token = util.getCookie('token')
    //   realParams = {
    //     type: '1'
    //   }
    //   params.params = JSON.stringify(realParams)
    //   let url = constants.JS_CONTEXT + '/system/download_template'
    //   let form = document.createElement('form')
    //   form.action = url
    //   form.method = 'post'
    //   Object.keys(params).forEach(function (key) {
    //     let input = document.createElement('input')
    //     input.type = 'hidden'
    //     input.name = key
    //     input.value = params[key]
    //     form.appendChild(input)
    //   })
    //   document.body.appendChild(form)
    //   form.submit()
    // }
  }
}
</script>

<style scoped>

</style>
