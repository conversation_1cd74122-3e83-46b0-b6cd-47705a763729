<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="120px"
        size="mini"
      >
        <el-form-item label="文库/吉因加编号">
          <el-input v-model="form.sampleName" type="textarea" :rows="1" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="到样时间">
         <date-picker v-model="form.time"></date-picker>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="form.projectName" type="textarea" :rows="1" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本标签 ">
          <el-select v-model="form.sampleSign" class="form-width" multiple clearable placeholder="请选择">
            <el-option
              v-for="item in tagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="mini"
        label-position="top"
        inline
      >
        <el-form-item label="样本类型">
          <el-checkbox-group v-model="form.sampleType">
            <el-checkbox v-for="item in sampleTypeOptions" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="业务类型">
          <el-checkbox-group v-model="form.businessType">
            <el-checkbox v-for="item in businessTypeOptions" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="产品 ">
          <el-input v-model.trim="form.product" class="form-width" clearable placeholder="请输入" @keyup.enter.native="handleSearch"></el-input>
        </el-form-item>
        <el-form-item label="质检结果">
          <el-select v-model="form.qcResult" class="form-width" multiple clearable placeholder="请选择">
            <el-option
              v-for="item in resultOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="待测数据量">
          <el-checkbox v-model="form.waitDateSize" class="form-width" :true-label="1" :false-label="0">>0
          </el-checkbox>
        </el-form-item>
        <el-form-item label="产出比判定">
          <el-radio-group v-model="form.outputProportionStatus" class="form-width" clearable placeholder="请选择">
            <el-radio :label="1">满足产出标准</el-radio>
            <el-radio :label="0">未满足产出标准</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文库/吉因加编号">
          <el-input v-model="form.sampleName" type="textarea" class="form-long-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="form.projectName" type="textarea" class="form-long-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="原始样本名称">
          <el-input v-model.trim="form.oldSampleName"  class="form-width" clearable placeholder="请输入" @keyup.enter.native="handleSearch"></el-input>
        </el-form-item>
        <el-form-item label="样本标签 ">
          <el-select v-model="form.sampleSign" class="form-width" multiple clearable placeholder="请选择">
            <el-option
              v-for="item in tagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="到样时间">
          <date-picker v-model="form.time"></date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>

    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('021007001', 'buttons')" type="primary" size="mini" @click="handleAddRemark">添加备注</el-button>
      <el-dropdown v-if="$setAuthority('021007003', 'buttons')" size="mini" @command="handleSignSample">
        <el-button type="primary" plain size="mini">标 记</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="1" >勾选样本</el-dropdown-item>
          <el-dropdown-item :command="2" >导入样本</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown v-if="$setAuthority('021007002', 'buttons')" size="mini" @command="handleCancelSignSample">
        <el-button type="danger" plain size="mini">取消标记</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="1" >勾选样本</el-dropdown-item>
          <el-dropdown-item :command="2" >导入样本</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown v-if="$setAuthority('021007002', 'buttons')" size="mini" @command="handleCommand">
        <el-button v-if="$setAuthority('021007004', 'buttons')" type="primary" plain size="mini">送检信息下载</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="1" >按勾选下载</el-dropdown-item>
          <el-dropdown-item :command="2" >按条件下载</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-if="$setAuthority('021007005', 'buttons')" type="primary" plain size="mini" @click="handleDownloadUnArriveSample">未到样信息下载</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55" show-overflow-tooltip></el-table-column>
        <el-table-column type="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column label="样本标签" prop="sampleSign" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.sampleSign === 0" size="mini" type="warning">未</el-tag>
            <el-tag v-if="scope.row.sampleSign === 1" size="mini" type="success">完</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" prop="projectName" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="文库/吉因加编号" prop="sampleName" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="原始样本名称" prop="oldSampleName" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="下单数据量/G" prop="orderDataSize" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="已排机数据量" prop="walkthroughDateSize" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="下机数据量" prop="dataSize" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="待测数据量" prop="waitDateSize" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="项目产量标准" prop="dataProductionStandard" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="产出比判定" prop="outputProportionStatusText" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="上机次数" prop="seqTimes" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="!scope.row.realData.seqTimes">{{scope.row.seqTimes}}</div>
            <el-button v-else type="text" @click="handleViewSeqTimes(scope.row)">{{ scope.row.seqTimes  }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="产出比" prop="outputProportion" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="质控结果" prop="qcResult" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="产品" prop="product" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="备注" prop="remark" min-width="50" show-overflow-tooltip></el-table-column>
        <el-table-column label="到样时间" prop="confirmTime" min-width="80" show-overflow-tooltip></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <sign-sample-dialog :pvisible.sync="signSampleVisible" :ids="ids" :type="type" @dialogConfirmEvent="getData"></sign-sample-dialog>
    <cancel-sign-sample-dialog :pvisible.sync="cancelSignSampleVisible" @dialogConfirmEvent="getData"></cancel-sign-sample-dialog>
    <download-un-arrive-sample-dialog :pvisible.sync="downloadUnArriveSampleVisible"></download-un-arrive-sample-dialog>
    <set-remark-dialog :pvisible.sync="setRemarkVisible" :ids="ids" @dialogConfirmEvent="getData"></set-remark-dialog>
    <count-detail-dialog :pvisible.sync="seqTimesVisible" :id="id"></count-detail-dialog>
  </div>
</template>

<script>

import mixins from '../../../../util/mixins'
import signSampleDialog from './components/signSampleDialog.vue'
import CancelSignSampleDialog from './components/cancelSignSampleDialog.vue'
import util, {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import DownloadUnArriveSampleDialog from './components/downloadUnArriveSampleDialog.vue'
import SetRemarkDialog from './components/setRemarkDialog.vue'
import CountDetailDialog from './components/countDetailDialog.vue'
import {
  cancelSignSample,
  downInspection,
  getUnTestSampleList
} from '../../../../api/sequencingManagement/unTestSampleApi'
import DatePicker from '../../../common/datePicker.vue'

export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    DatePicker,
    DownloadUnArriveSampleDialog,
    CancelSignSampleDialog,
    signSampleDialog,
    SetRemarkDialog,
    CountDetailDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      searchDialogVisible: false,
      signSampleVisible: false,
      cancelSignSampleVisible: false,
      downloadUnArriveSampleVisible: false,
      setRemarkVisible: false,
      seqTimesVisible: false,
      id: null,
      ids: [],
      type: null, // 标记样本类型
      tagOptions: [
        {
          value: 0,
          label: '未完成'
        },
        {
          value: 1,
          label: '完成'
        }
      ],
      sampleTypeOptions: [
        // 自建库、外来文库（cL）
        {
          value: 2,
          label: '自建库'
        },
        {
          value: 1,
          label: '外来文库（cL）'
        }
      ],
      businessTypeOptions: [
        // 科合业务文库、临检业务文库
        {
          value: 1,
          label: '科合业务文库'
        },
        {
          value: 2,
          label: '临检业务文库'
        }
      ],
      resultOptions: [
        // 合格、不合格、风险
        {
          value: '合格',
          label: '合格'
        },
        {
          value: '不合格',
          label: '不合格'
        },
        {
          value: '风险',
          label: '风险'
        },
        {
          value: '1级',
          label: '1级'
        },
        {
          value: '2级',
          label: '2级'
        },
        {
          value: '3级',
          label: '3级'
        },
        {
          value: '4级',
          label: '4级'
        }
      ],
      formSubmit: {},
      form: {
        code: '',
        time: [],
        sampleSign: [],
        sampleType: [],
        businessType: [],
        product: '',
        qcResult: [],
        waitDateSize: '',
        outputProportion: '',
        sampleName: '',
        projectName: '',
        oldSampleName: ''
      }
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        fconfirmTimeStart: time[0],
        fconfirmTimeEnd: time[1],
        fsampleSign: this.formSubmit.sampleSign,
        fsampleType: this.formSubmit.sampleType,
        fbusinessType: this.formSubmit.businessType,
        fproduct: this.formSubmit.product,
        fqcResult: this.formSubmit.qcResult,
        fwaitDateSize: this.formSubmit.waitDateSize,
        foutputProportionStatus: this.formSubmit.outputProportionStatus,
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        fprojectNameList: util.setGroupData(this.formSubmit.projectName, '、', false),
        foldSampleName: this.formSubmit.oldSampleName
      }
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getUnTestSampleList({
        ...params,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }}, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.records || []
        rows.forEach(v => {
          const standardConfig = {
            1: '满足产出标准',
            0: '未满足产出标准'
          }
          const item = {
            id: v.fid,
            orderDataSize: v.forderDataSize,
            sampleSign: v.fsampleSign,
            projectName: v.fprojectName,
            sampleName: v.fsampleName,
            walkthroughDateSize: v.fwalkthroughDateSize,
            dataSize: v.fdataSize,
            waitDateSize: v.fwaitDateSize,
            seqTimes: v.fseqTimes,
            outputProportion: v.foutputProportion,
            dataProductionStandard: v.fdataProductionStandard,
            outputProportionStatus: v.foutputProportionStatus,
            outputProportionStatusText: standardConfig[v.foutputProportionStatus],
            qcResult: v.fqcResult,
            product: v.fproduct,
            remark: v.fremark,
            confirmTime: v.fconfirmTime,
            oldSampleName: v.foldSampleName
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 添加备注
    handleAddRemark () {
      if (this.selectedRowsSize < 1) {
        this.$message.error('请勾选至少一条样本数据！')
        return
      }
      this.ids = [...this.selectedRows.keys()]
      this.setRemarkVisible = true
    },
    // 标记样本
    handleSignSample (command) {
      // 如果选中”勾选样本“，前置条件为勾选至少一条以上数据，否则提示：请勾选至少一条样本数据！
      if (command === 1 && this.selectedRowsSize < 1) {
        this.$message.error('请勾选至少一条样本数据！')
        return
      }
      this.ids = [...this.selectedRows.keys()]
      this.type = command
      this.signSampleVisible = true
    },
    // 取消标记样本
    async cancelSignSample () {
      await this.$confirm(`已选中${this.selectedRowsSize}个样本，确认取消标记么？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const ids = [...this.selectedRows.keys()]
      const {res} = await awaitWrap(cancelSignSample({
        fids: ids,
        file: new File([], '')
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('取消标记成功')
        await this.getData()
      }
    },
    // 取消标记样本
    handleCancelSignSample (command) {
      if (command === 1 && this.selectedRowsSize < 1) {
        this.$message.error('请勾选至少一条样本数据！')
        return
      }
      if (command === 1) {
        this.cancelSignSample()
        return
      }
      this.cancelSignSampleVisible = true
    },
    // 选择导出类型
    handleCommand (command) {
      command === 2 ? this.handleExportAll() : this.handleDownloadInfo()
    },
    async handleExportAll () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downInspection({
        ...this.getParams()
      }))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 下载送检信息
    async handleDownloadInfo () {
      // 勾选1条/多条数据，点击【送检信息下载】，否则提示：请勾选至少1条数据！
      if (this.selectedRowsSize < 1) {
        this.$message.error('请勾选至少1条数据！')
        return
      }
      this.downloadLoading = true
      const {res} = await awaitWrap(downInspection({
        fids: [...this.selectedRows.keys()]
      }))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 下载未到样信息
    handleDownloadUnArriveSample () {
      this.downloadUnArriveSampleVisible = true
    },
    handleViewSeqTimes (row) {
      this.seqTimesVisible = true
      this.id = row.id
    }
  }
}
</script>

<style scoped lang="scss">

</style>
