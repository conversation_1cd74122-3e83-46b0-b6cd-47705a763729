<template>
  <div>
    <div class="search-form">
      <el-form size="mini" :model="form" label-width="80px" inline @keyup.enter.native="handleSearch">
        <el-form-item label="样本编号">
          <el-input size="mini" v-model.trim="form.sampleCode" class="form-content" clearable></el-input>
        </el-form-item>
        <el-form-item label="快递单号">
          <el-input v-model.trim="form.trackingNum" class="form-content" clearable></el-input>
        </el-form-item>
        <el-form-item label="快递公司">
          <el-input v-model.trim="form.courierCompany" class="form-content" clearable></el-input>
        </el-form-item>
        <el-form-item label="送检医院">
          <el-input v-model.trim="form.hospital" class="form-content" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model.trim="form.customerName" class="form-content" clearable maxlength="100"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model.trim="form.dateType" placeholder="请选择" size="mini" style="width: 100px;" @change="handleSearchTimeChange">
            <el-option label="寄件时间" :value="0"></el-option>
            <el-option label="预计到达时间" :value="1"></el-option>
            <el-option label="签收时间" :value="2"></el-option>
          </el-select>
          <el-date-picker
            v-model.trim="form.date[0]"
            type="date"
            size="mini"
            value-format="yyyy-MM-dd"
            class="form-content"
            placeholder="选择日期">
          </el-date-picker>
          <span>--</span>
          <el-date-picker
            v-model.trim="form.date[1]"
            type="date"
            size="mini"
            value-format="yyyy-MM-dd"
            class="form-content"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否延期">
          <el-select v-model.trim="form.isExtension" size="mini" class="form-content">
            <el-option label="全部" value=""></el-option>
            <el-option :value="1" label="是"></el-option>
            <el-option :value="0" label="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="快递状态">
          <el-select v-model.trim="form.expressStatus" size="mini" class="form-content">
            <el-option label="全部" :value="2"></el-option>
            <el-option label="已签收" :value="1"></el-option>
            <el-option label="未签收" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label-width="100px" label="订单录入人">
          <el-input v-model.trim="form.orderEntryPerson" class="form-content" clearable maxlength="50"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <div class="operate-btns-group">
<!--        <template v-if="false">-->
        <template v-if="$setAuthority('003003001', 'buttons')">
          <el-button type="primary" size="mini" @click="handleSampleSign">签收</el-button>
        </template>
        <template v-if="$setAuthority('003003002', 'buttons')">
<!--        <template v-if="false">-->
          <el-button type="primary" size="mini" @click="handleSampleAbnormalSign">异常登记</el-button>
        </template>
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button size="mini" @click="handleReset">重置</el-button>
      </div>
      <el-table
        ref="table"
        :data="tableData"
        height="calc(100vh - 74px - 40px - 83px - 42px - 32px)"
        class="reservationTable"
        border
        style="width: 100%"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @sort-change="handleTableSort">
        <el-table-column type="selection" width="45"></el-table-column>
        <el-table-column prop="sampleCode" label="样本编号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="expressText" label="快递状态" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleType" label="样本类型" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="产品名称" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="area" label="生产片区" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="trackingNum" label="快递单号" width="220" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deliveryDate" sortable="custom" label="寄件时间" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="estimatedArrivalDate" sortable="custom" label="预计到达时间" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="abnormalNotes" label="异常备注" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deliveryAddress" sortable="custom" label="寄送地址" width="220" show-overflow-tooltip></el-table-column>
        <el-table-column prop="courierCompany" label="快递公司" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="trainNum" label="车次" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="isExtension" label="是否延期" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="submissionDate" label="签收时间" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="signer" label="签收人" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <desensitization :info="scope.row.signer" type="name"></desensitization>
          </template>
        </el-table-column>
        <el-table-column prop="transportationTime" label="运输耗时" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="customerName" label="客户名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="hospital" sortable="custom" label="送检医院" width="220" show-overflow-tooltip></el-table-column>
        <el-table-column prop="notes" label="备注" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sell" label="销售" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderEntryPerson" label="订单录入人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="recipient" label="收件人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="phone" label="联系电话" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <desensitization :info="scope.row.phone" type="phone"></desensitization>
          </template>
        </el-table-column>
        <el-table-column prop="receivingAddress" label="收样地址" width="220" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </div>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  name: 'sampleSigningManagement',
  mixins: [mixins.tablePaginationCommonData],
  beforeMount () {
    this.$_setTbHeight()
    window.addEventListener('resize', this.$_setTbHeight)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.$_setTbHeight)
    })
  },
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        sampleCode: '',
        trackingNum: '', // 快递单号
        courierCompany: '', // 快递公司
        productionAreaName: '',
        hospital: '',
        dateType: 0, // 时间类型
        date: ['', ''],
        customerName: '',
        orderEntryPerson: '',
        isExtension: '', // 是否延期
        expressStatus: 0, // 快递状态
        sort: '', // 排序字段
        order: '' // 排序方式 desc || asc || ''
      },
      tableHeight: 0,
      formSubmit: {},
      needSortBackendField: { // 需要排序的前台对应的后台地址
        estimatedArrivalDate: 'pre_receive_time', // 预计送达时间
        deliveryAddress: 'send_addr', // 寄件地址
        hospital: 'name', // 医院
        deliveryDate: 'send_time' // 寄件时间
      },
      searchDateType: 'deliveryDate' // deliveryDate || estimatedArrivalDate || submissionDate
    }
  },
  methods: {
    $_setTbHeight () {
      let h1 = document.documentElement.clientHeight - 1
      this.tableHeight = h1 - 64 - 50 - 20 - 51 * 2 - 45 - 52 - 20
    },
    getData () {
      this.$ajax({
        url: '/sample/express/page_sample_express',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {
            sampleNum: this.formSubmit.sampleCode,
            expressNum: this.formSubmit.trackingNum,
            expressCompany: this.formSubmit.courierCompany,
            sendHospital: this.formSubmit.hospital,
            timeType: this.formSubmit.dateType,
            productionAreaName: this.formSubmit.productionAreaName,
            timeStart: this.formSubmit.date[0],
            timeEnd: this.formSubmit.date[1],
            delay: this.formSubmit.isExtension,
            expressStatus: this.formSubmit.expressStatus,
            sort: this.formSubmit.sort,
            order: this.formSubmit.order,
            customerName: this.formSubmit.customerName,
            orderEntryPerson: this.formSubmit.orderEntryPerson
          }
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sample_express_id,
              sampleCode: v.sample_num,
              expressStatus: v.express_status, // 快递状态
              sampleType: v.sample_type,
              productName: v.product_name,
              area: v.productionAreaName,
              trackingNum: v.express_num,
              deliveryDate: v.send_time,
              estimatedArrivalDate: v.pre_receive_time,
              deliveryAddress: v.send_addr,
              courierCompany: v.express_company,
              trainNum: v.train_number,
              isExtension: v.isdelay,
              submissionDate: v.sign_time,
              signer: v.sign_person,
              transportationTime: v.transport_use_time,
              hospital: v.name,
              notes: v.remark,
              sell: v.sales_person,
              abnormalNotes: v.ex_remark,
              recipient: v.collect_person,
              phone: v.phone,
              receivingAddress: v.collect_addr,
              customerName: v.customerName,
              orderEntryPerson: v.factotumName
            }
            item.expressText = item.expressStatus === '1' ? '已签收' : '未签收'
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 签收
    handleSampleSign () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要进行签收的快递样例')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.$confirm('确认签收选择的样本？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/sample/express/sign_sample_express',
          data: {
            sampleExpressIds: ids.toString()
          },
          method: 'get',
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('签收成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 异常登记
    handleSampleAbnormalSign () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要登记异常的快递样例')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.$prompt('异常描述', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: function (value) {
          let v = value ? value.trim() : ''
          return v.length > 0 && v.length <= 50
        },
        inputErrorMessage: '异常不能为空并且长度不能大于50'
      }).then(({ value }) => {
        this.$ajax({
          url: '/sample/express/exception_sample_express',
          data: {
            sampleExpressIds: ids.toString(),
            exceptionRemark: value
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('登记异常成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    handleSearch () {
      this.formSubmit = JSON.parse(JSON.stringify(this.form))
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        sampleCode: '',
        trackingNum: '', // 快递单号
        courierCompany: '', // 快递公司
        hospital: '',
        dateType: 0, // 时间类型
        date: ['', ''],
        isExtension: '', // 是否延期
        expressStatus: '', // 快递状态
        customerName: '',
        orderEntryPerson: '',
        productionAreaName: '',
        sort: '', // 排序字段
        order: '' // 排序方式 desc || asc || ''
      }
      this.handleSearch()
    },
    // 选择时间的改变
    handleSearchTimeChange () {
      this.form.date = ['', '']
    },
    // 点击行
    handleRowClick (row) {
      // if (!this.selectedRows.has(row.patientCode)) {
      //   this.$refs.table.clearSelection()
      //   this.selectedRows.clear()
      // }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 排序
    handleTableSort ({column, prop, order}) {
      let orderKey = {
        descending: 'desc',
        ascending: 'asc'
      }
      if (order) {
        this.form.sort = this.needSortBackendField[prop]
        this.form.order = orderKey[order]
      } else {
        this.form.sort = ''
        this.form.order = ''
      }
      this.handleSearch()
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id)
        ? this.selectedRows.delete(row.id)
        : this.selectedRows.set(row.id, row)
    }
  }
}
</script>

<style scoped>
.form-content{
  width: 150px;
}
.buttonGroup{
  height: 45px;
  display: flex;
  align-items: center;
}
</style>
