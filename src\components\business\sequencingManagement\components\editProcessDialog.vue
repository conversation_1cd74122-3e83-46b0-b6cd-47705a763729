<template>
  <el-dialog
    title="配置工序"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @open="handleOpen">
    <el-form :model="form" label-suffix=":" :inline="true" :rules="rules" label-width="100px">
      <el-form-item label="文库修饰类型">
        {{form.type}}
      </el-form-item>
      <el-form-item label="makeDNB">
        <el-select
          v-if="showMakeDNBProcess.includes(form.type)"
          v-model.trim="form.makeDNB"
          placeholder="请选择"
          size="mini"
          style="width: 100%;"
          @change="handleMakeDNBChange"
        >
          <el-option
            v-for="item in makeDNBMethods"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
        <div v-else>
          {{form.makeDNB}}
        </div>
      </el-form-item>
    </el-form>
    <el-table
      ref="table"
      :data="tableData"
      :cell-style="handleCellStyle"
      class="table"
      size="mini"
      border
      style="width: 100%"
      max-height="400">
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="process" label="环节"></el-table-column>
      <el-table-column prop="explain" label="说明" min-width="180">
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <div v-if="(scope.$index === 0 && isAddSample) || scope.row.isEdit || scope.row.disabled || scope.row.process === 'makeDNB'">-</div>
          <div v-else>
            <el-button v-if="scope.row.isDelete" type="text" size="mini" @click="handleDisable(scope.$index, 0, scope.row.index)">恢复</el-button>
            <el-button v-if="!scope.row.isDelete" type="text" style="color: red" size="mini" @click="handleDisable(scope.$index, 1, scope.row.index)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'

export default {
  name: 'editProcessDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    isAddSample: {
      type: Boolean,
      default: false
    },
    indexList: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    },
    libType: {
      type: String,
      default: ''
    },
    libConcentration: {
      type: String,
      default: null
    },
    makeDNB: {
      type: String,
      default: ''
    },
    currentWorkFlowId: {
      type: String,
      default: null
    },
    currentProcess: {
      type: Number,
      default: 1
    },
    length: {
      type: Number,
      default: null
    },
    delPooling: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        type: '',
        makeDNB: ''
      },
      rules: {},
      makeDNBMethods: ['一步法', '两步法'],
      showMakeDNBProcess: ['illumina-未磷酸化', 'illumina-已磷酸化', 'MGI-未环化-单', 'MGI-未环化-双'],
      process: [
        {index: 1, process: 'pooling', explain: '', isDelete: 0},
        {index: 2, process: '转化', explain: 'illumina-未磷酸化”的才会执行转化', isDelete: 0},
        {index: 3, process: 'pooling', explain: '', isDelete: 1},
        {index: 4, process: '环化', explain: '“未环化”、且设置为“两步法”才会执行环化', isDelete: 0},
        {index: 5, process: 'makeDNB', explain: '', isDelete: 0}
      ],
      processFlow: {
        'illumina-未磷酸化': [0, 1, 2, 3, 4],
        'illumina-已磷酸化': [2, 3, 4],
        'MGI-未环化-单': [0, 3, 4],
        'MGI-未环化-双': [0, 3, 4],
        'illumina-已环化': [0, 4],
        'MGI-已环化': [0, 4]
      },
      tableData: [
      ]
    }
  },
  methods: {
    handleOpen () {
      this.process = [
        {index: 1, process: 'pooling', explain: '', isDelete: 0},
        {index: 2, process: '转化', explain: 'illumina-未磷酸化”的才会执行转化', isDelete: 0},
        {index: 3, process: 'pooling', explain: '', isDelete: 1},
        {index: 4, process: '环化', explain: '“未环化”、且设置为“两步法”才会执行环化', isDelete: 0},
        {index: 5, process: 'makeDNB', explain: '', isDelete: 0}
      ]
      this.tableData = []
      this.form.type = this.type
      this.form.makeDNB = this.makeDNB
      // 设置流程 & 禁用
      this.handleSetTableData()
    },
    /**
     * ①除illumina/MGI已环化，用户可修改makeDNB，则”环化“环节需要自动增删，反之，”环化“环节的增删，makeDNB也需要自动变化；
     * ②对应修饰类型/一步法/两步法固定要求环节的规则如下：
     *   默认工序中带有pooling的都必须展示，删除后字体置灰，按钮变为【恢复】，以便用户可恢复相关工序；
     *   illumina未磷酸化，工序必须包含“转化”（展示转化，不可删除）
     *   illumina已磷酸化/MGI-未环化-单/MGI-未环化-双，工序不能包含“转化”（不展示转化）
     *   illumina已环化/MGI已环化，工序不能包含“环化”（不展示环化）
     *   样本做一步法的，工序不能包含“环化”（环化--可【恢复】，改为两步法）
     *   样本做两步法的（除illumina已环化、MGI已环化），工序必须包含“环化”（环化--可【删除】，改为一步法）
     */
    // 设置流程 & 禁用
    handleSetTableData () {
      let flows = this.processFlow[this.form.type]
      // 判断文库浓度
      // if (this.libConcentration < 1 && this.type === 'illumina-未磷酸化') {
      //   // 第一位和第二位换位
      //   flows = [flows[1], flows[0], ...flows.slice(2)]
      // }
      this.tableData = flows.map((v, i) => {
        const flow = JSON.parse(JSON.stringify(this.process[v]))
        flow.isDelete = this.indexList.includes(v + 1)
        if (flow.index === 4 && this.makeDNB === '一步法') {
          flow.isDelete = 1
        }
        // if (flow.index === 3 && this.type === 'illumina-未磷酸化') {
        //   flow.isDelete = 1
        // }
        flow.disabled = 0
        // illumina未磷酸化，工序必须包含“转化”（展示转化，不可删除）
        if (this.type === 'illumina-未磷酸化' && flow.index === 2) {
          flow.isEdit = 1
        }
        let index = flow.index * 1
        if (i !== 0 && index === 1) {
          index = 3
        }
        // // 根据当前环节设置工序
        if (this.isAddSample) {
          if (index < this.currentProcess) {
            flow.disabled = 1
          }
        } else {
          console.log(this.delPooling, flow.index === 1, flow.index === 3, 'this.delPooling')
          if (this.delPooling && (flow.index === 1 || flow.index === 3)) {
            flow.isDelete = 1
          }
          if (this.currentWorkFlowId) {
            if (index <= this.currentWorkFlowId) {
              flow.disabled = 1
            }
          }
        }
        return flow
      }).filter(v => v.disabled !== 1)
    },
    // 禁用或删除环节
    handleDisable (index, isDelete, flag) {
      // if (isDelete && this.currentWorkFlowId === 4) {
      //   this.$message.error(`当前实验样本所属文库类型${this.libType}，不能转一步法，请检查！`)
      //   return
      // }
      /**
       * 设置makeDNB 两步法
       */
      const notOneLibType = [
        'PCR-free文库',
        '10xATAC单细胞文库',
        '甲基化文库-WGBS',
        '甲基化文库-RRBS',
        '甲基化文库-其他',
        'C4 v2.0单细胞文库',
        'C4 ATAC单细胞文库'
      ]

      if (flag === 4 && notOneLibType.some(lib => this.libType.includes(lib))) {
        this.$message.error(`当前实验样本所属文库类型${this.libType}，不能转一步法，请检查！`)
        return
      }

      this.tableData = this.tableData.map((v, i) => {
        if (i === index) {
          v.isDelete = isDelete
        }
        if (v.index === 4 && v.isDelete === 0) {
          this.form.makeDNB = '两步法'
        }
        if (v.index === 4 && v.isDelete === 1) {
          this.form.makeDNB = '一步法'
        }
        return v
      })
    },
    // 设置删除单元格样式
    handleCellStyle ({row}) {
      if (row.isDelete || row.disabled) {
        return {color: '#ccc !important', cursor: 'not-allowed'}
      }
      return ''
    },
    // 根据makeDNB修改工序
    handleMakeDNBChange () {
      if (this.form.makeDNB === '一步法' && (this.currentWorkFlowId === 4 || this.currentProcess === 4)) {
        this.$message.error(`当前环节不能转一步法！`)
        this.form.makeDNB = '两步法'
        return
      }
      /**
       * 设置makeDNB 两步法
       */
      const notOneLibType = [
        'PCR-free文库',
        '10xATAC单细胞文库',
        '甲基化文库-WGBS',
        '甲基化文库-RRBS',
        '甲基化文库-其他',
        'C4 v2.0单细胞文库',
        'C4 ATAC单细胞文库'
      ]
      if (notOneLibType.some(lib => this.libType.includes(lib))) {
        this.$message.error(`当前实验样本所属文库类型${this.libType}，不能转一步法，请检查！`)
        this.form.makeDNB = '两步法'
        return
      }
      this.tableData = this.tableData.map((v) => {
        if (v.index === 4 && this.form.makeDNB === '一步法') {
          v.isDelete = 1
        }
        if (v.index === 4 && this.form.makeDNB === '两步法') {
          v.isDelete = 0
        }
        return v
      })
    },
    async handleFix (isSync) {
      await this.$confirm(
        `
        <div>样本数：${isSync ? this.length : 1}</div>
        <div>配置的工序流程为： <span style="color: #c74747;">${this.tableData.filter(v => !v.isDelete).map(v => v.process).join('-')}</span></div>
        <div>提交后，样本将按照配置工序流转，确认继续提交？</div>
      `, '提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      this.$emit('dialogConfirmEvent', {
        type: this.form.type,
        makeDNB: this.form.makeDNB,
        processFlow: this.tableData,
        isSync: isSync
      })
      this.$message.success('修改成功')
      this.visible = false
    },
    // 保存工序
    async handleConfirm () {
      if (this.length > 1) {
        this.$confirm(`同项目、同文库修饰类型的样本还有${this.length - 1}条，是否同步更新数据？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleFix(1)
        }).catch(() => {
          this.handleFix(0)
        })
        return
      }
      this.handleFix(0)
    }
  }
}
</script>

<style scoped>

</style>
