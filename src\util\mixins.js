import {computeObjectValidKeyNum} from '@/util/util'
import {awaitWrap, downloadFile, readBlob} from './util'
import {myAjax} from './ajax'
const tablePaginationCommonData = { // 表格分页一般需要的数据
  computed: {
    searchParamsKeyNum () {
      return computeObjectValidKeyNum(this.form, [])
    }
  },
  data () {
    return {
      tableData: [],
      realComponentTableDataName: 'tableData', // 项目中这个table的表格叫什么，便于修改
      pageSizes: [30, 50, 100],
      pageSize: 30,
      pageSizeChange: 50,
      currentPage: 1,
      totalPage: 0,
      loading: false,
      tbHeight: 200,
      selectedRows: new Map(),
      selectedRowsSize: 0,
      startPoint: undefined, // 多选起点
      endPoint: undefined, // 多选终点
      pin: false, // 是否按住，默认不按住
      searchDialogVisible: false // 搜索弹窗是否显示
    }
  },
  created () {
    // 监听keydown：获取键盘按住事件,code返回按住的键信息
    window.addEventListener('keydown', this.handleKeyDown)
    // 监听keyup：获取键盘松开事件,code返回按住的键信息
    window.addEventListener('keyup', this.handleKeyUp)
  },
  methods: {
    $_setTbHeight (staticHeight = 74 + 8 + 12 + 41 + 42 + 40 + 12 + 8, dynamicElement) {
      const setHeight = () => {
        const h1 = document.documentElement.clientHeight - 1
        let dynamicElementHeight = 0
        if (dynamicElement && document.querySelector(dynamicElement)) {
          dynamicElementHeight = document.querySelector(dynamicElement).offsetHeight
        }
        this.tbHeight = h1 - dynamicElementHeight - staticHeight
      }
      this.$nextTick(() => {
        setHeight()
        window.addEventListener('resize', setHeight)
        // 通过hook监听组件销毁钩子函数，并取消监听事件
        // https://cn.vuejs.org/v2/guide/components-edge-cases.html
        this.$once('hook:beforeDestroy', () => {
          window.removeEventListener('resize', setHeight)
        })
      })
    },
    rowStyle () {},
    handleKeyDown (code) {
      // 判断是否按住了shift键（左右都包括）
      if (code.keyCode === 16 && code.shiftKey && !this.pin) {
        this.pin = true// 标记按住了shift键
      }
    },
    handleKeyUp (code) {
      if (code.keyCode === 16) {
        this.pin = false// 标记松开了shift键
        this.startPoint = undefined// 清空多选起点
        this.endPoint = undefined// 清空多选终点
      }
    },
    handleSizeChange (size) {
      this.pageSize = size
      this.pageSizeChange = size
      this.currentPage = 1
      this.$nextTick(() => {
        if (this.$refs.table) this.$refs.table.bodyWrapper.scrollTop = 0
      })
      this.getData()
    },
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage
      this.$nextTick(() => {
        if (this.$refs.table) this.$refs.table.bodyWrapper.scrollTop = 0
      })
      this.getData()
    },
    clearMap () {
      this.selectedRows.clear()
      this.selectedRowsSize = 0
    },
    handleRefresh () {
      this[this.realComponentTableDataName] = []
      this.$nextTick(() => {
        this.$refs.table.bodyWrapper.scrollTop = 0
      })
      this.getData()
      this.clearMap()
    },
    handleVxeRowStyle ({row}) {
      const selectedRows = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRows.some(v => v.fid === row.fid)) {
        return {
          backgroundColor: '#c7e1ff !important'
        }
      }
      return null
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff !important'}
      }
      return ''
    },
    handleClassName ({row, rowIndex}) {
      if (row.isHeightLight) {
        return 'height-light-row'
      }
      return ''
    },
    // 点击行
    handleRowClick (row, c) {
      this.handleSelectTable(undefined, row)
    },
    // 按住shift多选
    shiftSelect (row) {
      if (this.pin) { // 按住了shift键
        if (this.startPoint || this.startPoint === 0) { // 之前有多选起点（第一条单独）
          this.endPoint = this.tableData.findIndex(v => v.id === row.id)// 把当前项的index标记为多选终点
          if (this.startPoint > this.endPoint) { // 如果起点大于终点，则替换否则保留，确保起点始终小于终点
            let temp = this.startPoint
            this.startPoint = this.endPoint
            this.endPoint = temp
          }
          // 勾选数据
          let rows = this.tableData.slice(this.startPoint, this.endPoint + 1)
          rows.forEach(row => {
            if (!this.selectedRows.has(row.id)) this.selectedRows.set(row.id, row)
          })
          this.startPoint = undefined// 清空多选起点
          this.endPoint = undefined// 清空多选终点
        } else { // 之前没有多选起点
          if (!this.selectedRows.has(row.id)) this.selectedRows.set(row.id, row)
          this.startPoint = this.tableData.findIndex(v => v.id === row.id)// 把当前项的index标记为多选起点
        }
      }
    },
    // 回显选中
    handleEchoSelect () {
      let rows = [...this.selectedRows.values()]
      if (this.$refs.table) {
        this.$refs.table.clearSelection()
        console.log(rows)
        rows.forEach(row => {
          row = this.tableData.find(data => data.id === row.id) || {}
          if (row.id) {
            this.$nextTick(() => {
              this.$refs.table.toggleRowSelection(row, true)
            })
          }
        })
      }
      this.selectedRowsSize = rows.length
    },

    // 选中行
    handleSelectTable (selection, row) {
      if (this.pin) {
        this.shiftSelect(row)
      } else {
        this.startPoint = undefined// 清空多选起点
        this.endPoint = undefined// 清空多选终点
        this.selectedRows.has(row.id)
          ? this.selectedRows.delete(row.id)
          : this.selectedRows.set(row.id, row)
      }
      this.handleEchoSelect()
    },
    // 清除当前页数据
    handleDelCurrentDataMap () {
      const data = this.tableData || []
      data.forEach((row) => {
        if (this.selectedRows.has(row.id)) {
          this.selectedRows.delete(row.id)
        }
      })
    },
    // 全选
    handleSelectAll (selection) {
      this.handleDelCurrentDataMap()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.selectedRowsSize = this.selectedRows.size
    }
  },
  beforeDestroy () {
    window.removeEventListener('keydown', this.handleKeyDown)
    window.removeEventListener('keyup', this.handleKeyUp)
  }
}

const dialogBaseInfo = { // dialog组件常用数据
  props: {
    'pvisible': {
      type: Boolean
    }
  },
  watch: {
    'pvisible': function (newVal) {
      this.visible = newVal
    },
    visible (newVal) {
      this.$emit('update:pvisible', newVal)
    },
    tableData: {
      async handler () {
        await this.$nextTick()
        if (this.$refs.table) {
          if (this.$refs.table) this.$refs.table.doLayout()
        }
      },
      deep: true
    }
  },
  data () {
    return {
      visible: this.pvisible
    }
  },
  methods: {
    handleValidForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.$message.error('表单存在错误，请检查')
          }
        })
      })
    },
    handleClose () {
      this.visible = false
      this.$emit('dialogCloseEvent')
    }
  }
}

// 上传相关

// 下载
const exportBaseInfo = {
  data () {
    return {
      downloadLoading: false,
      url: ''
    }
  },
  methods: {
    // 选择导出类型
    handleCommand (command) {
      command === 1 ? this.handleExportAll() : this.handleExport()
    },
    async downloadExport (res) {
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 按条件导出
    async handleExportAll () {
      const params = this.setParams()
      await this.$confirm('是否确认导出查询数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const {res} = await awaitWrap(myAjax({
        method: 'post',
        url: this.url,
        responseType: 'blob',
        data: params
      }))
      await this.downloadExport(res)
    },
    // 导出所选
    async handleExport () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords() || [...this.selectedRows.values()]
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rowsId = selectRecords.map(item => item.fid)
      await this.$confirm('否确认导出选中数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const {res} = awaitWrap(myAjax({
        method: 'post',
        url: this.url,
        responseType: 'blob',
        data: {
          fidList: rowsId
        }
      }))
      await this.downloadExport(res)
    }
  }
}
export default {
  tablePaginationCommonData,
  dialogBaseInfo,
  exportBaseInfo
}
