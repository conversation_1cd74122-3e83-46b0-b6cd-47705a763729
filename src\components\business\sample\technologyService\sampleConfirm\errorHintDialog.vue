<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="数据错误提示" width="800px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)">
      <template>
        <el-table
          :data="tableData"
          stripe
          border
          max-height="400px"
        >
          <el-table-column prop="title" label="原始编号" width="220px"></el-table-column>
          <el-table-column prop="actualContent" label="异常说明" min-width="220px"></el-table-column>
        </el-table>
      </template>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'

export default {
  name: `errorDialog`,
  data () {
    return {
      visible: false,
      downloadLoading: false,
      tableData: []
    }
  },
  methods: {
    handleClose () {
      this.visible = false
      this.tableData = []
      this.downloadLoading = false
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table td, .el-table th{
  padding: 6px 0;
}
.title{
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
}
</style>
