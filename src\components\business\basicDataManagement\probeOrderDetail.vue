<template>
  <div>
    <div class="info-title margin" style="display: flex; justify-content: space-between;align-items: center;">
      <div class="title">组织变异分析结果</div>
      <div>
        <el-button size="mini" @click="$router.back()">关闭</el-button>
      </div>
    </div>
    <snv-list></snv-list>
    <sv-list></sv-list>
  </div>
</template>

<script>
import snvList from './snvList'
import svList from './svlist'
export default {
  components: {
    snvList,
    svList
  },
  methods: {
    handleToOrderDetail (sampleNum) {
      this.$router.push('/business/subpage/probeOrderDetail?sampleNum=' + sampleNum)
    }
  }
}
</script>

<style scoped lang="scss">
.info-title {
  padding: 10px;
  background-color: #aaa;
  border: 1px solid #eee;
}
.title {
  font-weight: 700;
  font-size: 24px;
}
</style>
