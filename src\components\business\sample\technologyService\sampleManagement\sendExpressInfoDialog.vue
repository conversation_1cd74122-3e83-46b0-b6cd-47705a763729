<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="快递信息"
      width="1300px"
      append-to-body
      @open="handleOpen">
      <div class="send-email-dialog">
        <el-form ref="form" :model="form" :rules="rules" :inline="true" label-width="110px" size="mini">
          <el-form-item label="寄件人" prop="sender">
            <el-input v-model="form.sender" placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="寄件电话" prop="senderPhone">
            <el-input v-model="form.senderPhone" placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item v-if="showSendSelect" label="寄件地区" prop="sendAddressCode">
            <el-cascader
              v-model.trim="form.sendAddressCode"
              ref="cascader"
              :options="regionList"
              :props ="{label: 'label', value: 'regionId'}"
              size="mini"
              clearable
              filterable
              class="form-width">
            </el-cascader>
          </el-form-item>
          <el-form-item label="寄件地址" prop="senderAddressDetail">
            <el-input v-model="form.senderAddressDetail" placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="收件人" prop="recipient">
            <el-input v-model="form.recipient" placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="收件电话" prop="recipientPhone">
            <el-input v-model="form.recipientPhone" placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item v-if="showRecipientSelect" label="收件地区" prop="recipientAddressCode">
            <el-cascader
              v-model.trim="form.recipientAddressCode"
              ref="cascader"
              :options="regionList"
              :props ="{label: 'label', value: 'regionId'}"
              size="mini"
              clearable
              filterable
              class="form-width">
            </el-cascader>
          </el-form-item>
          <el-form-item label="收件地址" prop="recipientAddressDetail">
            <el-input v-model="form.recipientAddressDetail" placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="快递公司" prop="courierCompany">
            <el-select v-model="form.courierCompany" class="form-width" placeholder="请选择（必填）">
              <el-option
                :key="k"
                :label="v"
                :value="k"
                v-for="(v, k) in courierCompanyObj">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="快递类型" prop="expressType">
            <el-select v-model="form.expressType" class="form-width" placeholder="请选择（必填）">
              <el-option
                :key="k"
                :label="v"
                :value="k"
                v-for="(v, k) in expressTypeObj">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="期待上门时间" prop="expectedTime">
            <el-cascader
              v-model="form.expectedTime"
              :options="expectTimeList"
              placeholder="请输入（必填）"
              separator=" "
              class="form-width">
            </el-cascader>
          </el-form-item>
          <el-form-item label="付款方式" prop="payType">
            <el-select v-model="form.payType" class="form-width" placeholder="请选择（必填）">
              <el-option
                :key="k"
                :label="v"
                :value="k"
                v-for="(v, k) in payTypeObj">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="['1', '2'].includes(form.payType)" label="月结账号" prop="accountType">
            <el-select v-model="form.accountType" class="form-width" placeholder="请选择（必填）">
              <el-option
                :key="k"
                :label="item.label"
                :value="item.value"
                v-for="(item, k) in accounts">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="保价" prop="insured">
            <el-select v-model="form.insured" class="form-width" placeholder="请选择（必填）">
              <el-option
                :key="k"
                :label="v"
                :value="k"
                v-for="(v, k) in insuredObj">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="+form.insured === 1" label="声明价值" prop="declaredValue">
            <el-input v-model="form.declaredValue" :min="1" :max="500000" placeholder="请输入（必填）" type="number" class="form-width"></el-input>
          </el-form-item>
          <div>
            <el-form-item label="给快递员留言" prop="notes">
              <el-input
                v-model="form.notes"
                :rows="3"
                placeholder="请输入"
                type="textarea"
                maxlength="150"
                show-word-limit
                style="width: 625px;"></el-input>
            </el-form-item>
          </div>

        </el-form>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="submitLoading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import constants from '@/util/constants'
import Cookies from 'js-cookie'
import util from '@/util/util'
import AddressParse from 'address-parse'

export default {
  name: 'sendExpressInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: Number
    },
    productionAreaId: {
      type: Number
    }
  },
  computed: {
    // 期待上门时间
    expectTimeList () {
      let list = this.timeList || []
      list[1].children = this.expectTimeChildrenList // 添加时间列表
      list[2].children = this.expectTimeChildrenList
      // 获取当前时间
      let cur = new Date(new Date().getTime())
      let curTime = util.getTimeDiff(cur, true).substring(11, 13)
      // 当前时间在21：00以内添加
      if (+curTime < 21) list[0].children.push({ value: '一小时内', label: '一小时内' })
      // 将大于当前时间的选项加入
      this.expectTimeChildrenList.forEach((v) => {
        let time = v.label.substring(0, 2)
        if (+time > +curTime) list[0].children.push(v)
      })
      return list
    }
  },
  data () {
    // 明天
    let tomorrow = () => {
      let cur = new Date(new Date().getTime() + 24 * 3600 * 1000)
      return util.getTimeDiff(cur, true).substring(5, 10)
    }
    // 后天
    let theDayAfterTomorrow = () => {
      let cur = new Date(new Date().getTime() + 2 * 24 * 3600 * 1000)
      return util.getTimeDiff(cur, true).substring(5, 10)
    }
    // 今天
    let today = () => {
      let cur = new Date(new Date().getTime())
      return util.getTimeDiff(cur, true).substring(5, 10)
    }
    return {
      form: {
        recipient: '', // 收件人
        recipientPhone: '', // 收件人电话
        recipientAddressCode: '', // 收件地址区域code
        recipientAddress: '', // 收件地址
        recipientAddressDetail: '', // 寄件详细地址
        sender: '', // 寄件人
        senderPhone: '', // 寄件电话
        sendAddressCode: [], // 寄件地址区域code
        senderAddress: [], // 寄件地址
        senderAddressDetail: '', // 寄件详细地址
        courierCompany: '顺丰快递', // 快递公司
        payType: '', // 付款方式
        accountType: '', // 账号类型
        expressType: '', // 快递类型
        expectedTime: '', // 期待上门时间
        insured: '2', // 保价
        declaredValue: '', // 声明价值
        notes: '' // 给快递员留言
      },
      showSendSelect: false,
      showRecipientSelect: false,
      headers: {
        token: Cookies.get('token')
      },
      uploadUrl: constants.JS_CONTEXT + '/cos/system/upload',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false,
      expressDescription: `温馨提示：物流费用按顺丰快递实际发生费用为准，将在尾款结算或月度结算中进行统一收取。<br/>如有疑问请咨询项目管理或销售人员。`,
      accounts: {
        '**********': '深圳月结',
        '**********': '苏州月结',
        '': '北京月结'
      },
      payTypeObj: {
        1: '寄付月结',
        2: '第三方付',
        3: '到付'
      }, // 付款方式
      courierCompanyObj: {
        '顺丰快递': '顺丰快递'
      },
      expressTypeObj: {
        '顺丰标快': '顺丰标快',
        '顺丰特快': '顺丰特快'
        // 顺丰即日: '顺丰即日'
      }, // 快递类型
      insuredObj: {
        1: '保价',
        2: '不保价'
      },
      submitLoading: false,
      timeList: [
        // 时间选择选项
        {
          value: `${new Date().getFullYear()}-${today()}`,
          label: `今天(${today()})`,
          children: []
        },
        {
          value: `${new Date().getFullYear()}-${tomorrow()}`,
          label: `明天(${tomorrow()})`,
          children: []
        },
        {
          value: `${new Date().getFullYear()}-${theDayAfterTomorrow()}`,
          label: `后天(${theDayAfterTomorrow()})`,
          children: []
        }
      ],
      expectTimeChildrenList: [
        {value: '08:00:00', label: '08:00 ~ 09:00'},
        {value: '09:00:00', label: '09:00 ~ 10:00'},
        {value: '10:00:00', label: '10:00 ~ 11:00'},
        {value: '11:00:00', label: '11:00 ~ 12:00'},
        {value: '12:00:00', label: '12:00 ~ 13:00'},
        {value: '13:00:00', label: '13:00 ~ 14:00'},
        {value: '14:00:00', label: '14:00 ~ 15:00'},
        {value: '15:00:00', label: '15:00 ~ 16:00'},
        {value: '16:00:00', label: '16:00 ~ 17:00'},
        {value: '17:00:00', label: '17:00 ~ 18:00'},
        {value: '18:00:00', label: '18:00 ~ 19:00'},
        {value: '19:00:00', label: '19:00 ~ 20:00'},
        {value: '20:00:00', label: '20:00 ~ 21:00'}
      ],
      regionList: [], // 地区
      area: '', // 订单所选片区
      senderAddress: '',
      rules: {
        sender: [
          {required: true, message: '请输入寄件人', trigger: 'blur'}
        ],
        senderPhone: [
          {required: true, message: '请输入寄件电话', trigger: 'blur'},
          {required: false, validator: util.validateElementPhone, trigger: ['change', 'blur']}
        ],
        sendAddressCode: [
          {required: true, message: '请选择寄件地区', trigger: ['change', 'blur']}
        ],
        senderAddressDetail: [
          {required: true, message: '请选择寄件地址', trigger: 'blur'}
        ],
        recipient: [
          {required: true, message: '请输入收件人', trigger: 'blur'}
        ],
        recipientPhone: [
          {required: true, message: '请输入收件电话', trigger: 'blur'},
          {required: false, validator: util.validateElementPhone, trigger: ['change', 'blur']}
        ],
        recipientAddressCode: [
          {required: true, message: '请选择收件地区', trigger: ['change', 'blur']}
        ],
        recipientAddressDetail: [
          {required: true, message: '请选择收件地址', trigger: 'blur'}
        ],
        payType: [
          {required: true, message: '请选择付款方式', trigger: ['change', 'blur']}
        ],
        courierCompany: [
          {required: true, message: '请选择快递公司', trigger: 'blur'}
        ],
        expressType: [
          {required: true, message: '请选择快递类型', trigger: ['change', 'blur']}
        ],
        accountType: [
          {required: true, message: '请选择月结账号', trigger: 'blur'}
        ],
        expectedTime: [
          {required: true, message: '请选择期待上门时间', trigger: ['change', 'blur']}
        ],
        insured: [
          {required: true, message: '请选择保价', trigger: 'blur'}
        ],
        declaredValue: [
          {required: true, message: '请输入声明价值', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(async () => {
        this.$refs.form.resetFields()
        this.form = {
          recipient: '', // 收件人
          recipientPhone: '', // 收件人电话
          recipientAddressCode: '', // 收件地址区域code
          recipientAddress: '', // 收件地址
          recipientAddressDetail: '', // 寄件详细地址
          sender: '', // 寄件人
          senderPhone: '', // 寄件电话
          sendAddressCode: [], // 寄件地址区域code
          senderAddress: [], // 寄件地址
          senderAddressDetail: '', // 寄件详细地址
          courierCompany: '顺丰快递', // 快递公司
          payType: '', // 付款方式
          accountType: '', // 账号类型
          expressType: '', // 快递类型
          expectedTime: '', // 期待上门时间
          insured: '2', // 保价
          declaredValue: '', // 声明价值
          notes: '' // 给快递员留言
        }
        await this.getAllRegionData()
        this.getExpressInfo()
        this.getReceiveInfo()
      })
    },
    // 获取片区快递信息
    getExpressInfo () {
      this.$ajax({
        url: '/sample/t7_return/get_lab_express_info',
        method: 'get',
        data: {
          productionAreaId: this.productionAreaId
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.form.sender = data.fsender // 寄件人
          this.form.senderPhone = data.fsenderPhone // 寄件电话
          this.form.senderAddressDetail = data.fsenderAddress // 寄件地址
          let addressData = this.handleDistinguish(data.fsenderAddress) || [] // 寄件地址区域code
          if (addressData.length !== 3) {
            // 显示寄件省市区选择框
            this.showSendSelect = true
          }
          this.form.sendAddressCode = addressData.map(v => v.regionId)
          let account = data.expressAccountList || []
          this.accounts = []
          account.forEach(v => {
            let account = {
              label: v.faccountName,
              value: v.faccountValue
            }
            this.accounts.push(account)
          })
        }
      })
    },
    // 获取收件人信息
    getReceiveInfo () {
      this.$ajax({
        url: '/sample/t7_return/get_t7_return_order_by_id',
        method: 'get',
        data: {
          orderId: this.orderId
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.form.recipient = data.freceiver
          this.form.recipientPhone = data.fphone
          this.form.recipientAddressDetail = data.faddress
          let addressData = this.handleDistinguish(data.faddress) || [] // 寄件地址区域code
          if (addressData.length !== 3) {
            // 显示收件省市区选择框
            this.showRecipientSelect = true
          }
          this.form.recipientAddressCode = addressData.map(v => v.regionId)
        }
      })
    },
    /**
     * 智能识别 根据地址返回对应省市区信息
     * @param textAdd 地址
     * @return {*[]} 对应省市区信息
     */
    handleDistinguish (textAdd) {
      let textObj = AddressParse.parse(textAdd)
      let {province, city, area} = textObj[0]
      if (!(province && city && area)) return
      if (!province.includes('省') && !province.includes('区')) province += '市'
      const provinceData = this.regionList.find(v => v.label === province) || {}
      const cityData = Array.isArray(provinceData.children) ? provinceData.children.find(v => v.label === city) : {}
      const areaData = Array.isArray(cityData.children) ? cityData.children.find(v => v.label === area) : {}
      return [provinceData, cityData, areaData]
    },
    // 获取地区
    async getAllRegionData () {
      let result = await this.$ajax({
        url: '/tRegion/list_region',
        loadingDom: '.send-email-dialog'
      })
      if (result.code === this.SUCCESS_CODE) {
        this.regionList = []
        let map = new Map()
        result.data.forEach(v => {
          if (!map.has(v.parentId)) {
            map.set(v.parentId, [{...v, children: []}])
          } else {
            map.get(v.parentId).push({...v, children: []})
          }
        })
        this.getRegionList(this.regionList, map)
      } else {
        this.$message.error(result.message)
      }
    },
    /**
     * 整合地区信息
     * @param list 地区列表
     * @param map 对应地区map
     */
    getRegionList (list, map) {
      if (list.length === 0) {
        let childrenList = map.get('0')
        childrenList.forEach(v => list.push({...v, label: v.province}))
        this.getRegionList(list, map)
      } else {
        // 循环遍历list获取下一级数据
        list.forEach(v => {
          if (map.has(v.regionId)) {
            let childrenList = map.get(v.regionId)
            childrenList.forEach(vv => v.children.push({...vv, label: vv.city ? vv.city : vv.region}))
            this.getRegionList(v.children, map)
          } else {
            if (v.hasOwnProperty('children')) {
              delete v.children
            }
          }
        })
      }
    },
    setSubmitData () {
      // 当选择的数据包含 一小时内 ，将一小时内转换成时间节点
      if (this.form.expectedTime && this.form.expectedTime[1] === '一小时内') {
        // 选项中还有元素时，获取后一个时间节点
        if (this.expectTimeList[0].children[1]) {
          this.form.expectedTime[1] = this.expectTimeList[0].children[1].value
        } else {
          // 获取最后一个时间节点
          this.form.expectedTime[1] = '20:00:00'
        }
      }
      return {
        fsampleCode: this.orderCode,
        freceiver: this.form.recipient,
        freceiverPhone: this.form.recipientPhone,
        freceiverRegionCode: this.form.recipientAddressCode.length > 0 ? this.form.recipientAddressCode.join('/') : '',
        freceiverAddress: this.form.recipientAddressDetail,
        fpaymentType: this.form.payType,
        fpaymentAccount: this.form.accountType,
        fsender: this.form.sender,
        fsenderPhone: this.form.senderPhone,
        fsenderRegionCode: this.form.sendAddressCode.length > 0 ? this.form.sendAddressCode.join('/') : '',
        fsenderAddress: this.form.senderAddressDetail,
        fcompany: this.form.courierCompany,
        fexpressType: this.form.expressType,
        fexpectTimeToVisit: this.form.expectedTime.join(' '),
        fisValuation: this.form.insured,
        fdeclaredValue: this.form.declaredValue,
        fexpressMessage: this.form.notes // 备注
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitLoading = true
          let data = this.setSubmitData()
          this.$ajax({
            url: '/sample/t7_return/send_express',
            data: data,
            loadingDom: '.send-email-dialog',
            loadingObject: {
              text: '下单中，请稍后',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            }
          }).then(async res => {
            if (res && res.code === this.SUCCESS_CODE) {
              await this.$confirm(`快递下单成功，快递单号为${res.data}, 已回填。`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                showCancelButton: false,
                type: 'warning'
              })
              this.$emit('dialogConfirmEvent', res.data)
              this.visible = false
            } else {
              await this.$confirm(`快递下单失败，失败原因: ${res.message}<br/> 请重试或联系管理员`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                showCancelButton: false,
                dangerouslyUseHTMLString: true,
                type: 'warning'
              })
            }
          }).finally(() => {
            this.submitLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.dialog {
  height: 45vh;
  overflow: auto;
}
.form-width{
  width: 280px;
}
</style>
