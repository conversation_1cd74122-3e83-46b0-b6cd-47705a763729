<template>
  <div>
    <div class="btn">
      <el-button size="mini" type="primary" @click="handleContrast(0)">数据库对比</el-button>
      <el-button size="mini" type="primary" @click="handleGetDorH(0)">自动获取数据</el-button>
      <el-button :loading="loading" size="mini" type="primary" @click="handleDownload">下载解读数据</el-button>
      <el-button size="mini" type="primary" @click="handleImportData">导入解读数据</el-button>
    </div>
    <el-card>
      <div class="content">
        <div class="menu">
          <el-tabs v-model="activeName"
                   tab-position="left"
                   style="height: calc(100vh - 40px - 110px - 32px - 20px)">
            <el-tab-pane name="snv" label="SNV"></el-tab-pane>
            <el-tab-pane name="cnv" label="CNV"></el-tab-pane>
            <el-tab-pane name="qc" label="QC结果"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="wrapper">
          <component ref="components" :is="activeName"></component>
        </div>
      </div>
    </el-card>
    <import-dialog
      :pvisible.sync="importDialogVisible"
      :pdata="importDialogType"
      @importDialogConfirmEvent="handleDialogConfirm"
    ></import-dialog>
  </div>
</template>

<script>
import snv from './snv'
import cnv from './cnv'
import qc from './qc'
import util from '../../../../util/util'
import importDialog from './importDialog'

export default {
  components: {
    snv,
    cnv,
    qc,
    importDialog
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      activeName: 'snv',
      importDialogVisible: false,
      importDialogType: '',
      loading: false
    }
  },
  methods: {
    // 数据库对比
    handleContrast (currentStep = 0) {
      this.$ajax({
        url: '/read/bigAi/database_comparison',
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        data: {
          analysisRsId: this.analysisRsId,
          currentStep: currentStep
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('比对成功')
          this.getData()
        } else {
          // this.$message.error(result.message)
          this.handleContinue(result.message, 0)
        }
      })
    },
    // 自动获取数据
    handleGetDorH (currentStep = 0) {
      this.$ajax({
        url: '/read/bigAi/auto_get_analysis_data',
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId,
          currentStep: currentStep
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          if (result.message) {
            this.$message({
              showClose: true,
              duration: 0,
              type: 'error',
              message: result.message
            })
          } else {
            this.$message.success('获取数据成功')
          }
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 下载解读数据
    handleDownload () {
      this.loading = true
      this.$ajax({
        url: '/read/unscramble/down_excel',
        data: {
          analysisRsId: this.analysisRsId,
          type: '3'
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.loading = false
      })
    },
    async handleContinue (message, type) {
      let currentStep = 0
      if (message.indexOf('可能存在连锁突变') !== -1) {
        currentStep = 1
      } else if (message.indexOf('可能提示耐药') !== -1) {
        currentStep = 2
      } else if (message.indexOf('可能存在药物提示') !== -1) {
        currentStep = 3
      }
      await this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      type === 0 ? this.handleContrast(currentStep) : this.handleGetDorH(currentStep)
    },
    // 导入解读数据
    handleImportData () {
      this.importDialogType = 2
      this.importDialogVisible = true
    },
    // 导入数据回显
    handleDialogConfirm () {
      this.$refs.components.getData()
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-card__body {
  padding: 0;
}
.btn {
  margin: 10px;
}
.content {
  display: flex;
  height: calc(100vh - 40px - 110px - 32px - 20px);
  .menu {
    width: 90px;
  }
  .wrapper {
    width: calc(100% - 50px);
    height: 100%;
  }
}
</style>
