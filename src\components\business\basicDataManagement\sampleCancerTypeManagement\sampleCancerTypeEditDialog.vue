<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" :close-on-click-modal="false" @close="handleClose"
    @opened="handleOpen">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="mini">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样本癌种类型" prop="cancerTypeName">
            <el-input v-model.trim="form.cancerTypeName" placeholder="请输入样本癌种类型" maxlength="10"
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="癌种简称" prop="cancerShortName">
            <el-input v-model.trim="form.cancerShortName" placeholder="请输入癌种简称" maxlength="10"
              show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="癌种配置类型" prop="configType">
            <el-select v-model.trim="form.configType" placeholder="请选择癌种配置类型" style="width: 100%"
              @change="handleConfigTypeChange">
              <el-option label="通用癌种" :value="0"></el-option>
              <el-option label="专用癌种" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联产品名称" prop="productNames">
            <el-input v-model.trim="form.productNames" :placeholder="`${form.configType === 0 ? '关联所有产品' : '请选择关联产品'}`"
              readonly>
              <el-button slot="append" v-if="form.configType === 1" @click="handleProductSelect" size="mini"
                type="primary">
                选择
              </el-button>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-switch v-model.trim="form.status" :active-value="0" :inactive-value="1"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button type="primary" size="mini" :loading="loading" @click="handleConfirm">保存</el-button>
    </div>

    <!-- 产品选择弹窗 -->
    <product-select-dialog :pvisible.sync="productSelectVisible" :checked-product-info="checkedProductInfo"
      @dialogConfirmEvent="handleProductConfirm" />
  </el-dialog>
</template>

<script>
import ProductSelectDialog from './productSelectDialog'
import { updateSampleCancerType } from '@/api/basicDataManagement/sampleCancerTypeApi'
import { awaitWrap } from '@/util/util'
import mixins from '@/util/mixins'

export default {
  name: 'SampleCancerTypeEditDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    ProductSelectDialog
  },
  props: {
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      form: {
        cancerTypeName: '',
        cancerShortName: '',
        configType: '',
        productNames: '',
        productCodes: '',
        status: 0
      },
      title: '',
      checkedProductInfo: [],
      cateaory: [],
      rules: {
        cancerTypeName: [
          { required: true, message: '请输入样本癌种类型', trigger: 'blur' },
          { max: 10, message: '最多输入10个字符', trigger: 'blur' }
        ],
        cancerShortName: [
          { required: true, message: '请输入癌种简称', trigger: 'blur' },
          // 只能输入字母
          { pattern: /^[a-zA-Z]+$/, message: '只能输入字母', trigger: 'blur' },
          { max: 10, message: '最多输入10个字符', trigger: 'blur' }
        ],
        configType: [
          { required: true, message: '请选择癌种配置类型', trigger: 'change' }
        ],
        productNames: [
          {
            validator: (_, value, callback) => {
              if (this.form.configType === 1 && (!value || value.trim() === '')) {
                callback(new Error('请选择关联产品'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      loading: false,
      productSelectVisible: false,
      selectedProducts: [] // 已选择的产品列表
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        if (this.rowData) {
          this.title = '编辑样本癌种类型'
          this.initFormData()
        } else {
          this.title = '新增样本癌种类型'
          this.form = {
            cancerTypeName: '',
            cancerShortName: '',
            configType: '',
            productNames: '',
            productCodes: '',
            status: 0
          }
        }
      })
    },
    initFormData () {
      // 直接从传入的行数据初始化表单
      const data = this.rowData
      this.form = {
        cancerTypeName: data.cancerTypeName,
        cancerShortName: data.cancerShortName,
        configType: data.configType,
        productNames: data.productName,
        productCodes: data.frelateProductCode,
        status: data.status
      }
    },
    handleConfigTypeChange (value) {
      if (value === 0) {
        this.form.productNames = ''
        this.form.productCodes = ''
      } else {
        this.form.productNames = ''
        this.form.productCodes = ''
      }
      // 清除产品名称字段的验证错误
      this.$nextTick(() => {
        this.$refs.form.clearValidate('productNames')
      })
    },
    handleProductSelect () {
      this.productSelectVisible = true
      const productCodeList = this.form.productCodes.split(',').filter(v => v)
      const productNameList = this.form.productNames.split(',').filter(v => v)
      console.log('productCodeList', productCodeList)
      this.checkedProductInfo = productCodeList.map((v, index) => {
        return {
          productName: productNameList[index],
          productCode: v
        }
      })
    },
    handleProductConfirm (products) {
      this.form.productNames = products.map(item => item.productName).join(',')
      this.form.productCodes = products.map(item => item.productCode).join(',')
    },
    async handleConfirm () {
      try {
        await this.$refs.form.validate()
        this.loading = true

        const params = {
          fid: this.rowData ? this.rowData.id : '',
          fsampleCancerType: this.form.cancerTypeName,
          fcancerSortName: this.form.cancerShortName,
          fcancerConfigType: this.form.configType,
          frelateProductName: this.form.productNames,
          frelateProductCode: this.form.productCodes,
          fstate: this.form.status
        }

        const { res } = await awaitWrap(updateSampleCancerType(params))
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('dialogConfirmEvent')
          this.handleClose()
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
