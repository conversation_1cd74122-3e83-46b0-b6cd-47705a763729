<template>
  <div>
    <div style="margin:10px;">
      <el-button type="primary" size="mini" @click="handleFixDrugInfo">修改</el-button>
    </div>
    <div class="card-wrapper">
      <el-table
        :data="tableData"
        ref="table"
        class="table"
        size="mini"
        border
        height="calc(100vh - 40px - 154px - 32px - 20px - 20px - 20px)"
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column show-overflow-tooltip label="基因名称" prop="gene" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="肿瘤类型" prop="accessType" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="核苷酸改变" prop="nucleotideMutation" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="氨基酸改变" prop="aminoAcidMutation" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="药物名称" prop="drugName" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="疗效相关性" prop="curativePertinence" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="药物状态" prop="fda" min-width="120px"></el-table-column>
      </el-table>
    </div>
    <fix-drug-info-dialog
      :pdata="pdata"
      :pvisible.sync="fixDrugInfoVisible"
      @dialogConfirmEvent="getData"></fix-drug-info-dialog>
  </div>
</template>

<script>

import FixDrugInfoDialog from './fixDrugInfoDialog'
export default {
  components: {FixDrugInfoDialog},
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: [],
      fixDrugInfoVisible: false,
      selectedRows: new Map(),
      pdata: {}
    }
  },
  methods: {
    async getData () {
      this.selectedRows = new Map()
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_report_h_drug_list',
        loadingDom: '.table',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.tableData = []
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            // accessType: v.access_type,
            aminoAcidMutation: v.amino_acid_mutation,
            accessType: v.cancer_class,
            curativePertinence: v.curative_pertinence,
            databaseId: v.databaseId,
            drugCurativeEffect: v.drug_curative_effect,
            drugName: v.drug_name,
            fda: v.fda,
            gene: v.gene,
            nucleotideMutation: v.nucleotide_mutation,
            nucleotideMutationNumber: v.curative_pertinence_number,
            oncohComparisonId: v.oncohComparisonId
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    },
    // 修改药物信息
    handleFixDrugInfo () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      this.pdata = [...this.selectedRows.values()][0]
      this.fixDrugInfoVisible = true
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
