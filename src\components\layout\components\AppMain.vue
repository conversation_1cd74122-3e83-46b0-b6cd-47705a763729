<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <div class="page-wrap">
        <keep-alive :include="cachedViews">
          <router-view :key="key" />
        </keep-alive>
      </div>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews () {
      return this.$store.state.tagsView.cachedViews
    },
    key () {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  //min-height: calc(100vh - 84px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background: $page_bg;
  padding: 8px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
