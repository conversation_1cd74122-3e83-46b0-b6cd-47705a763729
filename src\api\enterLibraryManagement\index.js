import { myAjax } from '../../util/ajax'

/**
 * 报存单个样本在出入库记录中的位置信息
 * @param data http://172.16.50.15:3000/repository/editor?id=35&mod=205209&itf=5799275
 * @param options
 * @returns {*}
 */
export function saveSamplePos (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/sample/order/save_sample_place_info',
    data: data,
    ...options
  })
}

/**
 * 报存单个样本在出入库记录中的位置信息
 * @param data http://172.16.50.15:3000/repository/editor?id=35&mod=205209&itf=5799275
 * @param options
 * @returns {*}
 */
export function delSamplePos (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/sample/order/delete_sample_place_info',
    data: data,
    ...options
  })
}
