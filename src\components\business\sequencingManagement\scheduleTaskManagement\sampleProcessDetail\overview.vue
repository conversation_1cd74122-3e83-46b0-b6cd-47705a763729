<template>
  <div class="page">
    <div class="page-title">进度</div>
    <!--    基础信息-->
    <div class="basic-info">
      <div class="module-title">基础信息</div>
      <div class="module-content">
        <el-table
          ref="table"
          :data="basicInfo"
          class="table"
          size="mini"
          border
          style="width: 100%"
          :height="200">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="任务单编号" prop="taskCode" min-width="120" show-overflow-tooltip/>
          <el-table-column label="项目名称" prop="projectName" min-width="120" show-overflow-tooltip/>
          <el-table-column label="实验样本" prop="sampleName" min-width="120" show-overflow-tooltip/>
          <el-table-column label="核酸/吉因加编号" prop="geneCode" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.geneCode !== '查看样本详情'">{{scope.row.geneCode}}</span>
              <div v-else class="link" @click="handleDetail(scope.row)">{{scope.row.geneCode}}</div>
            </template>
          </el-table-column>
          <el-table-column label="原始样本名称" prop="oldSampleName" min-width="120" show-overflow-tooltip/>
          <el-table-column label="工序流程" prop="processFlow" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="客户下单数据量/G" prop="orderDataVolumeSize" min-width="120" show-overflow-tooltip/>
          <el-table-column label="排单数据量/G" prop="dataVolumeSize" min-width="120" show-overflow-tooltip/>
          <el-table-column label="文库修饰类型" prop="libModificationType" min-width="120" show-overflow-tooltip/>
          <el-table-column label="文库类型" prop="libType" min-width="120" show-overflow-tooltip/>
          <el-table-column label="makeDNB" prop="makeDNB" min-width="120" show-overflow-tooltip/>
        </el-table>
      </div>
    </div>
    <!--    工序进度-->
    <div class="process-progress">
      <div class="module-title">详情</div>
      <div class="module-content flex">
        <template v-for="(item, index) in modules">
          <div :key="index">
            <el-button
              :type="item && status[item.stepStatus]"
              @click="handleChangeModule(item.workflowId, item.index)">
              {{item && item.stepName}}
            </el-button>
          </div>
          <div v-if="index !== modules.length - 1" :key="index + 'line'" class="line"></div>
        </template>
      </div>
    </div>

    <!--    样本结果-->
    <div class="sample-result">
      <div class="module-title">样本结果</div>
      <div class="module-content">
        <el-table
          ref="table"
          :data="sampleResult"
          class="table"
          size="mini"
          border
          style="width: 100%"
          :height="200">
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="实验样本" prop="sampleName" min-width="120" show-overflow-tooltip/>
          <el-table-column label="排单数据量/G" prop="size" min-width="120" show-overflow-tooltip/>
          <el-table-column label="浓度" prop="concentration" min-width="120" show-overflow-tooltip/>
          <el-table-column label="体积" prop="volume" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="产物名称" prop="resultName" min-width="120" show-overflow-tooltip/>
          <el-table-column label="产物浓度" prop="resultConcentration" min-width="120" show-overflow-tooltip/>
          <el-table-column label="产物体积" prop="resultVolume" min-width="120" show-overflow-tooltip/>
          <el-table-column label="产物文库类型" prop="libModificationType" min-width="120" show-overflow-tooltip/>
          <el-table-column label="检测人" prop="testPerson" min-width="120" show-overflow-tooltip/>
          <el-table-column label="复核人" prop="reviewPerson" min-width="120" show-overflow-tooltip/>
          <el-table-column label="提交时间" prop="taskSubmitTime" min-width="120" show-overflow-tooltip/>
          <el-table-column label="提交批次号" prop="taskSubmitNumber" min-width="120" show-overflow-tooltip/>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import util, {awaitWrap} from '../../../../../util/util'
import {getTaskDetail} from '../../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'overview',
  mounted () {
    this.getData()
  },
  computed: {
    basicInfo () {
      return [this.$store.getters.sampleInfo]
    }
  },
  data () {
    return {
      sampleResult: [],
      resultInfo: {},
      // 蓝色-进行中，黄色-登记了异常且未被处理，绿色-已完成，灰色-未做
      // 状态 -1未开始 0进行中 1已完成 2终止 3.异常
      status: {
        '-1': 'info',
        0: 'primary',
        1: 'primary',
        2: 'info',
        3: 'primary'
      },
      modules: [
      ]
    }
  },
  methods: {
    async getData () {
      const {res} = await awaitWrap(getTaskDetail({
        fsampleId: this.$route.query.sampleId
      }, {loadingDom: '.page'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        const sampleWorkflow = data.sampleWorkflowBeanList || []
        let index = 1
        sampleWorkflow.forEach(v => {
          const processFlow = {
            1: 'pooling',
            2: '转化',
            3: 'pooling',
            4: '环化',
            5: 'makeDNB'
          }
          if (v.fworkflowId === 3) {
            // 第二次pooling
            index = 2
          }
          const item = {
            workflowId: v.fworkflowId,
            stepName: processFlow[v.fworkflowId],
            stepStatus: v.fstatus,
            index: index
          }
          this.modules.push(item)
        })
        this.resultInfo[1] = data.poolingList.map(v => {
          const item = {
            id: v.fid,
            sampleName: v.fsampleName, // 样本名称
            size: v.fsize, // 数据量
            concentration: v.fconcentration, // 浓度
            volume: v.fvolume, // 混合体积
            resultName: v.fresultName, // pooling文库名称
            resultConcentration: v.fpoolingConcentration,
            libModificationType: v.fresultLibModificationType, // 修饰类型
            resultVolume: v.ftotalMixingVolume, // 混合总体积（ul）
            testPerson: v.ftestPerson,
            index: v.findex,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            taskSubmitNumber: v.fresultSubmitCode
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          return item
        }).filter(v => v.index === 1)
        this.resultInfo[2] = data.transformList.map(v => {
          const item = {
            id: v.fid,
            sampleName: v.fsampleName, // 样本名称
            size: v.fsize, // 数据量
            concentration: v.fconcentration, // 浓度
            volume: v.fvolume, // 混合体积
            resultName: v.fresultName, // pooling文库名称
            resultVolume: v.ftransformVolume, // 混合总体积（ul）
            libraryType: v.flibType, // 建库方法
            testPerson: v.ftestPerson,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            resultConcentration: v.ftransformConcentration,
            libModificationType: v.fresultLibModificationType, // 修饰类型
            taskSubmitNumber: v.fresultSubmitCode
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          return item
        })
        this.resultInfo[3] = data.poolingList.map(v => {
          const item = {
            id: v.fid,
            sampleName: v.fsampleName, // 样本名称
            size: v.fsize, // 数据量
            concentration: v.fconcentration, // 浓度
            volume: v.fvolume, // 混合体积
            resultName: v.fresultName, // pooling文库名称
            resultConcentration: v.fpoolingConcentration,
            libModificationType: v.fresultLibModificationType, // 修饰类型
            resultVolume: v.ftotalMixingVolume, // 混合总体积（ul）
            testPerson: v.ftestPerson,
            index: v.findex,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            taskSubmitNumber: v.fresultSubmitCode
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          return item
        }).filter(v => v.index === 2)
        this.resultInfo[4] = data.cyclizeLIst.map(v => {
          const item = {
            id: v.fid,
            sampleName: v.fsampleName, // 样本名称
            size: v.fsize, // 数据量
            concentration: v.fconcentration, // 浓度
            volume: v.fvolume, // 混合体积
            resultName: v.fresultName, // pooling文库名称
            resultConcentration: v.fcyclizeConcentration,
            libModificationType: v.fresultLibModificationType, // 修饰类型
            resultVolume: v.fcyclizeVolume, // 混合总体积（ul）
            libraryType: v.flibType, // 建库方法
            testPerson: v.ftestPerson,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            taskSubmitNumber: v.fresultSubmitCode
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          return item
        })
        this.resultInfo[5] = data.makeDnbList.map(v => {
          const item = {
            id: v.fid,
            sampleName: v.fsampleName, // 样本名称
            size: v.fsize, // 数据量
            concentration: v.fconcentration, // 浓度
            libModificationType: v.fresultLibModificationType, // 修饰类型
            volume: v.fvolume, // 混合体积
            resultName: v.fdnbName, // pooling文库名称
            resultConcentration: v.fdnbCalculatedConcentration,
            resultVolume: v.fdnbPoolingVolume, // 混合总体积（ul）
            libraryType: v.flibType, // 建库方法
            testPerson: v.ftestPerson,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            taskSubmitNumber: v.fresultSubmitCode
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          return item
        })
        let result = this.resultInfo[this.modules[0].workflowId]
        if (this.modules[0].workflowId === 1) {
          result = result.filter(v => v.index === 1)
        }
        this.sampleResult = result
      }
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    // 切换工序模块
    handleChangeModule (workId, index) {
      let result = this.resultInfo[workId] || []
      if (workId === 1) {
        result = result.filter(v => v.index === index)
      }
      this.sampleResult = result
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  height: calc(100vh - 40px);
  overflow: auto;
  padding: 10px;
  .page-title {
    font-size: 20px;
    font-weight: bold;
  }
  .module-content {
    margin-bottom: 15px;
  }
  .process-progress {
    margin: 40px 0;
  }
}

.line {
  height: 1px;
  background-color: #ccc;
  width: 50px;
  margin-top: 20px;
}
</style>
