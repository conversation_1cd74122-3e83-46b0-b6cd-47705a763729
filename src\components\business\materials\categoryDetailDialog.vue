<template>
  <div>
    <el-dialog
      :visible.sync="visible" :close-on-click-modal="false" :before-close="handleClose"
      title="类别管理" width="30%" @open="handleOpen">
      <div class="categoryDetail">
        <div class="buttonGroup">
          <el-button type="primary" size="mini" @click="handleAdd">添加类别</el-button>
        </div>
        <el-table
          ref="table"
          :data="tableData"
          size="mini"
          class="categoryDetailTable"
          height="300px"
          style="width: 100%">
          <el-table-column prop="name" label="物料类别" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="desc" label="描述" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" size="mini" @click="handleEdit(scope.row.realData)"></el-button>
              <el-button type="text" icon="el-icon-delete" size="mini" @click="handleDelete(scope.row.categoryId)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
    <category-save-dialog
      :pvisible.sync="categorySaveDialogVisible" :pcategory-id="categorySaveDialogData.categoryId"
      :pname="categorySaveDialogData.name" :pdesc="categorySaveDialogData.desc" :ptype="categorySaveDialogData.type"
      @categorySaveDialogConfirmEvent="handleCategorySaveDialogConfirm"
    ></category-save-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import categorySaveDialog from './categorySaveDialog'
export default {
  name: 'categoryDetailDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {
    categorySaveDialog
  },
  props: {
    ptype: {
      type: Number,
      default: 0,
      required: true
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      type: 0,
      selectedRow: new Map(),
      categorySaveDialogVisible: false,
      categorySaveDialogData: {}
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.type = this.ptype
        this.getData()
      })
    },
    getData () {
      this.$ajax({
        method: 'get',
        loadingDom: '.categoryDetailTable',
        url: '/materials/get_category_list',
        data: {
          type: this.type
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.tableData = []
          this.selectedRow.clear()
          let item = {}
          result.data.forEach(v => {
            item = {
              categoryId: v.categoryId,
              name: v.name,
              desc: v.desc
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row) {
      this.$refs.table.clearSelection()
      this.$refs.table.toggleRowSelection(row, !this.selectedRow.has(row.fid))
      let hasThisId = this.selectedRow.has(row.fid)
      this.selectedRow.clear()
      if (!hasThisId) {
        this.selectedRow.set(row.fid, row)
      }
    },
    handleAdd () {
      this.categorySaveDialogData = {
        categoryId: null,
        name: '',
        desc: '',
        type: this.type
      }
      this.categorySaveDialogVisible = true
    },
    handleEdit (row) {
      this.categorySaveDialogData = {
        categoryId: row.categoryId,
        name: row.name,
        desc: row.desc,
        type: this.type
      }
      this.categorySaveDialogVisible = true
    },
    handleDelete (categoryId) {
      this.$confirm(`是否移除该类别信息?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/materials/delete_category',
          method: 'get',
          data: {
            fid: categoryId
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getData()
          } else {
            this.$message.error(result.message)
          }
        })
      }).catch(err => {
        console.log(err)
      })
    },
    handleCategorySaveDialogConfirm () {
      this.categorySaveDialogVisible = false
      this.getData()
    }
  }
}
</script>

<style scoped>
  >>>.el-dialog__body{
    padding: 10px 20px;
  }
  .categoryDetail >>>.el-table__header .el-checkbox {
    display: none;
  }
  .buttonGroup{
    text-align: right;
    height: 40px;
    line-height: 40px;
  }
</style>
