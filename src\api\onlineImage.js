import ajax from '@/util/ajax'

const baseURL = 'http://172.16.31.109:5000' || ''

export default {
  // GET /api/images - 获取图片列表
  getImages (params = {}) {
    return ajax.myAjax({
      url: '/api/images',
      baseURL,
      method: 'get',
      showErrorMessageBox: false,
      data: params
    })
  },

  // GET /api/image/:imageId/info - 获取图片详细信息和瓦片配置
  getImageInfo (imageId) {
    return ajax.myAjax({
      url: `/api/image/${encodeURIComponent(imageId)}/info`,
      baseURL,
      showErrorMessageBox: false,
      method: 'get'
    })
  },

  // GET /api/thumbnail/:imageId - 获取缩略图
  getThumbnailUrl (imageId) {
    return `${baseURL}/api/thumbnail/${encodeURIComponent(imageId)}`
  },

  // GET /api/image/:imageId/tile/:level/:x/:y - 获取瓦片
  getTileUrl (imageId, level, x, y) {
    console.log(`${baseURL}/api/image/${encodeURIComponent(imageId)}/tile/${level}/${x}/${y}`)
    return `${baseURL}/api/image/${encodeURIComponent(imageId)}/tile/${level}/${x}/${y}`
  },

  // GET /api/markers/:imageId - 获取标记
  getMarkers (imageId) {
    return ajax.myAjax({
      url: `/api/markers/${encodeURIComponent(imageId)}`,
      showErrorMessageBox: false,
      baseURL,
      method: 'get'
    })
  },

  // POST /api/markers/:imageId - 保存标记
  saveMarkers (imageId, markers = []) {
    return ajax.myAjax({
      url: `/api/markers/${encodeURIComponent(imageId)}`,
      baseURL,
      method: 'post',
      showErrorMessageBox: false,

      data: { markers }
    })
  },

  // DELETE /api/markers/:imageId - 清空标记
  clearMarkers (imageId) {
    return ajax.myAjax({
      url: `/api/markers/${encodeURIComponent(imageId)}`,
      baseURL,
      method: 'delete',
      showErrorMessageBox: false,

      data: {}
    })
  },

  // POST /api/upload - 上传新图片
  uploadImage (file, extra = {}) {
    const data = { file, ...extra }
    return ajax.myAjax({
      url: '/api/upload',
      baseURL,
      method: 'post',
      showErrorMessageBox: false,

      isFormData: true,
      data
    })
  }
}
