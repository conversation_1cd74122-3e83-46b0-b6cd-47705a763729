<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPane" class="tags-view-wrapper">
      <router-link
          v-for="tag in visitedViews"
          ref="tag"
          :key="tag.path"
          :class="isActive(tag)?'active':''"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
          tag="span"
          class="tags-view-item"
          :style="activeStyle(tag)"
      >
        {{ tag.title }}
        <span v-if="visitedViews.length > 1" class="el-icon-close" @click.prevent.stop="delVisitedViews(tag.path)" />
      </router-link>
    </scroll-pane>
  </div>
</template>

<script>
export default {
  name: 'pathTabs',
  watch: {
    $route: {
      handler: function (newVal) {
        this.addVisitedViews(newVal)
      },
      immediate: true
    }
  },
  computed: {
    visitedViews () {
      return this.$store.getters.pathTabs
    },
    activeIndex () {
      return this.$store.getters.activeIndex
    },
    theme () {
      return this.$store.state.settings.theme
    }
  },
  data () {
    return {
      indexPath: '/main/index'
    }
  },
  methods: {
    addVisitedViews (route) {
      if (route.meta.title) {
        const page = {
          title: route.meta.title,
          path: route.path
        }
        if (route.name) page.name = route.name
        this.$store.dispatch('addTabs', page)
      }
    },
    isActive (route) {
      return route.path === this.$route.path
    },
    activeStyle (tag) {
      if (!this.isActive(tag)) return {}
      return {
        'background-color': this.theme,
        'border-color': this.theme
      }
    },
    isAffix (tag) {
      return tag.meta && tag.meta.affix
    },
    toPage (path) {
      this.$router.push({ path: path })
    },
    delVisitedViews (path) {
      this.$store.dispatch('delPath', path).then(({ activeIndex }) => {
        this.toPage(activeIndex)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .tags-view-container {
    height: 34px;
    width: 100%;
    background: #f8f8f8;
    //border-bottom: 1px solid #d8dce5;
    //box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
    .tags-view-wrapper {
      .tags-view-item {
        display: inline-block;
        position: relative;
        cursor: pointer;
        height: 26px;
        line-height: 26px;
        border: 1px solid #d8dce5;
        color: #495060;
        background: #fff;
        padding: 0 8px;
        font-size: 12px;
        margin-left: 5px;
        margin-top: 4px;
        &:first-of-type {
          margin-left: 15px;
        }
        &:last-of-type {
          margin-right: 15px;
        }
        &.active {
          background-color: #42b983;
          color: #fff;
          border-color: #42b983;
          &::before {
            content: '';
            background: #fff;
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: relative;
            margin-right: 2px;
          }
        }
      }
    }
    .contextmenu {
      margin: 0;
      background: #fff;
      z-index: 3000;
      position: absolute;
      list-style-type: none;
      padding: 5px 0;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #333;
      box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
      li {
        margin: 0;
        padding: 7px 16px;
        cursor: pointer;
        &:hover {
          background: #eee;
        }
      }
    }
  }
</style>

<style lang="scss">
  //reset element css of el-icon-close
  .tags-view-wrapper {
    .tags-view-item {
      .el-icon-close {
        width: 16px;
        height: 16px;
        vertical-align: 2px;
        border-radius: 50%;
        text-align: center;
        transition: all .3s cubic-bezier(.645, .045, .355, 1);
        transform-origin: 100% 50%;
        &:before {
          transform: scale(.6);
          display: inline-block;
          vertical-align: -3px;
        }
        &:hover {
          background-color: #b4bccc;
          color: #fff;
        }
      }
    }
  }
</style>
