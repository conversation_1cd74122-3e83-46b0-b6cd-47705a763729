<template>
  <el-dialog
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :width="dialogWidth"
    @open="handleOpen">
    <div slot="title">
      <div v-if="!isUpload">
        导入任务
        <div class="tips">通过导入excel表格，完成对应样本的实验任务下达</div>
      </div>
      <div v-if="isUpload">
        配置工序
      </div>
    </div>

    <div class="dialog-content">
      <el-form ref="form" v-if="!isUpload" :model="form" label-suffix=":" :rules="rules" label-width="120px">
        <div class="tips">请配置任务：</div>
        <div class="flex-wrapper">
          <el-form-item label="测序平台" prop="sequencingPlatform">
            <el-select
              v-model.trim="form.sequencingPlatform"
              placeholder="请选择"
              clearable
              filterable
              size="mini"
              style="width: 100%;">
              <el-option
                v-for="item in platformOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="测序类型" prop="sequencingType">
            <el-select
              v-model.trim="form.sequencingType"
              placeholder="请选择"
              clearable
              filterable
              size="mini"
              style="width: 100%;">
              <el-option
                v-for="item in typeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value">
                </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="makeDNB" prop="makeDNB">
            <el-select
              v-model.trim="form.makeDNB"
              placeholder="请选择"
              clearable
              filterable
              size="mini"
              style="width: 100%;">
              <el-option
                v-for="item in makeDNBMethods"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属片区" prop="area">
            <el-select v-model.trim="form.area" size="mini" clearable placeholder="请选择" @change="handleScheduleAreaChange">
              <el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="任务文件上传" prop="fileList">
            <el-button type="text" size="mini" @click="handleDownload">文件模板下载</el-button>
            <el-upload
              ref="upload"
              :auto-upload="false"
              :file-list="fileList"
              :action="uploadUrl"
              :data="{
                fplatforms: form.sequencingPlatform,
                fseqType: form.sequencingType,
                fmakeDnb: form.makeDNB,
                fproductionArea: options[form.area]
              }"
              :before-upload="handleBeforeUpload"
              :on-change="handleFileChange"
              :on-success="handleOnSuccess"
              :on-error="handleError"
              style="text-align: center;"
              drag
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
                <br/>
                仅支持 xls、xlsx，只能上传1份小于10M的文件；
              </div>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
      <div v-if="isUpload" class="result">
        <el-form
          ref="form"
          :model="form"
          :inline="true"
          size="mini"
          @keyup.enter.native="handleSearch">
          <el-form-item label="实验样本 ">
            <el-input v-model.trim="searchForm.sampleName" class="form-width" clearable placeholder="请输入实验样本"></el-input>
          </el-form-item>
          <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
          <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
        </el-form>
        <div style="margin-bottom: 10px">
          <el-button type="primary" size="mini" @click="handleSetMakeDNB(0)">转一步法</el-button>
          <el-button type="primary" size="mini" @click="handleSetMakeDNB(1)">转两步法</el-button>
        </div>
        <vxe-table
          ref="tableRef"
          border
          resizable
          size="mini"
          height="300px"
          :keep-source="false"
          :row-style="rowStyle"
          show-overflow="tooltip"
          :data="showTableData"
          :edit-rules="validRules"
          :checkbox-config="{trigger: 'row'}"
          :valid-config="{autoPos: true, showMessage: true, msgMode: 'full'}"
          :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
        >
          <vxe-column type="checkbox" width="60"></vxe-column>
          <vxe-column type="seq" title="序号" width="60"></vxe-column>
          <vxe-column field="name" show-overflow title="项目名称" width="100"></vxe-column>
          <vxe-column field="sampleName" show-overflow title="实验样本"  width="100"></vxe-column>
          <vxe-column field="libModificationType" width="180" show-overflow title="文库修饰类型"></vxe-column>
          <vxe-column field="libraryType" show-overflow title="文库类型"  width="100"></vxe-column>
          <vxe-column field="libConcentration" show-overflow title="文库浓度"  width="100"></vxe-column>
          <vxe-column field="customerAmount" show-overflow title="客户下单数据量/G" width="180"></vxe-column>
          <vxe-column field="scheduledAmount" show-overflow title="排单数据量/G" width="160" :edit-render="{name: '$input', props: {clearable: true}}">
          </vxe-column>
          <vxe-column field="makeDNB" show-overflow title="makeDNB" width="120">
          </vxe-column>
          <vxe-column field="processFlowText"  title="工序流程" min-width="200" show-overflow></vxe-column>
          <vxe-column title="操作" width="100">
            <template #default="{ row, rowIndex }">
              <el-button type="text" @click="handleEdit(rowIndex, row)">修改工序</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button v-if="!isUpload" :loading="loading"  type="primary" size="mini" @click="handleNext">下一步</el-button>
      <el-button v-if="isUpload" :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>

    <edit-process-dialog
      :pvisible.sync="editProcessDialogVisible"
      :index-list="indexList"
      :type="libModificationType"
      :lib-type="libType"
      :current-work-flow-id="currentWorkFlowId"
      :lib-concentration="libConcentration"
      :make-d-n-b="makeDNB"
      :length="fixData.length"
      :del-pooling="delPooling"
      @dialogConfirmEvent="handleEditProcess"
    />
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'
import EditProcessDialog from '../../components/editProcessDialog'
import {awaitWrap, downloadFile, getSessionInfo, readBlob} from '../../../../../util/util'
import {downloadTemplate, saveTaskList} from '../../../../../api/sequencingManagement/sequencingManagementApi'
import constants from '../../../../../util/constants'
import Cookies from 'js-cookie'

export default {
  name: 'uploadResultDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {EditProcessDialog},
  computed: {
    showTableData () {
      return this.tableData.filter(v => v.sampleName.includes(this.submitForm.sampleName))
    }
  },
  data () {
    return {
      isUpload: false,
      loading: false,
      libType: '',
      searchForm: {
        sampleName: ''
      },
      submitForm: {
        sampleName: ''
      },
      fileList: [],
      uploadUrl: constants.JS_CONTEXT + '/experiment/schedule/import_task',
      dialogWidth: '850px',
      process: [
        {index: 1, process: 'pooling', explain: '', isDelete: 0},
        {index: 2, process: '转化', explain: 'illumina-未磷酸化”的才会执行转化', isDelete: 0},
        {index: 3, process: 'pooling', explain: '', isDelete: 0},
        {index: 4, process: '环化', explain: '“未环化”、且设置为“两步法”才会执行环化', isDelete: 0},
        {index: 5, process: 'makeDNB', explain: '', isDelete: 0}
      ],
      fixData: [],
      indexList: [],
      index: 0,
      makeDNB: '',
      delPooling: false,
      libModificationType: '',
      currentWorkFlowId: null,
      processFlow: {
        'illumina-未磷酸化': [0, 1, 2, 3, 4],
        'illumina-已磷酸化': [2, 3, 4],
        'MGI-未环化-单': [0, 3, 4],
        'MGI-未环化-双': [0, 3, 4],
        'illumina-已环化': [0, 4],
        'MGI-已环化': [0, 4]
      },
      uploadParams: {},
      options: {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      },
      editProcessDialogVisible: false,
      libConcentration: null,
      form: {
        sequencingPlatform: '', // 测序平台
        sequencingType: '', // 测序类型
        makeDNB: '', // makeDNB
        area: '', // 片区
        fileList: []
      },
      rules: {
        sequencingPlatform: [
          {required: true, message: '请选择测序平台', trigger: 'change'}
        ],
        sequencingType: [
          {required: true, message: '请选择测序类型', trigger: 'change'}
        ],
        makeDNB: [
          {required: true, message: '请选择makeDNB', trigger: 'change'}
        ],
        area: [
          {required: true, message: '请选择片区', trigger: 'change'}
        ],
        fileList: [
          {required: true, message: '请选择文件', trigger: 'change'}
        ]
      },
      areaList: JSON.parse(Cookies.get('labOptions') || '').filter(v => (getSessionInfo('currentLab') || []).includes(v.value)).filter(v => v.label !== '苏州'),
      // DNBSEQ-T7RS、G2000-FCL、G2000-FCS
      platformOptions: [
        {label: 'DNBSEQ-T7RS', value: 'DNBSEQ-T7RS'},
        {label: 'G2000-FCL', value: 'G2000-FCL'},
        {label: 'G2000-FCS', value: 'G2000-FCS'}
      ],
      // PE150、PE100
      typeOptions: [
        {label: 'PE150', value: 'PE150'},
        {label: 'PE100', value: 'PE100'}
      ],
      makeDNBMethods: ['一步法', '两步法'],
      title: '导入任务',
      tableData: [],
      validRules: {
        scheduledAmount: [
          // 必填 ，类型数字
          { required: true, message: '排单数据量为空' },
          { type: 'number', message: '请输入数字' },
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change'},
          {max: 10, message: '排单数据量应不超过10字符'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.loading = false
      this.isUpload = false
      this.title = '导入任务'
      this.dialogWidth = '800px'
      this.tableData = []
      this.$nextTick(() => {
        this.form = {
          sequencingPlatform: 'DNBSEQ-T7RS', // 测序平台
          sequencingType: 'PE150', // 测序类型
          makeDNB: '两步法', // makeDNB
          area: '', // 片区
          fileList: []
        }
        this.$refs.form.resetFields()
        if (this.areaList.findIndex(v => v.value === +localStorage.getItem('scheduleArea')) !== -1) {
          this.form.area = +localStorage.getItem('scheduleArea')
        }
        this.$refs.upload.clearFiles()
      })
    },
    handleSearch () {
      this.submitForm = JSON.parse(JSON.stringify(this.searchForm))
    },
    handleReset () {
      this.searchForm = {
        sampleName: ''
      }
      this.submitForm = {
        sampleName: ''
      }
    },
    async handleDownload () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTemplate(this.type))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 排单地区变化
    handleScheduleAreaChange () {
      localStorage.setItem('scheduleArea', this.form.area)
    },
    // 设置makeDNB方法
    handleSetMakeDNB (methods) {
      const methodsList = ['一步法', '两步法']
      const notChangeTwoMethods = [
        'PCR-free文库',
        '10xATAC单细胞文库',
        '甲基化文库-WGBS',
        '甲基化文库-RRBS',
        '甲基化文库-其他',
        'C4 v2.0单细胞文库',
        'C4 ATAC单细胞文库'
      ]
      const notChangeOneMethods = ['illumina-已环化', 'MGI-已环化']
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length < 1) {
        this.$message.error('请选择数据')
        return
      }

      if (methods === 0) {
        const notChangeTwoMethodNames = selectRecords
          .filter(v => notChangeOneMethods.includes(v.libModificationType))
          .map(v => v.sampleName).join('/')
        if (notChangeTwoMethodNames) {
          this.$message.error(`${notChangeTwoMethodNames}（实验样本 ）为已环化文库，不能转一步法，请检查！`)
          return
        }
      }

      if (methods === 0) {
        const notChangeTwoMethodNames = selectRecords
          .filter(v => {
            return notChangeTwoMethods.some(lib => v.libraryType.includes(lib))
          })
          .map(v => v.sampleName).join('/')
        if (notChangeTwoMethodNames) {
          this.$message.error(`${notChangeTwoMethodNames}（实验样本 ）所属文库类型，不能转一步法，请检查！`)
          return
        }
      }
      // 获取无需转换的样本
      const skipSampleNames = selectRecords
        .filter(item => item.makeDNB === methodsList[methods])
      if (skipSampleNames.length > 0) {
        this.$message.warning(`样本${skipSampleNames.map(v => v.sampleName).join('/')}已为一步法/两步法，将自动跳过修改！`)
      }
      //  获取需要转换的样本
      const changeSamples = selectRecords
        .filter(item => item.makeDNB !== methodsList[methods])
        .map(item => {
          item.makeDNB = methodsList[methods]
          let processFlow = item.processFlow || []
          processFlow = processFlow.map(v => {
            if (v.index === 4) {
              v.isDelete = item.makeDNB !== '两步法'
            }
            return v
          })
          item.processFlow = processFlow
          item.processFlowText = processFlow.filter(v => !v.isDelete).map(v => v.process).join('-')
          item.processIndex = processFlow.filter(v => !v.isDelete).map(v => v.index).join(',')
          item.deleteIndex = processFlow.map((v, i) => {
            let value = ''
            if (v.isDelete) {
              value = i + 1
            }
            return value
          }).filter(v => v)
          return item
        })

      this.fixTableData(changeSamples)
    },
    fixTableData (changeSamples) {
      const data = this.tableData
      // 获取选中行索引
      const indexList = changeSamples
        .map(v => this.tableData.findIndex(data => v._X_ROW_KEY === data._X_ROW_KEY))
      indexList.forEach((v, index) => {
        data[v] = changeSamples[index]
      })
      this.$set(this, 'tableData', data)
      this.$nextTick(() => {
        this.$message.success('转化成功')
      })
    },
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.loading = false
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.loading = false
        return false
      }
    },
    // 文件改变时回调，判断文件是否替换
    async handleFileChange (file, fileList) {
      if (fileList.length < 2) return
      const { err } = await awaitWrap(this.$confirm('一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      err ? fileList.pop() : fileList.shift()
    },
    handleError () {
      this.loading = false
      this.$message.error('导入失败')
    },
    async handleOnSuccess (res) {
      this.loading = false
      if (res.code !== this.SUCCESS_CODE) {
        this.$message.error(res.message)
        return
      }
      this.$refs.upload.clearFiles()
      const data = res.data || {}
      const prompt = data.prompt || []
      if (prompt.length > 0) {
        this.$showSequencingErrorDialog({tableData: prompt})
      }
      const rows = data.rows || []
      if (rows.length < 1) {
        await this.$confirm('导入任务单中不存在有效样本，无法生成任务单，请知悉。', '提示', {
          cancelButtonText: '关闭',
          showConfirmButton: false,
          type: 'warning'
        })
        return
      }
      if (data.nonExtremeSampleTotal > 0) {
        const message = `导入样本中包含${data.nonExtremeSampleTotal}个非极测样本未被申请检测，确认继续导入吗？`
        await this.$confirm(message, '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })
      }
      this.isUpload = true
      this.title = '配置工序'
      this.dialogWidth = '80vw'
      this.tableData = []
      let isHeightLight = true
      rows.forEach((v, i) => {
        // 判断是否切换高亮 （“项目名称”、“文库修饰类型”）
        if (i !== 0 && (v.fprojectName !== rows[i - 1].fprojectName || v.flibModificationType !== rows[i - 1].flibModificationType)) {
          isHeightLight = !isHeightLight
        }
        const item = {
          name: v.fprojectName,
          sampleName: v.fsampleName,
          fgeneCode: v.fgeneCode,
          libModificationType: v.flibModificationType,
          concentration: v.fconcentration,
          libraryType: v.flibType,
          customerAmount: v.forderDataVolumeSize,
          scheduledAmount: v.fdataVolumeSize,
          makeDNB: v.fmakeDnb,
          currentWorkFlowId: v.fcurrentWorkFlowId,
          fnucleateCode: v.fnucleateCode,
          fvolume: v.fvolume,
          foldSampleName: v.foldSampleName,
          fisLibTestComplete: v.fisLibTestComplete,
          fisLibTestCompleteNum: v.fisLibTestCompleteNum,
          forderCode: v.forderCode,
          fsourceType: v.fsourceType,
          process: v.fprocessFlow,
          isHeightLight: isHeightLight,
          // 文库浓度
          libConcentration: v.fconcentration
        }
        /**
         * 设置makeDNB 两步法
         */
        const notOneLibType = [
          'PCR-free文库',
          '10xATAC单细胞文库',
          '甲基化文库-WGBS',
          '甲基化文库-RRBS',
          '甲基化文库-其他',
          'C4 v2.0单细胞文库',
          'C4 ATAC单细胞文库'
        ]

        if (notOneLibType.some(lib => item.libraryType.includes(lib))) {
          item.makeDNB = '两步法'
        }

        /**
         * 设置makeDNB 一步法
         */
        const notOneModificationLibType = ['illumina-已环化', 'MGI-已环化']
        if (notOneModificationLibType.includes(item.libModificationType)) {
          item.makeDNB = '两步法'
        }
        let flows = this.processFlow[v.flibModificationType] || []

        flows = flows.map((v, i) => {
          const flow = JSON.parse(JSON.stringify(this.process[v]))
          // 根据makeDNB设置工序
          if (flow.index === 4 && item.makeDNB === '一步法') {
            flow.isDelete = 1
          }
          // 判断是否是外来文库
          if (item.sampleName.includes('cL')) {
            // 项目/文库修饰类型在任务单中只有一个的，默认不配置2个pooling工序；
            const isOneLib = rows.filter(row => row.fsampleName.includes('cL')).filter(row => row.flibModificationType === item.libModificationType).length === 1
            const isOneProject = rows.filter(row => row.fsampleName.includes('cL')).filter(row => row.fprojectName === item.name).length === 1

            if (isOneLib || isOneProject) {
              if (flow.index === 1 || flow.index === 3) {
                flow.isDelete = 1
              }
            }
          }
          if (item.libModificationType === 'illumina-未磷酸化') {
            if (flow.index === 1 && item.libConcentration < 1) {
              flow.isDelete = 1
            }
            if (flow.index === 3 && item.libConcentration >= 1) {
              flow.isDelete = 1
            }
          }
          flow.disabled = 0
          let index = flow.index * 1
          // if (i !== 0 && index === 1) {
          //   index = 3
          // }
          // 根据当前环节设置工序
          if (index <= item.currentWorkFlowId * 1) {
            flow.disabled = 1
          }
          return flow
        })
        // 设置工序流程
        if (flows.length >= 0) {
          item.processFlowText = flows.filter(v => !v.isDelete && !v.disabled).map(v => v.process).join('-')
          item.processIndex = flows.filter(v => !v.isDelete && !v.disabled).map(v => v.index).join(',')
          item.processFlow = flows
          item.deleteIndex = flows.map((v, i) => {
            let value = ''
            if (v.isDelete) {
              value = i + 1
            }
            return value
          }).filter(v => v)
        }
        this.tableData.push(item)
      })
    },
    rowStyle  ({ row, rowIndex }) {
      if (row.isHeightLight) {
        return {
          backgroundColor: '#eee'
        }
      }
      return null
    },
    // 编辑工序
    handleEdit (index, row) {
      this.index = this.tableData.findIndex(v => v.sampleName === row.sampleName)
      this.editProcessDialogVisible = true
      this.libModificationType = row.libModificationType
      this.libType = row.libraryType
      this.libConcentration = row.libConcentration
      this.currentWorkFlowId = row.currentWorkFlowId
      this.indexList = row.deleteIndex
      const data = this.tableData[this.index] || {}
      this.makeDNB = data.makeDNB
      // 判断是否是外来文库且项目/文库修饰类型在任务单中只有一个的
      const isOneLib = this.tableData.filter(row => row.sampleName.includes('cL')).filter(item => row.libModificationType === item.libModificationType).length === 1
      const isOneProject = this.tableData.filter(row => row.sampleName.includes('cL')).filter(item => row.name === item.name).length === 1
      this.delPooling = isOneLib || isOneProject
      if (!row.sampleName.includes('cL')) {
        this.delPooling = false
      }
      this.fixData = this.tableData.filter(v => {
        return v.name === data.name && v.libModificationType === data.libModificationType
      })
    },
    handleEditProcess (processInfo) {
      const {isSync} = processInfo
      let data = this.tableData[this.index] || {}
      if (!isSync) {
        data = this.handleFixProcess(data, processInfo)
        this.tableData[this.index] = data
        this.$set(this, 'tableData', this.tableData)
        return
      }
      // 判断浓度是否小于1 ，是否足够
      const isEnough = data.libConcentration < 1
      // fixData 每条数据都需要同步
      data = this.tableData.map((v) => {
        // 判断浓度范围是否一致
        const isSome = isEnough === (v.libConcentration < 1)
        if (v.name === data.name && v.libModificationType === data.libModificationType && isSome) {
          v = this.handleFixProcess(v, processInfo)
        }
        return v
      })
      this.$set(this, 'tableData', data)
    },
    handleFixProcess (data, processInfo) {
      data.libModificationType = processInfo.type
      data.makeDNB = processInfo.makeDNB
      data.processFlow = processInfo.processFlow
      data.processFlowText = processInfo.processFlow.filter(v => !v.isDelete && !v.disabled).map(v => v.process).join('-')
      data.processIndex = processInfo.processFlow.filter(v => !v.isDelete && !v.disabled).map(v => v.index).join(',')
      data.deleteIndex = processInfo.processFlow.map((v, i) => {
        let value = ''
        if (v.isDelete) {
          value = i + 1
        }
        return value
      }).filter(v => v)
      return data
    },
    handleValidForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.$message.error('表单存在错误，请检查')
          }
        })
      })
    },
    // 下一步
    async handleNext () {
      const fileList = this.$refs.upload.uploadFiles || []
      if (fileList.length < 1) {
        this.$message.error('请上传任务文件')
        return
      }
      this.form.fileList = fileList
      await this.handleValidForm()
      this.loading = true
      this.$refs.upload.submit()
    },
    setParams () {
      const sampleList = []
      this.tableData.forEach(v => {
        const item = {
          fprojectName: v.name,
          fsampleName: v.sampleName,
          flibModificationType: v.libModificationType,
          fconcentration: v.concentration,
          flibType: v.libraryType,
          fgeneCode: v.fgeneCode,
          fvolume: v.fvolume,
          fsourceType: v.fsourceType,
          forderDataVolumeSize: v.customerAmount,
          fdataVolumeSize: v.scheduledAmount,
          fmakeDnb: v.makeDNB,
          fisLibTestComplete: v.fisLibTestComplete,
          fisLibTestCompleteNum: v.fisLibTestCompleteNum,
          fnucleateCode: v.fnucleateCode,
          foldSampleName: v.foldSampleName,
          // flibConcentration: v.libConcentration,
          forderCode: v.forderCode,
          fprocessFlow: v.processIndex
        }
        sampleList.push(item)
      })
      return {
        sampleList,
        fplatforms: this.form.sequencingPlatform,
        fseqType: this.form.sequencingType,
        fmakeDnb: this.form.makeDNB,
        fproductionArea: this.options[this.form.area]
      }
    },
    async handleConfirm () {
      const $table = this.$refs.tableRef
      const errMap = await $table.fullValidate(true)
      if (errMap) {
        this.$message.error('请检查表格内容是否有误')
        return
      }
      const params = this.setParams()
      this.loading = true
      const {res} = await awaitWrap(saveTaskList(params))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('导入成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-upload-dragger {
  width: 100%;
  height: 100%;
  padding: 20px;
}
/deep/ .el-upload {
  width: 100%;
  height: 100%;
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 10px 0;
}
.tips {
  margin: 3px 0;
  font-size: 14px;
  font-weight: 300;
  color: #666;
}
.dialog-content {
  max-height: 60vh;
  overflow: auto;
  padding: 0 10px;
}

</style>
