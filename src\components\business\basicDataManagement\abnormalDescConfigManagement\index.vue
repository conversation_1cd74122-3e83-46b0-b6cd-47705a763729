<template>
  <div>
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="120px" size="mini"
        style="display: flex;justify-content: space-between" @submit.native.prevent @keyup.enter.native.prevent="handleSearch">
        <el-form-item label="异常描述标准" prop="descName">
          <el-input v-model.trim="form.descName" clearable placeholder="请输入异常描述标准"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <!--按钮组-->
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('002021001', 'buttons')" type="primary" plain size="mini" @click="handleCreate">新增</el-button>
      <el-button v-if="$setAuthority('002021002', 'buttons')" type="primary" plain size="mini" @click="handleEdit(1)">复制新增</el-button>
      <el-button v-if="$setAuthority('002021003', 'buttons')" type="primary" plain size="mini" @click="handleEdit(2)">修改</el-button>
      <el-button type="primary" plain size="mini" @click="handleEdit(3)">查看详情</el-button>
      <el-button v-if="$setAuthority('002021004', 'buttons')"  type="danger" plain size="mini" @click="handleDelete">删除</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">搜索</el-button>
      <el-button type="primary" plain size="mini" @click="handleReset">重置</el-button>
    </div>

    <el-table
      ref="table"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      @row-click="handleRowClick"
      @select="handleSelectTable"
      @select-all="handleSelectAll"
      @sort-change="handleSortChange"
      size="mini"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="fexceptionDescriptionStandard" label="异常描述标准" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column prop="fdescription" label="说明" min-width="200" align="center" show-overflow-tooltip />
      <el-table-column prop="fcreator" label="创建人" width="120" align="center" />
      <el-table-column prop="createTime" label="创建时间" width="150" align="center" sortable="custom"
        :sort-orders="['ascending', 'descending', null]" />
      <el-table-column prop="fupdator" label="修改人" width="120" align="center" />
      <el-table-column prop="updateTime" label="修改时间" width="150" align="center" sortable="custom"
        :sort-orders="['ascending', 'descending', null]" />
    </el-table>

    <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>

    <!-- 新增/修改/查看详情对话框 -->
    <abnormal-desc-edit-dialog
      :pvisible.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :id="currentId"
      @dialogConfirmEvent="getData"
    />
  </div>
</template>

<script>
import AbnormalDescEditDialog from './abnormalDescEditDialog'
import { getAbnormalDescList, deleteAbnormalDesc } from '../../../../api/basicDataManagement/abnormalDescApi'
import mixins from '../../../../util/mixins'
import util, { awaitWrap } from '../../../../util/util'

export default {
  name: 'AbnormalDescConfigManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    AbnormalDescEditDialog
  },
  created () {
    this.getData()
  },
  data () {
    return {
      form: {
        descName: ''
      },
      submitForm: {},
      tableData: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '', // create, update, view, copy
      currentId: null,
      selectedRow: null,
      deleteDialogVisible: false,
      sortParams: {
        prop: 'fcreateTime',
        order: 'descending'
      }
    }
  },
  methods: {
    async getData () {
      const {res} = await awaitWrap(getAbnormalDescList({
        fexceptionDescriptionStandard: this.submitForm.descName,
        fupdateTimeOrder: this.sortParams.fupdateTimeOrder,
        fcreateTimeOrder: this.sortParams.fcreateTimeOrder,
        pagedRequest: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }))
      if (res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        const records = data.records || []
        this.totalPage = data.total
        this.selectedRows.clear()
        this.tableData = []
        records.forEach(v => {
          const item = {
            fexceptionDescriptionStandard: v.fexceptionDescriptionStandard,
            fdescription: v.fdescription,
            fupdator: v.fupdator,
            fcreator: v.fcreator,
            createTime: v.createTime,
            updateTime: v.updateTime,
            id: v.fid
          }
          util.setDefaultEmptyValueForObject(item)
          item.realData = util.deepCopy(v)
          this.tableData.push(item)
        })
      }
    },
    handleSearch () {
      this.submitForm = {
        descName: this.form.descName
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        descName: ''
      }
      this.handleSearch()
    },
    handleCreate () {
      this.dialogType = null
      this.currentId = null
      this.dialogVisible = true
    },
    handleEdit (type) {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一行数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.dialogType = type
      this.currentId = row.id
      this.dialogVisible = true
    },
    async handleDelete () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一行数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      await this.$confirm('确认要删除该异常描述配置吗？', '提示', {
        type: 'warning'
      })
      this.handleConfirmDelete(row)
    },
    async handleConfirmDelete (row) {
      const {res} = await awaitWrap(deleteAbnormalDesc(row.id))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
        this.getData()
        this.selectedRow = null
        this.deleteDialogVisible = false
      }
    },
    handleSortChange (column) {
      if (!column.order) {
        this.sortParams.fupdateTimeOrder = null
        this.sortParams.fcreateTimeOrder = null
      }
      if (column.prop === 'createTime') {
        this.sortParams.fupdateTimeOrder = null
        this.sortParams.fcreateTimeOrder = column.order === 'ascending' ? 1 : 0
      }
      if (column.prop === 'updateTime') {
        this.sortParams.fupdateTimeOrder = column.order === 'ascending' ? 1 : 0
        this.sortParams.fcreateTimeOrder = null
      }
      this.getData()
    }
  }
}
</script>

<style scoped>

</style>
