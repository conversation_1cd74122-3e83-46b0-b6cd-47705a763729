import { myAjax } from '@/util/ajax'

/**
 * 获取pooling列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205699&itf=5799149
 * @param options
 * @returns {*}
 */
export function getCyclizationList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cyclize/get_list',
    data: data,
    ...options
  })
}

/**
 * 下载任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205699&itf=5799163
 * @param options
 * @returns {*}
 */
export function downloadTask (data, options = {}) {
  return myAjax({
    url: '/experiment/cyclize/export_task',
    responseType: 'blob',
    data: data,
    ...options
  })
}
