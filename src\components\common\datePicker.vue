<script>
export default {
  name: 'datePicker',
  props: {
    type: {
      type: String,
      default: 'datetime'
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    value: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  computed: {
    times: {
      get () {
        return this.value
      },
      set (val) {
        this.$emit('update:value', val)
      }
    }
  },
  methods: {
    handleChange () {
      // 判断开始时间是否大于结束时间
      if (this.times[0] && this.times[1] && this.times[0] > this.times[1]) {
        this.$set(this.value, 1, '')
        this.$message.error('开始时间不能大于结束时间')
      }
    }
  }
}
</script>

<template>
  <div>
    <el-date-picker
      v-model="times[0]"
      size="mini"
      :type="type"
      :value-format="valueFormat"
      :default-time="'00:00:00'"
      placeholder="选择日期"
      @change="handleChange">
    >
    </el-date-picker>
    <span>--</span>
    <el-date-picker
      v-model="times[1]"
      :type="type"
      :value-format="valueFormat"
      :default-time="'23:59:59'"
      size="mini"
      placeholder="选择日期"
      @change="handleChange"
    >
    </el-date-picker>
  </div>
</template>

<style scoped lang="scss">

</style>
