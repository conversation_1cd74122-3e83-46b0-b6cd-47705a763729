<template>
  <el-dialog
    title="标记处理"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    width="500px"
    @open="handleOpen">
    <div>勾选样本例数：{{ids.length}}</div>
    <el-form ref="form" :model="form" label-suffix=":" :rules="rules" label-position="left" label-width="110px">
      <el-form-item label="异常处理措施" prop="abnormalMeasure">
        <el-input v-model.trim="form.abnormalMeasure" size="mini" clearable></el-input>
      </el-form-item>
    </el-form>
    <div>标记处理后无法撤销，确认继续？</div>
    <span slot="footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'
import {awaitWrap} from '../../../../../util/util'
import {signErrorHandler} from '../../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'signHandlerDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      form: {
        abnormalMeasure: ''
      },
      rules: {
        abnormalMeasure: [
          {required: true, message: '请输入异常处理措施', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    handleValidForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.$message.error('表单存在错误，请检查')
          }
        })
      })
    },
    // 保存
    async handleConfirm () {
      await this.handleValidForm()
      await awaitWrap(signErrorHandler({
        fidList: this.ids,
        fhandleMeasures: this.form.abnormalMeasure
      }))
      this.visible = false
      this.$emit('dialogConfirmEvent')
    }
  }
}
</script>

<style scoped>

</style>
