<template>
  <div class="sub-order-wrapper">
    <div class="table-wrapper">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="sub-table"
        size="mini"
        border
        :row-class-name="handleClassName"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column prop="projectCode" label="项目编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="species" label="物种" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneNum" label="吉因加编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="oriSampleName" label="样本原始名称" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="产品名称" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="probe" label="探针" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="libNum" label="文库编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="seqTime" label="上机时间" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="totalReads" label="TotalReads" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qcTaskId" label="质控任务id" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="internalControlResult" label="内控结果" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <!-- 根据质控状态设置不同的文字颜色:
                 0 - 灰色(未启动)
                 1 - 蓝色(已启动)
                 2 - 绿色(已完成) -->
            <tooltips :txt-info="scope.row.internalResultText + ''" :class="getQcResultClass(scope.row.internalResult)"></tooltips>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" min-width="100" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util, {awaitWrap} from '@/util/util'
import {getSubDeliverOrderList} from '@/api/sequencingManagement/singleCell/index'

export default {
  name: 'SubOrderList',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    id: {
      type: Number,
      default: null
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    selectedRowsSize () {
      this.$emit('subOrderSelectedChange', {key: this.id, data: [...this.selectedRows.values()]})
    }
  },
  data () {
    return {
      // 0不合格 1合格 2强制合格
      statusMap: {
        0: '不合格',
        1: '合格',
        2: '强制合格'
      },
      tableData: []
    }
  },
  methods: {
    async getData (id) {
      const { res } = await awaitWrap(getSubDeliverOrderList({
        fcosQualityDeliverId: id,
        ...this.params
      }, {loadingDom: '.sub-table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        data.forEach(v => {
          const item = {
            id: v.fcosQualityDeliverDetailId || '',
            projectCode: v.fprojectCode || '',
            projectName: v.fprojectName || '',
            species: v.fspecies || '',
            geneNum: v.fgeneNum || '',
            oriSampleName: v.fcosSampleName || '',
            productName: v.fproductName || '',
            probe: v.flibTypeSuffix || '',
            libNum: v.flibNum || '',
            seqTime: v.fsequenceTime || '',
            totalReads: v.ftotalReadsHalf || '',
            internalResult: v.finternalResult || '',
            internalResultText: this.statusMap[v.finternalResult] || '',
            qcTaskId: v.fqualityAnalysisTaskNum || '',
            operator: v.fqualityAnalysisTaskCreator || ''
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
          this.$nextTick(() => {
            if (this.$refs.table) {
              if (this.$refs.table) this.$refs.table.doLayout()
            }
          })
        })
      }
    },
    /**
     * 获取质控状态对应的样式类名
     * @param {number} status - 质控状态码
     * @returns {string} 对应的CSS类名
     */
    getQcResultClass (status) {
      const statusClassMap = {
        0: 'result-status--unfinish', // 不合格
        1: 'result-status--finish', // 合格
        2: 'result-status--finish' // 合格
      }
      return statusClassMap[status] || ''
    },
    checkSelectable (row) {
      return row.realData.cosDeliverBatchCode
    },
    // 点击行
    handleRowClick (row, c) {
      if (!this.checkSelectable(row)) return
      this.handleSelectTable(undefined, row)
    }
  }
}
</script>

<style scoped lang="scss">
.sub-order-wrapper {
  //max-height: 20vh;
  padding-left: 30px;
}
.table-wrapper {
}
.blue {
  color: $color
}
.green {
  color: $success-color
}
.red {
  color: $fail-color
}
.qc-status {
  &--not-started { color: #747474; }  // 未启动 - 灰色
  &--in-progress { color: #409EFF; }  // 已启动 - 蓝色
  &--completed { color: #51C86B; }    // 已完成 - 绿色
}

.result-status {
  &--unfinish { color: #FF4D4F; }     // 已启动 - 蓝色
  &--finish { color: #51C86B; }    // 已完成 - 绿色
}
</style>
