<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="65%"
      @open="handleOpen"
    >
      <div style="max-height: 400px; overflow: auto;">
        <el-form ref="form" :model="form" label-width="90px" size="mini" label-suffix=":">
          <el-row>
            <template v-for="(item, index) in form.list">
              <div :key="'familyHistory' + index">
                <el-col :span="21">
                  <el-form-item :prop="'list.' + index + '.illPlace'" :rules="illPlaceRules" label="疾病部位">
                    <el-select v-model="item.illPlace" clearable placeholder="请选择" @change="value => handleIllPlaceChange(value, index)">
                      <el-option
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        v-for="item in illPlacesList">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label-width="0px">
                    <el-button v-if="index === form.list.length - 1 && title === '新增疾病史'" type="primary" icon="el-icon-plus" circle @click="handleAdd"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle @click="handleDelete(index)"></el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="item.illPlace">
                  <el-form-item :prop="'list.' + index + '.illList'" :rules="illListRules">
                    <el-checkbox-group v-model="item.illList">
                      <template v-if="item.illPlace === '肺部'">
                        <el-checkbox :label="1">慢性阻塞性疾病(即具有气流阻塞特征的慢性支气管炎和/或肺气肿)</el-checkbox>
                        <el-checkbox :label="2">弥漫性肺纤维化</el-checkbox>
                      </template>
                      <template v-else-if="item.illPlace === '结直肠'">
                        <el-checkbox :label="1">便血</el-checkbox>
                        <el-checkbox :label="2">黑便</el-checkbox>
                        <el-checkbox :label="3">贫血</el-checkbox>
                        <el-checkbox :label="4">不明原因体重减轻</el-checkbox>
                        <el-checkbox :label="5">慢性腹泻</el-checkbox>
                        <el-checkbox :label="6">慢性便秘</el-checkbox>
                        <el-checkbox :label="7">黏液血便</el-checkbox>
                        <el-checkbox :label="8">慢性阑尾炎或阑尾切术史</el-checkbox>
                        <el-checkbox :label="9">慢性胆囊炎或胆囊切除史</el-checkbox>
                        <el-checkbox :label="10">大便习惯改变</el-checkbox>
                        <el-checkbox :label="11">长期溃疡性结肠炎</el-checkbox>
                        <el-checkbox :label="12">肠道腺瘤史（腺瘤、家族性腺瘤性息肉病、非家族性腺瘤性息肉病、炎症性）</el-checkbox>
                      </template>
                      <template v-else-if="item.illPlace === '食管'">
                        <el-checkbox :label="2">上消化道症状(如恶性、呕吐、腹痛、反酸、进食不适等症状)</el-checkbox>
                        <el-checkbox :label="3">慢性食管炎</el-checkbox>
                        <el-checkbox :label="4">反流性食管炎</el-checkbox>
                        <el-checkbox :label="5">食管白斑症</el-checkbox>
                        <el-checkbox :label="1">食管憩室</el-checkbox>
                        <el-checkbox :label="6">食管良性狭窄</el-checkbox>
                        <el-checkbox :label="7">贲门失迟缓症</el-checkbox>
                        <el-checkbox :label="8">Barrett食管</el-checkbox>
                        <el-checkbox :label="9">Barrett食管相关异性增生</el-checkbox>
                        <el-checkbox :label="10">食管鳞状上皮异性增生</el-checkbox>
                      </template>
                      <template v-else-if="item.illPlace === '胃部'">
                        <el-checkbox :label="1">中重度萎缩性胃炎</el-checkbox>
                        <el-checkbox :label="2">慢性胃溃疡</el-checkbox>
                        <el-checkbox :label="3">胃息肉</el-checkbox>
                        <el-checkbox :label="4">胃粘膜巨大皱褶征</el-checkbox>
                        <el-checkbox :label="5">胃部手术后残胃</el-checkbox>
                        <el-checkbox :label="6">肥厚性胃病</el-checkbox>
                        <el-checkbox :label="7">恶性贫血</el-checkbox>
                        <el-checkbox :label="8">胃上皮异型增生</el-checkbox>
                      </template>
                      <template v-else-if="item.illPlace === '肝部'">
                        <el-checkbox :label="1">乙肝</el-checkbox>
                        <el-checkbox :label="2">丙肝</el-checkbox>
                        <el-checkbox :label="3">非酒精脂肪性肝炎</el-checkbox>
                        <el-checkbox :label="4">肝硬化</el-checkbox>
                        <el-checkbox :label="5">药物性肝损伤</el-checkbox>
                      </template>
                      <template v-else-if="item.illPlace === '乳腺'">
                        <el-checkbox :label="1">无明显诱因的中上腹饱胀不适、腹痛</el-checkbox>
                        <el-checkbox :label="2">慢性胰腺炎反复发作</el-checkbox>
                        <el-checkbox :label="3">无家族遗传史的新近突发糖尿病</el-checkbox>
                        <el-checkbox :label="4">主胰管型粘液乳头状瘤</el-checkbox>
                        <el-checkbox :label="5">粘液性囊性腺瘤</el-checkbox>
                        <el-checkbox :label="6">实性假乳头状瘤</el-checkbox>
                      </template>
                      <template v-else-if="item.illPlace === '胰腺'">
                        <el-checkbox :label="1">乳腺导管或小叶有中度或重度不典型增生</el-checkbox>
                        <el-checkbox :label="2">小叶原位癌</el-checkbox>
                        <el-checkbox :label="3">乳管内乳头状瘤</el-checkbox>
                        <el-checkbox :label="4">胸部放疗史（≥10年）</el-checkbox>
                      </template>
                      <template v-else>
                        <el-checkbox :label="1">自身免疫性疾病（红斑狼疮、类风湿性关节炎、原发性甲亢、I型糖尿病等）</el-checkbox>
                        <el-checkbox :label="2">免疫缺陷病(HIV阳性）</el-checkbox>
                        <el-checkbox :label="3">使用免疫抑制药物</el-checkbox>
                      </template>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </div>
            </template>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'clinicalInfoManagementSickHistorySaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      sampleBasicId: null,
      title: '新增疾病史',
      form: {
        list: [
          {
            sickDiseaseHisid: null,
            sampleBasicId: null,
            illPlace: '',
            sickDetail: '',
            illDetail: '',
            illList: []
          }
        ]
      },
      illPlacesList: [
        {
          value: '肺部',
          label: '肺部'
        },
        {
          value: '结直肠',
          label: '结直肠'
        },
        {
          value: '食管',
          label: '食管'
        },
        {
          value: '胃部',
          label: '胃部'
        },
        {
          value: '肝部',
          label: '肝部'
        },
        {
          value: '乳腺',
          label: '乳腺'
        },
        {
          value: '胰腺',
          label: '胰腺'
        },
        {
          value: '免疫系统',
          label: '免疫系统'
        }
      ],
      illPlaceRules: [
        {required: true, message: '请选择部位', trigger: ['change', 'blur']}
      ],
      illListRules: [
        {required: true, message: '请选择病情', trigger: ['change', 'blur']}
      ]
    }
  },
  methods: {
    handleOpen () {
      this.sampleBasicId = this.pdata.sampleBasicId
      if (this.pdata.sickDiseaseHisid) {
        this.title = '编辑疾病史'
      } else {
        this.title = '新增疾病史'
      }
      this.form.list = []
      this.form.list.push(this.pdata)
    },
    handleIllPlaceChange (value, index) {
      this.form.list[index].illList = []
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = {
            sickHistories: [],
            sampleBasicId: this.sampleBasicId
          }
          this.form.list.forEach(v => {
            data.sickHistories.push({
              sickHistories: v.sickHistories,
              sampleBasicId: v.sampleBasicId,
              illPlace: v.illPlace,
              illDetail: v.illList.toString()
            })
          })
          this.$ajax({
            url: '/sample/clinical/save_sick_history',
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('sickHistorySaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          })
        }
      })
    },
    handleAdd () {
      this.form.list.push({
        sickDiseaseHisid: null,
        sampleBasicId: this.sampleBasicId,
        illPlace: '',
        sickDetail: '',
        illDetail: '',
        illList: []
      })
    },
    handleDelete (index) {
      this.form.list.splice(index, 1)
      if (this.form.list.length === 0) {
        this.handleAdd()
      }
    }
  }
}
</script>

<style scoped>

</style>
