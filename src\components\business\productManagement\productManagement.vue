<template>
  <div style="height: 100%;">
    <div class="container">
    <div class="left-part">
      <div class="main card">
        <product-catalog-tree
          @catalogChange="handleCatalogChange"/>
      </div>
    </div>
    <div class="resize" title="收缩侧边栏">
      ⋮
    </div>
    <div class="right-part">
      <div class="main card" style="height: 100%;">
           <!--搜索表单-->
    <div class="search">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini" style="display: flex;justify-content: space-between" @keyup.enter.native="handleSearch">
        <div>
<!--          <el-form-item label="产品分类" prop="typeName">-->
<!--            <el-input v-model.trim="form.typeName" clearable placeholder="请输入产品分类"></el-input>-->
<!--          </el-form-item>-->
          <el-form-item label="产品编号" prop="productCode">
            <el-input v-model.trim="form.productCode" clearable placeholder="请输入产品编号"></el-input>
          </el-form-item>
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model.trim="form.productName" clearable placeholder="请输入产品名称"></el-input>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--表格容器-->
    <div class="product-table-wrapper">
      <!--产品列表-->
      <div style="min-width:calc(100% - 300px);height: 100%;margin-left: 10px">
        <div class="table-name">产品列表</div>
        <el-table
          ref="tableList"
          :data="tableData"
          size="mini"
          height="calc(100% - 40px - 32px)"
          border
          class="table"
          style="width:100%">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="productCode" label="产品编号" show-overflow-tooltip min-width="120"></el-table-column>
          <el-table-column prop="productName" label="产品名称" show-overflow-tooltip min-width="180"></el-table-column>
          <el-table-column prop="shortname" label="产品简称"  min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productTypeName" label="产品目录" show-overflow-tooltip min-width="180"></el-table-column>
          <el-table-column prop="isComboType" label="产品类型" show-overflow-tooltip  min-width="120"></el-table-column>
          <el-table-column prop="state" label="上下架状态" show-overflow-tooltip min-width="120"></el-table-column>
          <el-table-column prop="creator" label="创建人" show-overflow-tooltip min-width="120"></el-table-column>
          <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="160"></el-table-column>
          <el-table-column prop="modifier" label="修改人" show-overflow-tooltip min-width="120"></el-table-column>
          <el-table-column prop="updateTime" label="修改时间" show-overflow-tooltip min-width="160"></el-table-column>
          <el-table-column label="操作" min-width="60" fixed="right">
            <template slot-scope="scope">
              <el-popover
                :trigger="($setAuthority('015001001', 'buttons')|| $setAuthority('015001002', 'buttons') || $setAuthority('015001003', 'buttons'))? 'click' : ''"
                placement="bottom-start"
                width="100"
              >
                <div v-if="$setAuthority('015001001', 'buttons')" class="options" @click="handleOpenConfigExperimental(scope.row)">实验配置</div>
                <div v-if="$setAuthority('015001001', 'buttons')" class="options" @click="handleSetProductEmail(scope.row)">产品邮件配置</div>
                <div v-if="$setAuthority('015001002', 'buttons')" class="options" @click="handleOpenInterpretConfig(scope.row)">解读配置</div>
                <div v-if="$setAuthority('015001003', 'buttons')" class="options" @click="handleCustomerProjectConfig(scope.row)">客户项目配置</div>
                <div v-if="$setAuthority('015001005', 'buttons')" class="options" @click="handleArriveSampleConfig(scope.row)">到样标准配置</div>
                <div slot="reference" class="options">
                  <icon-svg style="font-size: 16px" icon-class="icon-gengduo"></icon-svg>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
      </div>
    </div>
  </div>

    <product-experimental-config-dialog
      :is-sample="isSample"
      :product-id="productId"
      :pvisible.sync="configExperimentalDialogInfo.visible"
      :title="configExperimentalDialogInfo.title"
      @dialogConfirmEvent='handleExperimentalConfigDialog'
    />
    <product-interpret-config-dialog
      :is-sample="isSample"
      :product-id="productId"
      :pvisible.sync="interpretConfigDialogInfo.visible"
      :title="interpretConfigDialogInfo.title"
      @dialogConfirmEvent='handleInterpretConfigDialog'
    />
    <product-customer-project-dialog
      :pvisible.sync="customerProjectDialogInfo.visible"
      :product-id="productId"
      :title="customerProjectDialogInfo.title"
      @dialogConfirmEvent='handleCustomerProjectDialog'
    />

    <set-product-email-dialog
      :pvisible.sync="emailvisible"
      :product-id="productId"
      :product-code="productCode"
      :product-name="productName"
      @dialogConfirmEvent='getData'
    />
    <arrive-sample-dialog
      :pvisible.sync="arriveSampleDialogVisible"
      :product-id="productId"
      @dialogConfirmEvent='getData'/>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util from '@/util/util'
import productExperimentalConfigDialog from './productExperimentalConfigDialog'
import productInterpretConfigDialog from './productInterpretConfigDialog'
import productCustomerProjectDialog from './productCustomerProjectDialog'
import setProductEmailDialog from './setProductEmailDialog.vue'
import ArriveSampleDialog from './arriveSampleDialog.vue'
import productCatalogTree from './components/productCatalogTree.vue'

export default {
  name: 'productManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ArriveSampleDialog,
    setProductEmailDialog,
    productExperimentalConfigDialog,
    productInterpretConfigDialog,
    productCustomerProjectDialog,
    productCatalogTree
  },
  beforeMount () {
    window.addEventListener('resize', this.dragControllerDiv)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.dragControllerDiv)
  },
  mounted () {
    this.dragControllerDiv()
    // 获取产品分类
    this.handleSearch()
  },
  watch: {
    filterText (val) {
      console.log(val)
      this.$refs.tree.filter(val)
    }
  },
  data () {
    return {
      typeId: '',
      filterText: '',
      isSample: false,
      emailvisible: false,
      arriveSampleDialogVisible: false,
      productDirectoryId: '',
      productCode: '',
      productName: '',
      selectedCategoryRows: new Map(),
      currentCatalog: [],
      productCategory: [], // 产品分类列表
      form: { // 表单数据
        type: '',
        typeName: '',
        productCode: '',
        productName: ''
      },
      // 0草稿,1已上架,2已下架
      stateText: { // 产品状态
        '0': '草稿',
        '1': '已上架',
        '2': '已下架'
      },
      submitForm: { // 产品列表接口请求参数
        typeName: '',
        productCode: '',
        productName: ''
      },
      productId: 0,
      configExperimentalDialogInfo: {
        title: '实验配置',
        visible: false
      },
      interpretConfigDialogInfo: {
        title: '解读配置',
        visible: false
      },
      customerProjectDialogInfo: {
        title: '客户项目配置',
        visible: false
      }
    }
  },
  methods: {
    // 按条件搜索
    handleSearch () {
      this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.typeId = ''
      this.selectedCategoryRows.clear()
      this.currentPage = 1
      this.getData()
    },
    // 重置搜索
    handleReset () {
      this.$nextTick(() => {
        this.typeId = ''
        this.productDirectoryId = ''
        this.selectedCategoryRows.clear()
        this.$refs.form.resetFields()
        this.handleSearch()
      })
    },
    // 拖拽时改变宽度
    dragControllerDiv () {
      let menuWidth = 0
      let resize = document.getElementsByClassName('resize')
      let left = document.getElementsByClassName('left-part')
      let mid = document.getElementsByClassName('right-part')
      let box = document.getElementsByClassName('container')
      let that = this
      for (let i = 0; i < resize.length; i++) {
        // 鼠标按下事件
        resize[i].onmousedown = function (e) {
          that.flag = true
          // 颜色改变提醒
          resize[i].style.background = '#818181'
          let startX = e.clientX
          resize[i].left = resize[i].offsetLeft
          // 鼠标拖动事件
          document.onmousemove = function (e) {
            let endX = e.clientX
            let moveLen = resize[i].left + (endX - startX) - menuWidth // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
            let maxT = box[i].clientWidth - resize[i].offsetWidth // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

            if (moveLen < 120) moveLen = 120 // 左边区域的最小宽度为120
            if (moveLen > maxT - 500) moveLen = maxT - 500 // 右边区域最小宽度为500

            resize[i].style.left = moveLen // 设置左侧区域的宽度

            for (let j = 0; j < left.length; j++) {
              left[j].style.width = moveLen + 'px'
              mid[j].style.width = (box[i].clientWidth - moveLen - 10) + 'px'
            }
          }
          // 鼠标松开事件
          document.onmouseup = function (evt) {
            // 颜色恢复
            resize[i].style.background = '#d6d6d6'
            document.onmousemove = null
            document.onmouseup = null
            resize[i].releaseCapture && resize[i].releaseCapture() // 当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
          }
          resize[i].setCapture && resize[i].setCapture() // 该函数在属于当前线程的指定窗口里设置鼠标捕获
          return false
        }
      }
    },
    handleClickDirectory (data) {
      this.productDirectoryId = data.productDirectoryId
      this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.getData()
    },
    handleCatalogChange (row) {
      this.currentCatalog = row
      this.getData()
    },
    // 获取产品列表数据
    getData () {
      // this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.submitForm.typeId = this.typeId
      this.submitForm.productDirectoryIdList = this.currentCatalog
      this.submitForm.page = this.currentPage
      this.submitForm.rows = this.pageSize
      this.$ajax({
        url: '/system/product/get_product_list',
        method: 'post',
        loadingDom: '.table',
        data: this.submitForm
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          this.totalPage = data.total
          let item = {}
          data.records.forEach(v => {
            item = {
              productId: v.productId,
              productCode: v.productCode,
              productName: v.productName,
              shortname: v.shortname,
              productType: v.productType,
              productTypeName: v.productTypeName,
              state: this.stateText[v.fproductStatus],
              isComboType: v.isComboType,
              creator: v.creator,
              createTime: v.createTime,
              modifier: v.modifier,
              updateTime: v.updateTime
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 点击行
    handleCategoryRowClick (row) {
      this.handleCategorySelect(undefined, row)
    },
    // 选中行
    handleCategorySelect (selection, row) {
      if (!this.selectedCategoryRows.has(row.typeId)) {
        this.$refs.tableCategory.clearSelection()
        this.selectedCategoryRows.clear()
      }
      this.$refs.tableCategory.toggleRowSelection(row, !this.selectedCategoryRows.has(row.typeId))
      if (this.selectedCategoryRows.has(row.typeId)) {
        this.selectedCategoryRows.delete(row.typeId)
      } else {
        this.selectedCategoryRows.set(row.typeId, row)
        this.currentPage = 1
        this.submitForm = {}
        this.typeId = row.typeId
        this.getData()
      }
    },
    // 设置产品邮箱
    handleSetProductEmail (row) {
      this.isSample = (row.isComboType.trim() === '单产品')
      this.productId = row.productId
      this.productCode = row.productCode
      this.productName = row.productName
      this.emailvisible = true
    },
    // 开启实验配置弹窗
    handleOpenConfigExperimental (row) {
      this.isSample = (row.isComboType.trim() === '单产品')
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.configExperimentalDialogInfo.title = '实验配置' + ' : ' + row.productName
      this.configExperimentalDialogInfo.visible = true
    },
    // 实验配置确认
    handleExperimentalConfigDialog () {
      this.getData()
    },
    // 打开客户项目配置
    handleOpenInterpretConfig (row) {
      this.isSample = (row.isComboType.trim() === '单产品')
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.interpretConfigDialogInfo.title = '解读配置' + ' : ' + row.productName
      this.interpretConfigDialogInfo.visible = true
    },
    // 解读客户项目确认
    handleInterpretConfigDialog () {
      this.getData()
    },
    // 打开解读配置
    handleCustomerProjectConfig (row) {
      this.isSample = (row.isComboType.trim() === '单产品')
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.customerProjectDialogInfo.visible = true
    },
    handleArriveSampleConfig (row) {
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.arriveSampleDialogVisible = true
    },
    // 解读配置确认
    handleCustomerProjectDialog () {
      this.getData()
    },
    // 根据分类编号获取分类名 部分数据不对
    filterTypeName (typeCode) {
      try {
        return this.productCategory.find((item) => item.typeId === typeCode).typeName
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.search {
  height: 48px;
}
/deep/ .el-table__header .el-checkbox {
  display: none;
}

.search >>> .el-form-item--mini {
  margin-bottom: 10px;
  margin-top: 10px;
}

.table-style {
  .el-table-column--selection .el-checkbox {
    display: none !important;
    background: #409EFF;
  }
}

.el-input {
  width: 130px;
}

.product-table-wrapper {
  display: flex;
  width: 100%;
  height: calc(100% - 48px - 32px);
  margin-top: 22px;

  .table-name {
    display: flex;
    height: 40px;
    border: 1px solid #EBEEF5;
    border-bottom: none;
    align-items: center;
    padding-left: 10px;
  }
  .table-content {
    width: 100%;
    border: 1px solid #EBEEF5;
    border-top: none;
    align-items: center;
    padding: 0 10px;
    height: calc(100vh - 240px);
    overflow-y: scroll;
  }
}

.options {
  color: #409EFF;
  //margin: 5px 3px;
  cursor: pointer;
}

.container {
  width: 100%;
  height: 100%;
  margin: 1% 0;
  overflow: hidden;
  display: flex;
}

.left-part {
  width: 380px; /*左侧初始化宽度*/
  height: 100%;
  float: left;
  box-sizing: border-box;
}

/*拖拽区div样式*/
.resize {
  cursor: col-resize;
  float: left;
  background-color: #d6d6d6;
  border-radius: 5px;
  margin-top: 45vh;
  width: 10px;
  height: 50px;
  background-size: cover;
  background-position: center;
  /*z-index: 99999;*/
  font-size: 32px;
  color: white;
  line-height: 45px;
}

/*拖拽区鼠标悬停样式*/
.resize:hover {
  color: #444444;
}

.right-part {
  width: calc(100% - 380px);
  float: left;
  height: 100%;
}
</style>
