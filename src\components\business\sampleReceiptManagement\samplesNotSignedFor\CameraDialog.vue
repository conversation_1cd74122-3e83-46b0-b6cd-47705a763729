<template>
  <div>
    <el-dialog
      title="拍照记录"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="700px"
      top="5vh"
      @opened="handleOpen"
      :before-close="handleClose">
      <el-tabs v-model="uploadMethod">
        <el-tab-pane label="高拍仪" name="camera"></el-tab-pane>
        <el-tab-pane label="文件导入" name="file"></el-tab-pane>
      </el-tabs>
      <div v-show="uploadMethod === 'camera'">
        <div style="margin-bottom: 10px;">
          <el-button type="primary" size="mini" icon="el-icon-camera" @click="handleTakePhoto">拍摄</el-button>
          <el-button type="primary" size="mini" plain icon="el-icon-zoom-in" @click="handleZoomIn">放大</el-button>
          <el-button type="primary" size="mini" plain icon="el-icon-zoom-out" @click="handleZoomOut">缩小</el-button>
        </div>
        <KemeHSC ref="kemehscRef" @getPhotoResult="handleGetPhoto"></KemeHSC>
        <div v-if="currentPhoto" style="margin-top: 10px;">
          <h4>照片：</h4>
          <el-image :src="currentPhoto.base64" :preview-src-list="[currentPhoto.base64]" style="width: 100px; height: 100px;">
            <div slot="placeholder" class="image-slot">
              加载中<span class="dot">...</span>
            </div>
          </el-image>
        </div>
      </div>
      <el-upload
        v-show="uploadMethod === 'file'"
        ref="upload"
        :auto-upload="false"
        :action="action"
        :limit="1"
        :headers="headers"
        style="width: 100%"
        list-type="picture"
        :on-success="handleSuccess"
        :before-upload="handleBeforeUpload"
        :on-error="handleUploadError"
      >
        <el-button size="mini" type="primary">点击上传</el-button>
        <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
      </el-upload>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button v-if="uploadMethod === 'file'" size="mini" type="primary" :loading="loading || submitLoading" @click="handleConfirm">确定</el-button>
        <el-button v-else size="mini" type="primary" :loading="loading || submitLoading" @click="handleConfirmCamera">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../util/mixins'
import constants from '../../../../util/constants'
import Cookies from 'js-cookie'
import KemeHSC from '../../../common/KemeHSC.vue'
export default {
  name: 'CameraDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {KemeHSC},
  props: {
    codes: String
  },
  mounted () {
    this.$once('hook:beforeDestroy', () => {
      if (this.isSocketConnect) {
        this.close()
      }
    })
  },
  data () {
    return {
      uploadMethod: 'camera',
      action: constants.JS_CONTEXT + '/order/upload_file',
      headers: {
        token: Cookies.get('token')
      },
      currentPhoto: null,
      loading: false,
      submitLoading: false
    }
  },
  methods: {
    handleOpen () {
      this.uploadMethod = 'camera'
      this.currentPhoto = null
      setTimeout(() => {
        this.$refs.upload.clearFiles()
        this.$refs.kemehscRef.init()
      })
    },
    handleClose () {
      this.visible = false
      this.currentPhoto = null
      this.$refs.kemehscRef.close()
      this.$emit('dialogCloseEvent')
    },
    handleTakePhoto () {
      this.$refs.kemehscRef.getImage()
    },
    handleZoomIn () {
      this.$refs.kemehscRef.zoomIn()
    },
    handleZoomOut () {
      this.$refs.kemehscRef.zoomOut()
    },
    handleGetPhoto (photo) {
      console.log(photo)
      this.currentPhoto = photo
    },
    // 上传前校验文件名和文件大小
    handleBeforeUpload (file) {
      let name = file.name
      if (!/\.(png|jpg|jpeg)$/i.test(name)) {
        this.$message.error('只能上传PNG、JPG、JPEG文件')
        return false
      }
      this.loading = true
      return true
    },
    handleSuccess (response) {
      this.loading = false
      if (response.code === this.SUCCESS_CODE) {
        this.submit(JSON.stringify(response.data))
      } else {
        this.$message.error(response.message)
      }
    },
    handleUploadError (e, file) {
      this.$message.error(`文件：${file.name}上传失败。`)
      this.loading = false
    },
    handleConfirm () {
      const files = this.$refs.upload.uploadFiles
      if (!files || files.length === 0) {
        this.$message.error('请选择文件')
        return
      }
      this.$refs.upload.submit()
    },
    // 拍摄照片
    handleConfirmCamera () {
      if (!this.currentPhoto) {
        this.$message.error('请先拍摄照片')
        return
      }
      this.submitPhoto()
    },
    submitPhoto () {
      this.loading = true
      this.$ajax({
        url: this.action,
        data: {
          file: this.currentPhoto.file
        },
        isFormData: true
      }).then(res => {
        if (res.success) {
          this.submit(JSON.stringify(res.data))
        }
      }).finally(() => {
        this.loading = false
      })
    },
    submit (filesString) {
      this.submitLoading = true
      this.$ajax({
        url: '/experiment/sign/photographic_record',
        data: {
          forderCodeString: this.codes,
          ffile: filesString
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('上传成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        }
      }).finally(() => {
        this.submitLoading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
