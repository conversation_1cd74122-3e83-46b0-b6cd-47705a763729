<template>
  <div style="width: 80%; min-width:1000px; margin: auto;padding: 10px 20px;">
    <nav class="operateBar bg-white">
      <div style="font-size: 13px;">
        <span class="title">订单审核</span>
        <span>{{pageType === 1 ? '（吉因加上机文库信息单）' : pageType === 2 ? '（组织核酸样本信息单）' : ''}}</span>
        <span v-if="orderCode" style="margin-left: 20px">订单编号：{{orderCode}}</span>
      </div>
      <div>
        <el-button size="mini" @click="handleClose">关闭</el-button>
        <template v-if="!readOnly && !editMode">
          <el-button size="mini" type="primary" @click="handleEdit">修改</el-button>
          <el-button size="mini" type="primary" @click="handleRevoke">驳回</el-button>
          <el-button size="mini" type="primary" @click="handlePass">通过</el-button>
        </template>
        <template v-if="editMode">
          <el-button size="mini" type="primary" @click="handleExitEdit">退出编辑</el-button>
          <el-button size="mini" type="primary" @click="handleSubmit">保存</el-button>
        </template>
      </div>
    </nav>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">项目信息</p>
        </div>
      </div>
      <div class="content">
        <el-form
          :model="projectInfo"
          :rules="rules"
          ref="projectInfoForm"
          :inline="true"
          :label-position="editMode ? 'top' : 'right'"
          label-width="auto"
          size="mini">
          <el-form-item label="项目编号" prop="projectCode">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.projectCode" v-if="projectInfo.projectCode" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.projectCode}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <div v-else  class="form-width" style="display: flex;">
              <el-input
                      v-model="projectInfo.projectCode"
                      readonly
                      class="form-width"
              ></el-input>
              <el-button size="mini" type="primary" style="flex-shrink: 0;" @click="projectDialogVisible = true">选择</el-button>
            </div>
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.projectName" v-if="projectInfo.projectName" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.projectName}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <template v-else>
              <el-tooltip :content="projectInfo.projectName" v-if="projectInfo.projectName" class="item" effect="dark" placement="top">
                <el-input :value="projectInfo.projectName" disabled class="form-width"></el-input>
              </el-tooltip>
              <el-input :value="projectInfo.projectName" v-else disabled class="form-width"></el-input>
            </template>
          </el-form-item>
          <el-form-item label="送检单位" prop="unit">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.unit" v-if="projectInfo.unit" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.unit}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <template v-else>
              <el-tooltip :content="projectInfo.unit" v-if="projectInfo.unit" class="item" effect="dark" placement="top">
                <el-input v-model="projectInfo.unit" disabled class="form-width"></el-input>
              </el-tooltip>
              <el-input v-model="projectInfo.unit" v-else disabled class="form-width"></el-input>
            </template>
          </el-form-item>
          <el-form-item label="客户名称" prop="customer">
            <!--<span v-if="!editMode">{{projectInfo.customer}}</span>-->
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.customer" v-if="projectInfo.customer" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.customer}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="projectInfo.customer" v-else maxlength="50" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="客户邮箱" prop="email">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.email" v-if="projectInfo.email" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.email}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <div v-else class="form-width" style="display: flex;">
              <el-input v-model="projectInfo.email" disabled class="form-width"></el-input>
              <el-button size="mini" type="primary" style="flex-shrink: 0;" @click="editEmailDialogVisible = true">编辑</el-button>
            </div>
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <!--<span v-if="!editMode">{{projectInfo.phone}}</span>-->
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.phone" v-if="projectInfo.phone" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.phone}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="projectInfo.phone" v-else  class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="销售联系人" prop="sale">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.sale" v-if="projectInfo.sale" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.sale}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="projectInfo.sale" v-else  class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="送检日期" prop="sendDate">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.sendDate" v-if="projectInfo.sendDate" class="item" effect="dark" placement="top">
                <p class="form-width p">{{projectInfo.sendDate}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-date-picker
              v-model="projectInfo.sendDate"
              v-else
              class="form-width"
              value-format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="送检人地址" prop="address">
            <template v-if="!editMode">
              <el-tooltip :content="projectInfo.address" v-if="projectInfo.address" class="item" effect="dark" placement="top">
                <p class="p form-width">{{projectInfo.address}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="projectInfo.address" v-else maxlenght="200" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="送检实验室" prop="area">
            <template v-if="!editMode">
              <el-tooltip :content="areaOptions[projectInfo.area]" v-if="areaOptions[projectInfo.area]" class="item" effect="dark" placement="top">
                <p class="form-width p">{{areaOptions[projectInfo.area]}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-select v-model="projectInfo.area" v-else class="form-width" placeholder="请选择">
              <el-option
                :key="k"
                :label="v"
                :value="k"
                v-for="(v, k) in areaOptions">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">快递信息</p>
        </div>
      </div>
      <div class="content">
        <el-form
          :model="courierInfo"
          :rules="rules"
          ref="courierInfo"
          :inline="true"
          :label-position="editMode ? 'top' : 'right'"
          label-width="auto"
          size="mini">
          <el-form-item label="快递公司" prop="courierCompany">
            <p v-if="!editMode" class="form-width p">{{courierInfo.courierCompany}}</p>
            <el-select v-model="courierInfo.courierCompany" v-else class="form-width" placeholder="请选择">
              <el-option
                :key="k"
                :label="v"
                :value="k"
                v-for="(v, k) in courierCompanyLists">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="courierInfo.courierCompany === '其他'" label="其他运输公司" prop="otherCourierCompany">
            <template v-if="!editMode">
              <el-tooltip :content="courierInfo.otherCourierCompany" v-if="courierInfo.otherCourierCompany" class="item" effect="dark" placement="top">
                <p class="p form-width">{{courierInfo.otherCourierCompany}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="courierInfo.otherCourierCompany" v-else maxlength="20" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="快递单号" prop="trackingNum">
            <!--<span v-if="!editMode">{{courierInfo.trackingNum}}</span>-->
            <template v-if="!editMode">
              <el-tooltip :content="courierInfo.trackingNum" v-if="courierInfo.trackingNum" class="item" effect="dark" placement="top">
                <p class="form-width p">{{courierInfo.trackingNum}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="courierInfo.trackingNum" v-else maxlength="50" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="运输方式" prop="transportMethod">
            <span v-if="!editMode">{{courierInfo.transportMethod || '-'}}</span>
            <el-select v-model="courierInfo.transportMethod" v-else class="form-width" placeholder="请选择">
              <el-option
                      :key="k"
                      :label="v"
                      :value="k"
                      v-for="(v, k) in transportMethodLists">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="courierInfo.transportMethod === '其他'" label="其他运输方式" prop="otherTransportMethod">
            <!--<span v-if="!editMode">{{courierInfo.otherTransportMethod}}</span>-->
            <template v-if="!editMode">
              <el-tooltip :content="courierInfo.otherTransportMethod" v-if="courierInfo.otherTransportMethod" class="item" effect="dark" placement="top">
                <p class="p form-width">{{courierInfo.otherTransportMethod}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </template>
            <el-input v-model="courierInfo.otherTransportMethod" v-else maxlength="20" class="form-width"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <computer-library-info
            ref="computerLibraryInfo"
            :read-only="readOnly"
            :order-id="orderId"
            :online-form="computerLibraryInfoForm"
            :page-type="pageType"
            :edit-mode="editMode"
            v-if="pageType === 1"/>
    <tissue-or-acid-info
            ref="tissueOrAcidInfo"
            :read-only="readOnly"
            :order-id="orderId"
            :online-form="tissueOrAcidInfoForm"
            :page-type="pageType"
            :edit-mode="editMode"
            v-if="pageType === 2"/>
    <!--<computer-library-info :read-only="readOnly" />-->
    <!--<tissue-or-acid-info v-if="pageType === 2" />-->
    <reject-dialog
      :pvisible.sync="rejectDialogVisible"
      @dialogConfirmEvent="handleRejectConfirm"/>
    <edit-email-dialog
            :pvisible.sync="editEmailDialogVisible"
            :input-email="projectInfo.email"
            @dialogConfirmEvent="handleEditEmailDialogConfirm"/>
    <project-dialog
            :pvisible.sync="projectDialogVisible"
            @dialogConfirmEvent="handleProjectDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import computerLibraryInfo from './entryComputerLibraryInfo'
import tissueOrAcidInfo from './entryTissueOrAcidInfo'
import rejectDialog from './rejectDialog'
import editEmailDialog from './entryBaseInfoEditEmailDialog'
import projectDialog from './entryBaseInfoProjectDialog'
export default {
  name: 'entryBaseInfo',
  components: {
    computerLibraryInfo,
    tissueOrAcidInfo,
    rejectDialog,
    editEmailDialog,
    projectDialog
  },
  mounted () {
    this.orderCode = this.$route.query.code || ''
    this.getAreaOptions()
    if (this.orderId) {
      this.getOrderDetail(this.orderId)
    }
  },
  watch: {
    $route: {
      handler: function (newVal) {
        console.log(11111)
        if (newVal.path.indexOf('orderLibraryDetail') > -1) {
          this.pageType = 1
        }
        if (newVal.path.indexOf('orderTissueDetail') > -1) {
          this.pageType = 2
        }
      },
      immediate: true
    },
    libraryOperatingData: {
      handler: function (newVal) {
        if (newVal) {
          this.readOnly = newVal.type === 2
          this.orderId = newVal.orderId
          this.editMode = newVal.editMode
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    libraryOperatingData () {
      return this.$store.getters.getValue('libraryOperatingData')
    }
  },
  data () {
    return {
      userNoticeDialogVisible: false,
      deliveryRequirementsDialogVisible: false,
      rejectDialogVisible: false,
      editEmailDialogVisible: false,
      projectDialogVisible: false,
      pageType: '',
      readOnly: true,
      editMode: false,
      orderId: '',
      orderCode: '', // 订单编号
      infoCopyDialogVisible: false,
      areaLoading: false,
      computerLibraryInfoForm: {}, // 上机文库的数据，获取订单详情的时候传到子组件
      tissueOrAcidInfoForm: {}, // 核酸检测的数据，获取订单详情时候传到子组件
      projectInfo: {
        projectCode: '',
        projectName: '',
        unit: '',
        customer: '',
        email: '',
        phone: '',
        sale: '',
        sendDate: '',
        address: '',
        area: ''
      },
      courierInfo: {
        courierCompany: '', // 快递公司
        otherCourierCompany: '',
        trackingNum: '', // 快递单号
        transportMethod: '', // 运输方式
        otherTransportMethod: '' // 运输方式
      },
      areaOptions: {}, // 片区列表
      courierCompanyLists: {
        // '中集冷云': '中集冷云',
        // '城市映急': '城市映急',
        '顺丰快递': '顺丰快递',
        '四八同城': '四八同城',
        '生生物流': '生生物流',
        '其他': '其他'
      },
      transportMethodLists: {
        '干冰': '干冰',
        '冰袋': '冰袋',
        '常温': '常温',
        '其他': '其他'
      },
      typesRefs: { // pageType对应的组件的refs，便于快速处理
        1: 'computerLibraryInfo',
        2: 'tissueOrAcidInfo'
      },
      rules: {
        projectCode: [
          {required: true, message: '项目编号必选', trigger: 'blur'}
        ],
        customer: [
          {required: true, message: '客户名称必填', trigger: 'blur'}
        ],
        email: [
          {required: true, message: '客户邮箱必填', trigger: 'blur'}
        ],
        phone: [
          {required: true, message: '联系电话必填', trigger: 'blur'}
        ],
        sale: [
          {required: true, message: '销售联系人必填', trigger: 'blur'}
        ],
        sendDate: [
          {required: true, message: '送样日期必选', trigger: 'blur'}
        ],
        address: [
          {required: true, message: '送检人地址必填', trigger: 'blur'}
        ],
        area: [
          {required: true, message: '送检实验室必填', trigger: 'change'}
        ],
        courierCompany: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        otherCourierCompany: [
          {required: true, message: '请输入其他快递方式', trigger: 'blur'}
        ],
        trackingNum: [
          {required: true, message: '请输入订单编号', trigger: 'blur'}
        ],
        transportMethod: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        otherTransportMethod: [
          {required: true, message: '请输入其他运输方式', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    // 获取生产日期
    getAreaOptions () {
      this.areaLoading = true
      this.$ajax({
        url: '/order/get_productionArea_list',
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.areaOptions = {}
          data.forEach(v => {
            this.$set(this.areaOptions, v.productionAreaCode, v.productionAreaName)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.areaLoading = false
      })
    },
    handleClose () {
      this.$confirm('确认关闭吗', '提示', {
        confirmButtonText: '确认关闭',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        window.close()
      })
    },
    // 获取订单详情
    getOrderDetail (orderId) {
      this.$ajax({
        url: '/order/get_order_detail',
        method: 'get',
        data: {
          orderId: orderId,
          type: this.libraryOperatingData.type
        },
        loadingDom: '.page'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let frontData = JSON.parse(res.data)
          this.projectInfo = frontData.projectInfo
          this.courierInfo = frontData.courierInfo
          if (this.pageType === 1) {
            this.computerLibraryInfoForm = frontData.detectInfo
          }
          if (this.pageType === 2) {
            this.tissueOrAcidInfoForm = frontData.detectInfo
          }
        } else {
          this.$confirm(res.message, '提示', {
            confirmButtonText: '重新加载',
            cancelButtonText: '关闭页面',
            type: 'error'
          }).then(() => {
            this.getOrderDetail(orderId)
          }).catch(() => {
            this.handleClose()
          })
        }
      })
    },
    handleEdit () {
      this.editMode = true
    },
    // 驳回
    handleRevoke () {
      this.rejectDialogVisible = true
    },
    // 退出编辑
    handleExitEdit () {
      this.$confirm('退出编辑后，您修改的内容是不会被保存的，确定退出编辑吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.editMode = false
        this.getOrderDetail(this.orderId)
      })
    },
    // 确认驳回
    handleRejectConfirm (r) {
      this.$ajax({
        url: '/order/reject_order',
        data: {
          orderId: this.orderId,
          rejectReason: r
        },
        loadingDom: 'body'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('驳回成功')
          setTimeout(() => {
            window.close()
          }, 1000)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 修改邮箱弹窗确认
    handleEditEmailDialogConfirm (emails) {
      this.projectInfo.email = emails
    },
    // 选择项目弹窗确认
    handleProjectDialogConfirm (project) {
      console.log(project)
      let d = project.realData
      this.projectInfo.projectCode = d.projectCode
      this.projectInfo.projectName = d.projectName
      this.projectInfo.unit = d.hospital
      this.projectInfo.customer = d.customer
      this.projectInfo.sale = d.sale
    },
    // 校验
    validForm () {
      let msg = '存在必填字段未填写，请检查'
      let validProjectInfo = new Promise((resolve, reject) => {
        this.$refs.projectInfoForm.validate(valid => {
          valid ? resolve() : reject(msg)
        })
      })
      let validCourierInfo = new Promise((resolve, reject) => {
        this.$refs.courierInfo.validate(valid => {
          valid ? resolve() : reject(msg)
        })
      })
      let typeRef = this.typesRefs[this.pageType]
      console.log(typeRef)
      if (typeRef) {
        return Promise.all([validProjectInfo, validCourierInfo, this.$refs[typeRef].validForm()])
      }
    },
    // 提交
    handleSubmit () {
      this.validForm().then(() => {
        let data = this.setData(status)
        this.submit(data)
      }).catch(() => {
        this.$message.error('存在必填字段未填写，请检查')
      })
    },
    // 通过审核
    handlePass () {
      this.$confirm('确认审核通过吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/order/pass_order',
          data: {
            orderId: this.libraryOperatingData.orderId
          },
          method: 'get',
          page: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            window.close()
            // this.$alert('审核已通过', '操作成功', {
            //   confirmButtonText: '关闭页面',
            //   callback: () => {
            //     window.close()
            //   }
            // })
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 提交
    submit (data) {
      this.$ajax({
        url: '/order/modify_order_info',
        data: data,
        loadingDom: '.page',
        loadingObject: {
          lock: true,
          text: '正在提交',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('提交成功')
          setTimeout(() => {
            window.close()
          }, 1000)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 设置提交的数据
    setData () {
      // 子组件的实例
      let com = this.$refs[this.typesRefs[this.pageType]]
      let p = this.projectInfo // 缩写，写的时候方便
      let c = this.courierInfo // 缩写，写的时候方便
      // 提交的数据
      let data = {}
      data.cosOrder = {
        orderId: this.orderId,
        projectCode: p.projectCode,
        salemanName: p.sale,
        customerName: p.customer,
        concatPhone: p.phone,
        sale: p.sale,
        sendUnit: p.unit,
        sendTime: p.sendDate,
        sendAddr: p.address,
        customerEmail: p.email,
        area: p.area,
        express: c.courierCompany,
        expressCode: c.trackingNum,
        isOtherTransportType: c.transportMethod === '其他' ? 1 : 0,
        transportType: c.transportMethod === '其他' ? c.otherTransportMethod : c.transportMethod,
        type: this.pageType
      }
      // 上机文库说明的字段
      if (this.pageType === 1) {
        let d = com.detectInfo
        // let s = com.sampleInfoTable // 样本表格
        // 前端数据存放
        data.cosOrder.frontData = JSON.stringify({
          projectInfo: p,
          courierInfo: c,
          detectInfo: d
        })
        data.cosOrderLib = {
          detectType: d.detectType,
          applicationType: d.applicationType,
          species: d.speciesInfo,
          libType: d.libraryType,
          libDetail: d.libraryDetail[1],
          libMethod: d.buildLibraryMethod,
          baseNumber: d.nucleobaseNum,
          isOtherLinkerSequence: d.linkerIndex === '其他' ? 1 : 0,
          linkerSequence: d.linkerIndex === '其他' ? d.linkerIndexOther : d.linkerIndex,
          buildingReagent: d.dataBox,
          isOtherSaveMedium: d.sampleStorageMedium === '其他' ? 1 : 0,
          saveMedium: d.sampleStorageMedium === '其他' ? d.sampleStorageMediumOther : d.sampleStorageMedium,
          isOtherIndex: d.indexType === '其他' ? 1 : 0,
          index: d.indexType === '其他' ? d.indexTypeOther : d.indexType,
          specialSequence: d.specialIndex === '有' ? d.specialIndexOther : d.specialIndex,
          isCyclizing: d.isCyclization,
          sequenceType: d.sequencingMode,
          instrumentType: d.instrumentType,
          isOtherDeliveryType: d.deliveryDataType === '其他' ? 1 : 0,
          deliveryType: d.deliveryDataType === '其他' ? d.deliveryDataTypeOther : d.deliveryDataType,
          isSplit: d.splitDemand,
          deliveryMethod: d.deliveryMethod,
          isAutoDetect: d.isAutoDetect
        }
        // 文库结构
        let libraryStructure = {}
        // 单index结构
        if (d.libraryStructure.single.img.path) {
          let {path, group} = d.libraryStructure.single.img
          libraryStructure.singleIndexPic = JSON.stringify([{path, group}])
        } else {
          libraryStructure = {
            singleIndex: d.libraryStructure.single.input[1],
            singleInsert: d.libraryStructure.single.input[3],
            singleIndex1: d.libraryStructure.single.input[0],
            singleIndex2: d.libraryStructure.single.input[2],
            singleIndex3: d.libraryStructure.single.input[4]
          }
        }
        // 双index结构
        if (d.libraryStructure.double.img.path) {
          let {path, group} = d.libraryStructure.double.img
          libraryStructure.doubleIndexPic = JSON.stringify([{path, group}])
        } else {
          libraryStructure = {
            ...libraryStructure,
            doubleIndex: d.libraryStructure.double.input[1],
            doubleInsert: d.libraryStructure.double.input[3],
            doubleIndex_2: d.libraryStructure.double.input[5],
            doubleIndex1: d.libraryStructure.double.input[0],
            doubleIndex2: d.libraryStructure.double.input[2],
            doubleIndex3: d.libraryStructure.double.input[4],
            doubleIndex4: d.libraryStructure.double.input[6]
          }
        }
        data.cosOrderLib = {
          ...data.cosOrderLib,
          ...libraryStructure
        }
      }
      // 组织核酸说明的字段
      if (this.pageType === 2) {
        let d = com.detectInfo
        // 前端数据
        data.cosOrder.frontData = JSON.stringify({
          projectInfo: p,
          courierInfo: c,
          detectInfo: d
        })
        data.cosOrderTissue = {
          detectType: d.detectType,
          isOtherSpecies: d.species === '其他' ? 1 : 0,
          species: d.species === '其他' ? d.speciesOther : d.species,
          sampleType: d.sampleType,
          tissueSampleType: d.tissueSampleType === '其他' ? d.tissueSampleTypeOther : d.tissueSampleType,
          isOtherTissueSampleType: d.tissueSampleType === '其他' ? 1 : 0,
          tissueSampleState: d.tissueSampleStatus,
          nucleicAcidSampleType: d.acidSampleType,
          isOtherNucleicAcidSampleState: d.acidSampleStatus === '其他' ? 1 : 0,
          nucleicAcidSampleState: d.acidSampleStatus === '其他' ? d.acidSampleStatusOther : d.acidSampleStatus,
          isReturnSample: d.needBackSample,
          returnSampleNote: d.backSampleNotes,
          isAbleToUsedOut: d.canRunOut === '其他' ? d.canRunOutOther : d.canRunOut,
          isOtherAbleToUsedOut: d.canRunOut === '其他' ? 1 : 0,
          libType: d.buildLibraryType[1],
          sequenceType: d.sequencingMode,
          instrumentType: d.instrumentType,
          spilt: d.splitDemand,
          deliverType: d.deliveryMethod,
          isAutoDetect: d.isAutoDetect
        }
      }
      return data
    }
  }
}
</script>

<style scoped lang="scss">
  .title{
    margin-right: 30px;
    font-size: 20px;
    font-weight: 600;
  }
  .form-width{
    width: 300px;
  }
  .p{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .module{
    background: #fff;
    margin: 20px 0;
    .module-title-bar{
      @extend .operateBar;
      height: 50px;
      .min-title{
        // @extend .title;
        font-size: 16px;
      }
    }
    .content{
      padding: 10px 20px;
    }
  }
</style>
