<template>
  <div class="wrapper">
    <!--    操作按钮组-->
    <div class="button-group">
      <el-button :loading="loadingCompare" v-if="!isPreview" type="primary"  size="mini" @click="handleDbCompare">数据库比对</el-button>
      <el-button :loading="loadingAuto" v-if="!isPreview" type="primary"  size="mini" @click="handleAutoGetData">自动获取数据</el-button>
      <el-button v-if="!isPreview" type="primary" size="mini"   @click="handleImportData">数据导入</el-button>
      <el-button :loading="loadingDownload" v-if="!isPreview" type="primary"  size="mini" @click="handleDownload">Excel下载</el-button>
<!--      <el-button type="primary" size="mini" :loading="loadingCreate" @click="handleCreateReport">生成在线报告</el-button>-->
    </div>
    <!--    内容区-->
    <div class="pathogens-list-wrapper">
      <el-tabs v-model="type"
               tab-position="left"
               style="height: 100%;width: 89px"
               @tab-click="handleChangeTabs">
        <el-tab-pane label="细菌" name="0"></el-tab-pane>
        <el-tab-pane label="病毒" name="20"></el-tab-pane>
        <el-tab-pane label="真菌" name="10"></el-tab-pane>
        <el-tab-pane label="寄生虫" name="40"></el-tab-pane>
        <el-tab-pane label="QC结果" name="qc"></el-tab-pane>
      </el-tabs>
      <div class="components-wrapper">
        <div v-if="type !== 'qc' && !isPreview" style="height: 42px">
          <el-dropdown style="margin-bottom: 10px;" @command="handleCommand">
            <el-button type="primary" size="mini">
              标记为<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="item.value" :key="index" v-for="(item, index) in reports">{{item.label}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>

        <el-table
          ref="table"
          :data="tableData"
          :cell-style="handleCheckCell"
          :height="'calc(100% - 32px - 42px)'"
          v-if="type !== 'qc'"
          border
          style="width: 100%"
          class="dataFilterTable"
          @select="handleSelect"
          @select-all="handleSelectAll"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="30"></el-table-column>
          <el-table-column prop="report" label="Report" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="confirmed" label="Confirmed" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesChinese" label="Species_Name_CN" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesLatin" label="Species_Name" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesTaxId" label="Species_TaxID" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="Type" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="genusLatin" label="Genus_Name" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="genusChinese" label="Genus_Name_CN" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rpmcr" label="RPMCR" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rrpmcr" label="rRPMCR" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="confidenceScore" label="Confidence_Score" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rtlConfScore" label="RTL_Conf_Score" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deplot" label="Deplot" width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text" @click="handleShowImg(scope.row)">链接</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="sg" label="SG" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="un" label="UN" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="uniqreadsRatio" label="UniqReads_ratio" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="genusReadsNumber" label="Genus_Reads_Number" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="genusRelativeAbundance" label="Genus_Relative_Abundance" width="220" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesReadsNumber" label="Species_Reads_Number" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesUniqKmerNum" label="Species_UniqKmerNum" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesReadsMapped" label="Species_Reads_Mapped" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesReadsUnique" label="Species_Reads_Unique" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesReadsNoovlp" label="Species_Reads_Noovlp" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesReadsMarker" label="Species_Reads_Marker" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesReadsDiscreteness" label="Species_Reads_Discreteness" width="240" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesRelativeAbundance" label="Species_Relative_Abundance" width="240" show-overflow-tooltip></el-table-column>
          <el-table-column prop="speciesAbsoluteAbundance" label="Species_Absolute Abundance" width="240" show-overflow-tooltip></el-table-column>
          <el-table-column prop="apeciesKmerDupFreq" label="Species_KmerDupFreq" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="depth" label="Depth" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="coverage" label="Coverage" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rpm" label="RPM" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="pathogenic" label="Pathogenic" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="conditioned" label="Conditioned" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="industrial" label="Industrial" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="colonization" label="Colonization" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="description" label="Description" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="infectionSite" label="Infection_Site" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="pathogenicityLevel" label="Pathogenicity_Level" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="baselineRatio" label="Baseline_range" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="frequence" label="Frequence" width="120" show-overflow-tooltip></el-table-column>
        </el-table>
        <div v-show="type === 'qc'" style="height: 100%" class="qc-table">
          <el-table
            ref="table"
            :data="qcList"
            v-if="type === 'qc'"
            border
            height="100%"
            style="width: 100%"
          >
            <el-table-column key="item" prop="label" label="item" show-overflow-tooltip></el-table-column>
            <el-table-column prop="value" label="value" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          v-if="type !== 'qc'"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
        <import-dialog :pvisible.sync="visible" @importDialogConfirmEvent="getData"></import-dialog>
        <img-dialog :pvisible.sync="imgVisible" :tax-id="taxId"></img-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import util from '../../../../../util/util'
import mixins from '../../../../../util/mixins'
import importDialog from './components/importDialog'
import ImgDialog from './components/imgDialog'

export default {
  name: 'pathogensList',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ImgDialog,
    importDialog
  },
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId') || this.$route.query.oxym
    },
    pathogenicType () {
      return this.$store.getters.getValue('pathogenicType')
    },
    isPreview () {
      return this.$store.getters.getValue('isPreview') || this.$route.query.isPreview
    }
  },
  data () {
    return {
      type: '0',
      visible: false,
      imgVisible: false,
      taxId: '',
      loadingCompare: false,
      loadingAuto: false,
      loadingDownload: false,
      loadingCreate: false,
      loadingCommand: false,
      selectedRows: new Map(),
      reports: [
        { label: 'Y：高可信度报出', value: 'Y' },
        { label: 'B：疑似背景菌', value: 'B' },
        { label: 'L：低可信度报出', value: 'L' },
        { label: 'N：不报出', value: 'N' }
      ],
      qcList: [
        { label: 'RawReads(M)', props: 'frawReadsM', value: '' },
        { label: 'RawBases(G)', props: 'frawBasesG', value: '' },
        { label: 'RawQ20(%)', props: 'frawQTwenty', value: '' },
        { label: 'RawQ30(%)', props: 'frawQThirty', value: '' },
        { label: 'RawGC(%)', props: 'frawGc', value: '' },
        { label: 'CleanReads(M)', props: 'fcleanReads', value: '' },
        { label: 'CleanBases(G)', props: 'fcleanBaseG', value: '' },
        { label: 'CleanQ20(%)', props: 'fcleanQTwenty', value: '' },
        { label: 'CleanQ30(%)', props: 'fcleanQThirty', value: '' },
        { label: 'CleanGC(%)', props: 'fcleanGc', value: '' },
        { label: 'Adapter(%)', props: 'fadapterRatio', value: '' },
        { label: 'HostReads(%)', props: 'fhostReadsRatio', value: '' },
        { label: 'NonHostReadsClassified(%)', props: 'fnonHostReadsClassifiedRatio', value: '' },
        { label: 'NonHostReadsUnClassified(%)', props: 'fnonHostReadsUnclassifiedRatio', value: '' },
        { label: 'Bacteria', props: 'fbacteria', value: '' },
        { label: 'Fungi', props: 'ffungi', value: '' },
        { label: 'Viruses', props: 'fviruses', value: '' },
        { label: 'Parasites', props: 'fparasites', value: '' }],
      tableData: []
    }
  },
  methods: {
    // 动态行样式
    handleCheckCell ({row}) {
      // console.log(row)
      const id = row.id
      let rows = [...this.selectedRows.values()]
      const isSelect = rows.some(v => v.id === id)
      return isSelect ? 'background: #ecf6ff' : ''
    },
    // 显示图片
    handleShowImg (row) {
      this.taxId = row.speciesTaxId
      this.imgVisible = true
    },
    handleChangeTabs () {
      this.type === 'qc' ? this.getQcResultList() : this.getData()
    },
    async getQcResultList () {
      let {code, data = []} = await this.$ajax({
        url: '/read/pathogen/get_pathogen_excel_qc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get',
        loadingDom: '.qc-table'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        this.qcList = this.qcList || []
        this.qcList = this.qcList.map(v => {
          v.value = data[v.props]
          v.realData = JSON.parse(JSON.stringify(v))
          util.setDefaultEmptyValueForObject(v)
          return v
        })
      }
    },
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/read/pathogen/get_pathogen_data_list',
        data: {
          type: this.type,
          analysisId: this.analysisRsId,
          current: this.currentPage,
          size: this.pageSize
        },
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.tableData = []
        this.totalPage = data.total
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            deplot: v.fdeplot,
            analysisId: v.fanalysisId,
            confirmed: v.fconfirmed,
            check: v.fcheck,
            speciesTaxId: v.fspeciesTaxId,
            genusLatin: v.fgenusLatin,
            genusChinese: v.fgenusChinese,
            genusReadsNumber: v.fgenusReadsNumber,
            genusRelativeAbundance: v.fgenusRelativeAbundance,
            report: v.freport,
            speciesChinese: v.fspeciesChinese,
            speciesLatin: v.fspeciesLatin,
            speciesReadsNumber: v.fspeciesReadsNumber,
            speciesRelativeAbundance: v.fspeciesRelativeAbundance,
            speciesUniqKmerNum: v.fspeciesUniqKmerNum,
            apeciesKmerDupFreq: v.fapeciesKmerDupFreq,
            coverage: v.fcoverage,
            pathogenic: v.fpathogenic,
            conditioned: v.fconditioned,
            industrial: v.findustrial,
            colonization: v.fcolonization,
            pathogenTypeNum: v.fpathogenTypeNum,
            type: v.ftype,
            pathogenicType: v.fpathogenicType,
            colonizePart: v.fcolonizePart,
            speciesDescription: v.fspeciesDescription,
            drugReference: v.fdrugReference,
            drugTolerance: v.fdrugTolerance,
            virulenceFactor: v.fvirulenceFactor,
            rpm: v.frpm,
            speciesReadsMapped: v.fspeciesReadsMapped,
            speciesReadsUnique: v.fspeciesReadsUnique,
            speciesReadsNoovlp: v.fspeciesReadsNoovlp,
            speciesReadsMarker: v.fspeciesReadsMarker,
            speciesReadsDiscreteness: v.fspeciesReadsDiscreteness,
            confidenceScore: v.fconfidenceScore,
            rtlConfScore: v.frtlConfScore,
            speciesReadsNumberUniq: v.fspeciesReadsNumberUniq,
            speciesAbsoluteAbundance: v.fspeciesAbsoluteAbundance,
            rpmcr: v.frpmcr,
            allspeciesRatio: v.fallspeciesRatio,
            uniqreadsRatio: v.funiqreadsRatio,
            ntcRatio: v.fntcRatio,
            baselineRatio: v.fbaselineRatio,
            frequence: v.ffrequence,
            depthOne: v.fdepthOne,
            depthOneCov: v.fdepthOneCov,
            depthtwo: v.fdepthtwo,
            depthtwoCov: v.fdepthtwoCov,
            depthThree: v.fdepthThree,
            depthThreeCov: v.fdepthThreeCov,
            depthFour: v.fdepthFour,
            depthFourCov: v.fdepthFourCov,
            depthGeFive: v.fdepthGeFive,
            depthGeFiveCov: v.fdepthGeFiveCov,
            typeOriginal: v.ftypeOriginal,
            description: v.fdescription,
            infectionSite: v.finfectionSite,
            pathogenicityLevel: v.fpathogenicityLevel,
            depth: v.fdepth,
            rrpmcr: v.frrpmcr,
            sg: v.fsg,
            un: v.fun
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 数据库比对
    async handleDbCompare () {
      this.loadingCompare = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/db_compare',
          data: {
            analysisRsId: this.analysisRsId,
            version: this.pathogenicType
          },
          method: 'get'
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('比对成功')
          await this.getData()
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingCompare = false
      }
    },
    // 自动获取数据
    async handleAutoGetData () {
      this.loadingAuto = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/auto_get_read_excel',
          data: {
            analysisRsId: this.analysisRsId,
            version: this.pathogenicType
          },
          method: 'get'
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('获取数据成功')
          await this.getData()
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingAuto = false
      }
    },
    // 数据导入
    handleImportData () {
      this.visible = true
    },
    // 下载结果文件
    async handleDownload () {
      this.loadingDownload = true
      try {
        let res = await this.$ajax({
          url: '/read/pathogen/download_excel_result',
          data: {
            analysisRsId: this.analysisRsId
          },
          responseType: 'blob',
          method: 'get'
        })
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      } finally {
        this.loadingDownload = false
      }
    },
    // 生成在线报告
    async handleCreateReport () {
      this.loadingCreate = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/build_online_report',
          data: {
            analysisRsId: this.analysisRsId
          },
          method: 'get'
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('生成在线报告成功')
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingCreate = false
      }
    },
    async handleCommand (command) {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条数据标记')
        return
      }
      this.loadingCommand = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/sign_report',
          data: {
            ids: [...this.selectedRows.values()].map(v => v.id).join(','),
            report: command
          }
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('标记成功')
          await this.getData()
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingCommand = false
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  .button-group {
    padding: 20px 10px;
  }
  .pathogens-list-wrapper {
    display: flex;
    height: calc(100vh - 232px);
    width: 100%;
    background: #fff;
    .components-wrapper {
      padding: 10px 20px;
      width: calc(100% - 90px);
    }
  }
}
</style>
