<template>
  <div style="height: 100%;">
    <!--搜索表单-->
    <div class="search">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini" style="display: flex;justify-content: space-between" @keyup.enter.native="handleSearch">
        <div>
          <el-form-item label="产品分类" prop="typeName">
            <el-input v-model.trim="form.typeName" clearable placeholder="请输入产品分类"></el-input>
          </el-form-item>
          <el-form-item label="产品编号" prop="productCode">
            <el-input v-model.trim="form.productCode" clearable placeholder="请输入产品编号"></el-input>
          </el-form-item>
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model.trim="form.productName" clearable placeholder="请输入产品名称"></el-input>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--表格容器-->
    <div class="product-table-wrapper">
      <!--产品分类-->
      <div style="width:300px;height:100%">
        <div class="table-name">产品分类</div>
        <el-table
          ref="tableCategory"
          :data="productCategory"
          size="mini"
          height="calc(100% - 40px)"
          border
          style="width:100%"
          @select="handleCategorySelect"
          @row-click="handleCategoryRowClick"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="typeCode" label="分类编号" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="typeName" label="分类名称" width="160" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <!--产品列表-->
      <div style="min-width:calc(100% - 300px);height: 100%;margin-left: 10px">
        <div class="table-name">产品列表</div>
        <el-table
          ref="tableList"
          :data="tableData"
          size="mini"
          height="calc(100% - 40px - 32px)"
          border
          style="width:100%">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="productCode" label="产品编号" min-width="120">
            <template slot-scope="scope">
              <div style="color: deepskyblue;" @click="handleShowDetail(scope.row.fotherProductId)">{{scope.row.productCode}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="产品名称" show-overflow-tooltip min-width="120">
             <template slot-scope="scope">
              <div style="color: deepskyblue;" @click="handleShowDetail(scope.row.fotherProductId)">{{scope.row.productName}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="shortname" label="产品简称" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productType" label="产品分类" min-width="120"></el-table-column>
          <el-table-column prop="isComboType" label="产品类型" min-width="120"></el-table-column>
          <el-table-column prop="state" label="状态" min-width="120"></el-table-column>
          <el-table-column prop="creator" label="创建人" min-width="120"></el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160"></el-table-column>
          <el-table-column prop="modifier" label="修改人" min-width="120"></el-table-column>
          <el-table-column prop="updateTime" label="修改时间" min-width="160"></el-table-column>
          <el-table-column label="操作" min-width="60" fixed="right">
            <template slot-scope="scope">
              <el-popover
                :trigger="($setAuthority('015002001', 'buttons')|| $setAuthority('015002002', 'buttons') || $setAuthority('015002003', 'buttons'))? 'click' : ''"
                placement="bottom-start"
                width="100"
              >
                <div v-if="$setAuthority('015002001', 'buttons')" class="options" @click="handleOpenConfigExperimental(scope.row)">实验配置</div>
                <div v-if="$setAuthority('015002002', 'buttons')" class="options" @click="handleOpenInterpretConfig(scope.row)">解读配置</div>
                <div v-if="$setAuthority('015002003', 'buttons')" class="options" @click="handleCustomerProjectConfig(scope.row)">客户项目配置</div>
                <div slot="reference" class="options">
                  <icon-svg style="font-size: 16px" icon-class="icon-gengduo"></icon-svg>
                </div>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <!--      @dialogConfirmEvent="handleEditModuleDialogConfirm"-->
    <product-experimental-config-dialog
      :is-sample="isSample"
      :product-id="productId"
      :pvisible.sync="configExperimentalDialogInfo.visible"
      :title="configExperimentalDialogInfo.title"
      @dialogConfirmEvent='handleExperimentalConfigDialog'
    />
    <product-interpret-config-dialog
      :is-sample="isSample"
      :product-id="productId"
      :pvisible.sync="interpretConfigDialogInfo.visible"
      :title="interpretConfigDialogInfo.title"
      @dialogConfirmEvent='handleInterpretConfigDialog'
    />
    <product-customer-project-dialog
      :pvisible.sync="customerProjectDialogInfo.visible"
      :product-id="productId"
      :title="customerProjectDialogInfo.title"
      @dialogConfirmEvent='handleCustomerProjectDialog'
    />
    <sinkDrawer
      :pvisible.sync="sinkVisible"
      :fid="fid"
      @dialogCloseEvent='handleClose'
    ></sinkDrawer>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util from '@/util/util'
import sinkDrawer from './sinkProductDrawer.vue'
import productExperimentalConfigDialog from './productExperimentalConfigDialog'
import productInterpretConfigDialog from './productInterpretConfigDialog'
import productCustomerProjectDialog from './productCustomerProjectDialog'

export default {
  name: 'sinkMarketProductManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    sinkDrawer,
    productExperimentalConfigDialog,
    productInterpretConfigDialog,
    productCustomerProjectDialog
  },
  mounted () {
    // 获取产品分类
    this.getProductCategory()
    this.handleSearch()
  },
  data () {
    return {
      typeId: '',
      fid: null,
      isSample: false,
      selectedCategoryRows: new Map(),
      productCategory: [], // 产品分类列表
      form: { // 表单数据
        typeId: null,
        typeName: '',
        productCode: '',
        productName: '',
        otherProduct: '下沉市场产品'
      },
      stateText: { // 产品状态
        '0': '已启用',
        '1': '已禁用'
      },
      submitForm: { // 产品列表接口请求参数
        typeId: null,
        typeName: '',
        productCode: '',
        productName: '',
        otherProduct: '下沉市场产品'
      },
      productId: 0,
      sinkVisible: false,
      configExperimentalDialogInfo: {
        title: '实验配置',
        visible: false
      },
      interpretConfigDialogInfo: {
        title: '解读配置',
        visible: false
      },
      customerProjectDialogInfo: {
        title: '客户项目配置',
        visible: false
      }
    }
  },
  methods: {
    handleShowDetail (value) {
      this.sinkVisible = true
      this.fid = value
    },
    handleClose () {
      this.sinkVisible = false
    },
    // 按条件搜索
    handleSearch () {
      this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.typeId = ''
      this.selectedCategoryRows.clear()
      this.$refs.tableCategory.clearSelection()
      this.currentPage = 1
      this.getData()
    },
    // 重置搜索
    handleReset () {
      this.$nextTick(() => {
        this.typeId = ''
        this.selectedCategoryRows.clear()
        this.$refs.tableCategory.clearSelection()
        this.$refs.form.resetFields()
        this.handleSearch()
      })
    },
    // 获取产品分类
    async getProductCategory () {
      let result = await this.$ajax({
        url: '/system/otherProduct/get_other_product_types',
        method: 'get',
        data: {
          comeFrom: '下沉市场产品分类'
        }
      })
      if (result.code === this.SUCCESS_CODE) {
        let data = result.data || []
        this.tableData = []
        let item = {}
        data.forEach(v => {
          item = {
            typeId: v.typeId,
            typeCode: v.typeCode,
            typeName: v.typeName
          }
          util.setDefaultEmptyValueForObject(item)
          this.productCategory.push(item)
        })
      } else {
        this.$message.error(result.message)
      }
    },
    // 获取产品列表数据
    getData () {
      // this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.submitForm.typeId = this.typeId
      this.submitForm.page = this.currentPage
      this.submitForm.rows = this.pageSize
      this.$ajax({
        url: '/system/product/get_product_list',
        method: 'post',
        data: this.submitForm
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          this.totalPage = data.total
          let item = {}
          data.records.forEach(v => {
            item = {
              productId: v.productId,
              productCode: v.productCode,
              productName: v.productName,
              shortname: v.shortname,
              productType: this.filterTypeName(v.productType),
              state: this.stateText[v.state],
              isComboType: v.isComboType,
              creator: v.creator,
              createTime: v.createTime,
              modifier: v.modifier,
              updateTime: v.updateTime,
              fotherProductId: v.fotherProductId
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 点击行
    handleCategoryRowClick (row) {
      this.handleCategorySelect(undefined, row)
    },
    // 选中行
    handleCategorySelect (selection, row) {
      if (!this.selectedCategoryRows.has(row.typeId)) {
        this.$refs.tableCategory.clearSelection()
        this.selectedCategoryRows.clear()
      }
      this.$refs.tableCategory.toggleRowSelection(row, !this.selectedCategoryRows.has(row.typeId))
      if (this.selectedCategoryRows.has(row.typeId)) {
        this.selectedCategoryRows.delete(row.typeId)
      } else {
        this.selectedCategoryRows.set(row.typeId, row)
        this.currentPage = 1
        this.submitForm = {}
        this.typeId = row.typeId
        this.getData()
      }
    },
    // 开启实验配置弹窗
    handleOpenConfigExperimental (row) {
      this.isSample = (row.isComboType.trim() === '单产品')
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.configExperimentalDialogInfo.visible = true
    },
    // 实验配置确认
    handleExperimentalConfigDialog () {
      this.getData()
    },
    // 打开客户项目配置
    handleOpenInterpretConfig (row) {
      this.isSample = (row.isComboType.trim() === '单产品')
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.interpretConfigDialogInfo.visible = true
    },
    // 解读客户项目确认
    handleInterpretConfigDialog () {
      this.getData()
    },
    // 打开解读配置
    handleCustomerProjectConfig (row) {
      console.log(row)
      this.isSample = (row.isComboType.trim() === '单产品')
      row.productId === '-' ? this.productId = 0 : this.productId = row.productId
      this.customerProjectDialogInfo.visible = true
    },
    // 解读配置确认
    handleCustomerProjectDialog () {
      this.getData()
    },
    // 根据分类编号获取分类名 部分数据不对
    filterTypeName (typeCode) {
      try {
        return this.productCategory.find((item) => item.typeId === typeCode).typeName
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.search {
  height: 48px;
}

/deep/ .el-table__header .el-checkbox {
  display: none;
}

.search > > > .el-form-item--mini {
  margin-bottom: 10px;
  margin-top: 10px;
}

.table-style {
  .el-table-column--selection .el-checkbox {
    display: none !important;
    background: #409EFF;
  }
}

.el-input {
  width: 130px;
}

.product-table-wrapper {
  display: flex;
  width: 100%;
  height: calc(100% - 48px - 32px);
  margin-top: 22px;

  .table-name {
    display: flex;
    height: 40px;
    border: 1px solid #EBEEF5;
    border-bottom: none;
    align-items: center;
    padding-left: 10px;
  }
}

.options {
  color: #409EFF;
  //margin: 5px 3px;
  cursor: pointer;
}

</style>
