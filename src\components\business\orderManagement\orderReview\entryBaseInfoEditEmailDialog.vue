<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="邮箱"
      width="400px"
      @open="handleOpen">
      <div>
        <p class="label-item">邮箱</p>
        <div style="max-height: 350px;">
          <div :key="index" v-for="(item, index) in emails" style="display: flex;margin-bottom: 20px;">
            <el-input v-model.trim="emails[index]" clearable size="mini" style="width: 250px"></el-input>
            <el-button v-if="emails.length > 1" size="mini" style="margin-left: 20px" @click="handleDel(index)">删除</el-button>
          </div>
        </div>
        <el-button v-if="emails.length <= 5" size="mini" type="primary" @click="handleAdd">添加</el-button>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
export default {
  name: 'entryBaseInfoEditEmailDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    inputEmail: String
  },
  data () {
    return {
      emails: ['']
    }
  },
  methods: {
    handleOpen () {
      this.emails = this.inputEmail ? this.inputEmail.split(';') : ['']
    },
    handleAdd () {
      this.emails.push('')
    },
    handleDel (index) {
      this.emails.splice(index, 1)
    },
    handleConfirm () {
      let emails = this.emails.filter(v => v).join(';')
      this.$emit('dialogConfirmEvent', emails)
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
.label-item{
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}
</style>
