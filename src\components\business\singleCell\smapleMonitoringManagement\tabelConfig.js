// 定义一个常量，用于复用render属性中相同的配置
// const inputRenderConfig = {name: '$input', props: {clearable: true}, events: {input: 'handleSaveNote'}}

let idCounter = 1 // 新增全局变量用于追踪递增的ID
// 定义一个函数，用于生成具有共同属性的配置对象，提高代码的可维护性
function createCommonConfig ({
  field,
  title,
  renderConfig = null,
  formaterFN = null,
  width = 140,
  isCustomerField = true,
  fixed = '',
  isShow = true
} = {}) {
  if (!field || !title) {
    throw new Error('field 和 title 是必填项')
  }

  return {
    id: idCounter++,
    title,
    field,
    apiField: FIELD_MAPPING[field] || field,
    showOverflow: true,
    width,
    ...(renderConfig && { render: renderConfig }),
    ...(formaterFN && { formater: formaterFN }),
    ...(fixed && { fixed }),
    isCustomerField: true,
    isShow: isShow
  }
}

// 初始化idCounter，确保每次运行或刷新页面时ID序列重新开始
idCounter = 1

// 创建列配置常量对象，便于管理固定列
const FIXED_COLUMNS = {
  LEFT: 'left',
  RIGHT: 'right'
}

// 创建列宽常量对象，便于统一管理
const COLUMN_WIDTHS = {
  SMALL: 80,
  MEDIUM: 100,
  LARGE: 120,
  XLARGE: 200,
  XXLARGE: 300
}

// 添加前后端字段映射关系
export const FIELD_MAPPING = {
  deliverStatus: 'fdeliverStatus', // 交付状态
  warningFlag: 'fexceptionRemark', // 异常标记
  qcStatus: 'fqualityAnalysisStatus', // 质控分析状态
  sampleOriginalName: 'fcosSampleName', // 样本原始名称
  productionNo: 'fproductionNumber', // 生产序号
  geneCode: 'fgeneNum', // 吉因加编号
  libraryCode: 'flibNums', // 文库编号
  expectedCellCount: 'fexpectedCellsNumber', // 期望捕获细胞数
  productName: 'fproductName', // 产品名称
  experimentalLink: 'fexperimentalLink', // 实验环节
  openStatus: 'fisStaging', // 开启状态
  isBackup: 'fisBackUp', // 是否备份
  isDoorExperiment: 'fisSiteExperiment', // 是否上门实验
  deliveryMethod: 'fdeliverMethod', // 下单交付方式
  subOrderCode: 'fcosSubCode', // 子订单编号
  projectCode: 'fprojectCode', // 项目编号
  projectName: 'fprojectName', // 项目名称
  sampleArrivalDate: 'fconfirmTime', // 到样日期
  bioinformaticsDeliveryTime: 'fdeliverTime', // 生信交付时间
  isDeliveryStandard: 'fisQualityAnalysis', // 是否达到交付标准
  isSequencing: 'fisEmbarkation', // 是否上机
  testConclusion: 'ftestSummary', // 检测结论
  cellInput: 'fcellInput', // 细胞/核投入量
  captureRateAuto: 'fexpectedCellsRate', // 捕获率自动核算
  isQcStandard: 'fisQualityStandard', // 是否达到质控标准
  customerOrderDataTotal: 'fallOrderDataSize', // 客户下单累计数据量
  experimentalLibraryDataTotal: 'fallExpeirmentLibDataSize', // 实验文库累积下机数据
  oligoLibraryDataTotal: 'fallOligoLibDataSize', // oligo文库累积下机数据
  experimentalLibraryDataDiff: 'fexperimentLibDifference', // 实验文库数据量差额
  oligoLibraryDataDiff: 'foligoLibDifference', // oligo文库数据量差额
  remark: 'fremark', // 备注
  confirmException: 'fconfirmException',
  cosExceptionRemark: 'fcosExceptionRemark',
  fsampleAnomaly: 'fsampleAnomaly',
  fisSampleAnomaly: 'fisSampleAnomaly'
}

/**
 * 导出一个配置数组，用于表格设置。该数组包含多个配置对象，每个对象代表表格中的一列。
 * 这种方式有助于统一管理和创建表格列配置，提高代码的可维护性和可读性。
 *
 * @returns {Object[]} 返回一个配置对象数组，每个对象包含列的配置信息。
 */
export const tableConfig = [
  // 状态相关列
  createCommonConfig({ field: 'deliverStatus', title: '交付状态', isCustomerField: false, width: COLUMN_WIDTHS.SMALL, fixed: FIXED_COLUMNS.LEFT }),
  createCommonConfig({ field: 'warningFlag', title: '异常状态', isCustomerField: false, width: COLUMN_WIDTHS.MEDIUM, fixed: FIXED_COLUMNS.LEFT }),
  createCommonConfig({ field: 'qcStatus', title: '质控分析状态', isCustomerField: false, width: COLUMN_WIDTHS.MEDIUM, fixed: FIXED_COLUMNS.LEFT }),

  // 基本信息列
  createCommonConfig({ field: 'sampleOriginalName', title: '样本原始名称', isCustomerField: false, width: COLUMN_WIDTHS.MEDIUM, fixed: FIXED_COLUMNS.LEFT }),
  createCommonConfig({ field: 'productionNo', title: '生产序号', isCustomerField: false, width: COLUMN_WIDTHS.MEDIUM, fixed: FIXED_COLUMNS.LEFT }),
  createCommonConfig({ field: 'geneCode', title: '吉因加编号', isCustomerField: false, width: COLUMN_WIDTHS.LARGE }),

  // 文库编号
  createCommonConfig({ field: 'libraryCode', title: '文库编号', width: COLUMN_WIDTHS.LARGE }),
  createCommonConfig({ field: 'confirmException', title: '到样异常', width: COLUMN_WIDTHS.LARGE }),
  createCommonConfig({ field: 'cosExceptionRemark', title: '异常描述', width: COLUMN_WIDTHS.LARGE }),
  // 期望捕获细胞数(合同签订)
  createCommonConfig({ field: 'expectedCellCount', title: '期望捕获细胞数(合同签订)', width: COLUMN_WIDTHS.XLARGE }),
  // 产品名称
  createCommonConfig({ field: 'productName', title: '产品名称' }),
  // 实验环节
  createCommonConfig({ field: 'experimentalLink', title: '实验环节' }),
  // 开启状态
  createCommonConfig({ field: 'openStatus', title: '开启状态' }),
  // 是否备份
  createCommonConfig({ field: 'isBackup', title: '是否备份' }),
  // 是否上门实验
  createCommonConfig({ field: 'isDoorExperiment', title: '是否上门实验' }),
  // 下单交付方式
  createCommonConfig({ field: 'deliveryMethod', title: '下单交付方式' }),
  // 子订单编号
  createCommonConfig({ field: 'subOrderCode', title: '子订单编号' }),
  // 项目编号
  createCommonConfig({ field: 'projectCode', title: '项目编号' }),
  // 项目名称
  createCommonConfig({ field: 'projectName', title: '项目名称' }),
  // 到样日期
  createCommonConfig({ field: 'sampleArrivalDate', title: '到样日期' }),
  // 生信交付时间
  createCommonConfig({ field: 'bioinformaticsDeliveryTime', title: '生信交付时间' }),
  // 是否达到交付标准
  createCommonConfig({ field: 'isDeliveryStandard', title: '是否达到交付标准' }),
  // 是否上机
  createCommonConfig({ field: 'isSequencing', title: '是否上机' }),
  // 检测结论
  createCommonConfig({ field: 'testConclusion', title: '检测结论' }),
  // 细胞/核投入量
  createCommonConfig({ field: 'cellInput', title: '细胞/核投入量' }),
  // 捕获率自动核算
  createCommonConfig({ field: 'captureRateAuto', title: '捕获率自动核算' }),
  // 是否达到质控标准
  createCommonConfig({ field: 'isQcStandard', title: '是否达到质控标准' }),
  // 客户下单累计数据量/M
  createCommonConfig({ field: 'customerOrderDataTotal', title: '客户下单累计数据量/M', width: COLUMN_WIDTHS.XLARGE }),
  // 实验文库累积下机数据/M
  createCommonConfig({ field: 'experimentalLibraryDataTotal', title: '累积下机数据/M', width: COLUMN_WIDTHS.XLARGE }),
  // 生信指标
  createCommonConfig({ field: 'festimatedNumberOfCells', title: 'estimated number of cells', isCustomerField: false, width: COLUMN_WIDTHS.XLARGE }),
  createCommonConfig({ field: 'fmedianGenesPerCell', title: 'median genes per cell', isCustomerField: false, width: COLUMN_WIDTHS.XLARGE }),
  createCommonConfig({ field: 'freadsMappedToGenome', title: 'Reads mapped to genome', isCustomerField: false, width: COLUMN_WIDTHS.XLARGE }),
  createCommonConfig({ field: 'fcdnaNumberOfReads', title: 'cdna number of reads', isCustomerField: false, width: COLUMN_WIDTHS.XLARGE }),
  createCommonConfig({ field: 'fmedianFractionOfPeaks', title: 'Median fraction of fragments overlapping peaks', isCustomerField: false, width: COLUMN_WIDTHS.XXLARGE }),
  createCommonConfig({ field: 'fmedianFractionOfTss', title: 'Median fraction of fragments overlapping TSS', isCustomerField: false, width: COLUMN_WIDTHS.XXLARGE }),
  createCommonConfig({ field: 'ftotalNumberOfReadsPairs', title: 'Total number of reads pairs', isCustomerField: false, width: COLUMN_WIDTHS.XLARGE }),
  createCommonConfig({ field: 'fnumberOfSpanningPairs', title: 'Number of Cells With Productive V-J Spanning Pair', isCustomerField: false, width: 320 }),
  createCommonConfig({ field: 'freadsMappedToAnyGene', title: 'Reads Mapped to Any V(D)J Gene', isCustomerField: false, width: COLUMN_WIDTHS.XXLARGE }),
  createCommonConfig({ field: 'fnumberOfReadPairs', title: 'Number of Read Pairs', isCustomerField: false, width: COLUMN_WIDTHS.XLARGE }),

  // 样本异常描述、是否异常
  createCommonConfig({ field: 'fsampleAnomaly', title: '样本异常描述' }),
  createCommonConfig({ field: 'fisSampleAnomaly', title: '是否异常' }),

  // 操作栏
  createCommonConfig({ field: 'operation', title: '操作栏' })
]
