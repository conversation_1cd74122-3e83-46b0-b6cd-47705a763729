// 导入工具方法，用于设置对象的默认空值
import {setDefaultEmptyValueForObject, deepCopy} from '../../../../util/util'
/**
 * 布尔值配置
 * @type {{0: string, 1: string}}
 */
export const booleanOptions = {
  0: '否',
  1: '是'
}
export const exceptionTypeList = [
  { label: '无', value: '5' },
  { label: '暂停', value: '0' },
  { label: '终止-建库前', value: '3' },
  { label: '终止-建库后', value: '4' }
]

export const productStatusConfig = {
  0: '解离完成',
  1: '建库完成',
  2: '已确认上机'
}
/**
 * 格式化输入的数据数组，为每个项目添加额外的属性和处理默认值。
 * @param {Array} data - 原始数据数组，每个元素代表一个项目。
 * @returns {Array} - 格式化后的数据数组，每个项目都包含了更多的属性和处理了默认值。
 */
export const dataFormating = (data = []) => {
  // 遍历数据数组，对每个项目进行处理
  return data.map(v => {
    // 创建一个新的对象，包含项目的原始属性
    const item = {
      fid: v.fnewLibId,
      fisEmbarkationText: booleanOptions[v.fisEmbarkation] || '-',
      fisAddTestText: v.fisAddTest || '-',
      fisQualityText: booleanOptions[v.fisQuality] || '-',
      fproductionStatusText: productStatusConfig[v.fproductionStatus] || '-',
      warningFlagText: ['暂停', '终止-解离前', '终止-解离后', '终止-建库前', '终止-建库后'][v.fexceptionRemark] || '',
      ...v
    }
    // 为项目创建一个深拷贝的原始数据对象，用于保留项目的初始状态
    // 创建该项目的深拷贝，用于保留原始数据不变
    item.realData = deepCopy(item)
    // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
    // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
    setDefaultEmptyValueForObject(item)
    return item
  })
}
