<template>
  <div>
    <div>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleGetReferences">自动获取文献</el-button>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleEditReferences(0)">新增文献</el-button>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleEditReferences(1)">修改文献</el-button>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleSelectReferences">选择文献</el-button>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleDeleteReferences">移除</el-button>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleMove(1)">上移</el-button>
      <el-button type="primary" style="margin-top: 10px" size="mini" @click="handleMove(0)">下移</el-button>
    </div>
    <el-table
          ref="table"
          :data="tableData"
          size="mini"
          class="referencesTable"
          highlight-current-row
          @current-change="handleCurrentChange">
          <el-table-column type="index" label="#" width="45px"></el-table-column>
          <el-table-column prop="rIdNameCombo" label="文献名称"></el-table-column>
        </el-table>
    <save-references-dialog
      :pvisible="saveReferencesDialogVisible"
      :pdata="saveReferencesDialogData"
      @saveReferencesDialogCloseEvent="handleSaveReferencesDialogClose"
      @saveReferencesDialogConfirmEvent="handleSaveReferencesDialogConfirm"
    ></save-references-dialog>
    <search-references-dialog
      :pvisible="searchReferencesDialogVisible"
      @searchReferencesDialogCloseEvent="handleSearchReferencesDialogClose"
      @searchReferencesDialogConfirmEvent="handleSearchReferencesDialogConfirm"
    ></search-references-dialog>
  </div>
</template>

<script>
import saveReferencesDialog from './saveReferencesDialog'
import searchReferencesDialog from './searchReferencesDialog'
export default {
  components: {
    saveReferencesDialog,
    searchReferencesDialog
  },
  props: {
    rids: {
      type: [String, Number]
    }
  },
  mounted () {
    this.tableData = []
    // this.handleGetReferences()
  },
  watch: {
    rids () {
      this.ids = ''
      if (this.rids && this.rids !== '-') {
        this.ids = this.rids
      }
      this.handleGetReferences()
    },
    immediate: true
  },
  data () {
    return {
      ids: '',
      saveReferencesDialogVisible: false,
      searchReferencesDialogVisible: false,
      saveReferencesDialogData: {},
      tableData: [],
      selectedRow: ''
    }
  },
  methods: {
    // 自动获取参考文献
    handleGetReferences () {
      this.tableData = []
      this.$ajax({
        loadingDom: '.referencesTable',
        url: '/read/bigAi/auto_get_literature',
        method: 'get',
        data: {
          associatedId: this.ids
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRow = ''
          this.tableData = result.data
        } else {
          this.$message.error(result.message)
        }
      })
    },
    /**
     * 编辑参考文献
     * @param type 0: 新增 1：修改
     */
    handleEditReferences (type) {
      if (type === 0) {
        this.saveReferencesDialogData = {
          libraryId: '',
          referenceType: '',
          referenceId: '',
          referenceName: ''
        }
        this.saveReferencesDialogVisible = true
      } else {
        if (this.selectedRow) {
          let row = this.selectedRow
          this.saveReferencesDialogData = {
            libraryId: row.rLibraryId,
            referenceType: row.rClassified,
            referenceId: row.rId,
            referenceName: row.rName
          }
          this.saveReferencesDialogVisible = true
        } else {
          this.$message.error('请选择数据')
        }
      }
    },
    // 选择参考文献
    handleSelectReferences () {
      this.searchReferencesDialogVisible = true
    },
    // 删除参考文献
    handleDeleteReferences () {
      if (this.selectedRow) {
        this.ids = this.ids.split(',')
        let index = this.tableData.findIndex(v => v.rLibraryId === this.selectedRow.rLibraryId)
        this.tableData.splice(index, 1)
        this.selectedRow = ''
      } else {
        this.$message.error('请选择数据')
      }
    },
    /**
     * 移动参考文献
     * @param type 0 上 1 下
     */
    handleMove (type) {
      if (this.selectedRow) {
        let row = this.selectedRow
        if (type) {
          // 上移
          let index = this.tableData.findIndex(v => v.rLibraryId === row.rLibraryId)
          if (index !== 0) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index - 1, 0, row)
          }
        } else {
          // 下移
          let index = this.tableData.findIndex(v => v.rLibraryId === row.rLibraryId)
          if (index !== this.tableData.length - 1) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index + 1, 0, row)
          }
        }
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleCurrentChange (currentRow) {
      this.selectedRow = currentRow
    },
    // 弹窗回调
    handleSaveReferencesDialogClose () {
      this.saveReferencesDialogVisible = false
    },
    handleSaveReferencesDialogConfirm (type, data) {
      this.saveReferencesDialogVisible = false
      if (type === 1) {
        this.tableData.push(data)
      } else {
        let index = this.tableData.findIndex(v => {
          return data.rLibraryId === v.rLibraryId
        })
        this.$set(this.tableData, index, data)
      }
    },
    handleSearchReferencesDialogClose () {
      this.searchReferencesDialogVisible = false
    },
    handleSearchReferencesDialogConfirm (data) {
      this.searchReferencesDialogVisible = false
      if (data.length !== 0) {
        let index = -1
        data.forEach(v => {
          index = this.tableData.findIndex(vv => {
            return vv.rLibraryId === v.rLibraryId
          })
          if (index === -1) {
            this.tableData.push(v)
          }
        })
      }
    }
  }
}
</script>

<style scoped></style>
