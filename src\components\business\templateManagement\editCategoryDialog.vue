<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="600px"
      @open="handleOpen">
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        size="mini"
        label-width="120px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'editCategoryDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    categoryId: String | Number,
    categoryName: String,
    categoryParentId: String | Number
  },
  data () {
    return {
      form: {
        name: ''
      },
      title: '',
      submitBtnLoading: false, // 提交loading
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.form.name = this.categoryId ? this.categoryName : ''
      this.title = this.categoryId ? '编辑分类' : '新建分类'
    },
    handleDialogConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitBtnLoading = true
          let data = {
            fcategoryName: this.form.name,
            fparentId: this.categoryParentId
          }
          if (this.categoryId) data.fid = this.categoryId
          this.$ajax({
            url: '/system/template/create_or_update_category',
            data: data
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success(this.categoryId ? '修改成功' : '添加成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.submitBtnLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
