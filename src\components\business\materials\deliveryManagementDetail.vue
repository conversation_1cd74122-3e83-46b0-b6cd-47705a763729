<template>
  <div class="page">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top" size="mini" label-suffix="：">
      <div class="info">
        <el-row :gutter="10">
          <el-col :span="4">
            <el-form-item label="申请人">{{form.fapplicant}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售">{{form.fsaleMan}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="申请日期">{{form.fapplyTime}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="中心编号">{{form.fcenterCode}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="中心名称">{{form.fcenterName}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="通知邮箱">{{form.finformEmail}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="期望到达日期">{{form.fexpectReveiveTime}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="物料邮寄地址">{{form.fsendAddr}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="联系人">{{form.fcontactPerson}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="电话">{{form.fcontactPhone}}</el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="detailed">
        <div class="title">发货清单(合计金额: {{totalPrice}})</div>
        <el-table
          :data="form.tableData" :span-method="spanMethod"
          size="mini"
          border
          class="deliveryInfoTable"
          style="width: 100%">
          <el-table-column prop="categoryName" label="类别" align="center" width="100"></el-table-column>
          <el-table-column prop="materialsName" label="品类" align="center" width="100"></el-table-column>
          <el-table-column prop="specName" label="规格" align="center" width="100"></el-table-column>
          <el-table-column prop="unit" label="单位" align="center" width="100"></el-table-column>
          <el-table-column prop="materialsNum" label="数量" align="center" width="100"></el-table-column>
          <el-table-column label="批次号" align="center" min-width="100">
            <template slot="header">批次号</template>
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 4">
                <el-form-item :prop="'tableData.' + scope.$index + '.ratificationIssue'" :rules="ratificationIssueRules" label="">
                  <el-input v-model="scope.row.ratificationIssue" maxlength="20" clearable placeholder="请输入"></el-input>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.ratificationIssue}}
              </template>
            </template>
          </el-table-column>
          <el-table-column label="失效日期" align="center" min-width="100">
            <template slot="header">失效日期</template>

            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 4">
                <el-form-item :prop="'tableData.' + scope.$index + '.expirationDate'" label="">
                  <el-date-picker
                    v-model="scope.row.expirationDate"
                    clearable
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择">
                  </el-date-picker>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.expirationDate}}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="fapplyNum" label="申请数量" align="center" width="100"></el-table-column>
          <el-table-column align="center" width="100">
            <template slot="header"><span style="color: red;margin-right: 3px;">*</span>发货数量</template>
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 1">
                <el-form-item :prop="'tableData.' + scope.$index + '.fsendNum'" :rules="[{required: true, message: '请输入发货数量', trigger: 'blur'}, {validator: (rule, value, callback) => validateSendNum(rule, value, scope.row.fapplyNum, callback), trigger: 'blur'}]" label="">
                  <el-input v-model.number="scope.row.fsendNum" :min="0" :max="scope.row.fapplyNum" type="number" placeholder="请输入"></el-input>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.fsendNum}}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="funitPrice" label="物料单价" align="center" width="100"></el-table-column>
          <el-table-column prop="priceCount" label="金额" align="center" width="100"></el-table-column>
          <el-table-column label="发货备注" align="center" min-width="100">
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 1">
                <el-form-item :prop="'tableData.' + scope.$index + '.fsendRemark'" :rules="sendRemarkRules" label="">
                  <el-input v-model="scope.row.fsendRemark" placeholder="请输入"></el-input>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.fsendRemark}}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="isbarcode" class="record">
        <div class="title">条码记录</div>
        <el-row  :gutter="10">
          <div style="padding-left: 10px; margin: 10px 0;">
            条码使用人：{{[form.fsampleSale,form.fsampleCustomer].filter(v => v).join(';')}}
          </div>
          <div style="padding-left: 10px; margin: 10px 0;">条码号段：{{form.fminBarcode}} -{{form.fmaxBarcode}}</div>
        </el-row>
      </div>
      <div class="record">
        <div class="title">发货记录</div>
        <el-row :gutter="10">
         <el-col :span="6">
           <el-form-item label="快递单号" prop="fexpressCode">
             <el-input v-model="form.fexpressCode" disabled placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item label="经办人" prop="foperator">
             <el-input v-model="form.foperator" clearable disabled placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item label="发货时间" prop="fsendTime">
             <el-date-picker v-model="form.fsendTime" clearable disabled type="date" value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
           </el-form-item>
         </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="bottom">
      <el-button type="danger" size="mini" @click="handleClose">关 闭</el-button>
    </div>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  name: 'deliveryManagementSaveInfo',
  mixins: [mixins.tablePaginationCommonData],
  components: {},
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {
    totalPrice () {
      const tableData = this.form.tableData || []
      return tableData.reduce((pre, next) => {
        const result = util.add(pre, next.priceCount)
        if (!result) return pre
        return result
      }, 0)
    },
    isbarcode () {
      return this.form.tableData.some(v => v.materialsName.includes('条码'))
    },
    deliveryInfo () {
      return this.$store.getters.getValue('deliveryInfo')
    }
  },
  data () {
    return {
      loading: false,
      form: {
        fcustomer: '',
        fapplicant: '',
        fsaleMan: '',
        fapplyTime: '',
        fcenterCode: '',
        fcenterName: '',
        finformEmail: '',
        fexpectReveiveTime: '',
        fsendAddr: '',
        fcontactPhone: '',
        fcontactPerson: '',
        fexpressCode: '',
        foperator: '',
        fsendTime: '',
        tableData: []
      },
      currentIndex: 0, // 当前行的申请数量，用于判断发放数量是否小于等于申请数量
      rules: {
        fexpressCode: [
          {required: true, message: '请输入快递单号', trigger: 'blur'}
        ],
        foperator: [
          {required: true, message: '请输入经办人', trigger: 'blur'}
        ],
        fsendTime: []
      },
      sendNumRules: [
        {required: true, message: '请输入发货数量', trigger: 'blur'}
      ],
      sendRemarkRules: []
    }
  },
  methods: {
    // 获取发货信息以及发货记录信息
    getData () {
      this.$ajax({
        method: 'get',
        url: '/materials/get_one_applyform',
        data: {
          fid: this.deliveryInfo.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form = {
            fcustomer: data.fcustomer,
            fminBarcode: data.fminBarcode,
            fmaxBarcode: data.fmaxBarcode,
            fapplicant: data.fapplicant,
            fsaleMan: data.fsaleMan,
            fapplyTime: data.fapplyTime,
            fcenterCode: data.fcenterCode,
            fcenterName: data.fcenterName,
            finformEmail: data.finformEmail,
            fsampleSale: data.fsampleSale,
            fsampleCustomer: data.fsampleCustomer,
            fexpectReveiveTime: data.fexpectReveiveTime,
            fsendAddr: data.fsendAddr,
            fcontactPhone: data.fcontactPhone,
            fcontactPerson: data.fcontactPerson,
            fexpressCode: data.fexpressCode,
            foperator: data.foperator,
            fsendTime: data.fsendTime,
            tableData: []
          }
          this.getDeliveryInfoTable()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取发货清单列表数据
    getDeliveryInfoTable () {
      this.$ajax({
        method: 'get',
        loadingDom: '.deliveryInfoTable',
        url: '/materials/get_materials_data',
        data: {
          fid: this.deliveryInfo.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let list = []
          let item = {}
          result.data.forEach(v => {
            item = {
              categoryName: v.categoryName,
              materialsName: v.materialsName,
              specName: v.specName,
              unit: v.unit,
              materialsNum: v.materialsNum,
              fapplyNum: v.fapplyNum,
              fsendNum: v.fsendNum,
              materialsPackageId: v.materialsPackageId,
              materialsId: v.materialsId,
              fsendRemark: v.fremake,
              funitPrice: v.funitPrice,
              ratificationIssue: v.fratificationIssue,
              expirationDate: v.fexpirationDate
            }
            list.push(item)
          })
          this.getRealTableData(list)
          this.getPriceCount()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 计算总额
    getPriceCount () {
      // 物料在申请单中是散料时：单价×发货数量
      // 物料在申请单中是物料包时：单价×数量×发货数量
      this.form.tableData = this.form.tableData.map(materialInfo => {
        let priceCount = util.mul(materialInfo.fsendNum > 0
          ? materialInfo.fsendNum : materialInfo.fapplyNum,
        materialInfo.funitPrice)
        materialInfo.categoryName = materialInfo.categoryName || ''
        if (materialInfo.materialsPackageId) {
          priceCount = util.mul(priceCount, materialInfo.materialsNum || 1)
        }
        if (!materialInfo.funitPrice) {
          priceCount = '-'
        }
        materialInfo.funitPrice = materialInfo.funitPrice || '-'
        return {priceCount: priceCount.toString(), ...materialInfo}
      })
    },
    // map 结构，key是物料包ID(materialsPackageId),value是物料包下的物料数据
    getRealTableData (list) {
      let map = new Map()
      let key = null
      list.forEach(v => {
        key = v.materialsPackageId || v.categoryName
        if (map.has(key)) {
          map.get(key).push(v)
        } else {
          map.set(key, [v])
        }
      })
      let tableData = []
      // 将map数据转换成array格式
      let data = [...map.values()]
      // 对已经分类的数据进行再次处理，获取类别合并行数据
      data.forEach((v, i) => {
        v.forEach((vv, ii) => {
          // total 表示需要合并的行数，只有每个物料包的第一个品类有数据，剩余的均为0
          let item = {...vv, total: ii === 0 ? v.length : 0}
          tableData.push(item)
        })
      })
      // 使用set方法设置表格数据，避免页面数据不渲染的问题
      this.$set(this.form, 'tableData', tableData)
    },
    // 校验发货数量是否小于申请数量以及是否大于0
    validateSendNum (rule, value, applyNum, callback) {
      if (value <= 0) {
        callback(new Error('发货数量必须大于0'))
      } else {
        if (value > applyNum) {
          callback(new Error('发货数量不能大于申请数量'))
        } else {
          callback()
        }
      }
    },
    spanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 对类别列进行合并，行合并数为物料包里的品类数量
        return {
          rowspan: row.total,
          colspan: 1
        }
      } else if (columnIndex === 7) {
        // 对申请数量列进行行合并（只对物料包合并，散包不合并申请数量）
        // 通过物料包ID（materialsPackageId）进行判断是否为散包
        return {
          rowspan: row.materialsPackageId ? row.total : 1,
          colspan: 1
        }
      } else if (columnIndex === 8 || columnIndex === 11) {
        // 对发货数量和发货备注列进行行合并（只对物料包合并，散包不合并发货数量和发货备注）
        // 通过物料包ID（materialsPackageId）进行判断是否为散包
        return {
          rowspan: row.materialsPackageId ? row.total : 1,
          colspan: 1
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1
        }
      }
    },
    handleClose () {
      this.$store.commit({
        type: 'old/setValue',
        category: 'deliveryInfo',
        deliveryInfo: {
          fid: null,
          type: null
        }
      })
      window.close()
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    padding: 10px 20px;
    position: relative;
    height: calc(100vh - 40px);
    overflow: auto;
  }
  .title{
    font-size: 20px;
    /*font-weight: bold;*/
    line-height: 30px;
    height: 30px;
    border-bottom: 1px solid #DCDFE6;
    margin: 10px 0;
  }
  .detailed {
    min-height: calc(100vh - 184px - 92px - 40px - 20px - 30px - 20px - 30px - 20px);
    >>>.el-table {
      tbody tr {
        &:hover {
          td {
            background-color: transparent;
          }
        }
      }
      .el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item{
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }
  .bottom{
    padding: 0 10px;
    bottom: 0;
    position: sticky;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    text-align: right;
    z-index: 101;
    border-top: 1px solid #DCDFE6;
  }
</style>
