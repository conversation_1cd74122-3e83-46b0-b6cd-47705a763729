<template>
  <el-dialog
    title="下单数据量"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @opened="initTableData"
  >
    <div class="data-dialog">
      <el-table
        :data="allProductDataSize"
        border
        size="mini"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column
          prop="flibTypeSuffix"
          label="文库"
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="dataSize"
          label="下单数据量/M"
          min-width="120"
          align="center"
        />
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  name: 'DataDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    allProductDataSize: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      tableData: []
    }
  }
}
</script>

<style lang="scss" scoped>
.total-info {
  margin-top: 20px;
//   padding: 15px 0;
//   border-top: 1px solid #EBEEF5;
  .total-item {
    display: flex;
    justify-content: flex-end;
    font-size: 14px;
    .label {
      margin-right: 10px;
      font-weight: bold;
      color: #606266;
    }
    .value {
      font-weight: bold;
      color: #303133;
    }
  }
}
</style>
