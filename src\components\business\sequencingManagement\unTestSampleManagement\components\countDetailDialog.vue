<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="详情"
      min-width="1000px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @opened="handleOpen">
      <template>
        <el-table
          :data="tableData"
          stripe
          border
          class="table"
          size="mini"
          max-height="400px"
        >
          <el-table-column type="index" label="序号" width="50px"></el-table-column>
          <el-table-column prop="ffcNum" label="芯片号" min-min-width="140px"></el-table-column>
          <el-table-column prop="forderDataSize" label="客户下单数据量/G" min-width="120px"></el-table-column>
          <el-table-column prop="fwalkthroughDateSize" label="排机数据量" min-width="120px"></el-table-column>
          <el-table-column prop="fdataSize" label="下机数据量" min-width="120px"></el-table-column>
          <el-table-column prop="foutputProportion" label="产出比" min-width="120px"></el-table-column>
        </el-table>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import mixins from '@/util/mixins'
import util, {awaitWrap} from '../../../../../util/util'
import {getSeqTimes} from '../../../../../api/sequencingManagement/unTestSampleApi'
export default {
  name: `errorDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      visible: false,
      downloadLoading: false,
      totalPage: 0,
      tableData: []
    }
  },
  methods: {
    async handleOpen () {
      let {res} = await awaitWrap(getSeqTimes({
        fid: this.id
      }, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.tableData = []
        const rows = data || []
        rows.forEach(v => {
          const item = {
            ffcNum: v.ffcNum,
            forderDataSize: v.forderDataSize,
            fwalkthroughDateSize: v.fwalkthroughDateSize,
            fdataSize: v.fdataSize,
            foutputProportion: v.foutputProportion
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table td, .el-table th{
  padding: 6px 0;
}
.title{
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
}
</style>
