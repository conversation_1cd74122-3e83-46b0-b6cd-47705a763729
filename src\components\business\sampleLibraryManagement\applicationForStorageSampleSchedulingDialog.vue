<template>
  <div>
    <el-dialog
        title="样本调度"
        :visible.sync="visible"
        :close-on-click-modal="false"
        width="70%"
        @open="handleOpen"
        :before-close="handleClose">
      <div
          v-loading="loading"
          element-loading-text="正在上传文件"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.8)">
        <div class="form-container">
          <div class="form-item">
            <label>模板示例</label>
            <el-button type="primary" size="mini" @click="handleDownloadTemplate">下载模板</el-button>
          </div>
          <div class="form-item">
            <label>导入申请单</label>
            <div style="display: flex;">
              <el-upload
                  ref="upload"
                  :on-success="handleOnSuccess"
                  :on-error="handleOnError"
                  :data="uploadParams"
                  :auto-upload="true"
                  :limit="1"
                  :file-list="fileList"
                  :headers="headers"
                  :before-upload="handleBeforeUpload"
                  :action="uploadUrl">
                <el-button size="mini" type="primary">点击上传</el-button>
              </el-upload>
              <div style="margin-left: 20px;">
                <el-input
                    size="mini"
                    v-model.trim="inputSampleCodes"
                    clearable
                    placeholder="请输入样本编号，逗号隔开"
                    style="width: 300px;"></el-input>
                <el-button size="mini" type="primary" @click="handleImportSampleCode">导入</el-button>
              </div>
            </div>
          </div>
          <div style="display: flex; align-items: center;">
            <div class="form-item">
              <label>所属实验室：</label>
              <el-select size="mini" v-model.trim="sourceLab">
                <el-option v-for="(v, k) in regionObj" :key="k" :label="v" :value="+k"></el-option>
              </el-select>
            </div>
            <div class="form-item" style="margin-left: 20px;">
              <label>前往实验室：</label>
              <el-select size="mini" v-model.trim="targetLab">
                <el-option v-for="(v, k) in regionObj" :key="k" :label="v" :value="+k"></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="table">
          <h4>清单</h4>
          <el-table
              style="width: 100%"
              @select="handleSelect"
              @select-all="handleSelectAll"
              @row-click="handleRowClick"
              ref="table"
              height="300"
              :data="tableData">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本状态" width="100" prop="sampleStatus"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本编号" width="180" prop="sampleCode"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本类型" min-width="100" prop="sampleType"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本量" width="100" prop="sampleAmount"></el-table-column>
            <el-table-column show-overflow-tooltip label="样本存放位置" min-width="180" prop="samplePosition"></el-table-column>
            <el-table-column show-overflow-tooltip label="备注" min-width="180" prop="notes"></el-table-column>
          </el-table>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
            size="mini"
            type="primary"
            :loading="submitBtnLoading"
            @click="handleDialogSubmit"
           >提交</el-button>
      </div>
      <el-dialog
          title="提示"
          append-to-body
          :close-on-click-modal="false"
          :visible.sync="showSubmitSuccessDialogVisible"
          width="500px">
        <div v-if="showSubmitSuccessDialogVisible" class="success-dialog">
          <p>操作成功！</p>
          <p>本次调度{{selectedRows.size}}个样本</p>
        </div>
        <div style="display: flex;justify-content: center">
          <el-button size="mini" @click="handleCloseSuccessDialog">关闭</el-button>
          <el-button size="mini" :loading="downloading" @click="handleDownloadOrder" type="primary">下载申请单</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util from '../../../util/util'
export default {
  name: 'applicationForStorageSampleSchedulingDialog',
  mixins: [mixins.dialogBaseInfo],
  computed: {
    tableData () {
      return this.allTableData.slice(0, this.currentPage * this.pageSize)
    }
  },
  data () {
    let loginId = util.getSessionInfo('loginId')
    let id = loginId ? util.decryptBase64(loginId) : '1'
    return {
      downloading: false,
      orderType: '',
      title: '', // dialog标题
      uploadUrl: constants.JS_CONTEXT + '/sample/order/import_dispatch_order',
      headers: {
        'user-id': id
      },
      uploadParams: {},
      fileList: [],
      sourceLab: '', // 所属实验室
      targetLab: '', // 前往实验室
      loading: false,
      submitBtnLoading: false,
      showSubmitSuccessDialogVisible: false,
      // tableData: [],
      allTableData: [],
      inputSampleCodes: '',
      selectedRows: new Map(),
      orderNum: '',
      currentPage: 1,
      pageSize: 50,
      regionObj: constants.REGION_OBJ
    }
  },
  methods: {
    handleOpen () {
      this.sourceLab = ''
      this.targetLab = ''
      this.loading = false
      this.allTableData = []
      this.inputSampleCodes = ''
      this.selectedRows.clear()
    },
    // 为表格添加前端分页
    addTableScrollEvent () {
      const table = this.$refs.table.$el.querySelector('.el-table__body-wrapper')
      table.addEventListener('scroll', () => {
        let scrollTop = table.scrollTop
        let scrollHeight = table.scrollHeight
        let clientHeight = table.clientHeight
        if (scrollHeight - scrollTop - clientHeight < 1) {
          // this.handleGetMoreSample()
          this.currentPage++
          this.$nextTick(() => { this.selectRows() })
        }
      })
    },
    // // 反选
    // selectRows (tableName) {
    //   let data = tableName + 'TableAllData'
    //   let select = tableName + 'TableSelection'
    //   let table = tableName + 'Table'
    //   this[data].forEach(v => {
    //     this.$refs[table].toggleRowSelection(v, false)
    //   })
    //   this[data].forEach(v => {
    //     if (this[select].has(v.id)) {
    //       this.$refs[table].toggleRowSelection(v, true)
    //     }
    //   })
    // },
    // 下载模板
    handleDownloadTemplate () {
      let form = document.createElement('form')
      form.action = constants.JS_CONTEXT + '/sample/order/download_order_template'
      form.method = 'get'
      form.id = 'form'
      let submitData = {
        orderType: 5
      }
      for (let key in submitData) {
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = submitData[key]
        form.appendChild(input)
      }
      document.body.appendChild(form)
      form.submit()
      form.parentNode.removeChild(form)
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data || []
        let tableDataMap = new Map()
        this.allTableData.forEach(item => {
          tableDataMap.set(item.sampleCode, item)
        })
        data.forEach(v => {
          const item = {
            id: v.fid,
            sampleCode: v.fsampleNumber,
            sampleStatus: v.fsampleStatus,
            sampleType: v.fsampleType,
            sampleAmount: v.fsampleAmount,
            samplePosition: v.fsamplePlace,
            isPreciousSample: v.fisPreciousSample,
            notes: v.fnotes
          }
          tableDataMap.set(item.sampleCode, item)
        })
        this.allTableData = [...tableDataMap.values()]
        this.$nextTick(() => {
          this.selectRows()
        })
      } else {
        this.$showErrorDialog({
          tableData: res.data || []
        })
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 8) {
          this.loading = false
          this.$message.error('文件大小超过限制，无法上传')
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    },
    // 导入样本编号
    handleImportSampleCode () {
      if (!this.inputSampleCodes) {
        this.$message.error('样本编号不能为空')
        return
      }
      let codes = this.inputSampleCodes.replace(/\s+/g, ',').replace(/，/g, ',')
      this.$ajax({
        url: '/sample/get_sample_by_fsampleNumber',
        method: 'get',
        data: {
          fsampleNumbers: codes
        },
        loadingDom: '.form-container'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          let tableDataMap = new Map()
          this.allTableData.forEach(item => {
            tableDataMap.set(item.sampleCode, item)
          })
          // this.tableData = []
          data.forEach(v => {
            let item = {
              id: v.fid,
              sampleCode: v.fsampleNumber,
              sampleStatus: v.fsampleStatus,
              sampleType: v.fsampleType,
              sampleAmount: v.fsampleAmount,
              samplePosition: v.fsamplePlace,
              isPreciousSample: v.fisPreciousSample,
              notes: v.fnotes
            }
            tableDataMap.set(item.sampleCode, item)
          })
          this.allTableData = [...tableDataMap.values()]
          this.inputSampleCodes = ''
          this.$nextTick(() => {
            this.selectRows()
          })
        } else {
          if (Array.isArray(res.data)) {
            this.$showErrorDialog({
              tableData: res.data || []
            })
            return
          }
          this.$message.error(res.message)
        }
      })
    },
    // 点击表格行
    handleRowClick (row) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.sampleCode))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.sampleCode) ? this.selectedRows.delete(row.sampleCode) : this.selectedRows.set(row.sampleCode, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      if (selection.length > 0) {
        this.allTableData.forEach((row) => {
          this.selectedRows.set(row.sampleCode, row)
        })
      }
    },
    // 反选表格
    selectRows () {
      this.selectedRows.forEach(v => {
        this.$refs.table.toggleRowSelection(v, false)
      })
      this.tableData.forEach(v => {
        if (this.selectedRows.has(v.subId)) {
          this.$refs.table.toggleRowSelection(v, true)
        }
      })
    },
    setParams () {
      const selectedRows = [...this.selectedRows.values()]
      let codes = []
      selectedRows.forEach(item => {
        const v = {
          fid: item.id,
          fsampleNumber: item.sampleCode,
          fsampleType: item.sampleType,
          ftemperature: item.temperature,
          ftubeType: item.pipe,
          fsampleAmount: item.sampleAmount,
          isPreciousSample: item.isPreciousSample,
          fnotes: item.notes
        }
        codes.push(v)
      })
      return { // 提交选择的实验室
        fsourceLab: this.sourceLab,
        ftargetLab: this.targetLab,
        sampleInfoList: codes
      }
    },
    // 提交
    async handleDialogSubmit () {
      if (this.selectedRows.size === 0) {
        this.$message.error('未选择样本')
        return
      }
      if (!this.sourceLab) {
        this.$message.error('请选择所属实验室')
        return
      }
      if (!this.targetLab) {
        this.$message.error('请选择前往实验室')
        return
      }
      const params = this.setParams()
      const preciousList = params.sampleInfoList.filter(v => v.isPreciousSample === 1)
      console.log(preciousList)
      if (preciousList.length > 0) {
        const message =
          `${preciousList.map(v => v.fsampleNumber).join(',')}
             <br/>
             为珍贵样本，请确认是否出库操作？`
        await this.$confirm(message, '珍贵样本提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      this.submitBtnLoading = true
      this.$ajax({
        url: '/sample/order/submit_dispatch_order',
        data: params
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.orderNum = res.data
          this.showSubmitSuccessDialogVisible = true
        } else {
          console.log(Array.isArray(res.data), '1111111')
          if (Array.isArray(res.data)) {
            console.log(111111, 2222222)
            this.$showErrorDialog({tableData: res.data})
          } else {
            this.$message.error(res.message)
          }
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    },
    // 下载申请单
    handleDownloadOrder () {
      this.downloading = true
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${this.orderNum}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        }).finally(() => {
          this.downloading = false
        })
      })
    },
    // 关闭
    handleCloseSuccessDialog () {
      this.showSubmitSuccessDialogVisible = false
      this.handleClose()
    }
  }
}
</script>

<style scoped lang="scss">
  .form-container{
    .form-item{
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      label{
        margin-right: 20px;
        display: block;
        width: 7em;
      }
    }
  }
  .success-dialog{
    font-size: 16px;
    color: #000;
    font-weight: 600;
    text-align: center;
    line-height: 2;
    margin-bottom: 20px;
  }
</style>
