<template>
  <el-dialog
  title="数据交付-错误提示"
  :visible.sync="visible"
  width="600px"
  :before-close="handleClose"
  append-to-body
  >
  <div v-if="errorTypeList.includes(0)" class="error-content">
    <div class="error-type">
      <span class="label">以下样本未下机，无法交付请知悉:</span>
    </div>
    <div class="error-list">
      <el-table
        :data="errorListData1"
        style="width: 100%"
        max-height="300"
        size="mini"
        border
      >
      <el-table-column
          prop="geneNum"
          label="吉因加编号"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="errorMessage"
          label="报错内容"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>
  </div>
  <div v-if="errorTypeList.includes(2)" class="error-content">
    <div class="error-type">
      <span class="label">以下样本质控分析未合格，请确认是否交付:</span>
    </div>
    <div class="error-list">
      <el-table
        :data="errorListData2"
        style="width: 100%"
        max-height="300"
        size="mini"
        border
      >
      <el-table-column
          prop="geneNum"
          label="吉因加编号"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="errorMessage"
          label="报错内容"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>
  </div>
  <span slot="footer" class="dialog-footer">
    <el-button size="mini" @click="handleClose">关 闭</el-button>
    <el-button v-if="filterGeneList.length > 0" type="primary" size="mini" @click="handleStart(filterGeneList)">继续交付</el-button>
  </span>
</el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import { awaitWrap } from '@/util/util'
import {dataDelivery} from '../../../../../api/sequencingManagement/singleCell'
export default {
  name: 'qcErrorDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    errorTypeList: {
      type: Array,
      default: () => []
    },
    errorList: {
      type: Array,
      default: () => []
    },
    filterGeneList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    errorListData1 () {
      const errorInfo = this.errorList.find(v => v.errorType === 0) || {}
      return errorInfo.cosQualityAnalysisDeliverDataDTOS || []
    },
    errorListData2 () {
      const errorInfo = this.errorList.find(v => v.errorType === 2) || {}
      return errorInfo.cosQualityAnalysisDeliverDataDTOS || []
    }
  },
  methods: {
    async handleStart (filterGeneList) {
      const {res} = await awaitWrap(dataDelivery({
        fgeneNumList: filterGeneList,
        fcondition: 1
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('数据交付成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.error-type {
  margin: 10px 0;
}
</style>
