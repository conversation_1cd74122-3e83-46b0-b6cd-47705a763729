<template>
  <div>
    <div style="margin: 20px 0 0 0;border-bottom: 1px solid #ccc;">
      <el-form :model="form" size="mini" label-width="100px" inline>
        <div>
          <el-form-item label="样本编号">
            <el-input v-model="form.sampleCode" class="form-content" clearable></el-input>
          </el-form-item>
          <el-form-item label="产品/项目名称">
            <el-input v-model="form.productName" class="form-content" clearable></el-input>
          </el-form-item>
          <el-form-item label="结果上传时间">
            <el-date-picker
              v-model="form.uploadResultDate[0]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
            <span>--</span>
            <el-date-picker
              v-model="form.uploadResultDate[1]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="线下检测状态">
            <el-select v-model="form.detectLink" size="mini" class="form-content" clearable>
              <el-option label="全部" value="0"></el-option>
              <el-option label="待上传" value="1"></el-option>
              <el-option label="已上传" value="2"></el-option>
              <el-option label="异常" value="2"></el-option>
              <el-option label="停止检测" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检测环节">
            <el-select v-model="form.offlineDetectStatus" placeholder="请选择" size="mini" class="form-content">
              <el-option :value="0" label="到样时间"></el-option>
              <el-option :value="1" label="补录时间"></el-option>
              <el-option :value="2" label="审核时间"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary">查询</el-button>
            <el-button>重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="buttonGroup">
      <el-button type="text" size="mini" @click="uploadResultDialogVisible = true">结果上传</el-button>
      <el-button type="text" size="mini" @click="handleRegistrationException">登记异常</el-button>
      <el-button type="text" size="mini" >预览</el-button>
      <el-button type="text" size="mini" >导入结果</el-button>
      <el-popover
        placement="bottom"
        trigger="click">
        <el-button slot="reference" type="text" style="margin-left: 20px;">导出模板</el-button>
      </el-popover>
    </div>
    <div>
      <el-table
        ref="table"
        :data="tableData"
        :height="tableHeight"
        class="reservationTable"
        style="width: 100%"
        @select="handleSelectTable"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45"></el-table-column>
        <el-table-column prop="sampleCode" label="样本编号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="产品名称" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="area" label="生产片区" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="offlineDetectType" label="线下检测类型" width="220" show-overflow-tooltip></el-table-column>
        <el-table-column prop="offlineDetectStatus" label="线下检测状态" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="detectLink" label="检测环节" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="estimatedInteractionDate" label="预交付时间" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="overageTAT" label="剩余TAT" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="detectCompany" label="检测公司" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="abnormalDescription" label="异常描述" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="uploadResultPerson" label="结果上传人" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="uploadResultDate" label="结果上传时间" width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
              :page-sizes="pageSizes"
              :page-size="pageSize"
              :current-page.sync="currentPage"
              :total="totalPage"
              layout="total, sizes, prev, pager, next, jumper, slot"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </div>
    <upload-result-dialog :pvisible.sync="uploadResultDialogVisible" />
    <import-result-dialog :pvisible.sync="importResultDialogVisible" />
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import uploadResultDialog from './offlineDetectManagementUploadResultDialog'
import importResultDialog from './offlineDetectManagementImportResultDialog'
export default {
  name: 'offlineDetectManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    uploadResultDialog,
    importResultDialog
  },
  beforeMount () {
    this.$_setTbHeight()
    window.addEventListener('resize', this.$_setTbHeight)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.$_setTbHeight)
    })
  },
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        sampleCode: '',
        productName: '',
        uploadResultDate: ['', ''],
        offlineDetectStatus: '',
        detectLink: ''
      },
      formSubmit: {},
      tableHeight: 0,
      uploadResultDialogVisible: false,
      importResultDialogVisible: true
    }
  },
  methods: {
    $_setTbHeight () {
      let h1 = document.documentElement.clientHeight - 1
      this.tableHeight = h1 - 64 - 50 - 20 - 20 - 51 * 2 - 45 - 42 - 20
    },
    getData () {
      this.$ajax({
        url: '/sample/express/page_sample_express',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {}
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sample_express_id,
              sampleCode: v.sample_num,
              expressStatus: v.express_status,
              sampleType: v.sample_type,
              productName: v.product_name,
              area: '',
              trackingNum: v.express_num,
              deliveryDate: v.send_time,
              estimatedArrivalDate: v.pre_receive_time,
              deliveryAddress: v.send_addr,
              courierCompany: v.express_company,
              trainNum: v.train_number,
              isExtension: v.city,
              submissionDate: v.sign_time,
              signer: v.sign_person,
              transportationTime: v.transport_use_time,
              hospital: v.name,
              notes: v.remark,
              sell: v.sales_person,
              abnormalNotes: v.ex_remark,
              recipient: v.collect_person,
              phone: v.phone,
              receivingAddress: v.collect_addr
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSearch () {
      this.formSubmit = {...this.form}
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        sampleCode: '',
        productName: '',
        uploadResultDate: ['', ''],
        offlineDetectStatus: '',
        detectLink: ''
      }
      this.handleSearch()
    },
    // 登记异常
    handleRegistrationException () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择记录')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.$prompt('异常描述', '登记异常', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: function (value) {
          let v = value ? value.trim() : ''
          return v.length > 0 && v.length <= 200
        },
        inputErrorMessage: '异常不能为空并且长度不能大于200'
      }).then(({ value }) => {
        this.$ajax({
          url: '/sample/express/exception_sample_express',
          data: {
            sampleExpressIds: ids.toString(),
            exceptionRemark: value
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('登记异常成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 点击行
    handleRowClick (row) {
      // if (!this.selectedRows.has(row.patientCode)) {
      //   this.$refs.table.clearSelection()
      //   this.selectedRows.clear()
      // }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id)
        ? this.selectedRows.delete(row.id)
        : this.selectedRows.set(row.id, row)
    }
  }
}
</script>

<style scoped lang="scss">
  .form-content{
    width: 150px;
  }
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }
</style>
