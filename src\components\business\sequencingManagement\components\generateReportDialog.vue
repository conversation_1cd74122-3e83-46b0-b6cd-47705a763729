<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      title="报告生成"
      width="50%"
      @open="handleOpen"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        class="form"
        label-width="110px"
        size="mini"
        label-suffix=":"
        @paste.native="handlePaste">
        <el-form-item label="报告模版" prop="template">
          <el-select v-model.trim="form.template" size="mini" clearable filterable>
            <el-option
              :key="index"
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in templates"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="showPartTestPic" label="片段检测结果" prop="partTestPics">
          <uploader ref="showPartTestPic" v-model.trim="form.partTestPics"></uploader>
        </el-form-item>
        <el-form-item v-if="showElectrophoreticGelPic" label="电泳胶图" prop="electrophoreticGelPic">
          <uploader v-model.trim="form.electrophoreticGelPic"></uploader>
        </el-form-item>
        <el-form-item v-if="showElectrophoreticTestPic" label="电泳检测结果" prop="electrophoreticTestPic">
          <uploader v-model.trim="form.electrophoreticTestPic"></uploader>
        </el-form-item>
        <el-form-item v-if="showAilentCompleteTestPic" label="安捷伦完整性检测结果" prop="ailentCompleteTestPic">
          <uploader v-model.trim="form.ailentCompleteTestPic"></uploader>
        </el-form-item>
        <el-form-item v-if="showQsepOrLabchipPic" label="Qsep电泳图谱" prop="qsep">
          <uploader v-model.trim="form.qsep"></uploader>
        </el-form-item>
        <el-form-item v-if="showQsepOrLabchipPic" label="Labchip电泳图谱" prop="labchipPic">
          <uploader v-model.trim="form.labchipPic"></uploader>
        </el-form-item>
      </el-form>
      <div class="tips">
        <span class="label">注意事项:</span> 仅支持jpg/png/jpeg等图片格式的文件上传，一次性上传的文件大小不能超过30M
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm(1)">预览报告</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm(2)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import uploader from './uploader'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {
    uploader
  },
  props: {
    qcResultIdList: {
      type: Array
    },
    subOrderId: {
      type: Number
    },
    orderCode: {
      type: String
    },
    reportType: {
      type: Number
    }
  },
  computed: {
    // 电泳胶图
    showElectrophoreticGelPic () {
      return [4, 6, 7, 8, 14].includes(this.form.template)
    },
    // 电泳检测结果
    showElectrophoreticTestPic () {
      return [9, 10, 11, 12, 15].includes(this.form.template)
    },
    // 安捷伦完整性检测结果
    showAilentCompleteTestPic () {
      return [9, 10, 11, 12, 15].includes(this.form.template)
    },
    // 片段检测结果
    showPartTestPic () {
      return [1, 2, 3, 5].includes(this.form.template) || true
    },
    // Qsep电泳/Labchip电泳图谱
    showQsepOrLabchipPic () {
      return [13].includes(this.form.template)
    }
  },
  data () {
    return {
      loading: false,
      form: {
        template: '',
        partTestPics: [],
        electrophoreticGelPic: [],
        electrophoreticTestPic: [],
        ailentCompleteTestPic: [],
        qsep: [],
        labchipPic: []
      },
      rules: {
        template: [
          {require: true, type: 'number', message: '请选择报告模版', trigger: ['blur', 'change']}
        ]
      },
      templates: [
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          template: '',
          partTestPics: [],
          electrophoreticGelPic: [],
          electrophoreticTestPic: [],
          ailentCompleteTestPic: [],
          qsep: [],
          labchipPic: []
        }
        this.$refs.form.resetFields()
        this.getTemplates()
      })
    },
    handlePaste (value) {
      let files = value.clipboardData.items || []
      console.log(this.$refs.showPartTestPic)
      const fileList = []
      for (const file of files) {
        console.log(file)
        if (file.type.includes('image')) {
          let imgFile = file.getAsFile()
          this.$refs.showPartTestPic.$refs.upload
            .handleStart(imgFile)
          fileList.push(imgFile)
        }
      }
      this.$refs.showPartTestPic.$refs.upload.submit()
    },
    getTemplates () {
      this.$ajax({
        url: '/order/report/get_qc_report_template_list',
        data: {
          fqcResultIdList: this.qcResultIdList || [],
          fsubOrderId: this.subOrderId
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.templates = []
          data.forEach(v => {
            let item = {
              label: v.fdescribe,
              value: v.fvalue * 1
            }
            this.templates.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    async handleClose () {
      await this.handleConfirmMessage('是否放弃操作？“取消”后已上传的数据将不会被系统保存')
      this.visible = false
      this.$emit('dialogCloseEvent')
    },
    async handleConfirmMessage (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    handleConfirm (type) {
      let url = ''
      type === 2 ? url = '/order/report/save_qc_report' : url = '/order/report/get_pdf_report'
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = await this.setParams()
          this.loading = true
          this.$ajax({
            url: url,
            data: params,
            loadingDom: '.form'
          }).then((res) => {
            if (res && res.code === this.SUCCESS_CODE) {
              if (type === 2) {
                this.$message.success('生成成功')
                this.visible = false
                this.$emit('dialogConfirmEvent')
              } else {
                window.open(res.data, '_blank')
              }
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 设置请求参数
    async setParams () {
      let imgList = []
      let keys = ['partTestPics', 'electrophoreticGelPic', 'electrophoreticTestPic', 'ailentCompleteTestPic', 'qsep', 'labchipPic']
      keys.forEach((v, index) => {
        let files = this.form[v] || []
        console.log(files)
        files = files.map(file => {
          return {
            fname: file.name,
            furl: JSON.stringify({
              group: file.group,
              path: file.path
            }),
            ftype: index + 1
          }
        })
        imgList = imgList.concat(files)
      })
      if (imgList.length <= 0) {
        await this.handleConfirmMessage('当前未上传任何文件，是否生成报告？')
      }
      return {
        fqcResultIdList: this.qcResultIdList || [],
        fsubOrderId: this.subOrderId,
        ftype: this.form.template,
        forderCode: this.orderCode,
        freportType: this.reportType,
        imageList: imgList
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-select {
  width: calc(100% - 110px);
}
.tips {
  color: $color;
}
.label {
  display: inline-block;
  width: 110px;
  padding: 0 12px 0 0;
  text-align: right;
}
</style>
