<template>
  <div style="height: 100%;width: 100%;">
    <div class="content">
      <div class="left">
        <div class="header">工序步骤</div>
        <el-table
          ref="stepTable" :data="tableData" border
          size="mini" class="stepTable" height="calc(100% - 36px - 20px)"
          style="width: 100%"
          @row-click="handleRowClick"
          @select="handleSelect">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="stepName" label="工序步骤名称" min-width="140" show-overflow-tooltip></el-table-column>
        </el-table>
        <scroll-pane :scroll-height="36">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="totalPage"
            style="background-color: #ffffff;width: 350px;"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
          </el-pagination>
        </scroll-pane>
      </div>
      <div class="right">
        <div class="processName">
          <div style="min-width: 200px;color: #409EFF;font-size: 16px;" class="header">
            {{procedureStepName}}
          </div>
          <el-button v-if="$setAuthority('002006001', 'buttons')" type="primary" size="mini" icon="el-icon-edit" @click="handleEditStepDetail">修改</el-button>
        </div>
        <el-table
          :data="detailTableData" :show-header="false" :cell-style="cellStyle"
          border class="detailTable"
          height="calc(100% - 10px - 36px - 20px)"
          style="width: calc(100% - 20px); margin: 10px;">
          <el-table-column prop="label" label="名称" width="140" align="right"></el-table-column>
          <el-table-column prop="value" label="值" min-width="140" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
    <save-dialog
      :pvisible="saveDialogVisible" :pdata="saveDialogData"
      @processStepManagementSaveDialogConfirmEvent="handleSaveDialogConfirm"
      @processStepManagementSaveDialogCloseEvent="handleSaveDialogClose"
    ></save-dialog>
  </div>
</template>

<script>
import util from '../../../util/util'
import mixins from '../../../util/mixins'
import saveDialog from './processStepManagementSaveDialog'
export default {
  name: 'processStepManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    saveDialog
  },
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {},
  data () {
    return {
      procedureStepName: '',
      detailTableData: [],
      selectedRow: new Map(),
      saveDialogVisible: false,
      saveDialogData: {},
      form: {
        stepName: '',
        stepTypeName: '',
        sendSampleTypeName: '',
        sampleTypeName: '',
        useSampleTypeName: '',
        tatTime: ''
      }
    }
  },
  methods: {
    getData () {
      this.$ajax({
        url: '/system/procedure/get_all_step_data',
        data: {
          current: this.currentPage,
          size: this.pageSize
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.tableData = []
          this.selectedRow.clear()
          let data = result.data
          this.totalPage = data.total
          let item = {}
          data.rows.forEach(v => {
            item = {
              stepCode: v.stepCode,
              stepName: v.stepName,
              stepType: v.stepType,
              stepTypeName: v.stepTypeName,
              sendSampleType: v.sendSampleType,
              sendSampleTypeName: v.sendSampleTypeName,
              sampleType: v.sampleType,
              sampleTypeName: v.sampleTypeName,
              useSampleType: v.useSampleType,
              useSampleTypeName: v.useSampleTypeName,
              tatTime: v.tatTime,
              isInherit: v.isInherit
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleRowClick (row, event, column) {
      this.$refs.stepTable.clearSelection()
      this.$refs.stepTable.toggleRowSelection(row, !this.selectedRow.has(row.stepCode))
      let hasThisId = this.selectedRow.has(row.stepCode)
      this.selectedRow.clear()
      if (!hasThisId) {
        this.selectedRow.set(row.stepCode, row)
        this.handleSelectStepTemplate(1)
      } else {
        this.handleSelectStepTemplate(0)
      }
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleProductOrProjectDetail (data) {},
    handleSelectStepTemplate (type) {
      if (type) {
        let row = [...this.selectedRow.values()][0].realData
        this.procedureStepName = row.stepName
        this.detailTableData = []
        this.detailTableData.push({
          label: '工序名称：',
          value: row.stepName
        }, {
          label: '工序分类：',
          value: row.stepTypeName
        }, {
          label: '送样类型：',
          value: row.sendSampleTypeName
        }, {
          label: '样本类型：',
          value: row.sampleTypeName
        }, {
          label: '工序可用样本类型：',
          value: row.useSampleTypeName
        }, {
          label: '是否继承：',
          value: row.isInherit === 1 ? '是' : '否'
        }, {
          label: 'TAT时间：',
          value: row.tatTime
        })
      } else {
        this.procedureStepName = ''
        this.detailTableData = []
        // this.form = {
        //   stepName: '',
        //   stepTypeName: '',
        //   sendSampleTypeName: '',
        //   sampleTypeName: '',
        //   useSampleTypeName: '',
        //   tatTime: ''
        // }
      }
    },
    handleSaveDialogConfirm () {
      this.saveDialogVisible = false
      this.procedureStepName = ''
      this.detailTableData = []
      this.getData()
    },
    handleSaveDialogClose () {
      this.saveDialogVisible = false
    },
    cellStyle ({row, column, rowIndex, columnIndex}) {
      let style = {
        height: '40px',
        lineHeight: '40px'
      }
      if (columnIndex === 0) {
        style.background = '#f2f2f2'
      }
      return style
    },
    handleEditStepDetail () {
      if (this.selectedRow.size === 0) {
        this.$message.error('请选择一条数据')
      } else {
        let row = [...this.selectedRow.values()][0].realData
        this.saveDialogData = {
          stepCode: row.stepCode,
          stepName: row.stepName,
          stepType: row.stepType,
          stepTypeName: row.stepTypeName,
          sendSampleType: row.sendSampleType.split(',').map(v => Number(v)),
          sampleType: row.sampleType.split(',').map(v => Number(v)),
          useSampleType: row.useSampleType.split(',').map(v => Number(v)),
          tatTime: row.tatTime,
          isInherit: row.isInherit
        }
        this.saveDialogVisible = true
      }
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-dialog__body{
    padding: 10px 20px;
  }
  >>>.el-table th{
    background: #f2f2f2;
  }
  >>>.el-table, >>>.el-table thead{
    font-size: 12px;
  }
  .content{
    height: calc(100% - 10px);
    width: 100%;
    display: flex;
    .header{
      font-size: 14px;
      border-right: 2px solid #ffffff;
      padding-left: 10px;
      height: 40px;
      line-height: 40px;
    }
    .header::before{
      content: '';
      border-left: 3px solid #409EFF;
      margin-right: 10px;
    }
    .left {
      transition: width 0.28s;
      width: 340px;
      margin-right: 10px;
      height: 100%;
      .buttonGroup {
        overflow-x: hidden;
        display: flex;
        align-items: center;
        height: 40px;
      }
      >>>.el-table__header .el-checkbox {
        display: none;
      }
    }
    .right {
      min-width: calc(100% - 350px);
      flex: 1;
      height: 100%;
      .processName{
        padding-left: 10px;
        display: flex;
        align-items: center;
        height: 40px;
      }
    }
    .label{}
  }
</style>
