<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="70px"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="项目编码">
          <el-input v-model.trim="form.projectCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="产品类型">
          <el-input v-model.trim="form.productType" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="文库类型">
          <el-input v-model.trim="form.libraryType" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="流程名称">
          <el-input v-model.trim="form.processName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="交付类型">
          <el-select size="mini" v-model.trim="form.deliverType" class="form-width" placeholder="请选择" clearable>
            <el-option v-for="(item, index) in deliverTypeList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select size="mini" v-model.trim="form.fisVerify" class="form-width" placeholder="请选择" clearable>
            <el-option label="已审核" :value="1"></el-option>
            <el-option label="未审核" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建日期">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
<!--    <search-params-dialog-->
<!--      :pvisible.sync="searchDialogVisible"-->
<!--      @reset="handleReset"-->
<!--      @search="handleSearch">-->
<!--      <el-form-->
<!--        ref="form"-->
<!--        class="params-search-form"-->
<!--        :model="form"-->
<!--        label-width="80px"-->
<!--        label-suffix=":"-->
<!--        size="small"-->
<!--        label-position="top"-->
<!--        inline>-->
<!--        <el-form-item label="任务单编号">-->
<!--          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="提交时间">-->
<!--          <el-date-picker-->
<!--            v-model.trim="form.time"-->
<!--            class="form-long-width"-->
<!--            type="daterange"-->
<!--            value-format="yyyy-MM-dd HH:mm:ss"-->
<!--            start-placeholder="开始日期"-->
<!--            end-placeholder="结束日期"-->
<!--            :default-time="['00:00:00', '23:59:59']"-->
<!--          ></el-date-picker>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="项目名称">-->
<!--          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="实验样本 ">-->
<!--          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="吉因加编号">-->
<!--          <el-input v-model.trim="form.code" class="form-width" clearable placeholder="请输入"></el-input>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--    </search-params-dialog>-->
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('002018001', 'buttons')" type="primary" size="mini" @click="handleSetConfig">新增交付配置</el-button>
      <el-button v-if="$setAuthority('002018002', 'buttons')" type="primary" plain size="mini" @click="handleDelete">删除交付配置</el-button>
      <el-button v-if="$setAuthority('002018003', 'buttons')" type="primary" plain size="mini" @click="handleUpload">导入</el-button>
      <el-button v-if="$setAuthority('002018006', 'buttons')" type="primary" plain size="mini" @click="handleAudit">审核</el-button>
      <el-button v-if="$setAuthority('002018008', 'buttons')" type="primary" :loading="auditLoading" plain size="mini" @click="handleAuditCheck">批量审核</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" plain @click="handleReset">重置</el-button>
<!--      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">-->
<!--        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>-->
<!--      </el-badge>-->
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        :row-class-name="handleClassName"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" prop="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编码" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productType" label="产品类型" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="libraryType" label="文库类型" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deliverType" label="交付类型" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="isInsuranceText" label="是否全保" min-width="80" show-overflow-tooltip></el-table-column>
        <!--        审核状态-->
        <el-table-column prop="auditStatusText" label="审核状态" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
           <div :style="`color: ${scope.row.realData.auditStatus ? '#51C86B' : '#747474' }`">{{scope.row.auditStatusText}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="processName" label="流程名称" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="processParams" label="流程参数" min-width="240" show-overflow-tooltip></el-table-column>
<!--        <el-table-column prop="cleanProcessParams" label="cleandata流程参数" min-width="240" show-overflow-tooltip></el-table-column>-->
        <el-table-column prop="q30NStandard" label="Q30+N标准" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dataProductionStandard" label="数据产量标准（%）" min-width="130" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dataProductionStandardG" label="数据产量标准（G）" min-width="130" show-overflow-tooltip></el-table-column>
        <!--        公司名称-->
        <el-table-column prop="companyName" label="公司名称" min-width="80" show-overflow-tooltip></el-table-column>
        <!--        Company_English-->
        <el-table-column prop="companyEnglish" label="Company_Englis" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="creator" label="创建人" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="updator" label="更新人" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="updateTime" fixed="right" label="操作" min-width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="$setAuthority('*********', 'buttons')" class="link" style="margin-right: 10px" @click="handleSetConfig(scope.row)">编辑</span>
            <span v-if="$setAuthority('*********', 'buttons')" class="link" @click="handleSetAliyunConfig(scope.row)">云配置信息</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
          当前选中 {{ selectedRowsSize }} 条记录
        </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>

    <set-delivery-config-dialog :pvisible.sync="setConfigDialogVisible" :form-data="formData" @dialogConfirmEvent="getData"></set-delivery-config-dialog>
    <upload-dialog :pvisible.sync="uploadVisible" @dialogConfirmEvent="getData"></upload-dialog>
    <audit-config-dialog :pvisible.sync="auditConfigDialogVisible" :project-code="projectCode" :fid="fid" @dialogConfirmEvent="getData"></audit-config-dialog>
    <aliyun-config-dialog :pvisible.sync="aliyunConfigDialogVisible" :audit-status="auditStatus" :project-code="projectCode" @dialogCloseEvent="getData"></aliyun-config-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {
  auditCheckDeliveryConfig,
  deleteDeliveryConfig,
  getDeliveryConfigList
} from '../../../../api/deliveryManagement'
import SetDeliveryConfigDialog from './components/setDeliveryConfigDialog'
import uploadDialog from './components/uploadDialog'
import AuditConfigDialog from './components/auditConfigDialog.vue'
import AliyunConfigDialog from './components/aliyunConfigDialog.vue'

export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {AliyunConfigDialog, SetDeliveryConfigDialog, uploadDialog, AuditConfigDialog},
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      setConfigDialogVisible: false,
      uploadVisible: false,
      auditConfigDialogVisible: false,
      aliyunConfigDialogVisible: false,
      fid: null,
      auditStatus: null,
      formData: {},
      deliverTypeList: ['极致交付', '普通交付', '先下先交', '不交付', '慢交付'],
      projectCode: '',
      auditLoading: false,
      form: {
        projectCode: '',
        productType: '',
        libraryType: '',
        processName: '',
        deliverType: '',
        projectName: '',
        fisVerify: '',
        time: []
      }
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    getParams () {
      const time = this.form.time || []
      return {
        fprojectCode: this.formSubmit.projectCode,
        fproductType: this.formSubmit.productType,
        flibraryType: this.formSubmit.libraryType,
        fprocessName: this.formSubmit.processName,
        fdeliverType: this.formSubmit.deliverType,
        fprojectName: this.formSubmit.projectName,
        fisVerify: this.formSubmit.fisVerify,
        fcreateTimeStart: time[0],
        fcreateTimeEnd: time[1],
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getDeliveryConfigList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.records || []
        const booleanMap = {
          0: '否',
          1: '是'
        }
        rows.forEach((v, i) => {
          const item = {
            id: v.fid,
            projectCode: v.fprojectCode,
            productType: v.fproductType,
            libraryType: v.flibraryType,
            deliverType: v.fdeliverType,
            projectName: v.fprojectName,
            creator: v.fcreator,
            createTime: v.fcreateTime,
            auditStatus: v.fisVerify,
            auditStatusText: v.fisVerify ? '已审核' : '未审核',
            updator: v.fupdator,
            deliveryDelay: v.fslowDeliverDelay,
            processName: v.fprocessName,
            processParams: v.fprocessParams,
            q30NStandard: v.fq30NStandard,
            cleanProcessParams: v.fcleanProcessParams,
            isInsuranceText: booleanMap[v.fisInsurance],
            isInsurance: v.fisInsurance,
            isDelete: v.fisDelete,
            updateTime: v.fupdateTime,
            dataProductionStandard: v.fdataProductionStandard,
            dataProductionStandardG: v.fdataProductionStandardG,
            deliveryWay: v.fdeliveryWay,
            companyName: v.fcompanyName,
            fsingleDingdingWebhook: v.fsingleDingdingWebhook,
            fsingleDingdingPhone: v.fsingleDingdingPhone,
            companyEnglish: v.fcompanyEnglish
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 云配置信息
    handleSetAliyunConfig (row) {
      this.auditStatus = row.auditStatus
      this.projectCode = row.projectCode
      this.aliyunConfigDialogVisible = true
    },
    // 编辑配置
    handleSetConfig (row = {}) {
      // if (row.auditStatus) {
      //   this.$message.error('已审核的配置不能修改')
      //   return
      // }
      this.formData = row.realData
      this.setConfigDialogVisible = true
    },
    async handleDelete () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择要删除的记录')
        return
      }
      const ids = [...this.selectedRows.keys()]
      await this.$confirm('是否确认删除选中的记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let {res} = await awaitWrap(deleteDeliveryConfig({
        fids: ids.join(',')
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
        await this.getData()
      }
    },
    handleUpload () {
      this.uploadVisible = true
    },
    // 审核配置
    handleAudit () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条记录')
        return
      }
      const row = [...this.selectedRows.values()][0]
      if (row.realData.auditStatus) {
        this.$message.error('数据已审核，不可再次审核！')
        return
      }
      this.projectCode = row.projectCode
      this.fid = row.id
      this.auditConfigDialogVisible = true
    },
    async handleAuditCheck () {
      if (this.selectedRows.size < 2) {
        this.$message.error('请至少选择两条记录')
        return
      }
      const rows = [...this.selectedRows.values()]
      if (rows.some(row => row.realData.auditStatus)) {
        this.$message.error('存在已审核的数据，不可再次审核！')
        return
      }
      await this.$confirm(`是否确认对选中的${rows.length}条数据进行审核？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.auditLoading = true
      let {res} = await awaitWrap(auditCheckDeliveryConfig({
        fprojectDeliverConfigIdList: rows.map(item => item.id)
      }))
      this.auditLoading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('审核成功')
        await this.getData()
      }
    }
  }
}
</script>

<style scoped>

</style>
