<template>
  <div class="quality-control-task-export">
    <div class="container">
      <div class="left-container">
        <TaskListPanel
          :added-task-ids="addedTaskIds"
          @add-task="handleAddTask"
          @batch-add-tasks="handleBatchAddTasks"
        />
      </div>

      <div class="right-container">
        <SelectedTasksPanel
          :table-data="selectedTasks"
          @remove-task="handleRemoveTask"
          @batch-remove-tasks="handleBatchRemoveTasks"
          @sort-tasks="handleSortTasks"
          @export-complete="handleExportComplete"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TaskListPanel from './TaskListPanel.vue'
import SelectedTasksPanel from './SelectedTasksPanel.vue'

export default {
  name: 'QualityControlTaskExport',
  components: {
    TaskListPanel,
    SelectedTasksPanel
  },
  data () {
    return {
      // 已选择的任务单列表
      selectedTasks: [],
      // 存储已添加到右侧的任务单ID，用于左侧过滤（改为数组以支持响应式）
      addedTaskIds: []
    }
  },
  methods: {
    // 处理单个添加任务单
    handleAddTask (task) {
      this.selectedTasks.push(task)
      if (!this.addedTaskIds.includes(task.id)) {
        this.addedTaskIds.push(task.id)
      }
    },

    // 处理批量添加任务单
    handleBatchAddTasks (tasks) {
      tasks.forEach(task => {
        this.selectedTasks.push(task)
        if (!this.addedTaskIds.includes(task.id)) {
          this.addedTaskIds.push(task.id)
        }
      })
    },

    // 处理移除任务单
    handleRemoveTask (task) {
      this.selectedTasks = this.selectedTasks.filter(item => item.id !== task.id)
      const index = this.addedTaskIds.indexOf(task.id)
      if (index > -1) {
        // 使用 splice 方法移除元素，确保触发响应式更新
        this.addedTaskIds.splice(index, 1)
      }
    },

    // 处理批量移除任务单
    handleBatchRemoveTasks (tasks) {
      const taskIds = tasks.map(task => task.id)
      this.selectedTasks = this.selectedTasks.filter(item => !taskIds.includes(item.id))

      // 从 addedTaskIds 中移除这些任务ID
      taskIds.forEach(id => {
        const index = this.addedTaskIds.indexOf(id)
        if (index > -1) {
          this.addedTaskIds.splice(index, 1)
        }
      })
    },

    // 处理拖拽排序
    handleSortTasks ({ oldIndex, newIndex }) {
      const movedItem = this.selectedTasks.splice(oldIndex, 1)[0]
      this.selectedTasks.splice(newIndex, 0, movedItem)
    },

    // 处理导出完成
    handleExportComplete () {
      // 可以在这里添加导出完成后的逻辑
      console.log('导出完成')
    }
  }
}
</script>

<style lang="scss" scoped>
.quality-control-task-export {
  padding: 20px;
  height: 100%;
  background-color: #f5f7fa;

  .container {
    display: flex;
    gap: 16px;
    height: calc(100vh - 40px);

    .left-container, .right-container {
      width: 50%;
      padding: 16px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      display: flex;
      flex-direction: column;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .container {
      flex-direction: column;
      height: auto;

      .left-container, .right-container {
        flex: none;
        height: 50vh;
      }

      .left-container {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
