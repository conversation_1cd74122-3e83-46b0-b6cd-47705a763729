<template>
  <div>
    <el-dialog :visible.sync="visible" :before-close="handleClose" :close-on-click-modal="false" title="上传报告"
      width="40%" @opened="handleOpen">
      <el-upload class="upload-demo" drag ref="upload" :auto-upload="false" :action="uploadUrl" :file-list="fileList" :data="{ftype: type}"
        :headers="headers" :on-success="handleOnSuccess" :on-error="handleOnError" :on-progress="handleProgress"
        :before-upload="handleBeforeUpload" :before-remove="handleRemove" multiple accept="xlsx,xls">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div class="download-wrapper">
        <div>
          只能上传excel文件
        </div>
      </div>
      <div class="download-wrapper">
        <el-button :loading="downloadLoading" type="text" size="small" @click="handleTemplate">下载模版</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import Cookies from 'js-cookie'
import constants from '@/util/constants'
import {
  getTemplateApi
} from '@/api/experiment/index'
import {
  awaitWrap, readBlob, downloadFile, deepCopy, setDefaultEmptyValueForObject
} from '@/util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: {
      type: String,
      default: '1' // 1: 打断排单，2: 建库排单 3: 续index排单
    }
  },
  data () {
    return {
      loading: false,
      downloadLoading: false,
      form: {
        template: ''
      },
      fileList: [],
      headers: {
        token: Cookies.get('token')
      },
      files: [],
      uploadUrl: constants.JS_CONTEXT + '/experiment/lib_schedule/import_sample',
      onProgress: false, // 文件是否正在上传
      templates: [
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$refs.upload.clearFiles()
    },
    async handleConfirm () {
      const files = this.$refs.upload.uploadFiles
      if (files.length === 0) {
        this.$message.error('请选择上传文件')
        return
      }
      this.$refs.upload.submit()
      this.loading = true
    },
    async handleTemplate () {
      this.downloadLoading = true
      const { res } = await awaitWrap(getTemplateApi({
        ftype: this.type
      }))
      if (res) {
        const { err } = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      // 只能上次excel文件
      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isExcel) {
        this.$message.error('只能上传excel文件')
        this.loading = false
        return false
      }
      return true
    },
    // 提交成功回调
    async handleOnSuccess (res = {}) {
      this.loading = false
      if (res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        const samples = []
        if (data.errorLogDTOS && data.errorLogDTOS.length > 0) {
          this.$showErrorDialog({tableData: data.errorLogDTOS})
          this.$refs.upload.clearFiles()
          return
        }
        this.$message.success('上传成功')
        const scheduleSampleVos = data.scheduleSampleVos || []
        scheduleSampleVos.forEach(item => {
          const mappedItem = {
            id: item.flibNum || (item.fdnaNum + item.fprobe),
            // 按照要求映射字段
            nucleicAcidCode: item.fdnaNum, // 核酸编号
            sampleType: item.fsampleType, // 样本类型
            productCode: item.fproCode, // 产品项目编号
            productName: item.fproName, // 产品项目名称
            libraryType: item.flibType, // 文库类型
            nucleicAcidConcentration: item.fdnaConcentration, // 核酸浓度
            nucleicAcidVolume: item.fdnaVolume, // 核酸体积
            expectedTime: item.fdeliverTime, // 预计付时间
            libScheduleId: item.flibScheduleId, // 保存原始ID用于后续操作
            fbreakPlateNum: item.fbreakPlateNum, // 自动板号
            importType: 1, // 导入
            // 保留其他原始字段
            ftype: this.type,
            fpreDeliverTime: item.fpreDeliverTime,
            fsequenceType: item.fsequenceType,
            fspecialRemark: item.fspecialRemark,
            fsequencingPlatform: item.fsequencingPlatform,
            fprobe: item.fprobe,
            fhybridBaseNum: item.fhybridBaseNum,
            fdataSize: item.fdataSize,
            findexNum: item.findexNum,
            findexSerialNum: item.findexSerialNum,
            fconnectorType: item.fconnectorType,
            flibNum: item.flibNum,
            fdnaLevel: item.fdnaLevel,
            fdnaTotal: item.fdnaTotal,
            fsrpTaskOrderNum: item.fsrpTaskOrderNum,
            fproperty: item.fproperty,
            flibCount: item.flibCount,
            fdv200: item.fdv200,
            fseqBatch: item.fseqBatch,
            fseqTime: item.fseqTime,
            fseqType: item.fseqType,
            fseqLane: item.fseqLane,
            foriginNum: item.foriginNum,
            fcrossPoolingLib: item.fcrossPoolingLib,
            fpoolingLib: item.fpoolingLib,
            fcopyPath: item.fcopyPath,
            fsequenatorRequire: item.fsequenatorRequire,
            fsequenatorRun: item.fsequenatorRun,
            frealDnaNum: item.frealDnaNum,
            fisUrgent: item.fisUrgent
          }
          // 创建该项目的深拷贝，用于保留原始数据不变
          mappedItem.realData = deepCopy(mappedItem)
          // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
          setDefaultEmptyValueForObject(mappedItem)
          samples.push(mappedItem)
        })
        // 这里可以添加后续处理逻辑
        this.$emit('dialogConfirmEvent', samples)
        this.handleClose()
      } else {
        const data = res.data || {}
        if (data.errorLogDTOS && data.errorLogDTOS.length > 0) {
          this.$showErrorDialog({tableData: data.errorLogDTOS})
          this.submitLoading = false
        } else {
          this.$message.error(res.message)
        }
      }
    },
    async handleConfirmOption (message) {
      const result = await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
      return result
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.loading = false
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    async handleRemove (file) {
      if (file && file.status === 'success') {
        await this.handleConfirmOption('是否删除？')
        this.onProgress = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-upload-dragger {
  width: 100%;
}

/deep/ .el-upload {
  width: 100%;
}

.download-wrapper {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.tips {
  color: $color;
}

.label {
  display: inline-block;
  width: 110px;
  padding: 0 12px 0 0;
  text-align: right;
}
</style>
