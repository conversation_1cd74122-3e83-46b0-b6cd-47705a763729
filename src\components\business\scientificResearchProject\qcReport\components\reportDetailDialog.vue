<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :modal="true"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="查看报告"
      width="70%"
      @open="handleOpen"
    >
      <div class="flex search">
        <div>
          <el-button v-if="$setAuthority('017001004', 'buttons')" type="primary" size="mini" @click="handleSendEmail">发送报告</el-button>
          <el-button v-if="$setAuthority('017001005', 'buttons')" type="primary" size="mini" @click="handleExport">导出报告</el-button>
          <el-button v-if="$setAuthority('017001006', 'buttons')" type="primary" size="mini" @click="handleUpload">上传报告</el-button>
        </div>
          <div class="flex">
            <el-select v-model.trim="searchType" size="mini" clearable placeholder="请选择">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in optionsList">
              </el-option>
            </el-select>
            <el-input
              v-model.trim="searchValue"
              size="mini"
              placeholder="请输入"
              clearable
              @keyup.enter.native="getData([])"></el-input>
          </div>
      </div>
      <el-table
        ref="table"
        :data="tableData"
        class="table"
        height="400px"
        border
        @select="handleSelect"
        @row-click="handleRowClick"
        @select-all="handleSelectAll"
        @filter-change="handlerFilter">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="reportName" label="报告名称" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column :filters="filters"
                         prop="statusText"
                         label="报告状态"
                         min-width="120"
                         show-overflow-tooltip
                         column-key="status"
                         >
          <template slot-scope="scope">
              <span>{{scope.row.statusText}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="submitor" label="提交人" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="buildTime" label="生成时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sendTime" label="发送时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="index" label="操作" min-width="180">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status !== 3 && $setAuthority('017001007', 'buttons')" type="text" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <email-confirm-dialog :pvisible.sync="rVisible" :report-id="reportId" @dialogConfirmEvent="getData"></email-confirm-dialog>
    <upload-report-dialog :pvisible.sync="uVisible" :order-id="orderId" @dialogConfirmEvent="getData"></upload-report-dialog>
    <audit-report-dialog :pvisible.sync="aVisible" :type="type" :report-id="reportId" @dialogConfirmEvent="getData"></audit-report-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'
import EmailConfirmDialog from './emailConfirmDialog'
import AuditReportDialog from './auditReportDialog'
import util from '../../../../../util/util'
import UploadReportDialog from './uploadReportDialog'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {
    UploadReportDialog,
    EmailConfirmDialog,
    AuditReportDialog
  },
  props: {
    orderId: {
      type: [String, Number]
    }
  },
  watch: {
    searchType: function (value, oldValue) {
      if (!value) {
        this.searchValue = ''
        this.getData()
      }
    }
  },
  data () {
    return {
      rVisible: false,
      uVisible: false,
      aVisible: false,
      selectedRows: new Map(),
      reportId: 0,
      type: 0,
      filters: [
        // {text: '待审核', value: 0},
        // {text: '审核通过', value: 1},
        // {text: '驳回', value: 2},
        {text: '上传成功', value: 4},
        {text: '已发送', value: 3}
      ],
      searchType: '',
      searchValue: '',
      tableData: [],
      optionsList: [
        {
          label: '报告名称',
          value: 'reportName'
        },
        // {
        //   label: '报告编号',
        //   value: 'reportCode'
        // },
        {
          label: '提交人',
          value: 'submitor'
        }
        // , {
        //   label: '审核人',
        //   value: 'auditor'
        // }
      ]
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    // 删除报告：用于删除报告状态为“上传成功”的文件。状态为已发送的文件不可删除，提示：不能删除已发送的文件！
    async handleDelete (id) {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.status === 3) {
        this.$message.error('不能删除已发送的文件！')
      }
      // await this.$confirm('删除后不可恢复，确定吗?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // })
      let {code, message} = await this.$ajax({
        url: '/order/report/delete_report',
        data: {
          reportId: id
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.getData()
        this.$message.success('删除成功')
      } else {
        this.$message.error(message)
      }
    },
    getData (status) {
      status = status || []
      let data = {
        orderId: this.orderId,
        status: status.join(',')
      }
      if (this.searchType && this.searchValue) {
        data = {
          orderId: this.orderId,
          [this.searchType]: this.searchValue,
          status: status.join(',')
        }
      }
      this.$ajax({
        url: '/order/report/get_report_list_by_order',
        data: data,
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows = new Map()
          this.tableData = []
          let data = res.data || []
          data.forEach((v) => {
            let status = this.filters.find(vv => vv.value === v.fstatus) || {}
            let item = {
              id: v.fid,
              reportName: v.freportName,
              reportCode: v.freportCode,
              status: v.fstatus,
              rejectNote: v.frejectNote,
              statusText: status.text,
              sendTime: v.fsendTime,
              submitor: v.fsubmitor,
              buildTime: v.fbuildTime,
              auditor: v.fauditor,
              auditTime: v.fauditTime
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    /**
     * 状态筛选
     * @param filters 筛选过滤的状态
     */
    handlerFilter (filters) {
      this.getData(filters.status)
    },
    /**
     * 发送报告
     */
    async handleSendEmail () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      let status = rows.some(v => v.status !== 4 && v.status !== 3)
      if (status) {
        this.$message.error('请勾选报告状态为审核通过/已发送的报告！')
        return
      }
      let sendFiles = rows.filter(v => v.status === 3) || ''
      if (sendFiles.length > 0) {
        let fileNames = sendFiles.map(v => v.reportName).join('<br/>')
        await this.$confirm(`${fileNames}已发送, 确认继续发送么?`, '提示', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      this.reportId = rows.map(v => v.id).join(',')
      this.rVisible = true
    },
    // 导出报告: 已审核通过/已发送
    handleExport () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      // let status = rows.some(v => v.status !== 1 && v.status !== 3)
      let status = rows.some(v => v.status !== 4 && v.status !== 3)
      if (status) {
        // this.$message.error('请勾选报告状态为审核通过/已发送的报告！')
        this.$message.error('请勾选报告状态为上传成功/已发送的报告！')
        return
      }
      this.$ajax({
        url: '/order/report/export_report',
        data: {
          reportIds: rows.map(v => v.id).join(',')
        },
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    // 上传报告
    handleUpload () {
      this.uVisible = true
    },
    /**
     * 查看或审核报告
     * @param type 0 查看 1 报告
     * @param id 报告id
     */
    handleAudit (type, id) {
      this.type = type
      this.reportId = id
      this.aVisible = true
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped>
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search {
  padding: 10px;
}
/deep/ .el-message-box__message p {
  word-break: break-all;
}
</style>
