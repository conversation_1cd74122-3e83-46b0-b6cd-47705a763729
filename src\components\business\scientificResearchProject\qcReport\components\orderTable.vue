<template>
  <div>
    <div class="flex search">
      <el-button type="primary" size="mini" @click="handleToDetail">查看</el-button>
      <div class="flex">
        <el-select v-model.trim="searchType" style="width: 144px!important" size="mini" clearable placeholder="请选择">
          <el-option
            :key="item.value"
            :label="item.label"
            :value="item.value"
            v-for="item in optionsList">
          </el-option>
        </el-select>
        <el-select
          v-model.trim="searchValue"
          v-if="searchType === 'orderType'"
          size="mini"
          style="width: 256px"
          clearable
          placeholder="请选择"
          @change="handleSearch">
          <el-option
            :key="index"
            :label="item"
            :value="index * 1"
            v-for="(item, index) in typeOptions">
          </el-option>
        </el-select>
        <el-input
          v-model.trim="searchValue"
          v-else
          size="mini"
          style="width: 256px"
          placeholder="请输入"
          clearable
          @keyup.enter.native="handleSearch"></el-input>
      </div>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      class="table"
      border
      style="width: 100%"
      height="calc(100vh - 65px - 80px - 40px - 20px - 43px - 32px)"
      @select="handleSelect"
      @row-click="handleRowClick">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="projectName" label="项目名称" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="detectType" label="检测项目" min-width="140" show-overflow-tooltip></el-table-column>
<!--      <el-table-column prop="id" label="子订单编号" min-width="150" show-overflow-tooltip></el-table-column>-->
      <el-table-column prop="orderCode" label="订单编号" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="type" label="订单类型" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="projectCode" label="项目编号" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="customerName" label="客户姓名" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sendUnit" label="送检单位" min-width="140" show-overflow-tooltip></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <report-detail-dialog :pvisible.sync="visible" :order-id="orderId" @dialogCloseEvent="handleDialogClose"></report-detail-dialog>
  </div>
</template>

<script>
import ReportDetailDialog from './reportDetailDialog'
import mixins from '../../../../../util/mixins'
import util from '../../../../../util/util'
export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {ReportDetailDialog},
  mounted () {
    this.getData()
  },
  watch: {
    searchType: function (value) {
      if (!value) {
        this.searchValue = ''
        this.getData()
      }
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      searchType: '',
      searchValue: '',
      visible: false,
      typeOptions: {
        1: 'illumina文库订单',
        2: 'MGI文库订单',
        3: '组织核酸样本订单'
      },
      optionsList: [
        // {
        //   label: '子订单编号',
        //   value: 0
        // },
        {
          label: '订单编号',
          value: 'orderCode'
        }, {
          label: '订单类型',
          value: 'orderType'
        }, {
          label: '检测项目',
          value: 'detectType'
        }, {
          label: '项目编号',
          value: 'projectCode'
        }, {
          label: '项目名称',
          value: 'projectName'
        }, {
          label: '客户姓名',
          value: 'customerName'
        }
        // , {
        //   label: '送检单位',
        //   value: 7
        // }
      ],
      tableData: [],
      orderId: 0 // 选择订单id
    }
  },
  methods: {
    handleSearch () {
      this.currentPage = 1
      this.getData()
    },
    // 详情页关闭可能更新样本表格，故样本表格重新获取数据
    handleDialogClose () {
      let row = [...this.selectedRows.values()][0]
      this.$emit('selectOrderEvent', row.id)
    },
    getData () {
      this.selectedRows = new Map()
      let data = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      if (this.searchType && this.searchValue) {
        data = {
          [this.searchType]: this.searchValue,
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      this.$ajax({
        url: '/order/report/get_order_list_by_report',
        data: data,
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$emit('getNewDataEvent')
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.rows || []
          data.forEach((v) => {
            let item = {
              id: v.orderId,
              orderCode: v.orderCode,
              type: this.typeOptions[v.type],
              detectType: v.detectType,
              projectName: v.projectName,
              projectCode: v.projectCode,
              customerName: v.customerName,
              sendUnit: v.sendUnit
            }
            if (v.type === 3) {
              item.detectType = '-'
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 查看详情
    handleToDetail () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.orderId = row.id
      this.visible = true
    },
    // 点击行
    handleRowClick (row) {
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      if (!this.selectedRows.has(row.id)) {
        this.$refs.table.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      if (this.selectedRows.has(row.id)) {
        this.selectedRows.delete(row.id)
      } else {
        // this.$emit('selectOrderEvent', row.id)
        this.selectedRows.set(row.id, row)
      }
    }
  }
}
</script>

<style scoped>
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search {
  padding: 10px;
}
</style>
