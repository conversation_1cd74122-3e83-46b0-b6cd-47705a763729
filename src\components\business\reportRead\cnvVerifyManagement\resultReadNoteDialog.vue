<template>
  <div>
    <el-dialog
      title="结果解读"
      :modal="false"
      :visible.sync="visible"
      width="55%" :close-on-click-modal="false"
      @open="handleOpen"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="form"  size="mini" label-width="130px" label-suffix=":" :rules="rules">
        <el-form-item label="状态" prop="remarkstatus">
          <el-select
            v-model.trim="form.remarkstatus"
            clearable size="mini"
            filterable
            placeholder="请选择状态">
            <el-option
              v-for="(item, index) in status"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="解读备注" prop="readRemarks">
          <el-input
            v-model.trim="form.readRemarks"
            type="textarea"
            clearable
            maxlength="1000"
            show-word-limit
            :autosize="{ minRows: 4, maxRows: 4}"
            placeholder="请输入解读备注"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" type="primary" :loading="loading" @click="handleConfirm">保存</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'

export default {
  name: 'resultReadNoteDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array
    },
    remarkstatus: {
      type: Number
    }
  },
  data () {
    return {
      form: {
        remarkstatus: '',
        readRemarks: ''
      },
      loading: false,
      rules: {
        remarkstatus: [
          {required: true, message: '请选择解读状态', trigger: ['blur']}
        ]
      },
      status: [
        {
          label: '已处理',
          value: 2
        },
        {
          label: '暂缓处理',
          value: 3
        }
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        // 判断是否是未处理数据
        if (this.remarkstatus === 0) {
          return
        }
        this.getReadMark()
      })
    },
    // 获取解读备注回显数据
    getReadMark () {
      this.$ajax({
        url: '/read/cnv/get_cnv_readmark_status',
        data: {
          ids: this.ids.join(',')
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.form = {
            remarkstatus: res.data.remarkstatus,
            readRemarks: res.data.readRemarks
          }
        }
      })
    },
    // 解读备注
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/read/cnv/update_interpreting_remarks',
            data: {
              ids: this.ids.join(','),
              remarkstatus: this.form.remarkstatus,
              readRemarks: this.form.readRemarks
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
