import { myAjax } from '../../util/ajax'

/**
 * 获取交付订单列表
 * @param RAP2 http://************:3000/repository/editor?id=37&mod=205718&itf=5799408
 */
export function getDeliverOrderList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/get_cos_order_list',
    data: data,
    ...options
  })
}

/**
 * 获取交付批次列表
 * @param RAP2 http://************:3000/repository/editor?id=37&mod=205718&itf=5799429
 */
export function getSubDeliverOrderList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/get_cos_batch_detail',
    data: data,
    ...options
  })
}

/**
 * 导出订单数据
 * @param RAP2 http://************:3000/repository/editor?id=37&mod=205718&itf=5799410
 */
export function exportOrderData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/export_cos_deliver_order',
    responseType: 'blob',
    data: data,
    ...options
  })
}

/**
 * 更改交付状态
 * @param RAP2 http://************:3000/repository/editor?id=37&mod=205718&itf=5799409
 */
export function updateDeliverStatus (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/update_cos_deliver_order',
    data: data,
    ...options
  })
}
/**
 * 获取交付批次详情
 * @param RAP2 http://************:3000/repository/editor?id=37&mod=205718&itf=5799436
 */
export function getSubDeliverOrderInfoList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/get_details',
    data: data,
    ...options
  })
}

/**
 * 标记数据
 * @param RAP2 http://************:3000/repository/editor?id=37&mod=205718&itf=5799427
 */
export function saveSign (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/mark_cos_deliver_order',
    data: data,
    ...options
  })
}

/**
 * 获取项目交付配置列表
 * @param data http://************:3000/repository/editor?id=37&mod=205717&itf=5799389
 * @param options
 * @returns {*}
 */
export function getDeliveryConfigList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/deliver_config/get_list',
    data: data,
    ...options
  })
}
/**
 * 设置交付配置
 * @param data http://************:3000/repository/editor?id=37&mod=205717&itf=5799389
 * @param options
 * @returns {*}
 */
export function setDeliveryConfig (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/deliver_config/save_deliver_config',
    data: data,
    ...options
  })
}

/**
 * 设置交付配置
 * @param data http://************:3000/repository/editor?id=37&mod=205717&itf=5799389
 * @param options
 * @returns {*}
 */
export function deleteDeliveryConfig (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/deliver_config/delete_config',
    data: data,
    ...options
  })
}

/**
 * 下载模板
 * @param data http://************:3000/repository/editor?id=37&mod=205717&itf=5799444
 * @param options
 * @returns {*}
 */
export function downloadTemplate (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/deliver_config/download_excel_template',
    responseType: 'blob',
    data: data,
    ...options
  })
}

export function getDataCountList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/cos_deliver_batch_data_add',
    data: data,
    ...options
  })
}

export function splitDelivery (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_deliver_order/rebuild_cos_deliver_order',
    data: data,
    ...options
  })
}

/**
 * 获取云配置信息
 * @param data http://************:3000/repository/editor?id=37&mod=205717&itf=5799389
 * @param options 请求参数
 */
export function getDeliveryAliyunConfigList (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/aliyun_config/get_list',
    data: data,
    ...options
  })
}

/**
 * 获取所有的实验室配置
 * @param fexperimentRegion 实验室区域
 * @param options
 * @returns {Promise<void>}
 */
export function getDeliverType (fexperimentRegion, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/aliyun_config/get_experiment_config',
    data: {
      fexperimentRegion
    }
  })
}

/**
 * 更新云配置信息
 * @param data http://************:3000/repository/editor?id=37&mod=205717&itf=5799743
 * @param options
 * @returns {Promise<void>}
 */
export function setDeliveryAliyunConfig (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/aliyun_config/update_aliyun_config',
    data: data,
    ...options
  })
}

export function getDeliveryConfigDetail (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/deliver_config/get_verify_list',
    data: data,
    ...options
  })
}

export function auditDeliveryConfig (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/deliver_config/verify_config',
    data: data,
    ...options
  })
}

export function auditCheckDeliveryConfig (data, options = {}) {
  return myAjax({
    url: '/experiment/deliver_config/batch_verify_config',
    data: data,
    ...options
  })
}

export function addLab (data, options = {}) {
  return myAjax({
    url: '/experiment/aliyun_config/add_aliyun_config',
    data: data,
    ...options
  })
}

/**
 * 获取交付订单样本列表
 * @param {*} data
 */
export function getDeliverySampleList (data, options = {}) {
  return myAjax({
    url: '/experiment/aliyun_config/add_aliyun_config',
    data: data,
    ...options
  })
}

/**
 * http://************:3000/repository/editor?id=37&mod=205718&itf=5800245
 * 获取个性化样本列表
 */
export function getPersonalSampleList (data, options = {}) {
  return myAjax({
    url: '/experiment/cos_deliver_order/get_personalize_details',
    data: data,
    ...options
  })
}

/**
 * 下载个性化模板
 * URL_ADDRESS * http://************:3000/repository/editor?id=37&mod=205718&itf=5800246
 */
export function downloadPersonalTemplate (data, options = {}) {
  return myAjax({
    url: '/experiment/cos_deliver_order/download_excel_template',
    method: 'get',
    responseType: 'blob',
    data: data,
    ...options
  })
}

export function savePersonal (data, options = {}) {
  return myAjax({
    url: '/experiment/cos_deliver_order/cos_deliver_personalize',
    data: data,
    ...options
  })
}
