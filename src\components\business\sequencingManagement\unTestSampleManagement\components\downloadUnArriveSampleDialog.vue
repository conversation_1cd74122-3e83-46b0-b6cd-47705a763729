<script>
import mixins from '../../../../../util/mixins'
import Cookies from 'js-cookie'
import {awaitWrap, dateFormatter, downloadFile, getSessionInfo, readBlob} from '@/util/util'
import {downUnInspection} from '@/api/sequencingManagement/unTestSampleApi'
import DatePicker from '../../../../common/datePicker.vue'

export default {
  name: 'downloadUnArriveSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {DatePicker},
  data () {
    return {
      loading: false,
      areaList: JSON.parse(Cookies.get('labOptions') || '').filter(v => (getSessionInfo('currentLab') || []).includes(v.value)).filter(v => v.label !== '苏州'),
      form: {
        time: '',
        areaList: []
      },
      rules: {
        areaList: [
          {required: true, message: '请选择片区', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      if (this.areaList.length === 1) {
        this.form.areaList = this.areaList.map(v => v.value)
      }
      // 设置默认时间 18:00
      this.form.time = [dateFormatter(new Date(`${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate() - 1} 18:00:00`)), '']
    },
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      const time = this.form.time || []
      let options = {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      }
      this.form.areaList = this.form.areaList.map(v => {
        return options[v]
      })
      const {res} = await awaitWrap(downUnInspection({
        forderTimeStart: time[0],
        forderTimeEnd: time[1],
        forderProductionArea: this.form.areaList
      }))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.loading = false
    }
  }
}
</script>

<template>
  <el-dialog
    v-drag-dialog
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="visible"
    :before-close="handleClose"
    title="未到样信息下载"
    width="650px"
    @opened="handleOpen">
    <el-form ref="form" :model="form" :rules="rules" label-width="125px" label-suffix=":" label-position="left" size="small">
      <el-form-item label="请筛选订单时间" prop="time">
        <date-picker v-model="form.time"></date-picker>
      </el-form-item>
      <el-form-item label="请选择订单片区" prop="areaList">
        <el-checkbox-group v-model="form.areaList">
          <el-checkbox v-for="(item) in areaList" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
