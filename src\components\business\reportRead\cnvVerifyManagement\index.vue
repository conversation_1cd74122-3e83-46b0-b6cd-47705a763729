<template>
  <div>
    <!--搜索-->
    <div class="search-form search">
      <el-input
        v-model="form.sampleNums"
        type="textarea"
        clearable
        style="width: 200px"
        :autosize="{ minRows: 4, maxRows: 4}"
        placeholder="样本编号精准查找"></el-input>
      <el-form ref="form" style="flex: 1" label-position="right" label-width="140px" @keyup.enter.native="handleSearch">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="样本编号">
              <el-input
                v-model.trim="form.sampleNum"
                clearable
                size="mini" placeholder="请输入样本编号"></el-input>
            </el-form-item>
            <el-form-item label="验证提交人">
              <el-input v-model.trim="form.checkCreator" clearable size="mini" placeholder="请输入验证提交人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <div class="flex">
              <el-form-item label="验证结果">
                <el-select v-model.trim="form.testResult" clearable size="mini" filterable multiple collapse-tags placeholder="请选择验证结果">
                  <el-option
                    v-for="(item, index) in resultList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model.trim="form.remarkstatus" clearable size="mini" filterable multiple collapse-tags placeholder="请选择状态">
                  <el-option
                    v-for="(item, index) in status"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item>
                <div slot="label">
                  <el-select v-model.trim="form.dateKey" size="mini"  clearable>
                    <el-option
                      v-for="(item, index) in dateSelectOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <el-date-picker
                  v-model.trim="form.time"
                  type="daterange"
                  clearable
                  size="mini"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
              <div class="content">
                <div class="buttonGroup">
                  <el-button v-if="$setAuthority('006004001', 'buttons')" type="primary" size="mini" plain @click="handleReadNote">解读备注</el-button>
                  <el-button type="primary" size="mini" plain @click="handleSearch">查询</el-button>
                  <el-button type="primary" size="mini" plain @click="handleReset">重置</el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!--表格-->
    <div class="table-wrapper">
      <el-table
        ref="table"
        class="table"
        size="mini"
        :data="tableData"
        height="calc(100vh - 74px - 40px - 126px - 32px)"
        border
        style="width: 100%"
        @sort-change="handleTableSort"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column fixed="left" type="selection" width="45" ></el-table-column>
        <el-table-column fixed="left" type="index" label="序号" :index="indexMethod" ></el-table-column>
        <el-table-column show-overflow-tooltip prop="readRemarks" label="解读备注" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="remarkstatusText" label="状态" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="testResult" label="验证结果" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="sampleNum" label="样本编号" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="sampleType" label="样本类型" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="gene" label="Gene" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="exon" label="Exon" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="transcript" label="Transcript" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="status" label="Status" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="chr" label="Chr" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="start" label="Start" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="end" label="End" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="statusRatio" label="StatusRatio" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="validationSubmitter" label="验证提交人" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="submissionVerificationTime" sortable="custom" label="提交验证时间" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="resultUploadTime" sortable="custom" label="结果上传时间" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="readRemarkReviser" label="修改人" min-width="120"></el-table-column>
        <el-table-column show-overflow-tooltip prop="readRemarkReviseTime" sortable="custom" label="修改时间" min-width="120"></el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        :total="totalPage">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </div>
    <!--结果解读备注-->
    <result-read-note-dialog
      :pvisible.sync="resultReadNoteVisible"
      :ids="ids"
      :remarkstatus="remarkstatus"
      @dialogConfirmEvent="handleSearch"/>
  </div>
</template>

<script>
import util from '../../../../util/util'
import mixins from '../../../../util/mixins'
import ResultReadNoteDialog from './resultReadNoteDialog'

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {ResultReadNoteDialog},
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: [],
      form: {
        sampleNums: '', // 样本编号
        sampleNum: '', // 样本编号
        testResult: [], // 验证结果
        checkCreator: '', // 验证提交人
        remarkstatus: [], // 状态
        time: [],
        dateKey: 0 // 0 : 提交验证时间 1: 结果上传时间
      },
      selectedRows: new Map(),
      needSortBackendField: { // 需要排序的前台对应的后台地址
        submissionVerificationTime: 'commitTimeSortField', // 寄件地址
        resultUploadTime: 'uploadTimeSortField', // 医院
        readRemarkReviseTime: 'remarkModifySortField' // 寄件时间
      },
      resultReadNoteVisible: false,
      ids: [],
      remarkstatus: null,
      resultList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '野生型',
          value: 1
        },
        {
          label: '杂合缺失型',
          value: 2
        },
        {
          label: '纯合缺失型',
          value: 3
        },
        {
          label: '扩增型',
          value: 4
        },
        {
          label: '未知',
          value: 5
        }
      ],
      status: [
        {
          label: '全部',
          value: 1
        },
        {
          label: '已处理',
          value: 2
        },
        {
          label: '暂缓处理',
          value: 3
        },
        {
          label: '未处理',
          value: 0
        }
      ],
      dateSelectOptions: [
        {label: '提交验证时间', value: 0},
        {label: '结果上传时间', value: 1}
      ]
    }
  },
  methods: {
    // 索引更新
    indexMethod (index) {
      return (this.currentPage - 1) * this.pageSize + index + 1
    },
    // 查询
    handleSearch () {
      this.currentPage = 1
      this.getData()
    },
    // 重置
    handleReset () {
      this.form = {
        sampleNums: '',
        sampleNum: '',
        testResult: [],
        checkCreator: '',
        remarkstatus: [],
        time: [],
        dateKey: 0
      }
      this.handleSearch()
    },
    // 排序
    handleTableSort ({ prop, order }) {
      if (order) {
        this.form.sortFieldVo = {
          sortField: prop,
          isDesc: order === 'descending'
        }
      } else {
        this.form.sortFieldVo = {}
      }
      this.handleSearch()
    },
    // 设置请求参数
    setOptions () {
      let formSubmit = util.deepCopy(this.form)
      // 重置后赋值为数组
      formSubmit.time = formSubmit.time || []
      formSubmit.testResult = formSubmit.testResult || []
      formSubmit.remarkstatus = formSubmit.remarkstatus || []
      let params = {
        sampleNums: formSubmit.sampleNums.replace(/\s+|，/g, ',').split(',').filter(v => v).join(','),
        sampleNum: formSubmit.sampleNum,
        ftestResult: formSubmit.testResult.join(','),
        checkCreator: formSubmit.checkCreator,
        remarkstatus: formSubmit.remarkstatus.join(','),
        sortFieldVo: formSubmit.sortFieldVo
      }
      if (this.form.dateKey === 0) {
        params.commitCheckStartTime = formSubmit.time[0]
        params.commitCheckEndTime = formSubmit.time[1]
      }
      if (this.form.dateKey === 1) {
        params.resultUploadStartTime = formSubmit.time[0]
        params.resultUploadEndTime = formSubmit.time[1]
      }
      return {
        url: '/read/cnv/embryoid_line_cnv_data',
        data: {
          pageVo: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          ...params
        },
        loadingDom: '.table'
      }
    },
    // 获取cnv验证数据
    getData () {
      let options = this.setOptions()
      this.$ajax(options).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          let {data = {}} = res
          let {rows = []} = data
          this.tableData = []
          this.totalPage = data.total
          console.log(rows)
          // let result = this.resultList.find(vv => vv.value === vv.testResult) || {}
          rows.forEach(v => {
            let status = this.status.find(vv => vv.value === v.remarkstatus) || {}
            let item = {
              id: v.fid,
              readRemarks: v.readRemarks,
              remarkstatus: v.remarkstatus,
              remarkstatusText: status.label || '未处理',
              testResult: v.ftestResult,
              // testResultText: result.label,
              sampleNum: v.sampleNum,
              sampleType: v.sampleType,
              gene: v.gene,
              exon: v.exon,
              transcript: v.transcript,
              status: v.status,
              chr: v.chr,
              start: v.start,
              end: v.end,
              statusRatio: v.statusRatio,
              validationSubmitter: v.validationSubmitter,
              submissionVerificationTime: v.submissionVerificationTime,
              resultUploadTime: v.resultUploadTime,
              readRemarkReviser: v.readRemarkReviser,
              readRemarkReviseTime: v.readRemarkReviseTime
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 结果解读
    handleReadNote () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      if (!this.checkReadNote(rows)) {
        this.$message.error('所选数据存在不同的解读备注内容，请分开处理')
        return
      }
      this.resultReadNoteVisible = true
      this.ids = [...this.selectedRows.keys()]
      this.remarkstatus = rows[0].remarkstatus
    },
    /**
     * 检测解读备注是否完全一致 => 状态”及“解读备注”
     * @param rows 选中的数据
     * @return {boolean} true: 全部一致 false： 不一致
     */
    checkReadNote (rows = []) {
      // 是否选择多条数据
      if (rows.length <= 1) {
        return true
      }
      // 判断是否所有数据是否是第一次操作
      if (!rows.some(v => v.remarkstatus !== 0)) {
        return true
      }
      let remarkStatus = rows[0].remarkstatus
      let readRemarks = rows[0].readRemarks
      // 检测解读备注是否完全一致 => 状态”及“解读备注”
      return !(rows.some(v => !(v.remarkstatus === remarkStatus && v.readRemarks === readRemarks)))
    },
    // 点击行
    handleRowClick (row) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
}
.content{
  margin-left: 20px;
  background-color: #ffffff;
  .buttonGroup{
    display: flex;
    padding: 3px 0;
    //align-items: center;
  }
}
.search {
  display: flex
}

/deep/ .el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 100% !important;
}
</style>
