<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="750px"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" v-if="visible" label-width="120px" size="mini" label-suffix="：" inline>
        <el-form-item label="吉因加编号" prop="geneplusNum">
          <el-input v-model.trim="form.geneplusNum" disabled class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本名称" prop="sampleName">
          <el-input v-model.trim="form.sampleName" maxlength="50" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="物种">
          <el-input v-model.trim="form.species" class="form-width" maxlength="20"  placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item :rules="isCoreSpun ? rules.baseBalance : [{required: false}]" label="样本类型" prop="sampleType">
          <el-select v-model="form.sampleType" class="form-width" placeholder="请选择">
            <el-option label="组织样本" value="组织样本"></el-option>
            <el-option label="核酸类样本" value="核酸类样本"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="form.sampleType === '组织样本'">
          <div class="form-item">
            <div class="form-title">需提取样本填写</div>
            <el-form-item label="组织样本类型" prop="tissueSampleType">
              <el-select v-model="form.tissueSampleType" class="form-width" placeholder="请选择">
                <el-option label="人组织" value="人组织"></el-option>
                <el-option label="动物组织" value="动物组织"></el-option>
                <el-option label="植物组织" value="植物组织"></el-option>
                <el-option label="微生物组织（真菌）" value="微生物组织（真菌）"></el-option>
                <el-option label="微生物组织（细菌）" value="微生物组织（细菌）"></el-option>
                <el-option label="细胞（请在备注栏填写细胞数量）" value="细胞（请在备注栏填写细胞数量）"></el-option>
                <el-option label="外周血（冻存血）" value="外周血（冻存血）"></el-option>
                <el-option label="外周血（新鲜血）" value="外周血（新鲜血）"></el-option>
                <el-option label="血浆" value="血浆"></el-option>
                <el-option label="FFPE石蜡" value="FFPE石蜡"></el-option>
                <el-option label="口腔拭子" value="口腔拭子"></el-option>
                <el-option label="生殖道拭子" value="生殖道拭子"></el-option>
                <el-option label="骨髓" value="骨髓"></el-option>
                <el-option label="脑脊液" value="脑脊液"></el-option>
                <el-option label="单细胞" value="单细胞"></el-option>
                <el-option label="其他（请在备注栏中填写信息）" value="其他（请在备注栏中填写信息）"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="组织部分" prop="tissuePart">
              <el-input v-model.trim="form.tissuePart" maxlength="10" class="form-width" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="组织样本状态" prop="tissueSampleStatus">
              <el-select v-model="form.tissueSampleStatus" class="form-width" placeholder="请选择">
                <template v-for="item in tissueSampleStatusList">
                  <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="提取类型" prop="extractionType">
              <el-select v-model="form.extractionType" class="form-width" placeholder="请选择">
                <template v-for="item in extractionTypeList">
                  <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </div>
        </template>
        <template v-if="form.sampleType === '核酸类样本'">
          <div class="form-item">
            <div class="form-title">核酸样本填写</div>
            <el-form-item label="核酸样本类型" prop="nucleicAcidSampleType">
              <el-select v-model="form.nucleicAcidSampleType" class="form-width" placeholder="请选择">
                <el-option label="全基因组DNA" value="全基因组DNA"></el-option>
                <el-option label="total RNA" value="total RNA"></el-option>
                <el-option label="cf DNA" value="cf DNA"></el-option>
                <el-option label="FFPE DNA" value="FFPE DNA"></el-option>
                <el-option label="FFPE RNA" value="FFPE RNA"></el-option>
                <el-option label="PCR产物" value="PCR产物"></el-option>
                <el-option label="cDNA" value="cDNA"></el-option>
                <el-option label="其他（请在备注栏中填写信息）" value="其他（请在备注栏中填写信息）"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="核酸样本状态" prop="nucleicAcidSampleStatus">
              <el-select v-model="form.nucleicAcidSampleStatus" class="form-width" placeholder="请选择">
                <template v-for="item in nucleicAcidSampleStatusList">
                  <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </div>
        </template>
        <el-form-item label="检测项目" prop="detectType">
          <el-select v-model="form.detectType" class="form-width" placeholder="请选择">
            <el-option
              :key="k"
              :label="v"
              :value="k"
              v-for="(v, k) in detectTypeLists">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建库类型" prop="libraryType">
          <el-select v-model="form.libraryType" class="form-width" placeholder="请选择">
            <template v-for="item in libraryTypeList">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="数据量（G）" prop="dataNum">
          <el-input v-model.trim="form.dataNum" :min="0" maxlength="10" type="number" class="form-width" placeholder="请输入" @blur="handleNumBlur('dataNum')"></el-input>
        </el-form-item>
        <el-form-item label="测序平台" prop="sequencingPlatform">
          <el-select v-model="form.sequencingPlatform" class="form-width" placeholder="请选择">
            <el-option label="DNBSEQ-T7" value="DNBSEQ-T7"></el-option>
            <el-option label="Gene+2000" value="Gene+2000"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测序策略" prop="tactics">
          <el-select v-model="form.tactics" class="form-width" placeholder="请选择">
            <el-option label="PE100" value="PE100"></el-option>
            <el-option label="PE150" value="PE150"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实验环节" prop="labEnvironment">
          <el-select v-model="form.labEnvironment" class="form-width" placeholder="请选择">
            <template v-for="item in labEnvironmentList">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model.trim="form.notes" maxlength="150"  class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <!--<el-button size="mini" type="primary" @click="handleAddFakeData">添加假数据</el-button>-->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../../../util/mixins'
export default {
  name: `editSendSampleDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Object | null
    },
    rowIndex: Number | null,
    isCoreSpun: Boolean // 应用类型是否是包芯上机
  },
  data () {
    // 第一个验证，仅允许数字、字母、- _ 组成
    const valid1 = function (rule, value, callback) {
      let regx = /^[0-9A-Za-z_]{1,}$/
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error('仅允许数字和字母还有“_”组成'))
      }
    }
    return {
      title: '',
      form: {},
      // 组织样本类型
      tissueSampleStatusList: [
        {label: '速冻组织', value: '速冻组织'},
        {label: 'RNS later保存', value: 'RNS later保存'},
        {label: 'Trizol保存（请在备注栏填写trizol体积）', value: 'Trizol保存（请在备注栏填写trizol体积）'},
        {label: '细胞沉淀', value: '细胞沉淀'},
        {label: '硅胶干燥', value: '硅胶干燥'},
        {label: '福尔马林保存', value: '福尔马林保存'},
        {label: '乙醇保存', value: '乙醇保存'},
        {label: '其他（请在备注栏填写）', value: '其他（请在备注栏填写）'}
      ],
      // 核酸样本类型
      nucleicAcidSampleStatusList: [
        {label: '溶于纯水', value: '溶于纯水'},
        {label: '溶于EB', value: '溶于EB'},
        {label: '溶于TE', value: '溶于TE'},
        {label: '溶于乙醇', value: '溶于乙醇'},
        {label: '干粉', value: '干粉'},
        {label: '溶于无Rnase水(或DEPC)', value: '溶于无Rnase水(或DEPC)'},
        {label: '其他（请在备注中填写信息）', value: '其他（请在备注中填写信息）'}
      ],
      // 提取类型
      extractionTypeList: [
        {label: 'gDNA提取', value: 'gDNA提取'},
        {label: '宏基因组提取', value: '宏基因组提取'},
        {label: 'cfDNA提取（血液/脑脊液样本）', value: 'cfDNA提取（血液/脑脊液样本）'},
        {label: 'RNA提取', value: 'RNA提取'},
        {label: 'DNA/RNA同时提取', value: 'DNA/RNA同时提取'}
      ],
      // 建库类型
      libraryTypeList: [
        {label: '全基因组文库-WGS', value: '全基因组文库-WGS'},
        {label: 'PCR-free文库', value: 'PCR-free文库'},
        {label: '全外显子文库-WES', value: '全外显子文库-WES'},
        {label: '目标区域捕获文库', value: '目标区域捕获文库'},
        {label: 'mRNA链特异性文库', value: 'mRNA链特异性文库'},
        {label: 'IncRNA链特异性文库', value: 'IncRNA链特异性文库'},
        {label: '普通转录组文库', value: '普通转录组文库'},
        {label: '宏基因组文库', value: '宏基因组文库'},
        {label: '宏转录因组文库', value: '宏转录因组文库'},
        {label: '10x3’单细胞文库', value: '10x3’单细胞文库'},
        {label: '10x5’单细胞文库', value: '10x5’单细胞文库'},
        {label: '10xATAC单细胞文库', value: '10xATAC单细胞文库'},
        {label: '甲基化文库', value: '甲基化文库'},
        {label: 'ATAC-seq文库', value: 'ATAC-seq文库'},
        {label: 'chip-seq文库', value: 'chip-seq文库'},
        {label: 'Hi-C文库', value: 'Hi-C文库'},
        {label: 'TCR文库', value: 'TCR文库'},
        {label: '其他（请在备注中说明）', value: '其他（请在备注中说明）'}
      ],
      // 实验环节
      labEnvironmentList: [
        {label: '提取建库测序', value: '提取建库测序'},
        {label: '提取建库测序分析', value: '提取建库测序分析'},
        {label: '建库测序', value: '建库测序'},
        {label: '建库测序分析', value: '建库测序分析'}
      ],
      detectTypeLists: {}, // 检测项目类型
      rules: {
        sampleName: [
          {required: true, message: '请输入样本名称', trigger: 'blur'},
          {validator: valid1, trigger: 'blur'}
        ],
        detectType: [
          {required: true, message: '请选择检测类型', trigger: 'change'}
        ],
        libraryType: [
          {required: true, message: '请选择建库类型', trigger: 'change'}
        ],
        dataNum: [
          {required: true, message: '请输入数据量', trigger: 'blur'}
        ],
        baseBalance: [
          {required: true, message: '请选择碱基平衡', trigger: 'change'}
        ],
        storageMedium: [
          {required: true, message: '请选择保存介质', trigger: 'change'}
        ],
        sequencingPlatform: [
          {required: true, message: '请选择测序平台', trigger: 'change'}
        ],
        tactics: [
          {required: true, message: '请选择策略', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.getDetectType()
      if (this.pdata) {
        this.form = {...this.pdata}
        this.title = this.form.sampleName ? '编辑寄送样本信息' : '新增寄送样本信息'
      } else {
        this.title = '新增寄送样本信息'
        this.form = {
          geneplusNum: '',
          sampleName: '',
          species: '',
          sampleType: '',
          tissueSampleType: '',
          tissuePart: '',
          tissueSampleStatus: '',
          extractionType: '',
          nucleicAcidSampleType: '',
          nucleicAcidSampleStatus: '',
          detectType: '',
          libraryType: '',
          dataNum: '',
          sequencingPlatform: '', // 测序平台
          tactics: '',
          labEnvironment: '',
          notes: ''
        }
      }
    },
    // 获取检测项目
    getDetectType () {
      this.$ajax({
        url: '/order/get_detect_type_list'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.detectTypeLists = {}
          res.data.forEach(v => {
            this.detectTypeLists[v] = v
          })
        } else {
          this.$message.error('获取检测类型失败，错误：' + res.message)
        }
      })
    },
    // 数字类型的输入失焦后的操作
    handleNumBlur (k) {
      let v = +this.form[k]
      if (isNaN(v) || v < 0) {
        this.form[k] = ''
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let oldName = ''
          let code = ''
          if (this.pdata) oldName = this.pdata.sampleName
          if (this.pdata) code = this.pdata.geneplusNum
          this.$emit('dialogConfirmEvent', this.form, this.rowIndex, oldName, code)
        }
      })
    },
    async handleClose () {
      await this.$confirm(`是否确认放弃${this.pdata && this.pdata.sampleName ? '编辑' : '新增'}？确认后关闭弹窗`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.visible = false
    }
  }
}
</script>

<style scoped>
.form-width{
  width: 200px;
}
.form-item {
  border-top: 1px solid #f5f5f5;
  border-bottom: 1px solid #f5f5f5;
  margin-bottom: 5px;
  border-radius: 5px;
}
.form-title {
  margin: 5px 0 10px 10px;
  font-weight: bold;
}
</style>>
