import ExportDialogPlugin from './index.vue'

const ErrorDialog = {}

ErrorDialog.install = function (Vue) {
  let Dialogcom = Vue.extend(ExportDialogPlugin)
  let instance = new Dialogcom()
  Vue.prototype.$downLoadChoose = function (message, tableData) {
    instance.visible = true
    instance.message = message
    instance.type = 1
    instance.width = '500px'
    instance.form = {
      checkedFields: []
    }
    instance.tableData = tableData
    instance.checkAll = false
    instance.isIndeterminate = false

    return new Promise((resolve, reject) => {
      instance.$on('downLoad', (fields) => {
        resolve(fields)
      })
      instance.$on('close', (fields) => {
        reject(new Error('cancel'))
      })
    })
  }
  instance.$mount(document.createElement('div'))
  document.body.appendChild(instance.$el)
}

export default ErrorDialog
