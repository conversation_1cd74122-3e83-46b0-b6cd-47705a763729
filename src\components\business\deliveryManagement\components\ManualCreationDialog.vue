<template>
  <el-dialog
    append-to-body
    title="人工创建"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    @opened="handleOpen">
    <el-form
      ref="form"
      :model="form"
      inline
      label-position="right"
      class="dialog-form"
      label-width="120px"
      label-suffix=":"
      size="mini">
      <!--功能区-->
      <div class="flex-wrapper">
        <el-form-item label="任务模块" prop="personalizeType">
          <el-select v-model.trim="form.personalizeType" class="form-medium-width" placeholder="请选择">
            <el-option
              v-for="item in personalizeTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <div>
          <el-button type="primary" size="mini" @click="handleImport">样本导入</el-button>
        </div>
      </div>
      <el-card>
        <!--      参数区-->
        <template>
          <el-row>
            <el-col v-for="(item, index) in paramsOption[form.personalizeType]" :key="index" :span="item.span">
              <el-form-item v-if="item.show(form)" :label="item.label" :prop="item.prop" :rules="item.rules">
                <el-input v-if="item.type === 'input'" v-model.trim="form[item.prop]" :placeholder="item.placeholder"
                          clearable></el-input>
                <el-radio-group v-if="item.type === 'radio'" v-model.trim="form[item.prop]">
                  <el-radio v-for="(option, index) in item.options" :key="index" :label="option.value">
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>
                <el-select v-if="item.type === 'select'" v-model.trim="form[item.prop]" clearable>
                  <el-option
                    v-for="(option, index) in item.options"
                    :key="index"
                    :label="option.label"
                    :value="option.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </template>
        <!--      样本表格-->
        <vxe-table
          ref="tableRef"
          border
          resizable
          :height="300"
          keep-source
          class="manualCreationTable"
          :data="tableData"
          size="mini"
          show-overflow
          :auto-resize="true"
          :edit-rules="validRules"
          :valid-config="{msgMode: 'full'}"
          :checkbox-config="{trigger: 'row'}"
          :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false, showStatus: true }">
          :scroll-y="{enabled: true}"
          >
          <vxe-column type="seq" width="60"></vxe-column>
          <template v-if="form.personalizeType !== 'NG006'">
            <vxe-column field="ffcNum" title="芯片号" show-overflow
                        :edit-render="setEditRender(form.personalizeType === 'NG007')">
              <template #default="{ row }">
                {{ row.ffcNum || '-' }}
              </template>
              <template #edit="{ row }">
                <div class="flex-wrapper">
                  <el-input v-model.trim="row.ffcNum" class="form-width" size="mini" clearable></el-input>
                  <el-button type="text" size="small" icon="el-icon-check"
                             @click="handleSaveRow(row, 'ffcNum')"></el-button>
                  <el-button type="text" size="small" style="color: #F56C6C;" icon="el-icon-close"
                             @click="handleRevertData(row, 'ffcNum')"></el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column v-if="form.personalizeType === 'NG002'" field="foriSampleLibNameChange" title="改后子文库名称" show-overflow
                        :edit-render="setEditRender(form.personalizeType === 'NG002')">
              <template #default="{ row }">
                {{ row.foriSampleLibNameChange || '-' }}
              </template>
              <template #edit="{ row }">
                <div class="flex-wrapper">
                  <el-input v-model.trim="row.foriSampleLibNameChange" class="form-width" size="mini" clearable></el-input>
                  <el-button type="text" size="small" icon="el-icon-check"
                             @click="handleSaveRow(row, 'foriSampleLibNameChange')"></el-button>
                  <el-button type="text" size="small" style="color: #F56C6C;" icon="el-icon-close"
                             @click="handleRevertData(row, 'foriSampleLibNameChange')"></el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="foriSampleName" title="原始样本名称" show-overflow
                        :edit-render="setEditRender(form.personalizeType === 'NG002')">
              <template #default="{ row }">
                {{ row.foriSampleName || '-' }}
              </template>
              <template #edit="{ row }">
                <div class="flex-wrapper">
                  <el-input v-model.trim="row.foriSampleName" class="form-width" size="mini" clearable></el-input>
                  <el-button type="text" size="small" icon="el-icon-check"
                             @click="handleSaveRow(row, 'foriSampleName')"></el-button>
                  <el-button type="text" size="small" style="color: #F56C6C;" icon="el-icon-close"
                             @click="handleRevertData(row, 'foriSampleName')"></el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="foriSampleLibName" title="原始子文库名称" show-overflow>
              <template #default="{ row }">
                {{ row.foriSampleLibName || '-' }}
              </template>
            </vxe-column>
          </template>
          <vxe-column field="fgeneNum" title="吉因加编号" show-overflow>
            <template #default="{ row }">
              {{ row.fgeneNum || '-' }}
            </template>
          </vxe-column>
          <vxe-column title="操作" width="160">
            <template #default="{ row }">
              <template>
                <el-button v-if="showEditOperation" type="text" size="mini" @click="handleEditRowEvent(row)">编辑
                </el-button>
                <el-button type="text" style="color: #F56C6C;" size="mini" @click="handleRemove(row)">删除</el-button>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </el-card>

    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>

    <sample-import-dialog ref="sampleImportDialog" :pvisible.sync="sampleImportDialogVisible"
                          :personalize-type="form.personalizeType" @dialogConfirmEvent="handleMergeSamples"/>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {paramsOption, personModule} from '../constants/constants'
import sampleImportDialog from './sampleImportDialog.vue'
import {getPersonalSampleList, savePersonal} from '../../../../api/deliveryManagement'
import {awaitWrap} from '../../../../util/util'

export default {
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {
    sampleImportDialog
  },
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    subOrderIds: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showEditOperation () {
      return ['NG002', 'NG007'].includes(this.form.personalizeType)
    }
  },
  data () {
    return {
      form: {
        personalizeType: 'NG002',
        fnewFileName: '',
        fcutFragmentType: 1,
        fcutRead1: '',
        fcutRead2: '',
        q30: '',
        fnucleateContent: '',
        freadCountMin: '',
        fmailSubject: '',
        fadjustmentArea: ''
      },
      sampleImportDialogVisible: false, // 导入弹窗
      // 在paramsOption中补充参数配置
      paramsOption: paramsOption,
      tableData: [],
      validRules: {},
      personalizeTypeOptions: personModule
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.getTableData()
    },
    async getTableData () {
      this.tableData = []
      const {res} = await awaitWrap(getPersonalSampleList({
        fcosDeliverOrderIdList: this.ids,
        fcosDeliverBatchDetailIdList: this.subOrderIds
      }, {
        loadingDom: '.manualCreationTable'
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.tableData = (res.data || []).map((v, index) => {
          return {
            fsortNum: index,
            fgeneNum: v.fgeneNum,
            ffcNum: v.ffcNum,
            foriSampleLibNameChange: v.foriSampleLibNameChange,
            foriSampleLibName: v.foriSampleLibName,
            foriSampleName: v.foriSampleName
          }
        })
      }
    },
    // 导入样本
    handleImport () {
      this.sampleImportDialogVisible = true
    },
    // 合并样本
    handleMergeSamples (samples) {
      samples = samples.map((v, index) => {
        v.fsortNum = index
        return {
          ...v
        }
      })
      // 合并表格现有数据和导入的样本数据 对象数组并去重
      this.tableData = [...this.tableData, ...samples].reduce((acc, cur) => {
        // 判断是否是同一个样本
        const isSample = (per = {}, next = {}) => {
          return per.foriSampleName === next.foriSampleName &&
            per.foriSampleLibName === next.foriSampleLibName &&
            per.fgeneNum === next.fgeneNum &&
            per.ffcNum === next.ffcNum
        }
        if (!acc.some(v => isSample(v, cur))) {
          acc.push(cur)
        }
        return acc
      }, [])
    },
    setParams () {
      const options = this.paramsOption[this.form.personalizeType] || []
      return {
        personalizeType: this.form.personalizeType,
        personalizeReqs: this.tableData,
        ...(options.reduce((acc, cur) => {
          // 如果当前字段存在且显示 设置参数
          if (cur.prop && cur.show(this.form)) {
            acc[cur.prop] = this.form[cur.prop]
          }
          return acc
        }, {}))
      }
    },
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        // 校验表格数据是存在正在编辑的数据
        const table = this.$refs.tableRef
        const hasChange = table.getUpdateRecords().length > 0 || table.getEditRecord() !== null
        if (hasChange) {
          this.$message({
            message: '请确认编辑数据',
            type: 'error'
          })
          return
        }
        // 是否存在样本
        if (this.tableData.length === 0) {
          this.$message({
            message: '没有样本数据，不能创建交付订单',
            type: 'error'
          })
          return
        }
        if (valid) {
          this.loading = true
          const params = this.setParams()
          try {
            const {res = {}} = await awaitWrap(savePersonal(params))
            if (res.code === this.SUCCESS_CODE) {
              this.$message({
                message: '操作成功',
                type: 'success'
              })
              this.$emit('dialogConfirmEvent')
              this.visible = false
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.loading = false
          }
        } else {
          this.$message({
            message: '请填写完整信息',
            type: 'error'
          })
        }
      })
    },

    /* ********************************************************表格相关****************************************************************/
    setEditRender (enabled) {
      return {
        autofocus: true,
        enabled: enabled,
        immediate: true
      }
    },
    // 编辑
    handleEditRowEvent (row) {
      const table = this.$refs.tableRef
      if (table.getUpdateRecords().length > 0) {
        this.$message({
          message: '请先保存已修改的数据',
          type: 'error'
        })
        return
      }
      table.setEditRow(row)
    },
    // 保存
    async handleSaveRow (row, field) {
      const $table = this.$refs.tableRef
      // 非空判断
      if (row[field] === '') {
        this.$message({
          message: '请输入数据',
          type: 'error'
        })
        return
      }
      // 输入数字和字母之外类型字符
      if (field === 'ffcNum' && !/^[a-zA-Z0-9]+$/.test(row[field])) {
        this.$message({
          message: '请输入数字和字母',
          type: 'error'
        })
        return
      }
      if ((field === 'foriSampleLibName' || field === 'foriSampleLibNameChange') && !/^[0-9A-Za-z][0-9A-Za-z-_]*[0-9A-Za-z]$|^[0-9A-Za-z]$/.test(row[field])) {
        this.$message({
          message: '限定数字、字母、”-“及"_”组成，且只允许数字、字母开头/结尾',
          type: 'error'
        })
        return
      }
      // 限制长度20字符
      if (row[field].length > 50) {
        this.$message({
          message: '长度不能超过50个字符',
          type: 'error'
        })
        return
      }
      //
      await $table.clearEdit()
      await this.$refs.tableRef.reloadRow(row, null, field)
    },
    // 还原数据
    async handleRevertData (row) {
      const $table = this.$refs.tableRef
      await $table.clearEdit()
      await $table.revertData(row)
    },
    // 删除
    async handleRemove (item) {
      const {row} = await this.$refs.tableRef.remove(item)
      this.tableData = this.tableData.filter(item => item._X_ROW_KEY !== row._X_ROW_KEY)
      console.log(row, this.tableData)
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-wrapper {
  display: flex;
  justify-content: space-between;
}
</style>
