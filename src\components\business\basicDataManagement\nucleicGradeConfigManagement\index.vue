<template>
  <div>
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="120px" size="mini"
        style="display: flex;justify-content: space-between" @submit.native.prevent @keyup.enter.native.prevent="handleSearch">
        <el-form-item label="核酸等级标准" prop="gradeName">
          <el-input v-model.trim="form.gradeName" clearable placeholder="请输入核酸等级标准"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <!--按钮组-->
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('002020001', 'buttons')" type="primary" plain size="mini" @click="handleAddGrade">新增</el-button>
      <el-button v-if="$setAuthority('002020002', 'buttons')" type="primary" plain size="mini" @click="handleEditGrade(1)">复制新增</el-button>
      <el-button v-if="$setAuthority('002020003', 'buttons')" type="primary" plain size="mini" @click="handleEditGrade(2)">修改</el-button>
      <el-button type="primary" plain size="mini" @click="handleEditGrade(3)">查看详情</el-button>
      <el-button v-if="$setAuthority('002020004', 'buttons')" type="danger" plain size="mini" @click="handleDelete">删除</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">搜索</el-button>
      <el-button type="primary" plain size="mini" @click="handleReset">重置</el-button>
    </div>
    <!--表格-->
    <el-table ref="table" :data="tableData" class="table" border :height="tbHeight" :row-style="handleRowStyle"
      @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll"
      @sort-change="handleSortChange">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="gradeName" label="核酸等级定级标准" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="notes" label="说明" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="creatorName" label="创建人" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip
        sortable="custom"></el-table-column>
      <el-table-column prop="updateName" label="修改人" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="updateTime" label="修改时间" min-width="180" show-overflow-tooltip
        sortable="custom"></el-table-column>
    </el-table>
    <!--分页-->
    <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <nucleic-grade-edit-dialog :pvisible.sync="nucleicGradeEditInfo.visible" :title="nucleicGradeEditInfo.title"
      :id="nucleicGradeEditInfo.id" :type="nucleicGradeEditInfo.type" @dialogConfirmEvent="getData" />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import NucleicGradeEditDialog from './nucleicGradeEditDialog'
import util from '../../../../util/util'
import { getNucleicGradeList, deleteNucleicGrade } from '../../../../api/basicDataManagement/nucleicGradeApi'
import { awaitWrap } from '@/util/util'

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    NucleicGradeEditDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32 + 24, '.search-form')
    this.getData()
  },
  data () {
    return {
      form: {
        gradeName: ''
      },
      submitForm: {},
      nucleicGradeEditInfo: {
        visible: false,
        title: '',
        id: null,
        type: null
      },
      sortParams: {
        fcreateTimeOrder: null,
        fupdateTimeOrder: null
      }
    }
  },
  methods: {
    async getData () {
      const { res = {} } = await awaitWrap(getNucleicGradeList({
        fdnaLevelStandard: this.submitForm.gradeName,
        fupdateTimeOrder: this.sortParams.fupdateTimeOrder,
        fcreateTimeOrder: this.sortParams.fcreateTimeOrder,
        pagedRequest: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.selectedRows.clear()
        let data = res.data
        this.totalPage = data.total
        let rows = data.records || []
        this.tableData = []
        rows.forEach(row => {
          let item = {
            id: row.fid,
            gradeName: row.fdnaLevelStandard,
            notes: row.fdescription,
            creator: row.fcreator,
            creatorName: row.fcreator,
            createTime: row.createTime,
            updateName: row.fupdator,
            updateTime: row.updateTime
          }
          util.setDefaultEmptyValueForObject(item)
          item.realData = util.deepCopy(row)
          this.tableData.push(item)
        })
      }
    },
    handleSearch () {
      this.submitForm = {
        gradeName: this.form.gradeName
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        gradeName: ''
      }
      this.handleSearch()
    },
    // 排序变化
    handleSortChange (column) {
      if (!column.order) {
        this.sortParams.fupdateTimeOrder = null
        this.sortParams.fcreateTimeOrder = null
      }
      if (column.prop === 'createTime') {
        this.sortParams.fupdateTimeOrder = null
        this.sortParams.fcreateTimeOrder = column.order === 'ascending' ? 1 : 0
      }
      if (column.prop === 'updateTime') {
        this.sortParams.fupdateTimeOrder = column.order === 'ascending' ? 1 : 0
        this.sortParams.fcreateTimeOrder = null
      }
      this.getData()
    },
    // 新增核酸等级
    handleAddGrade () {
      this.nucleicGradeEditInfo.type = null
      this.nucleicGradeEditInfo.visible = true
      this.nucleicGradeEditInfo.title = '新增核酸等级'
      this.nucleicGradeEditInfo.id = null
    },
    // 修改核酸等级
    handleEditGrade (type) {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一行数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.nucleicGradeEditInfo.type = type
      this.nucleicGradeEditInfo.visible = true
      this.nucleicGradeEditInfo.title = '修改核酸等级'
      this.nucleicGradeEditInfo.id = row.id
    },
    // 删除
    async handleDelete () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条删除的数据')
        return
      }
      await this.$confirm(`所选数据将被删除，删除标准后产品配置的数据也会失效，请谨慎操作。`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const ids = [...this.selectedRows.values()].map(item => item.id)
      const { res } = await awaitWrap(deleteNucleicGrade({
        fdnaLevelConfigId: ids.join(',')
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
        this.getData()
      } else {
        this.$message.error(res.message)
      }
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.operate-btns-group {
  margin-bottom: 10px;
}
</style>
