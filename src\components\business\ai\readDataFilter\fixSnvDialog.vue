<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="850px"
    @open="handleOpen">
    <div v-if="visible" style="width: 100%; height:50vh; overflow: auto;">
      <el-form label-width="120px" style="width: 98%" label-suffix=":">
        <!--固定信息-->
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="基因名称">{{form.gene}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="参考序列">{{form.freferenceSeq}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="功能区域">{{form.fexinId}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Fr.1*">{{form.ffr1}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Fr.2*">{{form.ffr2}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Fr.3*">{{form.ffr3}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Function">{{form.ffunction}}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="rs号">{{form.frsId}}</el-form-item>
          </el-col>
        </el-row>

        <!--修改信息-->
        <el-row>
          <el-col :span="8">
            <el-form-item label="核苷酸改变">
              <el-input v-model.trim="form.nucleotideMutation" size="mini" clearable placeholder="请输入核苷酸改变"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="氨基酸改变">
              <el-input v-model.trim="form.aminoAcidMutation" size="mini" clearable placeholder="请输入氨基酸改变"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变异意义">
              <el-select v-model.trim="form.mutationType" size="mini" clearable placeholder="请选择">
                <el-option
                  :key="item"
                  :label="item"
                  :value="item"
                  v-for="item in means">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="纯合/杂合">
              <el-input v-model.trim="form.fzygosity" size="mini" clearable placeholder="请输入纯合/杂合"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="遗传几率">
              <el-input v-model.trim="form.fgeneticRate" size="mini" clearable placeholder="请输入遗传几率"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="变异概述">
              <el-input v-model.trim="form.gene_mutation_analysis_cn" :autosize="{ minRows: 4, maxRows: 4}" type="textarea" size="mini" clearable placeholder="请输入基因变异解析"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="基因变异解析">
              <el-input v-model.trim="form.gene_mutation_analysis" :autosize="{ minRows: 6, maxRows: 6}" type="textarea" size="mini" clearable placeholder="请输入基因变异解析"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!--参考文献-->
        <references ref="references" :rids="form.snvRids"></references>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import references from '../common/references'
import mixins from '../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {references},
  props: {
    pdata: {
      type: Object
    }
  },
  data () {
    return {
      title: '修改SNV',
      loading: false,
      means: ['致病', '疑似致病', '意义未明', '疑似良性', '良性'],
      form: {}
    }
  },
  methods: {
    handleOpen () {
      this.form = {}
      this.$nextTick(() => {
        this.form = this.pdata.realData
      })
    },
    handleConfirm () {
      this.loading = true
      let references = this.$refs.references.tableData || []
      this.form.snvRids = references.map(v => v.rId).join(',')
      this.$ajax({
        url: '/read/bigAi/save_h_snv_data',
        loadingDom: '.table',
        data: {
          analysisRsId: this.analysisRsId,
          ...this.form
        }
      }).then(res => {
        if (res.code && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped></style>
