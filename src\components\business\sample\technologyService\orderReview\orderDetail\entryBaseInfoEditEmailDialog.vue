<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="客户邮箱"
      width="600px"
      @open="handleOpen">
      <div style="margin-left: 30px;">
        <div style="max-height: 350px; overflow: auto;">
          <div :key="index" v-for="(item, index) in emails" style="margin-bottom: 20px;">
            <el-input v-model.trim="item.email" placeholder="请输入客户邮箱" clearable size="mini" style="width: 300px" @blur="handleValidateEmail(index)"></el-input>
            <el-button v-if="emails.length > 1" size="mini" style="margin-left: 20px" @click="handleDel(index)">删除</el-button>
            <el-button v-if="index === emails.length - 1 && emails.length < 10" size="mini" type="primary" @click="handleAdd">添加</el-button>
            <p v-if="!item.validate" style="font-size: 14px;color: red;">邮箱格式错误</p>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../../../util/mixins'
import util from '../../../../../../util/util'
export default {
  name: 'entryBaseInfoEditEmailDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    inputEmail: String
  },
  data () {
    return {
      emails: [
        {email: '', validate: true}
      ]
    }
  },
  methods: {
    handleOpen () {
      this.emails = [{email: '', validate: true}]
      if (this.inputEmail) {
        let emails = this.inputEmail.split(';')
        this.emails = []
        emails.forEach(v => {
          let item = {
            email: v,
            validate: true
          }
          this.emails.push(item)
        })
      }
    },
    // 检验邮箱的格式
    handleValidateEmail (index) {
      if (this.emails[index].email) {
        this.$set(this.emails[index], 'validate', util.validateEmail(this.emails[index].email))
        return
      }
      this.$set(this.emails[index], 'validate', true)
    },
    handleAdd () {
      this.emails.push({email: '', validate: true})
    },
    handleDel (index) {
      this.emails.splice(index, 1)
    },
    handleConfirm () {
      let hasAllValidate = this.emails.every(v => {
        return v.validate
      })
      if (!hasAllValidate) {
        this.$message.error('存在邮箱格式不正确，请检查')
        return
      }
      // 筛出所有的邮箱，并去除空邮箱
      let emails = this.emails.map(v => v.email).filter(v => v).join(';')
      this.$emit('dialogConfirmEvent', emails)
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
.label-item{
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}
</style>
