<template>
  <div class="sidepanel">
    <div class="section">
      <h3>标定与规格</h3>
      <div class="row">
        <label class="grow">像素标定 (μm/px)</label>
        <el-input-number v-model="local.umPerPx" :step="0.001" :precision="3" size="mini" :min="0.001" />
        <el-button size="mini" @click="applyUm">应用</el-button>
      </div>
      <div class="row">
        <label class="grow">芯片规格</label>
        <el-select v-model="local.chip" size="mini" class="grow">
          <el-option label="A1 · 6.5 x 6.5 mm" value="A1" />
          <el-option label="B2 · 5.0 x 7.0 mm" value="B2" />
          <el-option label="C3 · 8.0 x 4.0 mm" value="C3" />
          <el-option label="自定义" value="CUSTOM" />
        </el-select>
      </div>
      <div class="row">
        <label>宽 (mm)</label>
        <el-input-number v-model="local.mmW" size="mini" :step="0.01" :precision="2" :min="0.01" />
      </div>
      <div class="row">
        <label>高 (mm)</label>
        <el-input-number v-model="local.mmH" size="mini" :step="0.01" :precision="2" :min="0.01" />
      </div>
      <div class="row">
        <el-button type="primary" size="mini" @click="applySpec">按规格计算选片框尺寸</el-button>
      </div>
    </div>

    <div class="section">
      <h3>精确移动（每次步长）</h3>
      <div class="row">
        <label>步长 (mm)</label>
        <el-input-number v-model="local.stepMm" size="mini" :step="0.01" :precision="2" :min="0.001" />
      </div>
      <div class="grid-2">
        <el-button class="btn" size="mini" @click="move(0, -1)">↑ 上移</el-button>
        <el-button class="btn" size="mini" @click="move(0, 1)">↓ 下移</el-button>
        <el-button class="btn" size="mini" @click="move(-1, 0)">← 左移</el-button>
        <el-button class="btn" size="mini" @click="move(1, 0)">→ 右移</el-button>
      </div>
    </div>

    <div class="section">
      <h3>旋转控制</h3>
      <div class="grid-2">
        <el-button class="btn" size="mini" @click="$emit('rotateBy', -5)">⟲ 左旋 5°</el-button>
        <el-button class="btn" size="mini" @click="$emit('rotateBy', 5)">⟳ 右旋 5°</el-button>
      </div>
      <div class="row">
        <label>角度 (0-90整数，顺时针)</label>
      </div>
      <div class="row">
        <el-input-number v-model="local.angle" size="mini" :step="1" :min="0" :max="90" />
        <el-button size="mini" @click="applyAngle">设置角度</el-button>
      </div>
      <div class="row">
        <span class="kv">提示：按住红框边缘拖拽可自由旋转；按住红框内部可移动</span>
      </div>
    </div>

    <div class="section">
      <h3>标记与视图</h3>
      <div class="grid-2">
        <el-button size="mini" type="primary" @click="$emit('saveMarkers')">保存标记</el-button>
        <el-button size="mini" @click="$emit('loadMarkers')">加载标记</el-button>
      </div>
      <div class="row">
        <el-button size="mini" @click="$emit('resetView')">重置视图</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InspectorPanel',
  props: {
    umPerPx: { type: Number, default: 0.345 },
    mmW: { type: Number, default: 6.5 },
    mmH: { type: Number, default: 6.5 },
    chip: { type: String, default: 'A1' },
    angle: { type: Number, default: 0 }
  },
  watch: {
    umPerPx (v) { this.local.umPerPx = v },
    mmW (v) { this.local.mmW = v },
    mmH (v) { this.local.mmH = v },
    chip (v) { this.local.chip = v },
    angle (v) { this.local.angle = v }
  },
  data () {
    return {
      local: {
        umPerPx: this.umPerPx,
        mmW: this.mmW,
        mmH: this.mmH,
        chip: this.chip,
        stepMm: 0.1,
        angle: this.angle
      }
    }
  },
  methods: {
    applyUm () {
      this.$emit('update:umPerPx', this.local.umPerPx)
    },
    applySpec () {
      this.$emit('applySpec', {
        type: this.local.chip,
        mmW: this.local.mmW,
        mmH: this.local.mmH
      })
    },
    move (dxSign, dySign) {
      this.$emit('moveByMm', dxSign * this.local.stepMm, dySign * this.local.stepMm)
    },
    applyAngle () {
      this.$emit('setAngle', this.local.angle)
    }
  }
}
</script>

<style scoped>
.sidepanel{
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  padding:8px;
  display:flex;
  flex-direction:column;
  gap:8px;
  overflow:auto;
  height:100%;
  min-height:0;
  color:#303133
}
.section{
  background:#fff;
  border:1px dashed #dcdfe6;
  border-radius:4px;
  padding:8px
}
.section h3{margin:0 0 6px 0;font-size:13px;color:#606266;font-weight:600}
.row{display:flex;gap:8px;align-items:center;flex-wrap:wrap;margin-bottom:6px}
.row label{font-size:12px;color:#606266}
.row .grow{flex:1}
.grid-2{display:grid;grid-template-columns:1fr 1fr;gap:8px}
.kv{font-size:12px;color:#606266}
.btn{background:#fff;border:1px solid #dcdfe6;color:#303133}
</style>
