<template>
  <div class="card-wrapper desc">
    <img-info :type="type" :imgs="geneticMutationMap"></img-info>
  </div>
</template>

<script>
import imgInfo from '../common/imgInfo'

export default {
  components: {
    imgInfo
  },
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      type: 1,
      geneticMutationMap: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_frameshift_mutation_img',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || {}
        this.geneticMutationMap = [
          info.path
        ]
      }
    }
  }
}
</script>

<style scoped></style>
