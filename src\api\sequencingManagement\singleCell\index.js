import { myAjax } from '@/util/ajax'

/**
 * 获取解离列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205699&itf=5799149
 * @param options
 * @returns {*}
 */
export function getDissociationList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/get_dissociation_list',
    data: data,
    ...options
  })
}

// 导出解离数据
export function exportDissociationData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/export_dissociation',
    data: data,
    responseType: 'blob',
    ...options
  })
}

// 导出解离数据
export function submitDissociationResult (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/submit_dissociation_result',
    data: data,
    ...options
  })
}

export function updateDissociationData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/update_dissociation_result',
    data: data,
    ...options
  })
}

export function batchModifyDissociationStatus (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/remark_exception',
    data: data,
    ...options
  })
}

export function updateRemark (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/update_remark',
    data: data,
    ...options
  })
}
// 下载模板
export function downloadTemplate (type = 1) {
  return myAjax({
    method: 'get',
    url: type === 1 ? '/experiment/dissociation/download_excel_template' : '/experiment/new_build_lib/download_excel_template',
    responseType: 'blob'
  })
}

// 确认建库
export function confirmBuildLib (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/build_lib',
    data: data,
    ...options
  })
}

// 获取建库数据列表
export function getBuildLibDataList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/get_lib_list',
    data: data,
    ...options
  })
}

// 导出建库数据
export function exportBuildLibData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/export_lib',
    data: data,
    responseType: 'blob',
    ...options
  })
}
// 导出建库数据
export function cancelBuildLib (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/cancel_lib',
    data: data,
    ...options
  })
}

export function batchModifyBuildStatus (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/remark_exception',
    data: data,
    ...options
  })
}

export function remarkAddTest (data, options = {}) {
  return myAjax({
    url: '/experiment/new_build_lib/remark_add_test',
    data: data,
    ...options
  })
}

// 确认计算
export function confirmComputed (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/computer_practice',
    data: data,
    responseType: 'blob',
    ...options
  })
}

// 重建库
export function rebuildLibrary (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/rebuild_lib',
    data: data,
    ...options
  })
}

// 获取上机实践信息
export function getComputerPracticeInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/computer_practice_info',
    data: data,
    ...options
  })
}

export function setComputerPracticeInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/computer_practice',
    data: data,
    ...options
  })
}

export function checkQcAnalyse (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/check_quality_analysis_or_delivery',
    data: data,
    ...options
  })
}

// 开始QC分析
export function startQcAnalysis (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/update_quality_analysis_or_delivery',
    data: data,
    ...options
  })
}

// 数据交付
export function dataDelivery (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/update_quality_analysis_or_delivery',
    data: data,
    ...options
  })
}

export function getSampleMonitoringList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/get_monitor_list',
    data: data,
    ...options
  })
}

/**
 * 导出数据监控列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799677
 * @param options
 * @returns {*}
 */
export function exportSampleMonitoring (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/export_cos_monitor',
    responseType: 'blob',
    data: data,
    ...options
  })
}

// 批量修改状态
export function batchModifyStatus (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/remark_exception',
    data: data,
    ...options
  })
}

/**
 * 获取下单数据量
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799681
 * @param options
 * @returns {*}
 */
export function getOrderDetail (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/get_threshold_list',
    data: data,
    ...options
  })
}

/**
 * 获取测序数据
 * @param data 参数对象,包含fid
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function getSequencingData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/get_deplane_list',
    data: data,
    ...options
  })
}

/**
 * 获取质控分析列表
 * @param data 参数对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function getQcAnalyseList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/get_analysis_task_list',
    data: data,
    ...options
  })
}

/**
 * 获取质控分析详情
 * @param data 参数对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function getQcAnalyseDetail (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/get_analysis_task_detail',
    data: data,
    ...options
  })
}
/**
 * 获取质控分析详情
 * @param data 参数对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function getDeliverOrderList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_deliver/get_deliver_order_list',
    data: data,
    ...options
  })
}

export function getSubDeliverOrderList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_deliver/get_deliver_order_detail',
    data: data,
    ...options
  })
}

export function downloadReportFile (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_quality_monitor/download_zip',
    data: data,
    responseType: 'blob',
    ...options
  })
}

/**
 * 获取下载字段
 */
export function getDownloadFields (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/export_lib_filed',
    data: data,
    ...options
  })
}

/**
 * 更新建库信息
 */
export function updateBuildInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/update_new_lib_result',
    data: data,
    ...options
  })
}

export function fixInfoApi (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/new_build_lib/update_oligo_data',
    data: data,
    ...options
  })
}
