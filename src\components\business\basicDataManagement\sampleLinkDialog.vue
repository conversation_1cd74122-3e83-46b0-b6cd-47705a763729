<template>
  <div>
    <el-dialog
      title="关联术后样本"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="40%"
      :before-close="handleClose"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="个性化探针名称">
          <el-input v-model="name" size="mini" disabled></el-input>
        </el-form-item>
        <el-form-item label="样例编号" prop="sampleCode">
          <el-input v-model.trim="form.sampleCode" size="mini" clearable></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button size="mini" type="primary" :loading="loading" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    name: {
      type: String
    }
  },
  data () {
    return {
      loading: false,
      form: {
        sampleCode: ''
      },
      rules: {
        sampleCode: [{
          required: true, message: '请输入样例编号', trigger: ['blur']
        }]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.handleLinkSample()
        }
      })
    },
    // 关联术后样本
    handleLinkSample () {
      this.$ajax({
        url: '/system/probe/samplenum_probe_relevance',
        data: {
          sampleNum: this.form.sampleCode,
          probeName: this.name
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('关联成功')
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped></style>
