import { myAjax } from '@/util/ajax'

/**
 * 修改存储位置
 * @param data
 * @param options
 * @returns {*}
 */
export function fixPosition (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/sample/update_sample_place',
    data: data,
    ...options
  })
}

/**
 * 获取样本列表更具样本编号或文件
 * @param data
 * @param options
 * @returns {*}
 */
export function getSampleBySampleCode (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/sample/can_sign_sample',
    data: data,
    ...options
  })
}

/**
 * 标记操作
 * @param data
 * @param options
 * @returns {*}
 */
export function signOperate (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/sample/sign_selected_sample',
    data: data,
    ...options
  })
}

/**
 * 判断是否存在珍贵样本
 * @param data
 * @param options
 */
export function isExistPreciousSample (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/sSampleProduct/get_fis_precious_sample_by_sample_num',
    data: data,
    ...options
  })
}
