<template>
  <div>
    <div v-if="deviceSize === 'mini' && showMenu" class="dark-mask" @click="showMenu = false"></div>
    <div style="display: flex;align-items: start;">
      <el-scrollbar :class="{'fixed-menu': deviceSize === 'mini'}" :style="{width: menuWidth}" class="menu-container">
        <div class="aside-container">
          <div class="system-title"><span v-if="!(deviceSize === 'mini' && !showMenu)">运营后台</span></div>
          <div class="user-info">
            <template v-if="userInfo.avatar">
              <el-avatar :src="userInfo.avatar"></el-avatar>
            </template>
            <template v-else>
              <el-avatar icon="el-icon-user-solid"></el-avatar>
            </template>
            <div v-if="!menuStatus" class="name">{{userInfo.username}}</div>
          </div>
          <div>
            <el-menu
              :default-active="activeIndex"
              :collapse="!!menuStatus"
              background-color="#1E2933"
              text-color="#fff"
              active-text-color="#ffd04b"
              @select="handleMenuSelect">
              <el-menu-item-group>
                <template slot="title">组</template>
                <el-menu-item index="1">
                  <i class="el-icon-setting"></i>
                  <span slot="title">页面1</span>
                </el-menu-item>
                <el-menu-item index="2">
                  <i class="el-icon-setting"></i>
                  <span slot="title">页面2</span>
                </el-menu-item>
              </el-menu-item-group>
            </el-menu>
          </div>
        </div>
      </el-scrollbar>
      <div class="main-container">
        <div>
          <div style="height: 65px; background: #fff;display: flex;align-items: center;">
            <div style="font-size: 30px;cursor: pointer;">
              <i v-if="showIcon" class="el-icon-s-unfold" @click="toggleMenuStatus"></i>
              <i v-else class="el-icon-s-fold" @click="toggleMenuStatus"></i>
            </div>
          </div>
        </div>
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
// import num from './components/cc'
import { mapState } from 'vuex'
export default {
  beforeMount () {
    window.addEventListener('resize', this.$_resizeHandler)
  },
  mounted () {
    const ismini = this.$_screenSize()
    if (ismini) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'deviceSize',
        deviceSize: 'mini'
      })
    }
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.$_resizeHandler)
  },
  watch: {
    $route (val, oldVal) {
      this.setActiveIndex(val.path)
    }
  },
  computed: {
    ...mapState({ // 辅助函数，直接获取
      deviceSize: state => state.deviceSize
    }),
    userInfo: function () {
      return this.$store.getters.getValue('userInfo')
    },
    menuStatus: function () {
      return this.$store.getters.getValue('menuStatus')
    },
    activeIndex: function () {
      return this.$store.getters.getValue('activeIndex')
    },
    menuWidth () {
      if (this.deviceSize === 'normal') {
        return this.menuStatus ? '80px' : '200px'
      } else if (this.deviceSize === 'mini') {
        return this.showMenu ? '200px' : 0
      }
    },
    showIcon () {
      if (this.deviceSize === 'normal') {
        return !!this.menuStatus
      } else if (this.deviceSize === 'mini') {
        return true
      }
    }
  },
  data () {
    return {
      showMenu: false,
      dialogTableVisible: false,
      WIDTH: 992
    }
  },
  methods: {
    toggleMenuStatus () {
      if (this.deviceSize === 'mini') {
        this.showMenu = true
      } else {
        this.$store.commit({
          type: 'old/setValue',
          category: 'menuStatus',
          menuStatus: Number(!this.menuStatus)
        })
      }
    },
    $_screenSize () {
      const rect = document.body.getBoundingClientRect()
      return rect.width - 1 < this.WIDTH
    },
    $_resizeHandler () {
      if (!document.hidden) {
        const ismini = this.$_screenSize()
        this.$store.commit({
          type: 'old/setValue',
          category: 'deviceSize',
          deviceSize: ismini ? 'mini' : 'normal'
        })
      }
    },
    handleMenuSelect (key) {
      this.$router.push(key)
    },
    setActiveIndex (path) {
      let paths = [
        '1',
        '2'
      ]
      if (paths.indexOf(path) > -1) {
        this.$store.commit({
          type: 'old/setValue',
          category: 'activeIndex',
          activeIndex: path
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-menu--collapse{
    width: 80px;
  }
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  /deep/ .el-menu{
    border-right: none;
  }
  $main-color: #fff;
  $sub-color: #9BA4B0;
  .aside-container{
    width: 100%;
    background: #1E2933;
    color: $main-color;
    min-height: 100vh;
    overflow-y: auto;
    .system-title{ // 系统名称文字样式
      height: 65px;
    }
    .user-info{ // 用户头像姓名信息
      height: 85px;
      .avatar{
        height: 50px;
        width: 50px;
        border-radius: 50%;
        overflow: hidden;
        background: #9890A5;
      img{
        width: 100%;
        height: auto;
        display: block;
      }
      }
      .name{
        margin-left: 10px;
      }
    }
    .user-info, .system-title{
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom: 1px solid #323E4A;
    }
  }
  .el-input__icon{
    margin-right: 15px;
    font-size: 20px;
  }
  .dark-mask{
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background: #000;
    opacity: 0.3;
    z-index: 999;
  }
  .main-container{
    height: 100vh;
    overflow-y: auto;
    width: 100%
  }
  .menu-container{
    transition: width 0.28s;
    background: #545c64;
    height: 100vh;
    z-index: 1000;
    overflow-x: hidden;
    flex-shrink: 0;
  }
  .fixed-menu{
    position: absolute;
    top: 0;
    left: 0;
  }
</style>
