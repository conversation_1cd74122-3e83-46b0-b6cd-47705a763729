<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible" :modal="false"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="50%"
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="mini" label-suffix="：">
          <el-row :gutter="10">
           <el-col :span="12">
             <el-form-item label="诊断" prop="diagnosCancer">
               <el-input v-model.trim="form.diagnosCancer" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="确诊年龄" prop="diagnosAge">
               <el-input v-model.number="form.diagnosAge" placeholder="请输入" @blur="handleGetDiagnosTime"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="12">
              <el-form-item label="癌种" prop="cancer">
                <el-cascader
                  :options="cancerList"
                  v-model="form.cancer"
                  :props ="{label: 'name', value: 'cancerId',checkStrictly: true}"
                  style="width: 100%"
                  clearable
                  filterable
                  placeholder="请选择"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="病理诊断" prop="pathologicDiagnosis">
                <el-input v-model.trim="form.pathologicDiagnosis " maxlength="100" placeholder="请输入" @blur="handleGetDiagnosTime"></el-input>
              </el-form-item>
            </el-col>
           <el-col :span="12">
             <el-form-item label="确诊日期" prop="diagnosTime">
               <my-date-picker v-model="form.diagnosTime" @change="handleChange"></my-date-picker>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="复发日期" prop="recrudescenceTime">
               <my-date-picker v-model="form.recrudescenceTime"></my-date-picker>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="是否有病理">
               <el-radio-group v-model="form.pathology">
                 <el-radio :label="1">有</el-radio>
                 <el-radio :label="0">无</el-radio>
               </el-radio-group>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="目前临床分期">
               <el-select v-model="form.clinicalStages1" clearable placeholder="请选择" style="width: 120px" @change="handleClinicalStagesChange">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in clinicalStages1List">
                 </el-option>
               </el-select>
               <el-select v-model="form.clinicalStages2" :disabled="form.clinicalStages1 === '不详'" clearable placeholder="请选择" style="width: 120px">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in clinicalStages2List">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="24">
             <el-form-item label="TNM分期">
               T：<el-select v-model="form.tnmTstatges" placeholder="请选择" clearable style="width: 100px;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in tList">
                 </el-option>
               </el-select>
               N：<el-select v-model="form.tnmNstatges" placeholder="请选择" clearable style="width: 100px;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in nList">
                 </el-option>
               </el-select>
               M：<el-select v-model="form.tnmMstatges" placeholder="请选择" clearable style="width: 100px;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in mList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="24">
             <el-form-item label="转移部位">
               <el-checkbox-group v-model="form.cancerMetastasisSites" @change="handCheckboxChange">
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1 || form.cancerMetastasisSites.indexOf('不详') !== -1" label="骨"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1 || form.cancerMetastasisSites.indexOf('不详') !== -1" label="脑"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1 || form.cancerMetastasisSites.indexOf('不详') !== -1" label="肝"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1 || form.cancerMetastasisSites.indexOf('不详') !== -1" label="肺"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1 || form.cancerMetastasisSites.indexOf('不详') !== -1" label="淋巴结"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('不详') !== -1" label="无"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1" label="不详"></el-checkbox>
                 <el-checkbox :disabled="form.cancerMetastasisSites.indexOf('无') !== -1 || form.cancerMetastasisSites.indexOf('不详') !== -1" label="其他"></el-checkbox>
               </el-checkbox-group>
             </el-form-item>
           </el-col>
           <el-col :span="12" v-if="this.form.cancerMetastasisSites.indexOf('其他') !== -1">
             <el-form-item label="其他转移部位" prop="otherCancerMetastasisSites" label-width="120px">
               <el-input v-model.trim="form.otherCancerMetastasisSites" maxlength="50" size="mini" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
export default {
  name: 'clinicalInfoManagementDiagnosedCancerSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    myDatePicker
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      },
      required: true
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      title: '新增癌种信息',
      loading: false,
      form: {
        cancer: [],
        clinicalStages1: '',
        clinicalStages2: '',
        other: '',
        birthday: '',
        sampleClinicalCancerInfoId: '',
        sampleBasicId: '',
        diagnosCancer: '',
        firstcancerNameId: '',
        secondcancerNameId: '',
        thirdcancerNameId: '',
        fourthcancerNameId: '',
        fivecancerNameId: '',
        sixcancerNameId: '',
        popularCancerTypeId: '',
        diagnosAge: '',
        diagnosTime: '',
        pathologicDiagnosis: '',
        recrudescenceTime: '',
        pathology: '',
        cancerTypeInfo: '',
        otherCancer: '',
        clinicalStages: '',
        tnmTstatges: '',
        tnmNstatges: '',
        tnmMstatges: '',
        cancerMetastasisSites: [],
        otherCancerMetastasisSites: ''
      },
      cancerList: [],
      clinicalStages1List: [
        {
          value: 'Ⅰ',
          label: 'Ⅰ'
        },
        {
          value: 'Ⅱ',
          label: 'Ⅱ'
        },
        {
          value: 'Ⅲ',
          label: 'Ⅲ'
        },
        {
          value: 'Ⅳ',
          label: 'Ⅳ'
        },
        {
          value: '不详',
          label: '不详'
        }
      ],
      clinicalStages2List: [
        {
          value: 'a',
          label: 'a'
        },
        {
          value: 'b',
          label: 'b'
        },
        {
          value: 'c',
          label: 'c'
        }
      ],
      tList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: 'is',
          label: 'is'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        },
        {
          value: '4',
          label: '4'
        },
        {
          value: 'X',
          label: 'X'
        }
      ],
      nList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        },
        {
          value: 'X',
          label: 'X'
        }
      ],
      mList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: 'X',
          label: 'X'
        }
      ],
      rules: {
        diagnosCancer: [
          {required: true, message: '请输入诊断', trigger: 'blur'}
        ],
        cancer: [
          {required: true, message: '请选择癌种', trigger: ['blur', 'change']}
        ],
        otherCancerMetastasisSites: [
          {required: true, message: '请输入其他转移部位', trigger: 'blur'}
        ],
        diagnosAge: [
          {pattern: /^\d+$/, message: '格式错误', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    // 前置条件：一级癌症为“肺”，且转移部位不是“淋巴结、无、不详时”
    // 满足前置条件时，临床分期默认为“IV”（可手动修改。存储最终结果）【触发点：改动转移部位时判断】
    handleSetClinicalStages1 () {},
    getCancerList () {
      this.$ajax({
        url: '/sample/basic/get_all_cancer',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.cancerList = result.data
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleChange (value) {
      if (this.form.birthday) {
        console.log(value, this.form.birthday)
        let time = new Date(this.form.birthday).getTime()
        let nowTime = new Date(value).getTime()
        console.log(time, nowTime)
        let diagnosAge = Math.ceil((nowTime - time) / (365 * 24 * 60 * 60 * 1000))
        this.$set(this.form, 'diagnosAge', diagnosAge > 0 ? diagnosAge : '')
      }
    },
    handleOpen () {
      this.getCancerList()
      this.$nextTick(() => {
        this.form = {
          cancer: [],
          clinicalStages1: '',
          clinicalStages2: '',
          other: '',
          birthday: '',
          sampleClinicalCancerInfoId: '',
          sampleBasicId: '',
          diagnosCancer: '',
          firstcancerNameId: '',
          secondcancerNameId: '',
          thirdcancerNameId: '',
          fourthcancerNameId: '',
          fivecancerNameId: '',
          sixcancerNameId: '',
          popularCancerTypeId: '',
          diagnosAge: '',
          diagnosTime: '',
          pathologicDiagnosis: '',
          recrudescenceTime: '',
          pathology: '',
          cancerTypeInfo: '',
          otherCancer: '',
          clinicalStages: '',
          tnmTstatges: '',
          tnmNstatges: '',
          tnmMstatges: '',
          cancerMetastasisSites: [],
          otherCancerMetastasisSites: ''
        }
        this.form = Object.assign({}, this.form, this.pdata)
        if (this.form.firstcancerNameId) {
          this.form.cancer.push(this.form.firstcancerNameId)
        }
        if (this.form.secondcancerNameId) {
          this.form.cancer.push(this.form.secondcancerNameId)
        }
        if (this.form.thirdcancerNameId) {
          this.form.cancer.push(this.form.thirdcancerNameId)
        }
        if (this.form.fourthcancerNameId) {
          this.form.cancer.push(this.form.fourthcancerNameId)
        }
        if (this.form.fivecancerNameId) {
          this.form.cancer.push(this.form.fivecancerNameId)
        }
        if (this.form.sixcancerNameId) {
          this.form.cancer.push(this.form.sixcancerNameId)
        }
        this.$refs.form.resetFields()
        if (this.form.sampleClinicalCancerInfoId) {
          this.title = '编辑癌种信息'
        } else {
          this.title = '新增癌种信息'
        }
      })
    },
    async handleChangeTagField (data) {
      const result = await this.$ajax({
        url: '/sample/clinical/is_update_sign',
        data: data,
        // cancelToken: this.axiosSource.token,
        showErrorMessageBox: false
      })
      return result.code === this.SUCCESS_CODE
    },
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          if (this.form.cancerMetastasisSites.indexOf('其他') !== -1) {
            this.form.cancerMetastasisSites.splice(this.form.cancerMetastasisSites.indexOf('其他'), 1)
            this.form.cancerMetastasisSites.push('其他')
            this.form.cancerMetastasisSites.push(this.form.otherCancerMetastasisSites)
          }
          const data = {
            sampleClinicalCancerInfoId: this.form.sampleClinicalCancerInfoId,
            sampleBasicId: this.form.sampleBasicId,
            diagnosCancer: this.form.diagnosCancer,
            firstcancerNameId: this.form.cancer[0] || '',
            secondcancerNameId: this.form.cancer[1] || '',
            thirdcancerNameId: this.form.cancer[2] || '',
            fourthcancerNameId: this.form.cancer[3] || '',
            fivecancerNameId: this.form.cancer[4] || '',
            sixcancerNameId: this.form.cancer[5] || '',
            popularCancerTypeId: this.form.popularCancerTypeId,
            diagnosAge: this.form.diagnosAge,
            diagnosTime: this.form.diagnosTime,
            fpathologicDiagnosis: this.form.pathologicDiagnosis,
            recrudescenceTime: this.form.recrudescenceTime,
            pathology: this.form.pathology,
            cancerTypeInfo: this.form.cancerTypeInfo,
            otherCancer: this.form.otherCancer,
            clinicalStages: this.form.clinicalStages1 + ' ' + this.form.clinicalStages2,
            tnmTstatges: this.form.tnmTstatges,
            tnmNstatges: this.form.tnmNstatges,
            tnmMstatges: this.form.tnmMstatges,
            cancerMetastasisSites: this.form.cancerMetastasisSites.toString()
          }
          // 校验 是否跟新更标签
          if (await this.handleChangeTagField(data)) {
            await this.$confirm('样本已流入解读相关环节，请确认是否更改信息', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
          }
          this.$ajax({
            url: '/sample/clinical/save_cancer_type_info',
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('diagnosedCancerSaveDialogConfirmEvent')
            } else {
              this.$message.errors(result.message)
            }
          }).catch(() => {
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    getChildrenCancer (node, resolve) {
      if (node.level && !node.isLeaf) {
        this.$ajax({
          url: '/cancertype/list_cancers_by_cancer_class',
          data: {
            cancerClass: node.data.cancerClass + 1,
            parentId: node.data.id
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let data = result.data
            let item = {}
            let list = []
            data.forEach(v => {
              item = {
                label: v.cancerTypeName,
                value: v.cancerTypeId,
                id: v.cancerTypeId,
                cancerClass: v.cancerClass,
                leaf: !v.haveLowerElement
              }
              list.push(item)
            })
            resolve(list)
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        resolve([])
      }
    },
    handCheckboxChange (value) {
      console.log(value, this.form.cancer)
      if (value[value.length - 1] === ('无')) {
        this.$set(this.form, 'cancerMetastasisSites', ['无'])
      } else if (value[value.length - 1] === ('不详')) {
        this.$set(this.form, 'cancerMetastasisSites', ['不详'])
      } else if (value.indexOf('其他') !== -1) {
        this.form.otherCancer = ''
      }
      // if (value[value.length - 1] !== ('不详') && value[value.length - 1] !== ('无') && value.indexOf('淋巴结') === -1 && this.form.cancer[0] === 74) {
      //   this.form.clinicalStages1 = 'IV'
      // }
    },
    handleClinicalStagesChange () {
      this.form.clinicalStages2 = ''
    },
    handleGetDiagnosTime () {
      if (this.form.birthday && this.form.diagnosAge) {
        // let diagnosTime = ''
        // diagnosTime = Number(this.form.birthday) + this.form.diagnosAge
        // this.$set(this.form, 'diagnosTime', diagnosTime.toString())
        let birth = this.form.birthday.slice(0, 4)
        let diagnosTime = Number(birth) + this.form.diagnosAge
        this.$set(this.form, 'diagnosTime', diagnosTime.toString())
      }
    }
  }
}
</script>

<style scoped>

</style>
