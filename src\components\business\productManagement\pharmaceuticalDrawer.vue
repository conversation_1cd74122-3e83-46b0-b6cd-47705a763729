<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :with-header="false"
      :size="'40%'"
      :wrapper-closable="false"
      @open="handleOpen">
      <el-form :model="form" style="padding: 20px 5px" label-position="right" label-width="120px" label-suffix=":">
        <el-form-item label="产品分类编码">
          {{form.fproductTypeCode}}
        </el-form-item>
        <el-form-item label="产品分类名称">
          {{form.fproductTypeName}}
        </el-form-item>
        <el-form-item label="产品/套餐编码">
          {{form.fproductCode}}
        </el-form-item>
        <el-form-item label="产品/套餐名称">
          {{form.fproductName}}
        </el-form-item>
        <div class="title">产品</div>
        <el-form-item label="样本类型">
          <div style="padding-right: 20px;">{{form.fsampleType}}</div>
        </el-form-item>
        <el-form-item label="探针/芯片">
          {{form.fprobe}}
        </el-form-item>
        <el-form-item label="数据量">
          {{form.fnum}}
        </el-form-item>
        <el-form-item label="测序平台">
          {{form.fseqPlat}}
        </el-form-item>
        <el-form-item label="生产实验室">
          {{form.fprocedureLaboratory}}
        </el-form-item>
        <div class="title">产品基本信息</div>
        <el-form-item label="是否审核">
          {{form.fisCheck}}
        </el-form-item>
        <el-form-item label="检测样本">
          {{form.fsampleTest}}
        </el-form-item>
        <el-form-item label="基因数目">
          {{form.fgeneNum}}
        </el-form-item>
        <el-form-item label="适用人群">
          {{form.fusePeople}}
        </el-form-item>
        <el-form-item label="产品字母条码">
          {{form.fproductBarcode}}
        </el-form-item>
        <el-form-item label="生产片区">
          {{form.fprocedureArea}}
        </el-form-item>
        <el-form-item label="靶向药物数目">
          {{form.ftargetDrugNum}}
        </el-form-item>
        <el-form-item label="产品描述">
          {{form.fproductDes}}
        </el-form-item>
        <el-form-item label="癌种">
          {{form.fcancerName}}
        </el-form-item>
        <el-form-item label="备注">
          {{form.fnote}}
        </el-form-item>
        <div class="title">其他</div>
        <div style="display: flex;align-items: center;justify-content: space-around;margin: 10px;">
          <span>创建人: {{form.fcreator}}</span>
          <span>事业部：{{form.fworkPart}}</span>
          <span>创建时间：{{form.fcreateTime}}</span>
        </div>
<!--        <div>修改记录</div>-->
<!--        <hr/>-->
<!--        <el-table-->
<!--          ref="table"-->
<!--          size="mini"-->
<!--          height="200"-->
<!--          :data="tableData"-->
<!--          style="width:100%">-->
<!--        </el-table>-->
        <div  class="button-wrapper" style=""><el-button size="mini" @click="handleClose">返回</el-button></div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'sequenceProductManagement',
  mixins: [mixins.dialogBaseInfo],
  props: {
    fid: {
      type: Number
    }
  },
  data () {
    return {
      form: {
        productCategoryCode: '',
        tableData: []
      }
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/system/otherProduct/get_drug_factory_product_detail',
        method: 'get',
        data: {
          fotherProductId: this.fid
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form = {
            fid: data.fid,
            fproductTypeCode: data.fproductTypeCode,
            fproductTypeName: data.fproductTypeName,
            fproductFirstCode: data.fproductFirstCode,
            fproductFirstName: data.fproductFirstName,
            fproductCode: data.fproductCode,
            fproductName: data.fproductName,
            fsampleType: data.fsampleType,
            fprobe: data.fprobe,
            fnum: data.fnum,
            fseqPlat: data.fseqPlat,
            ftat: data.ftat,
            fanalysisCode: data.fanalysisCode,
            freadCode: data.freadCode,
            freportTemplateCode: data.freportTemplateCode,
            fproductBasic: data.fproductBasic,
            fisCheck: data.fisCheck === 0 ? '否' : '是',
            fsampleTest: data.fsampleTest,
            fgeneNum: data.fgeneNum,
            fusePeople: data.fusePeople,
            fproductBarcode: data.fproductBarcode,
            fprocedureArea: data.fprocedureArea,
            ftargetDrugNum: data.ftargetDrugNum,
            fproductDes: data.fproductDes,
            fcancerName: data.fcancerName,
            fnote: data.fnote,
            fupdateNote: data.fupdateNote,
            fprocedureLaboratory: data.fprocedureLaboratory, // 生产试验室
            fstate: data.fstate,
            fcreator: data.fcreator,
            fcreateTime: data.fcreateTime,
            fupdator: data.fupdator,
            fworkPart: data.fworkPart,
            fupdateTime: data.fupdateTime
          }
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    }
  }
}
</script>

<style scoped>
>>>.el-form-item {
  margin: 10px;
  color: #606266;
}
/*解决办法：*/
/*1.显示滚动条：当内容超出容器的时候，可以拖动：*/
>>>.el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}

/*2.隐藏滚动条，太丑了*/
>>>.el-drawer__container ::-webkit-scrollbar {
  display: none;
}

.title{
  margin: 20px 0 10px;
  padding: 0 10px;
  height: 35px;
  line-height: 35px;
  border-bottom: 1px solid #DCDFE6;
}
.button-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 10px;
  padding: 5px;
  height: 40px;
  width: 100%;
  box-shadow: 19px -2px 26px -8px rgba(49,44,44,0.75);
}
</style>
