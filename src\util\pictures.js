import vueLogo from '../assets/logo.png'

let context = window.localStorage.getItem('PROTOCOL') + '://' +
  window.localStorage.getItem('IP') + ':' +
  window.localStorage.getItem('PORT') + '/' +
  window.localStorage.getItem('PROJECT')

let geneplusLogo = context + '/images/geneplus-logo.png'
let loginBg = context + '/images/bg.png'

export default {
  VUE_LOGO: vueLogo,
  GENEPLUS_LOGO: geneplusLogo,
  LOGINBG: loginBg
}
