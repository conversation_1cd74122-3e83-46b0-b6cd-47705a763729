import { getSessionInfo, setSessionInfo, removeSessionInfo } from 'Util'

const uploadData = getSessionInfo('uploadData') || {
  dailyProductionList: [],
  dailySequenceList: [],
  dailyReportList: []
}

const state = {
  uploadData: uploadData
}

const mutations = {
  SET_UPLOAD_DATA: (state, uploadData) => {
    state.uploadData = uploadData
    setSessionInfo('uploadData', uploadData)
  },
  REMOVE_UPLOAD_DATA (state) {
    state.uploadData = {
      dailyProductionList: [],
      dailySequenceList: [],
      dailyReportList: []
    }
    removeSessionInfo('uploadData')
  }
}

const actions = {
  setUploadData ({ commit }, uploadData) {
    return new Promise(resolve => {
      commit('SET_UPLOAD_DATA', uploadData)
      resolve()
    })
  },
  removeUploadData ({ commit }) {
    return new Promise(resolve => {
      commit('REMOVE_UPLOAD_DATA')
      resolve()
    })
  }
}

export default {
  state,
  mutations,
  actions
}
