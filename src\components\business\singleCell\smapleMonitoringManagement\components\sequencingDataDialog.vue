<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="95vw"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @opened="initTableData"
  >
    <div class="sequencing-data-dialog">
      <el-table
        :data="tableData"
        border
        class="table"
        size="mini"
        height="600"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="orderTime"
          label="下单时间"
          min-width="120"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="dataAmount"
          label="下单数据量/M"
          min-width="120"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="belongType"
          label="归属类型"
          min-width="120"
          show-overflow-tooltip
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="libraryCode"
          label="文库编号"
          show-overflow-tooltip
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="fcCode"
          label="FC编号"
          min-width="120"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="startTime"
          label="上机时间"
          min-width="120"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="endTime"
          label="下机时间"
          show-overflow-tooltip
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="output"
          label="产出"
          show-overflow-tooltip
          min-width="100"
          align="center"
        />
        <el-table-column
          prop="difference"
          label="差额"
          min-width="100"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="isQualified"
          label="数据量是否达标"
          show-overflow-tooltip
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="isQcQualified"
          label="是否达到质控标准"
          show-overflow-tooltip
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="isResplit"
          label="是否重拆"
          show-overflow-tooltip
          min-width="100"
          align="center"
        />
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import { getSequencingData } from '@/api/sequencingManagement/singleCell'
import util, { awaitWrap } from '@/util/util'

export default {
  name: 'SequencingDataDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    geneNum: {
      type: String,
      default: null
    },
    libType: {
      type: Number,
      default: null
    }
  },
  computed: {
    totalAmount () {
      return this.tableData.reduce((sum, row) => sum + Number(row.output || 0), 0).toFixed(2)
    }
  },
  data () {
    return {
      tableData: [],
      title: '',
      belongTypeMap: {
        1: '订单下单',
        2: '申请加测',
        3: '吉云补测'
      }
    }
  },
  methods: {
    getBelongTypeText (type) {
      return this.belongTypeMap[type] || '-'
    },
    async initTableData () {
      this.title = '文库下单及产出详情'
      // 调用接口获取数据
      const params = {
        fgeneNum: this.geneNum
      }
      // 模拟数据,实际项目中应该从接口获取
      this.tableData = []
      const { res = {} } = await awaitWrap(getSequencingData(params, {
        loadingDom: '.table'
      }))
      if (res.code === this.SUCCESS_CODE) {
        const booleanMap = {
          0: '否',
          1: '是'
        }
        // 映射后端字段到前端字段
        this.tableData = (res.data || []).map(item => {
          const v = {
            orderTime: item.fcreateOrderTime,
            dataAmount: item.forderDataSize,
            belongType: item.forderType,
            libraryCode: item.flibNum,
            fcCode: item.ffcNum,
            startTime: item.fsequenceTime,
            endTime: item.fdeplaneTime,
            output: item.fdataSize,
            difference: item.fdifference,
            // diffDesc: item.fdifferenceInfo,
            isQualified: booleanMap[item.fisDataStandard],
            isQcQualified: booleanMap[item.fisQualityStandard],
            remark: item.fremark,
            qcTaskId: item.fqcTaskId,
            isResplit: booleanMap[item.fisDismantleAgain]
          }
          v.realData = JSON.parse(JSON.stringify(v))
          util.setDefaultEmptyValueForObject(v)
          return v
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.total-info {
  margin-top: 20px;

  .total-item {
    display: flex;
    justify-content: flex-end;
    font-size: 14px;

    .label {
      margin-right: 10px;
      font-weight: bold;
      color: #606266;
    }

    .value {
      font-weight: bold;
      color: #303133;
    }
  }
}
</style>
