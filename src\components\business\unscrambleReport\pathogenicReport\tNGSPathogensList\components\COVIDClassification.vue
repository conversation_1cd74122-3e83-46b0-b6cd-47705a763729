<template>
  <div>
    <el-table
      ref="table"
      border
      :data="tableData"
      style="width: 100%"
      :cell-style="handleCheckCell"
      :height="'calc(100% - 32px)'"
      class="dataFilterTable"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @row-click="handleRowClick"
    >
      <el-table-column :align="getColumnType['lineage']" prop="lineage"
                       label="lineage"  show-overflow-tooltip/>
      <el-table-column :align="getColumnType['seqName']" prop="seqName"
                       label="seqName"  show-overflow-tooltip/>
      <el-table-column :align="getColumnType['clade']" prop="clade"
                       label="clade"  show-overflow-tooltip/>
      <el-table-column :align="getColumnType['nextcladePango']" prop="nextcladePango"
                       label="Nextclade_pango"  show-overflow-tooltip/>
      <el-table-column :align="getColumnType['partiallyAliased']" prop="partiallyAliased"
                       label="partiallyAliased"  show-overflow-tooltip/>
      <el-table-column :align="getColumnType['cladeNextstrain']" prop="cladeNextstrain"
                       label="clade_nextstrain"  show-overflow-tooltip/>
      <el-table-column :align="getColumnType['cladeWho']" prop="cladeWho"
                       label="clade_who"  show-overflow-tooltip/>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      :total="totalPage">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh"/>
      </button>
    </el-pagination>
  </div>
</template>

<script>

import mixins from '@/util/mixins'
import util from '@/util/util'
import {getTypeArray} from '../../../../../../util/util'
export default {
  name: 'COVIDClassification',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    type: {
      type: String
    },
    refresh: {
      type: Boolean
    }
  },
  mounted () {
    this.getData()
    // 处理拉动列名时，fix列的行位置与非fix列的行位置错位
    const table = document.querySelector('.el-table')
    table.addEventListener('click', async (e) => {
      await this.$nextTick()
      this.$refs.table.doLayout()
    })
  },
  beforeDestroy () {
    const table = document.querySelector('.el-table')
    if (table) {
      table.removeEventListener('click', async (e) => {
        await this.$nextTick()
        this.$refs.table.doLayout()
      })
    }
  },
  watch: {
    refresh: {
      handler (newVal) {
        if (newVal) {
          this.getData()
        }
      }
    }
  },
  data () {
    return {
      tableData: [],
      selectedRows: new Map(),
      getColumnType: []
    }
  },
  methods: {
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/get_covid_somatotype',
        data: {
          analysisId: this.$route.query.oxym,
          current: this.currentPage + '',
          size: this.pageSize + ''
        },
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        let rows = data.rows || []
        this.totalPage = data.total
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            lineage: v.flineage,
            seqName: v.fseqName,
            clade: v.fclade,
            nextcladePango: v.fnextcladePango,
            partiallyAliased: v.fpartiallyAliased,
            cladeNextstrain: v.fcladeNextstrain,
            cladeWho: v.fcladeWho
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        this.getColumnType = getTypeArray(this.tableData)
      }
    },
    // 动态行样式
    handleCheckCell ({row}) {
      const id = row.id
      // 1.let rows = [...this.selectedRows.values()]
      // const isSelect = rows.some(v => v.id === id)
      // 2.this.selectedRows.has(id)
      return this.selectedRows.has(id) ? 'background: #ecf6ff' : ''
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
      this.$emit('handleRowClick', this.selectedRows)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
      this.$emit('handleSelect', this.selectedRows)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.$emit('handleSelectAll', this.selectedRows)
    }
  }
}
</script>

<style scoped lang="scss">
</style>
