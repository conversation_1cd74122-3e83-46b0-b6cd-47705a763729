<template>
  <div>
    <el-dialog
      :title="`设置${changeText('架')}`"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="750px"
      @open="handleOpen">
      <div>
        <div class="form">
          <div class="form-item">
            <label>容器名称：</label>
            <el-input :style="{width: formWidth}" v-model="currentOperateName" disabled size="mini"></el-input>
          </div>
          <div class="form-item">
            <label>添加{{changeText('架')}}数：</label>
            <el-select v-model="shelfTotal" :style="{width: formWidth}" size="mini" filterable @change="handleChangeShelfNum">
              <template v-for="item in maxShelfNum">
                <el-option :key="item" :label="item" :value="item"></el-option>
              </template>
            </el-select>
          </div>
          <template v-if="shelfTotal">
            <template v-for="(item, index) in shelfSampleTypeInput">
              <div :key="index" class="form-item">
                <label>{{changeText('架')}}数范围：</label>
                <el-input :style="{width: formWidth}" v-model="item.shelfNums" size="mini" @blur="handleChangeShelfInfo(index, 'shelfNums')"></el-input>
              </div>
              <div :key="index + 'sample'" class="form-item">
                <label>样本类型：</label>
                <el-select
                  v-model="item.sampleType"
                  :disabled="item.shelfNums.length === 0"
                  :style="{width: formWidth}"
                  size="mini"
                  multiple
                  collapse-tags
                  filterable
                  @change="handleChangeShelfInfo(index, 'sampleType')">
                  <template v-for="item in sampleTypeOptions">
                    <el-option :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </template>
                </el-select>
              </div>
              <div :key="index + 'icon'">
                <i
                  v-if="index === shelfSampleType.length - 1 && showAddIcon"
                  class="el-icon-plus icon"
                  style="font-size: 30px;font-weight: 600;cursor: pointer;"
                  @click="handleAddShelfSampleType"></i>
              </div>
            </template>
          </template>
        </div>
        <template v-if="shelf.length > 0">
          <div class="shelf">
            <template v-for="item in shelf">
              <div :key="item.num" class="shelf-item">
                <p>{{item.num}}{{changeText('架')}}</p>
                <p>{{item.sampleTypes.toString()}} </p>
              </div>
            </template>
          </div>
        </template>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    currentOperateName: { // 当前名称
      type: String
    },
    sampleTypeOptions: { // 样本值
      type: Array,
      default () {
        return []
      }
    },
    currentSetObj: {
      type: Object
    },
    containerData: { // 全部数据
      type: Array
    },
    currentFloor: { // 当前层
      type: Array
    },
    containerType: {
      type: String
    }
  },
  watch: {
    shelfSampleType (newVal) {
      let shelf = []
      newVal.forEach(item => {
        if (item.shelfNums.length > 0) {
          item.shelfNums.forEach(v => {
            let vv = {
              num: v,
              sampleTypes: item.sampleType
            }
            shelf.push(vv)
          })
        }
      })
      function compare (v1, v2) { // 按架数大小重排序
        if (v1.num < v2.num) {
          return -1
        } else if (v1.num > v2.num) {
          return 1
        } else {
          return 0
        }
      }
      this.shelf = shelf.sort(compare)
    }
  },
  computed: {
    showAddIcon () {
      let allComplete = this.shelfSampleType.every(item => {
        return item.shelfNums.length > 0
      })
      if (!allComplete) return false // 存在没有选择添加架数项，则无法再次添加项
      let allshelfs = []
      this.shelfSampleType.forEach(item => {
        allshelfs.push(...item.shelfNums)
      })
      let sets = new Set(allshelfs)
      return [...sets].length < this.shelfTotal
    },
    hasSelectShelf () { // 已选架数，防止重复选择
      let allShelfs = []
      this.shelfSampleType.forEach(item => {
        allShelfs.push(...item.shelfNums)
      })
      let sets = new Set(allShelfs)
      return [...sets]
    }
  },
  data () {
    return {
      maxShelfNum: 99,
      shelfTotal: 0,
      formWidth: '220px',
      shelf: [], // {num: '', sampleTypes: []}，就两个提交的时候再对比
      shelfSampleTypeInput: [
        {shelfNums: '', sampleType: []}
      ],
      // 输入的正确值，只有shelfSampleTypeInput输入的值正确通过后，才会赋值给这个
      // 避免用户直接输入的错误值影响显示
      shelfSampleTypeInputCorrect: [
        {floorNums: '', sampleType: []}
      ],
      shelfSampleType: [
        {shelfNums: [], sampleType: []}
      ],
      shelfNecessarySampleType: new Map()
    }
  },
  methods: {
    // 打开弹窗初始化
    handleOpen () {
      this.$nextTick(() => {
        let formInput = this.currentSetObj.formInput || {}
        let content = this.currentSetObj.children || []
        this.shelfTotal = content.length || 0
        this.shelfSampleTypeInput = [
          {shelfNums: '', sampleType: []}
        ]
        this.shelfSampleTypeInputCorrect = [
          {shelfNums: '', sampleType: []}
        ]
        this.shelfSampleType = [
          {shelfNums: [], sampleType: []}
        ]
        if (formInput.shelfSampleType) {
          this.shelfSampleType = JSON.parse(JSON.stringify(formInput.shelfSampleType))
          this.shelfSampleTypeInput = []
          this.shelfSampleTypeInputCorrect = []
          this.shelfSampleType.forEach(item => {
            let v = {
              shelfNums: item.shelfNums.toString(),
              sampleType: item.sampleType
            }
            this.shelfSampleTypeInput.push(v)
            this.shelfSampleTypeInputCorrect.push(util.deepCopy(v))
          })
        }
        this.shelf = []
        content.forEach(item => {
          let v = {
            num: +item.num,
            sampleTypes: item.sampleTypes
          }
          this.shelf.push(v)
        })
        this.setShelfNecessarySampleType()
      })
    },
    // 架数变化
    handleChangeShelfNum (val) {
      console.log(val)
      // 在架数减少的时候进行
      if (val < this.shelf.length) {
        let canModify = true // 可以修改，为true时将处理过的shelfSampleType赋值过去
        let shelfSampleType = JSON.parse(JSON.stringify(this.shelfSampleType))
        for (let i = 0; i < shelfSampleType.length; i++) {
          let v = shelfSampleType[i]
          let bigNum = v.shelfNums.filter(num => {
            return num > val
          })
          let content = JSON.parse(JSON.stringify(this.containerData))
          let currentFloors = content.filter(item => {
            return this.currentFloor.indexOf(item.num) > -1
          })
          console.log('bigNum', bigNum)
          if (bigNum.length > 0) {
            // 展开所有的选中层的架子
            let shelfs = []
            currentFloors.forEach(item => {
              item.children.forEach(v => {
                let vv = {
                  ...v,
                  floorNum: item.num
                }
                shelfs.push(vv)
              })
            })
            // 拿出所有不能删除的架子
            let cantDelShelf = shelfs.filter(item => {
              return bigNum.indexOf(item.num) > -1 && item.useHole && item.useHole > 0
            })
            if (cantDelShelf.length > 0) {
              let msg = ''
              cantDelShelf.forEach((item, i, a) => {
                let n = item.floorNum + this.changeText('架') + item.num + this.changeText('层')
                let m = i === a.length - 1 ? n : n + ','
                msg += m
              })
              this.shelfTotal = this.shelf.length
              this.$message.error(`${msg}已有孔位被使用，无法删除！`)
              return
            }
            for (let ii = 0; ii < bigNum.length; ii++) {
              let vv = bigNum[ii]
              /**
               * 这里加判断条件，判断是否可以修改
               * **/
              let index = v.shelfNums.indexOf(vv)
              if (index > -1) {
                v.shelfNums.splice(index, 1)
              }
            }
          }
        }
        console.log(shelfSampleType)
        if (canModify) {
          // 去除没有数量的空对象
          shelfSampleType = shelfSampleType.filter(item => {
            return item.shelfNums.length > 0
          })
          console.log(shelfSampleType)
          this.shelfSampleTypeInput = []
          this.shelfSampleTypeInputCorrect = []
          shelfSampleType.forEach(item => {
            let v = {
              shelfNums: item.shelfNums.toString(),
              sampleType: item.sampleType
            }
            this.shelfSampleTypeInput.push(v)
            this.shelfSampleTypeInputCorrect.push(util.deepCopy(v))
          })
          this.shelfSampleType = shelfSampleType
        }
      }
    },
    // 设置层必须包含的样本类型
    setShelfNecessarySampleType () {
      this.shelfNecessarySampleType.clear()
      this.containerData.forEach(item => {
        if (this.currentFloor.indexOf(item.num) > -1) {
          // 架子
          item.children.forEach(v => {
            let boxSampleSet = new Set()
            if (v.children && v.children.length > 0) {
              v.children.forEach(vv => {
                if (vv.sampleTypes && vv.sampleTypes.length > 0) {
                  vv.sampleTypes.forEach(vvv => {
                    boxSampleSet.add(vvv)
                  })
                }
              })
            }
            this.shelfNecessarySampleType.set(v.num, [...boxSampleSet])
          })
        }
      })
      console.log([...this.shelfNecessarySampleType])
    },
    handleAddShelfSampleType () {
      this.shelfSampleType.push({shelfNums: [], sampleType: []})
      this.shelfSampleTypeInput.push({shelfNums: '', sampleType: []})
      this.shelfSampleTypeInputCorrect.push({shelfNums: '', sampleType: []})
    },
    // 改变架数信息，具体注释请查看createContainerSetFloor.vue 组件中的handleChangeFloorInfo方法
    handleChangeShelfInfo (index, filedName) {
      let resetFormInput = (i = index) => {
        this.$set(this.shelfSampleTypeInput, i, util.deepCopy(this.shelfSampleTypeInputCorrect[i]))
      }
      let data = util.deepCopy(this.shelfSampleType[index])
      if (filedName === 'sampleType') {
        this.shelfSampleTypeInputCorrect = util.deepCopy(this.shelfSampleTypeInput)
        data.sampleType = this.shelfSampleTypeInputCorrect[index].sampleType
        this.$set(this.shelfSampleType, index, data)
      } else if (filedName === 'shelfNums') {
        if (this.shelfSampleTypeInput[index].shelfNums) { // 判断用户输入值是否为空
          let shelfNumsSet = new Set()
          let shelfNumsInput = JSON.parse(JSON.stringify(this.shelfSampleTypeInput[index])).shelfNums.trim()
          let regx = /[1-9]([\s+,，\\-]*[0-9])*$/
          if (regx.test(shelfNumsInput)) {
            let dataShelfNums = util.deepCopy(data.shelfNums)
            data.shelfNums = []
            this.$set(this.shelfSampleType, index, data)
            this.$nextTick(() => {
              // 将所有空格、中文逗号都变为英文逗号
              let input = shelfNumsInput.replace(/\s+/g, ',').replace(/，/, ',').replace(/(\s+-\s+)|(\s+-)|(-\s+)/, '-')
              // 转换字符串为数组,去除空项
              let f = input.split(',').filter(item => { return item })
              let numCorrect = true
              for (let i = 0; i < f.length; i++) {
                if (f[i].indexOf('-') > -1) {
                  let fArr = f[i].split('-').filter(item => { return item })
                  if (fArr.length !== 2) {
                    this.$message.error('输入格式不正确')
                    numCorrect = false
                    break
                  } else {
                    let correctNum = fArr.every(v => {
                      let num = +v
                      return !Number.isNaN(num) && num > 0 && num <= this.shelfTotal
                    })
                    if (correctNum) {
                      let arr = fArr.map(v => { return +v })
                      let max = Math.max(...arr)
                      let min = Math.min(...arr)
                      if (max <= this.shelfTotal && min > 0) {
                        let foolA = []
                        do {
                          foolA.push(min)
                          min++
                        } while (min <= max)
                        let hasSelect = foolA.filter(item => {
                          return this.hasSelectShelf.indexOf(item) > -1
                        })
                        if (hasSelect.length > 0) {
                          this.$message.error(`${hasSelect.toString()}架已被设置`)
                          numCorrect = false
                          break
                        } else {
                          foolA.forEach(item => {
                            shelfNumsSet.add(item)
                          })
                        }
                      } else {
                        this.$message.error(`请确保输入的值在1-${this.shelfTotal}之间`)
                        numCorrect = false
                        break
                      }
                    } else {
                      this.$message.error('输入格式不正确')
                      numCorrect = false
                      break
                    }
                  }
                } else {
                  let num = +f[i]
                  if (!Number.isNaN(num) && num > 0 && num <= this.shelfTotal) {
                    if (this.hasSelectShelf.indexOf(num) > -1) {
                      this.$message.error(`${f[i]}架已被设置`)
                      return
                    }
                    shelfNumsSet.add(num)
                  } else {
                    this.$message.error('输入格式不正确')
                  }
                }
              }
              if (numCorrect) {
                this.shelfSampleTypeInputCorrect = util.deepCopy(this.shelfSampleTypeInput)
                data.shelfNums = [...shelfNumsSet]
                this.$set(this.shelfSampleType, index, data)
              } else {
                data.shelfNums = dataShelfNums
                this.$set(this.shelfSampleType, index, data)
                resetFormInput(index)
              }
            })
          } else {
            resetFormInput(index)
            this.$message.error('输入格式不正确')
          }
        } else {
          // 如果用户输入的值为空，则返回他上次输入的正确值
          resetFormInput(index)
          // this.shelfSampleTypeInput[index].shelfNums = this.shelfSampleType[index].shelfNums.toString()
        }
      }
    },
    // 判断两个数组对象是否相同
    judgeArray (arr1, arr2) {
      if (arr1.length !== arr2.length) return false
      return arr1.every(item => {
        return arr2.includes(item)
      })
    },
    handleConfirm () {
      let allSelected = this.shelf.every(item => {
        return item.sampleTypes && item.sampleTypes.length > 0
      })
      if (this.shelfTotal > 0 && this.shelf.length === this.shelfTotal && allSelected) {
        // let hasSetshelfData = this.currentSetObj.content || []
        // let shelf = []
        // let hasSetshelfMap = new Map()
        // hasSetshelfData.forEach(item => {
        //   hasSetshelfMap.set(+item.num, item)
        // })
        let allSampleTypeIsTrue = true
        let errorNum = 0
        for (let i = 0; i < this.shelf.length; i++) {
          let item = this.shelf[i]
          let thisShelfNecessarySampleType = this.shelfNecessarySampleType.get(item.num)
          if (thisShelfNecessarySampleType && thisShelfNecessarySampleType.length > 0) {
            let c = thisShelfNecessarySampleType.every(v => {
              return item.sampleTypes.includes(v)
            })
            if (!c) {
              allSampleTypeIsTrue = false
              errorNum = item.num
              break
            }
          }
        }
        if (!allSampleTypeIsTrue) {
          this.$message.error(`${errorNum}${this.changeText('架')}的样本类型不符合`)
          return
        }
        let content = JSON.parse(JSON.stringify(this.containerData))
        let currentFloorData = content.filter(item => {
          return this.currentFloor.indexOf(item.num) > -1
        })
        let formInput = {
          name: this.currentOperateName,
          total: this.shelfTotal,
          shelfSampleType: this.shelfSampleType
        }
        currentFloorData.forEach(item => {
          let children = []
          this.shelf.forEach(v => {
            let vv = {
              tag: '架',
              filed: 'shelf',
              num: +v.num,
              sampleTypes: v.sampleTypes
            }
            let thisShelf = item.children.filter(shelfItem => {
              return shelfItem.num === +v.num
            })[0]
            console.log(thisShelf)
            if (thisShelf) {
              vv.children = thisShelf.children ? thisShelf.children : []
              if (thisShelf.id) {
                vv.id = thisShelf.id
              }
              if (thisShelf.useHole !== undefined) {
                vv.useHole = thisShelf.useHole
              }
              if (thisShelf.formInput) {
                vv.formInput = thisShelf.formInput
              }
            } else {
              vv.children = []
            }
            children.push(vv)
          })
          item.children = children
          item.formInput = formInput
        })
        this.$emit('dialogConfirmEvent', content)
      } else {
        this.$message.error(`${this.changeText('架')}未设置完整`)
      }
    },
    // 通过判断lab改变文字显示
    changeText (text) {
      let result = text
      if (this.containerType === 'C') {
        switch (text) {
          case '层':
            result = '组'
            break
          case '架':
            result = '抽屉'
            break
          case '盒':
            result = '道'
            break
        }
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  .form{
    display: flex;
    flex-wrap: wrap;
    .form-item{
      margin-bottom: 20px;
      margin-right: 20px;
    }
  }
  .shelf{
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background: #f2f2f2;
    padding: 10px 0;
    .shelf-item{
      background: $color;
      width: 40%;
      border-radius: 4px;
      color: #fff;
      text-align: center;
      margin: 0 auto 20px auto;
      p{
        line-height: 2;
      }
    }
  }
</style>
