<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="确认完成"
      width="80%"
      @open="handleOpen">
      <div v-loading="loading">
        <el-upload
          :on-success="handleOnSuccess"
          :on-error="handleOnError"
          :data="{orderNumber: orderId}"
          :auto-upload="true"
          :headers="headers"
          :limit="1"
          :file-list="fileList"
          :before-upload="handleBeforeUpload"
          :action="uploadUrl">
          <el-button type="primary" size="mini">批量导入</el-button>
        </el-upload>
        <el-table
          ref="sampleTable"
          :data="tableData"
          class="sample-table"
          style="width: 100%;margin-top: 10px;"
          height="400"
          @select="handleSelectTable"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column :selectable="tableCanSelect" type="selection" width="55"></el-table-column>
          <el-table-column label="样本编号" width="150" prop="fsampleNumber"></el-table-column>
          <el-table-column label="样本类型" width="120" prop="fsampleType"></el-table-column>
          <el-table-column label="样本状态" width="120" prop="fbackStatus"></el-table-column>
          <el-table-column show-overflow-tooltip label="是否异常" width="100">
            <template slot-scope="scope">
              <span :style="{color: scope.row.fisException ? '#F56C6C' : '#67C23A'}">{{scope.row.fisException ? '是' : '否'}}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="异常备注" min-width="180" prop="fexceptionNotes">
            <template slot-scope="scope">
              <el-input
                v-model.trim="scope.row.fexceptionNotes"
                v-if="scope.row.fbackStatus === '处理中' && scope.row.fisException === 1"
                size="mini"
                ></el-input>
              <span v-else>{{scope.row.fexceptionNotes}}</span>
            </template>
          </el-table-column>
          <el-table-column label="快递单号" width="180" prop="frecipientCourierNum">
            <template slot-scope="scope">
              <el-input
                v-model.trim="currentEditCourierNum"
                v-if="scope.$index === currentEditIndex"
                size="mini"
                maxlength="30"></el-input>
              <span v-else>{{scope.row.frecipientCourierNum}}</span>
            </template>
          </el-table-column>
          <el-table-column label="管型" width="100" prop="ftubeType"></el-table-column>
          <el-table-column label="样本量" width="100" prop="fsampleAmount"></el-table-column>
          <el-table-column label="所属实验室" width="120" prop="flab"></el-table-column>
          <el-table-column label="存储温度" width="100" prop="ftemperature"></el-table-column>
          <el-table-column label="存储位置" min-width="180" prop="fsamplePlace"></el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template slot-scope="scope">
              <template v-if="scope.row.fbackStatus === '处理中'">
                <el-button
                  type="text"
                  @click.stop="handleMark(scope.row)">{{scope.row.fisException ? '取消' : ''}}标记异常</el-button>
                <el-button
                  v-if="scope.$index !== currentEditIndex"
                  size="mini"
                  type="text"
                  @click.stop="handleToModifyPlace(scope.$index,scope.row.frecipientCourierNum)">修改单号</el-button>
                <el-button
                  v-else
                  size="mini"
                  type="text"
                  @click.stop="handleConfirmModifyCourierNum(scope.row)">确认单号</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <div style="line-height: 2.5;">
          共{{tableData.length}}条,已选{{hasChooseNum}}条
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import constants from '../../../util/constants'
import mixins from '../../../util/mixins'
export default {
  name: 'confirmCompleteBackSampleApplicationDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: String | Number
    }
  },
  data () {
    let loginId = util.getSessionInfo('loginId')
    let id = loginId ? util.decryptBase64(loginId) : ''
    return {
      uploadUrl: constants.JS_CONTEXT + '/sample/order/import_sample_back_excel',
      headers: {
        'user-id': id
      },
      fileList: [],
      tableData: [],
      currentPosition: '',
      paraffinCabinetLocationLists: [],
      paraffinCabinetLocationCheck: [],
      selectedRows: new Map(),
      hasChooseNum: 0, // 已选行数
      loading: false,
      currentEditIndex: '', // 当前编辑表格的行数
      currentEditCourierNum: ''// 当前编辑的位置
    }
  },
  methods: {
    handleOpen () {
      this.paraffinCabinetLocationLists = []
      this.paraffinCabinetLocationCheck = []
      this.hasChooseNum = 0
      this.currentEditIndex = ''
      this.currentEditCourierNum = ''
      this.tableData = []
      if (this.orderId) {
        this.loading = true
        this.$ajax({
          url: '/sample/order/get_sample_list_by_order_number',
          method: 'get',
          data: {
            orderNumber: this.orderId
          },
          loadingDom: '.sample-table'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.tableData = res.data || []
            this.$nextTick(() => {
              // this.$refs.sampleTable.toggleAllSelection()
              this.selectedRows.clear()
              this.tableData.forEach(item => {
                console.log(item.fid)
                this.$refs.sampleTable.toggleRowSelection(item, item.fbackStatus === '处理中')
                if (item.fbackStatus === '处理中') {
                  this.selectedRows.set(item.fid, item)
                  this.hasChooseNum++
                }
              })
              // this.hasChooseNum = this.tableData.length
            })
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.loading = false
        })
      }
    },
    // 可否选中
    tableCanSelect (row) {
      return row.fbackStatus === '处理中'
    },
    // 提交成功回调
    handleOnSuccess (res) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let tableDataMap = new Map()
        this.tableData.forEach(v => {
          tableDataMap.set(v.fid, v)
        })
        let data = res.data || []
        data.forEach(v => {
          if (tableDataMap.has(v.fid)) {
            tableDataMap.get(v.fid).frecipientCourierNum = v.frecipientCourierNum
          }
        })
      } else {
        this.$showErrorDialog({
          tableData: res.data || []
        })
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 8) {
          this.loading = false
          this.$message.error('文件大小超过限制，无法上传')
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
      this.hasChooseNum = this.selectedRows.size
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fid, row)
      })
      this.hasChooseNum = this.selectedRows.size
    },
    handleRowClick (row, c) {
      if (row.fbackStatus === '处理中') {
        this.$refs.sampleTable.toggleRowSelection(row, !this.selectedRows.has(row.fid))
        this.handleSelectTable(undefined, row)
      }
    },
    // 修改
    handleToModifyPlace (index, data) {
      console.log(index, data)
      this.currentEditIndex = index
      this.currentEditCourierNum = data
    },
    // 确认修改
    handleConfirmModifyCourierNum (row) {
      if (!this.currentEditCourierNum) {
        this.$message.error('快递单号不能为空')
        return
      }
      if (this.currentEditCourierNum === row.frecipientCourierNum) {
        this.currentEditCourierNum = ''
        this.currentEditIndex = ''
        return
      }
      row.frecipientCourierNum = this.currentEditCourierNum
      this.currentEditIndex = ''
      this.currentEditCourierNum = ''
    },
    // 输入异常弹窗关闭
    handleMark (row) {
      if (row.fisException) { // 等于1的是显示取消标记异常，此时点击清空异常备注
        row.fexceptionNotes = ''
      }
      row.fisException = row.fisException === 1 ? 0 : 1
    },
    // 确认
    handleDialogConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要提交的行')
        return
      }
      let data = [...this.selectedRows.values()]
      let hasAllSet = data.every(item => {
        // 只有是处理中且为异常时才检查是否有异常备注
        return item.backStatus === '处理中' && item.fisException ? !!item.fexceptionNotes : true
      })
      if (!hasAllSet) {
        this.$message.error('选择的行存在是异常但是没有异常备注的情况')
        return
      }
      let hasAllCourierNum = data.every(v => {
        // 如果是非异常的话，快点单号必填
        return v.fisException ? true : !!v.frecipientCourierNum
      })
      if (!hasAllCourierNum) {
        this.$message.error('选择的行存在是非异常但是没有快递单号的情况')
        return
      }
      this.loading = true
      this.$ajax({
        url: '/sample/order/confirm_complete_order',
        data: {
          orderNumber: this.orderId,
          sampleInfoList: data
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('提交成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
