<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="提示" :width="width"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)">
        <!--      提示-->
        <template v-if="type === 1">
          <div class="tips-container">
            <i class="el-icon-warning" style="color: #E6A23C; font-size: 18px"></i>
            <span>{{message}}</span>
          </div>

          <span  slot="footer" class="dialog-footer">
          <el-button size="mini" @click="handleClose">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleChangeType">可选字段导出</el-button>
          <el-button size="mini" type="primary" @click="handleConfirm">确 定</el-button>
        </span>
        </template>
        <!--      选择字段-->
        <template v-if="type === 2">
          <div class="dialog-wrapper">

          <el-form ref="form" :model="form" label-width="100px" label-suffix=":">
            <el-form-item label="是否全选">
              <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate"  border @change="handleCheckAllChange">全选</el-checkbox>
            </el-form-item>

            <el-form-item label="可选字段">
              <el-checkbox-group v-model="form.checkedFields" @change="handleCheckedCitiesChange">
                <el-checkbox v-for="item in tableData" :key="item.filedName" :label="item" style="width: 240px;" border>
                  <span v-html="item.filedHeaderName"></span>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
          </div>
          <span  slot="footer" class="dialog-footer">
          <el-button size="mini" @click="handleClose">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleConfirm">确 定</el-button>
        </span>
        </template>
    </el-dialog>
  </div>
</template>

<script>

import {awaitWrap} from '../../util/util'
import {getDownloadFields} from '../../api/sequencingManagement/singleCell'
import mixins from '../../util/mixins'

export default {
  name: `downLoadChooseDialog`,
  mixins: [mixins.tablePaginationCommonData],
  data () {
    return {
      visible: false,
      checkAll: false,
      isIndeterminate: false,
      type: 1, // 1: 导出提示 2: 选择字段
      message: '',
      width: '500px',
      form: {
        checkedFields: []
      },
      tableData: []
    }
  },
  methods: {
    handleClose () {
      this.visible = false
      this.$emit('close')
    },
    // 切换选择字段
    handleChangeType () {
      this.type = 2
      this.width = '1000px'
      this.getFields()
    },
    async getFields () {
      const parsms = this.tableData.filter(v => v.isShow).map(v => {
        console.log(v)
        // 判断prop 是否包含 text
        // 优化：使用正则表达式替换，增强代码的可读性和可维护性
        if (/text/.test(v.field)) {
          return v.field.replace(/text/g, '')
        }
        return v.field
      })
      const {res} = await awaitWrap(getDownloadFields({
        flibExportFileds: parsms
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.tableData = res.data.map(v => {
          return v
        })
      }
    },
    handleCheckAllChange (val) {
      this.form.checkedFields = val ? this.tableData : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange (value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.tableData.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.tableData.length
    },
    handleConfirm () {
      if (this.type === 1) {
        this.$emit('downLoad', [])
      } else {
        console.log(this.form.checkedFields, '11111111111111111111')
        if (this.form.checkedFields.length === 0) {
          this.$message.warning('请选择导出字段')
          return
        }
        this.$emit('downLoad', this.form.checkedFields)
      }
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
.tips-container {
  min-height: 80px;
  padding: 20px 20px;
}
.dialog-wrapper {
  max-height: 60vh;
  overflow: auto;
}
/deep/ .el-checkbox.is-bordered {
  margin-left: 10px !important;
}
</style>
