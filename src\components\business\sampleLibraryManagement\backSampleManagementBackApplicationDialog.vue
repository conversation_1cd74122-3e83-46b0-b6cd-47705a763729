<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="返样申请"
      width="70%"
      @open="handleOpen">
      <div class="form-container">
        <div class="form-item">
          <label>所属实验室：</label>
          <el-select size="mini" v-model.trim="lab">
            <el-option v-for="(v, k) in regionObj" :key="k" :label="v" :value="v"></el-option>
          </el-select>
        </div>
      </div>
      <el-table
        ref="backApplicationTable"
        :data="tableData"
        style="width: 100%;"
        height="300px"
        class="application-table"
        @select="handleSelectTable"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column width="120" label="样本编号" prop="sampleCode"></el-table-column>
        <el-table-column prop="sampleStatus" label="样本状态" width="120"></el-table-column>
        <el-table-column prop="lab" label="所属实验室" width="120"></el-table-column>
        <el-table-column prop="notes" label="返样备注" min-width="220" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-input v-model.trim="scope.row.notes" maxlength="50" show-word-limit></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="position" label="存放位置" min-width="220" show-overflow-tooltip></el-table-column>
        <!--<el-table-column prop="testingLink" label="检测环节" width="120"></el-table-column>-->
        <el-table-column prop="recipient" label="收件人" width="100"></el-table-column>
        <el-table-column prop="address" label="收件地址" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="phone" label="联系电话" width="150"></el-table-column>
        <el-table-column prop="backTime" label="返样时间" min-width="180"></el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click.stop="handleRemove(scope.row.id, scope.$index)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
          :loading="submitBtnLoading"
          size="mini"
          type="primary"
          @click="handleConfirm">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import constants from '../../../util/constants'
export default {
  name: `backSampleManagementBackApplicationDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    backSampleApplicationDialogTableData: Array
  },
  data () {
    return {
      tableData: [],
      lab: '',
      selectedRows: new Map(),
      submitBtnLoading: false,
      regionObj: constants.REGION_OBJ
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.lab = ''
        this.tableData = util.deepCopy(this.backSampleApplicationDialogTableData)
        this.selectedRows.clear()
        setTimeout(() => {
          this.tableData.forEach(item => {
            this.selectedRows.set(item.id, item)
            this.$refs.backApplicationTable.toggleRowSelection(item, true)
          })
        }, 500)
      })
    },
    // 移除
    handleRemove (id, index) {
      this.tableData.splice(index, 1)
      this.selectedRows.delete(id)
      // 再次选中已经选中的
      this.tableData.forEach(item => {
        this.$refs.backApplicationTable.toggleRowSelection(item, this.selectedRows.has(item.id))
      })
    },
    // 提交
    handleConfirm () {
      if (!this.lab) {
        this.$message.error('请选择实验室')
        return
      }
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择样本')
        return
      }
      let data = [...this.selectedRows.values()]
      let sampleList = data.map(v => {
        return {
          fid: v.id,
          fbackNotes: v.notes
        }
      })
      this.submitLoading = true
      this.$ajax({
        url: '/sample/order/submit_sample_back_application',
        data: {
          flab: this.lab,
          sampleList: sampleList
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('成功')
          this.$emit('dialogConfirmEvent', res.data)
          this.handleClose()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.backTable.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .form-container{
    .form-item{
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      label{
        margin-right: 30px;
        display: block;
        width: 7em;
      }
    }
  }
</style>
