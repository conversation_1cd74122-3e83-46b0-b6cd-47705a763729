<template>
  <el-dialog
    title="上机信息确认"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="95vw"
    append-to-body
    @opened="handleOpen"
  >
    <el-image src="https://axhub.im/ax9/8fa38a46fed247ad/images/%E5%BB%BA%E5%BA%93/u1068.png" class="logo" alt="logo"/>
    <div class="table-title">吉因加上机文库信息单-单细胞</div>
    <el-table
      ref="table"
      :data="basicInfo"
      class="table"
      size="mini"
      border
      style="width: 100%"
      max-height="600"
      :span-method="handleSpan">
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column label="样本名称" prop="sampleName" min-width="120" show-overflow-tooltip>
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.sampleName" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="子文库名称" prop="subLibraryName" min-width="120" show-overflow-tooltip>
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.subLibraryName" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="index编号" prop="indexCode" min-width="120" show-overflow-tooltip>
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.indexCode" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
      </el-table-column>
<!--      <el-table-column label="核酸/indexF-序列5'-3’方向" prop="indexF" min-width="180" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.indexF" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="indexR-序列5'-3’方向" prop="indexR" min-width="160" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.indexR" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="数据量（G reads）" prop="dataSize" min-width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model.trim="scope.row.realData.dataSize" size="mini" clearable class="form-width"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="浓度(ng/μl)" prop="concentration" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model.trim="scope.row.realData.concentration" size="mini" clearable class="form-width"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="体积(μl)" prop="volume" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model.trim="scope.row.realData.volume" size="mini" clearable class="form-width"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="策略" prop="strategy" min-width="120" show-overflow-tooltip>
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.volume" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
      </el-table-column>
<!--      <el-table-column label="是否环化" prop="isCyclic" min-width="120" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.isCyclic" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="index类型" prop="indexType" min-width="120" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.indexType" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="建库试剂类型" prop="makeDNB" min-width="120" show-overflow-tooltip>
<!--        <template slot-scope="scope">-->
<!--          <el-input v-model.trim="scope.row.realData.makeDNB" size="mini" clearable class="form-width"></el-input>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model.trim="scope.row.realData.remark" size="mini" clearable class="form-width"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="定位" prop="location" min-width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input v-model.trim="scope.row.realData.location" size="mini" clearable class="form-width"></el-input>
        </template>
      </el-table-column>
    </el-table>
    <send-email-dialog
      :pvisible.sync="sendEmailVisible"
      :computed-info="computedInfo"
      :ids="ids"
      @dialogConfirmEvent="handleConfirm"
    />
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button size="mini" :loading="loading" type="primary" @click="handleSendEmail">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {awaitWrap, deepCopy, setDefaultEmptyValueForObject} from '../../../../../util/util'
import {getComputerPracticeInfo} from '../../../../../api/sequencingManagement/singleCell'
import sendEmailDialog from './sendEmailDialog.vue'

export default {
  name: 'ConfirmComputedDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    sendEmailDialog
  },
  props: {
    ids: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      basicInfo: [
      ],
      computedInfo: [],
      sendEmailVisible: false,
      loading: false
    }
  },
  methods: {
    async handleOpen () {
      const { res } = await awaitWrap(getComputerPracticeInfo({
        fnewLibIdList: this.ids
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        // 处理后端返回的数据
        this.basicInfo = res.data.map(item => {
          const v = {
            fcount: item.fcount,
            sampleName: item.fcosSampleNameByEmbarkation,
            subLibraryName: item.flibNum,
            indexCode: item.fspliceIndex,
            // indexF: item.findexfSequence,
            // indexR: item.findexrSequence || '/',
            // indexType: item.findexType,
            dataSize: item.fdataSize,
            concentration: item.fcyclizationConcentration,
            volume: item.fcyclizationInputVolume,
            strategy: item.fcomputerStrategy,
            // isCyclic: item.fisCyclization,
            makeDNB: item.flibReagentType, // 后端数据中没有对应字段
            remark: item.fcyclizationRemark,
            location: item.flocation,
            fprojectCode: item.fprojectCode,
            fprojectName: item.fprojectName,
            libType: item.flibType // 后端数据中没有对应字段
          }
          v.realData = deepCopy(v)
          // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
          setDefaultEmptyValueForObject(v)
          return v
        })
      }
    },
    handleSpan ({row, column, rowIndex, columnIndex}) {
      // 检查是否为oligo文库
      const isOligo = this.basicInfo[rowIndex].libType === 'oligo'
      // 判断下一行是否是oligo文库
      // const nextIsOligo = this.basicInfo[rowIndex + 1] && this.basicInfo[rowIndex + 1].libType === 'oligo'

      if (isOligo) {
        const mergeGroups = []
        let currentGroup = []

        // 遍历basicInfo,根据浓度和体积分组
        for (let i = 0; i < this.basicInfo.length; i++) {
          const currentItem = this.basicInfo[i]
          const isSameLibType = currentItem.libType === 'oligo'

          if (isSameLibType) {
            if (currentGroup.length === 0) {
              currentGroup.push(i)
            } else {
              const prevItem = this.basicInfo[i - 1]
              const isSameConcentration = Math.floor(prevItem.concentration * 100) === Math.floor(currentItem.concentration * 100)
              const isSameVolume = Math.floor(prevItem.volume * 100) === Math.floor(currentItem.volume * 100)

              if (isSameConcentration && isSameVolume) {
                currentGroup.push(i)
              } else {
                mergeGroups.push(currentGroup)
                currentGroup = [i]
              }
            }
          } else {
            if (currentGroup.length > 0) {
              mergeGroups.push(currentGroup)
              currentGroup = []
            }
          }
        }

        if (currentGroup.length > 0) {
          mergeGroups.push(currentGroup)
        }

        // 处理样本名称列的合并
        if (column.property === 'sampleName') {
          for (const group of mergeGroups) {
            console.log(mergeGroups, group.includes(rowIndex), '判断是不是被合并大样本单元格')
            if (group.length > 1) {
              if (group[0] === rowIndex) {
                return [group.length, 1]
              } else if (group.includes(rowIndex)) {
                return [0, 0]
              }
            }
          }
        }

        // 处理浓度和体积列的合并
        if (column.property === 'concentration' || column.property === 'volume') {
          for (const group of mergeGroups) {
            if (group.length > 1) {
              if (group[0] === rowIndex) {
                return [group.length, 1]
              } else if (group.includes(rowIndex)) {
                return [0, 0]
              }
            }
          }
        }
      }

      // 对于其他情况，返回默认的单元格配置
      return [1, 1]
    },
    async handleSendEmail () {
      // 首先处理合并单元格的值
      const mergeGroups = []
      let currentGroup = []

      // 找出所有需要合并的oligo组
      for (let i = 0; i < this.basicInfo.length; i++) {
        const currentItem = this.basicInfo[i]
        if (currentItem.libType === 'oligo') {
          if (currentGroup.length === 0) {
            currentGroup.push(i)
          } else {
            const prevItem = this.basicInfo[i - 1]
            const isSameConcentration = Math.floor(prevItem.concentration * 100) === Math.floor(currentItem.concentration * 100)
            const isSameVolume = Math.floor(prevItem.volume * 100) === Math.floor(currentItem.volume * 100)

            if (isSameConcentration && isSameVolume) {
              currentGroup.push(i)
            } else {
              mergeGroups.push(currentGroup)
              currentGroup = [i]
            }
          }
        } else {
          if (currentGroup.length > 0) {
            mergeGroups.push(currentGroup)
            currentGroup = []
          }
        }
      }

      if (currentGroup.length > 0) {
        mergeGroups.push(currentGroup)
      }

      // 同步合并组中的值
      mergeGroups.forEach(group => {
        if (group.length > 1) {
          const firstItem = this.basicInfo[group[0]].realData
          group.forEach(index => {
            this.basicInfo[index].realData.concentration = firstItem.concentration
            this.basicInfo[index].realData.volume = firstItem.volume
          })
        }
      })
      this.computedInfo = this.basicInfo.map(row => {
        const item = row.realData
        const v = {
          fcount: item.fcount,
          fcosSampleNameByEmbarkation: item.sampleName,
          flibNum: item.subLibraryName,
          fspliceIndex: item.indexCode,
          fdataSize: item.dataSize,
          fcyclizationConcentration: item.concentration,
          fcyclizationInputVolume: item.volume,
          fcomputerStrategy: item.strategy,
          flibReagentType: item.makeDNB,
          fcyclizationRemark: item.remark,
          fprojectCode: item.fprojectCode,
          fprojectName: item.fprojectName,
          flocation: item.location,
          flibType: item.libType
        }
        return v
      })
      // 判断是否存在 index编号 + 备注相同的列
      const indexAndRemarkMap = {}
      let hasDuplicate = false

      for (const item of this.computedInfo) {
        const indexAndRemark = `${item.fspliceIndex}-${item.fcyclizationRemark}`
        if (indexAndRemarkMap[indexAndRemark]) {
          hasDuplicate = true
          break
        }
        indexAndRemarkMap[indexAndRemark] = true
      }
      if (hasDuplicate) {
        this.$message.error('存在index重复项，请核对后再提交')
        return
      }
      this.sendEmailVisible = true
    },
    handleConfirm () {
      this.visible = false
      this.$emit('dialogConfirmEvent')
    }
  }
}
</script>

<style lang="scss" scoped>
.logo {
  height: 24px;
  width: auto;
  margin: 10px 0;
}

.table-title {
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-weight: bold;
  border: 1px solid #EBEEF5;
  border-bottom: none;
}
</style>
