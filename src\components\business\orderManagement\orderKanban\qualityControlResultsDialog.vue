<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="样本列表"
      width="60%"
      @open="handleOpen">
      <!--<nav class="operateBar">-->
        <!--<div></div>-->
        <!--<div>-->
          <!--<el-select-->
            <!--v-model="form.content"-->
            <!--@clear="handleReset"-->
            <!--@change="clearInput"-->
            <!--size="mini"-->
            <!--placeholder="请选择"-->
            <!--style="width: 150px;"-->
            <!--clearable>-->
            <!--<template v-for="(v, k) in searchOptions">-->
              <!--<el-option :key="k" :label="v" :value="k"></el-option>-->
            <!--</template>-->
          <!--</el-select>-->
          <!--<template v-if="form.content === 'fgeneCode' || form.content === 'fname'">-->
            <!--<el-input-->
               <!--v-model="form.input"-->
               <!--style="width: 250px;margin: 0 20px;"-->
               <!--size="mini"-->
               <!--clearable-->
               <!--@clear="handleReset"-->
               <!--@keyup.enter.native="handleSearch"-->
               <!--:disabled="!form.content"-->
               <!--placeholder="请输入"></el-input>-->
          <!--</template>-->
          <!--<template v-else>-->
            <!--<el-input-->
              <!--v-model="form.input"-->
              <!--style="width: 250px;margin: 0 20px;"-->
              <!--size="mini"-->
              <!--clearable-->
              <!--@clear="handleReset"-->
              <!--@keyup.enter.native="handleSearch"-->
              <!--:disabled="!form.content"-->
              <!--placeholder="请输入"></el-input>-->
          <!--</template>-->
          <!--<el-button size="mini" type="primary" @click="handleSearch">查询</el-button>-->
        <!--</div>-->
      <!--</nav>-->
      <el-table
        :data="tableData"
        class="q-table"
        height="300px"
        style="width: 100%">
        <el-table-column prop="testProgress" label="检测进度" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="qualityControlResults" label="质控结果" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneplusNum" label="吉因加编号" width="180" show-overflow-tooltip></el-table-column>
        <template v-if="type === 1">
          <el-table-column prop="libraryName" label="文库名称" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fragmentSize" label="片段大小（bp）" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryDateNum" label="文库数据量（G）" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="baseBalance" label="碱基均衡" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingConcentration" label="送样浓度" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingVolume" label="送样体积" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryNotes" label="文库备注" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subLibraryName" label="子文库名称" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="indexDigits" label="Index位数" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="indexNum" label="Index编号" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="index1Index" label="Index1序列" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="index2Index" label="Index2序列" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subLibraryDateNum" label="子文库数据量（G）" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subLibraryNotes" label="子文库备注" width="180" show-overflow-tooltip></el-table-column>
        </template>
        <template v-else-if="type === 2">
          <el-table-column prop="sampleName" label="样本名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueSource" label="组织来源" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="concentration" label="浓度（ng/ul）" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="volume" label="体积（ul）" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dataNum" label="数据量（G）" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleTotal" label="样本量" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="notes" label="备注" min-width="180" show-overflow-tooltip></el-table-column>
        </template>
      </el-table>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        style="background: #ffffff;"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
// import constants from "../../../../util/constants";
export default {
  name: 'qualityControlResultsDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    type: Number, // 1上机文库 2核酸组织
    orderId: String | Number,
    btnType: Number // 1：质控结果 2：申请检测
  },
  data () {
    return {
      form: {
        content: '',
        input: ''
      },
      selectedRows: new Map(),
      submitBtnLoading: false,
      formSubmit: {},
      searchOptions: {
        'fgeneCode': '吉因加编号',
        'fname': '名称'
      },
      statusOptions: {
        0: '未下单',
        1: '已下单'
      },
      resultOptions: {
        1: '正常',
        2: '风险',
        3: '不合格'
      },
      pageSize: 40,
      pageSizes: [40]
    }
  },
  methods: {
    handleOpen () {
      this.tableData = []
      this.handleReset()
    },
    handleReset () {
      this.form = {
        content: '',
        input: ''
      }
      this.handleSearch()
    },
    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData()
    },
    getData () {
      let data = {
        orderId: this.orderId,
        type: this.type
      }
      data.pageVO = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$ajax({
        url: '/order/get_lib_or_tissue_list',
        data: data,
        loadingDom: '.q-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          this.selectedRows.clear()
          rows.forEach(v => {
            let item = {
              testProgress: v.testProgress,
              qualityControlResults: v.qualityControlResults,
              geneplusNum: v.geneCode
            }
            console.log(item)
            if (this.type === 1) {
              let item1 = {
                libraryName: v.name,
                fragmentSize: v.fragment,
                libraryDateNum: v.dataSize,
                baseBalance: v.baseBalance,
                samplingConcentration: v.concentration,
                samplingVolume: v.volume,
                libraryNotes: v.note
              }
              if (v.subLib && v.subLib.length > 0) {
                v.subLib.forEach(vv => {
                  let subItem = {
                    subLibraryName: vv.subName,
                    indexDigits: vv.indexNum,
                    indexNum: vv.indexCode,
                    index1Index: vv.index1,
                    index2Index: vv.index2,
                    subLibraryDateNum: vv.subDataSize,
                    subLibraryNotes: vv.subNote
                  }
                  item = {...item, ...item1, ...subItem}
                  item.realData = util.deepCopy(item)
                  util.setDefaultEmptyValueForObject(item)
                  this.tableData.push(item)
                })
              } else {
                item = {...item, ...item1}
                item.realData = util.deepCopy(item)
                util.setDefaultEmptyValueForObject(item)
                this.tableData.push(item)
              }
            }
            if (this.type === 2) {
              let item2 = {
                sampleName: v.name,
                tissueSource: v.tissueSource,
                concentration: v.concentration,
                volume: v.volume,
                dataNum: v.dataSize,
                sampleTotal: v.sampleSize,
                notes: v.note
              }
              item = {...item, ...item2}
              console.log(item)
              item.realData = util.deepCopy(item)
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            }
          })
        }
      })
    },
    // 下拉框发生改变时，清空input
    clearInput () {
      this.form.input = ''
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.qTable.toggleRowSelection(row, !this.selectedRows.has(row.sampleId))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.sampleId) ? this.selectedRows.delete(row.sampleId) : this.selectedRows.set(row.sampleId, row)
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.sampleId, row)
      })
    },
    // 确定
    handleConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下达的项')
        return
      }
      let datas = [...this.selectedRows.values()]
      let isCanSend = datas.every(v => v.status !== 1)
      if (!isCanSend) {
        this.$message.error('已下单的数据不可以下达任务')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.submitBtnLoading = true
      this.$ajax({
        url: '/cos/save_apply_inspection',
        data: {
          fid: this.orderId,
          sampleIdList: ids
        }
      }).then(res => {
        if (res.statusCode === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
