<template>
  <div class="page-wrapper">
    <!--    基础信息-->
    <div class="basic-info-wrapper">
      <!--      基础信息-->
      <div v-if="currentType === 'mNGS'" class="basic-info-container">
        <el-avatar icon="el-icon-user-solid"></el-avatar>
        <div class="margin">{{ sampleCode }}</div>
        <div>样本类型: {{ sampleType }}</div>
      </div>
      <div v-if="currentType === 'tNGS'" class="basic-info-container">
        <el-avatar icon="el-icon-user-solid"></el-avatar>
        <div class="margin">{{ sampleCode }}</div>
        <div class="margin">样本类型: {{ tngsSampleType }}</div>
        <div>诊断疾病: {{ tngsDiseaseType }}</div>
      </div>
      <!--      菜单栏-->
      <el-tabs v-model="currentComponent">
        <el-tab-pane label="检出病原列表" :name="currentType === 'mNGS' ? 'pathogensList' : 'tNGSPathogensList'" />
        <el-tab-pane label="报告审核" :name="currentType === 'mNGS' ? 'reportAudit' : 'tNGSReportAudit'" />
      </el-tabs>
    </div>
    <!---->
    <component v-if="currentType" :is="currentComponent" />
  </div>
</template>

<script>
import pathogensList from './pathogensList/index' // 检出病原报告
import reportAudit from './reportAudit' // 报告审核
import tNGSPathogensList from './tNGSPathogensList/index' // tNGS检出病原报告
import tNGSReportAudit from './tNGSReportAudit/index'// tNGS报告审核

export default {
  name: 'pathogenicReport',
  components: {
    pathogensList,
    reportAudit,
    tNGSPathogensList,
    tNGSReportAudit
  },
  mounted () {
    this.setAnalysisRsId()
    // this.getSampleType()
    this.getProductPathogenVersion()
  },
  data () {
    return {
      sampleCode: '',
      sampleType: '',
      tngsSampleType: '',
      tngsDiseaseType: '',
      currentComponent: 'pathogensList', // 激活菜单名
      currentType: 'mNGS' // 目前是tngs类型还是普通类型
    }
  },
  methods: {
    setAnalysisRsId () {
      this.sampleCode = this.$route.query.sampleCode
      this.$store.commit({
        type: 'old/setValue',
        category: 'analysisRsId',
        analysisRsId: this.$route.query.oxym
      })
      this.$store.commit({
        type: 'old/setValue',
        category: 'isPreview',
        isPreview: this.$route.query.isPreview
      })
    },
    async getSampleType () {
      let {code, data} = await this.$ajax({
        url: '/read/pathogen/get_sample_type',
        data: {
          analysisRsId: this.$route.query.oxym
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.sampleType = data
      }
    },
    async getProductPathogenVersion () {
      let {data, code} = await this.$ajax({
        url: '/read/get_product_pathogen_version',
        data: {
          analysisRsId: this.$route.query.oxym
        },
        method: 'get',
        loadingDom: '.page-wrapper'
      })
      if (code === this.SUCCESS_CODE) {
        this.currentType = data
        this.$store.commit({
          type: 'old/setValue',
          category: 'pathogenicType',
          pathogenicType: this.currentType === 'mNGS' ? '1' : '0'
        })
        if (this.currentType === 'tNGS') {
          this.currentComponent = 'tNGSPathogensList'
          await this.getUniversalModule()
        } else {
          await this.getSampleType()
        }
      }
    },
    async getUniversalModule () {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/get_universal_module',
        data: {
          analysisRsId: this.$route.query.oxym
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.tngsSampleType = data.sampleType
        this.tngsDiseaseType = data.clinicalDiagnoseMedical
      }
    }
  }
}
</script>

<style scoped lang="scss">
// 布局定位属性：display / position / float / clear / visibility / overflow（建议 display 第一个写，毕竟关系到模式）
// 自身属性：width / height / margin / padding / border / background
// 文本属性：color / font / text-decoration / text-align / vertical-align / white- space / break-word
// 其他属性（CSS3）：content / cursor / border-radius / box-shadow / text-shadow / background:linear-gradient …
.page-wrapper {
  height: calc(100vh - 40px);
  background: #f2f2f2;

  .basic-info-wrapper {
    height: 120px;
    padding: 10px;
    background: #fff;

    .basic-info-container {
      display: flex;
      align-items: center;
      height: 70px;
      line-height: 70px;

      .margin {
        margin: 0 120px 0 10px;
      }
    }
  }
}
</style>
