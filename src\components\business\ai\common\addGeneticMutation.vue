<template>
  <div class="page">
    <div class="header">
      <div class="title">
        新增遗传变异
      </div>
      <div>
        <el-button type="primary" size="mini" @click="handleSave('0')">保存草稿</el-button>
        <el-button type="primary" size="mini" @click="handleSave('1')">确认提交</el-button>
      </div>
    </div>
    <div class="content">
      <div>
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%;" @tab-click="handleClick">
          <el-tab-pane label="遗传性基因变异信息" name="1"></el-tab-pane>
          <el-tab-pane label="生物详情" name="2"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="height: 100%; overflow: auto; width: calc(100% - 160px); padding: 10px">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="mini" label-position="top" label-suffix="：">
          <el-row :gutter="15">
            <template v-if="activeName === '1'">
              <el-col :span="8">
                <el-form-item label="基因名称" prop="gene">
                  <el-input v-model="form.gene" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="变异意义">
                  <el-select v-model="form.fmutationType" placeholder="请选择" style="width: 100%">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in mutationTypeList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="核苷酸改变" prop="nucleotideMutation">
                  <el-input v-model="form.nucleotideMutation" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="氨基酸改变" prop="aminoAcidMutation">
                  <el-input v-model="form.aminoAcidMutation" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="参考序列" prop="referenceSeq">
                  <el-input v-model="form.referenceSeq" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="变异功能">
                  <el-select v-model="form.mutationFunction" placeholder="请选择" style="width: 100%">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in mutationFunctionList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="证据等级">
                  <el-input v-model="form.evidenceLevel" disabled style="width: calc(100% - 60px)"></el-input>
                  <el-button type="primary" @click="handleEditEvidence">编辑</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="变异概述" prop="geneMutationAnalysis">
                  <el-input v-model="form.geneMutationAnalysis" :autosize="{minRows: 5}" type="textarea" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="基因变异解析(中文)" prop="geneMutationAnalysisCn">
                  <el-input v-model="form.geneMutationAnalysisCn" :autosize="{minRows: 5}" type="textarea" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="基因变异解析(英文)">
                  <el-input v-model="form.geneMutationAnalysisEn" :autosize="{minRows: 5}" type="textarea" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input v-model="form.geneMutationAnalysisRemarks" :autosize="{minRows: 5}" type="textarea" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label-width="0">
                  <div>
                    <el-button type="primary" size="mini" @click="handleGetReferences">自动获取文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(0)">新增文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(1)">修改文献</el-button>
                    <el-button type="primary" size="mini" @click="handleSelectReferences">选择文献</el-button>
                    <el-button type="primary" size="mini" @click="handleDeleteReferences">移除</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(1)">上移</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(0)">下移</el-button>
                  </div>
                  <el-table
                    :data="tableData"
                    height="400"
                    highlight-current-row
                    class="referencesTable"
                    style="width: 100%"
                    @current-change="handleCurrentChange">
                    <el-table-column type="index" label="#"></el-table-column>
                    <!--<el-table-column type="selection" width="45"></el-table-column>-->
                    <el-table-column prop="date" label="文献名称"></el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </template>
            <template v-else>
              <el-col :span="8">
                <el-form-item label="Chr">
                  <el-input v-model="form.variationChr" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Start">
                  <el-input v-model="form.variationStart" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Stop">
                  <el-input v-model="form.variationStop" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Ref">
                  <el-input v-model="form.variationRef" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="Call">
                  <el-input v-model="form.variationCall" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
    </div>
    <save-references-dialog
      :pvisible="saveReferencesDialogVisible"
      :pdata="saveReferencesDialogData"
      @saveReferencesDialogCloseEvent="handleSaveReferencesDialogClose"
      @saveReferencesDialogConfirmEvent="handleSaveReferencesDialogConfirm"
    ></save-references-dialog>
    <search-references-dialog
      :pvisible="searchReferencesDialogVisible"
      @searchReferencesDialogCloseEvent="handleSearchReferencesDialogClose"
      @searchReferencesDialogConfirmEvent="handleSearchReferencesDialogConfirm"
    ></search-references-dialog>
    <save-evidence-dialog
      :pvisible="saveEvidenceDialogVisible"
      :pdata="saveEvidenceDialogData"
      @saveEvidenceDialogCloseEvent="handleSaveEvidenceDialogClose"
      @saveEvidenceDialogConfirmEvent="handleSaveEvidenceDialogConfirm"
    ></save-evidence-dialog>
  </div>
</template>

<script>
import saveReferencesDialog from './saveReferencesDialog'
import searchReferencesDialog from './searchReferencesDialog'
import saveEvidenceDialog from './addGeneticMutationSaveEvidenceDialog'
export default {
  name: 'addGeneticMutation',
  components: {saveReferencesDialog, searchReferencesDialog, saveEvidenceDialog},
  props: [],
  mounted () {
    this.getMutationFunctionList()
    this.getData()
  },
  watch: {},
  computed: {
    mutationsData () {
      return this.$store.getters.getValue('mutationsData')
    }
  },
  data () {
    return {
      activeName: '1',
      form: {
        fid: '',
        gene: '',
        mutationType: '',
        nucleotideMutation: '',
        aminoAcidMutation: '',
        referenceSeq: '',
        mutationFunction: '',
        evidenceLevel: '',
        geneMutationAnalysis: '',
        geneMutationAnalysisCn: '',
        geneMutationAnalysisEn: '',
        geneMutationAnalysisRemarks: '',
        geneMutationDrugId: '',
        flag: '',
        variationChr: '',
        variationStart: '',
        variationStop: '',
        variationRef: '',
        variationCall: ''
      },
      rules: {
        gene: [
          {required: true, message: '请输入基因名称', trigger: 'blur'}
        ],
        nucleotideMutation: [
          {required: true, message: '请输入核苷酸改变', trigger: 'blur'}
        ],
        aminoAcidMutation: [
          {required: true, message: '请输入氨基酸改变', trigger: 'blur'}
        ],
        referenceSeq: [
          {required: true, message: '请输入参考序列', trigger: 'blur'}
        ],
        geneMutationAnalysis: [
          {required: true, message: '请输入变异概述', trigger: 'blur'}
        ],
        geneMutationAnalysisCn: [
          {required: true, message: '请输入基因变异解析(中文)', trigger: 'blur'}
        ]
      },
      selectedRow: [],
      tableData: [],
      mutationTypeList: [
        {
          label: '未知',
          value: '未知'
        },
        {
          label: '致病',
          value: '致病'
        },
        {
          label: '疑似致病',
          value: '疑似致病'
        },
        {
          label: '意义未明',
          value: '意义未明'
        }
      ],
      mutationFunctionList: [],
      optionsList: [
        {
          label: '1',
          value: '1'
        }
      ],
      saveReferencesDialogVisible: false,
      saveReferencesDialogData: {},
      searchReferencesDialogVisible: false,
      saveEvidenceDialogVisible: false,
      saveEvidenceDialogData: []
    }
  },
  methods: {
    getData () {
      this.form = Object.assign({}, this.form, this.mutationsData)
    },
    getMutationFunctionList () {
      this.$ajax({
        loadingDom: '.referencesTable',
        url: '/read/unscramble/get_mutation_function',
        method: 'get',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.mutationFunctionList = []
          if (result.data) {
            result.data.forEach(v => {
              this.mutationFunctionList.push({
                label: v.mutationFunction,
                value: v.mutationFunction
              })
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSave (type) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$ajax({
            url: '/read/unscramble/genetic_variation_save_or_update',
            data: {
              fid: this.form.fid,
              gene: this.form.gene.trim(),
              mutationType: this.form.mutationType.trim(),
              nucleotideMutation: this.form.nucleotideMutation.trim(),
              aminoAcidMutation: this.form.aminoAcidMutation.trim(),
              referenceSeq: this.form.referenceSeq.trim(),
              mutationFunction: this.form.mutationFunction.trim(),
              evidenceLevel: this.form.evidenceLevel.trim(),
              geneMutationAnalysis: this.form.geneMutationAnalysis.trim(),
              geneMutationAnalysisCn: this.form.geneMutationAnalysisCn.trim(),
              geneMutationAnalysisEn: this.form.geneMutationAnalysisEn.trim(),
              geneMutationAnalysisRemarks: this.form.geneMutationAnalysisRemarks.trim(),
              geneMutationDrugId: this.form.geneMutationDrugId.trim(),
              flag: type,
              variationChr: this.form.variationChr.trim(),
              variationStart: this.form.variationStart.trim(),
              variationStop: this.form.variationStop.trim(),
              variationRef: this.form.variationRef.trim(),
              variationCall: this.form.variationCall.trim()
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              window.close()
            } else {
              this.$message.error(result.message)
            }
          })
        } else {
          this.$message.error('必填项校验不通过')
        }
      })
    },
    handleClick (tab, event) {},
    handleCurrentChange (val) {
      this.selectedRow = val
    },
    handleGetReferences () {
      this.$ajax({
        loadingDom: '.referencesTable',
        url: '/read/unscramble/auto_get_literature',
        method: 'get',
        data: {
          ftext: this.form.geneMutationAnalysisCn.trim(),
          associatedId: ''
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRow = []
          this.tableData = result.data.rows
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleEditReferences (type) {
      if (type === 0) {
        this.saveReferencesDialogData = {
          libraryId: '',
          referenceType: '',
          referenceId: '',
          referenceName: ''
        }
        this.saveReferencesDialogVisible = true
      } else {
        if (this.selectedRow.length !== 0) {
          this.saveReferencesDialogData = {
            libraryId: this.selectedRow.rLibraryId,
            referenceType: this.selectedRow.rClassified,
            referenceId: this.selectedRow.rId,
            referenceName: this.selectedRow.rName
          }
          this.saveReferencesDialogVisible = true
        } else {
          this.$message.error('请选择一条数据')
        }
      }
    },
    handleSelectReferences () {
      this.searchReferencesDialogVisible = true
    },
    handleEditEvidence () {
      this.saveEvidenceDialogData = this.form.evidenceLevel.split(',')
      this.saveEvidenceDialogVisible = true
    },
    handleDeleteReferences () {
      if (this.selectedRow) {
        let index = this.tableData.findIndex(v => v.rLibraryId === this.selectedRow.rLibraryId)
        this.tableData.splice(index, 1)
        this.selectedRow = ''
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleMove (type) {
      if (this.selectedRow) {
        let row = this.selectedRow
        if (type) {
          // 上移
          let index = this.tableData.findIndex(v => v.rLibraryId === row.rLibraryId)
          if (index !== 0) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index - 1, 0, row)
          }
        } else {
          // 下移
          let index = this.tableData.findIndex(v => v.rLibraryId === row.rLibraryId)
          if (index !== this.tableData.length - 1) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index + 1, 0, row)
          }
        }
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleSaveReferencesDialogClose () {
      this.saveReferencesDialogVisible = false
    },
    handleSaveReferencesDialogConfirm () {
      this.saveReferencesDialogVisible = false
      this.handleGetReferences()
    },
    handleSearchReferencesDialogClose () {
      this.searchReferencesDialogVisible = false
    },
    handleSearchReferencesDialogConfirm (data) {
      console.log(data)
      this.searchReferencesDialogVisible = false
      if (data.length !== 0) {
        let index = -1
        data.forEach(v => {
          index = this.tableData.findIndex(vv => {
            return vv.rLibraryId === v.rLibraryId
          })
          if (index === -1) {
            this.tableData.push(v)
          }
        })
      }
    },
    handleSaveEvidenceDialogClose () {
      this.saveEvidenceDialogVisible = false
    },
    handleSaveEvidenceDialogConfirm (data) {
      this.saveEvidenceDialogVisible = false
      this.form.evidenceLevel = data.join(',')
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    height: calc(100vh - 40px);
    .header{
      height: 58px;
      padding: 0 20px;
      border-bottom: 2px solid #E4E7ED;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title{
        font-size: 18px;
        font-weight: bold;
      }
    }
    .content{
      >>>.el-tabs__item{
        width: 160px;
        text-align: right;
      }
      height: calc(100% - 60px);
      display: flex;
      overflow: auto;
    }
    >>>.el-table thead{
      font-size: 14px;
      font-weight: 500;
      color: #909399;
    }
    >>>.el-table th{
      background-color: rgba(0, 0, 0, 0);
    }
  }
</style>
