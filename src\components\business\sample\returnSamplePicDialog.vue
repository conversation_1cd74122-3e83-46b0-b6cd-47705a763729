<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="文件上传"
      width="30%"
      @open="handleOpen"
    >
      <el-upload
        ref="upload"
        :on-success="handleOnSuccess"
        :on-error="handleOnError"
        :on-change="handleChange"
        :http-request="uploadFile"
        :auto-upload="false"
        :file-list="fileList"
        :before-upload="handleBeforeUpload"
        :action="uploadUrl"
        multiple>
        <el-button size="mini" type="primary">点击上传</el-button>
        <span slot="tip" style="margin-left: 10px; font-size: 12px; color: #F56C6C;">请选择JPG， PNG格式图片或压缩包</span>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading"  type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
export default {
  name: 'clinicalInfoManagementUploadDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  mounted () {},
  watch: {},
  computed: {},
  data () {
    return {
      type: '',
      uploadUrl: constants.JS_CONTEXT + '/sample/clinical/upload_pic_or_pdf',
      loading: false,
      fileList: [],
      fileData: new FormData()
    }
  },
  methods: {
    handleOpen () {
      this.fileList = []
    },
    uploadFile (file) {
      this.fileData.append('file', file.file) // append增加数据
    },
    handleConfirm () {
      if (this.fileList.length > 0) {
        let valid = this.fileList.every(v => {
          if (/\.(png|jpg|jpeg|zip|rar)$/i.test(v.name)) {
            return true
          } else {
            this.$message.error('只能上传图片或压缩文件')
            return false
          }
        })
        if (!valid) {
          this.$message.error('请检查，上传文件类型!')
        } else {
          this.submitUpload()
        }
      } else {
        this.$message.error('请上传文件')
      }
    },
    submitUpload () {
      this.loading = true
      this.$ajax({
        url: '/sample/return/upload_return_pic',
        data: {
          file: this.fileList.map(v => v.raw)
        },
        isFormData: true
      }).then(result => {
        console.log(result)
        if (result.code === this.SUCCESS_CODE) {
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          let message = result.message
          this.$alert(message, '上传反馈', {
            showConfirmButton: false
          })
        }
        this.$refs.upload.clearFiles()
      }).finally(() => {
        this.loading = false
      })
    },
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data
        let fileAbsolutePaths = data.fileAbsolutePath ? data.fileAbsolutePath.split(',') : []
        let paths = data.path ? data.path.split(',') : []
        let groups = data.group ? data.group.split(',') : []
        let fileNames = data.fileNames ? data.fileNames.split(',') : []
        let tableData = []
        let item = {}
        fileAbsolutePaths.forEach((v, i) => {
          item = {
            path: paths[i],
            group: groups[i],
            fileName: fileNames[i],
            fileAbsolutePath: fileAbsolutePaths[i]
          }
          tableData.push(item)
        })
        this.$emit('uploadDialogConfirmEvent', tableData)
      } else {
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    handleOnError () {
      this.loading = false
    },
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(png|jpg|jpeg|zip|rar)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过10M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传图片或压缩文件')
        return false
      }
    },
    handleChange (file, fileList) {
      let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)
      if (existFile) {
        this.$message.error('当前文件已经存在!')
        fileList.pop()
      }
      this.fileList = fileList
    }
  }
}
</script>

<style scoped></style>
