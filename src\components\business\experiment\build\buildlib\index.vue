<template>
  <div class="container">
    <div class="header">建库排单</div>
    <div class="build-page-wrapper">
      <el-tabs v-model="activeName" @tab-click="handleTabChange">
        <el-tab-pane label="常规样本" name="1">
          <div v-if="activeName === '1'" class="table-wrapper">
            <div class="btn-group">
              <el-button v-if="$setAuthority('004003014', 'buttons')" type="primary" size="mini" plain @click="handleAddSample">新增样本</el-button>
              <el-button v-if="$setAuthority('004003015', 'buttons')" type="primary" size="mini" plain @click="handleImport(1)">导入</el-button>
              <el-button v-if="$setAuthority('004003016', 'buttons')" type="primary" size="mini" plain @click="handleRemove">移除</el-button>
              <el-button v-if="$setAuthority('004003017', 'buttons')" :loading="savaTemplateLoading" type="primary" size="mini" plain @click="handleTemporarySave">暂存</el-button>
            </div>
            <el-table ref="table" :data="tableDataInfo.build" :height="tbHeight" :cell-style="handleRowStyle"
              class="table" size="mini" border style="width: 100%" @select="handleSelectTable"
              @row-click="handleRowClick" @select-all="handleSelectAll">
              <el-table-column type="selection" width="60"></el-table-column>
              <el-table-column type="index" label="序号" width="80"></el-table-column>
              <el-table-column prop="fbreakPlateNum" label="自动板号" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="nucleicAcidCode" label="核酸编号" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="sampleType" label="样本类型" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column prop="productCode" label="产品/项目编号" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="productName" label="产品/项目名称" min-width="150"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="libraryType" label="文库类型" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column prop="nucleicAcidConcentration" label="核酸浓度" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="nucleicAcidVolume" label="核酸体积" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="expectedTime" label="预交付时间" min-width="150"
                show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
          <div class="footer">
            <el-button type="primary" size="mini" plain @click="handleCancel">取消排单</el-button>
            <el-button :loading="submitLoading" type="primary" size="mini" plain @click="handleSubmit">提交排单</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="避index样本" name="2">
          <div v-if="activeName === '2'" class="table-wrapper">
            <div class="btn-group">
              <el-button v-if="$setAuthority('004003015', 'buttons')" type="primary" size="mini" plain @click="handleImport(2)">补充避index样本</el-button>
              <el-button v-if="$setAuthority('004003016', 'buttons')" type="primary" size="mini" plain @click="handleRemove">移除</el-button>
              <el-button v-if="$setAuthority('004003017', 'buttons')" :loading="savaTemplateLoading" type="primary" size="mini" plain @click="handleTemporarySave">暂存</el-button>
            </div>
            <el-table ref="table" :data="tableDataInfo.index" :height="tbHeight" :cell-style="handleRowStyle"
              class="table" size="mini" border style="width: 100%" @select="handleSelectTable"
              @row-click="handleRowClick" @select-all="handleSelectAll">
              <el-table-column type="selection" width="60"></el-table-column>
              <el-table-column type="index" label="序号" width="80"></el-table-column>
              <el-table-column prop="nucleicAcidCode" label="核酸编号" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="productCode" label="产品/项目编号" min-width="120"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="findexNum" label="index号" min-width="150"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="expectedTime" label="预交付时间" min-width="150"
                show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
          <div class="footer">
            <el-button v-if="$setAuthority('004003019', 'buttons')" :loading="cancelScheduleLoading" type="primary" size="mini" plain @click="handleCancel">取消排单</el-button>
            <el-button v-if="$setAuthority('004003018', 'buttons')" :loading="submitLoading" type="primary" size="mini" plain @click="handleSubmit">提交排单</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <add-sample-dialog :pvisible.sync="addSampleDialogVisible" :check-table-data-ids="checkTableDataIds"
      @dialogConfirmEvent="handleAddSampleConfirm">
    </add-sample-dialog>
    <import-sample-dialog :pvisible.sync="importDialogVisible" :type="importType"
      @dialogConfirmEvent="handleAddSampleConfirm"></import-sample-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import addSampleDialog from './AddBuildSampleDialog.vue'
import importSampleDialog from './importBuildSampleDialog.vue'
import { awaitWrap, deepCopy, setDefaultEmptyValueForObject, readBlob, downloadFile } from '../../../../../util/util'
import { getTemplateScheduleSampleListApi, saveTemplateScheduleSampleListApi, submitScheduleSampleListApi, cancelScheduleSampleListApi, downloadSchedulingListApi } from '@/api/experiment/index' // 导入获取排单样本列表API
export default {
  name: 'InterruptOrder',
  mixins: [mixins.tablePaginationCommonData],
  components: { addSampleDialog, importSampleDialog },
  mounted () {
    this.$_setTbHeight(74 + 58 + 50 + 55)
    this.getData()
  },
  data () {
    return {
      tableDataInfo: {
        build: [],
        index: []
      },
      activeName: '1',
      importType: 1, // 1 常规样本 2 避index样本
      map: {
        '1': 'build',
        '2': 'index'
      },
      submitLoading: false,
      savaTemplateLoading: false,
      cancelScheduleLoading: false,
      isFirstClickIndex: true,
      checkTableDataIds: [],
      importDialogVisible: false,
      addSampleDialogVisible: false
    }
  },
  methods: {
    async handleTabChange () {
      this.selectedRows.clear()
      // 判断是否有暂存的样本, 是否是第一次进入该页面
      if (this.isFirstClickIndex) {
        this.isFirstClickIndex = false
        await this.getData()
        this.tableData = this.tableDataInfo[this.map[this.activeName]]
      }
    },
    // 获取数据
    async getData () {
      const { res } = await awaitWrap(getTemplateScheduleSampleListApi({
        ftype: this.activeName
      }, {
        loadingDom: '.build-page-wrapper'
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.selectedRows.clear()
        const data = res.data || []
        this.tableDataInfo[this.map[this.activeName]] = data.map((item, index) => {
          const v = {
            id: item.flibNum || (item.fdnaNum + item.fprobe),
            nucleicAcidCode: item.fdnaNum, // 核酸编号
            sampleType: item.fsampleType, // 样本类型
            productCode: item.fproCode, // 产品项目编号
            productName: item.fproName, // 产品项目名称
            libraryType: item.flibType, // 文库类型
            nucleicAcidConcentration: item.fdnaConcentration, // 核酸浓度
            nucleicAcidVolume: item.fdnaVolume, // 核酸体积
            expectedTime: item.fdeliverTime, // 预计付时间
            libScheduleId: item.flibScheduleId, // 保存原始ID用于后续操作
            fbreakPlateNum: item.fbreakPlateNum, // 自动板号
            importType: item.importType || 0, // 导入类型：0-新增，1-导入
            // 保留其他原始字段
            fpreDeliverTime: item.fpreDeliverTime,
            fspecialRemark: item.fspecialRemark,
            fsequencingPlatform: item.fsequencingPlatform,
            fprobe: item.fprobe,
            fhybridBaseNum: item.fhybridBaseNum,
            fdataSize: item.fdataSize,
            findexNum: item.findexNum,
            findexSerialNum: item.findexSerialNum,
            fconnectorType: item.fconnectorType,
            flibNum: item.flibNum,
            fdnaLevel: item.fdnaLevel,
            fdnaTotal: item.fdnaTotal,
            fsrpTaskOrderNum: item.fsrpTaskOrderNum,
            fproperty: item.fproperty,
            flibCount: item.flibCount,
            fdv200: item.fdv200,
            ftype: item.ftype,
            fseqBatch: item.fseqBatch,
            fseqTime: item.fseqTime,
            fseqType: item.fseqType,
            fseqLane: item.fseqLane,
            foriginNum: item.foriginNum,
            fsequenceType: item.fsequenceType,
            fcrossPoolingLib: item.fcrossPoolingLib,
            fpoolingLib: item.fpoolingLib,
            fcopyPath: item.fcopyPath,
            fsequenatorRequire: item.fsequenatorRequire,
            fsequenatorRun: item.fsequenatorRun,
            frealDnaNum: item.frealDnaNum,
            fisUrgent: item.fisUrgent
          }
          // 创建该项目的深拷贝，用于保留原始数据不变
          v.realData = deepCopy(v)
          // 设置对象的默认空值，用于确保对象属性的一致性和避免null值问题
          setDefaultEmptyValueForObject(v)
          return v
        })
        this.checkTableDataIds = this.tableDataInfo[this.map[this.activeName]].map(item => item.id).filter(item => item) // 保存选中的id，用于后续添加和删除操作
      }
    },
    // 新增样本
    handleAddSample () {
      if (this.activeName === '2') {
        this.$message.error('暂不支持添加避index样本')
        return
      }
      // 实际项目中应该打开新增样本的对话框
      this.addSampleDialogVisible = true
    },
    handleAddSampleConfirm (samples) {
      let tableData = this.tableDataInfo[this.map[this.activeName]] || []
      console.log('samples', samples.map(v => v.id), tableData.map(v => v.id))
      tableData = tableData.filter(item => {
        return samples.findIndex(sample => sample.id === item.id) === -1
      })
      console.log('tableData', tableData)
      this.tableDataInfo[this.map[this.activeName]] = [...tableData, ...samples]
      this.tableData = this.tableDataInfo[this.map[this.activeName]]
      this.handleSort(this.tableData)
      this.checkTableDataIds = this.tableDataInfo[this.map[this.activeName]].map(item => item.id).filter(item => item) // 保存选中的id，用于后续添加和删除操作
    },
    handleSort (data) {
      // 调试信息（仅开发环境）
      this.debugSortInfo(data)

      this.tableData = data.sort((a, b) => {
        // 获取实际数据
        const aData = a.realData || a
        const bData = b.realData || b

        // 判断是否为样本查询列表（activeName为'1'表示建库，'2'表示index）
        const isSampleQuery = this.activeName === '1' || this.activeName === '2'

        if (isSampleQuery) {
          // 样本查询列表排序逻辑：相同板号 > 新增样本没有板号的数据
          return this.sortForSampleQuery(aData, bData)
        } else {
          // 排单列表排序逻辑：相同板号 > 新增样本没有板号的数据 > 导入的样本
          return this.sortForScheduleList(aData, bData)
        }
      })
    },

    // 样本查询列表排序
    sortForSampleQuery (a, b) {
      const aHasPlate = a.fbreakPlateNum && a.fbreakPlateNum.trim()
      const bHasPlate = b.fbreakPlateNum && b.fbreakPlateNum.trim()
      const aIsNew = a.importType === 0 // 新增样本
      const bIsNew = b.importType === 0 // 新增样本

      // 1. 相同板号的数据优先（新增样本且有板号）
      if (aIsNew && aHasPlate && bIsNew && bHasPlate) {
        // 都是新增且有板号，按板号分组，相同板号内按预交付时间倒序
        if (a.fbreakPlateNum === b.fbreakPlateNum) {
          return this.compareByExpectedTime(a, b)
        }
        return a.fbreakPlateNum.localeCompare(b.fbreakPlateNum)
      }

      // 2. 新增样本有板号 > 新增样本无板号
      if (aIsNew && aHasPlate && bIsNew && !bHasPlate) return -1
      if (aIsNew && !aHasPlate && bIsNew && bHasPlate) return 1

      // 3. 新增样本无板号的数据，按预交付时间倒序
      if (aIsNew && !aHasPlate && bIsNew && !bHasPlate) {
        return this.compareByExpectedTime(a, b)
      }

      // 4. 其他情况按原有逻辑
      return this.compareByExpectedTime(a, b)
    },

    // 排单列表排序
    sortForScheduleList (a, b) {
      const aHasPlate = a.fbreakPlateNum && a.fbreakPlateNum.trim()
      const bHasPlate = b.fbreakPlateNum && b.fbreakPlateNum.trim()
      const aIsNew = a.importType === 0 // 新增样本
      const bIsNew = b.importType === 0 // 新增样本
      const aIsImported = a.importType === 1 // 导入样本
      const bIsImported = b.importType === 1 // 导入样本

      // 1. 相同板号的数据优先（新增样本且有板号）
      if (aIsNew && aHasPlate && bIsNew && bHasPlate) {
        // 都是新增且有板号，按板号分组，相同板号内按预交付时间倒序
        if (a.fbreakPlateNum === b.fbreakPlateNum) {
          return this.compareByExpectedTime(a, b)
        }
        return a.fbreakPlateNum.localeCompare(b.fbreakPlateNum)
      }

      // 2. 新增样本有板号 > 新增样本无板号
      if (aIsNew && aHasPlate && bIsNew && !bHasPlate) return -1
      if (aIsNew && !aHasPlate && bIsNew && bHasPlate) return 1

      // 3. 新增样本无板号 > 导入样本
      if (aIsNew && !aHasPlate && bIsImported) return -1
      if (aIsImported && bIsNew && !bHasPlate) return 1

      // 4. 新增样本有板号 > 导入样本
      if (aIsNew && aHasPlate && bIsImported) return -1
      if (aIsImported && bIsNew && aHasPlate) return 1

      // 5. 相同类型内部按预交付时间倒序
      if (aIsNew && !aHasPlate && bIsNew && !bHasPlate) {
        return this.compareByExpectedTime(a, b)
      }

      if (aIsImported && bIsImported) {
        return this.compareByExpectedTime(a, b)
      }

      // 6. 其他情况按原有逻辑
      return this.compareByExpectedTime(a, b)
    },

    // 按预交付时间倒序比较
    compareByExpectedTime (a, b) {
      const aTime = a.expectedTime || a.fdeliverTime || a.fpreDeliverTime
      const bTime = b.expectedTime || b.fdeliverTime || b.fpreDeliverTime

      if (!aTime && !bTime) return 0
      if (!aTime) return 1
      if (!bTime) return -1

      // 倒序排序（最新的在前面）
      return new Date(bTime) - new Date(aTime)
    },

    // 调试方法：打印排序信息
    debugSortInfo (data) {
      if (process.env.NODE_ENV === 'development') {
        console.log('=== 排序调试信息 ===')
        console.log('当前页面类型:', this.activeName === '1' ? '建库' : 'index')
        console.log('数据总数:', data.length)

        const grouped = {
          newWithPlate: [],
          newWithoutPlate: [],
          imported: []
        }

        data.forEach(item => {
          const realData = item.realData || item
          const hasPlate = realData.fbreakPlateNum && realData.fbreakPlateNum.trim()
          const isNew = realData.importType === 0

          if (isNew && hasPlate) {
            grouped.newWithPlate.push({
              id: realData.nucleicAcidCode,
              plate: realData.fbreakPlateNum,
              time: realData.expectedTime || realData.fdeliverTime
            })
          } else if (isNew && !hasPlate) {
            grouped.newWithoutPlate.push({
              id: realData.nucleicAcidCode,
              time: realData.expectedTime || realData.fdeliverTime
            })
          } else {
            grouped.imported.push({
              id: realData.nucleicAcidCode,
              plate: realData.fbreakPlateNum,
              time: realData.expectedTime || realData.fdeliverTime
            })
          }
        })

        console.log('新增有板号:', grouped.newWithPlate)
        console.log('新增无板号:', grouped.newWithoutPlate)
        console.log('导入样本:', grouped.imported)
        console.log('==================')
      }
    },
    // 导入
    handleImport (type) {
      this.importType = type
      this.importDialogVisible = true
    },
    // 移除
    async handleRemove () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择需要移除的数据！')
        return
      }
      const rows = [...this.selectedRows.values()]
      // 判断是否有相同版号的样本没有被勾选,查找出对应版号的样本
      const notCheckSamples = []
      rows.forEach(row => {
        if (row.realData.fbreakPlateNum) {
          // 查找出对应版号的样本
          const samples = this.tableDataInfo[this.map[this.activeName]].filter(item => item.fbreakPlateNum === row.fbreakPlateNum)
          // 判断是否有相同版号的样本没有被勾选
          if (samples.some(item => !this.selectedRows.has(item.id))) {
            notCheckSamples.push(row.realData.nucleicAcidCode)
          }
        }
      })
      if (notCheckSamples.length > 0) {
        await this.$confirm(`所选样本核酸编号${notCheckSamples.join(',')}对应板号下有其他样本，不能单独移除当前样本。请确认是否移除整板样本。`, '移除样本', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } else {
        await this.$confirm('确认移除选中的样本或与选中样本相同板号的样本。', '移除样本', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      // 直接删除tableData中的数据
      rows.forEach(row => {
      // 删除所有 相同板号的数据
        if (row.realData.fbreakPlateNum) {
          const samples = this.tableDataInfo[this.map[this.activeName]].filter(item => item.fbreakPlateNum === row.fbreakPlateNum)
          samples.forEach(sample => {
            const index = this.tableDataInfo[this.map[this.activeName]].findIndex(item => item.id === sample.id)
            if (index !== -1) {
              this.tableDataInfo[this.map[this.activeName]].splice(index, 1)
            }
          })
        }
        const index = this.tableDataInfo[this.map[this.activeName]].findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.tableDataInfo[this.map[this.activeName]].splice(index, 1)
        }
      })
      this.selectedRows.clear()
      this.checkTableDataIds = this.tableDataInfo[this.map[this.activeName]].map(item => item.id).filter(item => item) // 保存选中的id，用于后续添加和删除操作
      this.$message.success('移除成功')
    },
    setParamList (list) {
      return list.map(v => {
        const item = v.realData || v
        return {
          ftype: item.ftype,
          // 按照要求映射字段
          fdnaNum: item.nucleicAcidCode, // 核酸编号
          fsampleType: item.sampleType, // 样本类型
          fproCode: item.productCode, // 产品项目编号
          fproName: item.productName, // 产品项目名称
          flibType: item.libraryType, // 文库类型 todo: 后台少字段了
          fdnaConcentration: item.nucleicAcidConcentration, // 核酸浓度
          fdnaVolume: item.nucleicAcidVolume, // 核酸体积
          fdeliverTime: item.expectedTime, // 预计付时间
          flibScheduleId: item.libScheduleId, // 保存原始ID用于后续操作

          // 保留其他原始字段
          fpreDeliverTime: item.fpreDeliverTime,
          fsequenceType: item.fsequenceType,
          fspecialRemark: item.fspecialRemark,
          fsequencingPlatform: item.fsequencingPlatform,
          fprobe: item.fprobe,
          fhybridBaseNum: item.fhybridBaseNum,
          fdataSize: item.fdataSize,
          findexNum: item.findexNum,
          findexSerialNum: item.findexSerialNum,
          fconnectorType: item.fconnectorType,
          flibNum: item.flibNum,
          fdnaLevel: item.fdnaLevel,
          fdnaTotal: item.fdnaTotal,
          fsrpTaskOrderNum: item.fsrpTaskOrderNum,
          fproperty: item.fproperty,
          flibCount: item.flibCount,
          fdv200: item.fdv200,
          fseqBatch: item.fseqBatch,
          fseqTime: item.fseqTime,
          fseqType: item.fseqType,
          fseqLane: item.fseqLane,
          foriginNum: item.foriginNum,
          fcrossPoolingLib: item.fcrossPoolingLib,
          fpoolingLib: item.fpoolingLib,
          fcopyPath: item.fcopyPath,
          fsequenatorRequire: item.fsequenatorRequire,
          fsequenatorRun: item.fsequenatorRun,
          frealDnaNum: item.frealDnaNum,
          fisUrgent: item.fisUrgent
        }
      })
    },
    submitParams () {
      return {
        ftype: this.activeName,
        fsampleList: [...this.setParamList(this.tableDataInfo['build']), ...this.setParamList(this.tableDataInfo['index'])]
      }
    },
    // 暂存
    async handleTemporarySave () {
      this.savaTemplateLoading = true
      const params = this.submitParams()
      const { res } = await awaitWrap(saveTemplateScheduleSampleListApi(params))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('暂存成功')
      }
      this.savaTemplateLoading = false
    },
    // 取消排单
    async handleCancel () {
      await this.$confirm('即将取消当前样本的排单操作，请确认。', '取消排单确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.cancelScheduleLoading = true
      const {res} = await awaitWrap(cancelScheduleSampleListApi({
        ftype: this.activeName
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('取消排单成功')
        window.close()
      }
      this.cancelScheduleLoading = false
    },
    // 提交排单
    async handleSubmit () {
      await this.$confirm('所选样本将进行自动排单，请再次确认是否提交。', '确认排单提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const params = this.submitParams()
      this.submitLoading = true
      const {res} = await awaitWrap(submitScheduleSampleListApi(params, {
        showErrorMessageBox: false
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.submitLoading = false
        await this.$confirm('样本已完成排单, 是否下载', '排单完成通知', {
          confirmButtonText: '下载',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.handleDownload(res.data)
          this.getData()
        }).catch(() => {
          this.getData()
        })
        this.isFirstClickIndex = true // 重新获取数据
      } else {
        const data = res.data || {}
        if (data.errorLogDTOS && data.errorLogDTOS.length > 0) {
          this.$showErrorDialog({tableData: data.errorLogDTOS})
          this.submitLoading = false
        } else {
          this.$message.error(res.message)
        }
      }
      this.submitLoading = false
    },
    // 下载到样本表
    async handleDownload (data) {
      const { res } = await awaitWrap(downloadSchedulingListApi({ fscheduleTaskId: data }))
      if (res) {
        const { err } = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  max-width: 100%;
  margin: 0 auto;
  background: #fff;
  border-radius: 10px;
  border: 1px solid #e5e6eb;
}

.header {
  padding: 28px 32px 16px 32px;
  color: #222;
  font-size: 22px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  letter-spacing: 1px;
}

.btn-group {
  padding: 12px 0;
}

.table-wrapper {
  border-radius: 6px;
  overflow-x: auto;
  background: #fff;
  border: 1px solid #f0f0f0;
}

.build-page-wrapper {
  margin: 0 20px
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 32px;
  background: transparent;
  border-top: none;

  .btn {
    min-width: 90px;
    font-size: 14px;
    margin-left: 12px;
  }
}

@media (max-width: 900px) {
  .container {
    max-width: 98vw;
  }

  .header,
  .btn-group,
  .table-wrapper,
  .footer {
    padding-left: 8px;
    padding-right: 8px;
  }
}
</style>
