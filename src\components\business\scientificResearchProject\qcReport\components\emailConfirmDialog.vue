<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
      title="邮件信息确认"
      width="60%"
      @open="handleOpen"
    >
      <div class="wrapper">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-suffix=":">
          <el-form-item label="邮件标题" prop="title">
            <el-input v-model.trim="form.title" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="邮件正文">
            <el-input  v-model.trim="form.content"
                       type="textarea"
                       autosize
                       size="mini"></el-input>
          </el-form-item>
          <el-form-item label="邮件附件">
            <div :key="index" v-for="(item, index) in form.attachmentsText">
              {{item}} <i class="el-icon-delete" @click="handleDelete(index)"></i>
            </div>
            <el-upload
              :data="{
                mailId: this.form.id
              }"
              :show-file-list="false"
              :on-success="handleSuccess"
              :action="actionUrl"
              class="upload-demo">
              <el-button size="mini" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
<!--          <el-form-item label="报告模版">-->
<!--            常规DNA报告模版-->
<!--          </el-form-item>-->
          <el-form-item label="收件箱" prop="receiver">
            <el-input v-model.trim="form.receiver" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="抄送邮箱" prop="fcc">
            <el-input v-model.trim="form.fcc" size="mini"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="submitLoading" size="mini" type="primary" @click="handleConfirm">确认发送</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'
import constants from '../../../../../util/constants'
import util from '../../../../../util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    reportId: {
      type: [String, Number]
    }
  },
  data () {
    return {
      actionUrl: constants.JS_CONTEXT + '/order/report/mail_attachment_upload',
      submitLoading: false,
      form: {
        title: '',
        content: '',
        receiver: '',
        fcc: ''
      },
      rules: {
        receiver: [
          {required: true, message: '请输入邮箱', trigger: ['change', 'blur']},
          {required: true, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ],
        fcc: [
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.getEmailData()
    },
    // 获取邮件数据
    async getEmailData () {
      let res = await this.$ajax({
        url: '/order/report/get_report_mail_data',
        data: {
          reportIds: this.reportId
        },
        method: 'get'
      })
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data || {}
        this.form = {
          id: data.fid,
          title: data.femailSubject,
          content: data.feamilContent,
          attachments: data.fattachments.split(';'),
          attachmentsText: data.fattachments.split(';').map(v => {
            let names = v.split('/')
            return names[names.length - 1]
          }),
          receiver: data.freceiver,
          fcc: data.fcc
        }
      }
    },
    // 发送邮件
    handleConfirm () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          this.$ajax({
            url: '/order/report/send_mail',
            data: {
              fid: this.form.id,
              freportId: this.reportId,
              femailSubject: this.form.title,
              feamilContent: this.form.content,
              fattachments: this.form.attachments.join(','),
              freceiver: this.form.receiver,
              fcc: this.form.fcc
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success('发送成功')
              this.$emit('dialogConfirmEvent')
              this.visible = false
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.submitLoading = false
          })
        }
      })
    },
    async handleClose () {
      await this.$confirm('关闭后邮件内容将不保存，确认关闭吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.visible = false
    },
    // 删除附件假, 提交时后台根据文件名真实删除
    async handleDelete (index) {
      await this.$confirm('是否删除附件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.form.attachments.splice(index, 1)
      this.form.attachmentsText.splice(index, 1)
    },
    handleSuccess (response, file, fileList) {
      if (response.code === this.SUCCESS_CODE) {
        this.form.attachments.push(file.name)
        this.form.attachmentsText.push(file.name)
      } else {
        // 删除上传失败的文件
        let index = 0
        for (const i in fileList) {
          if (fileList[i] === file) {
            index = i
            break
          }
        }
        // 移出当前文件对象
        fileList.splice(index, 1)
        this.$message.error(response.message)
      }
    },
    // 上传之前的处理函数
    beforeImgUpload (file) {
      const isImg = file.type.indexOf('image') > -1
      let name = file.name
      const isZip = /\.(zip|rar)$/i.test(name)
      if (file.size > 50 * 1024 * 1024) {
        this.$message.error(`文件: ${file.name} ,大小超过50M，无法上传`)
        return false
      }
      if (!isImg && !isZip) {
        this.$message.error('只能上传图片和压缩包！')
      }
      return isImg
    },
    async handleBeforeRemove (type) {
      let {code, message} = await this.$ajax({
        url: '/order/report/remove_report',
        data: {
          reportId: this.reportId,
          picType: type
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.$message.success('删除成功')
      } else {
        this.$message.error(message)
      }
    },
    // 浏览图片
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  height: 500px;
  overflow: auto;
}
</style>
