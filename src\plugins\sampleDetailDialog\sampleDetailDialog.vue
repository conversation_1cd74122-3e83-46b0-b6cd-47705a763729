<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="visible"
    :before-close="handleClose"
    title="样本详情"
    width="600px"
    @open="handleOpen">
    <el-table
      ref="table"
      :data="tableData"
      class="sample-table"
      size="mini"
      border
      height="50vh"
      style="width: 100%"
    >
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column label="核酸/吉因加编号" prop="geneCode" min-width="120" show-overflow-tooltip/>
      <el-table-column label="到样时间 " prop="arriveSampleTime" min-width="120" show-overflow-tooltip/>
    </el-table>
  </el-dialog>
</template>

<script>

// import xx form 'xxx'
import util, {awaitWrap} from '../../util/util'
import {} from '../../api/sequencingManagement/transformApi'
import {getSampleDetail} from '../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: `errorDialog`,
  data () {
    return {
      visible: false,
      downloadLoading: false,
      geneInfo: {},
      tableData: []
    }
  },
  methods: {
    async handleOpen () {
      let {res} = await awaitWrap(getSampleDetail({
        ...this.geneInfo
      }, {loadingDom: '.sample-table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.tableData = []
        data.forEach(v => {
          const item = {
            fid: v.fid,
            geneCode: v.fgeneCode,
            arriveSampleTime: v.farriveSampleTime
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    handleClose () {
      this.visible = false
      this.tableData = []
      this.downloadLoading = false
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table td, .el-table th{
  padding: 6px 0;
}
.title{
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
}
</style>
