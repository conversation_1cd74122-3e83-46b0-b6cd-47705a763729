<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :with-header="false"
      :size="'40%'"
      :wrapper-closable="false"
      @open="handleOpen">
      <el-form :model="form" style="padding: 20px 5px" label-position="right" label-width="140px" label-suffix=":">
        <el-form-item label="产品编号">
         {{form.fproductCode}}
        </el-form-item>
        <el-form-item label="产品名称">
         {{form.fproductName}}
        </el-form-item>
        <el-form-item label="生产实验室">
         {{form.fprocedureLaboratory}}
        </el-form-item>
        <div class="title">工序配置</div>
        <div style="margin:0 30px">
          <el-table
            ref="table"
            :header-cell-style="{background: '#ffffff'}"
            :data="tableData"
            size="mini"
            height="200"
            style="width:100%">
            <el-table-column prop="fcode" label="工序编码"></el-table-column>
            <el-table-column prop="fname" label="工序名称"></el-table-column>
          </el-table>
        </div>
        <el-form-item v-if="hasBuildType" label="建库类型">
         {{form.fbuildingType}}
        </el-form-item>
        <div class="title">产品参数</div>
        <el-form-item label="样本类型">
          <div style="padding-right: 20px;">{{form.fsampleType}}</div>
        </el-form-item>
        <el-form-item label="物种">
          <el-checkbox-group v-model.trim="form.fspecies" size="mini" disabled>
            <el-checkbox label="人">人</el-checkbox>
            <el-checkbox label="动物">动物</el-checkbox>
            <el-checkbox label="植物">植物</el-checkbox>
            <el-checkbox label="微生物">微生物</el-checkbox>
            <el-checkbox label="其他">其他</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="hasOtherSpecies" label="其他物种">
         {{form.fotherSpecies}}
        </el-form-item>
        <div class="title">其他</div>
        <hr/>
        <div style="display: flex;align-items: center;justify-content: space-around;margin: 10px 0">
          <span>创建人: {{form.fcreator}}</span>
          <span>事业部：{{form.fworkPart}}</span>
          <span>创建时间：{{form.fcreateTime}}</span>
        </div>
        <div  class="button-wrapper"><el-button size="mini" @click="handleClose">返回</el-button></div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'sequenceDrawer',
  mixins: [mixins.dialogBaseInfo],
  props: {
    fid: {
      type: Number
    }
  },
  computed: {
    hasOtherSpecies () {
      return this.form.fspecies ? this.form.fspecies.some((item) => item === '其他') : false
    },
    hasBuildType () {
      return this.tableData.includes('建库')
    }
    // hasComputed () { // 暂时没有上机
    //   return this.tableData.includes('上机')
    // }
  },
  data () {
    return {
      form: {
        productCategoryCode: ''
      },
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/system/otherProduct/get_seq_factory_product_detail',
        method: 'get',
        data: {
          fotherProductId: this.fid
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form = {
            fid: data.fid,
            fproductTypeCode: data.fproductTypeCode,
            fproductTypeName: data.fproductTypeName,
            fproductCode: data.fproductCode,
            fproductName: data.fproductName,
            fprocessCode: data.fprocessCode,
            fprocessName: data.fprocessName,
            fsampleType: data.fsampleType,
            fspecies: data.fspecies ? data.fspecies.split('/') : [], // 物种
            fotherSpecies: data.fotherSpecies,
            fgeneGroupSize: data.fgeneGroupSize,
            fprocedureLaboratory: data.fprocedureLaboratory,
            fextractSop: data.fextractSop,
            flibSop: data.flibSop,
            fseqPlat: data.fseqPlat, // 测序平台
            fseqPlot: data.fseqPlot ? data.fseqPlot.split('/') : [], // 测序策略
            fdataNum: data.fdataNum,
            fdataUnit: data.fdataUnit,
            fsampleNum: data.fsampleNum,
            fisAutoProduce: data.fisAutoProduce,
            fworkPart: data.fworkPart,
            fcreator: data.fcreator,
            fcreateTime: data.fcreateTime,
            fupdator: data.fupdator,
            fupdateTime: data.fupdateTime,
            fbuildingType: data.fbuildingType
          }
          this.tableData = []
          let processList = result.data.fprocessList || []
          processList.forEach(v => {
            let item = {
              fname: v.fname,
              fcode: v.fcode
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    }
  }
}
</script>

<style scoped>

>>>.el-form-item {
  margin: 10px;
  color: #606266;
}
>>>.el-input{
  width: 300px
}
>>>.el-form-item {
  margin-bottom: 0;
}

/*解决办法：*/
/*1.显示滚动条：当内容超出容器的时候，可以拖动：*/
>>>.el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}

/*2.隐藏滚动条，太丑了*/
>>>.el-drawer__container ::-webkit-scrollbar {
  display: none;
}
>>>.el-radio {
  margin-right: 10px;
}
.button-wrapper {
  position: absolute;
  bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 5px;
  height: 40px;
  width: 100%;
  box-shadow: 19px -2px 26px -8px rgba(49,44,44,0.75);
}
</style>
