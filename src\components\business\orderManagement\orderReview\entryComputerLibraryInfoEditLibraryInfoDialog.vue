<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="编辑"
      width="700px"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" v-if="visible" label-position="top" label-width="100px" size="mini" label-suffix="：" inline>
        <el-form-item label="吉因加编号" prop="geneplusNum">
          <el-input v-model.trim="form.geneplusNum" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="文库名称" prop="libraryName">
          <el-input v-model.trim="form.libraryName" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="片段大小（bp）" prop="fragmentSize">
          <el-input v-model.trim="form.fragmentSize" class="form-width" max-lenght="20" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="文库数据量（G）" prop="libraryDateNum">
        <el-input v-model.trim="form.libraryDateNum" type="number" class="form-width" max-lenght="20" placeholder="请输入"></el-input>
      </el-form-item>
        <el-form-item label="碱基均衡" prop="baseBalance">
          <el-select v-model="form.baseBalance" class="form-width" placeholder="请选择">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="送样浓度" prop="samplingConcentration">
          <el-input v-model.trim="form.samplingConcentration" max-lenght="10" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="送样体积" prop="samplingVolume">
          <el-input v-model.trim="form.samplingVolume" max-lenght="10"  class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="文库备注" prop="libraryNotes">
          <el-input v-model.trim="form.libraryNotes" max-lenght="150"  class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="子文库名称" prop="subLibraryName">
          <el-input v-model.trim="form.subLibraryName" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="Index位数" prop="indexDigits">
          <el-input v-model.trim="form.indexDigits" type="number" max-lenght="20" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="Index编号" prop="indexNum">
          <el-input v-model.trim="form.indexNum" max-lenght="20" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="Index1序列" prop="index1Index">
          <el-input v-model.trim="form.index1Index"  max-lenght="100" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="Index2序列" prop="index2Index">
          <el-input v-model.trim="form.index2Index" max-lenght="100" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="子文库数据量（G）" prop="subLibraryDateNum">
          <el-input v-model.trim="form.subLibraryDateNum" type="number" max-lenght="20" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="子文库备注" prop="subLibraryNotes">
          <el-input v-model.trim="form.subLibraryNotes" max-lenght="150" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
export default {
  name: 'entryComputerLibraryInfoEditLibraryInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Object | null
    }
  },
  data () {
    const self = this
    // 第一个验证，仅允许数字、字母、- _ 组成
    const valid1 = function (rule, value, callback) {
      let regx = /^[0-9A-Za-z\-_]{1,}$/
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error('仅允许数字和字母还有“-”“_”组成'))
      }
    }
    const sameNameValid = function (rule, value, callback) {
      self.form.libraryName === value ? callback(new Error('子文库名称不能与文库名称相同')) : callback()
    }
    const indexVaild = function (rule, value, callback) {
      let regx = /^[ATCG]{1,}$/i
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error('只允许输入ATCG四个字母'))
      }
    }
    return {
      title: '',
      form: {},
      rules: {
        libraryName: [
          {required: true, message: '请输入文库名称', trigger: 'blur'},
          {validator: valid1, trigger: 'blur'}
        ],
        libraryDateNum: [
          {required: true, message: '请输入文库数据量', trigger: 'blur'}
        ],
        baseBalance: [
          {required: true, message: '请选择碱基均衡', trigger: 'change'}
        ],
        subLibraryName: [
          {required: true, message: '请输入', trigger: 'blur'},
          {validator: valid1, trigger: 'blur'},
          {validator: sameNameValid, trigger: 'blur'}
        ],
        indexNum: [
          {required: true, message: '请输入Index编号', trigger: 'blur'},
          {validator: valid1, trigger: 'blur'}
        ],
        index1Index: [
          {required: true, message: '请输入Index1序列', trigger: 'blur'},
          {validator: indexVaild, trigger: 'blur'}
        ],
        index2Index: [
          {required: true, message: '请输入Index2序列', trigger: 'blur'},
          {validator: indexVaild, trigger: 'blur'}
        ],
        subLibraryDateNum: [
          {required: true, message: '请输入子文库数据量', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      if (this.pdata) {
        this.form = {...this.pdata.form}
      } else {
        this.form = {
          geneplusNum: '',
          libraryName: '',
          fragmentSize: '',
          libraryDateNum: '',
          baseBalance: '',
          samplingConcentration: '',
          samplingVolume: '',
          libraryNotes: '',
          subLibraryName: '',
          indexDigits: '',
          indexNum: '',
          index1Index: '',
          index2Index: '',
          subLibraryDateNum: '',
          subLibraryNotes: ''
        }
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = {
            form: this.form
          }
          if (this.pdata) data.index = this.pdata.index
          this.$emit('dialogConfirmEvent', data)
          this.visible = false
        }
      })
    }
  }
}
</script>

<style scoped>
  .form-width{
    width: 200px;
  }
</style>
