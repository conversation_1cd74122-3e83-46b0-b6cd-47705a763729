<template>
  <div>
    <el-dialog
        title="样本统计"
        :visible.sync="visible"
        width="900px"
        :close-on-click-modal="false"
        :before-close="handleClose"
        @open="handleOpen">
      <el-form
        ref="form"
        :model="form"
        label-width="80px"
        inline
        size="mini">
        <el-form-item prop="lab" label="实验室">
          <el-select size="mini" v-model.trim="form.lab" multiple clearable collapse-tags>
            <el-option v-for="(v, k) in regionObj" :key="k" :label="v" :value="v"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-date-picker
              style="width: 300px;"
              v-model.trim="form.time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="getData">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="num-row">
        <div class="num-item">
          <div class="num">{{inNum}}</div>
          <div class="text">入库数量</div>
        </div>
        <div class="num-item">
          <div class="num">{{outNum}}</div>
          <div class="text">出库数量</div>
        </div>
        <div class="num-item">
          <div class="num">{{preciousSampleCount}}</div>
          <div class="text">珍贵样本数量</div>
        </div>
        <div class="num-item">
          <div class="num">{{destroyedSampleCount}}</div>
          <div class="text">销毁样本数量</div>
        </div>
        <div class="num-item">
          <div class="num">{{returnedSampleCount}}</div>
          <div class="text">返样样本数量</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
export default {
  name: 'sampleSearchSampleStatisticDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      form: {
        lab: [],
        time: []
      },
      inNum: '-', // 入库数量
      outNum: '-',
      preciousSampleCount: '-',
      destroyedSampleCount: '-',
      returnedSampleCount: '-',
      regionObj: constants.REGION_OBJ
    }
  },
  methods: {
    handleOpen () {
      const regionValues = Object.values(this.regionObj)
      this.form = {
        lab: [...regionValues],
        time: []
      }
      this.inNum = '-'
      this.outNum = '-'
      this.preciousSampleCount = '-'
      this.destroyedSampleCount = '-'
      this.returnedSampleCount = '-'
    },
    handleReset () {
      this.form = {
        lab: [],
        time: []
      }
      this.inNum = '-'
      this.outNum = '-'
      this.preciousSampleCount = '-'
      this.destroyedSampleCount = '-'
      this.returnedSampleCount = '-'
    },
    setParams () {
      const times = this.form.time || []
      return {
        flabNames: this.form.lab,
        startTime: times[0],
        endTime: times[1]
      }
    },
    // 获取数据
    getData () {
      const { lab, time } = this.form
      if ((!lab || lab.length === 0) || (!time || time.length === 0)) {
        this.$message.error('所有查询条件必须完整')
        return
      }
      const params = this.setParams()
      this.$ajax({
        url: '/sample/statistic_sample_warehouse',
        data: params,
        loadingDom: '.num-row',
        loadingObject: {
          text: '正在查询',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          const data = res.data || {}
          this.inNum = data.innerCount
          this.outNum = data.outCount
          this.preciousSampleCount = data.preciousSampleCount
          this.destroyedSampleCount = data.destroyedSampleCount
          this.returnedSampleCount = data.returnedSampleCount
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.num-row{
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 150px;
  .num-item{
    text-align: center;
    .num{
      font-size: 35px;
    }
    .text{
      font-size: 14px;
    }
  }
}
</style>
