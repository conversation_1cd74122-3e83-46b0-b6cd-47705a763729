<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="选择文献"
      width="60%"
      @open="getData">
      <div>
        <el-form ref="form" :model="form" :inline="true" size="mini" label-width="110px" label-suffix="：" @submit.native.prevent>
          <el-form-item label="参考文献">
            <el-input v-model="form.reference" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="table"
          :data="tableData"
          size="mini"
          class="referenceTable"
          height="300"
          style="width: 100%"
          @select="handleSelect"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="rIdNameCombo" label="参考文献"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">添 加</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import obj from '../../../../../util/mixins'
export default {
  name: 'searchReferencesDialog',
  mixins: [obj.tablePaginationCommonData],
  components: {},
  props: ['pvisible'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
    }
  },
  computed: {},
  data () {
    return {
      title: '选择文献',
      visible: this.pvisible,
      selectedRows: [],
      form: {
        reference: ''
      }
    }
  },
  methods: {
    getData () {
      this.$ajax({
        loadingDom: '.referenceTable',
        method: 'get',
        url: '/read/unscramble/get_literature',
        data: {
          refence: this.form.reference,
          page: this.currentPage,
          rows: this.pageSize
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRows = []
          this.totalPage = result.data.total
          this.tableData = result.data.rows
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleClose () {
      this.$emit('searchReferencesDialogCloseEvent')
      this.form.reference = ''
      this.selectedRows = []
      this.tableData = []
    },
    handleConfirm () {
      this.$emit('searchReferencesDialogConfirmEvent', this.selectedRows)
      this.form.reference = ''
      this.selectedRows = []
      this.tableData = []
    },
    handleSelectAll (selection) {
      this.selectedRows = []
      selection.forEach((v, i) => {
        this.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = this.selectedRows.findIndex(v => v.rLibraryId === row.rLibraryId)
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs.table.toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs.table.toggleRowSelection(row, true)
      }
    }
  }
}
</script>

<style scoped>

</style>
