<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="900px"
      @open="handleOpen">
      <div class="content">
        <div class="info">
          <p>申请单号：{{form.orderNum}}</p>
          <p>订单状态：{{form.orderStatus}}<span v-if="statusDes">（{{statusDes}}）</span></p>
          <p>申请人：{{form.applicant}}</p>
          <p>申请时间：{{form.applicationTime}}</p>
          <p>样本总数：{{form.sampleTotal}}</p>
          <p v-if="form.orderNum.includes('调度出库')">目标实验室：{{form.targetLab }}</p>
          <p>所属实验室：{{form.lab}}</p>
        </div>
        <div class="btn">
          <div v-if="page === 'applicationForStorage'">
            <el-button type="primary" size="mini" @click="handleAddRow">增加</el-button>
            <el-button type="primary" size="mini" @click="handleDelRow">删除</el-button>
            <el-button type="primary" size="mini" @click="handleModifyRow">修改</el-button>
          </div>
          <div v-if="page === 'sampleSearch' || page === 'rejectSample'">
            <el-button type="primary" size="mini" :loading="downloading" @click="handleDownloadOrder">下载申请单</el-button>
          </div>
          <div v-if="page === 'reject'">
            <el-button type="primary" size="mini" :loading="loading" @click="handleConfirmOrderReject">确定</el-button>
            <el-button type="primary" size="mini" :loading="downloading" @click="handleDownloadOrder">下载申请单</el-button>
          </div>
        </div>
        <el-table
          :data="tableData"
          :key="orderId"
          ref="sampleTable"
          style="width: 100%;"
          height="300"
          @select="handleSelectTable"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column v-if="page === 'applicationForStorage'" show-overflow-tooltip type="selection" width="55"></el-table-column>
          <el-table-column show-overflow-tooltip type="index" width="70" label="序号"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本状态" width="100" prop="fsampleStatus"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本编号" width="150" prop="fsampleNumber">
            <template slot-scope="scope">
              <span>{{scope.row.fsampleNumber}}</span>
              <el-popover
                :content="scope.row.frejectRemark"
                v-if="scope.row.frejectTime"
                placement="bottom"
                width="200"
                trigger="hover">
                <i slot="reference" class="el-icon-warning-outline" style="color: red;font-size: 14px;"></i>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="样本类型" width="130" prop="fsampleType"></el-table-column>
          <el-table-column show-overflow-tooltip label="存储温度" width="100" prop="ftemperature"></el-table-column>
          <el-table-column show-overflow-tooltip label="管型" width="130" prop="ftubeType"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本量" width="130" prop="fsampleAmount"></el-table-column>
          <el-table-column v-if="form.orderNum.includes('返样')" prop="frecipientCourierNum" label="快递单号" width="120"></el-table-column>
          <el-table-column show-overflow-tooltip label="存储位置" width="130" prop="fsamplePlace"></el-table-column>
          <el-table-column v-if="form.orderNum.includes('调度入库')" show-overflow-tooltip label="快递单号" width="130" prop="frecipientCourierNum"></el-table-column>
          <template v-if="form.orderNum.includes('返样')">
            <el-table-column prop="frecipient" label="收件人" width="120"></el-table-column>
            <el-table-column prop="frecipientAddr" label="收件地址" min-width="180"></el-table-column>
            <el-table-column prop="frecipientTel" label="联系电话" min-width="180"></el-table-column>
            <el-table-column prop="fbackNotes" label="返样备注" min-width="180"></el-table-column>
          </template>
          <template v-else>
            <el-table-column show-overflow-tooltip label="备注" width="130" prop="fnotes"></el-table-column>
          </template>
          <el-table-column v-if="page === 'rejectSample'" label="操作" width="180" fixed="right">
            <template slot-scope="scope">
              <template v-if="scope.row.frejectTime">
                <div class="operateColumn">
                  <el-button :loading="confirmRejectSampleLoading" type="text" @click.stop="handleConfirmSampleReject(scope.row)">确定</el-button>
                  <el-button type="text" @click.stop="handleModifyRejectSample(scope.row)">修改</el-button>
                </div>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          :title="innerTitle"
          :visible.sync="innerDialogVisible"
          :close-on-click-modal="false"
          width="60%"
          append-to-body>
          <el-table
            :data="innerTableData"
            style="width: 100%;"
            height="400">
            <el-table-column type="index" width="70" label="序号"></el-table-column>
            <el-table-column width="110">
              <template slot="header" slot-scope="scope">
                <span v-if="canModify.fsampleStatus" class="red">*</span>
                <span>样本状态</span>
              </template>
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.fsampleStatus" :disabled="!canModify.fsampleStatus" size="mini" placeholder="-"></el-input>
              </template>
            </el-table-column>
            <el-table-column width="130">
              <template slot="header" slot-scope="scope">
                <span v-if="canModify.fsampleNumber" class="red">*</span>
                <span>样本编号</span>
              </template>
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.fsampleNumber" :disabled="!canModify.fsampleNumber" size="mini" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column width="130">
              <template slot="header" slot-scope="scope">
                <span v-if="canModify.fsampleType" class="red">*</span>
                <span>样本类型</span>
              </template>
              <template slot-scope="scope">
                <el-input
                  v-model.trim="scope.row.fsampleType"
                  :disabled="!canModify.fsampleType"
                  size="mini"
                  placeholder="请输入"
                  @change="(val) => handleSampleTypeChange(val, scope.row)"></el-input>
              </template>
            </el-table-column>
            <el-table-column width="130">
              <template slot="header" slot-scope="scope">
                <span class="red" v-if="canModify.ftemperature">*</span>
                <span>存储温度</span>
              </template>
              <template slot-scope="scope">
                <el-select
                  v-model.trim="scope.row.ftemperature"
                  :disabled="!canModify.ftemperature && !scope.row.fsampleType"
                  size="mini"
                  :loading="scope.row.getTemperatureLoading"
                  placeholder="请选择">
                  <template v-for="t in showSampleTypeTemperatureList(scope.row.fsampleType)">
                    <el-option :key="t" :label="t" :value="t"></el-option>
                  </template>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column width="130">
              <template slot="header" slot-scope="scope">
                <span v-if="canModify.ftubeType" class="red">*</span>
                <span>管型</span>
              </template>
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.ftubeType" :disabled="!canModify.ftubeType" size="mini" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column width="130">
              <template slot="header" slot-scope="scope">
                <span v-if="canModify.fsampleAmount" class="red">*</span>
                <span>样本量</span>
              </template>
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.fsampleAmount" :disabled="!canModify.fsampleAmount" size="mini" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="存储位置" min-width="130">
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.fsamplePlace" :disabled="!canModify.fsamplePlace" :placeholder="!canModify.fsamplePlace ? '-' : '请输入'" size="mini"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="备注" width="130">
              <template slot-scope="scope">
                <el-input v-model.trim="scope.row.fnotes" :disabled="!canModify.fnotes" size="mini" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column v-if="innerType === 'add'" label="操作" width="130">
              <template slot-scope="scope">
                <div class="operateBtn">
                  <div>
                    <i :style="{cursor: innerTableData.length !== 1 ? 'pointer' : 'no-drop'}"
                       class="el-icon-remove-outline"
                       @click="handleInnerRemoveRow(scope.$index)"></i>
                  </div>
                  <div>
                    <i :style="{cursor: 'pointer'}"
                       v-if="scope.$index === innerTableData.length - 1"
                       class="el-icon-circle-plus"
                       @click="handleInnerAddRow"></i>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" @click="innerDialogVisible = false">取 消</el-button>
            <template v-if="page === 'rejectSample'">
              <el-button :loading="submitLoading" size="mini" type="primary" @click="handleRejectSampleInnerDialogConfirm">确认</el-button>
            </template>
            <template v-else>
              <el-button size="mini" type="primary" @click="handleInnerDialogConfirm">确认</el-button>
            </template>
          </div>
        </el-dialog>
      </div>
      <div slot="footer" v-if="page === 'applicationForStorage'"  class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  name: 'modifyOrderDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: String | Number
    },
    status: {
      type: String
    },
    statusDes: { // 状态说明，主要用在驳回状态下的驳回原因
      type: String
    },
    page: { // 在样本查询页面值为 sampleSearch, 驳回确定为 reject，存在驳回样本为rejectSample
      type: String,
      default: 'applicationForStorage'
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '修改'
    }
  },
  mounted () {
    // this.handleOpen()
  },
  computed: {
    canModify () {
      if (this.orderId.indexOf('入库') > -1) {
        return {
          fsampleNumber: true,
          fsampleType: true,
          ftemperature: true,
          ftubeType: true,
          fsampleAmount: true,
          fnotes: true
        }
      } else if (this.orderId.indexOf('出库') > -1) {
        return {
          fsampleNumber: true,
          fnotes: true
        }
      } else {
        return {}
      }
    }
  },
  data () {
    return {
      downloading: false,
      tableData: [],
      innerTableData: [], // 内联dialog表格数据
      innerType: '', // add || modify, 内联弹窗的操作模式
      innerDialogVisible: false,
      loading: false,
      submitLoading: false, // 呢立案dialog在处理驳回按钮时候的提交
      confirmRejectSampleLoading: false, // 驳回样本确认时
      innerTitle: '',
      innerTableKey: 0, // 内联dialog表格的可以，每回打开key + 1,是vue 重新渲染table
      form: {
        orderNum: '',
        targetLab: '',
        orderStatus: '',
        applicant: '',
        applicationTime: '',
        sampleTotal: '',
        lab: ''
      },
      tableRow: {
        fsampleStatus: '',
        fsampleNumber: '',
        fsampleType: '',
        ftemperature: '',
        ftubeType: '',
        fsampleAmount: '',
        fsamplePlace: '',
        fnotes: ''
      },
      // 样本类型的温度，key样本类型，value 温度数组 ['-20', '-80']
      sampleTypeTemperatureObj: {},
      selectedRows: new Map()
    }
  },
  methods: {
    handleOpen () {
      if (this.orderId) {
        this.selectedRows.clear()
        this.$ajax({
          url: '/sample/order/get_order_info',
          method: 'get',
          data: {
            orderNumber: this.orderId
          },
          loadingDom: '.content'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            let data = res.data || {}
            this.form.orderNum = data.fsampleOrderNumber
            this.form.orderStatus = data.fsampleOrderStatus
            this.form.targetLab = data.ftargetLab
            this.form.applicant = data.fapplicantName
            this.form.applicationTime = data.fapplicantTime
            this.form.sampleTotal = data.sampleTotal
            this.form.lab = data.flab
            let rows = data.sampleInfos || []
            rows.forEach(item => {
              item.subId = item.fid // 辅助id,用于给新增行一个唯一值，前端用
              item.ftemperature = item.ftemperature || '' // 新增一个温度，避免无法双向绑定
            })
            this.tableData = rows
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    showSampleTypeTemperatureList (type) {
      return this.sampleTypeTemperatureObj[type] || []
    },
    // 添加表格行
    handleAddRow () {
      this.innerTableData = []
      let row = JSON.parse(JSON.stringify(this.tableRow))
      row.subId = 'add' + util.randomCode(10)
      this.innerTableData.push(row)
      this.innerType = 'add'
      this.innerTitle = '添加'
      this.showInnerDialog()
    },
    handleRowClick (row, c) {
      this.$refs.sampleTable.toggleRowSelection(row, !this.selectedRows.has(row.subId))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.subId) ? this.selectedRows.delete(row.subId) : this.selectedRows.set(row.subId, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.subId, row)
      })
    },
    // 删除行
    handleDelRow () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要删除的行')
        return
      }
      let sourceMapData = new Map()
      this.tableData.forEach(item => {
        sourceMapData.set(item.subId, item)
      })
      let selectRows = [...this.selectedRows.keys()]
      selectRows.forEach(k => {
        if (sourceMapData.has(k)) {
          sourceMapData.delete(k)
        }
      })
      this.tableData = []
      this.tableData.push(...sourceMapData.values())
      this.selectedRows.clear()
    },
    // 修改行
    handleModifyRow () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要修改的行')
        return
      }
      this.innerTableData = []
      this.innerTableData.push(...this.selectedRows.values())
      this.innerType = 'modify'
      this.innerTitle = '修改'
      this.showInnerDialog()
    },
    // 修改被驳回的样本
    handleModifyRejectSample (row) {
      this.innerTableData = []
      this.innerTableData.push(row)
      this.innerType = 'modify'
      this.innerTitle = '修改'
      this.showInnerDialog()
    },
    showInnerDialog () {
      this.innerDialogVisible = true
      this.innerTableKey++
    },
    // 内联dialog再加一行
    handleInnerAddRow () {
      let row = JSON.parse(JSON.stringify(this.tableRow))
      row.subId = 'add' + util.randomCode(10)
      this.innerTableData.push(row)
    },
    handleInnerRemoveRow (index) {
      if (this.innerTableData.length > 1) {
        this.innerTableData.splice(index, 1)
      }
    },
    // 样本类型改变
    handleSampleTypeChange (val, row) {
      console.log(val, row)
      if (val) {
        row.ftemperature = ''
        const list = this.sampleTypeTemperatureObj[val]
        if (!list || list.length === 0) this.getSampleTypeTemperature(val, row)
      }
    },
    // 获取温度
    async getSampleTypeTemperature (type, row) {
      this.$set(row, 'getTemperatureLoading', true)
      try {
        const { code, data, message } = await this.$ajax({
          url: '/sample/sample_type/get_temperature_by_sample_type',
          method: 'get',
          data: {
            sampleType: type
          }
        })
        code === this.SUCCESS_CODE ? this.$set(this.sampleTypeTemperatureObj, type, data || []) : this.$message.error(message)
      } finally {
        this.$set(row, 'getTemperatureLoading', false)
      }
    },
    // 内联弹窗校验必填项是否全部填写
    innerDialogValidateTableRow () {
      let hasAllWrite = true
      let requiredField = Object.keys(this.canModify)
      let notesIndex = requiredField.indexOf('fnotes')
      if (notesIndex > -1) {
        requiredField.splice(notesIndex, 1)
      }
      for (let i = 0; i < this.innerTableData.length; i++) {
        let item = this.innerTableData[i]
        let itemAllWrite = requiredField.every(v => {
          return item[v]
        })
        if (!itemAllWrite) {
          hasAllWrite = false
          break
        }
      }
      return hasAllWrite
    },
    // 修改被驳回样本确认
    handleRejectSampleInnerDialogConfirm () {
      let hasAllWrite = this.innerDialogValidateTableRow()
      if (!hasAllWrite) {
        this.$message.error('必填项未填写完整')
        return
      }
      this.submitLoading = true
      this.$ajax({
        url: '/sample/order/alter_sample',
        data: this.innerTableData[0]
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.innerDialogVisible = false
          this.handleOpen()
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    // 内联弹窗确认
    handleInnerDialogConfirm () {
      let hasAllWrite = this.innerDialogValidateTableRow()
      if (!hasAllWrite) {
        this.$message.error('必填项未填写完整')
        return
      }
      let sourceMapData = new Map()
      this.tableData.forEach(item => {
        sourceMapData.set(item.subId, item)
      })
      this.innerTableData.forEach(item => {
        sourceMapData.set(item.subId, item)
      })
      this.tableData = []
      this.tableData.push(...sourceMapData.values())
      this.selectedRows.clear()
      this.innerDialogVisible = false
    },
    // 确认修改
    handleDialogConfirm () {
      this.loading = true
      this.$ajax({
        url: 'sample/order/alter_order',
        data: {
          orderNumber: this.orderId,
          sampleInfos: this.tableData
        },
        loadingDom: '.content'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 反选表格
    selectRows () {
      this.selectedRows.forEach(v => {
        this.$refs.sampleTable.toggleRowSelection(v, false)
      })
      this.selectedRows.forEach(v => {
        if (this.selectedRows.has(v.subId)) {
          this.$refs.sampleTable.toggleRowSelection(v, true)
        }
      })
    },
    // 下载申请单
    handleDownloadOrder () {
      this.downloading = true
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${this.orderId}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        }).finally(() => {
          this.downloading = false
        })
      })
    },
    // 驳回订单下的确认
    handleConfirmOrderReject () {
      this.loading = true
      this.$ajax({
        url: '/sample/order/confirm_reject_order',
        method: 'get',
        data: {
          orderNumber: this.orderId
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('成功')
          this.handleDownloadOrder()
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 驳回样本的确认
    handleConfirmSampleReject (row) {
      this.$confirm('该样本将在申请单中删除，并且该样本此次入库信息不会在系统中存储, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning'
      }).then(() => {
        this.confirmRejectSampleLoading = true
        this.$ajax({
          url: '/sample/order/confirm_reject_sample',
          method: 'get',
          data: {
            sampleId: row.fid
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('成功')
            this.handleOpen()
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.confirmRejectSampleLoading = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .operateColumn .el-button{
    padding: unset;
  }
.info{
  display: flex;
  flex-wrap: wrap;
  p{
    width: 50%;
    line-height: 2;
    font-size: 16px;
  }
}
  .btn{
    padding: 15px 0;
  }
  .red{
    color: red;
  }
.operateBtn{
  width: 80px;
  flex-shrink: 0;
  display: flex;
  padding: 0;
  div{
    width: 50%;
    height: 100%;
    text-align: center;
  }
}
</style>
