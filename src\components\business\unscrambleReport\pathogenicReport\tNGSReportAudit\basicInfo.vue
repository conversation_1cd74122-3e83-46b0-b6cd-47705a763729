<template>
  <div>
    <el-descriptions :column="3" border>
      <el-descriptions-item label="姓名">{{basicInfo.name}}</el-descriptions-item>
      <el-descriptions-item label="性别">{{basicInfo.sex}}</el-descriptions-item>
      <el-descriptions-item label="出生年月">{{basicInfo.birthday}}</el-descriptions-item>
      <el-descriptions-item label="身份证/护照">{{basicInfo.idCard}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{basicInfo.contactPhone}}</el-descriptions-item>
      <el-descriptions-item label="病历号">{{basicInfo.caseNo}}</el-descriptions-item>
      <el-descriptions-item label="样本编号">{{basicInfo.sampleNum}}</el-descriptions-item>
      <el-descriptions-item label="样本类型">{{basicInfo.sampleType}}</el-descriptions-item>
      <el-descriptions-item label="样本采集日期">{{basicInfo.sampleCollectDate}}</el-descriptions-item>
      <el-descriptions-item label="样本接收日期">{{basicInfo.sampleReceiveDate}}</el-descriptions-item>
      <el-descriptions-item label="报告日期">{{basicInfo.reportDate}}</el-descriptions-item>
      <el-descriptions-item label="标本号">{{basicInfo.sampleNo}}</el-descriptions-item>
      <el-descriptions-item label="送检单位">{{basicInfo.sendUnit}}</el-descriptions-item>
      <el-descriptions-item label="送检医生">{{basicInfo.sendDoctor}}</el-descriptions-item>
      <el-descriptions-item label="送检项目">{{basicInfo.inspectionItems}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {formatYearMonth} from '@/util/util'
export default {
  mounted () {
    this.getBasicInfo()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      basicInfo: {}
    }
  },
  methods: {
    // 获取报告基本信息
    async getBasicInfo () {
      let {code, data} = await this.$ajax({
        url: '/read/pathogen/get_online_report_basic_info',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get',
        loadingDom: '.components-wrapper'
      })
      if (code && code === this.SUCCESS_CODE) {
        this.basicInfo = {
          name: data.name,
          sampleNum: data.sampleNum,
          sendUnit: data.hospital,
          sex: data.sex,
          sampleType: data.sampleType,
          sendDoctor: data.doctor,
          birthday: formatYearMonth(data.birthday),
          sampleCollectDate: data.sampleCollectDate,
          sendProject: data.sendProject,
          idCard: data.idcard,
          sampleReceiveDate: data.sampleReceiveDate,
          contactPhone: data.phone,
          reportDate: data.reportDate,
          caseNo: data.caseNo,
          sampleNo: data.sampleNo,
          inspectionItems: data.inspectionItems
        }
      }
    }
  }
}
</script>

<style scoped></style>
