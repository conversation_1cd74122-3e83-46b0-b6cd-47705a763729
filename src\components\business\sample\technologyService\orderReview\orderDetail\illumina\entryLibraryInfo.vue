<template>
  <div>
    <el-form
      ref="detectInfoForm"
      :model="detectInfo"
      :rules="rules"
      :show-message="false"
      size="mini"
      label-position="right"
      label-width="110px"
      inline>
      <div class="module">
        <div class="module-title-bar" style="height: 25px;">
          <div>
            <p class="min-title">检测信息</p>
          </div>
        </div>
        <div class="content">

          <el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="automaticDetection" style="position: relative">
                  <template #label>
                    实验模式
                  </template>
                  <div style="display: flex" class="form-width">
                    <p v-if="readOnly">{{experimentTypes[detectInfo.fexperimentType]}}</p>
<!--                    <el-select v-model="detectInfo.automaticDetection" v-else class="form-width" placeholder="请选择（必填）">-->
<!--                      <el-option label="是" value="是"></el-option>-->
<!--                      <el-option label="否" value="否"></el-option>-->
<!--                    </el-select>-->
                    <div style="display: inline-block; margin-left: 10px">
                      <el-tooltip class="item" effect="dark" placement="top-start">
                        <div slot="content">
                          <b>合格自动实验：</b>样本质控“合格”则自动开始后续实验，否则样本需要“申请检测”来确认开始后续实验<br/>
                          <b>全部自动实验：</b>样本无论质控结果如何，均默认开始后续实验<br/>
                          <b>确认后实验：</b>样本无论质控结果如何，均需要“申请检测”来确认开始后续实验<br/>
                          <b>注：</b>“申请检测”需要在本系统“测序工厂样本管理 - 订单查询”模块选择订单后执行操作
                        </div>
                        <i class="el-icon-question"></i>
                      </el-tooltip>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="样本可用完" prop="samplesAvailable">
                  <p v-if="readOnly" class="form-width">{{detectInfo.samplesAvailable || '-'}}</p>
                  <el-select v-model="detectInfo.samplesAvailable" v-else class="form-width" placeholder="请选择（必填）">
                    <el-option label="是" value="是"></el-option>
                    <el-option label="否" value="否"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-col :span="24">
              <el-form-item label="产品名称" prop="detectType">
                <p v-if="readOnly" class="form-width">{{detectInfo.detectType}}</p>
                <el-select v-model="detectInfo.detectType" v-else class="form-width" placeholder="请选择">
                  <el-option
                    :key="k"
                    :label="v"
                    :value="k"
                    v-for="(v, k) in detectTypeLists">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="detectInfo.detectType === '包lane测序'" label="数据拆分需求" prop="splitDemand">
                <p v-if="readOnly" class="form-width">{{detectInfo.splitDemand}}</p>
                <el-select v-model="detectInfo.splitDemand" v-else class="form-width" placeholder="请选择（必填）">
                  <el-option label="拆分" value="拆分"></el-option>
                  <el-option label="不拆分" value="不拆分"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="特殊序列" prop="specialIndex">
                <p v-if="readOnly" class="form-width">{{detectInfo.specialIndex || '-'}}</p>
                <el-select v-model="detectInfo.specialIndex" v-else class="form-width" placeholder="请选择（必填）">
                  <el-option label="有" value="有"></el-option>
                  <el-option label="无" value="无"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="detectInfo.specialIndex === '有'" label="特殊序列说明" prop="specialIndexOther">
                <span v-if="readOnly" class="form-width">{{detectInfo.specialIndexOther}}</span>
                <el-input
                  v-model.trim="detectInfo.specialIndexOther"
                  v-else
                  placeholder="请输入"
                  maxlength="100"
                  class="form-width"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="交付方式" prop="deliveryMethod" style="margin-bottom: 0;">
                <p v-if="readOnly" class="form-width">{{detectInfo.deliveryMethod}}</p>
                <el-select v-model="detectInfo.deliveryMethod" v-else class="form-width" placeholder="请选择（必填）">
                  <el-option label="硬盘交付" value="硬盘交付"></el-option>
                  <el-option label="云交付" value="云交付"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item  v-if="detectInfo.deliveryMethod === '硬盘交付'" label="硬盘寄送地址" prop="hardDriveShippingAddress" style="margin-bottom: 0;">
                <p v-if="readOnly" class="form-width">{{detectInfo.hardDriveShippingAddress}}</p>
                <el-input v-model.trim="detectInfo.hardDriveShippingAddress" v-else maxlength="100" class="form-width"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

        </div>
      </div>
      <div class="module">
        <div class="module-title-bar" style="height: 25px;">
          <div>
            <p class="min-title">订单备注</p>
          </div>
        </div>
        <el-form-item label="订单备注">
          <p v-if="readOnly" class="form-width">{{detectInfo.notes || '-'}}</p>
          <el-input
            v-model.trim="detectInfo.notes"
            v-else
            placeholder="请输入"
            maxlength="100"
            class="form-width"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <div class="module">
      <div class="module-title-bar" style="height: 25px;">
        <div>
          <p class="min-title">注意事项</p>
        </div>
      </div>
      <div class="content tips">
        <p class="p">1、为了顺利完成样品接收和后续实验，请您务必准确填写各项内容，必填项请填写完整。</p>
        <p class="p">2、客户须提供正确的index序列，否则因index序列错误导致拆分异常影响数据交付周期或是造成数据污染乙方将不承担责任。</p>
        <p class="p bold text-indent">填写index时请注意：</p>
        <div>
          <p class="p text-indent">●  index序列须提供合成序列（合成单上的序列）。</p>
          <p class="p text-indent">●  包芯片上机时，若选择不拆分，可不填写index编号和序列项。</p>
        </div>
        <p class="p">3、因甲方提供的index有误造成与同一条lane内其他客户index重复而无法对文库数据正确拆分，该损失由甲方承担。</p>
        <p class="p">4、样品若需其它特殊处理或有其它注意事项，请提前告知。</p>
        <p class="p">5、信息单填写不完整或填写错误产生的沟通确认时间将不计入交付周期； 请备注为空白对照/阴性对照的子文库，以免耽误周期。</p>
        <p class="p">6、交付方式选择硬盘交付时，请在数据下机前，将硬盘邮寄到吉因加，因硬盘提供不及时影响交付周期乙方将不承担责任。</p>
        <p class="p">7、提交订单后，请打印纸质版《吉因加上机文库信息单-Ilumina文库》与样本一同进行寄送。</p>
        <div style="display: flex;margin: 5px 0;">
          <div style="width: 50%;">
            <p class="p bold">单index文库</p>
            <img :src="singleIndexImg" alt="">
          </div>
          <div style="width: 50%;">
            <p class="p bold">双index文库</p>
            <img :src="doubleIndexImg" alt="">
          </div>
        </div>
        <p class="p bold">TruSeq library（单index文库）:</p>
        <p class="p">5’-AATGATACGGCGACCACCGAGATCTACACTCTTTCCCTACACGACGCTCTTCCGATCT-{insert}-AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC（I7）ATCTCGTATGCCGTCTTCTGCTTG-3’</p>
        <p class="p bold">TruSeq library（双index文库）:</p>
        <p class="p">5’-AATGATACGGCGACCACCGAGATCTACAC（I5）ACACTCTTTCCCTACACGACGCTCTTCCGATCT-{insert}-AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC（I7）ATCTCGTATGCCGTCTTCTGCTTG--3’</p>
        <p class="p bold">Nextera Library（双端TN5）:</p>
        <p class="p">5’-AATGATACGGCGACCACCGAGATCTACAC（I5）TCGTCGGCAGCGTCAGATGTGTATAAGAGACAG-{insert}-CTGTCTCTTATACACATCTCCGAGCCCACGAGAC（I7）ATCTCGTATGCCGTCTTCTGCTTG-3’</p>
        <p class="p bold">Nextera Library（TN5+Truseq）:</p>
        <p class="p">5’-AATGATACGGCGACCACCGAGATCTACAC（I5）TCGTCGGCAGCGTCAGATGTGTATAAGAGACAG-{insert}-AGATCGGAAGAGCACACGTCTGAACTCCAGTCAC（I7）ATCTCGTATGCCGTCTTCTGCTTG-3’</p>
      </div>
    </div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">寄送样本信息（{{sampleInfoTable.length}}）</p>
        </div>
        <div v-if="!readOnly">
          <el-button size="mini" @click="handleDeleteParent">删除</el-button>
          <el-button type="primary" size="mini" @click="handleAdd('edit')">编辑</el-button>
          <el-button type="primary" size="mini" @click="handleAdd('add')">新增</el-button>
        </div>
      </div>
      <div class="content">
        <el-table
            ref="table"
            :data="sampleInfoTable"
            size="mini"
            :max-height="340"
            border
            class="computer-table"
            style="width: 100%;"
            @select="(selection, row) => handleSelect(selection, row, 'table')"
            @select-all="(selection) => handleSelectAll(selection, 'table')"
            @row-click="(row, column, e) => handleRowClick(row, column, e, 'table')">
          <el-table-column type="index" width="45" fixed></el-table-column>
          <el-table-column prop="geneplusNum" label="吉因加编号" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleName" label="样本名称" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="species" label="物种" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fragmentSize" label="片段大小（bp）" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unit" label="数据量单位" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryDateNum" label="数据量" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="baseBalance" label="Index碱基平衡" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="storageMedium" label="保存介质" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="isCyclization" label="是否环化" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sequencingPlatform" label="测序平台" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tactics" label="策略" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingConcentration" label="浓度" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingVolume" label="体积" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="isBackUp" label="是否为备份" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="note" label="备注" width="120" show-overflow-tooltip></el-table-column>
        </el-table>
<!--        <el-pagination-->
<!--          @size-change="handleSizeChange"-->
<!--          style="background: #ffffff;"-->
<!--          @current-change="handleCurrentChange"-->
<!--          :page-sizes="pageSizes"-->
<!--          :page-size="pageSize"-->
<!--          :current-page.sync="currentPage"-->
<!--          layout="total, sizes, prev, pager, next, jumper, slot"-->
<!--          :total="totalPage">-->
<!--          <button @click="handleRefresh"><icon-svg iconClass="refresh" /></button>-->
<!--        </el-pagination>-->
      </div>
    </div>

    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">拆分样本信息（{{splitSampleTableData.length}}）</p>
        </div>
        <div v-if="!readOnly">
          <el-button size="mini" @click="handleDeleteChildRow">删除</el-button>
          <el-button type="primary" size="mini" @click="handleSplitSampleAdd('edit')">编辑</el-button>
          <el-button type="primary" size="mini" @click="handleSplitSampleAdd('add')">新增</el-button>
        </div>
      </div>
      <div class="content">
        <el-table
            ref="splitSampleTable"
            :data="splitSampleTableData"
            border
            :max-height="340"
            size="mini"
            class="computer-table"
            style="width: 100%;"
            @select="(selection, row) => handleSelect(selection, row, 'splitSampleTable')"
            @select-all="(selection) => handleSelectAll(selection, 'splitSampleTable')"
            @row-click="(row, column, e) => handleRowClick(row, column, e, 'splitSampleTable')">
          <el-table-column type="index" width="45" fixed></el-table-column>
          <el-table-column prop="sampleName" label="样本名称" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subLibraryName" label="子文库名称" min-width="120" show-overflow-tooltip></el-table-column>
<!--          <el-table-column prop="indexDigits" label="Index位数" width="70" show-overflow-tooltip></el-table-column>-->
          <el-table-column prop="indexNum" label="Index编号" width="70" show-overflow-tooltip></el-table-column>
          <el-table-column prop="indexSpecies" label="单（双）Index" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="index1Index" label="I7序列5'-3’方向" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="index2Index" label="I5序列5'-3’方向" width="110" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unit" label="数据量单位" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subLibraryDateNum" label="数据量" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="baseBalance" label="碱基平衡(插入片段)" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libraryType" label="文库类型" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="indexType" label="index类型" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="phosphorylation" label="末端5'磷酸化" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dataBox" label="建库试剂盒" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="subLibraryNotes" label="备注" min-width="150" show-overflow-tooltip></el-table-column>
        </el-table>
<!--        <el-pagination-->
<!--          @size-change="handleSubSizeChange"-->
<!--          style="background: #ffffff;"-->
<!--          @current-change="handleSubCurrentChange"-->
<!--          :page-sizes="pageSizes"-->
<!--          :page-size="subPageSize"-->
<!--          :current-page.sync="currentSubPage"-->
<!--          layout="total, sizes, prev, pager, next, jumper, slot"-->
<!--          :total="totalSubPage">-->
<!--          <button @click="handleSubRefresh"><icon-svg iconClass="refresh" /></button>-->
<!--        </el-pagination>-->
      </div>
    </div>
    <edit-send-sample-dialog
      :pvisible.sync="editSendSampleDialogVisible"
      :pdata="editSendSampleDialogData.form"
      :row-index="editSendSampleDialogData.rowIndex"
      :is-core-spun="isCoreSpunMachine"
      @dialogConfirmEvent="handleEditSendSampleDialogConfirm"/>
    <!-- 新增/修改拆分样本信息弹窗 -->
    <edit-split-sample-dialog
      :pvisible.sync="editSplitSampleDialogVisible"
      :pdata="editSplitSampleDialogData.form"
      :row-index="editSplitSampleDialogData.rowIndex"
      :sample-name-list="editSplitSampleDialogData.sampleNameList"
      :is-core-spun="isCoreSpunMachine"
      @dialogConfirmEvent="handleEditSplitSampleDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../../../../../util/util'
import mixins from '../../../../../../../util/mixins'

import editSendSampleDialog from './editSendSampleDialog'
import singleIndexImg from '../../../../../../../assets/singleIndex.png'
import doubleIndexImg from '../../../../../../../assets/doubleIndex.png'
import editSplitSampleDialog from './editSplitSampleDialog'
export default {
  name: 'entryLibraryInfo',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    editSplitSampleDialog,
    editSendSampleDialog
  },
  props: {
    readOnly: Boolean,
    orderId: String | Number,
    pageType: String | Number,
    onlineForm: Object // 线上的数据，之前前端填好了的
  },
  mounted () {
    this.getDetectType()
    if (this.orderId) {
      this.getData()
    }
  },
  watch: {
    onlineForm: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.detectInfo = {...newVal}
        }
      },
      deep: true
    }
  },
  computed: {
    sampleInfoTableComputed () {
      this.setSampleData()
      let tableData = []
      this.sampleInfoTable.forEach((v, i) => {
        let item = {}
        if (v.childLibrary && v.childLibrary.length > 0) {
          v.childLibrary.forEach((vv, ii) => {
            item = {...v, ...vv, rowIndex: i + ';' + ii}
            tableData.push(item)
          })
        } else {
          item = {...v}
          tableData.push(item)
        }
      })
      return tableData
    },
    isCoreSpunMachine () { // 应用类型是否是包芯上机
      return this.detectInfo.applicationType === '客户自建文库包芯上机'
    }
  },
  data () {
    return {
      experimentTypes: {
        0: '合格自动实验',
        1: '全部自动实验',
        2: '确认后实验'
      },
      realComponentTableDataName: 'sampleInfoTable',
      realSubComponentTableDataName: 'splitSampleTableData',
      singleIndexImg: singleIndexImg,
      doubleIndexImg: doubleIndexImg,
      detectInfo: {
        automaticDetection: '', // 自动检测
        samplesAvailable: '', // 样本可用完
        fexperimentType: '',
        notes: '', // 备注
        detectType: '', // 检测项目
        applicationType: '', // 数据应用类型
        specialIndex: '', // 特殊序列
        splitDemand: '', // 拆分需求
        specialIndexOther: '', // 特殊序列补充说明
        deliveryMethod: '', // 交付方式
        hardDriveShippingAddress: '' // 硬盘寄送地址
      },
      historyLibrarySampleDialogVisible: false,
      sampleTableType: 1, // 1: 寄送样本表 2：拆分样本表
      sendSampleNameList: [], // 寄送样本表的样本名称列表
      detectTypeLists: {},
      applicationTypeLists: {
        '客户自建文库包芯上机': '客户自建文库包芯上机',
        '客户自建文库散样上机': '客户自建文库散样上机'
      },
      selectedRows: new Map(),
      splitSelectedRows: new Map(), // 拆分样本信息选中
      sampleInfoTable: [],
      editSendSampleDialogData: {
        form: null,
        rowIndex: null
      }, // 编辑数据，新建时传null,编辑时传正常数据
      editSendSampleDialogVisible: false,
      editSplitSampleDialogData: {
        form: null,
        rowIndex: null,
        sampleNameList: [] // 样本名称
      }, // 编辑数据，新建时传null,编辑时传正常数据
      editSplitSampleDialogVisible: false,
      splitSampleTableData: [], // 拆分样本信息表
      importLibraryInfoDialogVisible: false,
      rules: {
        automaticDetection: [
          {required: true, message: '自动检测（必选）', trigger: 'change'}
        ], // 自动检测
        samplesAvailable: [
          {required: true, message: '样本可用完（必选）', trigger: 'change'}
        ], // 样本可用完
        notes: [
          {required: true, message: '请输入备注', trigger: 'blur'}
        ], // 备注
        detectType: [
          {required: true, message: '请选择检测类型', trigger: 'change'}
        ], // 检测类型
        applicationType: [
          {required: true, message: '请选择应用类型', trigger: 'change'}
        ], // 应用类型
        specialIndex: [
          {required: true, message: '请选择特殊序列', trigger: 'change'}
        ], // 特殊序列
        specialIndexOther: [
          {required: true, message: '请输入特殊序列说明', trigger: 'blur'}
        ], // 特殊序列说明
        splitDemand: [
          {required: true, message: '请选择数据拆分需求', trigger: 'change'}
        ], // 拆分需求
        deliveryMethod: [
          {required: true, message: '请选择交付方式', trigger: 'change'}
        ], // 交付方式
        hardDriveShippingAddress: [
          {required: true, message: '请输入硬盘寄送地址', trigger: 'change'}
        ], // 硬盘寄送地址
        isAutoDetect: [
          {required: true, message: '请选择', trigger: 'change'}
        ] // 是否允许根据质控结果自动开启检测
      }
    }
  },
  methods: {
    // 展示其他文案，当一个文案为 '其他'时，展示另外一个字段，用于只读模式
    showOtherText (firstFiled, otherFiled, keyWord = '其他') {
      let text = ''
      text = firstFiled === keyWord ? otherFiled : firstFiled
      return text !== 0 && !text ? '-' : text
    },
    // 获取检测项目
    getDetectType () {
      this.$ajax({
        url: '/order/get_detect_type_list'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.detectTypeLists = {}
          res.data.forEach(v => {
            this.detectTypeLists[v] = v
          })
        } else {
          this.$message.error('获取检测类型失败，错误：' + res.message)
        }
      })
    },
    // 获取文库样本表格数据
    getData () {
      let data = {
        orderId: this.orderId,
        type: this.pageType
      }
      // 只读情况下是要分页的，编辑情况下不分页，目前就在草稿的情况下不分页
      data.pageVO = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$ajax({
        url: '/order/get_lib_or_tissue_list',
        data: data,
        loadingDom: '.computer-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.sampleInfoTable = []
          this.splitSampleTableData = []
          rows.forEach((v, i) => {
            let item = {
              fid: v.fid,
              key: v.fname,
              fsampleCode: v.fsampleCode,
              geneplusNum: v.fgeneCode,
              sampleName: v.fname,
              sampleTag: util.addSampleTag(v.fstatus),
              libraryName: v.fname,
              species: v.fspecies,
              fragmentSize: v.ffragment,
              libraryDateNum: v.fdataSize,
              baseBalance: v.fbaseBalance,
              samplingConcentration: v.fconcentration,
              samplingVolume: v.fvolume,
              libraryNotes: v.fnote,
              storageMedium: v.fsaveMedium,
              tactics: v.fsequenceType,
              unit: v.fdataSizeUnit,
              isCyclization: v.fisCyclizing,
              sequencingPlatform: v.finstrumentType, // 测序平台
              note: v.fnote,
              isBackUp: v.fisBackUp === 0 ? '正常样本' : '备份样本',
              childLibrary: []
            }
            if (v.cosSampleSubBeanList && v.cosSampleSubBeanList.length > 0) {
              v.cosSampleSubBeanList.forEach((vv) => {
                let child = {
                  fid: vv.fsubId,
                  fcode: vv.fcode,
                  key: v.fname + vv.fsubLibName,
                  sampleSubCode: vv.fsampleSubCode,
                  sampleName: v.fname,
                  subLibraryName: vv.fsubLibName,
                  indexDigits: vv.findexNum,
                  indexNum: vv.findexCode,
                  indexSpecies: vv.findex,
                  index1Index: vv.findex1,
                  index2Index: vv.findex2,
                  subLibraryDateNum: vv.fsubDataSize,
                  subLibraryNotes: vv.fsubNote,
                  baseBalance: vv.fsubBaseBanlance,
                  libraryType: vv.flibraryType,
                  indexType: vv.findexType,
                  unit: vv.fsubDataSizeUnit,
                  phosphorylation: vv.fphosphorylation,
                  dataBox: vv.flibraryKit
                }
                item.childLibrary.push(child)
                this.splitSampleTableData.push(child)
              })
            }
            if (!data.pageVO) {
              item.libraryFrontId = i // 前端Id,只有在编辑时有
            }
            this.sampleInfoTable.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 寄送样本新增/编辑数据
    handleAdd (type) {
      let index = null
      let row = null
      if (type === 'edit') {
        if (this.selectedRows.size === 0) {
          this.$message.error('请选择一条数据')
          return
        }
        if (this.selectedRows.size > 1) {
          this.$message.error('只能选择一条数据')
          return
        }
        let key = [...this.selectedRows.keys()][0]
        index = this.sampleInfoTable.findIndex(v => v.key === key)
        row = [...this.selectedRows.values()][0]
      }
      this.editSendSampleDialogData = {rowIndex: index, form: row ? {...row} : null}
      this.editSendSampleDialogVisible = true
    },
    // 寄送样本新增/编辑确认弹窗
    async handleEditSendSampleDialogConfirm (form, rowIndex, oldName, code) {
      let addRepeatName = this.sampleInfoTable.some(v => {
        return oldName
          ? form.sampleName !== oldName && form.sampleName === v.sampleName
          : form.sampleName === v.sampleName
      })
      if (addRepeatName) {
        this.$message.error(`同一个订单中的“样本名称”不允许重复，样本名称“${form.sampleName}”重复`)
        return
      }
      // let addRepeatCode = this.sampleInfoTable.some(v => {
      //   return code
      //     ? form.geneplusNum !== code && form.geneplusNum === v.geneplusNum && form.geneplusNum !== ''
      //     : form.geneplusNum === v.geneplusNum && form.geneplusNum !== ''
      // })
      // if (addRepeatCode) {
      //   this.$message.error(`同一个订单中的“吉因加编号”不允许重复，吉因加编号“${form.geneplusNum}”重复`)
      //   return
      // }
      let relationList = this.splitSampleTableData.filter(v => v.sampleName === oldName).map(vv => vv.subLibraryName)
      if (form.sampleName !== oldName && relationList.length > 0) {
        await this.$confirm(`该样本名称：${oldName}已关联拆分样本信息表子文库名称为${relationList.join(',')}的数据，系统将自动同步修改相关拆分样本信息`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        this.splitSampleTableData.forEach(v => {
          if (v.sampleName === oldName) {
            v.sampleName = form.sampleName
          }
        })
      }
      form.key = form.sampleName // 给表格增加一个key值
      if (rowIndex !== null) {
        this.$set(this.sampleInfoTable, rowIndex, form)
      } else {
        this.sampleInfoTable.unshift({...form})
      }
      this.$refs.table.clearSelection()
      this.selectedRows.clear()
      this.editSendSampleDialogVisible = false
    },
    // 拆分样本新增/编辑数据
    handleSplitSampleAdd (type) {
      let index = null
      let row = null
      if (type === 'edit') {
        if (this.splitSelectedRows.size === 0) {
          this.$message.error('请选择一条数据')
          return
        }
        if (this.splitSelectedRows.size > 1) {
          this.$message.error('只能选择一条数据')
          return
        }
        let key = [...this.splitSelectedRows.keys()][0]
        index = this.splitSampleTableData.findIndex(v => v.key === key)
        row = [...this.splitSelectedRows.values()][0]
      }
      console.log(index, 'indexSub')
      let sampleNameList = [...new Set(this.sampleInfoTable)].map(v => v.sampleName) // 将寄送样本信息的样本名称去重
      this.editSplitSampleDialogData = {rowIndex: index, form: row ? {...row} : null, sampleNameList: sampleNameList}
      this.editSplitSampleDialogVisible = true
    },
    // 新增/修改拆分样本信息弹窗
    handleEditSplitSampleDialogConfirm (form, rowIndex, oldName) {
      let newName = this.subLibraryKey(form)
      let addRepeat = this.splitSampleTableData.some(v => {
        let name = this.subLibraryKey(v)
        return oldName
          ? newName !== oldName && newName === name
          : newName === name
      })
      if (addRepeat) {
        this.$message.error(`同一个订单中“子文库名称”不允许重复，子文库名${form.subLibraryName}重复。`)
        return
      }
      form.key = this.subLibraryKey(form) // 给表格增加一个key值
      if (rowIndex !== null) {
        this.$set(this.splitSampleTableData, rowIndex, form)
      } else {
        this.splitSampleTableData.unshift({...form})
      }
      this.$refs.splitSampleTable.clearSelection()
      this.splitSelectedRows.clear()
      this.editSplitSampleDialogVisible = false
    },
    // 将sampleInfo转化为map
    changeTable (skipIndex) {
      let tableMap = new Map()
      this.sampleInfoTable.forEach((v, i) => {
        let key = v.geneplusNum +
          v.libraryName +
          v.species +
          v.fragmentSize +
          v.libraryDateNum +
          v.baseBalance +
          v.samplingConcentration +
          v.samplingVolume +
          v.libraryNotes
        if (i !== skipIndex) {
          tableMap.set(key, util.deepCopy(v))
        }
      })
      return tableMap
    },
    // 删除寄送样本信息表数据
    delSampleInfo (list) {
      console.log(list, 'list')
      this.sampleInfoTable = this.sampleInfoTable.filter(v => {
        return !list.includes(v.sampleName)
      })
      this.selectedRows.clear()
    },
    // 删除父文库行
    async handleDeleteParent () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择一条记录后进行操作')
        return
      }
      await this.$confirm('数据删除后，拆分样本信息表中的子文库信息将会删除，确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let delSampleList = [...this.selectedRows.keys()]
      this.delSampleInfo(delSampleList)
      this.splitSampleTableData = this.splitSampleTableData.filter(vv => {
        return !delSampleList.includes(vv.sampleName)
      })
    },
    // 子文库唯一值key
    subLibraryKey (row) {
      return row.sampleName + '-' + row.subLibraryName
    },
    delSubLibraryInfo () {
      return new Promise((resolve, reject) => {
        let tableData = util.deepCopy(this.splitSampleTableData) // 深度复制拆分样本信息表
        let tableDataMap = new Map()
        let sampleNameNumObj = {} // 展示拆分样本信息表中各样本名称数量
        tableData.forEach(v => {
          let num = sampleNameNumObj[v.sampleName]
          sampleNameNumObj[v.sampleName] = num ? num + 1 : 1
          if (!tableDataMap.has(v.key)) tableDataMap.set(v.key, v)
        })
        this.splitSelectedRows.forEach((v, k) => {
          if (tableDataMap.has(k)) {
            tableDataMap.delete(k)
            sampleNameNumObj[v.sampleName] = sampleNameNumObj[v.sampleName] - 1
            console.log(sampleNameNumObj, 'sampleNameNumObj')
          }
        })
        resolve({tableData: tableDataMap, sampleNameNumObj: sampleNameNumObj})
      })
    },
    // 删除子文库数据
    handleDeleteChildRow () {
      if (this.splitSelectedRows.size === 0) {
        this.$message.error('请选择一条记录后进行操作')
        return
      }
      this.delSubLibraryInfo().then((obj) => {
        console.log(obj, 'tableData')
        let {tableData, sampleNameNumObj} = obj
        // 获取彻底删除的样本名称
        let completeDelList = []
        for (let k in sampleNameNumObj) {
          if (sampleNameNumObj[k] === 0) {
            completeDelList.push(k)
          }
        }
        // 若completeDelList不为空，则删除寄送样本信息表中的数据
        if (completeDelList.length > 0) {
          this.$confirm(`该记录是${completeDelList.join(',')}寄送样本对应的最后一个子文库，删除后系统将自动删除${completeDelList.join(',')}寄送样本的相关信息？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.splitSampleTableData = [...tableData.values()]
            this.delSampleInfo(completeDelList)
            this.splitSelectedRows.clear()
          })
          return
        }
        this.$confirm(`已选拆分样本${this.splitSelectedRows.size}条，是否确认删除？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.splitSampleTableData = [...tableData.values()]
          this.splitSelectedRows.clear()
        })
      })
    },
    setSampleData () {
      let sampleMap = new Map()
      this.sampleInfoTable.forEach(v => {
        let item = {
          ...v,
          childLibrary: []
        }
        sampleMap.set(v.sampleName, item)
      })
      this.splitSampleTableData.forEach(vv => {
        if (sampleMap.has(vv.sampleName)) {
          sampleMap.get(vv.sampleName).childLibrary.push(vv)
        }
      })
    },
    // 校验
    validForm () {
      return new Promise((resolve, reject) => {
        this.$refs.detectInfoForm.validate(valid => {
          let msg = ''
          if (!valid) {
            msg = '存在必填字段未填写，请检查'
            reject(msg)
            return
          }
          if (this.sampleInfoTable.length === 0 || this.splitSampleTableData.length === 0) {
            this.$alert('请填写样本信息', '提示', {
              confirmButtonText: '确定'
            })
            let m = ''
            reject(m)
            return
          }
          // if (!this.orderId) this.setSampleData() // 设置样本表的数据结构
          if (this.detectInfo.applicationType === '客户自建文库包芯上机') {
            /**
             * 数据量
             *“测序模式”字段为“PE100”时，数据量需要≤1000G
             *“测序模式”字段为“PE150”时，数据量需要≤1500G
             *若数据量超出，则需要提示：数据量超出标准数据量
             *注：此处的数据量为所有子文库数据量的总和
             * **/
            let sum = 0
            this.sampleInfoTableComputed.forEach(v => {
              sum += +v.subLibraryDateNum
            })
            if (this.sampleInfoTable.tactics === 'PE100') {
              msg = sum > 1000 ? '数据量超出标准数据量' : ''
            } else if (this.sampleInfoTable.tactics === 'PE150') {
              msg = sum > 1500 ? '数据量超出标准数据量' : ''
            }
            /**
             * 拆分需求
             * “数据拆分需求”字段为“拆分”，则文库信息中“I7序列5'-3’方向”或“I5序列5'-3’方向”字段不能同时为空
             * 未填写则提示：需要数据拆分则需要填写“I7序列”或“I5序列”
             * */
            if (this.detectInfo.splitDemand === '拆分') {
              let pathMsg = ''
              let hasEmpty = this.splitSampleTableData.some(v => (v.index1Index === '' && v.index2Index === ''))
              if (hasEmpty) pathMsg = '需要数据拆分则需要填写“I7序列”或“I5序列”'
              if (msg && pathMsg) {
                msg += `<br>${pathMsg}`
              } else {
                msg += pathMsg
              }
            }
            /**
             * “index位数”字段必须相同，否则提示：包芯上机时Index位数必须相同
             * */
            if (this.splitSampleTableData.length > 1) {
              let pathMsg = ''
              let firstIndexDigits = this.splitSampleTableData[0].indexDigits
              let hasSame = this.splitSampleTableData.some(v => v.indexDigits !== firstIndexDigits)
              if (hasSame) {
                pathMsg = '包芯上机时Index位数必须相同'
              }
              if (msg && pathMsg) {
                msg += `<br>${pathMsg}`
              } else {
                msg += pathMsg
              }
            }
            if (msg) {
              this.$alert(msg, '提示', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true
              })
              let m = '' // 单纯为了不让eslint 报错 reject
              reject(m)
              return
            }
          } else if (this.detectInfo.applicationType === '客户自建文库散样上机') {
            /**
             *  “拆分需求”字段为“拆分”，则文库信息中“index1序列”字段不能为空
             *  未填写则提示：需要数据拆分则需要填写“index1序列”
             * **/
            if (this.detectInfo.splitDemand === '拆分') {
              let pathMsg = ''
              let hasEmpty = this.splitSampleTableData.some(v => (v.index1Index === ''))
              if (hasEmpty) pathMsg = '需要数据拆分则需要填写“index1序列”'
              if (msg && pathMsg) {
                msg += `<br>${pathMsg}`
              } else {
                msg += pathMsg
              }
            }

            if (msg) {
              this.$alert(msg, '提示', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true
              })
              let m = '' // 单纯为了不让eslint 报错 reject
              reject(m)
              return
            }
          }
          resolve()
        })
      })
    },
    // 根据关键字获取选中的表格参数，因为选中的方法是一样的，只有字段和ref不同
    getTableKey (key) {
      return {
        ref: key === 'table' ? 'table' : 'splitSampleTable',
        selectRowMap: key === 'table' ? this.selectedRows : this.splitSelectedRows
      }
    },
    // 点击行
    handleRowClick (row, c, e, tableName) {
      let {ref, selectRowMap} = this.getTableKey(tableName)
      this.$refs[ref].toggleRowSelection(row, !selectRowMap.has(row.key))
      this.handleSelect(undefined, row, tableName)
    },
    // 选中行
    handleSelect (selection, row, tableName) {
      let {selectRowMap} = this.getTableKey(tableName)
      selectRowMap.has(row.key) ? selectRowMap.delete(row.key) : selectRowMap.set(row.key, row)
    },
    // 全选
    handleSelectAll (selection, tableName) {
      let {selectRowMap} = this.getTableKey(tableName)
      selectRowMap.clear()
      selection.forEach((row) => {
        selectRowMap.set(row.key, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-radio{
    margin: 10px;
  }
  .tips {
    max-height: 240px;
    background: #efefef;
    padding: 20px 40px !important;
    overflow: auto;
  }
  .el-table {
    /deep/ th {
      padding: 0;
      height: 30px;
    }
    /deep/ td {
      padding: 0;
      height: 30px;
    }
  }
  //.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>/deep/ .el-form-item__label:before,
  //.el-form-item.is-required:not(.is-no-asterisk)>/deep/ .el-form-item__label:before{
  //  content: none !important;
  //}
  .el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item {
    margin-bottom: 5px;
  }
  .childTableBg{
    background: red;
  }
  .title{
    margin-right: 30px;
    font-size: 20px;
    font-weight: 600;
  }
  .form-width{
    width: 250px;
  }
  .module{
    background: #fff;
    margin: 0;
    .module-title-bar{
      @extend .operateBar;
      height: 40px;
      .min-title{
        @extend .title;
        font-size: 16px;
      }
    }
    .content{
      padding: 0 20px;
      .p{
        font-size: 14px;
        padding: 2px 0;
      }
      .bold{
        font-weight: bold;
      }
      .text-indent{
        text-indent: 2em;
      }
    }
  }
  // 文库结构的样式
  .structure-container{
    padding: 10px;
    font-size: 13px;
    .bg-gray{
      background: #D7D7D7;
    }
    .bg-yellow{
      background: #f59a23;
    }
    .bg-blue{
      background: #1890ff;
    }
    .nav-bar > span{
      margin-right: 20px;
    }
    .index-content{
      margin-top: 10px;
      .row-item{
        display: flex;
        .index{
          display: flex;
          color: #333;
          max-width: 100%;
          overflow-x: auto;
          margin-right: 10px;
          .main-index{
            display: flex;
            margin: 0 10px;
            @extend .bg-gray;
            p{
              padding: 0 8px;
              .index-num{
                padding: 0 3px;
                color: red;
                border-bottom: 1px solid #333;
              }
            }
          }
        }
      }
    }
  }
  .page {
    .left-one {
      ~ .left-one {
        position: absolute;
        height: 9%;
        width: 11%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      box-sizing: border-box;
      font-size: 26px;
      top: 12%;
      left: 6%;
      padding:  0 0 1px 2px;
    }
    .right-one {
      ~ .right-one {
        box-sizing: border-box;
      }
    }
  }
</style>
