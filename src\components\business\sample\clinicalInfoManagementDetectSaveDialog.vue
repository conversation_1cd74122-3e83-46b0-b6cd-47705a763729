<template>
  <div>
    <el-dialog
      :title="title" :modal="false"
      :visible.sync="visible"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="55%"
      @open="handleOpen"
    >
      <div style="max-height: 400px; overflow-y: auto;overflow-x: hidden;">
        <el-form ref="form" :model="form" :rules="rules" label-width="90px" size="mini" label-suffix=":">
          <template v-if="type === 'immuneHistos'">
            <el-row :gutter="10">
             <el-col :span="12">
               <el-form-item label="检测日期">
                 <my-date-picker v-model="form.detectTime"></my-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="病灶部位" prop="focusPosition">
                 <el-input v-model.trim="form.focusPosition" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
              <template v-for="(item, index) in form.projects">
                <div :key="'project' + index">
                  <el-col :span="8">
                    <el-form-item label="检测项目">
                      <el-select v-model="item.project" filterable clearable placeholder="请选择" class="selectWidth">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in projectList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="检测结果">
                      <el-input v-model.trim="item.result" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" style="height: 51px;">
                    <el-form-item label-width="0px">
                      <el-button v-if="index === form.projects.length - 1" type="primary" icon="el-icon-plus" circle size="mini" @click="handleAdd('immuneHistos')"></el-button>
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="handleDelete(index, 'immuneHistos')"></el-button>
                    </el-form-item>
                  </el-col>
                </div>
              </template>
            </el-row>
          </template>
          <template v-else-if="type === 'geneTest'">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="检测日期">
                  <my-date-picker v-model="form.detectTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检测方法" prop="detectMethod">
                  <el-input v-model.trim="form.detectMethod" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <template v-for="(item, index) in form.detail">
                <div :key="'project' + index">
                  <el-col :span="8">
                    <el-form-item :prop="'detail.' + index + '.gene'" :rules="geneRules" label="检测基因">
                      <el-input v-model.trim="item.gene" autocomplete="on" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :prop="'detail.' + index + '.result'" :rules="form.detail.length >= 2 ? resultRules : {}" label="检测结果">
                      <el-input v-model.trim="item.result" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" style="height: 51px;">
                    <el-form-item label-width="0px">
                      <el-button v-if="index === form.detail.length - 1" type="primary" icon="el-icon-plus" circle size="mini" @click="handleAdd('geneTest')"></el-button>
                      <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="handleDelete(index, 'geneTest')"></el-button>
                    </el-form-item>
                  </el-col>
                </div>
              </template>
            </el-row>
          </template>
          <template v-else-if="type === 'iconography'">
            <el-tabs v-model="form.targetSpot">
              <el-tab-pane label="靶病灶" name="0"></el-tab-pane>
              <el-tab-pane label="非靶病灶" name="1"></el-tab-pane>
              <el-tab-pane label="专项检查" name="2"></el-tab-pane>
            </el-tabs>
            <template v-if="form.targetSpot === '0'">
              <el-row :gutter="10">
               <el-col :span="8">
                 <el-form-item label="检查日期">
                   <my-date-picker v-model="form.checkTime"></my-date-picker>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item label="所在器官" prop="checkPostion">
                   <el-select v-model="form.checkPostion" placeholder="请选择" class="selectWidth">
                     <el-option
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       v-for="item in positionList">
                     </el-option>
                   </el-select>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item v-if="form.checkPostion === '其他'" label="其他所在器官" label-width="110px" prop="checkPostion1">
                   <el-input v-model.trim="form.checkPostion1" placeholder="请输入" maxlength="15"></el-input>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item label="检测方法" prop="checkProject">
                   <el-select v-model="form.checkProject" placeholder="请选择" class="selectWidth">
                     <el-option
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       v-for="item in checkProjectList">
                     </el-option>
                   </el-select>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item v-if="form.checkProject === '其他'" label="其他检测方法" label-width="110px" prop="otherProject">
                   <el-input v-model.trim="form.otherProject" placeholder="请输入" maxlength="50"></el-input>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item label="最长直径(LD)" label-width="110px">
                   <el-input v-model.trim="form.targetSpotLength" placeholder="请输入" @blur="handleTargetSpotLengthBlur">
                     <template slot="append">mm</template>
                   </el-input>
                 </el-form-item>
               </el-col>
              </el-row>
            </template>
            <template v-else-if="form.targetSpot === '1'">
              <el-row :gutter="10">
               <el-col :span="8">
                 <el-form-item label="检查日期">
                   <my-date-picker v-model="form.checkTime"></my-date-picker>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item label="所在器官" prop="checkPostion">
                   <el-select v-model="form.checkPostion" placeholder="请选择" class="selectWidth">
                     <el-option
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       v-for="item in positionList">
                     </el-option>
                   </el-select>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item v-if="form.checkPostion === '其他'" label="其他所在器官" label-width="110px" prop="checkPostion1">
                   <el-input v-model.trim="form.checkPostion1" placeholder="请选择" maxlength="15" class="selectWidth"></el-input>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item label="检测方法" prop="checkProject">
                   <el-select v-model="form.checkProject" placeholder="请选择" class="selectWidth">
                     <el-option
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       v-for="item in checkProjectList">
                     </el-option>
                   </el-select>
                 </el-form-item>
               </el-col>
               <el-col :span="8">
                 <el-form-item v-if="form.checkProject === '其他'" label="其他检测方法" label-width="110px" prop="otherProject">
                   <el-input v-model.trim="form.otherProject" placeholder="请输入" maxlength="50"></el-input>
                 </el-form-item>
               </el-col>
               <el-col :span="24">
                 <el-form-item label="描述">
                   <el-radio-group v-model="form.targetSpotDes">
                     <el-radio label="新发">新发</el-radio>
                     <el-radio label="缩小">缩小</el-radio>
                     <el-radio label="增大">增大</el-radio>
                     <el-radio label="消失">消失</el-radio>
                     <el-radio label="同前">同前</el-radio>
                   </el-radio-group>
                 </el-form-item>
               </el-col>
              </el-row>
            </template>
            <template v-else-if="form.targetSpot === '2'">
              <template v-for="(item, index) in form.lesionPos">
                <div :key="'specialIcongraphy' + index">
                  <el-row :gutter="10">
                   <el-col :span="8" class="colHeight">
                     <el-form-item label="检查部位">
                       <el-select v-model="item.checkArea" placeholder="请选择" class="selectWidth" @change="value => handleCheckAreaChange(value, item)">
                         <el-option
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"
                           v-for="item in checkAreaList">
                         </el-option>
                       </el-select>
                     </el-form-item>
                   </el-col>
                   <el-col :span="12" class="colHeight">
                     <el-form-item label="检测时间">
                       <my-date-picker v-model="item.checkTime"></my-date-picker>
                     </el-form-item>
                   </el-col>
                   <el-col :span="4" class="colHeight">
                     <el-form-item label-width="0px">
                       <el-button v-if="index === form.lesionPos.length - 1" type="primary" size="mini" icon="el-icon-plus" circle @click="handleAdd('iconography')"></el-button>
                       <el-button type="danger" icon="el-icon-delete" size="mini" circle @click="handleDelete(index, 'iconography')"></el-button>
                     </el-form-item>
                   </el-col>
                    <template v-if="item.checkArea === '肺部'">
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.checkProject" placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in lungProjectList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测结果">
                          <el-select v-model="item.checkResult" placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in lungResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <template v-if="item.checkResult === '结节'">
                        <el-col :span="24">
                          <div style="padding: 5px;">外观特征</div>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="个数">
                            <el-input v-model.number="item.num" maxlength="30" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="突出病灶">
                            <el-select :disabled="item.num < 2"  v-model="item.protrudingLesions" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in trueOrFalseList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="大小">
                            <el-input v-model.trim="item.size" maxlength="30" placeholder="请输入">
                              <template slot="append">mm</template>
                            </el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="位置">
                            <el-select v-model="item.location" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in locationList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="形态">
                            <el-select v-model="item.form" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in formList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="边缘">
                            <el-select v-model="item.margin" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in marginList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="结节-肺界面" label-width="110px">
                            <el-select v-model="item.nodeLung" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in nodeLungList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" class="colHeight">
                          <el-form-item label="密度">
                            <el-select v-model="item.density" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in densityList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="item.density === '亚实性'" class="colHeight">
                          <el-form-item>
                            <el-select v-model="item.densityValue" placeholder="请选择" class="selectWidth">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in densityValueList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="item.density === '亚实性' && item.densityValue === '混合磨玻璃'" class="colHeight">
                          <el-form-item label="实性成分">
                            <el-input v-model.trim="item.element" placeholder="请输入">
                              <template slot="append">%</template>
                            </el-input>
                          </el-form-item>
                        </el-col>
                      </template>
                    </template>
                    <template v-else-if="item.checkArea === '乳腺'">
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.checkProject" clearable placeholder="请选择" class="selectWidth">
                            <el-option label="超声检查" value="超声检查"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="16" class="colHeight">
                        <el-form-item label="检测结果">
                          BI-RADS分级
                          <el-select v-model="item.checkResult" clearable placeholder="请选择" style="width: 120px;">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in breastResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.otherProject" clearable placeholder="请选择" class="selectWidth">
                            <el-option label="钼靶检查" value="钼靶检查"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="16" class="colHeight">
                        <el-form-item label="检查结果">
                          BI-RADS分级
                          <el-select v-model="item.checkOtherResult" clearable placeholder="请选择" style="width: 120px;">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in breastResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </template>
                    <template v-else-if="item.checkArea === '胃部'">
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.checkProject" clearable placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in stomachProjectList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测结果">
                          <el-select v-model="item.checkResult" clearable placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in stomachResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </template>
                    <template v-else-if="item.checkArea === '肠部'">
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.checkProject" clearable placeholder="请选择" class="selectWidth" @change="item.checkResult = ''">
                            <el-option label="粪便潜血" value="粪便潜血"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测结果">
                          <el-select v-model="item.checkResult" :disabled="item.checkProject === ''" clearable placeholder="请选择" class="selectWidth">
                            <el-option label="弱阳性" value="弱阳性"></el-option>
                            <el-option label="阳性" value="阳性"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight"></el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.otherProject" clearable placeholder="请选择" class="selectWidth" @change="item.checkOtherResult = ''">
                            <el-option label="肠镜检查" value="肠镜检查"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测结果">
                          <el-select v-model="item.checkOtherResult" :disabled="item.otherProject === ''" clearable placeholder="请选择" class="selectWidth" @change="item.diseaseTail = ''">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in colonoscopyResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item v-if="item.checkOtherResult === '肿瘤性息肉及炎性肠病'" label="" label-width="20px">
                          <el-select v-model="item.diseaseTail" clearable placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in neoplasticPolypsResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </template>
                    <template v-else-if="item.checkArea === '肝部'">
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测项目">
                          <el-select v-model="item.checkProject" clearable placeholder="请选择" class="selectWidth" @change="item.checkResult = ''">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in liverProjectList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="检测结果">
                          <el-select v-model="item.checkResult" :disabled="item.checkProject === ''" clearable placeholder="请选择" class="selectWidth" @change="item.diseaseTail = ''">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in liverResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8" class="colHeight">
                        <el-form-item label="" label-width="20px">
                          <el-select v-model="item.diseaseTail" clearable placeholder="请选择" class="selectWidth">
                            <template v-if="item.checkResult === '不典型病灶'">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in atypicalLesionsList">
                              </el-option>
                            </template>
                            <template v-else-if="item.checkResult === '占位'">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in placeholderList">
                              </el-option>
                            </template>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </template>
                  </el-row>
                </div>
              </template>
            </template>
          </template>
          <template v-else-if="type === 'cancerTag'">
            <el-row :gutter="10">
             <el-col :span="12">
               <el-form-item label="检测时间" label-width="80px">
                 <my-date-picker v-model="form.checkTime"></my-date-picker>
               </el-form-item>
             </el-col>
            </el-row>
            <template v-for="(item, index) in form.details">
              <div :key="'details' + index">
                <el-row :gutter="10">
                  <el-col :span="21">
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <el-form-item label="指标" label-width="80px">
                          <el-select v-model="item.cancerIndex" clearable filterable allow-create placeholder="请选择" class="selectWidth" @change="value => handleCancerIndexChange(value, index)">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in tagNameList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="5">
                        <el-form-item label="指标值" label-width="80px">
                          <el-select v-model="item.detectIndexValueSymbol" clearable placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in checkResultList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="3">
                        <el-form-item :prop="'details.' + index + '.cancerIndexValue'" :rules="rules.cancerIndexValue" label-width="0">
                          <el-input v-model="item.cancerIndexValue" placeholder="请输入" @blur="handleCancerIndexValueBlur(index)"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="单位" label-width="80px">
                          <el-select v-model="item.unitV" clearable filterable allow-create placeholder="请选择">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in unitList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="正常范围" label-width="80px">
                          <el-input v-model.trim="item.cancerIndexValueScope" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="临床判断" label-width="80px">
                          <el-select v-model="item.clinicalJudgement" clearable placeholder="请选择" class="selectWidth">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in clinicalJudgementList">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="3" style="height: 51px;">
                    <el-form-item label-width="0px">
                      <el-button v-if="index === form.details.length - 1" type="primary" size="mini" icon="el-icon-plus"
                                 circle @click="handleAdd('cancerTag')"></el-button>
                      <el-button type="danger" size="mini" icon="el-icon-delete" circle
                                 @click="handleDelete(index, 'cancerTag')"></el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>
          </template>
          <template v-else-if="type === 'biocheExamTag'">
            <el-row :gutter="10">
             <el-col :span="8">
               <el-form-item label="检测日期" label-width="80px">
                 <my-date-picker v-model="form.detectTime"></my-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="8">
               <el-form-item label="检测方法" label-width="80px">
                 <el-input v-model.trim="form.detectMethod" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
            </el-row>
            <template v-for="(item, index) in form.biocheDtl">
              <div :key="'biocheDtl' + index">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-form-item label="指标" label-width="80px">
                      <el-select v-model="item.detectIndex" clearable filterable allow-create placeholder="请选择" class="selectWidth">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in detectIndexList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="指标值" label-width="80px">
                      <el-input v-model.trim="item.detectIndexValue" placeholder="请输入" @blur="handleDetectIndexValueBlur('biocheExamTag', index)"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item label="单位" label-width="80px">
                      <el-select v-model="item.detectUnit" clearable filterable allow-create placeholder="请选择" class="selectWidth">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in detectUnitList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="3">
                    <el-form-item label-width="0px">
                      <el-button v-if="index === form.biocheDtl.length - 1" type="primary" size="mini" icon="el-icon-plus"
                                 circle @click="handleAdd('biocheExamTag')"></el-button>
                      <el-button type="danger" size="mini" icon="el-icon-delete" circle
                                 @click="handleDelete(index, 'biocheExamTag')"></el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>
          </template>
          <template v-else-if="type === 'sexhormoneTag'">
            <el-row :gutter="10">
             <el-col :span="8">
               <el-form-item label="检测日期">
                 <my-date-picker v-model="form.detectTime"></my-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="8">
               <el-form-item label="检测方法">
                 <el-input v-model.trim="form.detectMethod" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
            </el-row>
            <template v-for="(item, index) in form.sexhorDtl">
              <div :key="'sexhorDtl' + index">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-form-item label="指标">
                      <el-select v-model="item.detectIndex" clearable filterable allow-create placeholder="请选择" class="selectWidth">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in sexhorDtlDetectIndexList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="指标值">
                      <el-input v-model.trim="item.detectIndexValue" placeholder="请输入" @blur="handleDetectIndexValueBlur('sexhormoneTag', index)"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item label="单位">
                      <el-select v-model="item.detectUnit" clearable filterable allow-create placeholder="请选择" class="selectWidth">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in sexhorDtlDetectUnitList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="3" style="height: 51px;">
                    <el-form-item label-width="0px">
                      <el-button v-if="index === form.sexhorDtl.length - 1" type="primary" size="mini" icon="el-icon-plus"
                                 circle @click="handleAdd('sexhormoneTag')"></el-button>
                      <el-button type="danger" size="mini" icon="el-icon-delete" circle
                                 @click="handleDelete(index, 'sexhormoneTag')"></el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>
          </template>
          <template v-else></template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
export default {
  name: 'clinicalInfoManagementDetectSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    myDatePicker
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      title: '',
      type: '',
      form: {},
      rules: {
        checkPostion: [
          {required: true, message: '请选择靶病灶所在器官', trigger: ['blur', 'change']}
        ],
        checkPostion1: [
          {required: true, message: '请输入其他所在器官', trigger: 'blur'}
        ],
        checkProject: [
          {required: true, message: '请选择检测方法', trigger: ['blur', 'change']}
        ],
        detectMethod: [
          {required: true, message: '请选择检测方法', trigger: 'blur'}
        ],
        otherProject: [
          {required: true, message: '请输入其他检测方法', trigger: 'blur'}
        ],
        focusPosition: [
          {required: true, message: '请填写病灶部位', trigger: 'blur'}
        ],
        cancerIndexValue: [
          {required: true, message: '请填写指标值', trigger: 'blur'},
          {pattern: /^[0-9]+.?[0-9]*$/, message: '请填写正确格式', trigger: 'blur'}
        ]
      },
      geneRules: [
        {required: true, message: '请选择检出基因', trigger: ['blur', 'change']}
      ],
      resultRules: [
        {required: true, message: '请填写检测结果', trigger: 'blur'}
      ],
      trueOrFalseList: [
        {
          value: '否',
          label: '否'
        },
        {
          value: '是',
          label: '是'
        }
      ],
      projectList: [
        { label: 'ALK', value: 'ALK' },
        { label: 'ATRX', value: 'ATRX' },
        { label: 'BCL2', value: 'BCL2' },
        { label: 'BCL6', value: 'BCL6' },
        { label: 'BCOR', value: 'BCOR' },
        { label: 'BRAF', value: 'BRAF' },
        { label: 'BRCA1', value: 'BRCA1' },
        { label: 'BRCA2', value: 'BRCA2' },
        { label: 'BTK', value: 'BTK' },
        { label: 'CD10', value: 'CD10' },
        { label: 'CD117，BRAF', value: 'CD117，BRAF' },
        { label: 'CD20', value: 'CD20' },
        { label: 'CD3', value: 'CD3' },
        { label: 'CD30', value: 'CD30' },
        { label: 'CD5', value: 'CD5' },
        { label: 'CIC', value: 'CIC' },
        { label: 'c-KIT', value: 'c-KIT' },
        { label: 'c-MET', value: 'c-MET' },
        { label: 'EBER-ISH', value: 'EBER-ISH' },
        { label: 'EGFR', value: 'EGFR' },
        { label: 'ER', value: 'ER' },
        { label: 'EWSR1', value: 'EWSR1' },
        { label: 'FGFR2', value: 'FGFR2' },
        { label: 'H3F3A', value: 'H3F3A' },
        { label: 'HER2', value: 'HER2' },
        { label: 'HIST1H3B', value: 'HIST1H3B' },
        { label: 'IDH1/2', value: 'IDH1/2' },
        { label: 'KRAS', value: 'KRAS' },
        { label: 'Ki67', value: 'Ki67' },
        { label: 'MGMT', value: 'MGMT' },
        { label: 'MLH1', value: 'MLH1' },
        { label: 'MSH2', value: 'MSH2' },
        { label: 'MSH6', value: 'MSH6' },
        { label: 'MSI', value: 'MSI' },
        { label: 'MUM1/IRF4', value: 'MUM1/IRF4' },
        { label: 'MYC', value: 'MYC' },
        { label: 'MYCN', value: 'MYCN' },
        { label: 'NF1', value: 'NF1' },
        { label: 'NRAS', value: 'NRAS' },
        { label: 'NTRK', value: 'NTRK' },
        { label: 'PD-1', value: 'PD-1' },
        { label: 'PD-L1', value: 'PD-L1' },
        { label: 'PDGFRA', value: 'PDGFRA' },
        { label: 'PIK3CA', value: 'PIK3CA' },
        { label: 'PMS2', value: 'PMS2' },
        { label: 'POLE/D', value: 'POLE/D' },
        { label: 'PR', value: 'PR' },
        { label: 'RELA', value: 'RELA' },
        { label: 'RET', value: 'RET' },
        { label: 'ROS1', value: 'ROS1' },
        { label: 'TERT', value: 'TERT' },
        { label: 'TFE3', value: 'TFE3' }
      ],
      positionList: [
        {
          value: '头部',
          label: '头部'
        },
        {
          value: '肺',
          label: '肺'
        },
        {
          value: '横膈',
          label: '横膈'
        },
        {
          value: '心脏',
          label: '心脏'
        },
        {
          value: '淋巴结',
          label: '淋巴结'
        },
        {
          value: '胸膜',
          label: '胸膜'
        },
        {
          value: '肝脏',
          label: '肝脏'
        },
        {
          value: '骨骼',
          label: '骨骼'
        },
        {
          value: '肾脏',
          label: '肾脏'
        },
        {
          value: '肾上腺',
          label: '肾上腺'
        },
        {
          value: '胃肠道',
          label: '胃肠道'
        },
        {
          value: '纵膈',
          label: '纵膈'
        },
        {
          value: '软组织',
          label: '软组织'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      checkProjectList: [
        {
          value: 'CT',
          label: 'CT'
        },
        {
          value: 'MRI',
          label: 'MRI'
        },
        {
          value: '超声',
          label: '超声'
        },
        {
          value: 'ECT',
          label: 'ECT'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      checkAreaList: [
        {
          value: '肺部',
          label: '肺部'
        },
        {
          value: '乳腺',
          label: '乳腺'
        },
        {
          value: '胃部',
          label: '胃部'
        },
        {
          value: '肠部',
          label: '肠部'
        },
        {
          value: '肝部',
          label: '肝部'
        }
      ],
      lungProjectList: [
        {
          value: 'CT检查',
          label: 'CT检查'
        }
      ],
      lungResultList: [
        {
          value: '结节',
          label: '结节'
        },
        {
          value: '肺肿物',
          label: '肺肿物'
        }
      ],
      breastProjectList: [
        {
          value: '超声检查',
          label: '超声检查'
        },
        {
          value: '钼靶检查',
          label: '钼靶检查'
        }
      ],
      breastResultList: [
        {
          value: '1级',
          label: '1级'
        },
        {
          value: '2级',
          label: '2级'
        },
        {
          value: '3级',
          label: '3级'
        },
        {
          value: '4级',
          label: '4级'
        },
        {
          value: '5级',
          label: '5级'
        }
      ],
      stomachProjectList: [
        {
          value: '胃镜检查',
          label: '胃镜检查'
        }
      ],
      stomachResultList: [
        {
          value: '轻度和中度不典型增生',
          label: '轻度和中度不典型增生'
        },
        {
          value: '高级别上皮内肿瘤',
          label: '高级别上皮内肿瘤'
        },
        {
          value: '早期胃癌',
          label: '早期胃癌'
        }
      ],
      intestinalProjectList: [
        {
          value: '肠镜检查',
          label: '肠镜检查'
        },
        {
          value: '粪便潜血',
          label: '粪便潜血'
        }
      ],
      intestinal1ResultList: [],
      intestinal2ResultList: [],
      fecalOccultBloodResultList: [
        {
          value: '弱阳性',
          label: '弱阳性'
        },
        {
          value: '阳性',
          label: '阳性'
        }
      ],
      colonoscopyResultList: [
        {
          value: '非肿瘤性息肉',
          label: '非肿瘤性息肉'
        },
        {
          value: '肿瘤性息肉及炎性肠病',
          label: '肿瘤性息肉及炎性肠病'
        },
        {
          value: '结直肠癌',
          label: '结直肠癌'
        }
      ],
      neoplasticPolypsResultList: [
        {
          value: '直径>=1cm的腺瘤',
          label: '直径>=1cm的腺瘤'
        },
        {
          value: '绒毛结构>=25%的腺瘤',
          label: '绒毛结构>=25%的腺瘤'
        },
        {
          value: '伴高级别上皮内肿瘤的其他病变',
          label: '伴高级别上皮内肿瘤的其他病变'
        },
        {
          value: '其他腺瘤',
          label: '其他腺瘤'
        },
        {
          value: '炎症性肠病（如溃疡性结肠炎、克罗恩病）',
          label: '炎症性肠病（如溃疡性结肠炎、克罗恩病）'
        }
      ],
      liverProjectList: [
        {
          value: '超声检查',
          label: '超声检查'
        }
      ],
      liverResultList: [
        {
          value: '不典型病灶',
          label: '不典型病灶'
        },
        {
          value: '占位',
          label: '占位'
        }
      ],
      atypicalLesionsList: [
        {
          label: '肝硬化结节',
          value: '肝硬化结节'
        },
        {
          label: '肝腺瘤',
          value: '肝腺瘤'
        },
        {
          label: '不典型增生结节',
          value: '不典型增生结节'
        },
        {
          label: '其他',
          value: '其他'
        }
      ],
      placeholderList: [
        {
          label: '<=1cm',
          value: '<=1cm'
        },
        {
          label: '>1cm',
          value: '>1cm'
        },
        {
          label: '>2cm',
          value: '>2cm'
        }
      ],
      locationList: [
        {
          value: '左肺上叶',
          label: '左肺上叶'
        },
        {
          value: '左肺下叶',
          label: '左肺下叶'
        },
        {
          value: '右肺上叶',
          label: '右肺上叶'
        },
        {
          value: '右肺中叶',
          label: '右肺中叶'
        },
        {
          value: '右肺下叶',
          label: '右肺下叶'
        }
      ],
      formList: [
        {
          value: '圆形',
          label: '圆形'
        },
        {
          value: '类圆形',
          label: '类圆形'
        },
        {
          value: '不规则',
          label: '不规则'
        }
      ],
      marginList: [
        {
          value: '分叶状',
          label: '分叶状'
        },
        {
          value: '毛刺征（棘状突起）',
          label: '毛刺征（棘状突起）'
        },
        {
          value: '胸膜凹陷征',
          label: '胸膜凹陷征'
        },
        {
          value: '血管集束症',
          label: '血管集束症'
        }
      ],
      nodeLungList: [
        {
          value: '光整',
          label: '光整'
        },
        {
          value: '毛糙',
          label: '毛糙'
        },
        {
          value: '清楚',
          label: '清楚'
        },
        {
          value: '模糊',
          label: '模糊'
        }
      ],
      densityValueList: [
        {
          value: '纯磨玻璃',
          label: '纯磨玻璃'
        },
        {
          value: '混合磨玻璃',
          label: '混合磨玻璃'
        }
      ],
      densityList: [
        {
          value: '钙化',
          label: '钙化'
        },
        {
          value: '实性',
          label: '实性'
        },
        {
          value: '亚实性',
          label: '亚实性'
        }
      ],
      tagNameList: [
        {
          value: 'AFP',
          label: 'AFP'
        },
        {
          value: 'CA125',
          label: 'CA125'
        },
        {
          value: 'CA15-3',
          label: 'CA15-3'
        },
        {
          value: 'CA19-9',
          label: 'CA19-9'
        },
        {
          value: 'CA242',
          label: 'CA242'
        },
        {
          value: 'CA72-4',
          label: 'CA72-4'
        },
        {
          value: 'CEA',
          label: 'CEA'
        },
        {
          value: 'Cyfra21-1',
          label: 'Cyfra21-1'
        },
        {
          value: 'HE4',
          label: 'HE4'
        },
        {
          value: 'NSE',
          label: 'NSE'
        },
        {
          value: 'PSA',
          label: 'PSA'
        },
        {
          value: 'SCCA',
          label: 'SCCA'
        },
        {
          value: 'CA50',
          label: 'CA50'
        },
        {
          value: 'fPSA',
          label: 'fPSA'
        },
        {
          value: 'ProGRP',
          label: 'ProGRP'
        },
        {
          value: 'SF',
          label: 'SF'
        },
        {
          value: 'tPSA',
          label: 'tPSA'
        },
        {
          value: 'AFP-L3',
          label: 'AFP-L3'
        },
        {
          value: 'PIVKA II或DCP',
          label: 'PIVKA II或DCP'
        }
      ],
      checkResultList: [
        {
          label: '>',
          value: '>'
        },
        {
          label: '=',
          value: '='
        },
        {
          label: '<',
          value: '<'
        }
      ],
      unitList: [
        {
          label: 'ng/ml',
          value: 'ng/ml'
        },
        {
          label: 'U/ml',
          value: 'U/ml'
        },
        {
          label: 'mg/L',
          value: 'mg/L'
        }
      ],
      clinicalJudgementList: [
        {
          label: '正常',
          value: 3
        },
        {
          label: '异常无临床意义',
          value: 1
        },
        {
          label: '异常且有临床意义',
          value: 2
        }
      ],
      detectIndexList: [
        {
          value: 'RBC',
          label: 'RBC'
        },
        {
          value: 'HCT',
          label: 'HCT'
        },
        {
          value: 'MCV',
          label: 'MCV'
        },
        {
          value: 'HGB或Hb',
          label: 'HGB或Hb'
        },
        {
          value: 'WBC',
          label: 'WBC'
        },
        {
          value: 'NEUT',
          label: 'NEUT'
        },
        {
          value: 'LYMPH/LY',
          label: 'LYMPH/LY'
        },
        {
          value: 'PLT',
          label: 'PLT'
        },
        {
          value: 'PCT',
          label: 'PCT'
        },
        {
          value: 'MONO',
          label: 'MONO'
        },
        {
          value: 'ANC',
          label: 'ANC'
        },
        {
          value: 'ALT',
          label: 'ALT'
        },
        {
          value: 'AST',
          label: 'AST'
        },
        {
          value: 'TP',
          label: 'TP'
        },
        {
          value: 'ALB',
          label: 'ALB'
        },
        {
          value: 'ALP',
          label: 'ALP'
        },
        {
          value: 'BUN',
          label: 'BUN'
        },
        {
          value: 'Tbil',
          label: 'Tbil'
        },
        {
          value: 'Crea',
          label: 'Crea'
        },
        {
          value: 'GGT',
          label: 'GGT'
        },
        {
          value: 'CPK',
          label: 'CPK'
        },
        {
          value: 'UREA',
          label: 'UREA'
        },
        {
          value: 'LDH',
          label: 'LDH'
        }
      ],
      detectUnitList: [
        {
          value: '10^12/L',
          label: '10^12/L'
        },
        {
          value: '%',
          label: '%'
        },
        {
          value: 'fL',
          label: 'fL'
        },
        {
          value: 'g/L',
          label: 'g/L'
        },
        {
          value: 'pg',
          label: 'pg'
        },
        {
          value: '10^9/L',
          label: '10^9/L'
        },
        {
          value: 'U/L',
          label: 'U/L'
        },
        {
          value: 'umol/L',
          label: 'umol/L'
        },
        {
          value: 'mmol/L',
          label: 'mmol/L'
        },
        {
          value: 'IU/L',
          label: 'IU/L'
        },
        {
          value: 'U',
          label: 'U'
        },
        {
          value: 'pg/ml',
          label: 'pg/ml'
        }
      ],
      sexhorDtlDetectIndexList: [
        {
          value: 'E2',
          label: 'E2'
        },
        {
          value: 'P',
          label: 'P'
        },
        {
          value: 'FSH',
          label: 'FSH'
        },
        {
          value: 'LH',
          label: 'LH'
        },
        {
          value: 'T',
          label: 'T'
        },
        {
          value: 'PRL',
          label: 'PRL'
        }
      ],
      sexhorDtlDetectUnitList: [
        {
          value: 'pg/ml',
          label: 'pg/ml'
        },
        {
          value: 'ng/ml',
          label: 'ng/ml'
        },
        {
          value: 'IU/L',
          label: 'IU/L'
        },
        {
          value: 'U/L',
          label: 'U/L'
        }
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.type = this.pdata.type
        this.sampleBasicId = this.pdata.sampleBasicId
        switch (this.pdata.type) {
          case 'immuneHistos':
            this.title = '免疫组化结果'
            this.form = {
              immuneHistoId: null,
              sampleBasicId: null,
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectTime: '',
              focusPosition: '',
              prPercent: '',
              prValue: '',
              testProject: '',
              hre2Value: '',
              fishResult: '',
              fupdateTime: '',
              projects: [
                {
                  project: '',
                  result: ''
                }
              ]
            }
            break
          case 'geneTest':
            this.title = '基因检测'
            this.form = {
              preGeneDetectionId: null,
              sampleBasicId: null,
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectTime: '',
              detectMethod: '',
              sourceClinicals: '',
              fupdateTime: '',
              pregenes: [],
              detail: [
                {
                  gene: '',
                  result: ''
                }
              ]
            }
            break
          case 'iconography':
            this.title = '影像检测'
            this.form = {
              iconographyId: null,
              sampleBasicId: null,
              checkYear: '',
              checkMonth: '',
              checkDay: '',
              checkTime: '',
              checkProject: '',
              otherProject: '',
              checkPostion: '',
              checkPostion1: '',
              sourceImgs: '',
              targetSpot: 0,
              targetSpotLength: '',
              targetSpotDes: '',
              checkResult: '',
              checkOtherResult: '',
              lesionPos: [
                {
                  iconographyId: null,
                  sampleBasicId: null,
                  targetSpot: 2,
                  checkArea: '',
                  checkYear: '',
                  checkMonth: '',
                  checkDay: '',
                  checkTime: '',
                  checkProject: '',
                  checkResult: '',
                  otherProject: '',
                  checkOtherResult: '',
                  num: '',
                  protrudingLesions: '',
                  size: '',
                  location: '',
                  form: '',
                  margin: '',
                  nodeLung: '',
                  density: '',
                  densityValue: '',
                  element: '',
                  diseaseTail: ''
                }
              ]
            }
            break
          case 'cancerTag':
            this.title = '肿瘤标记物'
            this.form = {
              sampleBasicId: null,
              checkTime: '',
              details: [
                {
                  cancerTagId: null,
                  sampleBasicId: this.sampleBasicId,
                  cancerIndex: '',
                  cancerIndex1: '',
                  detectIndexValueSymbol: '',
                  cancerIndexValue: '',
                  cancerIndexValueScope: '',
                  unitV: '',
                  unitV1: '',
                  clinicalJudgement: ''
                }
              ]
            }
            break
          case 'biocheExamTag':
            this.title = '生化检查'
            this.form = {
              biocheExamId: null,
              sampleBasicId: null,
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectTime: '',
              detectMethod: '',
              biocheDtl: [
                {
                  biocheDetailId: null,
                  biocheExamId: null,
                  detectIndex: '',
                  detectIndexOther: '',
                  detectIndexValue: '',
                  detectUnit: '',
                  detectUnitOther: ''
                }
              ]
            }
            break
          case 'sexhormoneTag':
            this.title = '性激素'
            this.form = {
              sexhormoneId: null,
              sampleBasicId: null,
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectTime: '',
              detectMethod: '',
              sexhorDtl: [
                {
                  sexhorDetailId: null,
                  sexhormoneId: null,
                  detectIndex: '',
                  detectIndexOther: '',
                  detectIndexValue: '',
                  detectUnit: '',
                  detectUnitOther: ''
                }
              ]
            }
            break
        }
        this.form = Object.assign({}, this.form, this.pdata)
        if (this.type === 'iconography') {
          this.form.targetSpot = JSON.stringify(this.form.targetSpot)
        }
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let url = ''
          let data = {}
          let detectTime = this.form.detectTime ? this.form.detectTime.split('-') : []
          let checkTime = this.form.checkTime ? this.form.checkTime.split('-') : []
          let method = 'post'
          switch (this.type) {
            case 'immuneHistos':
              url = '/sample/clinical/save_immune_histo'
              data = {
                immuneHistoId: this.form.immuneHistoId,
                sampleBasicId: this.form.sampleBasicId,
                detectYear: detectTime[0] || '',
                detectMonth: detectTime[1] || '',
                detectDay: detectTime[2] || '',
                focusPosition: this.form.focusPosition,
                erValue: this.form.erValue,
                erPercent: this.form.erPercent,
                prValue: this.form.erValue,
                prPercent: this.form.projects.map(v => v.result).toString(),
                testProject: this.form.projects.map(v => v.project).toString(),
                hre2Value: this.form.hre2Value,
                fishResult: this.form.fishResult
              }
              break
            case 'geneTest':
              url = '/sample/clinical/save_clinical_predet'
              data = {
                preGeneDetectionId: this.form.preGeneDetectionId,
                sampleBasicId: this.form.sampleBasicId,
                detectYear: detectTime[0] || '',
                detectMonth: detectTime[1] || '',
                detectDay: detectTime[2] || '',
                detectMethod: this.form.detectMethod,
                sourceClinicals: this.form.sourceClinicals,
                detectGene: this.form.detail.map(v => v.gene).toString(),
                detectResult: this.form.detail.map(v => v.result).toString()
              }
              break
            case 'iconography':
              if (this.form.targetSpot === '2') {
                method = 'post'
                url = '/sample/clinical/save_special_icongraphy'
                let clinicalIconographyList = []
                this.form.lesionPos.forEach(v => {
                  checkTime = v.checkTime.split('-')
                  clinicalIconographyList.push({
                    iconographyId: v.iconographyId,
                    sampleBasicId: this.form.sampleBasicId,
                    targetSpot: 2,
                    checkArea: v.checkArea,
                    checkYear: checkTime[0] || '',
                    checkMonth: checkTime[1] || '',
                    checkDay: checkTime[2] || '',
                    checkProject: v.checkProject,
                    checkResult: v.checkResult,
                    otherProject: v.otherProject,
                    checkOtherResult: v.checkOtherResult,
                    num: v.num,
                    protrudingLesions: v.protrudingLesions,
                    size: v.size,
                    location: v.location,
                    form: v.form,
                    margin: v.margin,
                    nodeLung: v.nodeLung,
                    density: v.density,
                    densityValue: v.densityValue,
                    element: v.element,
                    diseaseTail: v.diseaseTail
                  })
                })
                data = {
                  clinicalIconographyList: clinicalIconographyList
                }
              } else {
                url = '/sample/clinical/save_icongraphy'
                data = {
                  iconographyId: this.form.iconographyId,
                  sampleBasicId: this.form.sampleBasicId,
                  checkYear: checkTime[0] || '',
                  checkMonth: checkTime[1] || '',
                  checkDay: checkTime[2] || '',
                  checkProject: this.form.checkProject,
                  otherProject: this.form.otherProject,
                  checkPostion: this.form.checkPostion,
                  checkPostion1: this.form.checkPostion1,
                  sourceImgs: this.form.sourceImgs,
                  targetSpot: this.form.targetSpot,
                  targetSpotLength: this.form.targetSpotLength,
                  targetSpotDes: this.form.targetSpotDes
                }
              }
              break
            case 'cancerTag':
              url = '/sample/clinical/save_cancer_tag'
              let cancertagList = []
              this.form.details.forEach(v => {
                cancertagList.push({
                  cancerTagId: v.cancerTagId,
                  sampleBasicId: v.sampleBasicId,
                  checkYear: checkTime[0] || '',
                  checkMonth: checkTime[1] || '',
                  checkDay: checkTime[2] || '',
                  cancerIndex: v.cancerIndex,
                  cancerIndex1: v.cancerIndex1,
                  detectIndexValueSymbol: v.detectIndexValueSymbol,
                  cancerIndexValueScope: v.cancerIndexValueScope,
                  cancerIndexValue: v.cancerIndexValue,
                  unitV: v.unitV,
                  unitV1: v.unitV1,
                  clinicalJudgement: v.clinicalJudgement
                })
              })
              data = {
                cancertagList: cancertagList,
                sampleBasicId: this.sampleBasicId
              }
              break
            case 'biocheExamTag':
              url = '/sample/clinical/save_bioche_exam'
              data = {
                biocheExamId: this.form.biocheExamId,
                sampleBasicId: this.form.sampleBasicId,
                detectYear: detectTime[0] || '',
                detectMonth: detectTime[1] || '',
                detectDay: detectTime[2] || '',
                detectMethod: this.form.detectMethod,
                biocheDtl: this.form.biocheDtl
              }
              break
            case 'sexhormoneTag':
              url = '/sample/clinical/save_sexhormone'
              data = {
                sexhormoneId: this.form.sexhormoneId,
                sampleBasicId: this.form.sampleBasicId,
                detectYear: detectTime[0] || '',
                detectMonth: detectTime[1] || '',
                detectDay: detectTime[2] || '',
                detectMethod: this.form.detectMethod,
                sexhorDtl: this.form.sexhorDtl
              }
              break
          }
          this.loading = true
          this.$ajax({
            method: method,
            url: url,
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('detectSaveDialogConfirmEvent', this.type)
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleAdd (type) {
      switch (type) {
        case 'immuneHistos':
          this.form.projects.push({
            project: '',
            result: ''
          })
          break
        case 'geneTest':
          this.form.detail.push({
            gene: '',
            result: ''
          })
          break
        case 'iconography':
          this.form.lesionPos.push({
            iconographyId: null,
            sampleBasicId: this.sampleBasicId,
            targetSpot: 2,
            checkArea: '',
            checkYear: '',
            checkMonth: '',
            checkDay: '',
            checkTime: '',
            checkProject: '',
            checkResult: '',
            otherProject: '',
            checkOtherResult: '',
            num: '',
            protrudingLesions: '',
            size: '',
            location: '',
            form: '',
            margin: '',
            nodeLung: '',
            density: '',
            densityValue: '',
            element: '',
            diseaseTail: ''
          })
          break
        case 'cancerTag':
          this.form.details.push({
            cancerTagId: null,
            sampleBasicId: this.sampleBasicId,
            cancerIndex: '',
            cancerIndex1: '',
            detectIndexValueSymbol: '',
            cancerIndexValue: '',
            cancerIndexValueScope: '',
            unitV: '',
            unitV1: '',
            clinicalJudgement: ''
          })
          break
        case 'biocheExamTag':
          this.form.biocheDtl.push({
            biocheDetailId: null,
            biocheExamId: null,
            detectIndex: '',
            detectIndexOther: '',
            detectIndexValue: '',
            detectUnit: '',
            detectUnitOther: ''
          })
          break
        case 'sexhormoneTag':
          this.form.sexhorDtl.push({
            sexhorDetailId: null,
            sexhormoneId: null,
            detectIndex: '',
            detectIndexOther: '',
            detectIndexValue: '',
            detectUnit: '',
            detectUnitOther: ''
          })
          break
      }
    },
    handleDelete (index, type) {
      switch (type) {
        case 'immuneHistos':
          this.form.projects.splice(index, 1)
          if (this.form.projects.length === 0) {
            this.handleAdd(type)
          }
          break
        case 'geneTest':
          this.form.detail.splice(index, 1)
          if (this.form.detail.length === 0) {
            this.handleAdd(type)
          }
          break
        case 'iconography':
          this.form.lesionPos.splice(index, 1)
          if (this.form.lesionPos.length === 0) {
            this.handleAdd(type)
          }
          break
        case 'cancerTag':
          this.form.details.splice(index, 1)
          if (this.form.details.length === 0) {
            this.handleAdd(type)
          }
          break
        case 'biocheExamTag':
          this.form.biocheDtl.splice(index, 1)
          if (this.form.biocheDtl.length === 0) {
            this.handleAdd(type)
          }
          break
        case 'sexhormoneTag':
          this.form.sexhorDtl.splice(index, 1)
          if (this.form.sexhorDtl.length === 0) {
            this.handleAdd(type)
          }
          break
      }
    },
    handleCancerIndexChange (value, index) {
      if (value === 'AFP' || value === 'CA15') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-25')
      } else if (value === 'CA125') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-35')
      } else if (value === 'CA19-9') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-37')
      } else if (value === 'CA242') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-20')
      } else if (value === 'CA72-4') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-6')
      } else if (value === 'CEA') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-5')
      } else if (value === 'Cyfra21-1') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-3.3')
      } else if (value === 'HE4') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-140')
      } else if (value === 'NSE') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-15.0')
      } else if (value === 'PSA') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-4.0')
      } else if (value === 'SCCA') {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '0-1.5')
      } else {
        this.$set(this.form.details[index], 'cancerIndexValueScope', '')
      }
    },
    handleCancerIndexValueBlur (index) {
      if (this.form.details[index].cancerIndexValue && /^[0-9]+.?[0-9]*$/.test(this.form.details[index].cancerIndexValue)) {
        this.$set(this.form.details[index], 'cancerIndexValue', parseFloat(this.form.details[index].cancerIndexValue).toFixed(3))
      }
    },
    handleTargetSpotLengthBlur () {
      if (this.form.targetSpotLength && /^[0-9]+.?[0-9]*$/.test(this.form.targetSpotLength)) {
        this.$set(this.form, 'targetSpotLength', Number(this.form.targetSpotLength).toFixed(3))
      }
    },
    handleDetectIndexValueBlur (type, index) {
      switch (type) {
        case 'sexhormoneTag':
          if (this.form.sexhorDtl[index].detectIndexValue && /^[0-9]+.?[0-9]*$/.test(this.form.sexhorDtl[index].detectIndexValue)) {
            this.$set(this.form.sexhorDtl[index], 'detectIndexValue', Number(this.form.sexhorDtl[index].detectIndexValue).toFixed(3))
          }
          break
        case 'biocheExamTag':
          if (this.form.biocheDtl[index].detectIndexValue && /^[0-9]+.?[0-9]*$/.test(this.form.biocheDtl[index].detectIndexValue)) {
            this.$set(this.form.biocheDtl[index], 'detectIndexValue', Number(this.form.biocheDtl[index].detectIndexValue).toFixed(3))
          }
          break
      }
    },
    handleCheckAreaChange (value, obj) {
      obj.checkProject = ''
      obj.checkResult = ''
      obj.otherProject = ''
      obj.checkOtherResult = ''
      obj.diseaseTail = ''
    }
  }
}
</script>

<style scoped>
 .colHeight{
   min-height: 51px;
 }
  .selectWidth{
    width: 100%;
  }
</style>
