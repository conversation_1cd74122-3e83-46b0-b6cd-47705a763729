<template>
  <div>
    <div class="form-container">
      <div class="form">
        <div class="form-item">
          <label class="el-form-item__label">快递单号：</label>
          <el-input v-model.trim="form.expressCode" size="mini" placeholder="请输入"></el-input>
        </div>
        <div class="form-item">
          <label class="el-form-item__label">订单编号：</label>
          <el-input v-model.trim="form.orderCode" size="mini" placeholder="请输入"></el-input>
        </div>
        <div class="form-item">
          <label class="el-form-item__label">原始样本编号：</label>
          <el-input v-model.trim="form.oldSampleName" size="mini" placeholder="请输入"></el-input>
        </div>
        <div class="form-item">
          <label class="el-form-item__label">吉因加编号：</label>
          <el-input v-model.trim="form.geneCode" size="mini" placeholder="请输入"></el-input>
        </div>
      </div>
    </div>

    <!--    全部查询弹窗-->
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="mini"
        label-position="top"
        inline>
        <el-form-item label="快递单号" prop="expressCode">
          <el-input
            v-model.trim="form.expressCode"
            clearable
            class="form-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="订单编号" prop="orderCode">
          <el-input
            v-model.trim="form.orderCode"
            clearable
            class="form-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="原始样本编号" prop="oldSampleName">
          <el-input
            v-model.trim="form.oldSampleName"
            clearable
            class="form-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号" prop="geneCode">
          <el-input
            v-model.trim="form.geneCode"
            clearable
            class="form-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本签收状态" prop="hospital">
          <el-select v-model="form.status" clearable multiple class="form-width" placeholder="请选择">
            <template v-for="(item, key) in statusObj">
              <el-option :key="key" :label="item.text" :value="+key"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input
            v-model.trim="form.productName"
            clearable
            class="form-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="签收人" prop="signer">
          <el-input
            v-model.trim="form.signer"
            clearable
            class="form-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="签收时间" prop="signTime">
          <el-date-picker
            v-model="form.signTime"
            type="datetimerange"
            clearable
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="form-long-width">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>

    <div style="margin: 10px 0;display: flex;justify-content: space-between;">
      <div  class="operate-btns-group">
        <template>
          <el-button v-if="$setAuthority('022002001', 'buttons')" type="primary" size="mini" @click="handleException">异常登记</el-button>
          <el-button v-if="$setAuthority('022002002', 'buttons')" type="primary" plain size="mini" :loading="downloading" @click="handlePrint">条码打印</el-button>
          <template v-if="$setAuthority('022002003', 'buttons')">
            <el-button v-if="downloadingExcelLoading" type="primary" plain size="mini" disabled><i class="el-icon-loading"></i> 正在导出</el-button>
            <el-dropdown v-else @command="(command) => handleExport(command, 1)" style="margin: 0 10px;">
              <el-button type="primary" size="mini" plain>数据导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
                <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-if="$setAuthority('022002004', 'buttons')">
            <el-button v-if="downloadingExcelLoading" type="primary" plain size="mini" disabled><i class="el-icon-loading"></i> 正在导出</el-button>
            <el-dropdown v-else @command="(command) => handleExport(command, 2)" style="margin: 0 10px;">
              <el-button type="primary" size="mini" plain>运营导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
                <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-if="$setAuthority('022002005', 'buttons')">
            <el-button v-if="downloadingExcelLoading" type="primary" plain size="mini" disabled><i class="el-icon-loading"></i> 正在导出</el-button>
            <el-dropdown v-else @command="(command) => handleExport(command, 3)" style="margin: 0 10px;">
              <el-button type="primary" size="mini" plain>产量导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
                <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <el-button type="primary" size="mini" plain @click="handleSearch">查询</el-button>
          <el-button type="default" size="mini" plain @click="handleReset">重置</el-button>
          <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" style="margin: 0 10px;" type="primary">
            <el-button size="mini" type="primary" plain @click="searchDialogVisible = true">更多查询</el-button>
          </el-badge>
        </template>
      </div>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      style="width: 100%;"
      :height="tbHeight"
      class="table"
      border
      size="mini"
      @select="handleSelectTable"
      @select-all="handleSelectAll"
      @row-click="handleRowClick">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" label="序号" width="55"></el-table-column>
      <el-table-column min-width="120" label="原始样本编号" prop="oldSampleName" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="150" label="吉因加编号" prop="geneCode" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="样本签收状态" prop="status" show-overflow-tooltip>
        <template #default="{row}">
          <span :style="{color: row.statusColor}">{{ row.statusText }}</span>
        </template>
      </el-table-column>
      <el-table-column min-width="180" label="样本类型" prop="sampleType" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="快递单号" prop="expressCode" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="订单编号" prop="orderCode" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            class="link"
            @click.stop="handleCheck(scope.row.realData, 2)">{{scope.row.orderCode}}</span>
        </template>
      </el-table-column>
      <el-table-column min-width="180" label="产品名称" prop="productName" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="生产片区" prop="area" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="交付周期" prop="deliveryCycle" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="签收序号" prop="signNumber" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="项目编码" prop="projectCode" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="项目名称" prop="projectName" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="120" label="异常原因" prop="exceptionCause" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="异常分类" prop="exceptionType" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="异常描述" prop="exceptionNote" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="异常文件" prop="exceptionFile" show-overflow-tooltip>
        <template #default="{row}">
          <el-button v-if="row.exceptionFile" :loading="row.getFileUrlLoading" type="text" size="mini"  @click.stop="handleShowImageUrl(row, 'exceptionFile','newExceptionFile')">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column min-width="180" label="登记人" prop="registrant" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="登记时间" prop="registrationTime" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="签收人" prop="signer" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="签收时间" prop="signTime" show-overflow-tooltip></el-table-column>
      <el-table-column width="150" label="操作" fixed="right">
        <template #default="{row}">
          <el-button v-if="row.file" type="text" :loading="row.getFileUrlLoading" size="mini"  @click.stop="handleShowImageUrl(row, 'file', 'newFileUrl')">查看记录文件</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <!--      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>-->
    </el-pagination>

    <error-dialog
      :pvisible.sync="errorDialogVisible"
      :ids="currentSelectedIds"
      :is-only-single-order="isOnlySingleOrder"
      @dialogConfirmEvent="getData"/>

    <image-viewer
      :pvisible.sync="showImageViewer"
      :img-list="[{ url: currentImageUrl }]"
    />
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
import ErrorDialog from './ErrorDialog.vue'
import util from '../../../../util/util'
import ImageViewer from '../../../common/imageViewer.vue'
export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ImageViewer,
    ErrorDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 65 + 21 + 42 + 32)
    this.handleSearch()
  },
  computed: {
    searchParamsKeyNum () {
      return util.computeObjectValidKeyNum(this.form, [])
    }
  },
  data () {
    return {
      form: {
        expressCode: '',
        orderCode: '',
        oldSampleName: '',
        geneCode: '',
        signTime: '',
        signer: '',
        status: '',
        productName: ''
      },
      formSubmit: {},
      downloading: false,
      statusObj: {
        0: { text: '未签收', color: '#909399' },
        1: { text: '已签收', color: '#67C23A' }
      },
      isOnlySingleOrder: false,
      errorDialogVisible: false,
      currentSelectedIds: '',
      selectedRows: new Map(),
      showImageViewer: false,
      pageSizes: [100, 200, 500],
      pageSize: 100,
      currentImageUrl: '', // 当前展示的异常文件地址
      downloadingExcelLoading: false // 导出Excel按钮的loading状态
    }
  },
  methods: {
    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData()
    },
    handleReset () {
      this.form = {
        expressCode: '',
        orderCode: '',
        oldSampleName: '',
        geneCode: '',
        signTime: '',
        signer: '',
        status: '',
        productName: ''
      }
      this.handleSearch()
    },
    getParams () {
      const f = this.formSubmit
      const t = f.signTime || []
      return {
        fstatusList: f.status || [],
        fexpressCode: f.expressCode,
        forderCode: f.orderCode,
        foldSampleName: f.oldSampleName,
        fgeneCode: f.geneCode,
        fsignTimeStart: t[0] || '',
        fsignTimeEnd: t[1] || '',
        fproductName: f.productName,
        fsigner: f.signer
      }
    },
    // 查看订单详情 type 1编辑 2 只读 3 详情
    handleCheck (row) {
      console.log('查看订单详情', row.orderType, row.orderId, row.orderCode)
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: 3,
          orderId: row.orderId,
          status: 2,
          code: row.orderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (row.orderType === 5) path = '/business/subpage/technologyService/singleCell'
      if (path) util.openNewPage(path)
    },
    getData () {
      const params = this.getParams()
      this.$ajax({
        url: '/experiment/sign/get_sign_sample_list',
        data: {
          ...params,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.selectedRows.clear()
          let rows = res.data.records || []
          this.tableData = []
          rows.forEach(v => {
            const statusItem = this.statusObj[v.fstatus] || {}
            let item = {
              id: v.fid,
              status: v.fstatus,
              statusText: statusItem.text,
              statusColor: statusItem.color,
              oldSampleName: v.foldSampleName,
              geneCode: v.fgeneCode,
              file: v.ffile,
              expressCode: v.fexpressCode,
              orderCode: v.forderCode,
              sampleType: v.ftissueOrNucleateSampleType,
              productName: v.fproductName,
              orderId: v.forderId,
              orderType: v.forderType,
              signNumber: v.fsignNumber,
              deliveryCycle: v.fdeliveryCycle,
              projectCode: v.fprojectCode,
              projectName: v.fprojectName,
              area: v.farea,
              exceptionCause: v.fexceptionCause,
              exceptionType: v.fexceptionType,
              exceptionNote: v.fexceptionNote,
              exceptionFile: v.fexceptionFile,
              registrant: v.fregistrant,
              registrationTime: v.fregistrationTime,
              signer: v.fsigner,
              signTime: v.fsignTime
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 查看图片
    async handleShowImageUrl (row, oldFileKey, newFileKey) {
      let file
      if (row[newFileKey]) {
        file = row[newFileKey]
      } else {
        file = await this.getLastedImgUrl(row, oldFileKey, newFileKey)
      }
      this.currentImageUrl = file
      this.showImageViewer = true
    },
    getLastedImgUrl (row, oldFileKey, newFileKey) {
      return new Promise(resolve => {
        this.$set(row, 'getFileUrlLoading', true)
        this.$ajax({
          url: '/experiment/common/get_file_url',
          data: {
            fflie: row[oldFileKey]
          }
        }).then(res => {
          if (res.code === this.SUCCESS_CODE) {
            const url = res.data || ''
            this.$set(row, newFileKey, url)
            resolve(url)
          }
        }).finally(() => {
          this.$set(row, 'getFileUrlLoading', false)
        })
      })
    },
    async handlePrint () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      const result = await this.validateSelectedStatus()
      if (!result) {
        this.$message.error('只有已签收的样本才能打印')
        return
      }
      const ids = Array.from(this.selectedRows.keys())
      this.downloading = true
      this.$ajax({
        url: '/experiment/sign/export_sign_sample_list',
        data: {
          fidList: ids
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloading = false
      })
    },
    async handleException () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要异常登记的样本')
        return
      }
      const result = await this.validateSelectedStatus()
      if (!result) {
        this.$message.error('只有已签收的样本才能进行异常登记')
        return
      }
      this.isOnlySingleOrder = [...this.selectedRows.values()].every(item => item.orderType === 5)
      this.currentSelectedIds = Array.from(this.selectedRows.keys()).join(',')
      this.errorDialogVisible = true
    },
    validateSelectedStatus () {
      return new Promise(resolve => {
        const list = [...this.selectedRows.values()]
        const result = list.every(item => item.status === 1)
        resolve(result)
      })
    },
    // 导出
    async handleExport (command, type) {
      // 没有选择查询项
      if (command === 2 && this.selectedRows.size === 0) {
        this.$message.error('请选择需要导出的数据')
        return
      }
      let message = ''
      // 提示
      command === 1 ? message = '是否确认导出查询数据？' : message = '是否确认导出选中数据？'
      await this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let params = null
      switch (command) {
        case 1:
          params = this.getParams()
          break
        case 2:
          params = {
            fidList: Array.from(this.selectedRows.keys())
          }
          break
        default:
          break
      }
      if (params) {
        this.downloadExcel({ftype: type, ...params})
      }
    },
    // 导出Excel
    downloadExcel (params) {
      this.downloadingExcelLoading = true
      this.$ajax({
        url: '/experiment/sign/export_sign_sample',
        data: params,
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadingExcelLoading = false
      })
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-container{
  display: flex;
  justify-content: space-between;
  .form{
    display: flex;
    .form-item{
      display: flex;
      align-items: center;
      margin-right: 20px;
      label{
        flex-shrink: 0;
      }
    }
  }
}
</style>
