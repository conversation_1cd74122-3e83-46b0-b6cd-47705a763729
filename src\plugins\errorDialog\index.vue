<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="数据错误提示" width="800px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)">
      <template v-if="tableData.length <= 20">
        <p class="title">部分错误数据：原因如下</p>
        <el-table
          :data="showTableData"
          stripe
          border
          max-height="400px"
          >
          <template v-if="dataInfo.actualContent">
            <el-table-column prop="title" label="列表" width="220px"></el-table-column>
            <el-table-column prop="actualContent" label="文件实际内容" min-width="220px"></el-table-column>
            <el-table-column prop="errorReason" label="错误原因" min-width="220px"></el-table-column>
          </template>
          <template v-if="dataInfo.rowNum">
            <el-table-column prop="rowNum" label="行数" min-width="220px"></el-table-column>
            <el-table-column prop="title" label="列名" width="220px"></el-table-column>
            <el-table-column prop="errorReason" label="错误原因" min-width="220px"></el-table-column>
          </template>
        </el-table>
      </template>
      <template v-else>
        <p class="title">错误数据较多，请下载表格查看错误内容</p>
        <div style="margin-bottom: 10px;">
          <img src="../../assets/excel.png" style="width: 100px;" alt="">
          <span style="color: #333;font-weight: 600;">错误提示.xlsx</span>
        </div>
        <el-button size="mini" type="primary" @click="handleDownErrorFile">&emsp;下&emsp;载&emsp;</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import util from 'Util'

export default {
  name: `errorDialog`,
  computed: {
    dataInfo () {
      return this.tableData[0] || {}
    },
    showTableData () {
      return this.tableData.slice(0, 20)
    }
  },
  data () {
    return {
      visible: false,
      downloadLoading: false,
      tableData: []
    }
  },
  methods: {
    handleClose () {
      this.visible = false
      this.tableData = []
      this.downloadLoading = false
    },
    handleDownErrorFile () {
      this.downloadLoading = true
      this.$ajax({
        url: '/sample/order/download_error_log_excel',
        data: {
          errorLogDTOS: this.tableData
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-table td, .el-table th{
    padding: 6px 0;
  }
.title{
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
}
</style>
