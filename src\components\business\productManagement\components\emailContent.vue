<script>
import {awaitWrap} from '../../../../util/util'
import {getCancerList, getCustomerList} from '../../../../api/system/productManagementApi'

export default {
  name: 'emailContent',
  // 设置v-model
  // model: {
  //   prop: 'emailContent',
  //   event: 'change'
  // },
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted () {
    this.getCancer()
    this.getCustomer()
  },
  computed: {
    // 设置v-model
    emailContent: {
      get () {
        return this.value
      },
      set (value) {
        this.$emit('change:emailContent', value)
      }
    },
    showCustomers () {
      return this.customerList.slice(0, this.endIndex)
    },
    previewCustomers () {
      return this.emailContent.testResultCompareExcludeOptions || []
    },
    previewMsi () {
      return this.emailContent.msiChoose || []
    }
  },
  data () {
    return {
      booleanValue: ['是', '否'],
      inputConfig: ['输出', '不输出'],
      northList: ['不输出', '北肿1021', '北肿Fusion'],
      clinicInfo: ['受检者临床诊断&治疗史', '个人肿瘤史&家族史'],
      cancerList: [],
      customerList: [],
      visible: false,
      list: [],
      endIndex: 100
    }
  },
  methods: {
    load () {
      this.endIndex += 100
      if (this.endIndex >= this.customerList.length) {
        this.endIndex = this.customerList.length
      }
    },
    async getCancer () {
      const {res} = await awaitWrap(getCancerList({
        cancerClass: 2
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.cancerList = res.data.map(v => {
          return {
            label: v,
            value: v
          }
        })
      }
    },
    async getCustomer () {
      const {res} = await awaitWrap(getCustomerList())
      if (res && res.code === this.SUCCESS_CODE) {
        this.customerList = res.data.map(v => {
          return {
            label: v.name,
            value: v.customerCode
          }
        })
      }
    },
    handlePreview (type = 1) {
      if (type === 1) {
        this.list = this.previewMsi
        if (this.list.length === 0) {
          this.$message.error('请先配置MSI癌肿选择(邮件)')
          return
        }
      } else {
        this.list = this.previewCustomers
        if (this.list.length === 0) {
          this.$message.error('请先配置检测结果对比排除项')
          return
        }
      }

      this.visible = true
    }
  }

}
</script>

<template>
  <div>
    <el-form
      ref="form"
      :model="emailContent"
      size="mini"
      inline
      label-width="140px">
      <!--      产品名称-->
      <el-form-item label="TMB状态报出(邮件)" prop="productName">
        <el-select v-model.trim="emailContent.tmbStatusReport" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in booleanValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="TMB值报出(邮件)" prop="productName">
        <el-select v-model.trim="emailContent.tmbValueReport" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in booleanValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="MSI报出(邮件)" prop="productName">
        <el-select v-model.trim="emailContent.msiReport" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in booleanValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="MSI癌肿选择(邮件)" prop="productName">
        <div style="display: flex; align-items: center">
          <el-select v-model.trim="emailContent.msiChoose" :disabled="emailContent.msiReport !== '是'" clearable
                     multiple collapse-tags placeholder="请选择">
            <el-option
              :key="index"
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in cancerList">
            </el-option>
          </el-select>
          <el-button type="text" size="mini" @click="handlePreview(1)">预览</el-button>
        </div>

      </el-form-item>
      <div class="col">
        <div class="cell title">模块名称</div>
        <div class="cell title">配置项</div>
        <div class="cell title">排除项</div>
      </div>
      <div class="col">
        <div class="cell title">邮件开头内容</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.emailContent" clearable placeholder="请选择" style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in inputConfig">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
      <div class="col">
        <div class="cell title">患者临床信息</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.patientClinicalInfo" clearable placeholder="请选择"
                       style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in clinicInfo">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
      <div class="col">
        <div class="cell title">北肿项目关键信息摘要</div>
        <div class="cell">
          <el-form-item prop="productName">
            <div style="display: flex; align-items: center">
              <el-tooltip class="item" effect="dark" content="该配置内容需要对应输出模版内容，请谨慎配置"
                          placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
              <el-select v-model.trim="emailContent.northInfo" clearable placeholder="请选择" style="width: 100%">
                <el-option
                  :key="index"
                  :label="item"
                  :value="item"
                  v-for="(item, index) in northList">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
      <div class="col">
        <div class="cell title">检测结果小结</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.testResult" clearable placeholder="请选择" style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in inputConfig">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
      <div class="col">
        <div class="cell title">肿瘤分子监测小结</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.cancerMutation" clearable placeholder="请选择" style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in inputConfig">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
      <div class="col">
        <div class="cell title">检测结果比对表</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.testResultCompare" clearable placeholder="请选择" style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in inputConfig">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">
          <el-select-v2 v-model.trim="emailContent.testResultCompareExcludeOptions" :options="customerList" size="mini"
                        clearable multiple collapse-tags filterable placeholder="请选择" style="width: 100%"/>
          <el-button type="text" size="mini" @click="handlePreview(2)">预览</el-button>

        </div>
      </div>
      <div class="col">
        <div class="cell title">靶向相关变异</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.variation" clearable placeholder="请选择" style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in inputConfig">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
      <div class="col">
        <div class="cell title">邮件结尾内容</div>
        <div class="cell">
          <el-form-item prop="productName">
            <el-select v-model.trim="emailContent.emailEndInfo" clearable placeholder="请选择" style="width: 100%">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in inputConfig">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="cell">禁用</div>
      </div>
    </el-form>

    <el-dialog
      title="产品邮件配置"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :modal-append-to-body='false'
      width="400px"
      append-to-body
    >
      <div slot="title">预览</div>
      <div>
        <div v-for="(item, index) in list" :key="index">
          {{ item }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.col {
  display: flex;
  height: 32px;
  line-height: 32px;
  text-align: center;
}

.cell {
  display: flex;
  justify-content: center;
  border: 1px solid #EBEEF5;
  flex: 1;
}

.title {
  color: #606266;
  background: #fafafa;
  font-weight: bold;
}

</style>
