<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="500px"
    title="编辑"
    :before-close="handleClose"
    @opened="handleOpen">
    <el-form ref="form" :model="form" size="mini" label-position="left">
      <el-form-item :label="`您正在对患者编号: ${patientCode}, 修改患者关注点`"></el-form-item>
      <el-form-item label-width="80" label="患者关注点">
        <el-input v-model.trim="form.patientFocus"  type="textarea" autosize maxlength="100" show-word-limit clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
  </el-dialog>
</template>
<script>
import mixins from '../../../../../util/mixins'
import {awaitWrap} from '../../../../../util/util'
import {fixPatientForce} from '../../../../../api/sample/patient'

export default {
  name: 'editPatientForceDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    patientCode: {
      type: String,
      default: ''
    },
    patientFocus: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      loading: false,
      form: {
        patientFocus: ''
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.form.patientFocus = this.patientFocus
    },
    async handleConfirm () {
      this.loading = true
      const {res} = await awaitWrap(fixPatientForce({
        fpatientID: this.patientCode,
        fpatientFocus: this.form.patientFocus
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('修改成功')
        this.handleClose()
        this.$emit('dialogConfirm')
      }
      this.loading = false
    }
  }
}
</script>
<style scoped lang="scss">
/deep/ .el-textarea__inner  {
 padding-bottom: 28px !important;
}
</style>
