<template>
  <el-dialog
    v-drag-dialog
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="visible"
    :before-close="handleClose"
    title="配置"
    width="660px"
    @open="handleOpen">
    <div style="margin-bottom: 10px;">添加样本数: {{sampleNums}}</div>
    <el-form ref="form" :model="form" size="mini" label-suffix=":" :rules="rules" label-width="90px" inline>
      <el-form-item label="工序类别" prop="processType">
        <el-select v-model.trim="form.processType" placeholder="请选择" @change="handleProcessTypeChange">
          <el-option v-for="item in processTypes" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="makeDNB" prop="makeDNB">
        <el-select v-model.trim="form.makeDNB" placeholder="请选择">
          <el-option v-for="item in makeDNBMethods" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="所属片区" prop="area">-->
<!--        <el-select v-model.trim="form.area" placeholder="请选择">-->
<!--          <el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value"/>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
import mixins from '../../../../util/mixins'

export default {
  name: 'setProcessConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleNums: {
      type: Number,
      default: null
    },
    currentProcess: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      form: {
        processType: '',
        makeDNB: '',
        area: ''
      },
      loading: false,
      processTypes: [
        {label: '仅当前环节', value: '仅当前环节'},
        {label: '完整工序', value: '完整工序'}
      ],
      makeDNBMethods: [
        {label: '一步法', value: '一步法'},
        {label: '两步法', value: '两步法'}
      ],
      areaList: JSON.parse(Cookies.get('labOptions') || '').filter(v => v.label !== '苏州'),
      rules: {
        processType: [{required: true, message: '请选择工序类型', trigger: 'change'}],
        makeDNB: [],
        area: [{required: true, message: '请选择片区', trigger: 'change'}]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        if (this.currentProcess === 5) {
          this.rules.makeDNB = [{required: true, message: '请选择makeDNB', trigger: 'change'}]
        } else {
          this.rules.makeDNB = []
        }
      })
    },
    // 流程类型变更，改变makeDNB选项必填性
    handleProcessTypeChange () {
      //  选择“完整工序”/处于makeDNB环节的添加样本"请选择makeDNB"为必填，否则为非必填
      if (this.form.processType === '完整工序' || this.currentProcess === 5) {
        this.rules.makeDNB = [{required: true, message: '请选择makeDNB', trigger: 'change'}]
      } else {
        this.rules.makeDNB = []
      }
    },
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      // this.$message.success('设置成功')
      this.$emit('dialogConfirmEvent', this.form)
      this.loading = false
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
