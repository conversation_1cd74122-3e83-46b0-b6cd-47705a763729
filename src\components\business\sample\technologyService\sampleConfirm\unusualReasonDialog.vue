<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      title="异常说明"
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        class="form"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template>
          <el-form-item label="异常分类" prop="notes">
            {{exceptionType}}
          </el-form-item>
          <el-form-item label="异常描述" prop="notes">
            {{exceptionRemark}}
          </el-form-item>
          <el-form-item v-if="picList.length > 0" label="图片说明">
            <img
              :key="index"
              :src="pic"
              v-for="(pic, index) in picList"
              style="width: 100px; height: 100px; margin: 2px;" @click="handlePictureCardPreview(pic)"/>
            <el-dialog  :visible.sync="dialogVisible" title="图片预览" width="500px" append-to-body>
              <img :src="dialogImageUrl" width="100%" style="margin-right: 3px" alt="">
            </el-dialog>
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleConfirmId: {
      type: Number || String
    }
  },
  data () {
    return {
      exceptionType: '',
      exceptionRemark: '',
      picList: [],
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  methods: {
    handleOpen () {
      this.getRejectSeason()
    },
    getRejectSeason () {
      this.picList = []
      this.exceptionType = ''
      this.exceptionRemark = ''
      this.$ajax({
        url: '/sample/confirm/get_exception_explain_data',
        method: 'get',
        loadingDom: '.form',
        data: {
          sampleConfirmId: this.sampleConfirmId
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.exceptionType = data.fexceptionType
          this.exceptionRemark = data.exceptionRemark
          this.picList = data.exceptionPicPath || []
        }
      })
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped></style>
