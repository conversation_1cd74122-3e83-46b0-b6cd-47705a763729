<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :show-close="false"
      size="560px">
      <template #title>
        <div class="search-custom-title-nav">
          <p class="title">查询条件</p>
          <div>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
          </div>
        </div>
      </template>
      <el-scrollbar class="search-main-wrap">
        <div>
<!--          <div class="module-title">查询条件</div>-->
          <slot/>
        </div>
      </el-scrollbar>
    </el-drawer>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
export default {
  name: 'searchParamsDialog',
  mixins: [mixins.dialogBaseInfo],
  methods: {
    handleReset () {
      this.$emit('reset')
      this.visible = false
    },
    handleSearch () {
      this.$emit('search')
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
// 一般搜索组件抽屉内部的样式
.search-custom-title-nav{
  display: flex;
  height: 40px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #EBEEF5;
  .title{
    color: #444;
    font-size: 16px;
    font-weight: 600;
  }
}
.search-main-wrap{
  height: calc(100vh - 50px - 40px - 10px);
  min-height: 200px;
}
.input-width{
  width: 250px;
}
.form-date {
  width: 350px;
}
</style>
