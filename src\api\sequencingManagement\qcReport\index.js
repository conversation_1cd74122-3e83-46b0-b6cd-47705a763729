import { myAjax } from '@/util/ajax'

/**
 * 获取质控任务单
 * @param {*} data
 * @param {*} options
 * @returns
 */
export function getQcTaskListApi (data, options = {}) {
  return myAjax({
    url: '/report/get_multi_task_list',
    data: data,
    ...options
  })
}

/**
 * 获取质控任务单详情
 * @param {*} data
 * @param {*} options
 * @returns
 */
export function getQcAnalyseDetailApi (data, options = {}) {
  return myAjax({
    url: '/report/get_qc_report_image_list',
    data: data,
    ...options
  })
}
