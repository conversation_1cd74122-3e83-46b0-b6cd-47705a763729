<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="40%"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="cancerName" label="癌种">
          <el-input v-model.trim="form.cancerName" size="mini" maxlength="20" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="name" label="癌种字母简称">
          <el-input v-model.trim="form.name" size="mini" maxlength="20" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="note" label="说明">
          <el-input v-model.trim="form.note"  type="textarea" size="mini" maxlength="100" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    cancerInfo: {
      type: Object,
      required: false
    }
  },
  data () {
    return {
      title: '新增癌种字母简称',
      loading: false,
      form: {
        cancerName: '',
        name: '',
        note: ''
      },
      rules: {
        cancerName: [
          {required: true, message: '请输入癌种', trigger: ['blur', 'change']}
        ],
        name: [
          {required: true, message: '请输入癌种字母简称', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.title = '新增癌种字母简称'
        if (this.cancerInfo) {
          this.form = this.cancerInfo
          this.title = '编辑癌种字母简称'
        }
      })
    },
    // 校验是否上传过文件
    async handleCheck () {
      let result = await this.$ajax({
        url: '/system/probe/import_probe_storage_before_check',
        data: {
          fid: this.id
        }
      })
      if (result.code === this.SUCCESS_CODE) {
        await this.$confirm(`已存在探针文件是否上传覆盖`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        return false
      } else {
        return true
      }
    },
    // 保存配置
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        let cancerInfo = this.cancerInfo || {}
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/system/probe/save_cancer_sort_name_config',
            data: {
              fid: cancerInfo.fid,
              fcancerName: this.form.cancerName,
              fsimpleName: this.form.name,
              fnote: this.form.note
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
