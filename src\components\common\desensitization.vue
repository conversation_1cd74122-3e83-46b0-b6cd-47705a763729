<template>
  <span>
    <el-tooltip
      v-if="!isDetail"
      :content="info"
      :disabled="toolTipDisabled || isNull"
      class="item"
      effect="dark"
      placement="top">
      <span>{{desensitizationInfo}}</span>
    </el-tooltip>
    <span v-else>
      <span v-if="isShow">
        {{desensitizationInfo}}
        <span @click="handleShowInfo">
          <icon-svg v-if="!isNull" class="show-info" icon-class="icon-yanjing"></icon-svg>
        </span>
      </span>
      <span v-else>
        {{info}}
        <span @click="handleShowInfo">
          <icon-svg class="show-info" icon-class="icon-yanjing1"></icon-svg>
        </span>
      </span>
    </span>
  </span>
</template>

<script>
export default {
  props: {
    info: {
      type: [String, Number]
    },
    type: { // 信息类型 name email phone idCard bankCard
      type: String
    },
    toolTipDisabled: { // 是否隐藏全部信息
      type: Boolean,
      default: false
    },
    isDetail: { // 是否是详情页
      type: Boolean,
      default: false
    }
  },
  watch: {
    info: {
      handler () {
        this.handleDesensitization()
      },
      immediate: true
    }
  },
  data () {
    return {
      desensitizationInfo: '',
      isNull: false,
      isShow: true
    }
  },
  methods: {
    // 显示脱敏信息
    handleShowInfo () {
      this.isShow = !this.isShow
    },
    handleDesensitization () {
      this.isNull = !this.info || this.info === '-'
      if (this.isNull) {
        this.desensitizationInfo = this.info || '-'
        return
      }
      let result = ''
      const info = this.info || []
      switch (this.type) {
        case 'name': result = this.nameEncoded(info)
          break
        case 'email': result = this.emailEncoded(info)
          break
        case 'phone': result = this.phoneEncoded(info)
          break
        case 'idCard': result = this.idCardEncoded(info)
          break
        case 'bankCard': result = this.bankCardEncoded(info)
          break
        default: result = ''
      }
      this.desensitizationInfo = result
    },
    /**
     * 规则
     *  1: 全脱
     *  2：脱姓
     *  3：脱敏中间一个字
     *  4：脱敏中间2个字
     *  5：脱敏前二后二
     * @param name 姓名
     * @returns {*} 脱敏名称
     */
    nameEncoded (name = []) {
      const length = name.length
      if (length === 1) name = this.encodedUtils(0, 1, name)
      if (length === 2) name = this.encodedUtils(0, 1, name)
      if (length === 3) name = this.encodedUtils(1, 2, name)
      if (length === 4) name = this.encodedUtils(1, 3, name)
      if (length > 4) name = this.encodedUtils(2, length - 2, name, true)
      return name
    },
    /**
     * 脱敏@前两位
     * @param email
     * @returns {*}
     */
    emailEncoded (email) {
      let emailArr = email.split('@') || []
      let emailStr = emailArr[0]
      emailArr[0] = this.encodedUtils(emailStr.length - 2, emailStr.length, emailStr)
      return emailArr.join('@')
    },
    /**
     * 脱敏手机前三后四 座机533****
     * @param phone
     * @returns {*}
     */
    phoneEncoded (phone) {
      if (!phone) return phone
      return phone.length === 11 ? phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3') : phone.replace(/([\d-]{2,})([\d-]{4})/, '$1****')
    },
    /**
     * 证件号后四
     * @param idCard
     * @returns {*}
     */
    idCardEncoded (idCard = []) {
      idCard = this.encodedUtils(0, idCard.length - 4, idCard)
      return idCard
    },
    /**
     * 银行卡前4后四
     * @returns {*}
     * @param bankCard
     */
    bankCardEncoded (bankCard = []) {
      bankCard = this.encodedUtils(4, bankCard.length - 4, bankCard)
      return bankCard
    },
    /**
     * 加密工具类
     * @param start 开始加密索引
     * @param end 结束加密索引
     * @param str 加密字符传
     * @param isSpec 特殊规则判断
     * @returns {string} 加密后字符传
     */
    encodedUtils (start, end, str, isSpec = false) {
      const startStr = str.substring(0, start)
      const endStr = str.substring(end, str.length)
      let length = end - start > 0
      if (length < 0) length = -length
      if (isSpec) length = 3
      let middleStr = new Array(length).fill('*').join('')
      return startStr + middleStr + endStr
    }
  }
}
</script>

<style scoped lang="scss">
.show-info {
  color: #409EFF;
  cursor: pointer;
}
</style>
