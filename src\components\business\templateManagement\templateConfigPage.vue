<template>
  <div>
    <div class="header">
      <p>模板配置</p>
      <div>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleSave">保存</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </div>
    </div>
    <div class="container">
      <div
        v-loading="categoryLoading"
        class="tree"
        width="250px"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="header-row">模板</div>
        <el-input v-model="moduleFilterText" style="margin: 10px 0;" placeholder="请输入模板"></el-input>
        <el-tree
          :data="moduleCategoryLists"
          ref="tree"
          :key="treeKey"
          :props="categoryProps"
          :filter-node-method="filterTemplateNode"
          show-checkbox
          node-key="fid"
          default-expand-all>
          <template slot-scope="{ node, data }">
            <div class="node-row">
              <span style="color: #606266">
                <icon-svg v-if="data.moduleCode" icon-class="iconwenjian"/>
                <icon-svg v-else icon-class="iconicon--" />
                <span :style="{color: showModel(data) ? '' : '#67C23A'}">{{ node.label }}</span>
              </span>
            </div>
          </template>
        </el-tree>
      </div>
      <div class="but">
        <div @click="handleOperating('add')">
          <span>添加</span>
          <i class="el-icon-right"></i>
        </div>
        <div @click="handleOperating('replace')">
          <i class="el-icon-back"></i>
          <span>替换模块</span>
          <i class="el-icon-right"></i>
        </div>
        <div @click="handleOperating('remove')">
          <i class="el-icon-back"></i>
          <span>移除</span>
        </div>
      </div>
      <div class="table">
        <div class="header-row">分配模板模块</div>
        <el-table
          :data="tableData"
          ref="dragTable" border style="width: 100%"
          height="calc(100vh - 64px - 20px - 20px - 50px - 10px - 50px - 10px)"
          @select="handleSelectTable"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="moduleCode" label="模板编号" min-width="180"></el-table-column>
          <el-table-column prop="moduleName" label="模板名称" min-width="180"></el-table-column>
          <el-table-column prop="creator" label="创建者" min-width="180"></el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="180"></el-table-column>
          <el-table-column prop="modifier" label="修改人" min-width="180"></el-table-column>
          <el-table-column prop="updateTime" label="修改时间" min-width="180"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import Sortable from 'sortablejs'
import IconSvg from '../../common/iconSvg'
export default {
  name: 'templateConfigPage',
  components: {IconSvg},
  created () {
    this.getModuleCategoryLists()
    this.getModuleLists()
  },
  mounted () {
    this.$nextTick(() => {
      this.setSort()
    })
  },
  watch: {
    moduleFilterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  computed: {
    templateId () {
      return this.$store.getters.getValue('templateId')
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      moduleCategoryLists: [],
      categoryProps: {
        label: 'name'
      },
      submitBtnLoading: false,
      categoryLoading: false,
      moduleFilterText: '',
      tableData: [],
      tableIds: [],
      treeKey: new Date().getTime()
    }
  },
  methods: {
    getTableId () {
      this.tableIds = this.tableData.map(v => {
        return v.fid
      })
      this.treeKey = new Date().getTime()
    },
    showModel (data) {
      console.log(data)
      if (data.moduleCode) {
        console.log(this.tableIds.indexOf(data.fid), data.fid)
        return this.tableIds.indexOf(data.fid) === -1
      } else {
        return true
      }
    },
    // 获取模板分类列表
    getModuleCategoryLists () {
      this.categoryLoading = true
      this.$ajax({
        url: '/system/model/get_modelCategory_and_model',
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          let lists = []
          this.dealCategoryData(data, lists)
          this.moduleCategoryLists = lists
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.categoryLoading = false
      })
    },
    // 递归处理节点数据
    dealCategoryData (result, data) {
      result.forEach(item => {
        let v = {
          name: item.name,
          fid: item.fid,
          disabled: false
        }
        if (item.isModel) {
          v.moduleCode = item.modelCode
          v.moduleName = item.name
          v.creator = item.creator
          v.createTime = item.create_time
          v.modifier = item.modifier
          v.updateTime = item.update_time
        }
        if (item.children) {
          if (item.children.length === 0) {
            v.disabled = true
          } else {
            v.children = []
            this.dealCategoryData(item.children, v.children)
          }
        }
        data.push(v)
      })
    },
    // 筛选节点
    filterTemplateNode (value, data) {
      console.log(data)
      if (!value) return true
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1
    },
    handleOperating (type) {
      let handle = null
      switch (type) {
        case 'add':
          handle = this.handleAdd
          break
        case 'replace':
          handle = this.handleReplace
          break
        case 'remove':
          handle = this.handleRemove
          break
      }
      handle().then(() => {
        this.selectedRows.clear()
        this.$refs.dragTable.clearSelection()
        this.$message.success('操作成功')
        this.getTableId()
      })
    },
    // 添加
    handleAdd () {
      return new Promise((resolve, reject) => {
        let models = this.$refs.tree.getCheckedNodes(true)
        if (models.length === 0) {
          this.$message.warning('请选择模块')
          // reject('false')
          return
        }
        let tableDataMap = new Map()
        this.tableData.forEach(item => {
          tableDataMap.set(item.fid, item)
        })
        models.forEach(item => {
          if (!tableDataMap.has(item.fid)) {
            tableDataMap.set(item.fid, item)
          }
        })
        this.tableData = [...tableDataMap.values()]
        resolve()
      })
    },
    // 替换模块
    handleReplace () {
      return new Promise((resolve, reject) => {
        let models = this.$refs.tree.getCheckedNodes(true)
        if (models.length === 0 || this.selectedRows.size === 0) {
          this.$message.warning('请选择模块')
          return
        }
        if (models.length > 1 || this.selectedRows.size > 1) {
          this.$message.warning('最多只能选择一个模块')
          return
        }
        let index
        console.log(this.tableData)
        this.tableData.forEach((v, i) => {
          if (v.fid === [...this.selectedRows.keys()][0]) {
            console.log(i)
            index = i
          }
        })
        this.tableData.splice(index, 1, models[0])
        resolve()
      })
    },
    handleRemove () {
      return new Promise((resolve, reject) => {
        if (this.selectedRows.size === 0) {
          this.$message.warning('请选择需要移除的模块')
          return
        }
        let tableDataMap = new Map()
        this.tableData.forEach(item => {
          tableDataMap.set(item.fid, item)
        })
        this.selectedRows.forEach((v, k) => {
          if (tableDataMap.has(k)) tableDataMap.delete(k)
        })
        this.tableData = [...tableDataMap.values()]
        resolve()
      })
    },
    getModuleLists () {
      this.$ajax({
        url: '/system/template/get_templateAndModel_config',
        data: {
          fid: this.templateId
        },
        method: 'get',
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          let rows = res.data || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              fid: v.fid,
              moduleCode: v.modelCode,
              moduleName: v.modelName,
              creator: v.creator,
              createTime: v.createTime,
              modifier: v.modifier,
              updateTime: v.updateTime
            }
            item.realData = {...item}
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.getTableId()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 保存
    handleSave () {
      let ids = []
      ids = this.tableData.map(v => {
        return v.fid
      })
      this.submitBtnLoading = true
      this.$ajax({
        url: '/system/template/setTemplateAndModel',
        data: {
          templateId: this.templateId,
          modelIdArr: ids
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    },
    handleClose () {
      window.history.go(-1)
    },
    setSort () {
      const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          setTimeout(() => {
            let nI = evt.newIndex
            let oI = evt.oldIndex
            let tableData = JSON.parse(JSON.stringify(this.tableData))
            tableData.splice(nI, 0, ...tableData.splice(oI, 1))
            console.log(tableData)
            this.tableData = []
            this.$nextTick(() => {
              this.tableData.push(...tableData)
            })
          })
        }
      })
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.dragTable.toggleRowSelection(row, !this.selectedRows.has(row.fid))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fid, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    padding: 0 20px;
    border-bottom: 1px solid #E4E7ED;
    & > p{
      font-size: 18px;
      color: slategrey;
      font-weight: 600;
    }
  }
 .container{
   display: flex;
   padding: 10px 20px;
   & > div{
     margin-right: 20px;
     height: calc(100vh - 64px - 20px - 50px - 10px - 20px - 10px);
     overflow-y: auto;
     box-sizing: border-box;
     .header-row{
       font-size: 16px;
       color: slategrey;
       font-weight: 600;
       background: #f2f2f2;
       height: 50px;
       line-height: 50px;
       padding-left: 20px;
     }
   }
   & > div:last-child{
     margin-right: 0;
   }
   .tree{
     background: #fff;
     flex-shrink: 0;
   }
   .but{
     width: 200px;
     flex-shrink: 0;
     border: 1px solid #EBEEF5;
     border-top: none;
     border-bottom: none;
     display: flex;
     align-items: center;
     justify-content: center;
     flex-direction: column;
     & > div{
       text-align: center;
       margin: 20px;
       font-size: 16px;
       font-weight: 600;
       cursor: pointer;
       &:hover{
         color: $color;
       }
     }
   }
   .table{
     width: 100%;
   }
 }
</style>
