<template>
  <div style="padding: 20px;height: calc(100vh - 65px - 40px);overflow-y: auto;">
    <template v-if="showSetContainer">
      <div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 5px;">
        <h2>{{title}}</h2>
        <el-button size="mini" type="primary" @click="handleSubmitData">{{pageType === 1 ? '创建' : '保存'}}</el-button>
      </div>
      <!--<el-button @click="saveMockData">保存数据</el-button>-->
      <!--<el-button @click="downloadMockData">下载数据</el-button>-->
      <div class="main-content">
        <div class="path">
          <div class="title">
            容器属性
          </div>
          <div class="content">
            <el-form
              :model="form"
              :rules="rules"
              ref="ruleForm"
              inline
              size="mini"
              label-width="100px">
              <el-form-item label="所属实验室" prop="lab" require>
                <el-select v-model="form.lab" placeholder="请选择" :disabled="pageType === 2" :style="{width: formWidth}">
                  <el-option v-for="(v, k) in labOptions" :key="k" :label="v" :value="+k"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="容器类型" prop="type" require>
                <el-select
                  v-model="form.type"
                  :disabled="pageType === 2"
                  :style="{width: formWidth}"
                  placeholder="请选择">
                  <template v-for="item in typeOptions">
                    <el-option
                      :label="item.label"
                      :key="item.value"
                      :value="item.value"></el-option>
                  </template>
                </el-select>
              </el-form-item>
              <el-form-item label="容器温度" require>
                <el-input v-model="form.temperature" :style="{width: formWidth}" v-form-loading="formLoading" disabled></el-input>
              </el-form-item>
              <el-form-item label="容器名称" require>
                <el-input v-model="form.name" :style="{width: formWidth}" v-form-loading="formLoading" disabled></el-input>
              </el-form-item>
              <el-form-item label="存储总容量">
                <el-input v-model="form.totalCapacity" :style="{width: formWidth}" disabled></el-input>
              </el-form-item>
              <el-form-item label="容器昵称">
                <el-input v-model.trim="form.nickname" :style="{width: formWidth}" maxlength="10"></el-input>
              </el-form-item>
            </el-form>
            <div style="display: flex;justify-content: flex-end;">
              <el-button size="mini" type="primary" @click="handleSaveBaseInfo">确定</el-button>
            </div>
          </div>
        </div>
        <div v-if="form.name">
          <div class="path">
            <div class="title">
              规格设置
            </div>
            <div class="content">
              <div class="setting-container">
                <div class="setting-model">
                  <el-button size="mini" type="primary" @click="handleShowSetFloorDialogVisible">设置{{changeText('层')}}</el-button>
                  <div class="box">
                    <el-checkbox
                      v-model="floorCheckedAll"
                      :disabled ="canCheckedAllFloor"
                      @change="(val) => {handleCheckAllChange(val, 'floor')}">全选</el-checkbox>
                    <el-scrollbar class="scroll-container">
                      <template v-for="item in container.content">
                        <div
                          :style="{opacity: currentFloor.indexOf(item.num) > -1 ? 1 : 0.6}"
                          :key="item.num"
                          class="floor-item"
                          @click="handleChooseFloorItem('floor', item)">
                          <p>{{item.num + changeText(item.tag)}}</p>
                          <p>{{item.sampleTypes.toString()}} </p>
                          <p>{{item.children.length > 0 ? '已' : '未'}}设置{{changeText('架')}}（{{item.children.length}}个{{changeText('架')}}）</p>
                        </div>
                      </template>
                    </el-scrollbar>
                  </div>
                </div>
                <div class="setting-model">
                  <template v-if="currentFloor.length">
                    <el-button size="mini" type="primary" @click="handleShowSetShelfDialogVisible">设置{{changeText('架')}}</el-button>
                    <div class="box">
                      <el-checkbox
                        v-model="shelfCheckedAll"
                        :disabled ="canCheckedAllShelf"
                        @change="(val) => {handleCheckAllShelfChange(val, 'floor')}">全选</el-checkbox>
                      <el-scrollbar class="scroll-container">
                        <template v-for="item in showShelfLists">
                          <div :key="item.num">
                            <template v-for="v in item.children">
                              <div
                                :style="{opacity: checkedThisShelf(item.num, v.num) ? 1 : 0.6}"
                                :key="v.num"
                                class="floor-item"
                                @click="handleChooseShelfItem(item.num, v)">
                                <p>{{item.num + changeText(item.tag)}}{{v.num + changeText(v.tag)}}</p>
                                <p>{{v.sampleTypes.toString()}} </p>
                                <p>{{v.children.length > 0 ? '已' : '未'}}设置{{changeText('盒')}}（{{v.children.length}}个{{changeText('盒')}}）</p>
                              </div>
                            </template>
                          </div>
                        </template>
                      </el-scrollbar>
                    </div>
                  </template>
                </div>
                <div class="setting-model">
                  <template v-if="currentShelf.length">
                    <el-button size="mini" type="primary" @click="handleShowSetBoxDialogVisible">设置{{changeText('盒')}}</el-button>
                    <div class="box">
                      <el-checkbox
                        v-model="boxCheckedAll"
                        :disabled ="canCheckedAllBox"
                        @change="(val) => {handleCheckAllBoxChange(val, 'floor')}">全选</el-checkbox>
                      <el-scrollbar class="scroll-container">
                        <template v-for="item in showBoxLists">
                          <div :key="item.floorNum + '层' + item.num + '架'">
                            <template v-for="v in item.children">
                              <div
                                :style="{opacity: checkedThisBox(item.floorNum, item.num, v.num) ? 1 : 0.6}"
                                :key="v.num"
                                class="floor-item"
                                @click="handleChooseBoxItem(item.floorNum, item.num, v)">
                                <p>{{item.floorNum + changeText('层')}}{{item.num + changeText(item.tag)}}{{v.num + changeText(v.tag)}}</p>
                                <p>{{v.sampleTypes.toString()}} </p>
                                <p v-if="formSubmit.type !== 'C'">{{isEmptyObj(v.children) ? '未' : '已'}}设置孔</p>
                              </div>
                            </template>
                          </div>
                        </template>
                      </el-scrollbar>
                    </div>
                  </template>
                </div>
                <div class="setting-model" style="width: auto;max-width: 600px;">
                  <template  v-if="formSubmit.type !== 'C'">
                    <template v-if="currentBox.length > 0 && showBoxLists.length > 0">
                      <el-button size="mini" type="primary" @click="handleShowSetHoleDialogVisible">设置孔</el-button>
                      <el-scrollbar class="scroll-container">
                        <div class="box">
                          <template v-if="showHole.row > 0 && showHole.column > 0">
                            <div class="hole-container">
                              <div class="hole-row">
                                <div class="y-text"></div>
                                <div :key="item" v-for="item in showHole.column"  class="x-text">{{addZero(item)}}</div>
                              </div>
                              <div :key="v" v-for="v in showHole.row" class="hole-row">
                                <div class="y-text">{{numToUppercase(v)}}</div>
                                <div :key="item" v-for="item in showHole.column" class="hole"></div>
                              </div>
                            </div>
                          </template>
                        </div>
                      </el-scrollbar>
                    </template>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div style="text-align: center;margin-top: 50px;">
        <p style="color: #67C23A;font-size: 18px;font-weight: 700;line-height: 2.5;">操作成功！</p>
        <p style="font-size: 18px;font-weight: 700;line-height: 2.5;">
          {{closeTime}}秒后
          <el-button v-if="pageType === 1" type="text" style="font-size: 18px;font-weight: 700;" @click="closePage">关闭该页面</el-button>
          <el-button v-if="pageType === 2" type="text" style="font-size: 18px;font-weight: 700;" @click="toContainerDetailPage">返回上一页</el-button>
        </p>
      </div>
    </template>
    <set-floor-dialog
      :pvisible="setFloorDialogVisible"
      :current-operate-name="currentOperateName"
      :sample-type-options="currentSampleTypeOptions"
      :current-set-obj="currentSetObj"
      :container-type="form.type"
      @dialogConfirmEvent="handleSetFloorDialogConfirm"
      @dialogCloseEvent="setFloorDialogVisible = false"
      />
    <set-shelf-dialog
      :pvisible="setShelfDialogVisible"
      :current-operate-name="currentOperateName"
      :sample-type-options="currentSampleTypeOptions"
      :current-set-obj="currentSetObj"
      :current-floor="currentFloor"
      :container-type="form.type"
      :container-data="container.content"
      @dialogConfirmEvent="handleSetShelfDialogConfirm"
      @dialogCloseEvent="setShelfDialogVisible = false"/>
    <set-box-dialog
      :pvisible="setBoxDialogVisible"
      :current-operate-name="currentOperateName"
      :sample-type-options="currentSampleTypeOptions"
      :current-set-obj="currentSetObj"
      :container-type="form.type"
      :container-data="container.content"
      :current-shelf="currentShelf"
      @dialogConfirmEvent="handleSetBoxDialogConfirm"
      @dialogCloseEvent="setBoxDialogVisible = false"/>
    <set-hole-dialog
      :pvisible="setHoleDialogVisible"
      :current-operate-name="currentOperateName"
      :current-set-obj="currentSetObj"
      @dialogConfirmEvent="handleSetHoleDialogConfirm"
      @dialogCloseEvent="setHoleDialogVisible = false"/>
  </div>
</template>

<script>
// import num from './components/cc'
import setFloorDialog from './createContainerSetFloorDialog'
import SetShelfDialog from './createContainerSetShelfDialog'
import setBoxDialog from './createContainerSetBoxDialog'
import setHoleDialog from './createContainerSetHoleDialog'
import util from '../../../util/util'
import constants from '../../../util/constants'
export default {
  name: 'createContainer',
  components: {
    setFloorDialog,
    SetShelfDialog,
    setBoxDialog,
    setHoleDialog
  },
  created () {

  },
  mounted () {
    // this.getSampleType()
  },
  beforeDestroy () {
    this.$notify.closeAll()
  },
  watch: {
    currentFloor () {
      this.currentShelf = []
      this.boxCheckedAll = false
      this.shelfCheckedAll = false
    },
    currentShelf () {
      this.currentBox = []
      this.boxCheckedAll = false
    },
    $route: {
      handler: function (newVal) {
        if (newVal.path === '/business/sub/modifyContainer') {
          this.title = '设置容器'
          this.pageType = 2
          this.getContainerData()
        }
      },
      immediate: true
    }
  },
  computed: {
    containerId () {
      return this.$store.getters.getValue('containerId')
    },
    // 是否可以点击层全选
    canCheckedAllFloor () {
      let l = this.container.content ? this.container.content.length : 0
      if (l === 0) {
        return true
      } else if (l === 1) {
        return false
      } else {
        // 判断架子的数量是否相同
        let targetShelfNum = this.container.content[0].children ? this.container.content[0].children.length : 0
        let hasSameShelfNum = this.container.content.every(item => {
          return item.children.length === targetShelfNum
        })
        if (!hasSameShelfNum) { // 每一层的架子数量不同，则无法全选
          return true
        }
        let targetSampleType = this.container.content[0].sampleTypes
        let hasCheckedItemsSampleType = this.container.content.filter((v, i) => {
          return i > 0
        }).map(v => {
          return v.sampleTypes
        })
        let hasSameSampleType = true
        for (let i = 0; i < hasCheckedItemsSampleType.length; i++) {
          let isSame = this.judgeArray(targetSampleType, hasCheckedItemsSampleType[i])
          if (!isSame) {
            hasSameSampleType = false
            break
          }
        }
        return !hasSameSampleType
      }
    },
    // 架的显示
    showShelfLists () {
      if (this.currentFloor.length > 0) {
        let floor = this.container.content.filter(item => {
          return this.currentFloor.indexOf(item.num) > -1
        })
        return floor
      } else {
        return []
      }
    },
    // 是否可以点击架全选
    canCheckedAllShelf () {
      /**
       * 可全选的条件：
       * 1、所有展示的架的盒子数量相同
       * 2、所有展示的架子的样本类型相同
       * */
      let shelf = []
      this.showShelfLists.forEach(item => {
        shelf.push(...item.children)
      })
      let l = shelf ? shelf.length : 0
      if (l === 0) {
        return true
      } else if (l === 1) {
        return false
      } else {
        let startShlefBoxNum = shelf[0].children.length // 第一个架子有的盒子数量
        let hasSameBoxNum = shelf.every((item) => {
          return startShlefBoxNum === item.children.length
        })
        if (!hasSameBoxNum) return true
        let targetSampleType = shelf[0].sampleTypes
        let hasCheckedItemsSampleType = shelf.filter((v, i) => {
          return i > 0
        }).map(v => {
          return v.sampleTypes
        })
        let hasSameSampleType = true
        for (let i = 0; i < hasCheckedItemsSampleType.length; i++) {
          let isSame = this.judgeArray(targetSampleType, hasCheckedItemsSampleType[i])
          if (!isSame) {
            hasSameSampleType = false
            break
          }
        }
        return !hasSameSampleType
      }
    },
    // 盒子的显示
    showBoxLists () {
      let r = []
      if (this.currentShelf.length > 0) {
        console.log(this.currentShelf)
        let currentShelfMap = new Map()
        this.currentShelf.forEach(item => {
          currentShelfMap.set(item.floorNum + '' + item.shelfNum, item.shelfNum)
        })
        this.showShelfLists.forEach(item => {
          item.children.forEach(v => {
            let shelfNum = currentShelfMap.get(item.num + '' + v.num)
            if (shelfNum) {
              let vv = {
                floorNum: +item.num,
                ...v
              }
              r.push(vv)
            }
          })
        })
      }
      return r
    },
    // 是否可以点击盒全选
    canCheckedAllBox () {
      let box = []
      this.showBoxLists.forEach(item => {
        box.push(...item.children)
      })
      let l = box ? box.length : 0
      if (l === 0) {
        return true
      } else if (l === 1) {
        return false
      } else {
        let targetSampleType = box[0].sampleTypes
        let hasCheckedItemsSampleType = box.filter((v, i) => {
          return i > 0
        }).map(v => {
          return v.sampleTypes
        })
        let hasSameSampleType = true
        for (let i = 0; i < hasCheckedItemsSampleType.length; i++) {
          let isSame = this.judgeArray(targetSampleType, hasCheckedItemsSampleType[i])
          if (!isSame) {
            hasSameSampleType = false
            break
          }
        }
        return !hasSameSampleType
      }
    },
    // 孔的显示
    showHole () {
      let r = {
        row: 0,
        column: 0
      }
      if (this.currentBox.length > 0) {
        let box = this.currentBox[0]
        this.showBoxLists.forEach(item => {
          if (item.floorNum === box.floorNum && item.num === box.shelfNum) {
            item.children.forEach(v => {
              if (v.num === box.boxNum) {
                r = v.children
              }
            })
          }
        })
      }
      return r
    }
  },
  data () {
    return {
      showSetContainer: true, // 用户设置完成后
      closeTime: 3, // 关闭倒计时
      container: {},
      title: '创建容器',
      pageType: 1, // 1新建容器 2设置容器
      formSubmit: {
        id: '',
        lab: '',
        type: '',
        temperature: '',
        name: '',
        totalCapacity: '',
        nickname: '',
        notes: ''
      },
      form: {
        id: '',
        lab: '',
        type: '',
        temperature: '',
        name: '',
        totalCapacity: '',
        nickname: '',
        notes: ''
      },
      currentOperateName: '',
      labOptions: constants.REGION_OBJ,
      typeOptions: [
        {label: '-80℃超低温冰箱', value: 'A'},
        {label: '-20℃卧式冰箱', value: 'B'},
        {label: '石蜡柜', value: 'C'},
        {label: '石蜡屑', value: 'D'},
        {label: '4℃冰箱', value: 'E'},
        {label: '常温暂存箱', value: 'F'},
        {label: '-20℃冰箱', value: 'G'},
        {label: '干冰箱', value: 'H'},
        {label: '百片石蜡柜', value: 'I'}
      ],
      sampleTypeOptions: [],
      currentSampleTypeOptions: [],
      formLoading: false,
      formWidth: '250px',
      currentSetObj: {}, // 当前设置的值
      currentFloor: [], // 选中的层
      floorCheckedAll: false, // 是否全选层
      currentShelf: [], // item: {floorNum: 1, shelfNum: 2}
      shelfCheckedAll: false, // 是否选中所有架
      currentBox: [],
      boxCheckedAll: false,
      hole: [],
      setFloorDialogVisible: false,
      setShelfDialogVisible: false,
      setBoxDialogVisible: false,
      setHoleDialogVisible: false,
      rules: {
        lab: [
          { required: true, message: '请选择实验室', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择容器类型', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    // 获取样本类型选项
    getSampleType (type) {
      this.$ajax({
        url: '/sample/t_dictory/get_tdictionary_sample_type',
        method: 'get',
        data: {
          containerType: type
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.sampleTypeOptions = []
          data.forEach(item => {
            let v = {
              value: item.dictValue,
              pipeList: item.fboxTypeList || []
            }
            this.sampleTypeOptions.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // setFormSubmit
    setFormSubmit () {
      this.formSubmit = {}
      this.formSubmit = {...this.form}
    },
    // 从后台获取容器数据 -- 编辑专用
    getContainerData () {
      this.$ajax({
        url: '/sample/container/get_container_details',
        data: {
          containerId: this.containerId
        },
        method: 'get',
        loadingDom: 'body'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          console.log(res.data)
          this.form.id = res.data.fid
          this.form.lab = res.data.flabNo
          this.form.type = res.data.ftype
          this.form.temperature = res.data.ftemperature
          this.form.nickname = res.data.fnickName
          this.form.name = res.data.fname
          this.form.totalCapacity = res.data.ftotalCapacity
          this.form.notes = res.data.fnotes
          let floorIdMap = new Map()
          let shelfIdMap = new Map()
          let boxIdMap = new Map()
          let content = res.data.floorList ? res.data.floorList : []
          /**
           * 遍历后台数据，把每层、架、盒的id,k孔的使用情况拿出来放到上面map
           * 然后通过指定的 map key值去拿
           * **/
          content.forEach(floorItem => { // 遍历容器，获取层
            floorIdMap.set(floorItem.ffloorNumber, {id: floorItem.fid, useHole: floorItem.ffloorUsedHole})
            if (floorItem.shelfList && floorItem.shelfList.length > 0) {
              floorItem.shelfList.forEach(shelfItem => { // 遍历层，获取架子
                let key = `${floorItem.ffloorNumber}_${shelfItem.fshelfNumber}` // 1_1的形式
                let value = {id: shelfItem.fid, useHole: shelfItem.fshelfUsedHole}
                shelfIdMap.set(key, value)
                if (shelfItem.boxList && shelfItem.boxList.length > 0) {
                  shelfItem.boxList.forEach(boxItem => { // 遍历架子，获取盒
                    let key = `${floorItem.ffloorNumber}_${shelfItem.fshelfNumber}_${boxItem.fboxNumber}` // 1_1_1的形式
                    let value = {id: boxItem.fid, useHole: boxItem.fboxUsedHole, useHoleIds: boxItem.fboxUsedHoleNumber ? boxItem.fboxUsedHoleNumber : ''}
                    boxIdMap.set(key, value)
                  })
                }
              })
            }
          })
          console.log(floorIdMap)
          console.log(shelfIdMap)
          console.log(boxIdMap)
          this.container = res.data.fcontainerInputInfo ? JSON.parse(res.data.fcontainerInputInfo) : {}
          this.container.content.forEach(floorItem => {
            let values = floorIdMap.has(floorItem.num) ? floorIdMap.get(floorItem.num) : {}
            floorItem.id = values.id
            floorItem.useHole = values.useHole
            if (floorItem.children && floorItem.children.length > 0) {
              console.log(floorItem)
              floorItem.children.forEach(shelfItem => {
                let k = `${floorItem.num}_${shelfItem.num}`
                let values = shelfIdMap.has(k) ? shelfIdMap.get(k) : {}
                shelfItem.id = values.id
                shelfItem.useHole = values.useHole
                if (shelfItem.children && shelfItem.children.length > 0) {
                  shelfItem.children.forEach(boxItem => {
                    let k = `${floorItem.num}_${shelfItem.num}_${boxItem.num}`
                    let values = boxIdMap.has(k) ? boxIdMap.get(k) : {}
                    boxItem.id = values.id
                    boxItem.useHole = values.useHole
                    boxItem.useHoleIds = values.useHoleIds.split(',')
                  })
                }
              })
            }
          })
          this.setFormSubmit()
          this.getSampleType(this.form.type)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取温度和容器名称
    getTemperatureAndName () {
      this.formLoading = true
      this.$ajax({
        url: '/sample/container/get_temperature_and_name',
        method: 'get',
        data: {
          fcontainerType: this.formSubmit.type,
          flabNo: this.formSubmit.lab
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.form.temperature = data.ftemperature
          this.form.name = data.fname
          this.setFormSubmit()
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.formLoading = false
      })
    },
    // 保存容器基本信息
    handleSaveBaseInfo () {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let getData = () => {
            this.form.temperature = ''
            this.form.name = ''
            this.form.notes = ''
            this.form.totalCapacity = ''
            this.setFormSubmit()
            this.getTemperatureAndName()
            this.getSampleType(this.form.type)
          }
          if (!this.container.content || this.container.content.length === 0) {
            getData()
          } else {
            this.$confirm('此操作会将您设置的容器内容全部清除，是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.container = {}
              this.currentFloor = []
              this.currentShelf = []
              this.currentBox = []
              getData()
            })
          }
        }
      })
    },
    // 点击设置层
    handleShowSetFloorDialogVisible () {
      this.currentOperateName = this.form.name
      this.currentSetObj = this.container
      this.currentSampleTypeOptions = this.sampleTypeOptions
      this.setFloorDialogVisible = true
    },
    // 点击设置架
    handleShowSetShelfDialogVisible () {
      if (this.currentFloor.length === 0) {
        this.$message.error(`请选择需要设置的${this.changeText('层')}`)
        return
      }
      this.currentOperateName = this.currentFloor.toString() + this.changeText('层')
      this.currentSetObj = this.container.content.filter(item => {
        return item.num === this.currentFloor[0]
      })[0]
      this.currentSampleTypeOptions = this.sampleTypeOptions.filter(item => {
        return this.currentSetObj.sampleTypes.indexOf(item.value) > -1
      })
      this.setShelfDialogVisible = true
    },
    // 点击设置盒
    handleShowSetBoxDialogVisible () {
      if (this.currentShelf.length === 0) {
        this.$message.error(`请选择需要设置的${this.changeText('架')}`)
        return
      }
      this.currentOperateName = ''
      this.currentShelf.forEach((v, i, a) => {
        this.currentOperateName += `${v.floorNum}${this.changeText('层')}${v.shelfNum}${this.changeText('架')}${i === a.length - 1 ? '' : '，'}`
      })
      this.currentSetObj = {}
      console.log('showShelfLists')
      console.log(this.showShelfLists)
      this.showShelfLists.forEach(item => {
        let firstShelf = this.currentShelf[0]
        if (item.num === firstShelf.floorNum) {
          item.children.forEach(v => {
            if (v.num === firstShelf.shelfNum) {
              this.currentSetObj = v
            }
          })
        }
      })
      this.currentSampleTypeOptions = this.sampleTypeOptions.filter(item => {
        return this.currentSetObj.sampleTypes.indexOf(item.value) > -1
      })
      this.setBoxDialogVisible = true
    },
    // 点击设置孔
    async handleShowSetHoleDialogVisible () {
      this.$notify.closeAll()
      let boxMap = new Map()
      const currentBox = this.currentBox[0] || {}
      let box = {}
      this.showBoxLists.forEach(item => { // 将以使用孔位的盒子列出来
        if (item.children) {
          item.children.forEach(v => {
            if (v.useHole && v.useHole > 0) {
              let k = `${item.floorNum}-${item.num}-${v.num}`
              boxMap.set(k, v.useHole)
            }
            if (item.floorNum === currentBox.floorNum && item.num === currentBox.shelfNum && v.num === currentBox.boxNum) {
              box = v
            }
          })
        }
      })
      let hasUsedBox = this.currentBox.filter(item => {
        let k = `${item.floorNum}-${item.shelfNum}-${item.boxNum}`
        return boxMap.has(k)
      })
      if (hasUsedBox.length > 0) {
        let msgs = []
        hasUsedBox.forEach(item => {
          let k = `${item.floorNum}层${item.shelfNum}架${item.boxNum}盒`
          msgs.push(k)
        })
        this.$message.error('选中盒子中已有孔位被使用，无法编辑')
        this.$notify.closeAll()
        this.$notify({
          title: '以下盒子被使用',
          message: `<p style="word-break: break-all;">${msgs.toString()}</p>`,
          duration: 0,
          dangerouslyUseHTMLString: true,
          position: 'top-left'
        })
        return
      }
      console.log(box)
      const sizeList = await this.getBoxHoles(box.sampleTypes)
      this.currentSetObj = {...this.showHole, sizeList}
      this.currentOperateName = ''
      this.currentBox.forEach((v, i, a) => {
        this.currentOperateName += `${v.floorNum}层${v.shelfNum}架${v.boxNum}盒${i === a.length - 1 ? '' : '，'}`
      })
      this.setHoleDialogVisible = true
    },
    // 获取盒子的孔
    getBoxHoles (sampleTypes) {
      return new Promise(resolve => {
        this.$ajax({
          url: '/sample/sample_type/get_box_type_by_sample_type',
          data: {
            sampleTypes: sampleTypes
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            resolve(res.data)
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 判断架子是否被选中
    checkedThisShelf (floorNum, shelfNum) {
      return this.currentShelf.some(item => {
        return item.floorNum === floorNum && item.shelfNum === shelfNum
      })
    },
    // 判断盒子是否被选中
    checkedThisBox (floorNum, shelfNum, boxNum) {
      return this.currentBox.some(item => {
        return item.floorNum === floorNum && item.shelfNum === shelfNum && item.boxNum === boxNum
      })
    },
    // 确认层
    handleSetFloorDialogConfirm (data) {
      this.setFloorDialogVisible = false
      this.container.formInput = data.formInput || {}
      let content = data.content || []
      let floorNum = content.map(item => {
        return item.num
      })
      // 如果选中的层被删除
      this.currentFloor = this.currentFloor.filter(item => {
        return floorNum.indexOf(item) > -1
      })
      this.$set(this.container, 'content', content)
    },
    // 确认架
    handleSetShelfDialogConfirm (data) {
      this.setShelfDialogVisible = false
      console.log(data)
      this.$set(this.container, 'content', data)
      let currentShowShelf = []
      this.container.content.forEach(item => {
        if (item.children) {
          item.children.forEach(v => {
            let vv = {
              floorNum: item.num,
              shelfNum: v.num
            }
            currentShowShelf.push(vv)
          })
        }
      })
      this.currentShelf = this.currentShelf.filter(item => {
        return currentShowShelf.some(v => {
          return v.floorNum === item.floorNum && v.shelfNum === item.shelfNum
        })
      })
    },
    // 确认盒
    handleSetBoxDialogConfirm (data) {
      console.log(data)
      this.setBoxDialogVisible = false
      let content = JSON.parse(JSON.stringify(this.container.content))
      content.forEach(item => {
        this.currentShelf.forEach(v => {
          if (item.num === v.floorNum) {
            item.children.forEach(vv => {
              if (vv.num === v.shelfNum) {
                vv.formInput = data.formInput || {}
                vv.children = data.content || []
              }
            })
          }
        })
      })
      this.$set(this.container, 'content', content)
      // 去除多余的选中项
      let currentShowBox = []
      this.showBoxLists.forEach(item => {
        if (item.children) {
          item.children.forEach(v => {
            let vv = {
              floorNum: item.floorNum,
              shelfNum: item.num,
              boxNum: v.num
            }
            currentShowBox.push(vv)
          })
        }
      })
      this.currentBox = this.currentBox.filter(item => {
        return currentShowBox.some(v => {
          return v.floorNum === item.floorNum && v.shelfNum === item.shelfNum && v.boxNum === item.boxNum
        })
      })
    },
    // 确认孔
    handleSetHoleDialogConfirm (hole) {
      console.log(hole)
      this.setHoleDialogVisible = false
      let content = JSON.parse(JSON.stringify(this.container.content))
      content.forEach(item => {
        this.currentBox.forEach(v => {
          if (item.num === v.floorNum) {
            item.children.forEach(vv => {
              if (vv.num === v.shelfNum) {
                vv.children.forEach(vvv => {
                  if (vvv.num === v.boxNum) {
                    vvv.children = hole
                  }
                })
              }
            })
          }
        })
      })
      this.$set(this.container, 'content', content)
    },
    // 选中具体层
    handleChooseFloorItem (type, item) {
      let num = item.num
      let targetSampleType = item.sampleTypes
      let index = this.currentFloor.indexOf(num)
      if (index > -1) {
        this.currentFloor.splice(index, 1)
      } else {
        // 判断架子的数量是否相同
        let targetShelfNum = item.children ? item.children.length : 0
        let hasSameShelfNum = this.container.content.every(v => {
          if (this.currentFloor.indexOf(v.num) > -1) {
            return targetShelfNum === (v.children ? v.children.length : 0)
          } else {
            return true
          }
        })
        if (!hasSameShelfNum) { // 每一层的架子数量不同，则无法全选
          this.$message.error('层的架子数量不同，无法同时选中')
          return
        }
        // 判断样本类型是否完全一样
        let hasCheckedItemsSampleType = this.container.content.filter(v => {
          return this.currentFloor.includes(v.num)
        }).map(v => {
          return v.sampleTypes
        })
        let hasSameSampleType = true
        for (let i = 0; i < hasCheckedItemsSampleType.length; i++) {
          let isSame = this.judgeArray(targetSampleType, hasCheckedItemsSampleType[i])
          if (!isSame) {
            hasSameSampleType = false
            break
          }
        }
        if (hasSameSampleType) {
          this.currentFloor.push(num)
        } else {
          this.$message.error('样本类型不同，无法同时选中')
        }
      }
      this.floorCheckedAll = this.currentFloor.length === this.container.content.length
    },
    // 选中具体架
    handleChooseShelfItem (floorNum, shelfItem) {
      let shelfNum = +shelfItem.num
      let targetSampleType = shelfItem.sampleTypes
      let index = null
      for (let i = 0; i < this.currentShelf.length; i++) {
        let s = this.currentShelf[i]
        if (s.floorNum === floorNum && s.shelfNum === shelfNum) {
          index = i
          break
        }
      }
      if (index !== null) {
        this.currentShelf.splice(index, 1)
      } else {
        // 判断架子的数量是否相同
        let targetBoxNum = shelfItem.children ? shelfItem.children.length : 0
        let hasSameBoxNum = true
        if (this.currentShelf.length > 0) {
          let currentShelfItem = this.currentShelf[0]
          let sourceBoxNum = []
          for (let i = 0; i < this.container.content.length; i++) {
            let v = this.container.content[i]
            if (v.num === currentShelfItem.floorNum) {
              for (let ii = 0; ii < v.children.length; ii++) {
                let vv = v.children[ii]
                if (vv.num === currentShelfItem.shelfNum) {
                  sourceBoxNum = vv.children
                  break
                }
              }
              break
            }
          }
          hasSameBoxNum = targetBoxNum === sourceBoxNum.length
        }
        if (!hasSameBoxNum) { // 每一层的架子数量不同，则无法全选
          this.$message.error('架的盒子数量不同，无法同时选中')
          return
        }
        let floorMap = new Map()
        let hasCheckedItemsSampleType = []
        this.container.content.forEach(v => {
          floorMap.set(v.num, v.children)
        })
        this.currentShelf.forEach(item => {
          let samples = floorMap.get(item.floorNum).filter(v => {
            return v.num === item.shelfNum
          }).map(v => {
            return v.sampleTypes
          })
          hasCheckedItemsSampleType.push(...samples)
        })
        let hasSameSampleType = true
        for (let i = 0; i < hasCheckedItemsSampleType.length; i++) {
          let isSame = this.judgeArray(targetSampleType, hasCheckedItemsSampleType[i])
          if (!isSame) {
            hasSameSampleType = false
            break
          }
        }
        console.log(hasSameSampleType)
        if (hasSameSampleType) {
          let item = {
            floorNum: +floorNum,
            shelfNum: +shelfNum
          }
          this.currentShelf.push(item)
        } else {
          this.$message.error('样本类型不同，无法同时选中')
        }
      }
      let total = 0
      this.showShelfLists.forEach(item => {
        total += item.children.length
      })
      this.shelfCheckedAll = total > 0 && total === this.currentShelf.length
    },
    // 选中具体盒
    handleChooseBoxItem (floorNum, shelfNum, boxItem) {
      console.log(floorNum, shelfNum, boxItem)
      let boxNum = +boxItem.num
      let targetSampleType = boxItem.sampleTypes
      let index = null
      for (let i = 0; i < this.currentBox.length; i++) {
        let s = this.currentBox[i]
        if (s.floorNum === floorNum && s.shelfNum === shelfNum && s.boxNum === boxNum) {
          index = i
          break
        }
      }
      if (index !== null) {
        this.currentBox.splice(index, 1)
      } else {
        let shelfMap = new Map()
        let hasCheckedItemsSampleType = []
        this.showBoxLists.forEach(v => {
          shelfMap.set(v.floorNum + '层' + v.num, v.children)
        })
        this.currentBox.forEach(item => {
          let samples = shelfMap.get(item.floorNum + '层' + item.shelfNum).filter(v => {
            return v.num === item.boxNum
          }).map(v => {
            return v.sampleTypes
          })
          hasCheckedItemsSampleType.push(...samples)
        })
        console.log(hasCheckedItemsSampleType)
        let hasSameSampleType = true
        for (let i = 0; i < hasCheckedItemsSampleType.length; i++) {
          let isSame = this.judgeArray(targetSampleType, hasCheckedItemsSampleType[i])
          if (!isSame) {
            hasSameSampleType = false
            break
          }
        }
        console.log(hasSameSampleType)
        if (hasSameSampleType) {
          let item = {
            floorNum: +floorNum,
            shelfNum: +shelfNum,
            boxNum: +boxNum
          }
          this.currentBox.push(item)
        } else {
          this.$message.error('样本类型不同，无法同时选中')
        }
      }
      let total = 0
      this.showBoxLists.forEach(item => {
        total += item.children.length
      })
      this.boxCheckedAll = total > 0 && total === this.currentBox.length
    },
    // 判断两个数组对象是否相同
    judgeArray (arr1, arr2) {
      if (arr1.length !== arr2.length) return false
      return arr1.every(item => {
        return arr2.includes(item)
      })
    },
    // 全选层
    handleCheckAllChange (val, type) {
      if (type === 'floor') {
        this.currentFloor = []
        if (!val) return
        let nums = this.container.content.map(item => { return item.num })
        this.currentFloor.push(...nums)
      }
    },
    // 全选架
    handleCheckAllShelfChange (val) {
      this.currentShelf = []
      if (!val) return
      let floor = JSON.parse(JSON.stringify(this.showShelfLists))
      floor.forEach(item => {
        item.children.forEach(v => {
          let vv = {
            floorNum: +item.num,
            shelfNum: +v.num
          }
          this.currentShelf.push(vv)
        })
      })
    },
    // 全选盒子
    handleCheckAllBoxChange (val) {
      this.currentBox = []
      if (!val) return
      let shelf = JSON.parse(JSON.stringify(this.showBoxLists))
      shelf.forEach(item => {
        item.children.forEach(v => {
          let vv = {
            floorNum: +item.floorNum,
            shelfNum: +item.num,
            boxNum: +v.num
          }
          this.currentBox.push(vv)
        })
      })
    },
    // 判断空对象
    isEmptyObj (obj) {
      return Object.keys(obj).length === 0
    },
    // 不到10补0
    addZero (num) {
      if (+num > 0 && +num < 10) {
        return 0 + '' + num
      }
      return num
    },
    // 数字转大写字母
    numToUppercase (num) {
      return String.fromCharCode(64 + num)
    },
    saveMockData () {
      console.log(this.container)
      util.setSessionInfo('container', this.container)
    },
    downloadMockData () {
      this.container = util.getSessionInfo('container')
    },
    // 上传数据
    handleSubmitData () {
      if (!this.container.content || this.container.content.length === 0) {
        this.$message.error('未设置容器')
        return
      }
      // 判断是否有设置架
      let notSetShelf = []
      this.container.content.forEach(item => {
        if (!item.children || item.children.length === 0) {
          notSetShelf.push(item.num)
        }
      })
      if (notSetShelf.length > 0) {
        this.$message({
          type: 'error',
          duration: 5000,
          message: `${notSetShelf.join('，')}${this.changeText('层')}未设置${this.changeText('架')}`
        })
        return
      }
      // 判断是否有设置架
      let notSetBox = []
      this.container.content.forEach(item => {
        item.children.forEach(v => {
          if (!v.children || v.children.length === 0) {
            notSetBox.push(`${item.num}${this.changeText('层')}${v.num}${this.changeText('架')}`)
          }
        })
      })
      if (notSetBox.length > 0) {
        this.$message({
          type: 'error',
          duration: 5000,
          message: `${notSetBox.join('，')}未设置${this.changeText('盒')}`
        })
        return
      }
      if (this.formSubmit.type !== 'C') {
        let notSetHole = []
        this.container.content.forEach(item => {
          item.children.forEach(v => {
            v.children.forEach(vv => {
              if (!vv.children.column || !vv.children.row) {
                notSetHole.push(`${item.num}层${v.num}架${vv.num}盒`)
              }
            })
          })
        })
        if (notSetHole.length > 0) {
          this.$message({
            type: 'error',
            duration: 5000,
            message: `${notSetHole.join('，')}未设置孔位`
          })
          return
        }
      }
      let data = {
        flab: this.formSubmit.lab,
        ftype: this.formSubmit.type,
        fnickName: this.form.nickname,
        fname: this.formSubmit.name,
        ftemperature: this.formSubmit.temperature,
        // fnotes: this.formSubmit.notes,
        fcontainerInputInfo: JSON.stringify(this.container)
      }
      if (this.$route.path === '/business/sub/modifyContainer') {
        data.fid = this.containerId
      }
      let floorList = []
      this.container.content.forEach(item => {
        let floor = {
          ffloorNumber: item.num,
          fsampleType: item.sampleTypes.toString(),
          fshelfCount: item.children ? item.children.length : 0
        }
        if (item.id) {
          floor.fid = item.id
        }
        floor.shelfList = []
        if (item.children && item.children.length > 0) {
          item.children.forEach(shelfItem => {
            let shelf = {
              fshelfNumber: shelfItem.num,
              fsampleType: shelfItem.sampleTypes.toString(),
              fboxCount: shelfItem.children ? item.children.length : 0
            }
            if (shelfItem.id) {
              shelf.fid = shelfItem.id
            }
            shelf.boxList = []
            if (shelfItem.children && shelfItem.children.length > 0) {
              shelfItem.children.forEach(boxItem => {
                let box = {
                  fboxNumber: boxItem.num,
                  fsampleType: boxItem.sampleTypes.toString()
                }
                if (boxItem.id) {
                  box.fid = boxItem.id
                }
                console.log(boxItem)
                if (this.form.type !== 'C') {
                  let hole = {
                    fxSize: boxItem.children.row,
                    fySize: boxItem.children.column,
                    fboxTypeId: boxItem.children.boxTypeId,
                    fholeTotalCount: boxItem.children.row * boxItem.children.column
                  }
                  box = {
                    ...box,
                    ...hole
                  }
                }
                shelf.boxList.push(box)
              })
            }
            floor.shelfList.push(shelf)
          })
        }
        floorList.push(floor)
      })
      data.floorList = floorList
      this.$ajax({
        url: '/sample/container/new_save_container',
        data: data,
        loadingDom: 'body'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.showSetContainer = false
          if (this.$route.path === '/business/sub/modifyContainer') {
            let timer = setInterval(() => {
              this.closeTime--
              if (this.closeTime === 0) {
                this.toContainerDetailPage()
                clearInterval(timer)
              }
            }, 1000)
          } else {
            let timer = setInterval(() => {
              this.closeTime--
              if (this.closeTime === 0) {
                this.closePage()
                clearInterval(timer)
              }
            }, 1000)
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 通过判断lab改变文字显示
    changeText (text) {
      let result = text
      if (this.formSubmit.type === 'C') {
        switch (text) {
          case '层':
            result = '组'
            break
          case '架':
            result = '抽屉'
            break
          case '盒':
            result = '道'
            break
        }
      }
      return result
    },
    closePage () {
      window.close()
    },
    toContainerDetailPage () {
      this.$router.push('/business/sub/containerDetail')
    }
  }
}
</script>

<style scoped lang="scss">
  .main-content{
    $border: 1px solid #ccc;
    $border-radius: 10px;
    .path{
      .title{
        line-height: 40px;
        background: #409EFF;
        padding-left: 30px;
        font-size: 16px;
        font-weight: 600;
        color: #fff;
        border: $border;
      }
      .content{
        padding: 30px;
        border: $border;
        /*border-right: $border;*/
        .setting-container{
          width: 100%;
          display: flex;
          justify-content: space-between;
          .setting-model{
            width: 220px;
            margin-right: 20px;
            .box{
              /*width: 200px;*/
              margin-top: 20px;
              .floor-item{
                background: $color;
                opacity: 0.7;
                width: 100%;
                border-radius: 4px;
                color: #fff;
                text-align: center;
                margin: 0 auto 20px auto;
                cursor: pointer;
                p{
                  line-height: 2;
                }
              }
              /*.hole-container{
                padding: 10px;
                background: #ccc;
                .hole-row{
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 15px;
                  .hole{
                    width: 20px;
                    height: 20px;
                    background: $color;
                    border-radius: 50%;
                  }
                }
              }*/
              .hole-container{
                padding: 10px;
                background: #EBEEF5;
                overflow-x: auto;
                .hole-row{
                  display: flex;
                  margin-bottom: 15px;
                  & > div{
                    flex-shrink: 0;
                  }
                  .x-text{
                    width: 20px;
                    margin-right: 20px;
                    font-size: 14px;
                    text-align: center;
                  }
                  .y-text{
                    font-size: 14px;
                    margin-right: 10px;
                    width: 1em;
                  }
                  .hole{
                    width: 20px;
                    height: 20px;
                    background: $color;
                    border-radius: 50%;
                    margin-right: 20px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .scroll-container{
    height: 400px;
    overflow-x: hidden;
  }
</style>
