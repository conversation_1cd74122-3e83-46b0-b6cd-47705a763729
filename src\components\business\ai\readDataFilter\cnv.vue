<template>
  <div>
    <div class="card-wrapper">
      <el-table
        :data="tableData"
        ref="table"
        class="table"
        size="mini"
        border
        height="calc(100vh - 40px - 154px - 20px - 20px)"
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column show-overflow-tooltip label="基因名称" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="核苷酸改变" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="氨基酸改变" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="变异意义" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="基因变异解析" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="遗传性肿瘤综合征" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="遗传方式" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="参考序列" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="功能区域" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="纯合/杂合" prop="desc" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="遗传几率" prop="desc" min-width="120px"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

export default {
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
