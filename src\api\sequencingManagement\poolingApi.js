import { myAjax } from '@/util/ajax'

/**
 * 获取pooling列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205697&itf=5799122
 * @param options
 * @returns {*}
 */
export function getPoolingList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/pooling/query_data',
    data: data,
    ...options
  })
}

/**
 * 下载任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799132
 * @param options
 * @returns {*}
 */
export function downloadTask (data, options = {}) {
  return myAjax({
    url: '/experiment/pooling/export_task',
    responseType: 'blob',
    data: data,
    ...options
  })
}
