<template>
  <div class="wrapper desc">
    <div class="card-wrapper desc">
      <div :style="`line-height: ${rowHeight}px`" class="box">
        <div :style="`height: ${rowHeight}px`" class="box-floor">
          <div class="box-title box-column">质控指标</div>
          <div class="box-title box-column">质控结果</div>
          <div class="box-title box-column">质控标准</div>
        </div>
        <div :key="index" :style="`height: ${val[1].length * rowHeight}px`" v-for="(val, index) in reportQcResult" class="box-floor">
          <div class="box-column" style="display: flex;">
            <div :style="`line-height: ${val[1].length * rowHeight}px`" class="box-column box-title">{{val[0]}}</div>
            <div class="box-column">
              <div :key="i"
                   :style="`height: ${rowHeight}px; min-width: 200px;`"
                   v-for="(item, i) in val[1]"
                   class="box-floor">
                {{item.dictValue}}
              </div>
            </div>
          </div>
          <template >
            <div class="box-column">
              <div :key="i" :style="`height: ${rowHeight}px`" v-for="(item, i) in val[1]" class="box-floor"> {{item.qcResult}}</div>
            </div>
            <div class="box-column">
              <div :key="i" :style="`height: ${rowHeight}px`" v-for="(item, i) in val[1]" class="box-floor"> {{item.qcStandard}}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="card-wrapper desc" style="margin-top: 20px;">
      <el-descriptions :label-style="{width: '120px'}" :column="2" class="margin-top" title="Insert Size质控" size="mini" border>
        <el-descriptions-item label="Insert Size峰值">{{insertSize}}</el-descriptions-item>
        <el-descriptions-item label="质控结果">{{insertSizeResult}}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="card-wrapper desc" style="margin-top: 20px;">
      <img-info :imgs="imgs" :analysis-rs-id="analysisRsId" @uploadFileSuccessEvent="getData"></img-info>
    </div>
  </div>
</template>

<script>
import imgInfo from '../common/imgInfo'
export default {
  components: {
    imgInfo
  },
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      rowHeight: 48,
      reportQcResult: new Array(7).fill({}),
      insertSize: '',
      insertSizeResult: '',
      imgs: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_qc_result',
        loadingDom: '.card-wrapper',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const reportQcResult = data.reportQcResult || {}
        this.reportQcResult = []
        let qcResult = new Map()
        reportQcResult.forEach(v => {
          let value = qcResult.get(v.description) || []
          qcResult.set(v.description, [v, ...value])
          this.reportQcResult = qcResult
        })
        this.imgs = []
        if (data.img) this.imgs.push('data:image/png;base64,' + data.img)
        this.insertSize = data.insertSize
        this.insertSizeResult = data.insertSizeResult
      }
    }
  }

}
</script>

<style scoped lang="scss">
.wrapper {
  .box {
    width: 100%;
    border: 1px solid #ccc;
    .box-title {
      text-align: center;
      font-weight: 700;
    }
    .box-floor {
      display: flex;
      border-bottom: 1px solid #ccc;
      justify-content: center;
      .box-column {
        flex: 1;
        border-right: 1px solid #ccc;
        text-align: center;
      }
      .box-column:last-child {
        border-right: none;
      }
    }
    .box-floor:last-child {
      border-bottom: none;
    }
  }
}

</style>
