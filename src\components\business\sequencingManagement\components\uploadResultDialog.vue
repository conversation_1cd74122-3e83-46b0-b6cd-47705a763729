<template>
  <el-dialog
    title="导入结果"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :width="dialogWidth"
    @open="handleOpen">
    <el-form v-if="!isUpload" ref="form" :model="form" label-suffix=":" label-width="70px">
      <div>
        <el-upload
          ref="upload"
          :auto-upload="false"
          :file-list="fileList"
          :action="uploadUrl"
          :data="uploadParams"
          :on-error="handleError"
          :before-upload="handleBeforeUpload"
          :on-change="handleChange"
          :on-success="handleOnSuccess"
          style="text-align: center;"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
            <br/>
            仅支持 xls、xlsx，只能上传1份小于10M的文件；
          </div>
        </el-upload>
      </div>
    </el-form>
    <div v-if="isUpload" class="result">
      <components ref="backFillInfo" :is="active" :type="type" :info="tableData"></components>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button v-if="!isUpload" :loading="loading" type="primary" size="mini" @click="handleNext">下一步</el-button>
      <el-button v-if="isUpload" :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>

import mixins from '../../../../util/mixins'
import constants from '../../../../util/constants'
import backFillResultInfo from './backFillResultInfoPooling'
import backFillResultInfoCyclization from './backFillResultInfoCyclization'
import backFillResultInfoTranslation from './backFillResultInfoTranslation'
import backFillResultInfoMakeDNB from './backFillResultInfoMakeDNB'
import {awaitWrap} from '../../../../util/util'
import {uploadUrls} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'uploadResultDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {
    backFillResultInfo,
    backFillResultInfoTranslation,
    backFillResultInfoCyclization,
    backFillResultInfoMakeDNB
  },
  props: {
    type: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      isUpload: false,
      active: 'backFillResultInfo',
      loading: false,
      fileList: [],
      uploadUrl: '',
      uploadParams: {},
      form: {},
      dialogWidth: '700px',
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      const components = {
        1: 'backFillResultInfo',
        2: 'backFillResultInfoTranslation',
        3: 'backFillResultInfoCyclization',
        4: 'backFillResultInfoMakeDNB'
      }
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
      this.loading = false
      this.active = components[this.type]
      this.uploadUrl = constants.JS_CONTEXT + uploadUrls(this.type)
      this.dialogWidth = '700px'
      this.isUpload = false
    },
    handleError () {
      this.loading = false
      this.$message.error('导入失败')
    },
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.loading = false
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.loading = false
        return false
      }
    },
    async handleChange (file, fileList) {
      if (fileList.length < 2) return
      const message = '一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？'
      const {err} = await awaitWrap(this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      err ? fileList.pop() : fileList.shift()
    },
    handleOnSuccess (res) {
      if (res && res.code === this.SUCCESS_CODE) {
        this.$refs.upload.clearFiles()
        this.isUpload = true
        this.dialogWidth = '98vw'
        this.tableData = res.data
      } else {
        this.$refs.upload.clearFiles()
        if (res && res.data) {
          this.$showSequencingErrorDialog({tableData: res.data, isShowButton: false})
        } else {
          this.$message.error(res.message)
        }
      }
      this.loading = false
    },
    // 下一步
    handleNext () {
      const fileList = this.$refs.upload.uploadFiles || []
      if (fileList.length < 1) {
        this.$message.error('请上传任务文件')
        return
      }
      this.loading = true
      this.$refs.upload.submit()
    },
    async handleConfirm () {
      this.loading = true
      try {
        const result = await this.$refs.backFillInfo.handleSubmit()
        if (result) {
          this.$emit('dialogConfirmEvent')
          this.visible = false
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-upload-dragger {
  width: 100%;
  height: 100%;
  padding: 20px;
}
/deep/ .el-upload {
  width: 100%;
  height: 100%;
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
}

</style>
