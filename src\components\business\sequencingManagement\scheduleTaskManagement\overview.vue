<template>
  <div class="wrapper">
    <div class="search">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="任务单号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入任务单号"></el-input>
        </el-form-item>
        <el-form-item label="进度范围">
          <el-cascader v-model.trim="form.progressRange" :value="form.progressRange" :options="rangeOption" separator=" - " clearable class="form-width"></el-cascader>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入实验样本"></el-input>
          </el-form-item>
        <el-form-item label="下单人">
          <el-input v-model.trim="form.orderPerson" class="form-width" clearable placeholder="请输入下单人"></el-input>
          </el-form-item>
      </el-form>
      <search-params-dialog
        :pvisible.sync="searchDialogVisible"
        @reset="handleReset"
        @search="handleSearch">
        <el-form
          ref="form"
          class="params-search-form"
          :model="form"
          label-width="80px"
          label-suffix=":"
          size="mini"
          label-position="top"
          inline>
          <el-form-item label="任务单号">
            <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入任务单号"></el-input>
          </el-form-item>
          <el-form-item label="进度范围">
            <el-cascader v-model.trim="form.progressRange" :value="form.progressRange" :options="rangeOption" separator=" - " clearable class="form-width"></el-cascader>
          </el-form-item>
          <el-form-item label="实验样本 ">
            <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入实验样本"></el-input>
          </el-form-item>
          <el-form-item label="下单人">
            <el-input v-model.trim="form.orderPerson" class="form-width" clearable placeholder="请输入下单人"></el-input>
          </el-form-item>
          <el-form-item label="下单时间">
            <el-date-picker
              v-model.trim="form.time"
              class="form-long-width"
              type="daterange"
              clearable
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </search-params-dialog>
    </div>
    <div class="btn-group">
      <el-button v-if="$setAuthority('021001001', 'buttons')" type="primary" class="fix-dropdown-margin" size="mini" @click="handleImportTask">导入任务</el-button>
      <el-button v-if="$setAuthority('021001002', 'buttons')" type="primary" plain size="mini" @click="handleDownloadTask">下载任务单</el-button>
      <el-button v-if="$setAuthority('021001003', 'buttons')" type="danger" plain size="mini" @click="handleReturnTask">撤 回</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" plain @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <div class="content-wrapper" :style="`height: calc(${tbHeight}px + 54px + 42px)`">
      <div v-show="taskWidth !== 0" :style="`width: ${taskWidth}%;`" class="task-list">
        <div class="title">任务列表</div>
        <el-table
          ref="table"
          :data="tableData"
          :cell-style="handleRowStyle"
          class="table"
          size="mini"
          border
          style="width: 100%"
          :height="tbHeight"
          @select="handleSelectTable"
          @row-click="handleRowClick"
          @select-all="handleSelectAll">
          <el-table-column type="selection" width="55" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column type="index" label="序号" width="50" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="任务单号" prop="code" min-width="160px" show-overflow-tooltip></el-table-column>
          <el-table-column label="任务单状态" prop="statusText" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="样本数量" prop="sampleTotal" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="排单数据量/G" prop="size" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="外来文库质控" prop="qcComplateNum" min-width="100px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.qcComplateNum === '-'">-</span>
              <span v-else class="link" @click="handleShowSampleList(scope.row.id, 0, scope.row.qcComplateNum)">{{scope.row.qcComplateNum}}</span>
            </template>
          </el-table-column>
          <el-table-column label="文库pooling1" prop="libPooling" min-width="100px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.libPooling === '-'">-</span>
              <span v-else class="link" @click="handleShowSampleList(scope.row.id, 1, scope.row.libPooling)">{{scope.row.libPooling}}</span>
            </template>
          </el-table-column>
          <el-table-column label="文库pooling2" prop="libPooling" min-width="100px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.libPooling2 === '-'">-</span>
              <span v-else class="link" @click="handleShowSampleList(scope.row.id, 5, scope.row.libPooling2)">{{scope.row.libPooling2}}</span>
            </template>
          </el-table-column>
          <el-table-column label="文库转化" prop="libTransformation" min-width="100px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.libTransformation === '-'">-</span>
              <span v-else class="link" @click="handleShowSampleList(scope.row.id, 2, scope.row.libTransformation)">{{scope.row.libTransformation}}</span>
            </template>
          </el-table-column>
          <el-table-column label="文库环化" prop="libLoop" min-width="100px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.libLoop === '-'">-</span>
              <span v-else class="link" @click="handleShowSampleList(scope.row.id, 3, scope.row.libLoop)">{{scope.row.libLoop}}</span>
            </template>
          </el-table-column>
          <el-table-column label="makeDNB" prop="makeDnb" min-width="100px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.makeDnb === '-'">-</span>
              <span v-else class="link" @click="handleShowSampleList(scope.row.id, 4, scope.row.makeDnb)">{{scope.row.makeDnb}}</span>
            </template>
          </el-table-column>
          <el-table-column label="总体进度" prop="totalProgress" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="测序平台" prop="platforms" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="测序类型" prop="seqType" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="下单人" prop="orderUserName" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="下单时间" prop="orderDateTime" min-width="100px" show-overflow-tooltip></el-table-column>
        </el-table>
        <div style="display: flex; align-items: center;font-size: 13px;">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh">
              <icon-svg icon-class="icon-refresh"/>
            </button>
          </el-pagination>
        </div>
      </div>
      <div class="btn">
        <el-button
          v-if="sampleWidth !== 0"
          type="primary"
          icon="el-icon-arrow-right"
          size="mini"
          @click="handleSetWidth(100, 0)">
        </el-button>
        <el-button
          v-if="taskWidth !== 0"
          type="primary"
          icon="el-icon-arrow-left"
          size="mini"
          @click="handleSetWidth(0, 100)">
        </el-button>
      </div>
      <div v-show="sampleWidth !== 0" :style="`width: ${sampleWidth}%`" class="sample-list">
        <sample-list ref="sampleList" :task-id="taskId"></sample-list>
      </div>
    </div>
    <uploader-task-dialog :pvisible.sync="uploadTaskVisible" @dialogConfirmEvent="handleSearch"></uploader-task-dialog>
    <sample-detail-dialog :pvisible.sync="sampleListVisible" :task-id="id" :work-flow="workFlow" :title="title"/>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import uploaderTaskDialog from './components/uploaderTaskDialog'
import sampleList from './sampleList'
import util, {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import {downloadTask, getTaskList, returnTask} from '../../../../api/sequencingManagement/sequencingManagementApi'
import SampleDetailDialog from '../components/sampleDetailDialog'

export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    SampleDetailDialog,
    sampleList,
    uploaderTaskDialog
  },
  mounted () {
    this.$_setTbHeight(90 + 24 + 47 + 54 + 10, '.search')
    this.setRangeOption()
    this.handleSearch()
  },
  data () {
    return {
      taskWidth: 50,
      sampleWidth: 50,
      hasScale: false,
      rangeOption: [],
      searchDialogVisible: false,
      uploadTaskVisible: false,
      sampleListVisible: false,
      taskId: null,
      id: null,
      title: '',
      props: {
        lazy: true,
        checkStrictly: true,
        lazyLoad (node, resolve) {
          const { level } = node
          const value = node.value || 0
          const nodes = Array.from({ length: 11 - value / 10 || 11 })
            .map((item, index) => ({
              value: index * 10 + value,
              label: `${index * 10 + value}%`,
              leaf: level >= 1
            }))
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          resolve(nodes || [])
        }
      },
      workFlow: null,
      form: {
        taskCode: '', // 任务编号
        progressRange: [], // 进度范围
        sampleName: '', // 样本名称
        orderPerson: '', // 下单人
        time: '' // 下单时间
      },
      formSubmit: {},
      tableData: []
    }
  },
  methods: {
    // 设置级联选择器数据
    setRangeOption () {
      this.rangeOption = new Array(11).fill({}).map((v, index) => {
        const item = {
          value: index * 10,
          label: `${index * 10}%`,
          children: []
        }
        item.children = Array.from({length: 11 - (item.value / 10)})
          .map((vv, index) => ({
            value: index * 10 + item.value,
            label: `${index * 10 + item.value}%`
          }))
        return item
      })
    },
    // 查询
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    // 重置
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        fcode: this.formSubmit.taskCode,
        fscheduleRangeStart: this.formSubmit.progressRange[0],
        fscheduleRangeEnd: this.formSubmit.progressRange[1],
        forderUserName: this.formSubmit.orderPerson,
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        forderDateTimeStart: time[0],
        forderDateTimeEnd: time[1],
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      const params = this.getParams()
      let {res} = await awaitWrap(getTaskList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.taskId = null
        this.tableData = []
        const rows = data.rows || []
        rows.forEach(v => {
          const statusList = ['未启用', '正常', '撤回']
          const item = {
            id: v.fid,
            code: v.fcode, // 任务单号
            status: v.fstatus, // 任务状态
            statusText: (v.fstatus || v.fstatus === 0) && statusList[v.fstatus], // 任务状态
            sampleTotal: v.fsampleTotal, // 样本量
            size: v.fsampleDataVolumeSize, // 排单数据量总和/G
            qcComplateNum: v.flibTestComplete, // 文库质检
            libPooling: v.flibPooling1, // 文库pooling
            libPooling2: v.flibPooling2, // 文库pooling
            libTransformation: v.flibTransform, // 文库转化
            libLoop: v.flibCyclize,
            makeDnb: v.flibMakeDnb,
            totalProgress: v.foverallProgress,
            platforms: v.fplatforms, // 平台
            seqType: v.fseqType, // 测序类型
            orderUserName: v.forderUserName, // 下单人
            orderDateTime: v.forderDateTime // 下单时间
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    handleRowStyle ({row}) {
      if (this.taskId === row.id) {
        return {backgroundColor: '#c7e1ff !important'}
      }
      return ''
    },
    // 点击行
    handleRowClick (row, c) {
      if (this.taskId === row.id) {
        this.taskId = null
        return
      }
      this.taskId = row.id
      this.$nextTick(() => {
        this.$refs.sampleList.handleReset()
      })
    },
    // 切换列表
    handleSetWidth (taskWidth, sampleWidth) {
      if (this.taskWidth === 0 || this.sampleWidth === 0) {
        this.taskWidth = 50
        this.sampleWidth = 50
      } else {
        this.taskWidth = taskWidth
        this.sampleWidth = sampleWidth
      }
    },
    // 显示未完成样本列表
    handleShowSampleList (taskId, type, performance) {
      const performanceList = performance.split('/') || []
      if (performanceList[0] === performanceList[1]) {
        this.$message.error('无未完成样本！')
        return
      }
      const types = ['文库质控', '文库pooling1', '文库转化', '文库环化', 'makeDNB', '文库pooling2']
      this.sampleListVisible = true
      this.id = taskId
      this.workFlow = type
      this.title = `未完成${types[type]}样本`
    },
    // 导入任务单
    handleImportTask () {
      this.uploadTaskVisible = true
    },
    // 下载任务单
    async handleDownloadTask () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择要下载的任务单！')
        return
      }
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTask({fidList: [...this.selectedRows.keys()]}))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 撤回任务单
    async handleReturnTask () {
      // 可单选/多选任务单为“正常”状态的任务单点击【撤回】
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择要撤回的任务单！')
        return
      }
      const rows = [...this.selectedRows.values()]
      const isNormal = rows.every(item => item.status === 1)
      if (!isNormal) {
        this.$message.error('只能撤回状态为“正常”的任务单！')
        return
      }
      const message = '已回填结果的数据不会删除，其余未开始/处理中的任务单及数据将删除，删除后无法恢复，确认继续？'
      await this.$confirm(message, '撤回任务', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const {res} = await awaitWrap(returnTask({fidList: rows.map(item => item.id)}))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('撤回成功！')
        this.getData()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;
  .btn-group {
    margin-bottom: 10px;
  }
  .content-wrapper {
    display: flex;
    .title {
      font-size: 14px;
      font-weight: bold;
      color: #000;
      margin: 5px 0;
    }
    .task-list {
      border: 1px solid #eee;
      padding: 5px;
      overflow: hidden;
      transition: all 0.5s;
    }
    .sample-list {
      border: 1px solid #eee;
      padding: 5px;
      overflow: hidden;
      transition: all 0.5s;
    }
    .btn {
      display: flex;
      margin: 30vh 0;
      /deep/ .el-button {
        padding: 14px 0;
        min-width: 0;
      }
    }
  }
}
</style>
