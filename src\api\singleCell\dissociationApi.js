import { myAjax } from '@/util/ajax'

/**
 * 获取自动审核详情
 * @param data 包含fid的参数对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function getAutoAuditDetail (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/dissociation/get_verify_dissociation_result',
    data: data,
    ...options
  })
}

/**
 * 保存自动审核数据
 * @param data 审核数据对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function saveAutoAuditData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/dissociation/update_dissociation_agent_result',
    data: data,
    ...options
  })
}

/**
 * 计算细胞相关数据
 * @param data 包含体积等参数的对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function calculateCellData (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/singleCell/dissociation/calculate_cell_data',
    data: data,
    ...options
  })
}

/**
 * 发送邮件通知
 * @param data 包含通知信息的对象
 * @param options 请求配置选项
 * @returns {*} 返回请求Promise
 */
export function sendEmailNotification (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/singleCell/dissociation/send_email_notification',
    data: data,
    ...options
  })
}
