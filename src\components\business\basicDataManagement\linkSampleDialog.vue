<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="关联样本"
      width="80%"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-table
        :data="tableData"
        border
        class="table"
        size="mini"
        height="400">
        <el-table-column type="index" label="序号" width="70"></el-table-column>
        <el-table-column prop="sampleNum" min-width="100" label="样例编号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneSampleNum" min-width="100" label="样本编号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="cancerName" min-width="100" label="癌种" show-overflow-tooltip></el-table-column>
        <el-table-column prop="hospital" min-width="100" label="送检单位" show-overflow-tooltip></el-table-column>
        <el-table-column prop="doctor" min-width="100" label="送检医生" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" min-width="120" label="产品/项目名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="currentStep" min-width="100" label="检测环节" show-overflow-tooltip></el-table-column>
        <el-table-column prop="confrimTime" min-width="100" label="到样时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sendTime" min-width="100" label="送检时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reportTime" min-width="120" label="报告时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="testResult" min-width="100" label="检测结果" show-overflow-tooltip></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: String | Number
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    // 关联样本列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_sample_list',
        data: {
          fid: this.id
        },
        method: 'get'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              fid: v.fid,
              sampleNum: v.sampleNum,
              geneSampleNum: v.geneSampleNum,
              cancerName: v.cancarName,
              hospital: v.hospital,
              doctor: v.doctor,
              productName: v.productName,
              currentStep: v.currentStep,
              confrimTime: v.confrimTime,
              sendTime: v.sendTime,
              reportTime: v.reportTime,
              testResult: v.testResult
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped></style>
