<!--前三种：细菌、病毒、真菌-->
<template>
  <div>
    <el-table
        ref="table"
        border
        :data="tableData"
        style="width: 100%"
        :cell-style="handleCheckCell"
        :height="'calc(100% - 32px)'"
        class="dataFilterTable"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick"
        :row-class-name="tableRowClassName"
    >
      <el-table-column fixed align="right" type="index" width="45"/>
      <el-table-column fixed align="center" type="selection" width="45"/>
      <el-table-column fixed :align="getColumnType['report']" prop="report" label="是否报出" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['confirmed']" prop="confirmed" label="报出参考" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['ARG_Name']" prop="ARG_Name"
                       label="耐药基因亚型" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button class="underline" type="text" @click.stop="handleShowImg(scope.row.realData.fdbSeq)">
            <div class="clamp">
              {{ scope.row[scope.column.property] }}
            </div>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column fixed :align="getColumnType['ARG_Gene']" prop="ARG_Gene" label="耐药基因"
                       width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['DrugClass']" prop="DrugClass" label="耐药类别"
                       width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['DrugResistanceMerchanism']" prop="DrugResistanceMerchanism"
                       label="耐药机制" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['AssociatedMicroorganisms']" prop="AssociatedMicroorganisms"
                       label="关联物种" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Target_Reads']" prop="Target_Reads"
                       label="Target_Reads" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Uniq_Target_Reads']" prop="Uniq_Target_Reads"
                       label="Uniq_Target_Reads" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['SDSMRN']" prop="SDSMRN"
                       label="SDSMRN" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Target_RPMCR']" prop="Target_RPMCR"
                       label="Target_RPMCR" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Uniq_Target_RPMCR']" prop="Uniq_Target_RPMCR"
                       label="Uniq_Target_RPMCR" width="180" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Target_rRPMCR']" prop="Target_rRPMCR"
                       label="Target_rRPMCR" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['RPKM']" prop="RPKM"
                       label="RPKM" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Uniq_RPKM']" prop="Uniq_RPKM"
                       label="Uniq_RPKM" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['logRPKM']" prop="logRPKM"
                       label="logRPKM" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Gene_Reads']" prop="Gene_Reads"
                       label="Gene_Reads" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['History_NTC_Info']" prop="History_NTC_Info"
                       label="History_NTC_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['FoldChange_Info']" prop="FoldChange_Info"
                       label="FoldChange_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['History_Case_Info']" prop="History_Case_Info"
                       label="History_Case_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['RUN_Case_Info']" prop="RUN_Case_Info"
                       label="RUN_Case_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Ave_Depth']" prop="Ave_Depth"
                       label="Ave_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Med_Depth']" prop="Med_Depth"
                       label="Med_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Mode_Depth']" prop="Mode_Depth"
                       label="Mode_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['1X_Coverage']" prop="1X_Coverage"
                       label="1X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['2X_Coverage']" prop="2X_Coverage"
                       label="2X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['5X_Coverage']" prop="5X_Coverage"
                       label="5X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['10X_Coverage']" prop="10X_Coverage"
                       label="10X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['UniformityT02M']" prop="UniformityT02M"
                       label="Uniformity(T_0.2_M)" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['UniformityT05M']" prop="UniformityT05M"
                       label="Uniformity(T_0.5_M)" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Dispersion(CV)']" prop="Dispersion(CV)"
                       label="Dispersion(CV)" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['freasonOfFilter']" prop="freasonOfFilter"
                       label="Reason_of_Filter" width="160" show-overflow-tooltip/>
    </el-table>
    <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        :total="totalPage">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh"/>
      </button>
    </el-pagination>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util from '@/util/util'
import {getTypeArray} from '../../../../../../util/util'

export default {
  name: 'DrugResistantGene',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    type: {
      type: String
    },
    refresh: {
      type: Boolean
    }
  },
  mounted () {
    this.getData()
    // 处理拉动列名时，fix列的行位置与非fix列的行位置错位
    const table = document.querySelector('.el-table')
    table.addEventListener('click', async (e) => {
      await this.$nextTick()
      this.$refs.table.doLayout()
    })
  },
  beforeDestroy () {
    const table = document.querySelector('.el-table')
    if (table) {
      table.removeEventListener('click', async (e) => {
        await this.$nextTick()
        this.$refs.table.doLayout()
      })
    }
  },
  watch: {
    refresh: {
      handler (newVal) {
        if (newVal) {
          this.getData()
        }
      }
    }
  },
  data () {
    return {
      tableData: [],
      selectedRows: new Map(),
      getColumnType: []
    }
  },
  methods: {
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/get_drug_resistance_gene',
        data: {
          analysisId: this.$route.query.oxym,
          current: this.currentPage + '',
          size: this.pageSize + ''
        },
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        let rows = data.rows || []
        this.totalPage = data.total
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            report: v.freport,
            confirmed: v.fconfirmed,
            ARG_Name: v.fname,
            ARG_Gene: v.fgene,
            DrugClass: v.fdrugClass,
            DrugResistanceMerchanism: v.fdrugResistanceMerchanism,
            AssociatedMicroorganisms: v.fassociatedMicroorganisms,
            Target_Reads: v.ftargetReads,
            Uniq_Target_Reads: v.funiqTargetReads,
            SDSMRN: v.fsdsmrn,
            Target_RPMCR: v.ftargetRpmcr,
            Uniq_Target_RPMCR: v.funiqTargetRpmcr,
            Target_rRPMCR: v.ftargetRrpmcr,
            RPKM: v.frpkm,
            Uniq_RPKM: v.funiqRpkm,
            logRPKM: v.flogRpkm,
            Gene_Reads: v.fgeneReads,
            History_NTC_Info: v.fhistoryNtcInfo,
            FoldChange_Info: v.ffoldChangeInfo,
            History_Case_Info: v.fhistoryCaseInfo,
            RUN_Case_Info: v.frunCaseInfo,
            Ave_Depth: v.faveDepth,
            Med_Depth: v.fmedDepth,
            Mode_Depth: v.fmodeDepth,
            '1X_Coverage': v.f1xCoverage,
            '2X_Coverage': v.f2xCoverage,
            '5X_Coverage': v.f5xCoverage,
            '10X_Coverage': v.f10xCoverage,
            UniformityT02M: v.funiformity02tm,
            UniformityT05M: v.funiformity05tm,
            'Dispersion(CV)': v.fdispersionCv,
            fdbSeq: v.fdbSeq,
            freasonOfFilter: v.freasonOfFilter
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        this.getColumnType = getTypeArray(this.tableData)
      }
    },
    // 动态行样式
    handleCheckCell ({row}) {
      const id = row.id
      // 1.let rows = [...this.selectedRows.values()]
      // const isSelect = rows.some(v => v.id === id)
      // 2.this.selectedRows.has(id)
      return this.selectedRows.has(id) ? 'background: #ecf6ff' : ''
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
      this.$emit('handleRowClick', this.selectedRows)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
      this.$emit('handleSelect', this.selectedRows)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.$emit('handleSelectAll', this.selectedRows)
    },
    // 行样式
    tableRowClassName ({row, rowIndex}) {
      const obj = {
        Y: 'red',
        L: 'blue'
      }
      if (!row.report) return ''
      return obj[row.report]
    },
    // 链接图片
    handleShowImg (fdbSeq) {
      this.$emit('showImg', fdbSeq)
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table {
  .red {
    color: #EE3838FF;
  }

  .blue {
    color: #539fff;
  }
}

.underline {
  text-decoration: underline;
}
.clamp {
  width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
