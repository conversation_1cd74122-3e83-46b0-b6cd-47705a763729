<template>
  <div class="wrapper">
    <el-tabs v-model.trim="active" @tab-click="handleClick">
      <el-tab-pane label="处理中" name="2"></el-tab-pane>
      <el-tab-pane label="已完成" name="3"></el-tab-pane>
    </el-tabs>
    <transition name="fade">
      <keep-alive>
        <component :is="activeComponents"></component>
      </keep-alive>
    </transition>
  </div>
</template>

<script>
import processing from './processing'
import finished from './finished'

export default {
  name: 'index',
  components: {
    processing,
    finished
  },
  data () {
    return {
      activeComponents: 'processing',
      active: '2'
    }
  },
  methods: {
    handleClick () {
      const components = {
        2: 'processing',
        3: 'finished'
      }
      this.activeComponents = components[this.active]
    }
  }
}
</script>

<style scoped lang="scss">
.fade-enter,.fade-leave-to{
  opacity: 0;
  transition: all .5s;
}
.fade-enter-to,.fade-leave{
  opacity: 1;
  transition: all .5s;
}
.fade-enter-active,.fade-leave-active{
  transition: all .5s;
}
// 设置滑动过渡必须给每个组件设定宽度
.wrapper {
  width: 100%;
  overflow: hidden;
}
</style>
