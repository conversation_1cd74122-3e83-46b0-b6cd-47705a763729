<template>
  <div>
    <b class="title">体系SNV</b>
    <el-table
      :data="tableDataSnv"
      ref="table"
      border
      class="table"
      size="mini"
      height="35vh"
    >
      <el-table-column prop="geneSampleNum" min-width="100" label="样本编号"></el-table-column>
      <el-table-column prop="fisSelect" min-width="120" label="Is_Select"></el-table-column>
      <el-table-column prop="fcount" min-width="100" label="Count"></el-table-column>
      <el-table-column prop="fmrdSelect" min-width="100" label="MRDSelect"></el-table-column>
      <el-table-column prop="fgeneSymbol" min-width="120" label="Gene Symbol"></el-table-column>
      <el-table-column prop="fchgvs" min-width="120" label="cHGVS"></el-table-column>
      <el-table-column prop="fphgvs" min-width="120" label="pHGVS"></el-table-column>
      <el-table-column prop="ffunction" min-width="120" label="Function"></el-table-column>
      <el-table-column prop="fexinId" min-width="120" label="ExIn_ID"></el-table-column>
      <el-table-column prop="ftranscript" min-width="120" label="Transcript"></el-table-column>
      <el-table-column prop="fcaseAf" min-width="120" label="CaseAF"></el-table-column>
      <el-table-column prop="fpyCloneCluster" min-width="180" label="PycloneVI_Cluster"></el-table-column>
      <el-table-column prop="fctrlAf" min-width="120" label="ControlAF"></el-table-column>
      <el-table-column prop="fisTnb" min-width="120" label="isTNB"></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :total="totalPage"
      style="background-color: #ffffff;width: 350px;"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
  </div>
</template>

<script>
import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.tablePaginationCommonData],
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableDataSnv: [],
      pageSize: 20,
      pageSizes: [20, 50, 100]
    }
  },
  methods: {
    // snv列表数据
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_organize_analysis_snv_result',
        method: 'get',
        data: {
          fsarId: this.$route.query.fsarId,
          page: this.currentPage,
          rows: this.pageSize
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.totalPage = data.total
          let rows = data.records || []
          this.tableDataSnv = []
          rows.forEach(v => {
            let item = {
              geneSampleNum: v.geneSampleNum,
              fisSelect: v.fisSelect,
              fcount: v.fcount,
              fmrdSelect: v.fmrdSelect,
              fgeneSymbol: v.fgeneSymbol,
              fchgvs: v.fchgvs,
              fphgvs: v.fphgvs,
              ffunction: v.ffunction,
              fexinId: v.fexinId,
              ftranscript: v.ftranscript,
              fcaseAf: v.fcaseAf,
              fpyCloneCluster: v.fpyCloneCluster,
              fctrlAf: v.fctrlAf,
              fisTnb: v.fisTnb
            }
            item.realData = item
            util.setDefaultEmptyValueForObject(item)
            this.tableDataSnv.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    }

  }
}
</script>

<style scoped lang="scss">
.info-title {
  padding: 10px;
  background-color: #aaa;
  border: 1px solid #eee;
}
.title {
  height: 40px;
  line-height: 40px;
  padding: 10px;
}
</style>
