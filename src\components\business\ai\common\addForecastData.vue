<template>
  <el-card class="card-wrapper">
  <div class="page">
    <div class="header">
      <div class="title">
        新增遗传性变异
      </div>
      <div>
        <el-button :loading="saveLoading" type="primary" size="mini" @click="handleSave('0')">保存草稿</el-button>
        <el-button :loading="saveLoading" type="primary" size="mini" @click="handleSave('1')">确认提交</el-button>
      </div>
    </div>
    <div class="content">
      <div>
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%;" @tab-click="handleClick">
          <el-tab-pane label="基本信息" name="baseInfo"></el-tab-pane>
          <el-tab-pane label="药物解析(中文)" name="drugAnalysisChinese"></el-tab-pane>
<!--          <el-tab-pane label="药物解析(英文)" name="drugAnalysisEnglish"></el-tab-pane>-->
        </el-tabs>
      </div>
      <div style="height: 100%; overflow: auto; width: calc(100% - 140px - 70px); padding: 10px;">
        <el-form ref="form" :model="form" :rules="rules" label-width="130px" size="mini" label-position="right" label-suffix="：">
          <el-row :gutter="15">
            <el-card class="card-wrapper">
              <div v-show="activeName === 'baseInfo'">
              <el-col :span="8" class="minHeight">
                <el-form-item label="基因名称">
                  基因名称
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="参考序列" prop="gSequenceChanges">
                  <el-input v-model="form.gSequenceChanges" placeholder="请输入参考序列"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="药物名称" prop="gRenferenceSequence">
                  <el-input v-model="form.gRenferenceSequence" placeholder="请输入药物名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="药物名称(英文)" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入药物名称(英文)"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="肿瘤类型" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入肿瘤类型"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="药物状态" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入药物状态"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="疗效相关性" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入疗效相关性"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="核苷酸改变" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入核苷酸改变 "></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="氨基酸改变" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入氨基酸改变"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="证据等级" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入证据等级"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="minHeight">
                <el-form-item label="特殊变异" prop="gAminoAcidChanges">
                  <el-input v-model="form.gAminoAcidChanges" placeholder="请输入特殊变异"></el-input>
                </el-form-item>
              </el-col>
            </div>
              <div v-show="activeName === 'drugAnalysisChinese'">
              <el-col :span="24">
                <el-button type="primary" size="mini" @click="handleMatchData">数据匹配</el-button>
                <el-form-item label="药物状态" prop="clinicTrialReview">
                  <el-input v-model="form.clinicTrialReview" :autosize="{minRows: 10}" type="textarea" placeholder="请输入药物状态"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label-width="0">
                  <div>
                    <el-button type="primary" size="mini" @click="handleGetReferences">自动获取文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(0)">新增文献</el-button>
                    <el-button type="primary" size="mini" @click="handleEditReferences(1)">修改文献</el-button>
                    <el-button type="primary" size="mini" @click="handleSelectReferences">选择文献</el-button>
                    <el-button type="primary" size="mini" @click="handleDeleteReferences">移除</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(1)">上移</el-button>
                    <el-button type="primary" size="mini" @click="handleMove(0)">下移</el-button>
                  </div>
                  <el-table
                    ref="table"
                    :data="tableData"
                    size="mini"
                    height="300px"
                    class="referencesTable"
                    highlight-current-row
                    style="width: 100%"
                    @current-change="handleCurrentChange">
                    <el-table-column type="index" label="#"></el-table-column>
                    <el-table-column prop="rIdNameCombo" label="文献名称"></el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </div>
              <div v-show="activeName === 'drugAnalysisEnglish'">
              <el-col :span="24">
                <el-form-item label="药物解析(英文)" prop="clinicTrialReviewE">
                  <el-input v-model="form.clinicTrialReviewE" :autosize="{minRows: 10}" type="textarea" placeholder="请输入药物解析(英文)"></el-input>
                </el-form-item>
              </el-col>
            </div>
            </el-card>
          </el-row>
        </el-form>
      </div>
    </div>
    <save-references-dialog
      :pvisible="saveReferencesDialogVisible"
      :pdata="saveReferencesDialogData"
      @saveReferencesDialogCloseEvent="handleSaveReferencesDialogClose"
      @saveReferencesDialogConfirmEvent="handleSaveReferencesDialogConfirm"
    ></save-references-dialog>
    <search-references-dialog
      :pvisible="searchReferencesDialogVisible"
      @searchReferencesDialogCloseEvent="handleSearchReferencesDialogClose"
      @searchReferencesDialogConfirmEvent="handleSearchReferencesDialogConfirm"
    ></search-references-dialog>
    <match-data-dialog
      :pvisible="matchDataDialogVisible"
      :pdata="matchDataDialogData"
      @matchDataDialogCloseEvent="handleMatchDataDialogClose"
      @matchDataDialogConfirmEvent="handleMatchDataDialogConfirm"
    ></match-data-dialog>
  </div>
  </el-card>
</template>

<script>
import matchDataDialog from './matchDataDialog'
import saveReferencesDialog from './saveReferencesDialog'
import searchReferencesDialog from './searchReferencesDialog'
import obj from '@/util/mixins'
export default {
  name: 'addForecastData',
  mixins: [obj.tablePaginationCommonData],
  components: {saveReferencesDialog, searchReferencesDialog, matchDataDialog},
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {
    forecastData () {
      return this.$store.getters.getValue('forecastData')
    }
  },
  data () {
    return {
      saveLoading: false,
      activeName: 'baseInfo',
      form: {
        fid: '',
        gene: '',
        gSequenceChanges: '',
        gAminoAcidChanges: '',
        gRenferenceSequence: '',
        cancerKind: [
          {cancerTypeName: ''}
        ],
        gMutationType: '',
        evidenceLevel1: '',
        curativePertinence: '',
        variationType: '',
        drugName: '',
        drugNameE: '',
        drugStatus: '',
        operateDrug: '',
        clinicTrialReview: '',
        clinicTrialReviewE: '',
        flag: ''
      },
      optionsList: [
        {
          label: '',
          value: ''
        }
      ],
      selectedRow: '',
      tableData: [],
      mutationTypeList: [
        {
          label: 'Gain of Function',
          value: 'Gain of Function'
        },
        {
          label: 'Loss of Function',
          value: 'Loss of Function'
        },
        {
          label: 'No Function',
          value: 'No Function'
        },
        {
          label: '未知',
          value: '未知'
        }
      ],
      curativePertinenceList: [
        {
          label: '提示敏感',
          value: '提示敏感'
        },
        {
          label: '提示耐药或无效',
          value: '提示耐药或无效'
        },
        {
          label: '研究结论不一致',
          value: '研究结论不一致'
        },
        {
          label: '没有关联',
          value: '没有关联'
        },
        {
          label: '未知',
          value: '未知'
        }
      ],
      evidenceLevel1List: [
        {
          label: '1A',
          value: '1A'
        },
        {
          label: '1B',
          value: '1B'
        },
        {
          label: '1C',
          value: '1C'
        },
        {
          label: '1D',
          value: '1D'
        },
        {
          label: '2A',
          value: '2A'
        },
        {
          label: '2B',
          value: '2B'
        },
        {
          label: '2C',
          value: '2C'
        },
        {
          label: '2D',
          value: '2D'
        },
        {
          label: '3B',
          value: '3B'
        },
        {
          label: '3C',
          value: '3C'
        },
        {
          label: '3D',
          value: '3D'
        },
        {
          label: '4B',
          value: '4B'
        },
        {
          label: '4C',
          value: '4C'
        },
        {
          label: '4D',
          value: '4D'
        },
        {
          label: '5',
          value: '5'
        }
      ],
      operateDrugList: [
        {
          label: '是',
          value: '是'
        },
        {
          label: '否',
          value: '否'
        }
      ],
      rules: {
        gene: [
          {required: true, message: '请填写基因名称', trigger: 'blur'}
        ],
        variationType: [
          {required: true, message: '请填写变异类别', trigger: ['blur', 'change']}
        ],
        drugName: [
          {required: true, message: '请填写药物名称', trigger: 'blur'}
        ],
        cancerKind: [
          {required: true, message: '请填写癌种', trigger: 'blur'}
        ]
      },
      cancerRules: [
        {required: true, message: '请填写癌种', trigger: 'blur'}
      ],
      saveReferencesDialogVisible: false,
      saveReferencesDialogData: {},
      searchReferencesDialogVisible: false,
      matchDataDialogVisible: false,
      matchDataDialogData: {}
    }
  },
  methods: {
    getData () {
      this.form = Object.assign({}, this.form, this.forecastData)
    },
    handleSave (type) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.saveLoading = true
          let cancerKind = this.form.cancerKind.map(v => v.cancerTypeName)
          let rlibraryIds = this.tableData.map(v => v.rLibraryId)
          this.$ajax({
            url: '/read/unscramble/forecast_data_save_or_update',
            data: {
              fid: this.form.fid,
              gene: this.form.gene,
              gSequenceChanges: this.form.gSequenceChanges,
              gAminoAcidChanges: this.form.gAminoAcidChanges,
              evidenceLevel1: this.form.evidenceLevel1,
              gRenferenceSequence: this.form.gRenferenceSequence,
              gMutationType: this.form.gMutationType,
              curativePertinence: this.form.curativePertinence,
              variationType: this.form.variationType,
              drugName: this.form.drugName,
              drugNameE: this.form.drugNameE,
              drugStatus: this.form.drugStatus,
              operateDrug: this.form.operateDrug,
              cancerKind: cancerKind.join(','),
              clinicTrialReview: this.form.clinicTrialReview,
              clinicTrialReviewE: this.form.clinicTrialReviewE,
              flag: type,
              rlibraryIds: rlibraryIds.join(',')
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功！')
              this.$refs.form.resetFields()
              window.close()
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.saveLoading = false
          })
        } else {
          this.$message.error('必填项校验不通过')
        }
      })
    },
    handleClick (tab, event) {
      if (tab.name === 'drugAnalysisChinese') {
        this.selectedRow = ''
      }
    },
    handleAddCancer () {
      this.form.cancerKind.push({
        cancerTypeName: ''
      })
    },
    handleDeleteCancer (index) {
      this.form.cancerKind.splice(index, 1)
      if (this.form.cancerKind.length === 0) {
        this.handleAddCancer()
      }
    },
    handleCurrentChange (val) {
      this.selectedRow = val
    },
    handleAutoGetVariationType () {
      this.form.variationType = this.form.gAminoAcidChanges ? this.form.gAminoAcidChanges : this.form.gSequenceChanges ? this.form.gSequenceChanges : ''
    },
    handleMatchData () {
      this.matchDataDialogData = {
        gene: this.form.gene,
        nucleotideMutation: this.form.gSequenceChanges,
        aminoAcidMutation: this.form.gAminoAcidChanges,
        drugName: this.form.drugName,
        mutationType: this.form.variationType,
        curativePertinence: this.form.curativePertinence,
        cancerClass: ''
      }
      this.matchDataDialogVisible = true
    },
    handleGetReferences () {
      this.$ajax({
        loadingDom: '.referencesTable',
        url: '/read/unscramble/auto_get_literature',
        method: 'get',
        data: {
          ftext: this.form.clinicTrialReview,
          associatedId: ''
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRow = ''
          this.tableData = result.data.rows
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleEditReferences (type) {
      if (type === 0) {
        this.saveReferencesDialogData = {
          libraryId: '',
          referenceType: '',
          referenceId: '',
          referenceName: ''
        }
        this.saveReferencesDialogVisible = true
      } else {
        if (this.selectedRow) {
          let row = this.selectedRow
          this.saveReferencesDialogData = {
            libraryId: row.rLibraryId,
            referenceType: row.rClassified,
            referenceId: row.rId,
            referenceName: row.rName
          }
          this.saveReferencesDialogVisible = true
        } else {
          this.$message.error('请选择数据')
        }
      }
    },
    handleSelectReferences () {
      this.searchReferencesDialogVisible = true
    },
    handleDeleteReferences () {
      if (this.selectedRow) {
        let index = this.tableData.findIndex(v => v.rLibraryId === this.selectedRow.rLibraryId)
        this.tableData.splice(index, 1)
        this.selectedRow = ''
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleMove (type) {
      if (this.selectedRow) {
        let row = this.selectedRow
        if (type) {
          // 上移
          let index = this.tableData.findIndex(v => v.rLibraryId === row.rLibraryId)
          if (index !== 0) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index - 1, 0, row)
          }
        } else {
          // 下移
          let index = this.tableData.findIndex(v => v.rLibraryId === row.rLibraryId)
          if (index !== this.tableData.length - 1) {
            this.tableData.splice(index, 1)
            this.tableData.splice(index + 1, 0, row)
          }
        }
      } else {
        this.$message.error('请选择数据')
      }
    },
    handleSaveReferencesDialogClose () {
      this.saveReferencesDialogVisible = false
    },
    handleSaveReferencesDialogConfirm () {
      this.saveReferencesDialogVisible = false
      this.handleGetReferences()
    },
    handleSearchReferencesDialogClose () {
      this.searchReferencesDialogVisible = false
    },
    handleSearchReferencesDialogConfirm (data) {
      console.log(data)
      this.searchReferencesDialogVisible = false
      if (data.length !== 0) {
        let index = -1
        data.forEach(v => {
          index = this.tableData.findIndex(vv => {
            return vv.rLibraryId === v.rLibraryId
          })
          if (index === -1) {
            this.tableData.push(v)
          }
        })
      }
    },
    handleMatchDataDialogClose () {
      this.matchDataDialogVisible = false
    },
    handleMatchDataDialogConfirm (data) {
      this.matchDataDialogVisible = false
      this.form.clinicTrialReview = data
      this.handleGetReferences()
    },
    handleGetDrugNameE () {
      this.$ajax({
        method: 'get',
        url: '/read/unscramble/auto_get_drugname_english',
        data: {
          drugName: this.form.drugName
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.form.drugNameE = result.data
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    height: calc(100vh - 40px - 40px - 30px);
    .header{
      height: 58px;
      padding: 0 20px;
      border-bottom: 2px solid #E4E7ED;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title{
        font-size: 18px;
        font-weight: bold;
      }
    }
    .content{
      >>>.el-tabs__item{
        width: 140px;
        text-align: right;
      }
      .minHeight{
        min-height: 95px;
      }
      height: calc(100% - 60px);
      display: flex;
      overflow: auto;
    }
    >>>.el-table thead{
      font-size: 14px;
      font-weight: 500;
      color: #909399;
    }
    >>>.el-table th{
      background-color: rgba(0, 0, 0, 0);
    }
  }
</style>
