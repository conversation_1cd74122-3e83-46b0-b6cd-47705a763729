<template>
  <div>
    <el-form
      ref="detectInfoForm"
      :model="detectInfo"
      :rules="rules"
      :show-message="false"
      size="mini"
      label-position="right"
      label-width="110px"
      inline>
      <div class="module">
        <div class="module-title-bar" style="height: 25px;">
          <div>
            <p class="min-title">检测信息</p>
          </div>
        </div>

        <div class="content">

<!--          <el-form-item prop="automaticDetection" style="position: relative">-->
<!--            <template #label>-->
<!--              实验模式-->
<!--            </template>-->
<!--            <div style="display: flex" class="form-width">-->
<!--              <p v-if="readOnly">{{ experimentTypes[detectInfo.fexperimentType] }}</p>-->
<!--              <el-select v-model="detectInfo.automaticDetection" v-else class="form-width" placeholder="请选择（必填）">-->
<!--                <el-option label="是" value="是"></el-option>-->
<!--                <el-option label="否" value="否"></el-option>-->
<!--              </el-select>-->
<!--              <div style="display: inline-block; margin-left: 10px">-->
<!--                <el-tooltip class="item" effect="dark" placement="top-start">-->
<!--                  <div slot="content">-->
<!--                    <b>合格自动实验：</b>样本质控“合格”则自动开始后续实验，否则样本需要“申请检测”来确认开始后续实验<br/>-->
<!--                    <b>全部自动实验：</b>样本无论质控结果如何，均默认开始后续实验<br/>-->
<!--                    <b>确认后实验：</b>样本无论质控结果如何，均需要“申请检测”来确认开始后续实验<br/>-->
<!--                    <b>注：</b>“申请检测”需要在本系统“测序工厂样本管理 - 订单查询”模块选择订单后执行操作-->
<!--                  </div>-->
<!--                  <i class="el-icon-question"></i>-->
<!--                </el-tooltip>-->
<!--              </div>-->
<!--            </div>-->
<!--          </el-form-item>-->
          <!--          <el-form-item label="需要返样" prop="needToReturn">-->
          <!--            <p class="form-width" v-if="readOnly">{{detectInfo.needToReturn}}</p>-->
          <!--            <el-select v-else v-model="detectInfo.needToReturn" class="form-width" placeholder="请选择（必填）">-->
          <!--              <el-option label="是" value="是"></el-option>-->
          <!--              <el-option label="否" value="否"></el-option>-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item v-if="detectInfo.needToReturn === '是'" label="返样备注" prop="samplingRemarks">-->
          <!--            <p class="form-width" v-if="readOnly">{{detectInfo.samplingRemarks}}</p>-->
          <!--            <el-input-->
          <!--              v-else-->
          <!--              v-model.trim="detectInfo.samplingRemarks"-->
          <!--              placeholder="请输入"-->
          <!--              maxlength="100"-->
          <!--              class="form-width"></el-input>-->
          <!--          </el-form-item>-->
          <el-form-item label="实验类型" prop="samplesAvailable">
            <p v-if="readOnly" class="form-width">{{ detectInfo.labType || '-' }}</p>
          </el-form-item>
          <el-form-item v-if="detectInfo.labType === '上门实验'" label="上门实验类型" prop="samplesAvailable">
            <p v-if="readOnly" class="form-width">{{ detectInfo.inDoorLabType || '-' }}</p>
          </el-form-item>

          <el-form-item label="样本可用完" prop="samplesAvailable">
            <p v-if="readOnly" class="form-width">{{ detectInfo.samplesAvailable || '-' }}</p>
            <el-select v-model="detectInfo.samplesAvailable" v-else class="form-width" placeholder="请选择（必填）">
              <el-option label="是" value="是"></el-option>
              <el-option label="否" value="否"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="测序平台" prop="samplesAvailable">
            <p v-if="readOnly" class="form-width">{{ detectInfo.sequencingPlatform || '-' }}</p>
          </el-form-item>
          <el-form-item label="测序策略" prop="samplesAvailable">
            <p v-if="readOnly" class="form-width">{{ detectInfo.tactics || '-' }}</p>
          </el-form-item>
          <el-form-item label="交付方式" prop="deliveryMethod" style="margin-bottom: 0;">
            <p v-if="readOnly" class="form-width">{{ detectInfo.deliveryMethod }}</p>
            <el-select v-model="detectInfo.deliveryMethod" v-else class="form-width" placeholder="请选择（必填）">
              <el-option label="硬盘交付" value="硬盘交付"></el-option>
              <el-option label="云交付" value="云交付"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="detectInfo.deliveryMethod === '硬盘交付'" label="硬盘寄送地址"
                        prop="hardDriveShippingAddress" style="margin-bottom: 0;">
            <p v-if="readOnly" class="form-width">{{ detectInfo.hardDriveShippingAddress }}</p>
            <el-input v-model.trim="detectInfo.hardDriveShippingAddress" v-else maxlength="100"
                      class="form-width"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="module">
        <div class="module-title-bar" style="height: 25px;">
          <div>
            <p class="min-title">订单备注</p>
          </div>
        </div>
        <el-form-item label="订单备注">
          <p v-if="readOnly" class="form-width">{{ detectInfo.notes || '-' }}</p>
          <el-input
            v-model.trim="detectInfo.notes"
            v-else
            placeholder="请输入"
            maxlength="100"
            class="form-width"></el-input>
        </el-form-item>
      </div>
    </el-form>

    <div class="module">
      <div class="module-title-bar" style="height: 25px;">
        <div>
          <p class="min-title">注意事项</p>
        </div>
      </div>
      <div class="content tips">
        <p class="p">
          1、样品管上名称和信息单名称请务必保持一致，除名称以外，请勿将其他信息标记于管盖，以避免出现可能的识别错误。</p>
        <p class="p">
          2、请您务必认真填写表格中各项内容，标*为必填项。如果没有填写，将被视为无效样品；延误的时间将不被记录到项目周期内。</p>
        <p class="p">3、对人体有害或者可能造成实验室环境污染的生物源样本，原则上不予以接收，如确有需要，请一定提前联系实验室。</p>
        <p class="p">4、测序数据量：具体以合同签订为准，可以根据试剂需求调整，但增加数据量需要增加额外费用。</p>
        <p class="p">5、冻存样本以优先满足实验需求为主，剩余样本冻存，其余样本默认无剩余。</p>
      </div>
    </div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">寄送样本信息（{{ sampleInfoTable.length }}）</p>
        </div>
        <div v-if="!readOnly">
          <el-button size="mini" @click="handleDeleteParent">删除</el-button>
          <el-button type="primary" size="mini" @click="handleAdd('edit')">编辑</el-button>
          <el-button type="primary" size="mini" @click="handleAdd('add')">新增</el-button>
        </div>
      </div>
      <div class="content">
        <el-table
          ref="table"
          :data="sampleInfoTable"
          max-height="370px"
          border
          stripe
          class="computer-table"
          style="width: 100%"
          :header-cell-style="{'background': '#f2f2f2'}"
          :cell-style="obj => handleRowStyle(obj, 'splitSampleTable')"
          @select="(selection, row) => handleSelect(selection, row, 'table')"
          @select-all="(selection) => handleSelectAll(selection, 'table')"
          @row-click="(row, column, e) => handleRowClick(row, column, e, 'table')">
          <el-table-column type="selection" width="50" fixed></el-table-column>
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="吉因加编号" prop="geneplusNum" width="150" align="center"></el-table-column>
          <el-table-column prop="sampleCode" label="样本编号" width="150" align="center"></el-table-column>
          <el-table-column prop="speciesTypeName" label="物种类型" width="150" align="center"></el-table-column>
          <el-table-column prop="freferenceGene" label="参考基因组" width="150" align="center"></el-table-column>
          <el-table-column prop="organType" label="取样部位" width="150" align="center"></el-table-column>
          <el-table-column prop="tissueSampleType" label="组织样本类型" width="150" align="center"></el-table-column>
          <el-table-column prop="tissueSampleStatus" label="组织状态" width="150" align="center"></el-table-column>
          <el-table-column prop="getSampleType" label="取样方式" width="150" align="center"></el-table-column>
          <el-table-column prop="expectCellNum" label="期望捕获细胞数（合同签订）" width="150" align="center"></el-table-column>
          <template>
            <el-table-column prop="cDNADatasize" label="cDNA测序数据量/M" min-width="160" key="1-sampleType" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="foligoDataSize" label="oligo测序数据量/M" min-width="160" key="1-sampleType" show-overflow-tooltip></el-table-column>
            <el-table-column prop="BCRDatasize" label="BCR测序数据量/M" min-width="160" key="1-sampleType" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="TCRDatasize" label="TCR测序数据量/M" min-width="160" key="1-sampleType" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="ATACDatasize" label="ATAC测序数据量/M" min-width="160" key="1-sampleType" show-overflow-tooltip></el-table-column>
          </template>
          <el-table-column prop="productName" label="产品名称" width="150" align="center"></el-table-column>
          <el-table-column prop="labType" label="实验环节" width="150" align="center"></el-table-column>
          <el-table-column prop="statusName" label="开启状态" width="150" align="center">
          </el-table-column>
          <el-table-column prop="isBackupName" label="是否备份" width="150" align="center"></el-table-column>
          <el-table-column prop="notes" label="备注" width="150" align="center"></el-table-column>
        </el-table>
      </div>
    </div>

    <!--寄送样本表新增/修改弹窗-->
    <edit-send-sample-dialog
      :pvisible.sync="editSendSampleDialogVisible"
      :pdata="editSendSampleDialogData.form"
      :row-index="editSendSampleDialogData.rowIndex"
      :is-core-spun="isCoreSpunMachine"
      @dialogConfirmEvent="handleEditSendSampleDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../../../../../util/util'
import mixins from '../../../../../../../util/mixins'

import editSendSampleDialog from './editSendSampleDialog'

export default {
  name: 'entryLibraryInfo',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    editSendSampleDialog
  },
  props: {
    readOnly: Boolean,
    orderId: String | Number,
    pageType: String | Number,
    onlineForm: Object // 线上的数据，之前前端填好了的
  },
  mounted () {
    if (this.orderId) {
      this.getData()
    }
  },
  watch: {
    onlineForm: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.detectInfo = {...newVal}
        }
      },
      deep: true
    }
  },
  computed: {
    sampleInfoTableComputed () {
      this.setSampleData()
      let tableData = []
      this.sampleInfoTable.forEach((v, i) => {
        let item = {}
        if (v.childLibrary && v.childLibrary.length > 0) {
          v.childLibrary.forEach((vv, ii) => {
            item = {...v, ...vv, rowIndex: i + ';' + ii}
            tableData.push(item)
          })
        } else {
          item = {...v}
          tableData.push(item)
        }
      })
      return tableData
    },
    isCoreSpunMachine () { // 应用类型是否是包芯上机
      return this.detectInfo.applicationType === '客户自建文库包芯上机'
    }
  },
  data () {
    return {
      experimentTypes: {
        0: '合格自动实验',
        1: '全部自动实验',
        2: '确认后实验'
      },
      realComponentTableDataName: 'sampleInfoTable',
      rowHeight: '40px',
      detectInfo: {
        automaticDetection: '', // 自动检测
        needToReturn: '', // 需要返样
        samplingRemarks: '', // 返样备注
        samplesAvailable: '', // 样本可用完
        notes: '', // 备注
        deliveryMethod: '', // 交付方式
        hardDriveShippingAddress: '' // 硬盘寄送地址
      },
      historyLibrarySampleDialogVisible: false,
      sampleTableType: 1, // 1: 寄送样本表 2：拆分样本表
      sampleNameList: [], // 寄送样本表的样本名称
      selectedRows: new Map(),
      splitSelectedRows: new Map(), // 拆分样本信息选中
      sampleInfoTable: [],
      editSendSampleDialogData: {
        form: null,
        rowIndex: null
      }, // 编辑数据，新建时传null,编辑时传正常数据
      editSendSampleDialogVisible: false,
      editSplitSampleDialogData: {
        form: null,
        rowIndex: null,
        sampleNameList: [] // 样本名称
      }, // 编辑数据，新建时传null,编辑时传正常数据
      importLibraryInfoDialogVisible: false,
      rules: {
        automaticDetection: [
          {required: true, message: '自动检测（必选）', trigger: 'change'}
        ], // 自动检测
        needToReturn: [
          {required: true, message: '需要返样（必选）', trigger: 'change'}
        ], // 需要返样
        samplingRemarks: [
          {required: true, message: '请输入返样备注', trigger: 'blur'}
        ], // 返样备注
        samplesAvailable: [
          {required: true, message: '样本可用完（必选）', trigger: 'change'}
        ], // 样本可用完
        notes: [
          {required: true, message: '请输入备注', trigger: 'blur'}
        ], // 备注
        deliveryMethod: [
          {required: true, message: '请选择交付方式', trigger: 'change'}
        ], // 交付方式
        hardDriveShippingAddress: [
          {required: true, message: '请输入硬盘寄送地址', trigger: 'change'}
        ] // 硬盘寄送地址
      }
    }
  },
  methods: {
    // 展示其他文案，当一个文案为 '其他'时，展示另外一个字段，用于只读模式
    showOtherText (firstFiled, otherFiled, keyWord = '其他') {
      let text = ''
      text = firstFiled === keyWord ? otherFiled : firstFiled
      return text !== 0 && !text ? '-' : text
    },
    // 获取文库样本表格数据
    getData () {
      let data = {
        orderId: this.orderId,
        type: this.pageType
      }
      data.pageVO = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$ajax({
        url: '/order/get_lib_or_tissue_list',
        data: data,
        loadingDom: '.computer-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.sampleInfoTable = []
          const statusMap = {
            0: '直接开启',
            1: '暂存'
          }
          const backUpMap = {
            0: '正常样本',
            1: '备份样本'
          }
          rows.forEach((v, i) => {
            let item = {
              urgent: v.fisUrgent,
              // isUrgent: v.fdeliveryType === '极致交付', // 是否可加急
              // fsampleCode: v.fsampleCode,
              sampleName: v.fname,
              key: v.fname,
              sampleCode: v.fname,
              geneplusNum: v.fgeneCode,
              speciesType: v.fspecies,
              speciesTypeName: v.fspecies,
              organType: v.forganType,
              tissueSampleType: v.ftissueSampleType,
              tissueSampleStatus: v.ftissueSampleState,
              getSampleType: v.fsamplingMethod,
              expectCellNum: v.fexpectedCellsNumber,
              dataSize: v.fdataSize,
              productName: v.fdetectType,
              labType: v.fexperimentalLink,
              foligoDataSize: v.foligoDataSize,
              cDNADatasize: v.fcdnaDataSize,
              BCRDatasize: v.fbcrDataSize,
              TCRDatasize: v.ftcrDataSize,
              ATACDatasize: v.fatacDataSize,
              freferenceGene: v.freferenceGene,
              status: v.fisStaging + '',
              isBackup: v.fisBackUp, // 是否为备份
              statusName: statusMap[v.fisStaging],
              isBackupName: backUpMap[v.fisBackUp],
              notes: v.fnote
            }
            if (!data.pageVO) {
              item.libraryFrontId = i // 前端Id,只有在编辑时有
            }
            this.sampleInfoTable.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 寄送样本新增/编辑数据
    handleAdd (type) {
      let index = null
      let row = null
      if (type === 'edit') {
        if (this.selectedRows.size === 0) {
          this.$message.error('请选择一条数据')
          return
        }
        if (this.selectedRows.size > 1) {
          this.$message.error('只能选择一条数据')
          return
        }
        let key = [...this.selectedRows.keys()][0]
        index = this.sampleInfoTable.findIndex(v => v.key === key)
        row = [...this.selectedRows.values()][0]
      }
      this.editSendSampleDialogData = {rowIndex: index, form: row ? {...row} : null}
      this.editSendSampleDialogVisible = true
    },
    // 寄送样本新增/编辑确认弹窗
    async handleEditSendSampleDialogConfirm (form, rowIndex, oldName, code) {
      let addRepeatName = this.sampleInfoTable.some(v => {
        return oldName
          ? form.sampleName !== oldName && form.sampleName === v.sampleName
          : form.sampleName === v.sampleName
      })
      if (addRepeatName) {
        this.$message.error(`同一个订单中的“样本名称”不允许重复，样本名称“${form.sampleName}”重复`)
        return
      }
      // let addRepeatCode = this.sampleInfoTable.some(v => {
      //   return code
      //     ? form.geneplusNum !== code && form.geneplusNum === v.geneplusNum && form.geneplusNum !== ''
      //     : form.geneplusNum === v.geneplusNum && form.geneplusNum !== ''
      // })
      // if (addRepeatCode) {
      //   this.$message.error(`同一个订单中的“吉因加编号”不允许重复，吉因加编号“${form.geneplusNum}”重复`)
      //   return
      // }
      form.key = form.sampleName // 给表格增加一个key值
      if (rowIndex !== null) {
        this.$set(this.sampleInfoTable, rowIndex, form)
      } else {
        this.sampleInfoTable.unshift({...form})
      }
      this.$refs.table.clearSelection()
      this.selectedRows.clear()
      this.editSendSampleDialogVisible = false
    },
    // 将sampleInfo转化为map
    changeTable (skipIndex) {
      let tableMap = new Map()
      this.sampleInfoTable.forEach((v, i) => {
        let key = v.geneplusNum +
          v.libraryName +
          v.species +
          v.fragmentSize +
          v.libraryType +
          v.baseBalance +
          v.samplingConcentration +
          v.samplingVolume +
          v.libraryNotes
        if (i !== skipIndex) {
          tableMap.set(key, util.deepCopy(v))
        }
      })
      return tableMap
    },
    // 删除寄送样本信息表数据
    delSampleInfo (list) {
      this.sampleInfoTable = this.sampleInfoTable.filter(v => {
        return !list.includes(v.sampleName)
      })
      this.selectedRows.clear()
    },
    // 删除父文库行
    async handleDeleteParent () {
      await this.$confirm(`已选寄送样本${this.selectedRows.size}条，是否确认删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let delSampleList = [...this.selectedRows.keys()]
      this.delSampleInfo(delSampleList)
      this.splitSampleTableData = this.splitSampleTableData.filter(vv => {
        return !delSampleList.includes(vv.sampleName)
      })
    },
    // 子文库唯一值key
    subLibraryKey (row) {
      return row.sampleName + '-' + row.subLibraryName
    },
    setSampleData () {
      let sampleMap = new Map()
      this.sampleInfoTable.forEach(v => {
        let item = {
          ...v,
          childLibrary: []
        }
        sampleMap.set(v.sampleName, item)
      })
      this.splitSampleTableData.forEach(vv => {
        if (sampleMap.has(vv.sampleName)) {
          sampleMap.get(vv.sampleName).childLibrary.push(vv)
        }
      })
    },
    // 校验
    validForm () {
      return new Promise((resolve, reject) => {
        this.$refs.detectInfoForm.validate(valid => {
          let msg = ''
          if (!valid) {
            msg = '存在必填字段未填写，请检查'
            reject(msg)
            return
          }
          if (this.sampleInfoTable.length === 0) {
            this.$alert('请填写寄送样本表信息', '提示', {
              confirmButtonText: '确定'
            })
            let m = '寄送样本未填写'
            reject(m)
            return
          }
          resolve()
        })
      })
    },
    // 根据关键字获取选中的表格参数，因为选中的方法是一样的，只有字段和ref不同
    getTableKey (key) {
      return {
        ref: key === 'table' ? 'table' : 'splitSampleTable',
        selectRowMap: key === 'table' ? this.selectedRows : this.splitSelectedRows
      }
    },
    // 点击行
    handleRowClick (row, c, e, tableName) {
      let {ref, selectRowMap} = this.getTableKey(tableName)
      this.$refs[ref].toggleRowSelection(row, !selectRowMap.has(row.key))
      this.handleSelect(undefined, row, tableName)
    },
    // 选中行
    handleSelect (selection, row, tableName) {
      let {selectRowMap} = this.getTableKey(tableName)
      selectRowMap.has(row.key) ? selectRowMap.delete(row.key) : selectRowMap.set(row.key, row)
    },
    // 全选
    handleSelectAll (selection, tableName) {
      let {selectRowMap} = this.getTableKey(tableName)
      selectRowMap.clear()
      selection.forEach((row) => {
        selectRowMap.set(row.key, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-radio {
  margin: 10px;
}

.el-table {
  /deep/ th {
    padding: 0;
    height: 30px;
  }

  /deep/ td {
    padding: 0;
    height: 30px;
  }
}

//.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>/deep/ .el-form-item__label:before,
//.el-form-item.is-required:not(.is-no-asterisk)>/deep/ .el-form-item__label:before{
//  content: none !important;
//}
.el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item {
  margin-bottom: 5px;
}

.childTableBg {
  background: red;
}

.title {
  margin-right: 30px;
  font-size: 20px;
  font-weight: 600;
}

.form-width {
  width: 250px;
}

.module {
  background: #fff;
  margin: 0;

  .module-title-bar {
    @extend .operateBar;
    height: 40px;

    .min-title {
      @extend .title;
      font-size: 16px;
    }
  }

  .content {
    padding: 0 20px;

    .p {
      font-size: 14px;
      padding: 2px 0;
    }

    .bold {
      font-weight: bold;
    }

    .text-indent {
      text-indent: 2em;
    }
  }
}

// 文库结构的样式
.structure-container {
  padding: 10px;
  font-size: 13px;

  .bg-gray {
    background: #D7D7D7;
  }

  .bg-yellow {
    background: #f59a23;
  }

  .bg-blue {
    background: #1890ff;
  }

  .nav-bar > span {
    margin-right: 20px;
  }

  .index-content {
    margin-top: 10px;

    .row-item {
      display: flex;

      .index {
        display: flex;
        color: #333;
        max-width: 100%;
        overflow-x: auto;
        margin-right: 10px;

        .main-index {
          display: flex;
          margin: 0 10px;
          @extend .bg-gray;

          p {
            padding: 0 8px;

            .index-num {
              padding: 0 3px;
              color: red;
              border-bottom: 1px solid #333;
            }
          }
        }
      }
    }
  }
}

.page {
  .left-one {
    ~ .left-one {
      position: absolute;
      height: 9%;
      width: 11%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    box-sizing: border-box;
    font-size: 26px;
    top: 12%;
    left: 6%;
    padding: 0 0 1px 2px;
  }

  .right-one {
    ~ .right-one {
      box-sizing: border-box;
    }
  }
}

.tips {
  max-height: 240px;
  background: #efefef;
  padding: 20px 40px !important;
  overflow: auto;
}

//.computer-table {
//  max-height:  360px;
//  overflow: auto;
//}
</style>
