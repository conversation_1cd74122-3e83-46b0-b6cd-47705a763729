<template>
  <div v-loading="loading">
    <div ref="cameraCtl" style="margin: 0 auto" :style="canvasStyle"></div>
  </div>
</template>

<script>

// import xx form 'xxx'
export default {
  name: 'KemeHSC',
  props: {
    canvasHeight: {
      type: Number,
      default: 353
    },
    canvasWidth: {
      type: Number,
      default: 650
    }
  },
  computed: {
    canvasStyle () {
      return {
        width: `${this.canvasWidth}px`,
        height: `${this.canvasHeight}px`
      }
    }
  },
  data () {
    return {
      loading: false,
      // 高拍仪参数
      getImageLoading: false,
      src: '',
      socket: null,
      isSocketConnect: false,
      lockReconnect: false,
      openFlagA: false,
      isOpenMainCamera: false,
      MainCanvas: '',
      MainContext: '',
      pMainShowStartX: 0,
      pMainShowStartY: 0,
      isMouseDown: false,
      pALastX: 0,
      pALastY: 0,
      pACurrentX: 0,
      pACurrentY: 0,
      MainCamCutMode: 0
    }
  },
  methods: {
    // 初始化
    init () {
      this.$refs.cameraCtl.innerHTML = ''
      this.webSocketConnect()
      this.initCanvas(this.$refs.cameraCtl, 0, 0, this.canvasWidth, this.canvasHeight)
    },
    // 关闭
    close () {
      try {
        if (this.isSocketConnect) {
          this.closeCamera() // 关闭摄像头
          this.isOpenMainCamera = false
          this.getImageLoading = false
          this.releaseSocketPro()
          this.socket.close()
          this.socket = null
          this.$refs.cameraCtl.innerHTML = ''
        }
      } finally {
        this.loading = false
      }
    },
    // 拍照
    getImage () {
      if (this.isSocketConnect) {
        this.takePhoto()
      } else {
        this.$message.error('未连接')
      }
    },
    // 放大
    zoomIn () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x03 // 放大
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      } else {
        this.$message.error('未连接')
      }
    },
    // 缩小
    zoomOut () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x04 // 缩小
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      } else {
        this.$message.error('未连接')
      }
    },

    // ---------------------下面是摄像头相关的函数-------------------
    webSocketConnect () {
      this.socket = new WebSocket('ws://localhost:22225')
      this.socket.binaryType = 'arraybuffer'
      this.loading = true
      try {
        this.socket.onopen = (event) => {
          // heartCheck.reset().start();
          this.isSocketConnect = true
          // if (isOpenMainCamera === false)
          // this.Cam_getDevCount()
          console.log('socket.onopen')
          this.openCamera()
          this.setFileType(1)
          this.loading = false
        }

        this.socket.onclose = (event) => {
          console.log('socket.onclose')
          // this.reconnect()
          this.isSocketConnect = false
          // $('TextInfo').value = 'websocket connect close!'
        }

        this.socket.onerror = (event) => {
          console.log(event)
          console.log('socket.onclose')
          this.isSocketConnect = false
          this.loading = false
          this.reconnect()
          // $('TextInfo').value = 'websocket connect error!'
        }

        this.socket.onmessage = (event) => {
          // heartCheck.reset().start();
          let rDataArr = new Uint8Array(event.data)
          if (rDataArr.length > 0) {
            if (rDataArr[0] === 0x55 && rDataArr[1] === 0x66) {
              // 摄像头数目返回
              if (rDataArr[2] === 0x50) {
                let devCount = rDataArr[3]
                let devNameBufLen = rDataArr.length - 4
                let devNameData = new Uint8Array(devNameBufLen)
                for (let i = 0; i < devNameBufLen; i++) {
                  devNameData[i] = rDataArr[4 + i]
                }
                // let AllCamName = uint8ArrayToString(devNameData);
                let str = this.byteToString(devNameData)
                let AllCamName = decodeURIComponent(str)
                let camName = []
                camName = AllCamName.split('|')
                this.getDevCountAndNameResultCB(devCount, camName)
              }
              // 摄像头开启状态返回
              if (rDataArr[2] === 0x01) {
                if (rDataArr[3] === 0x01) {
                  this.isOpenMainCamera = true
                  this.getCameraOnOffStatus(0)
                }
                if (rDataArr[3] === 0x03) {
                  this.isOpenMainCamera = false
                  this.getCameraOnOffStatus(1)
                }
              }
              // 拍照结果返回
              if (rDataArr[2] === 0x10) {
                let flag
                if (rDataArr[3] === 0x01) {
                  flag = 0
                }
                if (rDataArr[3] === 0x02) {
                  flag = 2
                }
                let imgpathLen = rDataArr[4] * 256 + rDataArr[5]
                if (imgpathLen === 0) {
                  let base64Len = rDataArr[6] * 65536 + rDataArr[7] * 256 + rDataArr[8]
                  let imgPathStr = ''
                  let base64Data = new Uint8Array(base64Len)
                  for (let i = 0; i < base64Len; i++) {
                    base64Data[i] = rDataArr[9 + imgpathLen + i]
                  }
                  let base64Str = this.uint8ArrayToString(base64Data)
                  this.getCaptureImgResultCB(flag, imgPathStr, base64Str)
                } else {
                  let base64Len = rDataArr[6] * 65536 + rDataArr[7] * 256 + rDataArr[8]
                  let pData = new Uint8Array(imgpathLen)
                  for (let i = 0; i < imgpathLen; i++) {
                    pData[i] = rDataArr[9 + i]
                  }
                  let str = this.byteToString(pData)
                  let imgPathStr = decodeURIComponent(str)

                  let base64Data = new Uint8Array(base64Len)
                  for (let i = 0; i < base64Len; i++) {
                    base64Data[i] = rDataArr[9 + imgpathLen + i]
                  }
                  let base64Str = this.uint8ArrayToString(base64Data)

                  this.getCaptureImgResultCB(flag, imgPathStr, base64Str)
                }
              }

              // 摄像头数据
              if (rDataArr[2] === 0xcc) {
                let ww = rDataArr[3] * 256 + rDataArr[4]
                let hh = rDataArr[5] * 256 + rDataArr[6]
                this.pMainShowStartX = rDataArr[7] * 256 + rDataArr[8]
                this.pMainShowStartY = rDataArr[9] * 256 + rDataArr[10]
                this.MainContext.clearRect(0, 0, this.MainCanvas.width, this.MainCanvas.height)
                let imgData = this.MainContext.createImageData(ww, hh)
                let dataNum = 0
                dataNum = dataNum + 11
                for (let i = 0; i < imgData.data.length; i += 4) {
                  imgData.data[i] = rDataArr[dataNum]
                  imgData.data[i + 1] = rDataArr[dataNum + 1]
                  imgData.data[i + 2] = rDataArr[dataNum + 2]
                  imgData.data[i + 3] = 255
                  dataNum = dataNum + 3
                }
                this.MainContext.putImageData(imgData, 0, 0)

                if (this.MainCamCutMode === 1) {
                  this.MainContext.strokeStyle = 'red' // 设置线条的颜色
                  this.MainContext.lineWidth = 2 // 设置线条的宽度
                  this.MainContext.beginPath() // 绘制直线
                  this.MainContext.rect(this.pALastX, this.pALastY, (this.pACurrentX - this.pALastX), (this.pACurrentY - this.pALastY))
                  this.MainContext.closePath()
                  this.MainContext.stroke()
                }
              }
            }
          }
        }
      } catch (ex) {
        alert('异常错误!')
      }
    },
    initCanvas (DivMainBox, mX, mY, mwidth, mheight) {
      if (this.MainCanvas) {
        this.MainCanvas = ''
      }
      // var DivMainBox = $("CameraCtl");
      if (mwidth !== 0 && mheight !== 0) {
        this.MainCanvas = document.createElement('canvas')
        // this.MainCanvas.style.border = 'solid 1px #A0A0A0'
        this.MainCanvas.id = 'MainCamCanvas'
        this.MainCanvas.width = mwidth
        this.MainCanvas.height = mheight
        this.MainContext = this.MainCanvas.getContext('2d')
        DivMainBox.appendChild(this.MainCanvas) // 添加画布
      }
    },
    openCamera (iCamNo, width, height) {
      this.MainCanvas = {
        width: 600,
        height: 600
      }
      this.pALastX = 0
      this.pALastY = 0
      this.pACurrentX = 0
      this.pACurrentY = 0

      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(12)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x01 // 打开摄像头
        aDataArray[3] = iCamNo
        aDataArray[4] = this.MainCanvas.width >> 8 & 0xff
        aDataArray[5] = this.MainCanvas.width & 0xff
        aDataArray[6] = this.MainCanvas.height >> 8 & 0xff
        aDataArray[7] = this.MainCanvas.height & 0xff
        aDataArray[8] = width >> 8 & 0xff
        aDataArray[9] = width & 0xff
        aDataArray[10] = height >> 8 & 0xff
        aDataArray[11] = height & 0xff

        this.sendBuffer(aDataArray.buffer)
      }
    },
    setFileType (filetype = 0) {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x28
        aDataArray[3] = filetype // 0为jpg格式
        if (filetype === 1) {
          aDataArray[3] = 2// png格式
        }
        if (filetype === 2) {
          aDataArray[3] = 3 // tif格式
        }
        if (filetype === 3) {
          aDataArray[3] = 4 // pdf格式
        }
        this.sendBuffer(aDataArray.buffer)
      }
    },
    stringToUint8Array (str) {
      let arr = []
      for (let i = 0, j = str.length; i < j; ++i) {
        arr.push(str.charCodeAt(i))
      }
      arr.push('\0')
      let tmpUint8Array = new Uint8Array(arr)
      return tmpUint8Array
    },
    uint8ArrayToString (fileData) {
      let dataString = ''
      for (let i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i])
      }
      return dataString
    },
    stringToByte (str) {
      let bytes = []
      let len, c
      len = str.length
      for (let i = 0; i < len; i++) {
        c = str.charCodeAt(i)
        if (c >= 0x010000 && c <= 0x10FFFF) {
          bytes.push(((c >> 18) & 0x07) | 0xF0)
          bytes.push(((c >> 12) & 0x3F) | 0x80)
          bytes.push(((c >> 6) & 0x3F) | 0x80)
          bytes.push((c & 0x3F) | 0x80)
        } else if (c >= 0x000800 && c <= 0x00FFFF) {
          bytes.push(((c >> 12) & 0x0F) | 0xE0)
          bytes.push(((c >> 6) & 0x3F) | 0x80)
          bytes.push((c & 0x3F) | 0x80)
        } else if (c >= 0x000080 && c <= 0x0007FF) {
          bytes.push(((c >> 6) & 0x1F) | 0xC0)
          bytes.push((c & 0x3F) | 0x80)
        } else {
          bytes.push(c & 0xFF)
        }
      }
      return bytes
    },
    byteToString (arr) {
      if (typeof arr === 'string') {
        return arr
      }
      let str = ''
      let _arr = arr
      let one = ''
      let v = false
      for (let i = 0; i < _arr.length; i++) {
        one = _arr[i].toString(2)
        v = one.match(/^1+?(?=0)/)
        if (v && one.length === 8) {
          let bytesLength = v[0].length
          let store = _arr[i].toString(2).slice(7 - bytesLength)
          for (let st = 1; st < bytesLength; st++) {
            store += _arr[st + i].toString(2).slice(2)
          }
          str += String.fromCharCode(parseInt(store, 2))
          i += bytesLength - 1
        } else {
          str += String.fromCharCode(_arr[i])
        }
      }
      return str
    },
    dataURItoBlob (base64Data) {
      // console.log(base64Data);//data:image/png;base64,
      let byteString
      if (base64Data.split(',')[0].indexOf('base64') >= 0) byteString = atob(base64Data.split(',')[1])// base64 解码
      else {
        byteString = unescape(base64Data.split(',')[1])
      }
      let mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0]// mime类型 -- image/png

      // let arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
      // let ia = new Uint8Array(arrayBuffer);//创建视图
      let ia = new Uint8Array(byteString.length)// 创建视图
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }
      let blob = new Blob([ia], {
        type: mimeString
      })
      return blob
    },
    getCaptureImgResultCB (flag, path, base64Str) {
      if (flag === 0) {
        const imgs = {
          base64: 'data:image/png;base64,' + base64Str,
          file: this.dataURItoBlob('data:image/png;base64,' + base64Str)
        }
        this.$emit('getPhotoResult', imgs)
        if (path === '') {
          console.log('拍照成功')
        } else {
          console.log('拍照成功，图片保存位置：' + path)
        }
      } else {
        console.log('拍照失败!')
      }
      this.getImageLoading = false
    },
    camPhoto (fileAddr) {
      if (this.MainCamCutMode === 1) {
        this.SetCutRect(this.pALastX, this.pALastY, (this.pACurrentX - this.pALastX), (this.pACurrentY - this.pALastY)) // 手动裁剪区域
      }
      console.log('camPhoto')
      console.log('fileAddr')
      this.captureImage('')
    },
    captureImage (fileAddr) {
      if (this.isSocketConnect) {
        this.getImageLoading = true
        // let pathArray = this.stringToUint8Array(fileAddr)
        if (fileAddr === '') {
          let packageCount = 1
          let len = 0
          let pindex = 0
          let totalLen = 12
          let aDataArray = new Uint8Array(totalLen)
          aDataArray[0] = 0x77
          aDataArray[1] = 0x88
          aDataArray[2] = 0x10
          aDataArray[3] = 0x00
          aDataArray[4] = len >> 16 & 0xff
          aDataArray[5] = len >> 8 & 0xff
          aDataArray[6] = len & 0xff
          aDataArray[7] = packageCount >> 8 & 0xff // 包总数
          aDataArray[8] = packageCount & 0xff // 包总数
          aDataArray[9] = 0 // 分包长度
          aDataArray[10] = pindex >> 8 & 0xff // 包序号
          aDataArray[11] = pindex & 0xff // 包序号
          console.log('无地址')
          console.log('pindex:' + pindex)
          console.log(aDataArray.buffer)
          this.sendBuffer(aDataArray.buffer)
        } else {
          let path = encodeURI(fileAddr)
          // console.log(path);
          let pathArray = this.stringToByte(path)
          console.log(pathArray)
          let len = pathArray.length
          console.log(len)

          let packageCount = 0
          let tmpLen = len
          console.log('tmpLen', tmpLen)
          while (tmpLen > 0) {
            tmpLen = tmpLen - 90
            packageCount++
          }

          console.log('packageCount:' + packageCount)

          let pindex = 0
          tmpLen = len
          console.log(tmpLen)
          while (tmpLen > 0) {
            tmpLen = tmpLen - 90

            if (tmpLen > 0) {
              let totalLen = 90 + 12
              let aDataArray = new Uint8Array(totalLen)
              aDataArray[0] = 0x77
              aDataArray[1] = 0x88
              aDataArray[2] = 0x10
              aDataArray[3] = 0x00
              aDataArray[4] = len >> 16 & 0xff
              aDataArray[5] = len >> 8 & 0xff
              aDataArray[6] = len & 0xff
              aDataArray[7] = packageCount >> 8 & 0xff // 包总数
              aDataArray[8] = packageCount & 0xff // 包总数
              aDataArray[9] = 90 // 分包长度
              aDataArray[10] = pindex >> 8 & 0xff // 包序号
              aDataArray[11] = pindex & 0xff // 包序号
              console.log('pindex:' + pindex)
              for (let i = 0; i < 90; i++) {
                aDataArray[12 + i] = pathArray[i + pindex * 90]
              }
              this.sendBuffer(aDataArray.buffer)
            } else {
              let totalLen = 90 + tmpLen + 12 // 此时tmpLen为负数，做加法运算
              console.log('totalLen', totalLen)
              let aDataArray = new Uint8Array(totalLen)
              aDataArray[0] = 0x77
              aDataArray[1] = 0x88
              aDataArray[2] = 0x10
              aDataArray[3] = 0x00
              aDataArray[4] = len >> 16 & 0xff
              aDataArray[5] = len >> 8 & 0xff
              aDataArray[6] = len & 0xff
              aDataArray[7] = packageCount >> 8 & 0xff // 包总数
              aDataArray[8] = packageCount & 0xff // 包总数
              aDataArray[9] = 90 + tmpLen // 分包长度
              aDataArray[10] = pindex >> 8 & 0xff // 包序号
              aDataArray[11] = pindex & 0xff // 包序号
              console.log('pindex:' + pindex)
              for (let i = 0; i < (90 + tmpLen); i++) {
                aDataArray[12 + i] = pathArray[i + pindex * 90]
              }
              this.sendBuffer(aDataArray.buffer)
            }
            pindex++
            this.toSleep(80)
          }
        }
      }
    },
    toSleep (milliSeconds) {
      console.log('sleep...')
      let startTime = new Date().getTime()
      while (new Date().getTime() < startTime + milliSeconds);
      console.log('sleep end')
    },
    closeCamera () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x02 // 关闭摄像头
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    releaseSocketPro () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(3)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0xFF
        this.sendBuffer(aDataArray.buffer)
      }
    },
    getCameraOnOffStatus (status) {
      if (status === 0) {
        console.log('设备开启成功')
      } else {
        console.log('设备开启失败!')
      }
    },
    bestSize () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x05 // 适合大小
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    trueSize () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x06 // 1:1
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    sendBuffer (buffer) {
      if (this.socket) {
        this.socket.send(buffer)
      }
    },
    takePhoto () {
      // let name = this.formatDate(new Date().getTime())
      // let obj = document.getElementById('FileType')
      // let path = 'D:\\' + name + '.jpg'
      // console.log('主摄像头拍照')
      this.camPhoto() // 主摄像头拍照
      // camPhoto("");  //主摄像头拍照
    }
  }
}
</script>

<style scoped>

</style>
