<template>
  <el-dialog
    :visible.sync="visible"
    :modal="true"
    v-drag-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
    title="样本入库"
    width="1200px"
    @open="handleOpen"
  >
    <div class="tips">
      <div>{{tips}}</div>
    </div>
    <vxe-table
      border
      resizable
      show-overflow
      :data="tableData"
      :edit-config="{trigger: 'click', mode: 'cell'}">
      <vxe-column type="seq" title="序号" width="60"></vxe-column>
      <vxe-column field="name" title="吉因加编号" :edit-render="{autofocus: '.vxe-input--inner'}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.name" type="text"></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="role" title="样本类型" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.role" type="text" placeholder="请输入昵称"></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="sex" title="入库管型" :edit-render="{}">
        <template #default="{ row }">
          <span>{{ formatSex(row.sex) }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model.trim="row.sex" transfer>
            <vxe-option v-for="item in sexList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column field="sex2" title="样本量" :edit-render="{}">
        <template #default="{ row }">
          <span>{{ formatMultiSex(row.sex2) }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model.trim="row.sex2" multiple transfer>
            <vxe-option v-for="item in sexList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column field="num6" title="存储温度" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.num6" type="number" placeholder="请输入数值"></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date12" title="入库备注" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input v-model.trim="row.date12" type="date" placeholder="请选择日期" transfer></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>

    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  name: 'sampleWarehouseDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      loading: false,
      action: '',
      form: {},
      tips: '',
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.tips = `勾选样本例数：3 ，库外例数：2，实际入库例数：2，请选择入库信息：`
    },
    handleConfirm () {}
  }
}
</script>

<style scoped>
.tips {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
