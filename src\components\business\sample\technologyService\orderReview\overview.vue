<template>
  <div class="page">
    <div>
      <div class="operate-btns-group">
        <el-button
          :loading="downloadOrderLoading"
          v-if="$setAuthority('018001001', 'buttons')"
          size="mini"
          type="primary"
          @click="handleDownloadOrder">{{ downloadOrderLoading ? '正在下载' : '订单下载' }}
        </el-button>
        <el-button
          :loading="exportLoading"
          v-if="$setAuthority('018001002', 'buttons')"
          size="mini"
          type="primary"
          plain
          @click="handleExport">{{exportLoading ? '正在导出' : '导出快递信息'}}</el-button>
        <el-button v-if="$setAuthority('018001008', 'buttons')" type="primary" size="mini" plain @click="handleEditCourier">修改快递单号</el-button>
        <el-button type="primary" size="mini" plain @click="handleTest(2)">申请检测</el-button>
        <el-button type="primary" size="mini" plain @click="handleShowSearch">查询</el-button>
        <el-button type="primary" size="mini" plain @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="content">
      <div>
          <el-table
            ref="table"
            :data="tableData"
            height="calc(100vh - 74px - 40px - 42px - 32px)"
            :cell-style="handleRowStyle"
            class="table"
            size="mini"
            border
            style="width: 100%"
            @select="handleSelectTable"
            @row-click="handleRowClick"
            @select-all="handleSelectAll"
            @filter-change="filterMethod"
          >
            <el-table-column type="selection" width="45" fixed="left"></el-table-column>
            <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
            <el-table-column :filters="filterList"
                             prop="sampleTag"
                             label="订单标签"
                             width="100"
                             show-overflow-tooltip
                             column-key="tag">
              <template slot-scope="scope">
                  <el-tag v-if="scope.row.isAutoDetect === '是'"  style="margin-right: 5px;"  size="mini">自动</el-tag>
                  <el-tag v-if="scope.row.isAutoDetect === '否'" style="margin-right: 5px;"  size="mini">非自动</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approvalStatusTest" label="订单状态" width="100">
              <template slot-scope="scope">
                <el-tooltip v-if="[4].includes(scope.row.status)" class="item" effect="dark" content="点击查看驳回说明" placement="top">
                  <p
                    class="link"
                    @click="handleOpenReasonDialog(scope.row.id)">{{scope.row.approvalStatusTest}}</p>
                </el-tooltip>
                <p v-else>{{scope.row.approvalStatusTest}}</p>
              </template>
            </el-table-column>
            <el-table-column prop="expressCode" label="快递单号" width="140" show-overflow-tooltip>
              <template slot-scope="scope">
            <span
              class="link"
              @click="handleToPreview(scope.row.expressCode)">{{scope.row.expressCode}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="expressStatus" label="快递状态" width="80" show-overflow-tooltip>
              <template slot-scope="scope">
                <span :style="`color: ${scope.row.color}`">{{scope.row.expressStatus}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="etfArrivalTime" label="预计送达时间" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <span :style="`color: ${scope.row.timeColor}`">{{scope.row.etfArrivalTime}}</span>
              </template>
            </el-table-column>
            <el-table-column label="订单编号" width="160" show-overflow-tooltip>
              <template slot-scope="scope">
                <span
                  class="link"
                  @click="handleCheck(scope.row.realData, 2)">
                  {{scope.row.orderNum}}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="orderTypeText" label="订单类型" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="productionArea" label="生产片区" width="80" show-overflow-tooltip></el-table-column>
            <el-table-column prop="testItems" label="产品名称" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="projectNum" label="项目编号" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="projectName" label="项目名称" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="customerName" label="客户姓名" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column prop="inspectionUnit" label="送检单位" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="sales" label="销售" min-width="80" show-overflow-tooltip></el-table-column>
            <el-table-column prop="submissionDate" label="提交日期" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="reviewer" label="审核人" min-width="80" show-overflow-tooltip></el-table-column>
            <el-table-column prop="reviewTime" label="审核时间" min-width="180" show-overflow-tooltip></el-table-column>
          </el-table>
        <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh">
              <icon-svg icon-class="icon-refresh"/>
            </button>
          </el-pagination>
        </div>

      </div>
    </div>
    <courier-number-dialog :pvisible.sync="expressVisible" :code="expressCode"></courier-number-dialog>
    <search-params-dialog
      :pvisible.sync="searchParamsDialogVisible"
      @dialogConfirmEvent="handleSearch"/>
    <reject-reason-dialog
      :order-id="orderId"
      :pvisible.sync="rejectReasonVisible"></reject-reason-dialog>
    <quality-control-results-dialog
      :pvisible.sync="qualityControlResultsDialogVisible"
      :order-code="qualityControlResultsDialogData.orderCode"
      :order-id="qualityControlResultsDialogData.orderId"
      :note="qualityControlResultsDialogData.note"
      :btn-type="qualityControlResultsDialogData.btnType"
      :type="qualityControlResultsDialogData.type"
      @dialogConfirmEvent="getData"/>
    <edit-courier-dialog
      :pvisible.sync="editCourierDialogVisible"
      :code="editCourierDialogData.code"
      :order-id="editCourierDialogData.orderId"
      :order-code="editCourierDialogData.orderCode"
      @dialogConfirmEvent="getData"/>
    <single-cell-quality-control-results-dialog
      :pvisible.sync="singleCellQualityControlResultsDialogVisible"
      :order-id="qualityControlResultsDialogData.orderId"
      :btn-type="qualityControlResultsDialogData.btnType"
      :type="qualityControlResultsDialogData.type"
      :note="qualityControlResultsDialogData.note"
      :is-third="qualityControlResultsDialogData.isThird"
      @dialogConfirmEvent="getData"/>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'
import util from '../../../../../util/util'
import searchParamsDialog from './searchParamsDialog'
import {commonData} from './commonData'
import constants from '../../../../../util/constants'
import RejectReasonDialog from './rejectReasonDialog'
import CourierNumberDialog from './courierNumberDialog'
import qualityControlResultsDialog from './qualityControlResultsDialog'
import editCourierDialog from './editCourierDialog'
import SingleCellQualityControlResultsDialog from './singleCellQualityControlResultsDialog'

export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData, commonData],
  components: {
    CourierNumberDialog,
    RejectReasonDialog,
    searchParamsDialog,
    qualityControlResultsDialog,
    editCourierDialog,
    SingleCellQualityControlResultsDialog
  },
  mounted () {
    this.initData()
  },
  computed: {
    username () {
      return this.$store.getters.getValue('userInfo').name
    }
  },
  data () {
    return {
      exportLoading: false,
      expressCode: '',
      expressVisible: false,
      rejectReasonVisible: false,
      editCourierDialogVisible: false,
      editCourierDialogData: {
        code: '',
        orderId: '',
        orderCode: ''
      },
      singleCellQualityControlResultsDialogVisible: false,
      orderId: null,
      iframeUrl: constants.IFRAME_URL, // 监听科服修改页面
      selectedRows: new Map(),
      qualityControlResultsDialogVisible: false,
      filterList: [
        { text: '自动', value: '是' },
        { text: '非自动', value: '否' }
        // { text: '院内', value: '院内' },
      ],
      qualityControlResultsDialogData: {
        orderId: '',
        orderCode: '',
        btnType: null,
        type: null,
        note: ''
      },
      colorOptions: {
        '全部': '#f2e800',
        '待揽收': '#ff4949',
        '运输中': '#539fff',
        '已揽收': '#3bc7c7',
        '已送达': '#13ce66',
        '已取消': '#606261'
      },
      tableData: [],
      downloadOrderLoading: false,
      tableHeight: 0,
      formSubmit: {}, // 提交的form
      searchParamsDialogVisible: false // 查询框visible
    }
  },
  methods: {
    initData () {
      let self = this
      window.addEventListener('message', function (event) {
        if (event.origin !== self.iframeUrl) {
          // 来自未知的源的内容，我们忽略它
          return
        }

        if (window === event.source) {
          // chrome 下, 页面初次加载后会触发一次 message 事件, event.source 是 window 对象
          // 此时 event.source.postMessage 会形成死循环
          // 因此，要跳过第一次的初始化触发的情况
          return
        }
        let msg = event.data
        console.log('close', msg)
        if (msg === 'close') {
          self.handleSearch()
        }
      })
    },
    // 预览物流信息
    handleToPreview (code) {
      this.expressCode = code
      this.expressVisible = true
    },
    /** *************************查询********************************/
    handleShowSearch () {
      this.searchParamsDialogVisible = true
    },
    setParams () {
      let arrivalSampleTime = this.formSubmit.arrivalSampleTime || []
      let reviewTime = this.formSubmit.reviewTime || []
      let etfArrivalTime = this.formSubmit.etfArrivalTime || []
      let data = util.getSessionInfo('currentLab') || []
      let options = {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      }
      data = data.map(v => {
        return options[v]
      })
      let expressCode = this.formSubmit.expressCode || ''
      if (this.formSubmit.expressCode) {
        expressCode = expressCode.replace(/，/g, ',').split(',')
      } else {
        expressCode = []
      }
      return {
        detectType: this.formSubmit.detectType,
        projectCode: this.formSubmit.projectCode,
        projectName: this.formSubmit.projectName,
        orderCode: this.formSubmit.orderCode,
        expressCode: expressCode,
        isAutoDetect: this.formSubmit.isAutoDetect,
        auditStatus: this.formSubmit.auditStatus || [],
        expressStatus: this.formSubmit.expressStatus || [],
        orderType: this.formSubmit.orderType || [],
        etfArrivalStartTime: etfArrivalTime[0],
        etfArrivalEndTime: etfArrivalTime[1],
        sendUnit: this.formSubmit.unit,
        arrivalSampleStartTime: arrivalSampleTime[0],
        arrivalSampleEndTime: arrivalSampleTime[1],
        reviewStartTime: reviewTime[0],
        reviewEndTime: reviewTime[1],
        area: data
      }
    },
    // 表格过滤
    filterMethod (value = []) {
      this.formSubmit.isAutoDetect = value.tag
      this.handleSearch({
        ...this.formSubmit
      })
    },
    handleSearch (data) {
      this.formSubmit = {...data}
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    getData () {
      let data = this.setParams()
      this.$ajax({
        url: '/order/get_order_audit_list',
        data: {
          ...data,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach((v) => {
            let item = {
              id: v.orderId,
              status: v.status,
              rejectReason: v.rejectReason,
              approvalStatusTest: this.statusOptions[v.status] || '',
              orderNum: v.orderCode,
              orderType: v.type,
              orderTypeText: this.typeOptions[v.type] || '',
              productionArea: v.area,
              color: this.colorOptions[v.expressStatus] || '',
              testItems: v.detectType, // 为组织或核酸订单时，不展示检测项目
              projectNum: v.projectCode,
              projectName: v.projectName,
              customerName: v.customerName,
              inspectionUnit: v.sendUnit,
              sales: v.sellerName,
              submissionDate: v.submitTime,
              note: v.fnote,
              reviewer: v.auditor,
              reviewTime: v.auditTime,
              expressCode: v.expressCode,
              expressStatus: v.expressStatus,
              etfArrivalTime: v.etfArrivalTime,
              isAutoDetect: v.isAutoDetect
            }
            item.timeColor = ''
            let now = new Date()
            if (item.etfArrivalTime) {
              let etfArrivalTime = new Date(item.etfArrivalTime)
              console.log(now, etfArrivalTime)
              if (item.status === 1) console.log(now.getTime() - etfArrivalTime.getTime())
              if (item.status === 1 && now.getTime() - etfArrivalTime.getTime() > 0) {
                item.timeColor = 'red'
              }
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.handleEchoSelect()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleReset () {
      this.$refs.table.clearFilter()
      this.form = {
      }
      this.handleSearch()
    },
    /** *************************查询********************************/
    // 申请检测
    async handleTest (btnType) {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择一条数据')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.error('只能选择一条数据')
        return
      }
      const row = [...this.selectedRows.values()][0] || {}
      if (![2, 6, 7, 8].includes(+row.status)) {
        const message = +row.orderType === 5
          ? '请选择订单状态为“已审核、已接收、质检中、生产中”，且存在暂存样本的订单！'
          : '请选择订单状态为“已审核、已接收、质检中、生产中”，且存在未下达检测任务且已质控的样本（或文库）！'
        this.$message.error(message)
        return
      }
      this.qualityControlResultsDialogData = {
        orderId: row.id,
        type: row.orderType,
        note: row.note,
        orderCode: row.orderNum,
        btnType: btnType
      }
      row.orderType === 5 ? this.singleCellQualityControlResultsDialogVisible = true : this.qualityControlResultsDialogVisible = true
    },
    // 导出订单
    handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要导出快递信息的数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      if (rows.some(v => v.realData.status !== 2)) {
        this.$message.error('存在订单状态不为已审核的订单')
        return
      }
      const orderIds = [...this.selectedRows.keys()]
      this.exportLoading = true
      this.$ajax({
        url: '/order/export_express_info_excel',
        data: {
          orderIds: orderIds.join(',')
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    // 修改快递单号
    handleEditCourier () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      const row = [...this.selectedRows.values()][0] || {}
      this.editCourierDialogData = {
        code: row.expressCode,
        orderId: row.id,
        orderCode: row.orderNum
      }
      this.editCourierDialogVisible = true
    },
    // 打开驳回说明弹窗
    handleOpenReasonDialog (orderId) {
      this.rejectReasonVisible = true
      this.orderId = orderId
    },
    // 下载
    handleDownloadOrder () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下载的数据')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.downloadOrderLoading = true
      this.$ajax({
        url: '/order/download_order',
        data: {
          orderIds: ids.toString()
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadOrderLoading = false
      })
    },
    // 查看 type 1编辑 2 只读
    handleCheck (row, type) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type, // type 1编辑 2 只读
          orderId: row.id,
          status: row.status,
          code: row.orderNum,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (row.orderType === 5) path = '/business/subpage/technologyService/singleCell'
      if (path) util.openNewPage(path)
    }
  }
}
</script>

<style scoped lang="scss">
.input-width {
  width: 150px;
}

.search-wrapper {
  background-color: #ffffff;
  display: flex;
  align-items: center;

  .search-row {
    display: flex;
    width: auto;
    margin-bottom: 20px;

    & > div {
      margin-right: 30px;

      label {
        font-size: 14px;
        color: #606266;
        display: inline-block;
        width: 100px;
        text-align: right;
        padding: 0 12px 0 0;
        box-sizing: border-box;
      }

      .input {
        width: 200px;
      }
    }
  }
}
.content{
  background-color: #ffffff;
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    //margin: 0 20px;
    >>>.el-button--mini{
      padding: 5px 10px;
    }
    .iconClass{
      padding-right: 5px;
      font-size: 16px;
    }
  }
}
>>>.el-pagination{
  padding: 7px 2em;
}
.link {
  color: $color;
  cursor: pointer
}
/*/deep/ .el-table__header .el-checkbox {*/
/*display: none;*/
/*}*/
</style>
