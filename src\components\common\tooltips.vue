<template>
  <el-tooltip :content="txtInfo" :disabled="isDisabled" placement="top" effect="dark">
    <div :class="{'link-text': isLink} " class="wordnowrap" @mouseenter="isShowTooltip" @click="linkTo">{{txtInfo}}</div>
  </el-tooltip>
</template>

<script>
export default{
  name: 'ShowTooltips',
  props: {
    txtInfo: '',
    textClass: {
      type: String,
      default: '',
      required: false
    },
    isLink: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      isDisabled: false
    }
  },
  methods: {
    isShowTooltip (e) {
      let clientWidth = e.target.clientWidth
      let scrollWidth = e.target.scrollWidth
      // let arrList = Array.from(e.target.classList)
      if (scrollWidth > clientWidth) {
        this.isDisabled = false
        // if (!arrList.includes('hover-blue')) {
        //   e.target.classList.add('hover-blue')
        // }
      } else {
        this.isDisabled = true
        // e.target.classList.remove('hover-blue')
      }
    },
    linkTo () {
      if (this.isLink) {
        this.$emit('linkTo')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wordnowrap{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.hover-blue:hover{
  color: $color;
}
.link-text{
  color: $color;
  cursor: pointer;
}
</style>
