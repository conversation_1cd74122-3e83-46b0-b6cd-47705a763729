<template>
  <div>
    <el-dialog
      title="异常登记"
      :close-on-click-modal="false"
      :visible.sync="visible"
      v-drag-dialog
      width="700px"
      :before-close="handleClose"
      @opened="handleOpen"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="mini"
        inline
        label-width="100px">
        <el-form-item label="异常原因">
          <div class="input-width">样本原因</div>
        </el-form-item>
        <el-form-item label="异常分类" prop="exceptionType">
          <el-select v-model="form.exceptionType" filterable placeholder="请选择" class="input-width" clearable>
            <el-option
              v-for="item in exceptionTypeOptions"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="异常描述" prop="exceptionNote">
          <el-input
            v-model="form.exceptionNote"
            :autosize="{minRows: 2}"
            type="textarea"
            rows="2"
            class="input-long-width"
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="异常文件" prop="exceptionFile">
          <el-upload
            ref="upload"
            auto-upload
            :action="action"
            :limit="1"
            :headers="headers"
            style="width: 100%"
            list-type="picture"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
            :before-upload="handleBeforeUpload"
            :on-error="handleUploadError"
          >
            <el-button size="mini" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button :loading="submitLoading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../util/mixins'
import constants from '../../../../util/constants'
import Cookies from 'js-cookie'
export default {
  name: 'ErrorDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: String,
    isOnlySingleOrder: Boolean
  },
  data () {
    return {
      action: constants.JS_CONTEXT + '/order/upload_file',
      submitLoading: false,
      loading: false,
      form: {
        exceptionType: '',
        exceptionNote: '',
        exceptionFile: '' // 上传的文件
      },
      rules: {
        exceptionType: [
          { required: true, message: '请选择异常类型', trigger: 'change' }
        ],
        exceptionNote: [
          { required: true, message: '请输入异常说明', trigger: 'blur' }
        ]
      },
      headers: {
        token: Cookies.get('token')
      },
      exceptionTypeOptions: ['到样温度异常', '样本外观异常', '采集容器错误', '送样量异常', '运输异常', '其他']
    }
  },
  methods: {
    handleOpen () {
      if (this.isOnlySingleOrder) {
        this.exceptionTypeOptions = ['到样温度异常', '样本外观异常', '采集容器错误', '送样量异常', '运输异常', '冰融', '超温', '超时', '管材不符', '其他']
      } else {
        this.exceptionTypeOptions = ['到样温度异常', '样本外观异常', '采集容器错误', '送样量异常', '运输异常', '其他']
      }
      this.$refs.form.resetFields()
      this.$refs.upload.clearFiles()
    },
    // 上传前校验文件名和文件大小
    handleBeforeUpload (file) {
      let name = file.name
      if (!/\.(png|jpg|jpeg)$/i.test(name)) {
        this.$message.error('只能上传PNG、JPG、JPEG文件')
        return false
      }
      this.loading = true
      return true
    },
    handleSuccess (response) {
      this.loading = false
      if (response.code === this.SUCCESS_CODE) {
        this.form.exceptionFile = JSON.stringify(response.data)
      } else {
        this.$message.error(response.message)
      }
    },
    handleRemove () {
      this.form.exceptionFile = ''
    },
    handleUploadError (e, file) {
      this.$message.error(`文件：${file.name}上传失败。`)
      this.loading = false
    },
    validateForm () {
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            resolve(true)
          }
        })
      })
    },
    async handleConfirm () {
      await this.validateForm()
      const params = {
        fexceptionCause: '样本原因',
        fexceptionType: this.form.exceptionType,
        fexceptionNote: this.form.exceptionNote,
        fexceptionFile: this.form.exceptionFile,
        fidString: this.ids
      }
      this.submitLoading = true
      this.$ajax({
        url: '/experiment/sign/exception_registration',
        data: params
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.visible = false
          this.$emit('dialogConfirmEvent')
        }
      }).finally(() => {
        this.submitLoading = false
      })
    }
  }
}
</script>

<style scoped>
.input-width{
  width: 200px;
}
.input-long-width{
  width: 515px;
}

</style>
