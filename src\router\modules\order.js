// 订单管理相关路由
export default [
  {
    path: '/business/view/orderReview',
    meta: {
      title: '订单审核'
    },
    component: () => import('@/components/business/orderManagement/orderReview/orderReview.vue')
  },
  {
    path: '/business/view/sampleAbnormal',
    meta: {
      title: '异常样本'
    },
    component: () => import('@/components/business/orderManagement/sampleAbnormal/abnormalSample.vue')
  },
  {
    path: '/business/view/orderKanban',
    meta: {
      title: '订单看板'
    },
    component: () => import('@/components/business/orderManagement/orderKanban/orderKanban.vue')
  },
  {
    path: '/business/view/orderDelivery',
    meta: {
      title: '交付订单管理'
    },
    component: () => import('@/components/business/deliveryManagement/index.vue')
  },
  {
    path: '/business/view/deliveryConfigManagement',
    meta: {
      title: '交付配置管理'
    },
    component: () => import('@/components/business/deliveryManagement/deliveryConfigManagement/index.vue')
  },
  {
    path: '/business/view/repeatClinicManagement',
    meta: {
      title: '加测订单管理'
    },
    component: () => import('@/components/business/dataMonitoringManagement/repeatClinicManagement/overview.vue')
  },
  {
    path: '/business/sub/orderLibraryDetail',
    component: () => import('@/components/business/orderManagement/orderReview/entryBaseInfo.vue')
  },
  {
    path: '/business/sub/orderTissueDetail',
    component: () => import('@/components/business/orderManagement/orderReview/entryBaseInfo.vue')
  }
]
