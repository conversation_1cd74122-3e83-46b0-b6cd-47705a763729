<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="复制模板"
      width="600px"
      @open="handleOpen">
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        size="mini"
        label-width="120px">
        <el-form-item label="原模板编码" prop="oldTemplateCode">
          <el-input v-model="form.oldTemplateCode" disabled></el-input>
        </el-form-item>
        <el-form-item label="原模板名称" prop="oldTemplateName">
          <el-input v-model="form.oldTemplateName" disabled></el-input>
        </el-form-item>
        <el-form-item label="所属分类" prop="category">
          <el-cascader
            :options="templateCategoryLists"
            v-model="form.category"
            v-formLoading="categoryLoading"
            :disabled="categoryLoading"
            :show-all-levels="false"
            :props="templateCategoryProp"
            style="width: 100%;"
            collapse-tags
            filterable
            clearable></el-cascader>
        </el-form-item>
        <el-form-item label="模板编码" prop="templateCode">
          <el-input v-model="form.templateCode"></el-input>
        </el-form-item>
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="form.templateName"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">下一步</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'copyTemplateDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    templateId: String | Number,
    templateName: String,
    templateCode: String,
    oldTemplateRow: Object | null
  },
  data () {
    return {
      form: {
        oldTemplateName: '',
        oldTemplateCode: '',
        templateName: '',
        category: '',
        templateCode: ''
      },
      templateCategoryLists: [], // 模板分类
      categoryLoading: false, // 模板分类
      templateCategoryProp: {
        checkStrictly: true,
        value: 'fid',
        label: 'fcategoryName'
      },
      submitBtnLoading: false,
      rules: {
        templateCode: [
          { required: true, message: '请输入模板编码', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        templateName: [
          { required: true, message: '请输入模板编名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.form.oldTemplateName = this.templateName
      this.form.oldTemplateCode = this.templateCode
      this.form = {
        oldTemplateName: this.templateName,
        oldTemplateCode: this.templateCode,
        templateName: '',
        category: '',
        templateCode: ''
      }
      this.getTemplateCategoryLists()
    },
    // 获取模板分类列表
    getTemplateCategoryLists () {
      this.categoryLoading = true
      this.$ajax({
        url: '/system/template/get_category_tree',
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.templateCategoryLists = res.data || []
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.categoryLoading = false
      })
    },
    // 确认复制
    handleDialogConfirm () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let row = {
            ...this.oldTemplateRow,
            id: '',
            oldTemplateId: this.templateId,
            templateName: this.form.templateName,
            categoryArr: this.form.category,
            templateCode: this.form.templateCode
          }
          this.$emit('dialogConfirmEvent', row)
          this.handleClose()
          // this.submitBtnLoading = true
          // let type = this.form.category.length > 0 ? this.form.category[this.form.category.length - 1] : ''
          // this.$ajax({
          //   url: '/template/copy_template',
          //   data: {
          //     reportTemplateId: this.templateId,
          //     templateType: type,
          //     reportCode: this.form.templateCode,
          //     reportName: this.form.templateName
          //   }
          // }).then(res => {
          //   if (res && res.code === this.SUCCESS_CODE) {
          //     this.$message.success('复制成功')
          //     this.$emit('dialogCloseEvent')
          //   } else {
          //     this.$message.error(res.message)
          //   }
          // }).finally(() => {
          //   this.submitBtnLoading = false
          // })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
