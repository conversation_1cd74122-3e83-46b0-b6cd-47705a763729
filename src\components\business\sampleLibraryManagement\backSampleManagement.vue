<template>
  <div>
    <div class="search-form">
      <el-form :model="form" size="mini" label-width="80px" inline style="display: flex;">
        <el-form-item label="样本编号">
          <el-input v-model.trim="formInput.sampleCode" size="mini" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="返样时间">
          <el-date-picker
            v-model.trim="formInput.time"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label-width="120px" label="产品/项目名称">
          <el-input v-model.trim="formInput.productName" size="mini" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <template v-if="$setAuthority('011007001', 'buttons')">
        <el-button type="primary" size="mini" @click="handleShowBackSampleApplicationDialog">申请返样</el-button>
      </template>
      <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleResetForm">重置</el-button>
    </div>
    <el-table
      ref="backTable"
      :data="tableData"
      style="width: 100%;"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      class="table"
      @filter-change="handleFilterTable"
      @select="handleSelectTable"
      @select-all="handleSelectAll"
      @row-click="handleRowClick">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column width="180" label="样本编号" prop="sampleCode"></el-table-column>
      <el-table-column
        :filters="progressFilterOptions"
        :filtered-value="sampleStatusFilterValues" prop="sampleStatus"
        label="样本状态"
        width="180"
        column-key="sampleStatus">
        <template slot-scope="scope">
          <span>{{scope.row.sampleStatus}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="backForm" label="样本属性" width="150"></el-table-column>
      <el-table-column prop="sampleUseTypeText" label="样品使用类型" width="180"></el-table-column>
      <el-table-column prop="lab" label="所属实验室" width="180"></el-table-column>
      <el-table-column prop="position" label="存放位置" width="220"></el-table-column>
      <el-table-column prop="productName" label="产品/项目名称" width="220"></el-table-column>
      <!--<el-table-column prop="testingLink" label="检测环节" width="120"></el-table-column>-->
      <el-table-column prop="recipient" label="收件人" width="120"></el-table-column>
      <el-table-column prop="address" label="收件地址" show-overflow-tooltip min-width="300"></el-table-column>
      <el-table-column prop="phone" label="联系电话" min-width="180"></el-table-column>
      <el-table-column prop="backTime" label="返样时间" min-width="180"></el-table-column>
      <el-table-column prop="notes" label="返样备注" min-width="180"></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <back-sample-application-dialog
      :pvisible.sync="backSampleApplicationDialogVisible"
      :back-sample-application-dialog-table-data="backSampleApplicationDialogTableData"
      @dialogConfirmEvent="handleDialogConfirmEvent"/>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import backSampleApplicationDialog from './backSampleManagementBackApplicationDialog'
export default {
  name: `backSampleManagement`,
  mixins: [mixins.tablePaginationCommonData],
  components: {
    backSampleApplicationDialog
  },
  mounted () {
    this.getData()
  },
  data () {
    return {
      form: {
        sampleCode: '',
        time: [],
        productName: ''
      },
      formInput: {
        sampleCode: '',
        time: [],
        productName: ''
      },
      progressFilterOptions: [
        {text: '待领取', value: '待领取'},
        {text: '处理中', value: '处理中'},
        {text: '已完成', value: '已完成'},
        {text: '部分完成', value: '部分完成'},
        {text: '驳回', value: '驳回'},
        {text: '已返样', value: '已返样'}
      ],
      sampleStatusFilterValues: [],
      selectedRows: new Map(),
      backSampleApplicationDialogVisible: false,
      backSampleApplicationDialogTableData: []
    }
  },
  methods: {
    getData () {
      let time = this.form.time || []
      this.$ajax({
        url: '/sample/order/page_back_sample',
        data: {
          param: {
            sampleNum: this.form.sampleCode,
            startTime: time[0],
            endTime: time[1],
            sampleStatus: this.sampleStatusFilterValues,
            proName: this.form.productName
          },
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.selectedRows.clear()
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              sampleStatus: v.fsampleStatus,
              sampleCode: v.fsampleNumber,
              lab: v.flab,
              position: v.fsamplePlace,
              productName: v.fproName,
              sampleUseType: v.fsampleUseType,
              sampleUseTypeText: v.fsampleUseType === 0 ? '科研' : v.fsampleUseType === 1 ? '临床' : '-',
              // testingLink: v,
              recipient: v.frecipient,
              address: v.frecipientAddr,
              phone: v.frecipientTel,
              backTime: v.frecipientTime,
              notes: v.fbackNotes,
              backForm: v.fbackFrom
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.form = {...this.formInput}
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.formInput = {
        sampleCode: '',
        time: []
      }
      this.handleSearch()
    },
    // 筛选事件触发
    handleFilterTable (val) {
      if (val.sampleStatus) {
        this.sampleStatusFilterValues = []
        val.sampleStatus.forEach(item => {
          this.sampleStatusFilterValues.push(item)
        })
        this.handleSearch()
      }
    },
    // 显示返样申请
    handleShowBackSampleApplicationDialog () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      let tableData = [...this.selectedRows.values()]
      if (this.selectedRows.size > 1) {
        // 临床返样只允许单个样本操作
        // 是否存在临床
        let hasClinical = tableData.some(v => v.sampleUseType === 1)
        if (hasClinical) {
          this.$message.error('临床返样请选择一条数据操作')
          return
        }
      }
      // 所有数据实验室必须相同且样本状态必须为已入库
      let firstLab = tableData[0].lab
      const isAllRight = tableData.every(v => {
        return v.lab === firstLab && v.sampleStatus === '已入库'
      })
      if (!isAllRight) {
        this.$message.error('所选的实验室必须相同且样本状态必须为已入库')
        return
      }
      this.backSampleApplicationDialogTableData = tableData
      this.backSampleApplicationDialogVisible = true
    },
    // 申请成功后。更新列表，下载申请单
    handleDialogConfirmEvent (data) {
      this.handleSearch()
      this.downloadFile(data)
    },
    // 下载申请单
    downloadFile (data) {
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${data}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.backTable.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .form-container{
    display: flex;
    justify-content: space-between;
    margin: 20px 0 0 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #ccc;
    .form{
      display: flex;
      .form-item{
        display: flex;
        align-items: center;
        margin-right: 20px;
        label{
          width: 5em;
          flex-shrink: 0;
        }
      }
    }
  }
</style>
