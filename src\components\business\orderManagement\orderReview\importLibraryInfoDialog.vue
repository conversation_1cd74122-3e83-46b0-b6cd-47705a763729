<template>
  <div>
    <el-dialog
            :visible.sync="visible"
            :close-on-click-modal="false"
            :before-close="handleClose"
            title="数据导入"
            width="600px"
            @open="handleOpen">
      <div
              v-loading="loading"
              v-if="visible"
              element-loading-text="正在上传文件"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)">
        <div class="upLoad">
          <span><span style="color: red;">*</span>数据文件：</span>
          <el-upload
                  ref="upload"
                  :on-success="handleOnSuccess"
                  :on-error="handleOnError"
                  :headers="headers"
                  :data="{type: type}"
                  :auto-upload="false"
                  :limit="1"
                  :before-upload="handleBeforeUpload"
                  :action="uploadUrl">
            <span class="btn"><i class="el-icon-upload"></i>上传文件</span>
            <span slot="tip" class="el-upload__tip">仅支持Excel</span>
          </el-upload>
        </div>
        <div>
          <el-button type="text" style="" @click="handleClickDownload">下载导入模板</el-button>
        </div>
        <template v-if="showNoFileTips">
          <p style="margin-top: 25px;font-size: 14px;color: #F56C6C;"><i class="el-icon-warning-outline" style="margin-right: 10px;"></i>您还没有上传文件</p>
        </template>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
import constants from '../../../../util/constants'
import util from '../../../../util/util'
import Cookies from 'js-cookie'
export default {
  name: 'importLibraryInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: Number // 1: 上机文库 2： 组织或核酸
  },
  data () {
    return {
      loading: false,
      showNoFileTips: false,
      headers: {
        token: Cookies.get('token')
      },
      uploadUrl: constants.JS_CONTEXT + '/covid/manage/yide_import_offline_data'
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
        // this.getSamplingPoint()
      })
    },
    // 点击确认
    handleConfirm: function () {
      if (this.$refs.upload.uploadFiles.length === 0) {
        this.$message.error('请选择Excel文件')
        return
      }
      this.$refs.upload.submit()
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res.success) {
        this.$message.success('上传成功')
        this.$emit('dialogConfirmEvent', res)
        this.$refs.upload.clearFiles()
        this.handleClose()
      } else {
        this.$message.error(res.message)
      }
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      if (/\.(xlsx|xls)$/.test(name)) {
        return true
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    },
    handleClickDownload () {
      this.$ajax({
        url: '',
        data: {
          type: this.type
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
