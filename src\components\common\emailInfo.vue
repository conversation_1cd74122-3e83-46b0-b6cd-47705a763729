<template>
  <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-width="80px">
    <el-form-item label="邮箱标题" prop="emailTitle">
      <el-input v-model="form.emailTitle" :disabled="isDetail" size="mini" placeholder="请输入邮箱标题"></el-input>
    </el-form-item>
    <el-form-item label="邮箱正文" prop="emailContent">
      <div class="email-content">
        <div>
          您好！
        </div>
        <div>
          <div
            v-if="isDetail"
            class="editor-wrap"
            v-html="form.emailContent"></div>
          <edit
            v-else
            :value="form.emailContent"
            @input="handleEmailContentInput"/>
        </div>
        <div>祝好</div>
        <div>吉因加医学检验实验室有限公司</div>
        <img src="https://cdn.geneplus.org.cn/LIMS/logo.png" alt="logo"/>
        <div>400-166-6506</div>
      </div>
      <el-input v-show="false" v-model="form.emailContent"></el-input>

    </el-form-item>
    <el-form-item label="附件" prop="attachFile">
      <el-upload
        v-if="!isDetail"
        ref="upload"
        :action="uploadUrl"
        :headers="headers"
        :file-list="form.attachFile"
        class="upload-demo"
        multiple
        :on-success="handleOnSuccess">
        <el-button size="mini" icon="el-icon-plus">添加附件</el-button>
      </el-upload>
      <div v-else>
        <span v-for="(item, index) in form.attachFile" :key="index">
          <div class="link" @click="handleDown(item.fileAbsolutePath)">{{item.originalFileName}}</div>
        </span>
      </div>
    </el-form-item>
    <el-form-item label="收件邮箱" prop="inbox">
      <el-input
        v-model.trim="form.inbox"
        :rows="2"
        :disabled="isDetail"
        maxlength="1000"
        size="mini"
        class="form-width"
        placeholder="请输入"></el-input>
    </el-form-item>
    <el-form-item label="抄送邮箱" prop="sendEmail">
      <el-input
        v-model.trim="form.sendEmail"
        :disabled="isDetail"
        maxlength="1000"
        size="mini"
        class="form-width"
        placeholder="请输入"></el-input>
    </el-form-item>
  </el-form>
</template>

<script>
import Cookies from 'js-cookie'
import constants from '../../util/constants'
import util from '../../util/util'

export default {
  name: 'emailInfo',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    form: {
      get () {
        return this.value
      },
      set (value) {
        this.$emit('update:value', value)
      }
    }
  },
  data () {
    return {
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      headers: {
        token: Cookies.get('token')
      },
      // form: {
      //   emailTitle: '',
      //   emailContent: '',
      //   inbox: '',
      //   sendEmail: '',
      //   attachFile: []
      // },
      rules: {
        emailTitle: [
          {required: true, message: '此项为必填', trigger: 'blur'}
        ],
        emailContent: [
          {required: true, message: '此项为必填', trigger: 'blur'}
        ],
        inbox: [
          {required: true, message: '此项为必填', trigger: 'blur'},
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ],
        sendEmail: [
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ]
      }
    }
  },
  methods: {
    handleOnSuccess () {
      this.form.attachFile = this.setAttachFile()
      console.log(this.form.attachFile)
    },
    setAttachFile () {
      let files = []
      let uploadFiles = this.$refs.upload.uploadFiles || []
      uploadFiles.forEach(v => {
        if (v.response) {
          let data = v.response.data
          data.name = data.originalFileName
          files.push(data)
        } else {
          files.push(v)
        }
      })
      return files
    },
    handleDown (path) {
      console.log(path)
      window.open(path)
    },
    handleEmailContentInput (content) {
      this.form.emailContent = content
    }
  }
}
</script>

<style scoped>
.email-content {
  padding: 10px;
  height: 350px;
  line-height: 1.5;
  overflow: auto;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
}

.editor-wrap {
  padding: 5px;
  border: 1px solid #DCDFE6;
  min-height: 200px;
  overflow: auto;
  border-radius: 4px;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
}

.editor-wrap:empty:before {
  color: #DCDFE6;
  content: attr(data-placeholder);
}
.editor-wrap:hover {
  border: 1px solid #C0C4CC;
}
:focus {
  outline: 1px solid $color;
}
</style>
