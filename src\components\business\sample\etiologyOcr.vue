<template>
  <div>
    <div class="title">图片信息</div>
    <el-tabs v-model="imgsType" @tab-click="handleTabChange">
      <el-tab-pane :label="`送检单${getImgIndexAndLength('sampleOrderImg')}`" name="sampleOrderImg"></el-tab-pane>
      <el-tab-pane :label="`临床资料${getImgIndexAndLength('imgNames')}`" name="imgNames"></el-tab-pane>
    </el-tabs>
    <div class="change-picture">
      <el-button size="mini" @click="handlePreviousPicture">上一张</el-button>
      <el-button size="mini" @click="handleNextPicture">下一张</el-button>
      <el-button size="mini" @click="handleImageAction('right')">右转<i class="el-icon-refresh-right"></i></el-button>
      <el-button size="mini" @click="handleImageAction('left')">左转<i class="el-icon-refresh-left"></i></el-button>
<!--      <el-button size="mini" @click="handleImageReset">还原<i class="el-icon-full-screen"></i></el-button>-->
      <el-button v-if="!isDisabled" size="mini" @click="handleEditPicture">编辑</el-button>
      <el-button :disabled="ocrDialogVisible" size="mini" @click="handleText">文字识别</el-button>
    </div>
    <div class="picture">
      <div v-if="picSrc" class="image">
        <template v-if="picSrc.indexOf('.pdf') !== -1">
          <div style="width: 100%; height: 100%;display: flex; justify-content: center;align-items: center;">
            <div>
              <icon-svg icon-class="icon-pdf" style="font-size: 150px;color: #409EFF;"></icon-svg>
              <div style="text-align: center;">
                <el-button type="text" size="mini" @click="handleViewPdf(picSrc)">点击查看PDF文件</el-button>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
            <div class="preview-img">
              <vueCropper
                  ref="cropper"
                  style="z-index:10000"
                  :img="option.img"
                  :mode="option.mode"
                  :auto-crop="option.autoCrop"
                  :center-box="option.centerBox"
                  @imgLoad="imgLoad"
              ></vueCropper>
<!--              <div-->
<!--                  v-if="imgLoading"-->
<!--                  class="error-preview"-->
<!--                  ref="imgDiv"-->
<!--                  v-loading="imgLoading"-->
<!--                  element-loading-text="加载失败"-->
<!--                  element-loading-spinner="el-icon-loading"-->
<!--                  element-loading-background="rgba(0, 0, 0, 0.8)">-->
<!--              </div>-->
              <el-result v-if="imgLoading" icon="error" title="加载失败" class="error-preview">
              </el-result>
          </div>
        </template>
      </div>
    </div>
    <el-dialog
        title="识别结果"
        :visible.sync="ocrDialogVisible"
        :modal='false'
        v-drag-dialog
        :close-on-click-modal="false"
        width="250px"
        class="el-dialog-text"
        top="200px"
        :before-close="handleClose"
        >
      <div v-loading="loadingText" element-loading-text="文字识别中">
      </div>
      <div class="ocr-text">
      <el-scrollbar>
        <p v-for="(item,index) in copyText" :key="index">{{item}}</p>
      </el-scrollbar>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="handleBegin">开始识别</el-button>
<!--        <el-button size="mini" icon="el-icon-close" @click="handleCancle">取消截图</el-button>-->
        <el-button size="mini" icon="el-icon-document-copy" @click="handlecopyText">复制</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import VueCropper from '../../common/vueCropper/src/vue-cropper.vue'
import {VueCropper} from 'vue-cropper'
// import axios from 'axios'
import util from '../../../util/util'
export default {
  name: 'sampleImgInfo',
  components: {
    VueCropper
  },
  props: {
    imgType: {
      type: String
    },
    imgSrc: {
      type: String
    },
    imgNames: {
      type: Array
    }, // 临床资料
    sampleOrderImg: {
      type: Array
    }, // 送检单资源
    isDisabled: {
      type: Boolean,
      default: false
    },
    title: {
      type: String
    },
    sampleBasicId: {
      type: Number
    }
  },
  mounted () {
  },
  watch: {
    imgSrc: {
      handler (newValue) {
        console.log(newValue)
        this.picSrc = newValue
        this.option.img = newValue
      },
      immediate: true
    },
    imgType: {
      handler (newValue) {
        this.imgsType = newValue
      },
      immediate: true
    },
    picSrc (newValue) {
      console.log(newValue)
      this.option.img = newValue
    }
  },
  data () {
    return {
      multiples: 100, // 放大或者缩小
      deg: 0, // 旋转的角度
      isDrag: false, // 是否开始拖拽
      startX: 0, // 鼠标的点击X轴
      startY: 0, // 鼠标的点击Y轴
      moveX: 0, // 鼠标移动的X轴
      moveY: 0, // 鼠标移动的Y轴
      endX: 0,
      endY: 0,
      imgLoading: false,
      pictureInfoSaveDialogVisible: false,
      pictureInfoSaveDialogData: {},
      picSrc: '', // 图片展示路径
      imgsType: '',
      option: {
        img: this.picSrc,
        mode: 'cover',
        autoCrop: false,
        centerBox: false
      }, // 裁剪图片配置
      ocrDialogVisible: false, // 识别弹窗显示
      copyText: [], // 识别内容
      loadingText: false
    }
  },
  methods: {
    // 图片失败
    imgLoad (data) {
      console.log(data === 'success')
      this.imgLoading = data !== 'success'
    },
    // 文字识别
    handleText () {
      console.log(this.$refs.cropper.cropW)
      this.option.centerBox = true
      this.copyText = []
      this.$refs.cropper.startCrop()
      this.ocrDialogVisible = true
    },
    // 开始识别
    handleBegin () {
      this.loadingText = true
      console.log(this.title)
      this.$refs.cropper.getCropData(data => {
        // 测试接口
        this.$ajax({
          method: 'post',
          url: '/sample/basic/volc_engine_ocr_service',
          data: {
            imageBase64: data.split(',')[1],
            specificLocation: `病原样本管理-${this.title}`
          }
        }).then(res => {
          console.log(res)
          if (res && res.code === this.SUCCESS_CODE) {
            this.copyText = res.data
          } else {
            this.$message.error('识别失败')
          }
        }).finally(() => {
          this.loadingText = false
        })
      })
    },
    // 复制文字
    handlecopyText () {
      util.copyText(this.copyText)
      this.$message.success('复制成功')
    },
    // 取消截图
    handleCancle () {
      this.$refs.cropper.clearCrop()
    },
    // 关闭识别弹窗
    handleClose () {
      this.option.centerBox = false
      this.copyText = []
      this.$refs.cropper.stopCrop()
      this.$refs.cropper.clearCrop()
      this.ocrDialogVisible = false
    },
    // 切换上一张图片
    handlePreviousPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      switch (this.imgsType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === 0 ? this.picSrc = this.imgNames[this.imgNames.length - 1].fileAbsolutePath : this.picSrc = this.imgNames[index - 1].fileAbsolutePath
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === 0 ? this.picSrc = this.sampleOrderImg[this.sampleOrderImg.length - 1].fileAbsolutePath : this.picSrc = this.sampleOrderImg[index - 1].fileAbsolutePath
          break
      }
    },

    // 切换下一张图片
    handleNextPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      switch (this.imgsType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === this.imgNames.length - 1 ? this.picSrc = this.imgNames[0].fileAbsolutePath : this.picSrc = this.imgNames[index + 1].fileAbsolutePath
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === this.sampleOrderImg.length - 1 ? this.picSrc = this.sampleOrderImg[0].fileAbsolutePath : this.picSrc = this.sampleOrderImg[index + 1].fileAbsolutePath
          break
      }
    },

    // 编辑图片信息
    handleEditPicture () {
      this.pictureInfoSaveDialogData = {
        type: this.imgsType,
        sampleBasicId: this.sampleBasicId,
        tableData: this.imgsType === 'sampleOrderImg' ? JSON.parse(JSON.stringify(this.sampleOrderImg)) : [] // 送检单与知情同意书专用
      }
      console.log(this.pictureInfoSaveDialogData)
      this.$emit('fixImgEvent', {
        pictureInfoSaveDialogData: this.pictureInfoSaveDialogData,
        pictureInfoSaveDialogVisible: true
      })
    },
    // 图片的操作
    handleImageAction (value) {
      value === 'right' ? this.$refs.cropper.rotateRight() : this.$refs.cropper.rotateLeft()
    },
    // 获取图片索引
    getImgIndexAndLength (type) {
      let index = 0
      let length = 0
      switch (type) {
        case 'sampleOrderImg':
          length = this.sampleOrderImg.length
          if (this.imgsType === type) {
            index = this.sampleOrderImg.findIndex(v => v.fileAbsolutePath === this.picSrc)
          }
          break
        case 'imgNames':
          length = this.imgNames.length
          if (this.imgsType === type) {
            index = this.imgNames.findIndex(v => v.fileAbsolutePath === this.picSrc)
          }
          break
      }
      return length === 0 ? '' : `(${index + 1} / ${length})`
    },
    // 切换图片类型
    handleTabChange () {
      if (this.ocrDialogVisible) {
        this.handleClose()
      }
      this.deg = 0
      this.multiples = 100
      switch (this.imgsType) {
        case 'imgNames':
          if (this.imgNames.length !== 0) {
            this.picSrc = this.imgNames[0].fileAbsolutePath || ''
          } else {
            this.picSrc = ''
          }
          break
        case 'sampleOrderImg':
          if (this.sampleOrderImg.length !== 0) {
            this.picSrc = this.sampleOrderImg[0].fileAbsolutePath || ''
          } else {
            this.picSrc = ''
          }
          break
      }
    },
    // 查看pdf文件
    handleViewPdf (url) {
      window.open(url, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.change-picture{
  position: relative;
  //z-index:2000;
  //width:65%
}
.el-dialog-text{
  right: -20px;
  left: auto;
  z-index: 1 !important;
}

.right{
  overflow-y: auto;
  flex: 1;
  padding: 0 5px;
  border-left: 1px solid #DCDFE6;
  height: 100%;
  .picture{
    margin: 10px auto;
    width: 90%;
    height: calc(100% - 54px - 28px - 20px - 40px);
    .image{
      overflow: auto;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
    }
  }
  .title{
    height: 30px;
    line-height: 30px;
    font-size: 13px;
  }
  .preview-img{
    position: relative;
    z-index: 1000;
    width:26vw;
    height:55vh;
    box-shadow: 0 0 10px #ccc;
  }
  .error-preview{
    position: absolute;
    z-index: 100000;
    top: 0;
    left: 0;
    width:26vw;
    height:55vh;
    box-shadow: 0 0 10px #ccc;
    background-color: rgba(0, 0, 0,0.8);
    >>>.el-result__title p{
      color: white;
    }
  }
  @media (min-width: 1799px) {
    .ocr-text{
      height:30vh
    }
    .el-scrollbar{
      height:30vh
    }
  }
  @media (min-width: 900px) and (max-width: 1799px) {
    .ocr-text{
      height:20vh
    }
    .el-scrollbar{
      height:20vh
    }
  }
}
</style>
