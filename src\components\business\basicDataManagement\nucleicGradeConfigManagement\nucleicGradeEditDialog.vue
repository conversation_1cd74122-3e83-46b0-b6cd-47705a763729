<template>
  <el-dialog :title="title" :visible.sync="visible" width="1400px" :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="handleClose" class="nucleic-grade-dialog" @opened="handleOpen">
    <el-form ref="form" :model="form" :rules="rules" :disabled="type === 3" label-width="130px" size="mini">
      <!-- 标准说明 -->
      <div class="form-section">
        <div class="section-title">标准说明</div>
        <el-form-item label="核酸等级定级标准" prop="gradeName" class="required-label">
          <el-input v-model.trim="form.gradeName" placeholder="请输入核酸等级定级标准" maxlength="20" show-word-limit size="mini"
            clearable></el-input>
        </el-form-item>
        <el-form-item label="说明" prop="notes">
          <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入说明" v-model.trim="form.notes"
            maxlength="500" show-word-limit size="mini" clearable>
          </el-input>
        </el-form-item>
      </div>

      <!-- 核酸等级判定 -->
      <div class="form-section">
        <div class="section-title">核酸等级判定</div>
        <div class="tips-container">
          <div class="tips-title">温馨提示：为能够正确判断等级，在输入判断标准时请正确使用对应的分隔符，分隔符含如下几类：</div>
          <div class="tips-content">
            ①"小于"＜ &nbsp;&nbsp; ②"大于"＞ &nbsp;&nbsp; ③"小于等于"≤ &nbsp;&nbsp; ④"大于等于"≥ &nbsp;&nbsp; ⑤"等于"= &nbsp;&nbsp;
            ⑥"不等于"≠ &nbsp;&nbsp; ⑦用*号代替判断的指标项：如：≤100，与*≤100，含义是一样的，均为判断指标项小于等于100的情况
          </div>
        </div>

        <div v-for="(standard, standardIndex) in form.standards" :key="'standard-' + standardIndex"
          class="standard-container">
          <div class="standard-header">
            <span>标准 {{ standardIndex + 1 }}</span>
            <div class="standard-actions">
              <el-button v-if="standardIndex === form.standards.length - 1 && form.standards.length < 5" type="primary"
                icon="el-icon-plus" circle size="mini" @click="addStandard" class="circle-button"></el-button>
              <el-button v-if="form.standards.length > 1" type="danger" icon="el-icon-delete" circle size="mini"
                @click="removeStandard(standardIndex)" class="circle-button"></el-button>
            </div>
          </div>

          <div class="flex-container">
            <el-form-item label="工序环节" style="width: 50%" :prop="'standards[' + standardIndex + '].processSteps'">
              <el-select v-model.trim="standard.processSteps" multiple collapse-tags placeholder="请选择工序环节"
                style="width: 100%" size="mini" clearable>
                <el-option v-for="item in processStepOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="样本类型" style="width: 50%" :prop="'standards[' + standardIndex + '].sampleTypes'">
              <el-select v-model.trim="standard.sampleTypes" multiple collapse-tags placeholder="请选择样本类型"
                style="width: 100%" size="mini" clearable>
                <el-option v-for="item in sampleTypeList" :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="nucleic-grade-indicators-table">
            <!-- 自定义表头部分，替代el-table的表头插槽 -->
            <div class="custom-table-header">
              <div class="header-row">
                <div class="header-cell nuclearTotal-cell">
                  <div class="header-content">
                    <span>核酸总量</span>
                    <el-select v-model="standard.nuclearTotalJudge" placeholder="请选择" size="mini" class="judge-select"
                      clearable>
                      <el-option v-for="item in judgeOptions" :key="item.value + '核酸总量'" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="header-cell quality-cell">
                  <div class="header-content">
                    <span>质量浓度</span>
                    <el-select v-model="standard.qualityConcentrationJudge" placeholder="请选择" size="mini"
                      class="judge-select" clearable>
                      <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="header-cell nq-cell">
                  <div class="header-content">
                    <span>N/Q</span>
                    <el-select v-model="standard.nqJudge" placeholder="请选择" size="mini" class="judge-select" clearable>
                      <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="header-cell dv200-cell">
                  <div class="header-content">
                    <span>DV200</span>
                    <el-select v-model="standard.dv200Judge" placeholder="请选择" size="mini" class="judge-select"
                      clearable>
                      <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="header-cell nano-cell">
                  <div class="header-content">
                    <span>Nano</span>
                    <el-select v-model="standard.nanoJudge" placeholder="请选择" size="mini" class="judge-select"
                      clearable>
                      <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="header-cell grade-cell">
                  <div class="header-content">
                    <span>核酸等级</span>
                  </div>
                </div>
                <div class="header-cell action-cell">
                  <div class="header-content">
                    <span>操作</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表格主体部分 -->
            <el-table :data="standard.indicators" border size="mini" :show-header="false" style="width: 100%">
              <el-table-column prop="nuclearTotal" width="180px">
                <template slot-scope="scope">
                  <el-form-item label-width="0"
                    :prop="'standards[' + standardIndex + '].indicators[' + scope.$index + '].nuclearTotalStandard'"
                    :rules="[{ required: standard.nuclearTotalJudge === 1, message: '请输入核酸总量', trigger: 'blur' }, { validator: validateStandard, trigger: 'blur' }]">
                    <el-input v-model.trim="scope.row.nuclearTotalStandard" placeholder="请输入标准" size="mini" clearable>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="qualityConcentration" width="180px">
                <template slot-scope="scope">
                  <el-form-item label-width="0"
                    :prop="'standards[' + standardIndex + '].indicators[' + scope.$index + '].qualityConcentrationStandard'"
                    :rules="[{ required: standard.qualityConcentrationJudge === 1, message: '请输入质量浓度', trigger: 'blur' }, { validator: validateStandard, trigger: 'blur' }]">
                    <el-input v-model.trim="scope.row.qualityConcentrationStandard" placeholder="请输入标准" size="mini"
                      clearable>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="nqStandard" min-width="150">
                <template slot-scope="scope">
                  <el-form-item label-width="0"
                    :prop="'standards[' + standardIndex + '].indicators[' + scope.$index + '].nqStandard'"
                    :rules="[{ required: standard.nqJudge === 1, message: '请输入N/Q', trigger: 'blur' }, { validator: validateStandard, trigger: 'blur' }]">
                    <el-input v-model.trim="scope.row.nqStandard" placeholder="请输入标准" size="mini" clearable>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="dv200Standard" min-width="150">
                <template slot-scope="scope">
                  <el-form-item label-width="0"
                    :prop="'standards[' + standardIndex + '].indicators[' + scope.$index + '].dv200Standard'"
                    :rules="[{ required: standard.dv200Judge === 1, message: '请输入DV200', trigger: 'blur' }, { validator: validateStandard, trigger: 'blur' }]">
                    <el-input v-model.trim="scope.row.dv200Standard" placeholder="请输入标准" size="mini" clearable>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="nanoStandard" min-width="150">
                <template slot-scope="scope">
                  <el-form-item label-width="0"
                    :prop="'standards[' + standardIndex + '].indicators[' + scope.$index + '].nanoStandard'"
                    :rules="[{ required: standard.nanoJudge === 1, message: '请输入Nano', trigger: 'blur' }, { validator: validateStandard, trigger: 'blur' }]">
                    <el-input v-model.trim="scope.row.nanoStandard" placeholder="请输入标准" size="mini" clearable>
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="grade" width="120">
                <template slot-scope="scope">
                  <el-select v-model.trim="scope.row.grade" placeholder="请选择" size="mini" style="width: 100%" clearable>
                    <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column width="100" fixed="right">
                <template slot-scope="scope">
                  <div class="actions-column">
                    <el-button type="primary" icon="el-icon-plus" circle size="mini"
                      @click="addIndicator(standardIndex)" class="circle-button"></el-button>
                    <el-button v-if="standard.indicators.length > 1" type="danger" icon="el-icon-delete" circle
                      size="mini" @click="removeIndicator(standardIndex, scope.$index)"
                      class="circle-button"></el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 处理措施配置 -->
          <div class="form-section">
            <div class="section-title">处理措施配置</div>
            <div class="measures-container">
              <div v-for="(measure, measureIndex) in standard.measures" :key="'measure-' + measureIndex"
                class="measure-row">
                <div class="measure-content">
                  <el-form-item label-width="100px" label="核酸等级">
                    <el-select v-model.trim="measure.grade" placeholder="请选择核酸等级" size="mini" clearable
                      style="width: 150px;">
                      <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="100px" label="核酸等级质控">
                    <el-select v-model.trim="measure.qcGrade" placeholder="请选择核酸等级质控" size="mini" clearable
                      style="width: 150px;">
                      <el-option v-for="item in qcGradeOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="100px" label="处理措施">
                    <el-select v-model.trim="measure.action" placeholder="请选择处理措施" size="mini" clearable
                      style="width: 150px;">
                      <el-option v-for="item in actionOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="100px" label="异常告知报告">
                    <el-select v-model.trim="measure.report" placeholder="请选择异常告知报告" size="mini" clearable
                      style="width: 150px;">
                      <el-option v-for="item in reportOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div class="measure-actions">
                  <el-button type="primary" icon="el-icon-plus" circle size="mini" @click="addMeasure(standardIndex)"
                    class="circle-button"></el-button>
                  <el-button v-if="standard.measures.length > 1" type="danger" icon="el-icon-delete" circle size="mini"
                    @click="removeMeasure(standardIndex, measureIndex)" class="circle-button"></el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button v-if="type !== 3" type="primary" @click="handleConfirm" size="mini">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import { getNucleicGradeDetail, updateNucleicGrade, getSampleTypeListApi } from '../../../../api/basicDataManagement/nucleicGradeApi'
import { awaitWrap } from '@/util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    pvisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: null
    },
    type: {
      type: [String, Number],
      default: null
    }
  },
  data () {
    return {
      form: {
        gradeName: '',
        notes: '',
        standards: [{
          processSteps: [],
          sampleTypes: [],
          indicators: [this.getDefaultIndicator()],
          nuclearTotalJudge: 0,
          qualityConcentrationJudge: 0,
          nqJudge: 0,
          dv200Judge: 0,
          nanoJudge: 0,
          measures: [this.getDefaultMeasure()]
        }]
      },
      rules: {
        gradeName: [
          { required: true, message: '请输入核酸等级定级标准', trigger: 'blur' }
        ]
      },
      title: '',
      sampleTypeList: [],
      gradeOptions: [
        { label: '1级', value: '1级' },
        { label: '2级', value: '2级' },
        { label: '3级', value: '3级' },
        { label: '4级', value: '4级' },
        { label: '合格', value: '合格' },
        { label: '不合格', value: '不合格' },
        { label: '风险', value: '风险' }
      ],
      qcGradeOptions: [
        { label: '合格', value: 1 },
        { label: '风险', value: 2 },
        { label: '不合格', value: 3 }
      ],
      actionOptions: [
        { label: '继续检测', value: 1 },
        { label: '暂停检测', value: 2 }
      ],
      reportOptions: [
        { label: '写风险告知', value: 1 },
        { label: '不写风险告知', value: 2 },
        { label: '写不合格告知', value: 3 },
        { label: '不写不合格告知', value: 4 }
      ],
      processStepOptions: [
        { label: 'FFPE DNA提取', value: 'FFPE DNA提取' },
        { label: '组织RNA提取', value: '组织RNA提取' },
        { label: '组织DNARNA提取', value: '组织DNARNA提取' },
        { label: '全基因组DNA提取', value: '全基因组DNA提取' },
        { label: '血浆游离DNA提取', value: '血浆游离DNA提取' },
        { label: '核酸检测', value: '核酸检测' }
      ],
      judgeOptions: [
        { label: '不判断', value: 0 },
        { label: '判断', value: 1 }
      ]
    }
  },
  methods: {
    // 验证输入的标准格式是否正确
    validateStandard (rule, value, callback) {
      if (!value || value.trim() === '') {
        callback()
        return
      }
      // 检查是否包含非法字符
      const validChars = /^[<>≤≥=≠*0-9.]+$/
      if (!validChars.test(value)) {
        callback(new Error('只能输入＜＞≤≥=≠*和数字'))
        return
      }

      // 检查运算符是否单独存在
      const operators = ['<', '>', '≤', '≥', '=', '≠', '＞', '＜', '＝']
      for (const op of operators) {
        if (value === op) {
          callback(new Error('运算符不能单独存在'))
          return
        }
      }

      // 检查*是否单独存在
      if (value === '*') {
        callback(new Error('*不能单独存在'))
        return
      }

      // 检查运算符是否连续
      for (let i = 0; i < operators.length; i++) {
        for (let j = 0; j < operators.length; j++) {
          if (value.includes(operators[i] + operators[j])) {
            callback(new Error('运算符不能连续使用'))
            return
          }
        }
      }
      callback()
    },

    handleOpen () {
      this.resetForm()
      console.log(this.form)
      this.getSampleTypeList()
      this.id && this.getDetailData()
      const titles = {
        1: '复制新增',
        2: '修改',
        3: '查看详情'
      }
      this.title = titles[this.type] || '新增'
    },
    // 获取样本类型列表
    async getSampleTypeList () {
      this.sampleTypeList = []
      const { res } = await awaitWrap(getSampleTypeListApi())
      if (res.code === this.SUCCESS_CODE) {
        this.sampleTypeList = res.data
      }
    },
    // 获取默认的标准对象
    getDefaultStandard () {
      return {
        processSteps: [],
        sampleTypes: [],
        indicators: [this.getDefaultIndicator()],
        nuclearTotalJudge: 0,
        qualityConcentrationJudge: 0,
        nqJudge: 0,
        dv200Judge: 0,
        nanoJudge: 0,
        measures: [this.getDefaultMeasure()]
      }
    },
    // 获取默认的指标对象
    getDefaultIndicator () {
      return {
        nuclearTotalStandard: '',
        qualityConcentrationStandard: '',
        nqStandard: '',
        dv200Standard: '',
        nanoStandard: '',
        grade: '',
        nuclearTotalTouched: false,
        qualityConcentrationTouched: false,
        nqTouched: false,
        dv200Touched: false,
        nanoTouched: false,
        gradeTouched: false
      }
    },
    // 获取默认的处理措施对象
    getDefaultMeasure () {
      return {
        grade: '',
        qcGrade: '',
        action: '',
        report: ''
      }
    },
    // 添加标准
    addStandard () {
      if (this.form.standards.length < 5) {
        this.form.standards.push(this.getDefaultStandard())
      } else {
        this.$message.warning('最多只能添加5个标准')
      }
    },
    // 移除标准
    removeStandard (index) {
      this.$confirm('是否删除此标准?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.standards.splice(index, 1)
      }).catch(() => { })
    },
    // 添加指标
    addIndicator (standardIndex) {
      if (this.form.standards[standardIndex].indicators.length < 100) {
        this.form.standards[standardIndex].indicators.push(this.getDefaultIndicator())
      } else {
        this.$message.warning('单个标准最多只能添加100个指标')
      }
    },
    // 移除指标
    removeIndicator (standardIndex, indicatorIndex) {
      this.$confirm('是否删除此行标准?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.standards[standardIndex].indicators.splice(indicatorIndex, 1)
      }).catch(() => { })
    },
    // 添加处理措施
    addMeasure (index) {
      this.form.standards[index].measures.push(this.getDefaultMeasure())
    },
    // 移除处理措施
    removeMeasure (standardsIndex, index) {
      this.$confirm('是否删除此处理措施?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.standards[standardsIndex].measures.splice(index, 1)
      }).catch(() => { })
    },
    // 获取详情
    async getDetailData () {
      let { res } = await awaitWrap(getNucleicGradeDetail({
        fdnaLevelConfigId: this.id,
        fisCopy: this.type === 1 ? 1 : 0
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.form.gradeName = res.data.fdnaLevelStandard
        this.form.notes = res.data.fdescription
        this.form.standards = res.data.dnaLevelJudgmentList.map(judgment => ({
          processSteps: judgment.forderStep.split(',').filter(v => v),
          sampleTypes: judgment.fsampleType.split(',').filter(v => v),
          indicators: judgment.dnaLevelJudgmentList.map(indicator => ({
            nuclearTotalStandard: indicator.fdnaTotal,
            qualityConcentrationStandard: indicator.fqualityConcentration,
            nqStandard: indicator.ftotalNano || '',
            dv200Standard: indicator.fdv200 || '',
            nanoStandard: indicator.fnanoCon || '',
            grade: indicator.fdnaLevel || ''
          })),
          measures: judgment.dnaLevelDealList.map(measure => ({
            grade: measure.fdnaLevel,
            qcGrade: measure.flevelQc,
            action: measure.fdealMethod,
            report: measure.fexceptionReport
          })),
          nuclearTotalJudge: judgment.fisDnaTotal,
          qualityConcentrationJudge: judgment.fisQualityConcentration,
          nqJudge: judgment.fisTotalNano,
          dv200Judge: judgment.fisDv200,
          nanoJudge: judgment.fisNanoCon
        }))
      }
    },
    // 重置表单
    resetForm () {
      this.form = {
        gradeName: '',
        notes: '',
        standards: [{
          processSteps: [],
          sampleTypes: [],
          indicators: [this.getDefaultIndicator()],
          nuclearTotalJudge: 0,
          qualityConcentrationJudge: 0,
          nqJudge: 0,
          dv200Judge: 0,
          nanoJudge: 0,
          measures: [this.getDefaultMeasure()]
        }]
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.resetFields()
      })
    },
    // 校验处理措施中的核酸等级是否重复
    validateMeasures () {
      let gradeSet = new Set()
      for (let standard of this.form.standards) {
        gradeSet.clear()
        for (let measure of standard.measures) {
          if (measure.grade && gradeSet.has(measure.grade)) {
            this.$message.error('处理措施配置对应的核酸等级有重复数据，请核对')
            return false
          }
          if (measure.grade) {
            gradeSet.add(measure.grade)
          }
        }
      }
      return true
    },
    // 校验标准中的必填项
    validateIndicators () {
      let hasError = false
      for (let standardIndex = 0; standardIndex < this.form.standards.length; standardIndex++) {
        const standard = this.form.standards[standardIndex]
        // 设置所有指标为已触摸状态，以便显示错误
        for (let i = 0; i < standard.indicators.length; i++) {
          const indicator = standard.indicators[i]
          indicator.nuclearTotalTouched = true
          indicator.qualityConcentrationTouched = true
          indicator.nqTouched = true
          indicator.dv200Touched = true
          indicator.nanoTouched = true
          indicator.gradeTouched = true
        }

        // 核酸总量验证
        if (standard.nuclearTotalJudge === 1) {
          for (let indicatorIndex = 0; indicatorIndex < standard.indicators.length; indicatorIndex++) {
            const indicator = standard.indicators[indicatorIndex]
            if (!indicator.nuclearTotalStandard) {
              this.$message.error(`标准${standardIndex + 1}的第${indicatorIndex + 1}行核酸总量不能为空`)
              hasError = true
              break
            }
          }
        }
        // 质量浓度验证
        if (!hasError && standard.qualityConcentrationJudge === 1) {
          for (let indicatorIndex = 0; indicatorIndex < standard.indicators.length; indicatorIndex++) {
            const indicator = standard.indicators[indicatorIndex]
            if (!indicator.qualityConcentrationStandard) {
              this.$message.error(`标准${standardIndex + 1}的第${indicatorIndex + 1}行质量浓度不能为空`)
              hasError = true
              break
            }
          }
        }
        // N/Q验证
        if (!hasError && standard.nqJudge === 1) {
          for (let indicatorIndex = 0; indicatorIndex < standard.indicators.length; indicatorIndex++) {
            const indicator = standard.indicators[indicatorIndex]
            if (!indicator.nqStandard) {
              this.$message.error(`标准${standardIndex + 1}的第${indicatorIndex + 1}行N/Q不能为空`)
              hasError = true
              break
            }
          }
        }
        // DV200验证
        if (!hasError && standard.dv200Judge === 1) {
          for (let indicatorIndex = 0; indicatorIndex < standard.indicators.length; indicatorIndex++) {
            const indicator = standard.indicators[indicatorIndex]
            if (!indicator.dv200Standard) {
              this.$message.error(`标准${standardIndex + 1}的第${indicatorIndex + 1}行DV200不能为空`)
              hasError = true
              break
            }
          }
        }
        // Nano验证
        if (!hasError && standard.nanoJudge === 1) {
          for (let indicatorIndex = 0; indicatorIndex < standard.indicators.length; indicatorIndex++) {
            const indicator = standard.indicators[indicatorIndex]
            if (!indicator.nanoStandard) {
              this.$message.error(`标准${standardIndex + 1}的第${indicatorIndex + 1}行Nano不能为空`)
              hasError = true
              break
            }
          }
        }
        // 核酸等级验证（必填）
        if (!hasError) {
          for (let indicatorIndex = 0; indicatorIndex < standard.indicators.length; indicatorIndex++) {
            const indicator = standard.indicators[indicatorIndex]
            if (!indicator.grade) {
              this.$message.error(`标准${standardIndex + 1}的第${indicatorIndex + 1}行核酸等级不能为空`)
              hasError = true
              break
            }
          }
        }
        if (hasError) {
          return false
        }
      }
      return true
    },
    // 检查表单验证
    handleValidForm () {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('表单验证失败'))
          }
        })
      })
    },
    // 提交表单
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (!valid) {
          this.$message.error('存在需要判断的指标没填写，请检查！')
          return
        }
        // 首先验证处理措施
        if (!this.validateMeasures()) {
          return
        }

        try {
          // 处理数据，准备提交
          const params = {
            fdnaLevelStandard: this.form.gradeName,
            fdescription: this.form.notes,
            dnaLevelJudgmentList: this.form.standards.map((standard, index) => ({
              fstandardNum: index + 1,
              forderStep: standard.processSteps.join(','),
              fsampleType: standard.sampleTypes.join(','),
              fisDnaTotal: standard.nuclearTotalJudge,
              fisQualityConcentration: standard.qualityConcentrationJudge,
              fisTotalNano: standard.nqJudge,
              fisDv200: standard.dv200Judge,
              fisNanoCon: standard.nanoJudge,
              dnaLevelJudgmentList: standard.indicators.map(indicator => ({
                fstandardNum: index + 1,
                fdnaTotal: indicator.nuclearTotalStandard,
                fqualityConcentration: indicator.qualityConcentrationStandard,
                ftotalNano: indicator.nqStandard,
                fdv200: indicator.dv200Standard,
                fnanoCon: indicator.nanoStandard,
                fdnaLevel: indicator.grade
              })),
              dnaLevelDealList: standard.measures.map(measure => ({
                fstandardNum: index + 1,
                fdnaLevel: measure.grade,
                flevelQc: measure.qcGrade,
                fdealMethod: measure.action,
                fexceptionReport: measure.report
              }))
            })),
            fdnaLevelConfigId: this.type === 2 ? this.id : null
          }
          updateNucleicGrade(params).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            }
          }).catch(error => {
            this.$message.error(error.message || '保存失败')
          })
        } catch (error) {
          this.$message.error(error.message || '保存失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.nucleic-grade-dialog {
  max-height: 90vh;
}

.flex-container {
  display: flex;
  align-items: center;
}

.nucleic-grade-dialog>>>.el-dialog {
  margin-top: 5vh !important;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  border-radius: 4px;
}

.nucleic-grade-dialog>>>.el-dialog__body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  max-height: calc(90vh - 150px);
}

.nucleic-grade-dialog>>>.el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

.nucleic-grade-dialog>>>.el-dialog__footer {
  padding: 10px 20px;
  border-top: 1px solid #EBEEF5;
}

.nucleic-grade-dialog>>>.el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.form-section {
  margin-bottom: 20px;
}

.section-title {
  position: relative;
  font-size: 15px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

/* 自定义表头样式 */
.custom-table-header {
  background-color: #f5f7fa;
  border: 1px solid #EBEEF5;
  border-bottom: none;
}

.header-row {
  display: flex;
  align-items: center;
  height: 40px;
}

.header-cell {
  position: relative;
  box-sizing: border-box;
  text-align: left;
  padding: 8px;
  border-right: 1px solid #EBEEF5;
  overflow: hidden;
}

.nuclearTotal-cell,
.quality-cell {
  width: 180px;
}

.nq-cell,
.dv200-cell,
.nano-cell {
  flex: 1;
  min-width: 150px;
}

.grade-cell {
  width: 120px;
}

.action-cell {
  width: 100px;
}

.header-cell:last-child {
  border-right: none;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-content span {
  min-width: 55px;
  text-align: right;
  margin-right: 5px;
  font-weight: 500;
  color: #333;
}

.judge-select {
  width: 80px;
  margin-left: 5px;
}

.nucleic-grade-indicators-table {
  margin-bottom: 15px;
}

.tips-container {
  background-color: #f8f8f8;
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.tips-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 13px;
  color: #303133;
}

.tips-content {
  color: #666;
  font-size: 12px;
  line-height: 1.6;
}

.standard-container {
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  background-color: #fafafa;
}

.standard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
}

.standard-actions {
  display: flex;
  gap: 8px;
}

.required-label>>>.el-form-item__label:before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

.nucleic-grade-dialog>>>.el-table th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 500;
  padding: 8px 0;
  height: 40px;
}

.nucleic-grade-dialog>>>.el-table td {
  padding: 5px;
}

.actions-column {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.circle-button {
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  border-radius: 50% !important;
  min-width: 28px !important;
}

.measures-container {
  margin-top: 15px;
}

.measure-row {
  display: flex;
  align-items: flex-start;
  border: 1px dashed #DCDFE6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  position: relative;
  background-color: #fafafa;
}

.measure-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.measure-content .el-form-item {
  margin-right: 10px;
  min-width: 250px;
}

.nucleic-grade-indicators-table .el-form-item {
  margin-bottom: 9px;
  margin-top: 9px;
}

.measure-content .el-select {
  width: 150px;
}

.measure-content .el-form-item__label {
  width: 100px !important;
  text-align: right;
}

.measure-actions {
  display: flex;
  gap: 8px;
  margin-left: 15px;
}

/* 确保所有表单元素统一大小 */
.nucleic-grade-dialog>>>.el-input,
.nucleic-grade-dialog>>>.el-select,
.nucleic-grade-dialog>>>.el-button {
  font-size: 12px;
}

/* 表单项样式优化 */
.nucleic-grade-dialog>>>.el-input__inner,
.nucleic-grade-dialog>>>.el-textarea__inner {
  border-radius: 3px;
  border-color: #DCDFE6;
}

/* 让标签居中对齐 */
.nucleic-grade-dialog>>>.el-form-item__label {
  line-height: 28px;
  color: #606266;
}

.nucleic-grade-dialog>>>.el-form-item {
  /* margin-bottom: 18px; */
}

.nucleic-grade-dialog>>>.el-table .cell {
  line-height: 20px;
}

.nucleic-grade-dialog>>>.el-table th>.cell {
  display: inline-flex;
  align-items: center;
}

.nucleic-grade-dialog>>>.el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

/* 优化表头样式 */
.nucleic-grade-dialog>>>.el-table th>.cell>div {
  display: flex;
  align-items: center;
  width: 100%;
}

.nucleic-grade-dialog>>>.el-table th>.cell>div>span {
  min-width: 55px;
  display: inline-block;
  text-align: right;
  margin-right: 5px;
}

.nucleic-grade-dialog>>>.el-table th>.cell .el-select {
  width: 80px !important;
}

/* 优化表格整体样式 */
.nucleic-grade-dialog>>>.el-table {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.nucleic-grade-dialog>>>.el-table::before {
  display: none;
}

/* 表格单元格垂直居中 */
.nucleic-grade-dialog>>>.el-table .el-table__row td {
  height: 40px;
}

/* 输入框样式优化 */
.nucleic-grade-dialog>>>.el-input--mini .el-input__inner {
  padding-left: 8px;
  padding-right: 8px;
}

/* 按钮样式优化 */
.nucleic-grade-dialog>>>.el-button {
  padding: 7px 15px;
}

.nucleic-grade-dialog>>>.el-button--mini {
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 3px;
}

.nucleic-grade-dialog>>>.dialog-footer .el-button {
  min-width: 80px;
}

/* 多选下拉框样式 */
.nucleic-grade-dialog>>>.el-select .el-tag {
  margin: 2px 0 2px 6px;
  height: 20px;
  line-height: 18px;
}

/* 滚动条样式优化 */
.nucleic-grade-dialog>>> ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.nucleic-grade-dialog>>> ::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.nucleic-grade-dialog>>> ::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 表单验证错误样式 */
.nucleic-grade-dialog>>>.el-form-item.is-error .el-input__inner,
.nucleic-grade-dialog>>>.el-form-item.is-error .el-textarea__inner,
.nucleic-grade-dialog>>>.el-form-item.is-error .el-input-group__append,
.nucleic-grade-dialog>>>.el-form-item.is-error .el-input-group__prepend {
  border-color: #F56C6C !important;
}

.nucleic-grade-dialog>>>.el-form-item.is-error .el-input__validateIcon {
  color: #F56C6C !important;
}

.nucleic-grade-dialog>>>.el-form-item__error {
  padding-top: 2px;
  font-size: 12px;
  color: #F56C6C;
}

/* 表格中的表单项错误样式 */
.nucleic-grade-dialog>>>.el-table .el-form-item.is-error {
  margin-bottom: 18px !important;
  margin-top: 0 !important;
}

.nucleic-grade-dialog>>>.el-table .el-form-item.is-error .el-form-item__error {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 9;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 原必填字段样式可以保留，但不再需要 */
.required-field::before {
  content: '*' !important;
  color: #F56C6C !important;
  position: absolute !important;
  left: -8px !important;
  top: 7px !important;
  z-index: 999 !important;
  font-size: 14px !important;
}
</style>
