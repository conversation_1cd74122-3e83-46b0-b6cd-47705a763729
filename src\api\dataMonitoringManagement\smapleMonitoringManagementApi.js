import { myAjax } from '@/util/ajax'

/**
 * 获取获取数据监控列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205699&itf=5799149
 * @param options
 * @returns {*}
 */
export function getSampleMonitoringList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/get_sample_monitor_list',
    data: data,
    ...options
  })
}

/**
 * 导出数据监控列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799677
 * @param options
 * @returns {*}
 */
export function exportSampleMonitoring (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/export_sample_monitor',
    responseType: 'blob',
    data: data,
    ...options
  })
}

export function exportExecutionTable (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/export_execution_table',
    responseType: 'blob',
    data: data,
    ...options
  })
}

/**
 * 获取数据监控状态
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799678
 * @param options
 * @returns {*}
 */
export function fixSampleMonitoringStatus (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/update_sample_monitor_status',
    data: data,
    ...options
  })
}

/**
 * 获取数据监控详情
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799681
 * @param options
 * @returns {*}
 */
export function getOrderDetail (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/get_data_size_detail',
    data: data,
    ...options
  })
}

/**
 * 更新数据监控信息
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799679
 * @param options
 * @returns {*}
 */
export function updateSampleMonitoringInfoList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/update_data_size',
    data: data,
    ...options
  })
}

/**
 * 更新数据监控信息
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205741&itf=5799679
 * @param options
 * @returns {*}
 */
export function updateSampleMonitoringInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/monitor/update_sample_monitor',
    data: data,
    ...options
  })
}
