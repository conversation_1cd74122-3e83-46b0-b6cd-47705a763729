<template>
  <div>
    <el-dialog :visible.sync="visible" :before-close="handleClose" :close-on-click-modal="false" title="多组学快速生成报告"
      width="1000px" @open="handleOpen">
      <el-button size="mini" type="primary" style="margin-bottom: 10px;"
        @click="handleGenerateReport()">生成报告</el-button>
      <el-table ref="table" :cell-style="handleRowStyle" :data="tableData" class="table" border style="width: 100%"
        height="50vh">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="type" label="质控任务编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="number" label="子订单编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reportStatus" label="样本个数" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="auditTime" label="操作" min-width="50" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDetail(scope.row)">生成报告</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--报告生成-->
    <generate-report-dialog :pvisible.sync="generateReportVisible" :qc-result-id-list="qcResultIdList"
      :order-code="orderCode" :report-type="2" @dialogConfirmEvent="getData" />
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util, { awaitWrap, dateFormatter } from '@/util/util'
import { getQcTaskListApi } from '@/api/sequencingManagement/qcReport'

export default {
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    subOrderId: {
      type: Number
    },
    orderCode: {
      type: String
    }
  },
  data () {
    return {
      tableData: [],
      qcResultIdList: [],
      generateReportVisible: false
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    // 获取表格数据
    async getData () {
      const { res } = await awaitWrap(getQcTaskListApi())
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.tableData = data.map(item => {
          const taskInfo = {
            id: item.id,
            type: item.type,
            number: item.number,
            reportStatus: item.reportStatus,
            auditTime: item.auditTime ? dateFormatter(item.auditTime) : ''
          }
          taskInfo.realData = JSON.parse(JSON.stringify(taskInfo))
          util.setDefaultEmptyValueForObject(taskInfo)
          return taskInfo
        })
      }
    },
    // 生成报告
    handleGenerateReport (row) {
      let qcResultIdList = []
      if (row) {
        qcResultIdList.push(row.qcResultId)
      } else {
        if (this.selectedRows.size < 1) {
          this.$message.error('请选择至少一条数据')
          return
        }
        [...this.selectedRows.values()].forEach(row => {
          qcResultIdList.concat(row.qcResultIdList)
        })
      }
      this.generateReportVisible = true
      this.qcResultIdList = qcResultIdList
    }
  }
}
</script>

<style scoped></style>
