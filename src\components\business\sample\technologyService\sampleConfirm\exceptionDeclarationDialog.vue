<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      title="驳回说明"
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template>
          <el-form-item label="异常分类" prop="notes">
            {{reason}}
          </el-form-item>
          <el-form-item label="异常描述" prop="notes">
            {{reason}}
          </el-form-item>
          <el-form-item v-if="picList.length > 0" label="图片说明">
            <img
              :key="index"
              :src="pic"
              v-for="(pic, index) in picList"
              style="width: 100px; height: 100px; margin: 2px;" @click="handlePictureCardPreview(pic)"/>
            <el-dialog  :visible.sync="dialogVisible" title="图片预览" width="500px" append-to-body>
              <img :src="dialogImageUrl" width="100%" alt="">
            </el-dialog>
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: Number || String
    }
  },
  data () {
    return {
      reason: '',
      picList: [],
      dialogVisible: false
    }
  },
  methods: {
    handleOpen () {
      this.getRejectSeason()
    },
    getRejectSeason () {
      this.$ajax({
        url: '/order/get_reject_reason',
        method: 'get',
        data: {
          orderId: this.orderId
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.reason = data.rejectReason
          this.picList = data.picUrlList
        }
      })
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped></style>
