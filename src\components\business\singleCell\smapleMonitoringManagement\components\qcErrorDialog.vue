<template>
  <el-dialog
  title="启动质控分析-错误提示"
  :visible.sync="visible"
  width="600px"
  :before-close="handleClose"
  append-to-body
  >
  <div v-if="errorTypeList.includes(0)" class="error-content">
    <div class="error-type">
      <span class="label">以下样本未下机，无法启动质控分析：</span>
    </div>
    <div class="error-list">
      <el-table
        :data="errorListData1"
        style="width: 100%"
        max-height="300"
        size="mini"
        border
      >
        <el-table-column
          prop="geneNum"
          label="吉因加编号"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="errorMessage"
          label="报错内容"
          min-width="200"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
    </div>
  </div>
  <div v-if="errorTypeList.includes(1)" class="error-content">
    <div class="error-type">
      <span class="label">以下样本存在未完成的质控任务，无法启动：</span>
    </div>
    <div class="error-list">
      <el-table
        :data="errorListData2"
        style="width: 100%"
        max-height="300"
        size="mini"
        border
      >
      <el-table-column
          prop="geneNum"
          label="吉因加编号"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="errorMessage"
          label="报错内容"
          min-width="200"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
    </div>
  </div>
  <div v-if="filterGeneList.length > 0" style="margin-top: 10px;">请确认是否针对其它符合条件的样本继续启动分析？</div>
  <span slot="footer" class="dialog-footer">
    <el-button size="mini" @click="handleClose">关 闭</el-button>
    <el-button v-if="filterGeneList.length > 0" type="primary" size="mini" @click="handleStart(filterGeneList)">继续启动</el-button>
  </span>
</el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {startQcAnalysis} from '@/api/sequencingManagement/singleCell'
import { awaitWrap } from '@/util/util'
export default {
  name: 'qcErrorDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    errorTypeList: {
      type: Array,
      default: () => []
    },
    errorList: {
      type: Array,
      default: () => []
    },
    filterGeneList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    errorListData1 () {
      const errorInfo = this.errorList.find(v => v.errorType === 0) || {}
      return errorInfo.cosQualityAnalysisDeliverDataDTOS || []
    },
    errorListData2 () {
      const errorInfo = this.errorList.find(v => v.errorType === 1) || {}
      return errorInfo.cosQualityAnalysisDeliverDataDTOS || []
    }
  },
  methods: {
    async handleStart (filterGeneList) {
      const {res} = await awaitWrap(startQcAnalysis({
        fgeneNumList: filterGeneList,
        fcondition: 0
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('启动质控分析成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.error-type {
  margin: 10px 0;
}
</style>
