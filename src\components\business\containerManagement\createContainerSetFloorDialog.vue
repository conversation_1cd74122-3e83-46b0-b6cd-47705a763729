<template>
  <div>
    <el-dialog
      :title="`设置${changeText('层')}`"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="750px"
      @open="handleOpen">
      <div>
        <div class="form">
          <div class="form-item">
            <label>容器名称：</label>
            <el-input :style="{width: formWidth}" v-model="currentOperateName" disabled size="mini"></el-input>
          </div>
          <div class="form-item">
            <label>添加{{changeText('层')}}数：</label>
            <el-select v-model="floorTotal" :style="{width: formWidth}" size="mini" filterable @change="handleChangeFloorNum">
              <template v-for="item in maxFloorNum">
                <el-option :key="item" :label="item" :value="item"></el-option>
              </template>
            </el-select>
          </div>
          <template v-if="floorTotal">
            <template v-for="(item, index) in floorSampleTypeInput">
              <div :key="index" class="form-item">
                <label>{{changeText('层')}}数范围：</label>
                <el-input :style="{width: formWidth}" v-model="item.floorNums" size="mini" @blur="handleChangeFloorInfo(index, 'floorNums')"></el-input>
              </div>
              <div :key="index + 'sample'" class="form-item">
                <label>样本类型：</label>
                <el-select
                  v-model="item.sampleType"
                  :disabled="item.floorNums.length === 0"
                  :style="{width: formWidth}"
                  size="mini"
                  multiple
                  collapse-tags
                  filterable
                  @change="handleChangeFloorInfo(index, 'sampleType')">
                  <template v-for="item in sampleTypeOptions">
                    <el-option :key="item.value" :label="item.value" :value="item.value"></el-option>
                  </template>
                </el-select>
              </div>
              <div :key="index + 'icon'">
                <i
                  v-if="index === floorSampleType.length - 1 && showAddIcon"
                  class="el-icon-plus icon"
                  style="font-size: 30px;font-weight: 600;cursor: pointer;"
                  @click="handleAddFloorSampleType"></i>
              </div>
            </template>
          </template>
        </div>
        <template v-if="floor.length > 0">
          <div class="floor">
            <template v-for="item in floor">
              <div :key="item.num" class="floor-item">
                <p>{{item.num}}{{changeText('层')}}</p>
                <p>{{item.sampleTypes.toString()}} </p>
              </div>
            </template>
          </div>
        </template>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/**
   * 如果层数发生改变，所有的数据重新设计，如果某列的样本类型发生改变，该层children全部清除
   * 1、用户输入floorSampleTypeInput相关的值；
   * 2、floorSampleTypeInput 转换为 floorSampleType；
   * 3、监听floorSampleType，与floor进行每列的比对；
   * 4、最后提交的时候，跟我们传进来的层次进行对比，如果长度不同，则以新值为准，否则就对比sampleTypes,如果完全一样，则以旧值为主
   * **/
// import num from './components/cc'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    currentOperateName: { // 当前名称
      type: String
    },
    sampleTypeOptions: { // 样本值
      type: Array,
      default () {
        return []
      }
    },
    currentSetObj: {
      type: Object
    },
    containerType: {
      type: String
    }
  },
  watch: {
    floorSampleType (newVal) {
      let floor = []
      newVal.forEach(item => {
        if (item.floorNums.length > 0) {
          item.floorNums.forEach(v => {
            let vv = {
              num: v,
              sampleTypes: item.sampleType
            }
            floor.push(vv)
          })
        }
      })
      function compare (v1, v2) { // 按层数大小重排序
        if (v1.num < v2.num) {
          return -1
        } else if (v1.num > v2.num) {
          return 1
        } else {
          return 0
        }
      }
      this.floor = floor.sort(compare)
    }
  },
  computed: {
    showAddIcon () {
      let allComplete = this.floorSampleType.every(item => {
        return item.floorNums.length > 0
      })
      if (!allComplete) return false // 存在没有选择添加层数项，则无法再次添加项
      let allFloors = []
      this.floorSampleType.forEach(item => {
        allFloors.push(...item.floorNums)
      })
      let sets = new Set(allFloors)
      return [...sets].length < this.floorTotal
    },
    hasSelectFloor () { // 已选层数，防止重复选择
      let allFloors = []
      this.floorSampleType.forEach(item => {
        allFloors.push(...item.floorNums)
      })
      let sets = new Set(allFloors)
      return [...sets]
    }
  },
  data () {
    return {
      maxFloorNum: 9,
      floorTotal: 0,
      formWidth: '220px',
      floor: [], // {num: '', sampleTypes: []}，就两个提交的时候再对比
      floorSampleTypeInput: [
        {floorNums: '', sampleType: []}
      ],
      // 输入的正确值，只有floorSampleTypeInput输入的值正确通过后，才会赋值给这个,避免用户直接输入的错误值影响显示
      floorSampleTypeInputCorrect: [
        {floorNums: '', sampleType: []}
      ],
      floorSampleType: [
        {floorNums: [], sampleType: []}
      ],
      floorNecessarySampleType: new Map()
    }
  },
  methods: {
    // 打开弹窗初始化
    handleOpen () {
      this.$nextTick(() => {
        let formInput = this.currentSetObj.formInput || {}
        let content = this.currentSetObj.content || []
        this.floorTotal = formInput.total || 0
        this.floorSampleTypeInput = [{floorNums: '', sampleType: []}]
        this.floorSampleTypeInputCorrect = [{floorNums: '', sampleType: []}]
        this.floorSampleType = [{floorNums: [], sampleType: []}]
        if (formInput.floorSampleType) {
          this.floorSampleType = util.deepCopy(formInput.floorSampleType)
          this.floorSampleTypeInput = []
          this.floorSampleTypeInputCorrect = []
          this.floorSampleType.forEach(item => {
            let v = {
              floorNums: item.floorNums.toString(),
              sampleType: util.deepCopy(item.sampleType)
            }
            this.floorSampleTypeInput.push(v)
            this.floorSampleTypeInputCorrect.push(util.deepCopy(v))
          })
        }
        this.floor = []
        content.forEach(item => {
          let v = {
            num: +item.num,
            sampleTypes: item.sampleTypes
          }
          this.floor.push(v)
        })
        this.setFloorNecessarySampleType()
      })
    },
    // 设置层必须包含的样本类型
    setFloorNecessarySampleType () {
      this.floorNecessarySampleType.clear()
      if (this.currentSetObj.content && this.currentSetObj.content.length > 0) {
        this.currentSetObj.content.forEach(item => {
          if (item.children && item.children.length > 0) {
            let shelfSampleTypeSet = new Set()
            item.children.forEach(v => {
              v.sampleTypes.forEach(vv => {
                shelfSampleTypeSet.add(vv)
              })
            })
            this.floorNecessarySampleType.set(item.num, [...shelfSampleTypeSet])
          }
        })
      }
      console.log([...this.floorNecessarySampleType.valueOf()])
    },
    // 改变层数
    handleChangeFloorNum (val) {
      console.log(val)
      // 在层数减少的时候进行
      if (val < this.floor.length) {
        let canModify = true // 可以修改，为true时将处理过的floorSampleType赋值过去
        let floorSampleType = JSON.parse(JSON.stringify(this.floorSampleType))
        for (let i = 0; i < floorSampleType.length; i++) {
          let v = floorSampleType[i]
          let bigNum = v.floorNums.filter(num => {
            return num > val
          })
          console.log('bigNum', bigNum)
          if (bigNum.length > 0) {
            let content = this.currentSetObj.content || []
            let cantDelFloor = content.filter(item => {
              return bigNum.indexOf(item.num) > -1 && item.useHole && item.useHole > 0
            })
            if (cantDelFloor.length > 0) {
              let msg = ''
              cantDelFloor.forEach((item, i, a) => {
                let m = i === a.length - 1 ? '' + item.num : '' + item.num + '，'
                msg += m
              })
              this.$message.error(`第${msg}${this.changeText('层')}已有孔位被使用，无法删除！`)
              this.floorTotal = this.floor.length
              return
            }
            for (let ii = 0; ii < bigNum.length; ii++) {
              let vv = bigNum[ii]
              /**
               * 这里加判断条件，判断是否可以修改
               * **/
              let index = v.floorNums.indexOf(vv)
              if (index > -1) {
                v.floorNums.splice(index, 1)
              }
            }
          }
        }
        console.log(floorSampleType)
        if (canModify) {
          // 去除没有数量的空对象
          floorSampleType = floorSampleType.filter(item => {
            return item.floorNums.length > 0
          })
          console.log(floorSampleType)
          this.floorSampleTypeInput = []
          this.floorSampleTypeInputCorrect = []
          floorSampleType.forEach(item => {
            let v = {
              floorNums: item.floorNums.toString(),
              sampleType: item.sampleType
            }
            this.floorSampleTypeInput.push(v)
            this.floorSampleTypeInputCorrect.push(util.deepCopy(v))
          })
          this.floorSampleType = floorSampleType
        }
      }
    },
    handleAddFloorSampleType () {
      this.floorSampleType.push({floorNums: [], sampleType: []})
      this.floorSampleTypeInput.push({floorNums: '', sampleType: []})
      this.floorSampleTypeInputCorrect.push({floorNums: '', sampleType: []})
    },
    // 改变层数信息
    handleChangeFloorInfo (index, filedName) {
      let resetFormInput = (i) => {
        this.$set(this.floorSampleTypeInput, i, util.deepCopy(this.floorSampleTypeInputCorrect[i]))
      }
      let data = util.deepCopy(this.floorSampleType[index])
      if (filedName === 'sampleType') {
        this.floorSampleTypeInputCorrect = util.deepCopy(this.floorSampleTypeInput)
        data.sampleType = this.floorSampleTypeInputCorrect[index].sampleType
        this.$set(this.floorSampleType, index, data)
      } else if (filedName === 'floorNums') {
        if (this.floorSampleTypeInput[index].floorNums) { // 如果用户输入的值不为空
          let floorNumsSet = new Set()
          let floorNumsInput = util.deepCopy(this.floorSampleTypeInput[index]).floorNums.trim()
          let regx = /[1-9]([\s+,，\\-]*[0-9])*$/
          if (regx.test(floorNumsInput)) {
            // 这个操作是清空当前index所选的层数，避免再次聚焦再失焦后判断错误
            // dataFloorNums是在发现用户输入错误时，再给sampleType把上一次的值赋值回去
            let dataFloorNums = util.deepCopy(data.floorNums)
            data.floorNums = []
            this.$set(this.floorSampleType, index, data)
            // $nextTick是因为要等computed 中的hasSelectFloor更新后再进行下面的操作
            this.$nextTick(() => {
              // 将所有空格、中文逗号都变为英文逗号
              let input = floorNumsInput.replace(/\s+/g, ',').replace(/，/, ',').replace(/(\s+-\s+)|(\s+-)|(-\s+)/, '-')
              // 转换字符串为数组,去除空项
              let f = input.split(',').filter(item => { return item })
              let numCorrect = true
              for (let i = 0; i < f.length; i++) {
                if (f[i].indexOf('-') > -1) { // 包含横杠，则取两个数中间的所有值
                  let fArr = f[i].split('-').filter(item => { return item })
                  if (fArr.length !== 2) { // 包含横杠但是不是两端都有数字
                    this.$message.error('输入区间格式不正确')
                    numCorrect = false
                    break
                  } else {
                    let correctNum = fArr.every(v => {
                      let num = +v
                      return !Number.isNaN(num) && num > 0 && num <= this.floorTotal
                    })
                    if (correctNum) {
                      let arr = fArr.map(v => { return +v })
                      let max = Math.max(...arr)
                      let min = Math.min(...arr)
                      if (max <= this.floorTotal && min > 0) {
                        let foolA = []
                        do { // 得到两端区间所有的数字
                          foolA.push(min)
                          min++
                        } while (min <= max)
                        let hasSelect = foolA.filter(item => {
                          return this.hasSelectFloor.indexOf(item) > -1
                        })
                        if (hasSelect.length > 0) {
                          this.$message.error(`${hasSelect.toString()}层已被设置`)
                          numCorrect = false
                          break
                        } else {
                          foolA.forEach(item => {
                            floorNumsSet.add(item)
                          })
                        }
                      } else {
                        this.$message.error(`请确保输入的值在1-${this.floorTotal}之间`)
                        numCorrect = false
                        break
                      }
                    } else {
                      this.$message.error(`输入数字且数字的值需在1至 ${this.floorTotal}之间`)
                      numCorrect = false
                      break
                    }
                  }
                } else {
                  let num = +f[i]
                  if (!Number.isNaN(num) && num > 0 && num <= this.floorTotal) {
                    if (this.hasSelectFloor.indexOf(num) > -1) {
                      this.$message.error(`${f[i]}层已被设置`)
                      return
                    }
                    floorNumsSet.add(+f[i])
                  } else {
                    this.$message.error('输入格式不正确')
                  }
                }
              }
              if (numCorrect) {
                this.floorSampleTypeInputCorrect = util.deepCopy(this.floorSampleTypeInput)
                data.floorNums = [...floorNumsSet]
                this.$set(this.floorSampleType, index, data)
              } else {
                // 输入不符合规范，则回到上一次输入的值
                data.floorNums = dataFloorNums
                this.$set(this.floorSampleType, index, data)
                resetFormInput(index)
              }
            })
          } else {
            resetFormInput(index)
            this.$message.error('输入格式不正确')
          }
        } else {
          // 如果用户输入的值为空，则返回他上次输入的正确值
          resetFormInput(index)
        }
      }
    },
    // 判断两个数组对象是否相同
    judgeArray (arr1, arr2) {
      if (arr1.length !== arr2.length) return false
      return arr1.every(item => {
        return arr2.includes(item)
      })
    },
    handleConfirm () {
      let allSelected = this.floor.every(item => {
        return item.sampleTypes && item.sampleTypes.length > 0
      })
      if (this.floorTotal > 0 && this.floor.length === this.floorTotal && allSelected) {
        let hasSetFloorData = this.currentSetObj.content || []
        let floor = []
        let hasSetFloorMap = new Map()
        hasSetFloorData.forEach(item => {
          hasSetFloorMap.set(+item.num, item)
        })
        let allSampleTypeIsTrue = true
        let errorNum = 0
        for (let i = 0; i < this.floor.length; i++) {
          let item = this.floor[i]
          let thisFloorNecessarySampleType = this.floorNecessarySampleType.get(item.num)
          if (thisFloorNecessarySampleType && thisFloorNecessarySampleType.length > 0) {
            let c = thisFloorNecessarySampleType.every(v => {
              return item.sampleTypes.includes(v)
            })
            if (!c) {
              allSampleTypeIsTrue = false
              errorNum = item.num
              break
            }
          }
        }
        if (!allSampleTypeIsTrue) {
          this.$message.error(`${errorNum}层的样本类型不符合`)
          return
        }
        this.floor.forEach(item => {
          let v = {
            tag: '层',
            filed: 'floor',
            num: +item.num,
            sampleTypes: item.sampleTypes
          }
          let floorItem = hasSetFloorMap.has(item.num) ? hasSetFloorMap.get(item.num) : {}
          if (floorItem.id) {
            v.id = floorItem.id
          }
          if (floorItem.useHole !== undefined) {
            v.useHole = floorItem.useHole
          }
          if (floorItem.formInput) {
            v.formInput = floorItem.formInput
          }
          // let isChangeSampleType = this.judgeArray(item.sampleTypes, hasSetFloorMap.get(+item.num).sampleTypes)
          v.children = hasSetFloorMap.has(item.num) ? hasSetFloorMap.get(item.num).children : []
          floor.push(v)
        })
        let formInput = {
          name: this.currentOperateName,
          total: this.floorTotal,
          floorSampleType: this.floorSampleType
        }
        this.$emit('dialogConfirmEvent', JSON.parse(JSON.stringify({formInput, content: floor})))
      } else {
        this.$message.error('层未设置完整')
      }
    },
    // 通过判断lab改变文字显示
    changeText (text) {
      let result = text
      if (this.containerType === 'C') {
        switch (text) {
          case '层':
            result = '组'
            break
          case '架':
            result = '抽屉'
            break
          case '盒':
            result = '道'
            break
        }
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  .form{
    display: flex;
    flex-wrap: wrap;
    .form-item{
      margin-bottom: 20px;
      margin-right: 20px;
    }
  }
  .floor{
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background: #f2f2f2;
    padding: 10px 0;
    .floor-item{
      background: $color;
      width: 40%;
      border-radius: 4px;
      color: #fff;
      text-align: center;
      margin: 0 auto 20px auto;
      p{
        line-height: 2;
      }
    }
  }
</style>
