import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import 'pdfjs-dist/build/pdf.worker.entry'

// 起始坐标x y 宽 高
const cropX = 135
const cropY = 220
const cropWidth = 900
const cropHeight = 1200

/**
 * 处理PDF文件
 * @param file 文件
 * @returns {Promise<File> | Promise<never>} 图片文件
 */
export async function handlePdfToImage (file) {
  if (!file) return
  let name = file.name
  // 初始化PDFJS
  pdfjsLib.GlobalWorkerOptions.workerSrc = window.pdfjsWorker

  try {
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await pdfjsLib.getDocument(arrayBuffer).promise

    // 只处理第一页
    const page = await pdf.getPage(1)
    const viewport = page.getViewport({scale: 2})

    // 创建隐藏canvas
    const canvas = document.createElement('canvas')
    canvas.width = viewport.width
    canvas.height = viewport.height

    // 渲染PDF到canvas
    await page.render({
      canvasContext: canvas.getContext('2d'),
      viewport
    }).promise

    const dataUrl = await cropImage(canvas.toDataURL('image/png'))
    // dataUrl 转成File类型
    name = name.replace(/\.pdf$/, '.png')
    return dataURLtoFile(dataUrl, name)
  } catch (error) {
    console.error('PDF处理失败:', error)
  }
}

/**
 * 裁剪图片
 * @param image 图片
 * @returns {Promise<String>} 裁剪图片base64
 */
function cropImage (image) {
  if (!image) return
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      const canvas = document.createElement('canvas')
      canvas.width = cropWidth
      canvas.height = cropHeight

      const ctx = canvas.getContext('2d')
      ctx.drawImage(
        img,
        cropX, cropY,
        cropWidth, cropHeight,
        0, 0,
        cropWidth, cropHeight
      )

      resolve(canvas.toDataURL('image/png'))
    }
    img.src = image
  })
}

/**
 * dataURL 转 File
 * @param dataUrl base64
 * @param filename 文件名
 * @returns {File}
 */
function dataURLtoFile (dataUrl, filename) {
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}
