<template>
  <div class="selected-tasks-panel">
    <div class="selected-tasks-header">
      <div class="title">已选任务单</div>
      <div class="header-actions">
        <el-button type="danger" size="small" @click="handleBatchRemove"
          :disabled="tableData.length === 0">批量移除</el-button>
        <el-button type="primary" @click="handleExport" :disabled="tableData.length === 0">导出质控任务单</el-button>
      </div>
    </div>

    <el-table ref="table" :data="tableData" size="mini" :cell-style="handleRowStyle" border style="width: 100%"
      height="calc(100vh - 300px)" row-key="ftaskOrderId" @selection-change="handleSelectionChange"
      @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="拖拽" width="60" align="center">
        <template slot-scope="scope">
          <i class="el-icon-rank drag-handle" style="cursor: move; color: #409EFF;"></i>
        </template>
      </el-table-column>
      <el-table-column prop="ftaskOrderNum" label="任务单编号" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="ftaskOrderType" label="任务单类型" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="ftaskOrderNumCount" label="任务量" width="80"></el-table-column>
      <el-table-column prop="ftaskOrderStatus" label="任务单状态" width="100"></el-table-column>
      <el-table-column prop="ftaskCreateTime" label="创建时间" width="150"></el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleRemove(scope.row)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import mixins from '@/util/mixins'

export default {
  name: 'SelectedTasksPanel',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  mounted () {
    this.initSort()
  },
  watch: {
    tableData () {
      this.initSort()
    }
  },
  data () {
    return {
      sortable: null
    }
  },
  beforeDestroy () {
    if (this.sortable) {
      this.sortable.destroy()
    }
  },
  methods: {
    // 初始化拖拽排序
    initSort () {
      this.$nextTick(() => {
        try {
          const tableEl = this.$refs.selectedTable.$el.querySelector('.el-table__body-wrapper tbody')
          if (tableEl) {
            // 销毁之前的实例
            if (this.sortable) {
              this.sortable.destroy()
            }

            this.sortable = Sortable.create(tableEl, {
              handle: '.drag-handle',
              animation: 150,
              ghostClass: 'sortable-ghost',
              chosenClass: 'sortable-chosen',
              dragClass: 'sortable-drag',
              onEnd: (evt) => {
                try {
                  const { oldIndex, newIndex } = evt
                  if (oldIndex !== newIndex && oldIndex !== undefined && newIndex !== undefined) {
                    // 通知父组件更新排序
                    this.$emit('sort-tasks', { oldIndex, newIndex })
                  }
                } catch (error) {
                  console.error('拖拽排序失败:', error)
                  this.$message.error('拖拽排序失败，请重试')
                }
              }
            })
          }
        } catch (error) {
          console.error('初始化拖拽排序失败:', error)
        }
      })
    },

    // 表格选择变化
    handleSelectionChange (selection) {
      this.tableSelectedRows = selection
    },

    // 单个移除任务单
    handleRemove (row) {
      this.$emit('remove-task', row)
    },

    // 批量移除任务单
    handleBatchRemove () {
      if (this.tableSelectedRows.length === 0) {
        this.$message.warning('请先选择要移除的任务单')
        return
      }

      this.$confirm(`确定要移除选中的 ${this.tableSelectedRows.length} 个任务单吗？`, '批量移除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 通知父组件批量移除
        this.$emit('batch-remove-tasks', this.tableSelectedRows)
        // 清空选中状态
        this.$refs.selectedTable.clearSelection()
        this.$message.success('批量移除成功')
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 导出Excel
    handleExport () {
      if (this.tableData.length === 0) {
        this.$message.warning('请先添加需要导出的任务单')
        return
      }

      // 模拟导出功能
      this.$message({
        message: '正在生成Excel文件...',
        type: 'info'
      })

      setTimeout(() => {
        // 模拟Excel文件下载
        const csvContent = this.generateCSVContent()
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `质控任务单_${new Date().getTime()}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        this.$message.success('导出成功')

        // 通知父组件导出完成
        this.$emit('export-complete')
      }, 1500)
    },

    // 生成CSV内容
    generateCSVContent () {
      const headers = ['任务单编号', '任务单类型', '任务量', '任务单状态', '创建时间', '创建人', '优先级']
      const csvRows = [headers.join(',')]

      this.tableData.forEach(item => {
        const row = [
          item.ftaskOrderNum,
          item.ftaskOrderType,
          item.ftaskOrderNumCount,
          item.ftaskOrderStatus,
          item.ftaskCreateTime,
          item.ftaskCreateUser || '',
          item.ftaskPriority || ''
        ]
        csvRows.push(row.join(','))
      })

      return '\uFEFF' + csvRows.join('\n') // 添加BOM以支持中文
    }
  }
}
</script>

<style lang="scss" scoped>
.selected-tasks-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .selected-tasks-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f2f5;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background-color: #409eff;
        border-radius: 2px;
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;

      .el-button {
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.el-button--primary {
          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          &:disabled {
            opacity: 0.6;
          }
        }

        &.el-button--danger {
          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
          }

          &:disabled {
            opacity: 0.6;
          }
        }
      }
    }
  }

  // 表格样式优化
  ::v-deep .el-table {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-table__header {
      th {
        background-color: #fafbfc;
        color: #606266;
        font-weight: 600;
        border-bottom: 2px solid #ebeef5;
      }
    }

    .el-table__body {
      tr {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f8f9ff;
        }

        td {
          border-bottom: 1px solid #f5f7fa;
        }
      }
    }

    .el-button--text {
      color: #409eff;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        color: #66b1ff;
        transform: translateY(-1px);
      }
    }
  }
}

// 拖拽排序样式
::v-deep .sortable-ghost {
  opacity: 0.6;
  background-color: #c8ebfb;
  transform: rotate(2deg);
}

::v-deep .sortable-chosen {
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

::v-deep .sortable-drag {
  background-color: #fff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  transform: rotate(-1deg);
}

.drag-handle {
  cursor: move !important;
  color: #409eff;
  font-size: 16px;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.2);
  }
}
</style>
