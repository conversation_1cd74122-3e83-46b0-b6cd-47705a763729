<template>
  <div>
    <el-table
      :data="tableData"
      ref="table"
      border
      class="table"
      size="mini"
      height="calc(100vh - 320px)"
    >
      <el-table-column prop="sampleNum" min-width="100" label="吉因加编号"></el-table-column>
      <el-table-column prop="cancerName" min-width="100" label="匹配癌种"></el-table-column>
      <el-table-column prop="currentStep" min-width="100" label="检测环节"></el-table-column>
      <el-table-column prop="testResult" min-width="100" label="检测结果"></el-table-column>
      <el-table-column prop="reportTime" min-width="100" label="报告时间"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../util/util'
export default {
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 探针探针使用列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_use_detail',
        method: 'get',
        data: {
          fid: this.$route.query.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              sampleNum: v.sampleNum,
              cancerName: v.cancarName,
              currentStep: v.currentStep,
              testResult: v.testResult,
              reportTime: v.reportTime
            }
            item.realData = item
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped></style>
