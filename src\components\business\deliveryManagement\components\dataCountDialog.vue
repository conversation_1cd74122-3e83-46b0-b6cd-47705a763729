<template>
  <el-dialog
    append-to-body
    title="数据求和"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1000px"
    @opened="handleOpen">
    <el-table
      ref="table"
      :data="tableData"
      class="data-table"
      size="mini"
      border
      height="400px"
      style="width: 100%"
    >
      <el-table-column prop="geneNum" label="吉因加编号" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="oriSampleName" label="原始原本名称" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="allOrderDataSize" label="下单数据量" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="allDataSize" label="过滤前产量（G）" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="allAfterFilterDataSize" label="过滤后产量（G）" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="allAfterFilterDataSizeMinus" label="差值" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="allAfterFilterDataSizeDivide" label="产出比" min-width="120" show-overflow-tooltip></el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import {getDataCountList} from '../../../../api/deliveryManagement'
import {awaitWrap} from '../../../../util/util'

export default {
  name: 'dataCountDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    subOrderIds: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async handleOpen () {
      this.tableData = []
      await this.getDetailInfo()
    },
    async getDetailInfo () {
      const {res} = await awaitWrap(getDataCountList({
        fcosDeliverOrderIdList: this.ids,
        fcosDeliverBatchDetailIdList: this.subOrderIds
      }, {loadingDom: '.data-table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        this.tableData = res.data.map(v => {
          return {
            oriSampleName: v.foriSampleName,
            geneNum: v.fgeneNum,
            allDataSize: v.fallDataSize,
            allAfterFilterDataSize: v.fallAfterFilterDataSize,
            allAfterFilterDataSizeMinus: v.fallAfterFilterDataSizeMinus,
            allAfterFilterDataSizeDivide: v.fallAfterFilterDataSizeDivide,
            allOrderDataSize: v.fallOrderDataSize
          }
        }) || []
      }
    }
  }
}
</script>
