<template>
  <el-dialog
    title="驳回"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="500px"
    @opened="handleOpen">
    <div>
      <div class="tips">
        确认驳回所选记录? 请填写驳回说明
      </div>
      <el-form ref="form" :model="form" label-suffix=":">
        <el-form-item label="驳回说明" prop="note">
          <el-input v-model="form.note" size="mini" type="textarea" :rows="3" maxlength="200" clearable placeholder="请输入驳回说明" />
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {awaitWrap} from '@/util/util'
import {auditAddTestInfo} from '../../../../../api/sequencingManagement/repeatClinicManagementApi'

export default {
  name: 'fixStatusDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      loading: false, // 控制提交按钮的加载状态
      statusOptions: [],
      form: {
        note: '' // 用于存储选中的交付状态
      }
    }
  },
  methods: {
    /**
     * 对话框打开时获取表格数据
     * 目前未在代码中实现具体功能，可能用于加载下拉列表选项等
     */
    handleOpen () {
      this.$refs.form.resetFields()
    },
    /**
     * 处理状态修改确认操作
     * 提交表单数据并根据返回结果提示用户操作是否成功
     */
    async handleConfirm () {
      this.loading = true
      // 确认判断
      const {res} = await awaitWrap(auditAddTestInfo({
        fcosAddTestingIds: this.ids,
        fauditOrRelinquish: 1,
        fauditRejectionDescription: this.form.note
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('驳回成功')
        this.$emit('dialogConfirmEvent') // 触发父组件数据刷新
        this.visible = false // 关闭对话框
      }
      this.loading = false // 结束加载
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  display: flex;
  align-items: center;
  .status {
    color: #FEC171;
    font-size: 20px;
    margin-right: 5px;
  }
}

</style>
