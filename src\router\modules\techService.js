// 科服相关路由
export default [
  // 科服订单详情路由
  {
    path: '/technologyService/entryIlluminaChangeInfo',
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/illumina/changeInfo.vue')
  },
  {
    path: '/technologyService/entryMGIChangeInfo',
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/mgi/changeInfo.vue')
  },
  {
    path: '/technologyService/entryTissueChangeInfo',
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/tissueOrNucleicAcid/changeInfo.vue')
  },
  {
    path: '/technologyService/singleCell',
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/singleCell/changeInfo.vue')
  },
  // 科服管理路由
  {
    path: '/business/view/technologyService',
    component: () => import('@/components/business/sample/technologyService/index.vue'),
    children: [
      {
        path: 'orderReview',
        meta: {
          title: '科服订单审核'
        },
        component: () => import('@/components/business/sample/technologyService/orderReview/overview.vue')
      },
      {
        path: 'sampleConfirm',
        meta: {
          title: '样本管理'
        },
        component: () => import('@/components/business/sample/technologyService/sampleConfirm/overview.vue')
      },
      {
        path: 'sampleManagement',
        meta: {
          title: '样本处理'
        },
        component: () => import('@/components/business/sample/technologyService/sampleManagement/overview.vue')
      }
    ]
  },
  // 科服订单详情子页面
  {
    path: '/business/subpage/technologyService/entryIlluminaLibraryOrder',
    meta: {
      title: 'illumina文库订单详情'
    },
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/illumina/index.vue')
  },
  {
    path: '/business/subpage/technologyService/entryMGILibraryOrder',
    meta: {
      title: 'MGI文库订单详情'
    },
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/mgi/index.vue')
  },
  {
    path: '/business/subpage/technologyService/entryTissueOrder',
    meta: {
      title: '组织或核酸订单详情'
    },
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/tissueOrNucleicAcid/index.vue')
  },
  {
    path: '/business/subpage/technologyService/singleCell',
    meta: {
      title: '单细胞订单详情'
    },
    component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/singleCell/index.vue')
  }
]
