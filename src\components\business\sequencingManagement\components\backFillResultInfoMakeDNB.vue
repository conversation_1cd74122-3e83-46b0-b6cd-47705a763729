<template>
  <div>
    <el-form :model="form" ref="form" size="mini" label-suffix=":" inline :rules="rules" label-width="80px">
      <el-form-item label="检测人" prop="detector">
        <el-select v-model.trim="form.detector" filterable multiple collapse-tags clearable placeholder="请选择检测人">
          <el-option v-for="(item, index) in detectorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="复核人" prop="auditor">
        <el-select v-model.trim="form.auditor" filterable clearable multiple collapse-tags placeholder="请选择复核人">
          <el-option v-for="(item, index) in auditorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <vxe-table
      ref="tableRef"
      border
      resizable
      height="300px"
      keep-source
      show-overflow
      :data="tableData"
      size="mini"
      :edit-rules="validRules"
      :valid-config="{msgMode: 'full'}"
      :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
    >
<!--      <vxe-column type="seq" title="序号" width="60"></vxe-column>-->
      <vxe-column field="samplePosition" show-overflow title="样本孔位" width="120" :edit-render="renderConfig"/>
      <vxe-column field="originSamplePosition" show-overflow title="原始样本定位" width="120" :edit-render="renderConfig"/>
      <vxe-column field="sampleName" title="实验样本 " min-width="80"></vxe-column>
      <vxe-column field="geneCode" title="核酸/吉因加编号" min-width="110">
        <template #default="{ row }">
          <span v-if="row.geneCode !== '查看样本详情'">{{row.geneCode}}</span>
          <div v-else class="link" @click="handleDetail(row)">{{row.geneCode}}</div>
        </template>
      </vxe-column>
<!--      <vxe-column field="libType" title="文库类型" min-width="120"></vxe-column>-->
<!--      <vxe-column field="libModificationType" title="文库修饰类型" min-width="120"></vxe-column>-->
      <vxe-column field="projectName" title="项目名称" min-width="120"></vxe-column>

      <vxe-column field="oldSampleName" title="原始样本名称" min-width="110"></vxe-column>
      <vxe-column field="concentration" title="浓度（ng/ul)" min-width="120"></vxe-column>
      <vxe-column field="clipLength" title="片段长度" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="inputQuantity" title="输入量（pmol）" min-width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="tubeNumber" title="管数" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="sampleSize" title="总取样本量" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="te" title="TE（ul）" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="dnbName" title="DNB名称" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="size" title="排单数据量/G" min-width="120"></vxe-column>
      <vxe-column field="dnbConcentration1" title="DNB浓度 1" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="dnbConcentration2" title="DNB浓度 2" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="dnbConcentration3" title="DNB浓度 3" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="dnbConcentration4" title="DNB浓度 4" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="dnbCalculatedConcentration" title="DNB计算浓度" min-width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="theoreticalTotal" title="理论相对量" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="relativeTotal" title="相对总量" min-width="120" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="dnbPoolingVolume" title="DNBpooling体积" min-width="140" :edit-render="renderConfig"></vxe-column>

      <vxe-column field="targetPosition" show-overflow title="目标孔位" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="targetMaterial" show-overflow title="目标耗材" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="taskCode" title="任务单编号" min-width="140"></vxe-column>
    </vxe-table>
    <div class="tips">
      <span style="margin-right: 10px; color: #409EFF">样本总数: {{tableData.length}}</span>
      提交后数据如需修改，请点击【信息变更】，确认继续提交？
    </div>
  </div>
</template>

<script>
import {awaitWrap} from '../../../../util/util'
import {getTestOrCheck, saveResult} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'backFillResultDialog',
  props: {
    info: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number,
      default: null
    }
  },
  mounted () {
    this.init()
  },
  watch: {
    info: {
      handler: function () {
        this.init()
      },
      deep: true
    }
  },
  data () {
    return {
      form: {
        detector: '', // 检测人
        auditor: '' // 审核人
      },
      detectorOptions: [],
      auditorOptions: [],
      tableData: [],
      renderConfig: {name: '$input', props: {clearable: true}},
      rules: {
        detector: [{required: true, message: '请输入检测人', trigger: 'change'}],
        auditor: [{required: true, message: '请输入复核人', trigger: 'change'}]
      },
      validRules: {
        clipLength: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*$/, message: '请输入整数', trigger: 'change' }
        ],
        sampleSize: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        tubeNumber: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*$/, message: '请输入整数', trigger: 'change' }
        ],
        te: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        inputQuantity: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        // concentration: [
        //   {required: true, message: '请输入', trigger: 'change'}
        // ],
        dnbConcentration1: [
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        dnbConcentration2: [
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        dnbConcentration3: [
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        dnbConcentration4: [
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        dnbCalculatedConcentration: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        theoreticalTotal: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        relativeTotal: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        dnbPoolingVolume: [
          {required: true, message: '请输入', trigger: 'change'},
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' }
        ],
        dnbName: [
          {required: true, message: '请输入', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    init () {
      this.tableData = []
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
      this.getTestOrCheckPerson(1, 'detectorOptions')
      this.getTestOrCheckPerson(2, 'auditorOptions')
      this.info.forEach(v => {
        const item = {
          projectName: v.fprojectName,
          sampleName: v.fsampleName,
          geneCode: this.handleSetGeneCode(v), // 吉因加编号,
          fnucleateCode: v.fnucleateCode,
          fgeneCode: v.fgeneCode,
          geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
          geneInfo: {
            fgeneCode: v.fgeneCode,
            fnucleateCode: v.fnucleateCode
          },
          size: v.fsize,
          oldSampleName: v.foldSampleName,
          lastConcentration: v.flastConcentration,
          sampleSize: v.fsampleSize,
          te: v.fte,
          clipLength: v.fclipLength,
          inputQuantity: v.finputQuantity,
          tubeNumber: v.ftubeNumber,
          concentration: v.fconcentration,
          dnbConcentration: v.fdnbConcentration,
          dnbCalculatedConcentration: v.fdnbCalculatedConcentration,
          dnbPoolingVolume: v.fdnbPoolingVolume,
          libModificationType: v.flibModificationType,
          libType: v.flibType,
          dnbName: v.fdnbName,
          samplePosition: v.fsamplePosition, // 样本孔位
          originSamplePosition: v.foriginSamplePosition, // 原始样本定位
          volume: v.fvolume, // 体积
          targetPosition: v.ftargetPosition, // 目标定位
          targetMaterial: v.ftargetMaterial, // 目标耗材
          dnbConcentration1: v.fdnbConcentration1,
          dnbConcentration2: v.fdnbConcentration2,
          dnbConcentration3: v.fdnbConcentration3,
          dnbConcentration4: v.fdnbConcentration4,
          theoreticalTotal: v.ftheoreticalTotal,
          relativeTotal: v.frelativeTotal,
          taskCode: v.ftaskCode
        }
        this.tableData.push(item)
      })
    },
    async getTestOrCheckPerson (roleType, key) {
      const {res} = await awaitWrap(getTestOrCheck({roleType}))
      if (res && res.code === this.SUCCESS_CODE) {
        this[key] = []
        const data = res.data || []
        data.forEach(v => {
          const item = {
            label: v.frealName,
            value: v.fid
          }
          this[key].push(item)
        })
      }
    },
    handleValidForm () {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('表单存在错误，请检查'))
          }
        })
      })
    },
    handleValidate () {
      const $table = this.$refs.tableRef
      return new Promise(async (resolve, reject) => {
        const valid = await $table.fullValidate(true)
        if (valid) {
          this.$message.error('表格存在错误，请检查')
          reject(new Error('表格存在错误，请检查'))
        } else {
          resolve()
        }
      })
    },
    setParams () {
      const list = []
      this.tableData.forEach(v => {
        const item = {
          fprojectName: v.projectName,
          fsampleName: v.sampleName,
          fnucleateCode: v.fnucleateCode,
          fgeneCode: v.fgeneCode,
          fsize: v.size,
          foldSampleName: v.oldSampleName,
          flastConcentration: v.lastConcentration,
          fsampleSize: v.sampleSize || '',
          fte: v.te || '',
          fclipLength: v.clipLength || '',
          finputQuantity: v.inputQuantity || '',
          ftubeNumber: v.tubeNumber || '',
          fconcentration: v.concentration || '',
          fdnbConcentration: v.dnbConcentration || '',
          fdnbCalculatedConcentration: v.dnbCalculatedConcentration || '',
          fdnbPoolingVolume: v.dnbPoolingVolume || '',
          fdnbName: v.dnbName ? 'DNB' + v.dnbName : '',
          ftaskCode: v.taskCode || '',
          fsamplePosition: v.samplePosition, // 样本孔位
          foriginSamplePosition: v.originSamplePosition, // 原始样本定位
          ftargetPosition: v.targetPosition, // 目标定位
          ftargetMaterial: v.targetMaterial, // 目标耗材
          flibModificationType: v.libModificationType,
          flibType: v.libType,
          fdnbConcentration1: v.dnbConcentration1,
          fdnbConcentration2: v.dnbConcentration2,
          fdnbConcentration3: v.dnbConcentration3,
          fdnbConcentration4: v.dnbConcentration4,
          ftheoreticalTotal: v.theoreticalTotal,
          frelativeTotal: v.relativeTotal
        }
        list.push(item)
      })
      return {
        resultList: list,
        ftestPerson: this.form.detector,
        fcheckPerson: this.form.auditor
      }
    },
    async handleSubmit () {
      await Promise.all([this.handleValidate(), this.handleValidForm()])
      const params = this.setParams()
      // 判断浓度是否为 0
      if (params.resultList.some(v => v.fdnbConcentration1 === '0' || v.fdnbConcentration2 === '0' ||
        v.fdnbConcentration3 === '0' || v.fdnbConcentration4 === '0' || v.fdnbCalculatedConcentration === '0')) {
        await this.$confirm('导入结果内存在浓度为0的数据，请确认数据是否正确？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      if (params.fcheckPerson.some(v => params.ftestPerson.findIndex(vv => v === vv) !== -1)) {
        this.$message.error('检测人不能与复核人相同')
        return
      }
      const {res} = await awaitWrap(saveResult(params, this.type))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success(res.message)
        return true
      } else {
        if (res && res.data) {
          this.$showSequencingErrorDialog({tableData: res.data, isShowButton: false})
        } else {
          this.$message.error(res.message)
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
