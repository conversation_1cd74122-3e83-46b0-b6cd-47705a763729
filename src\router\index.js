import Vue from 'vue'
import Router from 'vue-router'
import Main from '@/components/main'
import setupGuards from './guards'

// 导入各个模块的路由配置
import baseRoutes from './modules/base'
import sampleRoutes from './modules/sample'
import orderRoutes from './modules/order'
import techServiceRoutes from './modules/techService'
import sequencingRoutes from './modules/sequencing'
import materialRoutes from './modules/material'
import onlineImageRoutes from './modules/onlineImage'
import basicDataRoutes from './modules/basicData'
import reportRoutes from './modules/report'
import productRoutes from './modules/product'
import containerRoutes from './modules/container'

// vue-router在v3.1版本会有一个报重复错误的bug
// 现在可以回退router版本到3.0
// 也可以如下重写 push 方法
const routerPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return routerPush.call(this, location).catch(error => error)
}

Vue.use(Router)

// 定义主要业务路由
const businessRoutes = [
  {
    path: '/business',
    component: Main,
    children: [
      {
        path: 'view',
        component: () => import('@/components/layout/index.vue'),
        redirect: 'view/containerManagement',
        children: [
          ...containerRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...sampleRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...orderRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...sequencingRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...materialRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...basicDataRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...reportRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...productRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          })),
          ...onlineImageRoutes.filter(route => route.path.includes('/business/view')).map(route => ({
            ...route,
            path: route.path.replace('/business/view/', '')
          }))
        ]
      },
      {
        path: 'sub',
        component: () => import('@/components/layout/subpage.vue'),
        children: [
          ...containerRoutes.filter(route => route.path.includes('/business/sub')).map(route => ({
            ...route,
            path: route.path.replace('/business/sub/', '')
          })),
          ...sampleRoutes.filter(route => route.path.includes('/business/sub')).map(route => ({
            ...route,
            path: route.path.replace('/business/sub/', '')
          })),
          ...orderRoutes.filter(route => route.path.includes('/business/sub')).map(route => ({
            ...route,
            path: route.path.replace('/business/sub/', '')
          })),
          ...reportRoutes.filter(route => route.path.includes('/business/sub')).map(route => ({
            ...route,
            path: route.path.replace('/business/sub/', '')
          })),
          ...onlineImageRoutes.filter(route => route.path.includes('/business/sub')).map(route => ({
            ...route,
            path: route.path.replace('/business/sub/', '')
          })),
          // 线下检测详情
          {
            path: 'offineDetectionDetails',
            component: () => import('@/components/business/offineDetectionDetails/overview.vue')
          },
          {
            path: 'enterLibraryDetail',
            meta: {
              pageTitle: '领取入库'
            },
            component: () => import('@/components/business/sampleLibraryManagement/enterLibraryDetail.vue')
          },
          {
            path: 'moveStorageApplication',
            meta: {
              pageTitle: '移库'
            },
            component: () => import('@/components/business/sampleLibraryManagement/moveStorageApplication.vue')
          }
        ]
      }
    ]
  }
]

// 定义子页面路由
const subpageRoutes = [
  {
    path: '/business/subpage',
    component: () => import('@/components/layout/subpage.vue'),
    children: [
      ...sequencingRoutes.filter(route => route.path.includes('/business/subpage')).map(route => ({
        ...route,
        path: route.path.replace('/business/subpage/', '')
      })),
      ...materialRoutes.filter(route => route.path.includes('/business/subpage')).map(route => ({
        ...route,
        path: route.path.replace('/business/subpage/', '')
      })),
      ...basicDataRoutes.filter(route => route.path.includes('/business/subpage')).map(route => ({
        ...route,
        path: route.path.replace('/business/subpage/', '')
      })),
      ...reportRoutes.filter(route => route.path.includes('/business/subpage')).map(route => ({
        ...route,
        path: route.path.replace('/business/subpage/', '')
      })),
      ...techServiceRoutes.filter(route => route.path.includes('/business/subpage')).map(route => ({
        ...route,
        path: route.path.replace('/business/subpage/', '')
      }))
    ]
  }
]

// 定义解读报告路由
const unscrambleRoutes = [
  {
    path: '/business/unscramble',
    component: () => import('@/components/layout/subpage.vue'),
    children: reportRoutes.filter(route => route.path.includes('/business/unscramble')).map(route => ({
      ...route,
      path: route.path.replace('/business/unscramble/', '')
    }))
  }
]

// 创建路由实例
const router = new Router({
  mode: 'history',
  routes: [
    ...baseRoutes,
    ...techServiceRoutes.filter(route => !route.path.includes('/business/')),
    ...businessRoutes,
    ...subpageRoutes,
    ...unscrambleRoutes
  ]
})

// 设置路由守卫
setupGuards(router)

export default router
