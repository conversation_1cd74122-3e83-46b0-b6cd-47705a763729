import util from '../../util/util'
import Cookies from 'js-cookie'

const state = {
  userInfo: {
    id: '',
    username: '',
    avatar: '',
    emailPrefix: ''
  },
  loginId: null, // 登录的id
  menuStatus: 0, // 默认展开
  deviceSize: 'normal', // normal or mini
  activeIndex: '/business/view/containerManagement',
  pathTabs: [],
  enterLibraryOrder: '', // 入库单号
  containerId: '', // 容器id
  forecastData: null, // 新增预测数据
  dxData: null, // 新增DX数据,
  mutationsData: null, // 新增遗传变异数据
  analysisRsId: null,
  matchCancer: null,
  isPreview: 0,
  libraryOperatingData: null,
  clinicalInfo: {
    sampleBasicId: null,
    sampleNum: null
  },
  templateId: '', // 模板id
  deliveryInfo: { // 物料模块中的确认快递、修改邮件
    id: null,
    type: null
  },
  applicationInfo: { // 物料\宣传品申请详情数据
    id: null,
    type: null,
    needReissue: false
  },
  pixelRatio: null,
  pathogenicType: 0, // 当前病原是什么类型，0-非tNGS，1-tNGS
  sampleQuality: [],
  needUpdated: []
}

const mutations = {
  setValue (state, payload) {
    if (payload.category === 'userInfo') {
      state.userInfo = payload.userInfo
      util.setSessionInfo('userInfo', payload.userInfo)
    } else if (payload.category === 'menuStatus') {
      state.menuStatus = payload.menuStatus
      Cookies.set('menuStatus', payload.menuStatus)
    } else if (payload.category === 'activeIndex') {
      state.activeIndex = payload.activeIndex
      util.setSessionInfo('activeIndex', payload.activeIndex)
    } else if (payload.category === 'deviceSize') {
      state.deviceSize = payload.deviceSize
    } else if (payload.category === 'pathTabs') {
      state.pathTabs = payload.pathTabs
    } else if (payload.category === 'enterLibraryOrder') {
      state.enterLibraryOrder = payload.enterLibraryOrder
      util.setSessionInfo('enterLibraryOrder', payload.enterLibraryOrder)
    } else if (payload.category === 'containerId') {
      state.containerId = payload.containerId
      util.setSessionInfo('containerId', payload.containerId)
    } else if (payload.category === 'loginId') {
      state.loginId = payload.loginId
      util.setSessionInfo('loginId', payload.loginId)
    } else if (payload.category === 'forecastData') {
      state.forecastData = payload.forecastData
      util.setSessionInfo('forecastData', payload.forecastData)
    } else if (payload.category === 'dxData') {
      state.dxData = payload.dxData
      util.setSessionInfo('dxData', payload.dxData)
    } else if (payload.category === 'mutationsData') {
      state.mutationsData = payload.mutationsData
      util.setSessionInfo('mutationsData', payload.mutationsData)
    } else if (payload.category === 'analysisRsId') {
      state.analysisRsId = payload.analysisRsId
      util.setSessionInfo('analysisRsId', payload.analysisRsId)
    } else if (payload.category === 'matchCancer') {
      state.matchCancer = payload.matchCancer
      util.setSessionInfo('matchCancer', payload.matchCancer)
    } else if (payload.category === 'clinicalInfo') {
      state.clinicalInfo = payload.clinicalInfo
      util.setSessionInfo('clinicalInfo', payload.clinicalInfo)
    } else if (payload.category === 'templateId') {
      state.templateId = payload.templateId
      util.setSessionInfo('templateId', payload.templateId)
    } else if (payload.category === 'deliveryInfo') {
      state.deliveryInfo = payload.deliveryInfo
      util.setSessionInfo('deliveryInfo', payload.deliveryInfo)
    } else if (payload.category === 'applicationInfo') {
      state.applicationInfo = payload.applicationInfo
      util.setSessionInfo('applicationInfo', payload.applicationInfo)
    } else if (payload.category === 'libraryOperatingData') {
      state.libraryOperatingData = payload.libraryOperatingData
      util.setSessionInfo('libraryOperatingData', payload.libraryOperatingData)
    } else if (payload.category === 'isPreview') {
      state.isPreview = payload.isPreview
      util.setSessionInfo('isPreview', payload.isPreview)
    } else if (payload.category === 'pathogenicType') {
      state.pathogenicType = payload.pathogenicType
      util.setSessionInfo('pathogenicType', payload.pathogenicType)
    } else if (payload.category === 'sampleQuality') {
      state.sampleQuality = payload.sampleQuality
      util.setSessionInfo('sampleQuality', payload.sampleQuality)
    } else if (payload.category === 'needUpdated') {
      state.needUpdated = payload.needUpdated
      util.setSessionInfo('needUpdated', payload.needUpdated)
    } else if (payload.category === 'pixelRatio') {
      state.pixelRatio = payload.pixelRatio
      util.setSessionInfo('pixelRatio', payload.pixelRatio)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations
}
