'use strict'
const path = require('path')
const fs = require('fs')
const config = require('../config')
const ExtractTextPlugin = require('extract-text-webpack-plugin')
const packageConfig = require('../package.json')

exports.assetsPath = function (_path) {
  const assetsSubDirectory = process.env.NODE_ENV === 'production'
    ? config.build.assetsSubDirectory
    : config.dev.assetsSubDirectory

  return path.posix.join(assetsSubDirectory, _path)
}

exports.cssLoaders = function (options) {
  options = options || {}

  const cssLoader = {
    loader: 'css-loader',
    options: {
      sourceMap: options.sourceMap
    }
  }

  const postcssLoader = {
    loader: 'postcss-loader',
    options: {
      sourceMap: options.sourceMap
    }
  }

  // generate loader string to be used with extract text plugin
  function generateLoaders (loader, loaderOptions) {
    const loaders = options.usePostCSS ? [cssLoader, postcssLoader] : [cssLoader]

    if (loader) {
      loaders.push({
        loader: loader + '-loader',
        options: Object.assign({}, loaderOptions, {
          sourceMap: options.sourceMap
        })
      })
    }

    // Extract CSS when that option is specified
    // (which is the case during production build)
    if (options.extract) {
      return ExtractTextPlugin.extract({
        use: loaders,
        fallback: 'vue-style-loader'
      })
    } else {
      return ['vue-style-loader'].concat(loaders)
    }
  }

  function resolveResource(name) {
    return path.resolve(__dirname, '../src/style/' + name);
  }
  function generateSassResourceLoader() {
    var loaders = [
      cssLoader,
      // 'postcss-loader',
      'sass-loader',
      {
        loader: 'sass-resources-loader',
        options: {
          // it need a absolute path
          resources: [resolveResource('common.scss')]
        }
      }
    ];
    if (options.extract) {
      return ExtractTextPlugin.extract({
        use: loaders,
        fallback: 'vue-style-loader'
      })
    } else {
      return ['vue-style-loader'].concat(loaders)
    }
  }

  // https://vue-loader.vuejs.org/en/configurations/extract-css.html
  return {
    css: generateLoaders(),
    postcss: generateLoaders(),
    less: generateLoaders('less'),
    sass: generateSassResourceLoader(),
    scss: generateSassResourceLoader(),
    stylus: generateLoaders('stylus'),
    styl: generateLoaders('stylus')
  }
}

// Generate loaders for standalone style files (outside of .vue)
exports.styleLoaders = function (options) {
  const output = []
  const loaders = exports.cssLoaders(options)

  for (const extension in loaders) {
    const loader = loaders[extension]
    output.push({
      test: new RegExp('\\.' + extension + '$'),
      use: loader
    })
  }

  return output
}

exports.createNotifierCallback = () => {
  const notifier = require('node-notifier')

  return (severity, errors) => {
    if (severity !== 'error') return

    const error = errors[0]
    const filename = error.file && error.file.split('!').pop()

    notifier.notify({
      title: packageConfig.name,
      message: severity + ': ' + error.name,
      subtitle: filename || '',
      icon: path.join(__dirname, 'logo.png')
    })
  }
}

exports.readJson = function (dir) {
  return new Promise(function (resolve, reject) {
    fs.readFile(dir, 'utf8', function (err, data) {
      if(err) {
        console.log(err)
        reject()
      }
      resolve(JSON.parse(data))
    })
  })
}

exports.writeJson = function (sourDir, sourceKey, tarDir, cb) {
  exports.readJson(sourDir).then(function (sourData) {
    exports.readJson(tarDir).then (function (tarData) {
      let data = sourData[sourceKey]
      // // 目标文件的keys
      // let keys = Object.keys(tarData)
      // keys.forEach(function (item) {
      //   if (sourData[item] === undefined) {
      //     sourData[item] = tarData[item]
      //   } else {
      //     // 判断是不是对象
      //     if (sourData[item] instanceof Object && tarData[item] instanceof Object) {
      //       let k = Object.keys(tarData[item])
      //       k.forEach(function (v) {
      //         if (sourData[item][k] === undefined) {
      //           sourData[item][v] = tarData[item][v]
      //         }
      //       })
      //     }
      //   }
      // })
      // 重写json
      fs.writeFile(tarDir, JSON.stringify(data, null, 2), 'utf8', function (err) {
        if (err) {
          console.log(err)
          reject(err)
          return
        }
        cb()
      })
    })
  })
}
