<template>
  <div>
    <el-row>
     <el-col :span="8">
       <el-select
         :disabled="disabled"
         v-model="currentDate.year"
         size="mini"
         clearable
         style="width: 100%"
         placeholder="年"
         @change='judgeDay'>
         <el-option
           :key="item"
           :label="item"
           :value="item"
           v-for="item in years">
         </el-option>
       </el-select>
     </el-col>
     <el-col :span="8">
       <el-select
         :disabled="currentDate.year === '' || disabled"
         v-model="currentDate.month"
         clearable
         size="mini"
         style="width: 100%"
         placeholder="月"
         @change='judgeDay'>
         <el-option
           :key="item"
           :label="String(item).length === 1 ? String('0'+item): String(item)"
           :value="item"
           v-for="item in months">
         </el-option>
       </el-select>
     </el-col>
     <el-col :span="8">
       <el-select
         :disabled="currentDate.month === '' || disabled"
         :class="{'error':hasError}"
         v-model="currentDate.day"
         clearable
         size="mini"
         style="width: 100%"
         placeholder="日">
         <el-option
           :key="item"
           :label="String(item).length===1 ? String('0'+item): String(item)"
           :value="item"
           v-for="item in days">
         </el-option>
       </el-select>
     </el-col>
    </el-row>

  </div>
</template>
<script>
export default {
  name: 'myDatePicker',
  props: {
    isDisabled: {
      type: Boolean,
      default: false
    },
    sourceDate: {
      type: String
    },
    // outDate: { // 必须要使用value
    //   type: String
    // },
    // index: {
    //   type: Number
    // },
    // dateName: {
    //   type: String
    // },
    value: {
      type: String,
      default: '',
      required: false
    }
  },
  created () {
    this.getFullYears()
    this.getFullDays()
  },
  watch: {
    // value (newVal) {
    //   this.time = newVal
    // },
    // time (newVal) {
    //   console.log(newVal)
    // },
    sourceDate (newVal) {
      // console.log(newVal)
      if (newVal !== '' && newVal !== null && newVal !== undefined) {
        let arr = newVal.split('-')
        this.currentDate.year = arr[0]
        this.currentDate.month = arr[1] === undefined ? '' : arr[1]
        this.currentDate.day = arr[2] === undefined ? '' : arr[2]
      }
    },
    value: {
      handler (newVal) {
        this.date = newVal
        if (newVal !== '' && newVal !== null && newVal !== undefined) {
          let arr = newVal.split('-')
          this.currentDate.year = arr[0]
          this.currentDate.month = arr[1] === undefined ? '' : arr[1]
          this.currentDate.day = arr[2] === undefined ? '' : arr[2]
        } else {
          this.currentDate.year = ''
          this.currentDate.month = ''
          this.currentDate.day = ''
        }
      },
      immediate: true
    },
    isDisabled (newVal) {
      this.disabled = newVal
      if (newVal) {
        this.currentDate = {
          year: '',
          month: '',
          day: ''
        }
      }
    },
    normalMaxDays () {
      this.getFullDays()
      if (this.currentDate.year && this.currentDate.day > this.normalMaxDays) {
        this.currentDate.day = ''
      }
    },
    currentDate: {
      handler (newValue, oldValue) {
        this.judgeDay()
        this.hasError = !(newValue.year && newValue.month && newValue.day)
        this.emitDate()
      },
      deep: true
    }
  },
  data () {
    return {
      date: this.value,
      currentDate: {
        year: '',
        month: '',
        day: ''
      },
      maxYear: new Date().getFullYear(),
      minYear: 1910,
      years: [],
      months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      normalMaxDays: 31,
      days: [],
      hasError: false,
      disabled: this.isDisabled
    }
  },
  methods: {
    emitDate () {
      let time = '' // 暂默认传给父组件时间戳形式
      // if (this.currentDate.year && this.currentDate.month && this.currentDate.day) {
      //   let month = this.currentDate.month < 10 ? ('0' + this.currentDate.month) : this.currentDate.month
      //   let day = this.currentDate.day < 10 ? ('0' + this.currentDate.day) : this.currentDate.day
      //   let dateStr = this.currentDate.year + '-' + month + '-' + day
      //   timestamp = new Date(dateStr).getTime()
      // } else {
      //   timestamp = ''
      // }
      let month = ''
      let day = ''
      if (this.currentDate.year !== '') {
        if (this.currentDate.month !== '') {
          if (this.currentDate.day !== '') {
            month = this.currentDate.month < 10 ? ('0' + Number(this.currentDate.month)) : this.currentDate.month
            day = this.currentDate.day < 10 ? ('0' + Number(this.currentDate.day)) : this.currentDate.day
            time = this.currentDate.year + '-' + month + '-' + day
          } else {
            month = this.currentDate.month < 10 ? ('0' + Number(this.currentDate.month)) : this.currentDate.month
            time = this.currentDate.year + '-' + month
          }
        } else {
          time = String(this.currentDate.year)
        }
      }
      this.$emit('input', String(time))
      this.$emit('change', String(time))
      // this.$emit('outDate', String(time), this.index, this.dateName)
    },
    timestampToTime (timestamp) {
      let dateObject = {}
      if (typeof timestamp === 'number') {
        dateObject.year = new Date(timestamp).getFullYear()
        dateObject.month = new Date(timestamp).getMonth() + 1
        dateObject.day = new Date(timestamp).getDate()
        return dateObject
      }
    },
    getFullYears () {
      for (let i = this.maxYear; i >= this.minYear; i--) {
        this.years.push(i)
      }
    },
    getFullDays () {
      this.days = []
      for (let i = 1; i <= this.normalMaxDays; i++) {
        this.days.push(i)
      }
    },
    judgeDay () {
      if (this.currentDate.year === '') {
        this.currentDate.month = ''
        this.currentDate.day = ''
      }
      if (this.currentDate.year !== '' && this.currentDate.month === '') {
        this.currentDate.day = ''
      }
      if (this.currentDate.month !== '') {
        if ([4, 6, 9, 11, '04', '06', '09', '11'].indexOf(this.currentDate.month) !== -1) {
          this.normalMaxDays = 30 // 小月30天
          if (this.currentDate.day && this.currentDate.day === 31) {
            this.currentDate.day = ''
          }
        } else if (this.currentDate.month === '02') {
          if (this.currentDate.year) {
            if (
              (this.currentDate.year % 4 === 0 &&
                  this.currentDate.year % 100 !== 0) ||
                this.currentDate.year % 400 === 0
            ) {
              this.normalMaxDays = 29 // 闰年2月29天
            } else {
              this.normalMaxDays = 28 // 闰年平年28天
            }
          } else {
            this.normalMaxDays = 28// 闰年平年28天
          }
        } else {
          this.normalMaxDays = 31// 大月31天
        }
      }
    }
  }
}
</script>
<style scoped>
</style>
