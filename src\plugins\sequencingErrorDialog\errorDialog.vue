<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="提示" :width="showTaskCode ? '1000px' : '800px'"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)">
      <template>
        <el-table
          :data="showTableData"
          stripe
          border
          max-height="400px"
        >
          <el-table-column prop="row" label="行数"></el-table-column>
          <el-table-column prop="fsampleName" label="实验样本"></el-table-column>
          <el-table-column v-if="showTaskCode" prop="ftaskCode" label="任务单编号"></el-table-column>
          <el-table-column prop="ferrorContent" label="报错内容"></el-table-column>
        </el-table>

        <span slot="footer" class="dialog-footer" style="display: flex; justify-content: space-between; align-items: center">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh">
              <icon-svg icon-class="icon-refresh"/>
            </button>
          </el-pagination>
          <span>
            <el-button v-if="isShowButton" :loading="downloadLoading" type="primary" size="mini" @click="handleDownErrorFile">导出报错样本</el-button>
          </span>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import util from 'Util'
import mixins from '../../util/mixins'
import IconSvg from '../../components/common/iconSvg.vue'

export default {
  name: `errorDialog`,
  mixins: [mixins.tablePaginationCommonData],
  components: {IconSvg},
  computed: {
    showTaskCode () {
      const data = this.tableData[0] || {}
      return data.ftaskCode
    },
    showTableData () {
      return this.tableData.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize + this.pageSize)
    }
  },
  data () {
    return {
      visible: false,
      downloadLoading: false,
      isShowButton: true,
      totalPage: 0,
      loading: false,
      tableData: []
    }
  },
  methods: {
    handleClose () {
      this.visible = false
      this.tableData = []
      this.downloadLoading = false
    },
    handleDownErrorFile () {
      this.downloadLoading = true
      this.$ajax({
        url: '/experiment/schedule/download_error_task_list',
        data: {
          errorLogDTOS: this.tableData
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.visible = false
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table td, .el-table th {
  padding: 6px 0;
}

.title {
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
}
</style>
