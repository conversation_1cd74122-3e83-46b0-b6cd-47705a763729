<template>
  <el-dialog
    title="探针弃用"
    :visible.sync="visible"
    width="450px"
    @close="handleClose"
  >
    <div style="word-break: break-all">
        您正在进行探针弃用操作，请再次确认是否要继续弃用以下探针：{{rows.map(v => v.hybirdProbeName).join(',')}}
        <div style="color: #eec384">注意: 探针弃用后将不能再使用，也不能恢复启用</div>
    </div>
    <el-form :model="form" :rules="rules" ref="form" label-width="80px">
      <el-form-item label="弃用原因" prop="reason">
        <el-select
              v-model.trim="form.reason"
              size="mini"
              filterable
              clearable
              placeholder="请选择">
              <el-option v-for="(item, index) in reasonList" :label="item" :value="item" :key="index"></el-option>
            </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
              v-model.trim="form.description"
              size="mini"
              type="textarea"
              clearable
              maxlength="200"
              placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button size="mini" type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {abandonProbe} from '@/api/basicDataManagement/probeApi'
import {awaitWrap} from '../../../util/util'
import mixins from '../../../util/mixins'

export default {
  name: 'ProbeCancelDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    rows: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      visible: false,
      form: {
        reason: '',
        description: ''
      },
      reasonList: ['合成原因', '定制错误', '前端要求', '其他'],
      rules: {
        reason: [
          { required: true, message: '请输入弃用原因', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入详细描述', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
    },
    handleClose () {
      this.$refs.form.resetFields()
      this.visible = false
    },
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const {res} = await awaitWrap(abandonProbe({
            ids: this.ids,
            freorderReason: this.form.reason,
            ffailureReorderReason: this.form.description
          }))
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('操作成功')
            this.$emit('dialogConfirmEvent')
            this.visible = false
          }
        }
      })
    }
  }
}
</script>
