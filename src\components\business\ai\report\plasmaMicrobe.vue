<template>
  <div class="card-wrapper">
    <el-table :data="tableData"
              border style="width: 100%">
      <el-table-column prop="microbialIndex" label="微生物指数"></el-table-column>
      <el-table-column prop="microorganismRanking" label="排名"></el-table-column>
      <el-table-column prop="humanHerpesvirusType4EbVirus" label="人类疱疹病毒第四型（EB病毒）"></el-table-column>
      <el-table-column prop="hepatitisBVirus" label="乙型肝炎病毒"></el-table-column>
      <el-table-column prop="humanPapillomavirus" label="人乳头瘤病毒"></el-table-column>
      <el-table-column prop="helicobacterpyloriColumn" label="幽门螺旋杆菌列"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_plasma_microbes',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || {}
        const plasmaMicrobe = {
          id: info.fid,
          microbialIndex: info.fmicrobialIndex,
          microorganismRanking: info.fmicroorganismRanking,
          humanHerpesvirusType4EbVirus: info.fhumanHerpesvirusType4EbVirus,
          hepatitisBVirus: info.fhepatitisBVirus,
          humanPapillomavirus: info.fhumanPapillomavirus,
          helicobacterpyloriColumn: info.fhelicobacterpyloriColumn
        }
        this.tableData = [plasmaMicrobe]
      }
    }
  }
}
</script>

<style scoped></style>
