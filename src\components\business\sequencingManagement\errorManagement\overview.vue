<template>
  <div>
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="异常登记时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 200px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneCode" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('021006001', 'buttons')" size="mini" type="primary" @click="handleSign">标记处理</el-button>
      <el-button v-if="$setAuthority('021006002', 'buttons')" size="mini" plain type="primary" @click="handleNoticeException">发送邮件</el-button>
      <el-button size="mini" plain type="primary" @click="handleSearch">查 询</el-button>
      <el-button size="mini" plain @click="handleReset">重 置</el-button>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="实验样本 " prop="sampleName" show-overflow-tooltip min-width="150"></el-table-column>
        <el-table-column label="核酸/吉因加编号" prop="nucleateCode" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="原始样本名称" prop="oldSampleName" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="任务单编号" prop="taskCode" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="是否告知客户" prop="isSentEmailText" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常状态" prop="statusText" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常登记时间" prop="signTime" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常登记人" prop="signer" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常环节" prop="workflowId" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常描述" prop="describe" show-overflow-tooltip min-width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.realData.pictures" class="link" @click="handleShowDetail(scope.row)">
              {{scope.row.describe}}
            </div>
            <div v-else>{{scope.row.describe}}</div>
          </template>
        </el-table-column>
        <el-table-column label="生产片区" prop="productionArea" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常处理措施" prop="handleMeasures" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常处理人" prop="handler" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常处理时间" prop="handlingTime" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="异常编号" prop="code" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="操作" prop="isNotified">
          <template slot-scope="scope">
            <div v-if="scope.row.isSentEmail && $setAuthority('021006003', 'buttons')" class="link" @click="handleEmailDetail(scope.row)">查看邮件</div>
            <div v-if="!scope.row.isSentEmail || !$setAuthority('021006003', 'buttons')" @click="handleEmailDetail(scope.row)">-</div>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <sign-handler-dialog
      :pvisible.sync="signHandlerVisible"
      :ids="exceptionIds"
      @dialogConfirmEvent="getData">
    </sign-handler-dialog>
    <exception-notice-dialog
      :pvisible.sync="exceptionNoticeVisible"
      :source="1"
      :is-detail="isDetail"
      :exception-ids="exceptionIds"
      @dialogConfirmEvent="getData"
      @dialogCloseEvent="getData">
    </exception-notice-dialog>
    <error-img-detail-dialog
      :pvisible.sync="errorImgDetailVisible"
      :pictures="pictures"
    />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import signHandlerDialog from './components/signHandlerDialog' // 标记处理
import exceptionNoticeDialog from '../components/exceptionNoticeDialog'
import util, {awaitWrap} from '../../../../util/util'
import {getErrorList} from '../../../../api/sequencingManagement/sequencingManagementApi'
import ErrorImgDetailDialog from './components/errorImgDetailDialog' // 发送异常邮件
export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ErrorImgDetailDialog,
    signHandlerDialog,
    exceptionNoticeDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 41 + 42 + 32)
    this.handleSearch()
  },
  data () {
    return {
      signHandlerVisible: false,
      exceptionNoticeVisible: false,
      isDetail: false,
      exceptionIds: [],
      errorImgDetailVisible: false,
      pictures: [],
      form: {
        sampleName: '',
        time: [],
        geneCode: ''
      },
      tableData: []
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    // 标记异常
    handleSign () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择样本！')
        return
      }
      this.exceptionIds = [...this.selectedRows.keys()]
      this.signHandlerVisible = true
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        fgeneCodeList: util.setGroupData(this.formSubmit.geneCode, '、', false),
        fstartSignTime: time[0],
        fendSignTime: time[1],
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getErrorList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.rows || []
        rows.forEach(v => {
          const processFlow = {
            1: 'pooling', 2: '转化', 4: '环化', 5: 'makeDNB'
          }
          const item = {
            id: v.fid,
            sampleName: v.fsampleName,
            nucleateCode: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
            oldSampleName: v.foldSampleName,
            taskCode: v.ftaskCode,
            isSentEmailText: v.fisSentEmail ? '是' : '否',
            isSentEmail: v.fisSentEmail,
            statusText: v.fstatus ? '已处理' : '待处理',
            signTime: v.fsignTime,
            signer: v.fsigner,
            describe: v.fdescribe,
            workflowId: processFlow[v.fworkflowId],
            pictures: v.fpicture ? JSON.parse(v.fpicture) : '',
            productionArea: v.fproductionArea,
            status: v.fstatus,
            handleMeasures: v.fhandleMeasures,
            handler: v.fhandler,
            handlingTime: v.fhandlingTime,
            code: v.fcode
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 查看图片详情
    handleShowDetail (row) {
      this.pictures = row.pictures.map(v => v.fileAbsolutePath)
      this.errorImgDetailVisible = true
    },
    // 查看邮件详情
    handleEmailDetail (row) {
      this.exceptionIds = [row.id]
      this.isDetail = true
      this.exceptionNoticeVisible = true
    },
    // 发送异常通知
    handleNoticeException () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择样本！')
        return
      }
      this.isDetail = false
      // 是否告知客户”为“否”，否则提示：异常已发送客户，请勿重复操作！
      const rows = [...this.selectedRows.values()]
      if (rows.some(v => v.isSentEmail)) {
        this.$message.error('异常已发送客户，请勿重复操作！')
        return
      }
      this.exceptionIds = [...this.selectedRows.keys()]
      this.exceptionNoticeVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
</style>
