// 物料管理相关路由
export default [
  {
    path: '/business/view/materialInventoryManagement',
    meta: {
      title: '物料库存管理'
    },
    component: () => import('@/components/business/materials/materialInventoryManagement.vue')
  },
  {
    path: '/business/view/publicityInventoryManagement',
    meta: {
      title: '宣传品库存管理'
    },
    component: () => import('@/components/business/materials/publicityInventoryManagement.vue')
  },
  {
    path: '/business/view/materialApplicationManagement',
    meta: {
      title: '物料申请管理'
    },
    component: () => import('@/components/business/materials/materialApplicationManagement.vue')
  },
  {
    path: '/business/view/publicityApplicationManagement',
    meta: {
      title: '宣传品申请管理'
    },
    component: () => import('@/components/business/materials/publicityApplicationManagement.vue')
  },
  {
    path: '/business/view/deliveryManagement',
    meta: {
      title: '发货管理'
    },
    component: () => import('@/components/business/materials/deliveryManagement.vue')
  },
  {
    path: '/business/view/grantReport',
    meta: {
      title: '发放报表'
    },
    component: () => import('@/components/business/materials/grantReport.vue')
  },
  {
    path: '/business/subpage/deliveryManagementSaveInfo',
    component: () => import('@/components/business/materials/deliveryManagementSaveInfo.vue')
  },
  {
    path: '/business/subpage/deliveryManagementDetail',
    component: () => import('@/components/business/materials/deliveryManagementDetail.vue')
  },
  {
    path: '/business/subpage/applicationDetail',
    component: () => import('@/components/business/materials/applicationDetail.vue')
  }
]
