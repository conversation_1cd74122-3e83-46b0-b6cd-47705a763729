<template>
  <el-dialog
    title="审核通过"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="500px"
    @opened="handleOpen">
    <div>
      <div class="tips">
        确定审核通过所选记录？此为{{orderType === 3 ? '组织核酸' : '单细胞'}}订单，请选择加测类型
      </div>
      <el-form ref="form" :model="form" label-suffix=":">
        <el-form-item label="加测类型" prop="status">
          <el-select v-model="form.status" size="mini" clearable>
            <template  v-for="(item, index) in statusOptions">
              <el-option
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </template>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {awaitWrap} from '@/util/util'
import {auditAddTestInfo} from '../../../../../api/sequencingManagement/repeatClinicManagementApi'

export default {
  name: 'fixStatusDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    orderType: {
      type: Number,
      default: null
    },
    deliverStatus: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false, // 控制提交按钮的加载状态
      statusOptions: [
        {
          label: '文库加测',
          value: 0
        },
        {
          label: '杂交文库加测',
          value: 1
        },
        {
          label: '重提取加测',
          value: 2
        },
        {
          label: '重建库加测',
          value: 3
        }
      ],
      form: {
        status: '' // 用于存储选中的交付状态
      }
    }
  },
  methods: {
    /**
     * 对话框打开时获取表格数据
     * 目前未在代码中实现具体功能，可能用于加载下拉列表选项等
     */
    handleOpen () {
      this.orderType === 3 ? this.statusOptions = [
        {
          label: '文库加测',
          value: 0
        },
        {
          label: '杂交文库加测',
          value: 1
        },
        {
          label: '重提取加测',
          value: 2
        },
        {
          label: '重建库加测',
          value: 3
        }
      ] : this.statusOptions = [
        {
          label: '文库加测',
          value: 0
        },
        {
          label: '重建库加测',
          value: 3
        }
      ]
      this.$refs.form.resetFields()
    },
    /**
     * 处理状态修改确认操作
     * 提交表单数据并根据返回结果提示用户操作是否成功
     */
    async handleConfirm () {
      // 确认判断
      this.loading = true // 开始加载
      const {res} = await awaitWrap(auditAddTestInfo({
        fcosAddTestingIds: this.ids,
        fauditOrRelinquish: 0,
        faddTestType: this.form.status
      }))

      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('审核成功')
        this.$emit('dialogConfirmEvent') // 触发父组件数据刷新
        this.visible = false // 关闭对话框
      }
      this.loading = false // 结束加载
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  display: flex;
  align-items: center;
  .status {
    color: #FEC171;
    font-size: 20px;
    margin-right: 5px;
  }
}

</style>
