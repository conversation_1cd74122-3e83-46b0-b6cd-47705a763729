<template>
  <el-dialog title="选择关联产品" :visible.sync="visible" width="800px" append-to-body :close-on-click-modal="false"
    @opened="handleOpen" @close="handleClose">
    <!-- 查询区域 -->
    <div class="search-form">
      <el-form ref="searchForm" :model="searchForm" :inline="true" label-width="80px" size="mini">
        <el-form-item label="产品编号" prop="productCode">
          <el-input v-model.trim="searchForm.productCode" clearable placeholder="请输入产品编号"></el-input>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model.trim="searchForm.productName" clearable placeholder="请输入产品名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="checkedProductList.length > 0" class="check-product-list-group">
      已选择的产品：
      <template v-if="checkedProductList.length <= displayLimit">
        <el-tag class="product-list" v-for="(item, index) in checkedProductList" :key="index" closable size="mini"
          style="margin-right: 5px; margin-bottom: 5px;"
          @close="handleCloseTag(item.productCode)">
          {{ item.productName }}
        </el-tag>
      </template>
      <template v-else>
        <el-tag class="product-list" v-for="(item, index) in displayedProducts" :key="index" closable size="mini"
          style="margin-right: 5px; margin-bottom: 5px;"
          @close="handleCloseTag(item.productCode)">
          {{ item.productName }}
        </el-tag>
        <el-popover
          placement="bottom"
          width="600"
          trigger="click">
          <div class="popover-tags">
            <el-tag class="product-list" v-for="(item, index) in hiddenProducts" :key="index" closable size="mini"
              style="margin-right: 5px; margin-bottom: 5px;"
              @close="handleCloseTag(item.productCode)">
              {{ item.productName }}
            </el-tag>
          </div>
          <el-button slot="reference" type="text" size="mini">
            +{{ checkedProductList.length - displayLimit }}个更多
          </el-button>
        </el-popover>
      </template>
    </div>

    <!-- 数据列表区域 -->
    <el-table ref="table" :data="tableData" class="product-table" border :height="400" :row-style="handleRowStyle"
      @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll" @selection-change="handleSelectChange"
>
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="productCode" label="产品编号" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="productName" label="产品名称" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="shortname" label="产品简称" min-width="120" show-overflow-tooltip></el-table-column>
    </el-table>

    <!--分页-->
    <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh" />
      </button>
    </el-pagination>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getProductList } from '@/api/basicDataManagement/sampleCancerTypeApi'
import util, { awaitWrap } from '@/util/util'
import mixins from '@/util/mixins'

export default {
  name: 'ProductSelectDialog',
  mixins: [mixins.tablePaginationCommonData, mixins.dialogBaseInfo],
  props: {
    pvisible: {
      type: Boolean,
      default: false
    },
    checkedProductInfo: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 显示的产品列表（前8个）
    displayedProducts () {
      return this.checkedProductList.slice(0, this.displayLimit)
    },
    // 隐藏的产品列表（超过8个的部分）
    hiddenProducts () {
      return this.checkedProductList.slice(this.displayLimit)
    }
  },
  data () {
    return {
      searchForm: {
        productCode: '',
        productName: ''
      },
      checkedProducts: [], // 初始化选中数组用于默认回显
      checkedProductList: [],
      initflag: 1,
      tableData: [],
      submitForm: {},
      treeData: [], // 树形数据
      cateaory: [], // 分类ID列表
      displayLimit: 3 // 最多显示的标签数量
    }
  },
  methods: {
    handleOpen () {
      this.searchForm = {
        productCode: '',
        productName: ''
      }
      this.clearMap()
      this.checkedProducts = this.checkedProductInfo
      this.checkedProductList = this.checkedProductInfo
      this.getProductCategory()
    },

    getProductCategory () {
      this.$ajax({
        url: '/system/product/get_all_product_directory',
        method: 'get',
        data: {}
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.cateaory = this.getAllChildrenFids('医学', res.data, 'productDirectoryChildrenList', 'productDirectoryName', 'productDirectoryId')
          this.handleSearch()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /**
      * 递归获取指定节点下所有子元素的 fid 字段值
      * @param {Object|String} nodeOrName - 节点对象或节点名称
      * @param {Array} treeData - 树形数据源（当第一个参数是名称时需要）
      * @param {String} childrenKey - 子节点的键名，默认为 'children'
      * @param {String} nameKey - 节点名称的键名，默认为 'name'
      * @param {String} fidKey - fid字段的键名，默认为 'fid'
      * @param {Set} visited - 已访问的节点集合，用于避免无限循环
      * @returns {Array} 包含所有子元素 fid 值的数组
    */
    getAllChildrenFids (
      nodeOrName,
      treeData = [],
      childrenKey = 'children',
      nameKey = 'name',
      fidKey = 'fid',
      visited = new Set()
    ) {
      const fidSet = new Set() // 使用 Set 避免重复

      // 如果输入是字符串，先在树形数据中查找对应的节点
      let targetNode = null
      if (typeof nodeOrName === 'string') {
        targetNode = this.findNodeByName(nodeOrName, treeData, childrenKey, nameKey)
        if (!targetNode) {
          console.warn(`未找到名称为 "${nodeOrName}" 的节点`)
          return []
        }
      } else if (typeof nodeOrName === 'object' && nodeOrName !== null) {
        targetNode = nodeOrName
      } else {
        console.warn('无效的节点参数')
        return []
      }

      // 递归收集子元素的 fid
      this.collectChildrenFids(targetNode, fidSet, childrenKey, fidKey, visited)

      return Array.from(fidSet).filter(fid => fid !== null && fid !== undefined)
    },

    /**
     * 在树形数据中查找指定名称的节点
     * @param {String} name - 节点名称
     * @param {Array} treeData - 树形数据
     * @param {String} childrenKey - 子节点键名
     * @param {String} nameKey - 名称键名
     * @returns {Object|null} 找到的节点或 null
     */
    findNodeByName (name, treeData, childrenKey, nameKey) {
      if (!Array.isArray(treeData)) return null
      for (const node of treeData) {
        if (!node || typeof node !== 'object') continue

        // 检查当前节点
        if (node[nameKey] === name) {
          return node
        }

        // 递归查找子节点
        if (node[childrenKey] && Array.isArray(node[childrenKey])) {
          const found = this.findNodeByName(name, node[childrenKey], childrenKey, nameKey)
          if (found) return found
        }
      }

      return null
    },
    // 监听选中行变化
    handleSelectChange () {
      this.checkedProductList = [...this.selectedRows.values(), ...this.checkedProducts]
      this.$nextTick(() => {
        // 强制更新视图
        this.$forceUpdate()
      })
    },
    // 处理全选操作
    handleSelectAll (selection) {
      this.handleDelCurrentDataMap()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.selectedRowsSize = this.selectedRows.size
      // 更新选中产品列表
      this.handleSelectChange()
    },
    // 移除选择的数据
    handleCloseTag (productCode) {
      console.log(productCode)
      // 判断是否是checkedProducts的数据
      const isInCheckedProducts = this.checkedProducts.some(v => v.productCode === productCode)
      if (isInCheckedProducts) {
        this.checkedProducts = this.checkedProducts.filter(item => item.productCode !== productCode)
      } else {
        this.selectedRows.delete(productCode)
      }
      console.log('11111', this.selectedRows, this.checkedProducts)
      this.handleEchoSelect()
      this.handleSelectChange()
    },
    /**
     * 递归收集节点下所有子元素的 fid
     * @param {Object} node - 当前节点
     * @param {Set} fidSet - fid 集合
     * @param {String} childrenKey - 子节点键名
     * @param {String} fidKey - fid 键名
     * @param {Set} visited - 已访问节点集合
     */
    collectChildrenFids (node, fidSet, childrenKey, fidKey, visited) {
      if (!node || typeof node !== 'object') return

      // 防止无限循环：检查节点是否已访问
      const nodeId = node.id || node[fidKey] || JSON.stringify(node)
      if (visited.has(nodeId)) {
        console.warn('检测到循环引用，跳过节点:', nodeId)
        return
      }
      visited.add(nodeId)

      // 获取子节点数组
      const children = node[childrenKey]
      if (!Array.isArray(children)) return

      // 遍历所有子节点
      for (const child of children) {
        if (!child || typeof child !== 'object') continue

        // 收集当前子节点的 fid
        const fid = child[fidKey]
        if (fid !== null && fid !== undefined) {
          fidSet.add(fid)
        }

        // 递归收集孙子节点的 fid
        this.collectChildrenFids(child, fidSet, childrenKey, fidKey, visited)
      }

      // 移除当前节点的访问标记（允许在其他分支中重新访问）
      visited.delete(nodeId)
    },
    async getData () {
      const { res = {} } = await awaitWrap(getProductList({
        productCode: this.submitForm.productCode,
        productName: this.submitForm.productName,
        productDirectoryIdList: this.cateaory,
        page: this.currentPage,
        rows: this.pageSize
      }, {
        loadingDom: '.product-table'
      }))

      if (res.code === this.SUCCESS_CODE) {
        const data = res.data
        this.totalPage = data.total
        this.tableData = []
        data.records.forEach(v => {
          const item = {
            id: v.productCode,
            productCode: v.productCode,
            productName: v.productName,
            shortname: v.shortname,
            productType: v.productType,
            productTypeName: v.productTypeName,
            state: v.fproductStatus,
            isComboType: v.isComboType,
            creator: v.creator,
            createTime: v.createTime,
            modifier: v.modifier,
            updateTime: v.updateTime
          }
          if (this.checkedProducts.length > 0) {
            const index = this.checkedProducts.findIndex(product => product.productCode === v.productCode)
            if (index !== -1) {
              // 移除对应值
              this.checkedProducts.splice(index, 1)
              this.selectedRows.set(v.productCode, item)
            }
          }
          util.setDefaultEmptyValueForObject(item)
          // 只展示已上架的数据
          // if (v.fproductStatus === 1) {
          this.tableData.push(item)
          // }
        })
        this.handleEchoSelect()
      }
    },
    handleSearch () {
      this.submitForm = {
        productCode: this.searchForm.productCode,
        productName: this.searchForm.productName
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.searchForm = {
        productCode: '',
        productName: ''
      }
      this.handleSearch()
    },
    handleConfirm () {
      this.$emit('dialogConfirmEvent', this.checkedProductList)
      this.handleClose()
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
}
.check-product-list-group {
  margin: 10px 0;
}
.popover-tags {
  max-height: 200px;
  overflow-y: auto;
  padding: 5px;
}
</style>
