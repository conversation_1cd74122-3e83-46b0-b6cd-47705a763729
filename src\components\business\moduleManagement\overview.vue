<template>
  <div>
    <el-container>
      <el-aside
        v-loading="categoryLoading"
        width="250px"
        class="template-category"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)">
        <div>
          <template v-if="$setAuthority('002010001', 'buttons')">
            <el-button type="primary" size="mini" @click="handleAddChild({},{fid: 0})">+创建分类</el-button>
          </template>
          <el-input v-model.trim="templateFilterText" size="small" style="margin: 10px 0;" placeholder="请输入模板"></el-input>
        </div>
        <p :style="{color: !currentTemplateCategoryId ? '#409EFF' : '#606266'}" class="uncategorized" @click="handleNodeClick({fid: null})">未分类</p>
        <el-tree
          :data="templateCategoryLists"
          ref="tree"
          :props="categoryProps"
          :expand-on-click-node="false"
          :filter-node-method="filterTemplateNode"
          draggable
          node-key="fid"
          default-expand-all>
          <template slot-scope="{ node, data }">
            <div class="node-row">
              <p :style="{color: data.fid === currentTemplateCategoryId ? '#409EFF' : '#606266'}" @click="handleNodeClick(data)"> {{ node.label }}</p>
                <el-popover
                  placement="right"
                  width="200"
                  trigger="click">
                  <div>
                    <template v-if="$setAuthority('002010001', 'buttons')">
                      <p v-if="node.level < 5" class="operate-item" @click="handleAddChild(node, data)">新建子分类</p>
                    </template>
                    <template v-if="$setAuthority('002010002', 'buttons')">
                      <p class="operate-item" @click="handleEditChild(node, data)">编辑</p>
                    </template>
                    <template v-if="$setAuthority('002010003', 'buttons')">
                      <p class="operate-item" @click="handleDelete(node, data)">删除</p>
                    </template>
                  </div>
                  <i slot="reference" class="el-icon-more more"></i>
                </el-popover>
            </div>
          </template>
        </el-tree>
      </el-aside>
      <el-main>
        <module-list :module-id="currentTemplateCategoryId" />
      </el-main>
    </el-container>
    <edit-category-dialog
      :pvisible="editCategoryDialogData.visible"
      :category-id="editCategoryDialogData.categoryId"
      :category-name="editCategoryDialogData.categoryName"
      :category-parent-id="editCategoryDialogData.categoryParentId"
      @dialogCloseEvent="editCategoryDialogData.visible = false"
      @dialogConfirmEvent="handleEditCategoryDialogConfirm"
    />
  </div>
</template>

<script>
// import num from './components/cc'
import moduleList from './moduleList'
import editCategoryDialog from './editCategoryDialog'
export default {
  name: 'overview',
  components: {
    moduleList,
    editCategoryDialog
  },
  created () {
    this.getModuleCategoryLists()
  },
  watch: {
    templateFilterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  data () {
    return {
      templateFilterText: '', // 模板筛选文字
      templateCategoryLists: [], // 模板列表
      currentTemplateCategoryId: null, // 当前模板分类的Id
      categoryLoading: false,
      categoryProps: {
        label: 'fmodelCategoryName'
      },
      editCategoryDialogData: {
        visible: false,
        categoryId: '',
        categoryName: '',
        categoryParentId: ''
      }
    }
  },
  methods: {
    // 获取模板分类列表
    getModuleCategoryLists () {
      this.categoryLoading = true
      this.$ajax({
        url: '/system/model/get_modelCategory_tree',
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.templateCategoryLists = data
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.categoryLoading = false
      })
    },
    // 筛选节点
    filterTemplateNode (value, data) {
      console.log(value, data)
      if (!value) return true
      return data.fmodelCategoryName.indexOf(value) !== -1
    },
    handleNodeClick (data) {
      console.log(data)
      this.currentTemplateCategoryId = data.fid
    },
    handleAddChild (node, data) {
      console.log(data)
      this.editCategoryDialogData.categoryId = ''
      this.editCategoryDialogData.categoryName = ''
      this.editCategoryDialogData.categoryParentId = data.fid
      this.editCategoryDialogData.visible = true
    },
    handleEditChild (node, data) {
      console.log(data)
      this.editCategoryDialogData.categoryId = data.fid
      this.editCategoryDialogData.categoryName = data.fmodelCategoryName
      this.editCategoryDialogData.categoryParentId = data.fparentId
      this.editCategoryDialogData.visible = true
    },
    handleDelete (node, data) {
      this.$confirm('此操作将删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/system/model/delete_modelCategory',
          data: {
            fid: data.fid
          },
          method: 'get'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.getModuleCategoryLists()
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      })
    },
    // 分类修改完成弹窗
    handleEditCategoryDialogConfirm () {
      this.editCategoryDialogData.visible = false
      this.getModuleCategoryLists()
    },
    allowDrop (draggingNode, dropNode, type) {
      console.log('drop', dropNode)
      return dropNode.key.indexOf('测试') < 0
    },
    allowDrag (draggingNode) {
      console.log('drag', draggingNode)
      return draggingNode.key.indexOf('测试') < 0
    }
  }
}
</script>

<style scoped lang="scss">
.uncategorized{
  color: #606266;
  padding-left: 1em;
  font-size: 16px;
  margin-bottom: 10px;
  cursor: pointer;
}
.template-category{
  margin-right: 10px;
  background: #fff;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  height: calc(100vh - 40px - 30px - 12px - 30px);
}
.operate-item{
  cursor: pointer;
  line-height: 2;
  padding-left: 10px;
}
.operate-item:hover{
  color: #fff;
  background: $color;
}
.node-row{
  display: flex;
  justify-content: space-between;
  width: 100%;
  .more{
    display: none;
  }
  &:hover{
   .more{
     display: block;
   }
  }
}
</style>
