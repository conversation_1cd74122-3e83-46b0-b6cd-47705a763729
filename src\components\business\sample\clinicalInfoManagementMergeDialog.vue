<template>
  <div>
    <el-dialog
            :visible.sync="visible"
            :close-on-click-modal="false"
            :before-close="handleClose" title="合并"
            width="45%"
            @open="handleOpen">
      <el-table
              ref="pictureTable"
              :data="tableData"
              class="pictureTable" size="mini" height="400"
              style="width: 100%">
        <el-table-column prop="patientId" label="患者编号" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleCode" label="样本编号" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sex" label="性别" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="idCard" label="身份证/护照" min-width="180" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-form>
        <el-form-item label="患者编号">
          <el-select v-model="mergePatient" size="mini">
            <template v-for="code in patientCodeOptions">
              <el-option :key="code" :label="code" :value="code"></el-option>
            </template>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="submitLoading" size="mini" type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'clinicalInfoManagementMergeDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    tableData: {
      type: Array,
      default () { return [] }
    }
  },
  data () {
    return {
      patientCodeOptions: [],
      mergePatient: '',
      submitLoading: false
    }
  },
  methods: {
    handleOpen () {
      this.mergePatient = ''
      this.patientCodeOptions = this.tableData.map(v => v.patientId)
    },
    handleConfirm () {
      if (!this.mergePatient) {
        this.$message.error('请选择患者编号')
        return
      }
      let ids = this.tableData.map(v => v.id)
      this.submitLoading = true
      this.$ajax({
        url: '/sample/patient/combine_patient_id',
        data: {
          patientId: this.mergePatient,
          ids: ids.toString()
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('合并成功')
          this.$emit('dialogConfirm')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitLoading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
