<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="800px"
      append-to-body
      @open="handleOpen">
      <el-button size="mini" type="primary" style="margin-bottom: 25px" @click="fixOrAddCustomerProject">+ 新增</el-button>
      <el-table :data="tableData" border size="mini">
        <el-table-column prop="customer"  label="客户" min-width="360"></el-table-column>
        <el-table-column prop="fprojectName" label="项目名称" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="fisEnroll" label="是否入排" min-width="100"></el-table-column>
        <el-table-column  label="操作" min-width="60">
          <template slot-scope="scope">
            <div class="icon-wrapper">
              <i class="el-icon-edit icon" @click="fixCustomerProject(scope.row)"></i>
              <el-popconfirm
                confirm-button-text='好的'
                cancel-button-text='不用了'
                icon="el-icon-info"
                icon-color="red"
                title="是否删除该客户项目配置?"
                @confirm="deleteProject(scope.row)"
              >
                <i slot="reference" class="el-icon-delete icon"></i>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <product-add-customer-project-dialog
        :title="addCustomerProjectDialogInfo.title"
        :product-id="productId"
        :info="info"
        :pvisible.sync="addCustomerProjectDialogInfo.visible"
        @dialogConfirmEvent="getData"
      />
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import ProductAddCustomerProjectDialog from './productAddCustomerProjectDialog'

export default {
  name: 'productCustomerProjectDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {ProductAddCustomerProjectDialog},
  props: {
    title: String, // 弹窗标题
    productId: Number // 产品id
  },
  data () {
    return {
      info: {},
      tableData: [],
      addCustomerProjectDialogInfo: {
        title: '新增',
        visible: false
      }
    }
  },
  methods: {
    // 打开弹窗
    handleOpen () {
      this.getData()
    },
    // 获取项目配置列表
    getData () {
      console.log(this.productId)
      this.$ajax({
        url: '/system/product/get_custom_config_list',
        method: 'get',
        data: {productId: this.productId}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          let item = {}
          data.forEach(v => {
            item = {
              fid: v.fid, // 记录自增长编号
              fcustomerName: v.fcustomerName, // 医院/机构名称
              fprojectName: v.fprojectName, // 项目名称
              fisEnroll: v.fisEnroll, // 是否入排（否/是）
              fproductId: v.fproductId, // 产品id
              fcustomerId: v.fcustomerId, // 医院/机构id
              fdoctorId: v.fdoctorId, // 医生id
              fdoctorName: v.fdoctorName,
              fprojectId: v.fprojectId, // 关联项目id
              customer: `${v.fcustomerName}` + (v.fdoctorName ? `/${v.fdoctorName}` : '')
            }
            this.tableData.push(item)
          })
        }
      }).catch(() => {
      })
    },
    // 点击修改客户定制项目
    fixCustomerProject (row) {
      this.info = row
      this.addCustomerProjectDialogInfo.title = '修改'
      this.addCustomerProjectDialogInfo.visible = true
    },
    // 新增客户定制项目
    fixOrAddCustomerProject () {
      this.info = {
        fid: 0
      }
      this.addCustomerProjectDialogInfo = {
        title: '新增',
        visible: true
      }
    },
    // 删除项目
    deleteProject (row) {
      this.$ajax({
        url: '/system/product/delete_custom_config',
        method: 'get',
        data: {fid: row.fid}
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除客户项目配置成功')
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.icon-wrapper {
  display: flex;
  align-content: center;
  justify-content: space-between;
  color: deepskyblue;
}
.icon {
  display: flex;
  align-content: center;
  justify-content: space-between;
  cursor: pointer;
}
</style>
