<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="数据导入错误"
      width="700px">
      <div style="margin: 20px 0">数据导入错误， 原因如下:</div>
      <el-table
        ref="backTable"
        :data="tableData"
        height="50vh"
        style="width: 100%;"
        class="table">
        <el-table-column prop="title" min-width="60" label="字段" ></el-table-column>
        <el-table-column prop="actualContent" min-widt="60" label="文件实际值"></el-table-column>
        <el-table-column prop="errorReason" label="错误原因" min-widt="360" show-overflow-tooltip></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    tableData: {
      type: Array
    }
  }
}
</script>

<style></style>
