<template>
  <div>
    <div class="card-wrapper">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column prop="geneticCancer" label="遗传性肿瘤综合征" width="360"></el-table-column>
        <el-table-column prop="diseaseAbout" label="疾病简介"></el-table-column>
        <el-table-column prop="referencesDoc" label="参考文献"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_interpretation_genetic_diseases_list',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data || []
        this.tableData = []
        rows.forEach(v => {
          let item = {
            id: v.fid,
            geneticCancer: v.geneticCancer,
            diseaseAbout: v.diseaseAbout,
            referencesDoc: v.referencesDoc
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    }
  }
}

</script>

<style scoped></style>
