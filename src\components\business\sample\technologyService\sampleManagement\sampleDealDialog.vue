<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      :title="title"
      width="1200px"
      @open="handleOpen">
      <el-form :model="form" ref="form" :rules="rules" label-width="100px">
        <template v-if="!isNext">

          <el-form-item label="处理措施" prop="handleStatus">
            <el-select v-model="form.handleStatus" placeholder="请选择" size="mini" style="width: 100%;">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in dealList">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.handleStatus === 2" label="快递单号" prop="expressCodes">
            <div :key="index" v-for="(item, index) in form.expressCodes">
              <el-input v-model="item.value" size="mini" style="width: 80%;"/>
              <el-button v-if="form.expressCodes.length > 1" size="mini" plain @click="handleDelete(index)">删除</el-button>
              <el-button v-if="index + 1 === form.expressCodes.length" size="mini" type="primary" @click="handleAdd">添加</el-button>
              <el-button v-if="index + 1 === form.expressCodes.length" size="mini" type="primary" @click="handleMail(index)">寄件</el-button>
            </div>
          </el-form-item>

          <el-form-item label="处理备注:" prop="inputMessage">
            <el-input
              v-model.trim="form.note"
              :rows="3"
              type="textarea"
              maxlength="150"
              show-word-limit
              size="mini"
              autocomplete="off"></el-input>
          </el-form-item>

          <el-form-item label="图片说明">
            <el-upload
              ref="picList"
              :action="uploadUrl"
              :auto-upload="false"
              :file-list="form.picList"
              :headers="headers"
              :on-success="handleOnSuccess"
              :on-error="handleOnError"
              :on-progress="handleProgress"
              :on-change="handleBeforeUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              multiple
              accept="image/jpg,image/jpeg,image/png"
              list-type="picture-card">
              <i class="el-icon-plus"></i>
            </el-upload>
            <Tips :size="10"></Tips>
            <el-dialog :visible.sync="dialogVisible" title="图片预览" append-to-body>
              <img :src="dialogImageUrl" width="100%" alt="">
            </el-dialog>
          </el-form-item>
        </template>

        <template v-else>
          <div class="email-content">
          <el-form-item label="邮件标题" prop="emailTitle">
            <el-input
              v-model.trim="form.emailTitle"
              maxlength="150"
              class="form-width"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="邮件正文" prop="emailContent">
            <div class="form-width">
              <edit
                :value="form.emailContent"
                @input="handleEmailContentInput"/>
            </div>
          </el-form-item>
          <el-form-item v-if="!this.isSampleReturn" label="附件文件">
            <el-upload
              ref="attachFile"
              :file-list="form.attachFile"
              :auto-upload="false"
              :action="uploadUrl"
              :headers="headers"
              :on-remove="handleRemove"
              :on-error="handleOnError"
              :on-change="handleChange"
              :on-success="handleOnSuccessFile"
              class="upload-demo"
              multiple>
              <el-button size="mini" icon="el-icon-plus">添加附件</el-button>
              <Tips :size="50"></Tips>
            </el-upload>
          </el-form-item>
          <el-form-item label="收件邮箱" prop="inbox">
            <el-input
              v-model.trim="form.inbox"
              :rows="2"
              maxlength="150"
              show-word-limit
              class="form-width"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="抄送邮箱" prop="sendEmail">
            <el-input
              v-model.trim="form.sendEmail"
              maxlength="150"
              class="form-width"
              placeholder="请输入"></el-input>
          </el-form-item>
        </div>
        </template>

      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" v-if="isNext" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
        <el-button v-else type="primary" size="mini" @click="handleNext">下一步</el-button>
      </span>

      <send-express-info-dialog
        :pvisible.sync="sendExpressDialogVisible"
        :production-area-id="productionAreaId"
        :order-id="orderId"
        @dialogConfirmEvent="handleSendExpress"
      />

    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import constants from '@/util/constants'
import Cookies from 'js-cookie'
import SendExpressInfoDialog from './sendExpressInfoDialog'
import util from '@/util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {SendExpressInfoDialog},
  props: {
    sampleDealData: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    const validateExpressCodes = (rule, value, callback) => {
      if (value[value.length - 1].value) {
        callback()
      } else {
        callback(new Error('请先填写快递单号！'))
      }
    }
    return {
      title: '样本处理',
      isNext: false,
      loading: false,
      sendExpressDialogVisible: false,
      tableData: [],
      typeOptions: {
        2: (orderId, expressCode) => {
          return {
            title: '【返样完成提醒】-',
            content: '<div style="line-height: 40px;">您好，<br/>' +
            `您的返样申请${orderId}（申请单号）共${this.tableData.length}例样本已完成返样，快递单号为：${expressCode}，请注意查收，有问题请及时联系我们。<br/>` +
            '祝好！</div>'
          }
        },
        3: (orderId) => {
          return {
            title: '【样本销毁完成提醒】-',
            content: '<div style="line-height: 40px;">您好，<br/>' +
            `您的样本销毁申请${orderId}（申请单号）共${this.tableData.length}例样本已完成销毁，请知悉。<br/>` +
              this.drawTable() +
            '祝好！</div>'
          }
        }
      },
      dealList: [
      ], // 处理下拉框
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      dialogImageUrl: '',
      dialogVisible: false,
      headers: {
        token: Cookies.get('token')
      },
      form: {
        handleStatus: '',
        expressCodes: [{value: ''}],
        note: '',
        emailTitle: '', // 邮件标题
        emailContent: '', // 邮件正文
        attachFile: [], // 附件文件
        inbox: '', // 收件邮箱
        sendEmail: '' // 寄件邮箱
      },
      rules: {
        handleStatus: [
          {required: true, message: '请选择处理措施', trigger: ['blur']}
        ],
        expressCodes: [
          {required: true, validator: validateExpressCodes, trigger: ['blur']}
        ],
        emailTitle: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        emailContent: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        inbox: [
          {required: true, message: '请输入', trigger: 'blur'},
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ],
        sendEmail: [
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ]
      }
    }
  },
  methods: {
    drawTable () {
      let tableStr = `<style>.line {
          text-align: center;padding: 0 8px; border: 1px solid #ccc
        }</style><table style="border-collapse: collapse;width: 100%;">
        <thead>
          <tr>
            <th class="line">序号</th>
            <th class="line">样本名称</th>
            <th class="line">项目名称</th>
            <th class="line">项目编号</th>
            <th class="line">到样时间</th>
            <th class="line">样本类型</th>
            <th class="line">申请人</th>
            <th class="line">申请日期</th>
          </tr>
        </thead>
        <tbody>`
      this.tableData.forEach((data, index) => {
        tableStr += `<tr>
            <td class="line">${index + 1}</td>
            <td class="line">${data.oldSampleName}</td>
            <td class="line">${data.t7ProjectName}</td>
            <td class="line">${data.t7ProjectCode}</td>
            <td class="line">${data.storageTime}</td>
            <td class="line">${data.sampleTypeName}</td>
            <td class="line">${this.recipients}</td>
            <td class="line">${this.applyTime}</td>
          </tr>`
      })
      tableStr += `</tbody>
        </table>`
      return tableStr
    },
    handleOpen () {
      this.$nextTick(v => {
        this.isNext = false
        this.form = {
          picList: [],
          handleStatus: '',
          expressCodes: [{value: ''}],
          note: '',
          emailTitle: '', // 邮件标题
          emailContent: '', // 邮件正文
          attachFile: [], // 附件文件
          inbox: '', // 收件邮箱
          sendEmail: '' // 寄件邮箱
        }
        this.isSampleReturn = !this.sampleDealData.hasNotReturnSample
        this.getApplyList()
        this.getEmailData()
        this.getData()
      })
    },
    // 获取样本列表
    getData () {
      const params = !this.isSampleReturn ? {
        returnOrderId: this.sampleDealData.ids[0]
      } : {
        returnOrderIdList: this.sampleDealData.ids
      }
      // 格式化处理参数
      this.$ajax({
        url: '/sample/t7_return/page_t7_return_sample',
        data: {
          page: {current: 1, size: -1},
          params: params
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleReturnId,
              sampleConfirmId: v.sampleConfirmId,
              geneSampleNum: v.geneSampleNum,
              oldSampleName: v.oldSampleName,
              sampleTypeCode: v.sampleTypeCode,
              sampleTypeName: v.sampleTypeName,
              productCode: v.productCode,
              productName: v.productName,
              t7OrderCode: v.t7OrderCode,
              t7ProjectCode: v.t7ProjectCode,
              t7ProjectName: v.t7ProjectName,
              storageTime: v.storageTime,
              applySampleType: v.applySampleTypeCode,
              applySampleTypeName: v.applySampleTypeName,
              sampleStatus: v.sampleStatus,
              sampleStatusName: v.sampleStatusName,
              originalExpireTime: v.originalExpireTime,
              extractExpireTime: v.extractExpireTime,
              plasmaRsNum: v.plasmaRsNum,
              extractDnaNum: v.extractDnaNum
            }
            item.realData = v
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取申请处理状态列表
    getApplyList () {
      this.$ajax({
        url: '/sample/t7_return/list_apply_type'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          // 获取对应状态下的处理状态列表
          let handleStatusList = data.find(v => v.code === this.sampleDealData.applyTypeCode * 1).handleStatusList || []
          this.dealList = handleStatusList.map(v => {
            return {
              label: v.name,
              value: v.code
            }
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    /**
     * 删除快递单号
     * @param index 删除快递好索引
     */
    handleDelete (index) {
      if (this.form.expressCodes.length <= 1) return
      this.form.expressCodes.splice(index, 1)
    },
    // 增加快递单号 未填写快递单号时不允许添加，否则下方红字提示：请先填写快递单号！(form校验单个属性)
    handleAdd () {
      this.$refs.form.validateField('expressCodes', (valid) => {
        if (!valid) {
          this.form.expressCodes.push({value: ''})
        }
      })
    },
    // 获取邮件信息
    getEmailData () {
      const data = !this.isSampleReturn ? {
        orderId: this.sampleDealData.ids[0]
      } : {
        forderIdList: this.sampleDealData.ids
      }
      this.$ajax({
        url: this.isSampleReturn ? '/sample/t7_return/get_order_list_return_handle_mail_info' : '/sample/t7_return/get_order_return_handle_mail_info',
        method: !this.isSampleReturn ? 'get' : 'post',
        data: data
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          const data = res.data || {}
          this.form.inbox = data.to
          this.form.sendEmail = data.cc
        }
      })
    },
    // 寄件
    handleMail () {
      this.sendExpressDialogVisible = true
    },
    // 提交样本处理信息
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let attachFileComponent = this.$refs.attachFile || {}
          let files = attachFileComponent.uploadFiles || []
          this.form.attachFile = files.map(v => v.raw)
          this.handleSendEmail()
        }
      })
    },
    // 寄快递回填
    handleSendExpress (data) {
      let length = this.form.expressCodes.length
      if (this.form.expressCodes[length - 1].value) {
        this.form.expressCodes.push({value: data})
        return
      }
      this.$set(this.form.expressCodes, length - 1, {value: data})
    },
    // 发送邮箱
    handleSendEmail () {
      this.loading = true
      const params = this.setParams()
      this.$ajax({
        url: !this.isSampleReturn ? '/sample/t7_return/handle_t7_return' : '/sample/t7_return/handle_t7_return_list',
        data: params,
        isCustomFormData: true
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('发送成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 设置发送邮箱请求参数
    setParams () {
      let formData = new FormData()
      !this.isSampleReturn ? formData.append('orderId', this.sampleDealData.ids[0]) : formData.append('forderIdList', this.sampleDealData.ids.join(','))
      formData.append('handleStatus', this.form.handleStatus)
      formData.append('handleNote', this.form.note)
      this.form.expressCodes.forEach(v => {
        formData.append('expressList', v.value)
      })
      this.form.picList.forEach(v => {
        formData.append('returnImgList', v)
      })
      formData.append('emailDTO.to', this.form.inbox)
      formData.append('emailDTO.cc', this.form.sendEmail)
      formData.append('emailDTO.subject', this.form.emailTitle)
      formData.append('emailDTO.content', this.form.emailContent)
      this.form.attachFile.forEach(v => {
        formData.append('emailDTO.attachments', v)
      })
      return formData
    },
    // 校验表单 => 切换弹窗
    handleNext () {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.title = '邮件信息'
          this.isNext = true
          let content = this.typeOptions[this.form.handleStatus](this.sampleDealData.orderNumList.join('、'), this.form.expressCodes.map(v => v.value).join(','))
          this.form.emailTitle = content.title + (this.isSampleReturn ? this.sampleDealData.fprojectCode : this.sampleDealData.orderId) + '-' + util.dateFormatter(new Date().getTime(), false)
          this.form.emailContent = content.content
          // 保存附件信息
          let picListComponent = this.$refs.picList || {}
          let files = picListComponent.uploadFiles || []
          this.form.picList = files.map(v => v.raw)
        }
      })
    },
    handleEmailContentInput (val) {
      this.form.emailContent = val
    },
    handleRemove (file, fileList) {
      this.onProgress = false
      this.form.picList = [...fileList]
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 附件提交成功回调
    handleOnSuccessFile (res) {
      this.onProgress = false
      console.log(res)
      this.form.attachFile.push(res.data)
    },
    handleChange (file, fileList) {
      let name = file.name
      fileList.pop()
      const index = fileList.findIndex(v => v.name === name)
      console.log(index, fileList, name)
      if (index !== -1) {
        fileList[index] = file
        console.log('替换')
      } else {
        fileList.push(file)
        console.log('添加')
      }
      if (fileList.length > 10) {
        this.$message.error('上传数量超出限制')
        fileList.pop()
        return false
      }
      if (file.size > 1024 * 1024 * 50) {
        this.$message.error('文件不能大于50M，请重新上传')
        fileList.pop()
        return false
      }
      return true
    },
    // 提交前的函数
    handleBeforeUpload (file, fileList) {
      let name = file.name
      if (fileList.length > 10) {
        this.$message.error('上传数量超出限制')
        fileList.pop()
        return false
      }
      if (!/\.(jpg|png|jpeg)$/.test(name)) {
        this.$message.error('只能上传jpg、png或jpeg的图片')
        fileList.pop()
        return false
      }
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件不能大于10M，请重新上传')
        fileList.pop()
        return false
      }
      return true
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.onProgress = false
      if (res && res.code === this.SUCCESS_CODE) {
        file.onlineUrl = res.data.absolutePath
        file.group = res.data.group
        file.path = res.data.path
      }
      this.form.picList = [...fileList]
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    }
  }
}
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  text-align: center;
  padding: 8px;
}

th {
  background-color: #98fb98;
}

tbody tr:nth-child(even) {
  background-color: #f2f2f2;
}

td:first-of-type {
  font-weight: bold;
}

td:nth-of-type(2) {
  text-align: left;
}
</style>
