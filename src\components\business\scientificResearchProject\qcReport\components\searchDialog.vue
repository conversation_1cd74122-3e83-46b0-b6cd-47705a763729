<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :modal="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :show-close="false"
    >

      <template #title>
        <div class="search-custom-title-nav">
          <p class="title">高级查询</p>
          <div>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleSearch">确认</el-button>
          </div>
        </div>
      </template>

      <div class="box">
        <div>
          <el-form
            ref="form"
            :model="form"
            label-width="100px"
            label-suffix=":"
            size="mini"
            inline
            @keyup.enter.native="handleSearch">
            <el-form-item label="任务单编号">
              <el-input
                v-model.trim="form.forderNumList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="吉因加编号">
              <el-input
                v-model.trim="form.fgeneCodeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="子订单编号">
              <el-input
                v-model.trim="form.fsubOrderCodeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="核酸编号">
              <el-input
                v-model.trim="form.fnucleateCodeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="项目编号">
              <el-input
                v-model.trim="form.fprojectCodeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="样本类型">
              <el-input
                v-model.trim="form.fsampleTypeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="报告状态">
              <el-checkbox-group v-model="form.freportStatusList">
                <el-checkbox
                  :key="index"
                  :label="item.value"
                  v-for="(item, index) in statusList">{{item.label}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="数据范围">
              <el-radio
                v-model="form.fdataRange" :key="index"
                :label="item.value"
                v-for="(item, index) in rangeList"
                border
              >
                {{item.label}}
              </el-radio>
            </el-form-item>
          </el-form>
        </div>
      </div>

    </el-drawer>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../util/mixins'
import util from '@/util/util'
export default {
  name: `searchParamsPath`,
  mixins: [mixins.dialogBaseInfo],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      form: {
        forderNumList: '',
        fgeneCodeList: '',
        fnucleateCodeList: '',
        fprojectCodeList: '',
        fsampleTypeList: '',
        fsubOrderCodeList: '',
        freportStatusList: [2, 20, 3, 30, 31],
        fdataRange: 1
      },
      statusList: [
        {
          label: '生成失败',
          value: 2
        },
        {
          label: '被驳回',
          value: 20
        },
        {
          label: '未生成',
          value: 3
        },
        {
          label: '生成中',
          value: 30
        },
        {
          label: '待审核',
          value: 31
        },
        {
          label: '审核通过',
          value: 1
        },
        {
          label: '已发送',
          value: 10
        }
      ],
      rangeList: [
        {
          label: '我提交的样本',
          value: 0
        },
        {
          label: '全部样本',
          value: 1
        }
      ],
      placeholder: '请输入, 批量查询用逗号分隔，不区分中英文逗号， 或直接粘粘Excel中整行或整列数据'
    }
  },
  methods: {
    setParams () {
      let data = util.deepCopy(this.form)
      let keys = ['forderNumList', 'fgeneCodeList', 'fnucleateCodeList', 'fprojectCodeList', 'fsampleTypeList', 'fsubOrderCodeList']
      keys.forEach(v => {
        data[v] = this.formateData(this.form[v])
      })
      return data
    },
    formateData (data) {
      return data ? data.replace(/，/g, ',').replace(/\n/g, ',').replace(/\s+/g, ',').split(',') : []
    },
    // 重置
    handleReset () {
      this.form = {
        forderNumList: '',
        fgeneCodeList: '',
        fnucleateCodeList: '',
        fprojectCodeList: '',
        fsampleTypeList: '',
        freportStatusList: [2, 20, 3, 30, 31],
        fdataRange: 0
      }
      this.handleSearch()
    },
    handleSearch () {
      this.$emit('dialogConfirmEvent', this.setParams())
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">

.search-custom-title-nav {
  display: flex;
  justify-content: space-between;
  .title {
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    border-left: 4px solid #539fff;
  }
}
.search-main-wrap {
  margin: 0 30px;
}
.sub-title {
  overflow: scroll;
  font-size: 16px;
  margin-bottom: 10px;
  color: #539fff;
}
.el-tag {
  margin-left: 10px;
  margin-bottom: 5px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  margin-bottom: 5px;
  vertical-align: bottom;
}
.input-width{
  width: 350px;
}
.el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item {
  margin-bottom: 12px;
}
>>>.el-drawer__body{
  overflow: scroll;
}
>>>.el-form-item__content{
  width: calc(100% - 100px);
}
//>>>.el-select {
//  width: 400px !important;
//}
</style>
