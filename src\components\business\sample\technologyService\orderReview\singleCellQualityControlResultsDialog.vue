<template>
  <div>
    <el-dialog
      title="样本列表"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="94%"
      v-drag-dialog
      @open="handleOpen"
      :before-close="handleClose">
      <nav class="dialog-operateBar">
        <div>
          <el-select
            v-model="form.content"
            @clear="handleReset"
            @change="clearInput"
            size="mini"
            placeholder="请选择"
            :popper-append-to-body="false"
            style="width: 150px;"
            clearable>
            <template v-for="(v, k) in searchOptions">
              <el-option :key="k" :label="v" :value="k"></el-option>
            </template>
          </el-select>
          <template v-if="form.content === 'fisStaging'">
            <el-select
              v-model="form.input"
              style="width: 250px;margin: 0 20px;"
              size="mini"
              clearable
              :popper-append-to-body="false"
            >
              <el-option v-for="(v, k) in statusOptions" :key="k" :label="v" :value="k"></el-option>
            </el-select>
          </template>
          <template v-else-if="form.content === 'fisBackUp'">
            <el-select
              v-model="form.input"
              style="width: 250px;margin: 0 20px;"
              size="mini"
              clearable
              :popper-append-to-body="false"
            >
              <el-option v-for="(v, k) in backUpMap" :key="k" :label="v" :value="k"></el-option>
            </el-select>
          </template>
          <template v-else>
            <el-input
              v-model="form.input"
              style="width: 250px;margin: 0 20px;"
              size="mini"
              clearable
              @keyup.enter.native="handleSearch"
              :disabled="!form.content"
              placeholder="请输入"></el-input>
          </template>
          <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </div>
      </nav>
      <template v-if="visible">
        <el-table
          :data="tableData"
          class="q-table tableBox"
          ref="table"
          height="340px"
          style="width: 100%"
          stripe
          border
          :cell-style="handleRowStyle"
          @header-dragend="handleTableDragend"
          @select="handleSelectTable"
          @row-click="handleRowClickCheck"
          @select-all="handleSelectAll">
          <template v-if="btnType === 2">
            <el-table-column type="selection" :selectable="handleSelectable" disabled="true" width="50"></el-table-column>
          </template>
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column label="吉因加编号" prop="geneplusNum" width="150" align="center"></el-table-column>
          <el-table-column label="开启状态" prop="statusName" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleName" label="样本名称" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleCode" label="样本编号" width="150" align="center"></el-table-column>
          <el-table-column prop="speciesType" label="物种类型" width="150" align="center"></el-table-column>
          <el-table-column prop="organType" label="取样部位" width="150" align="center"></el-table-column>
          <el-table-column prop="tissueSampleType" label="组织样本类型" width="150" align="center"></el-table-column>
          <el-table-column prop="tissueSampleStatus" label="组织状态" width="150" align="center"></el-table-column>
          <el-table-column prop="getSampleType" label="取样方式" width="150" align="center"></el-table-column>
          <el-table-column prop="expectCellNum" label="期望捕获细胞数（合同签订）" width="150" align="center"></el-table-column>
          <el-table-column prop="dataSize" label="测序数据量/M reads" width="150" align="center"></el-table-column>
          <el-table-column prop="productName" label="产品名称" width="150" align="center"></el-table-column>
          <el-table-column prop="labType" label="实验环节" width="150" align="center"></el-table-column>
          <el-table-column prop="isBackupName" label="是否备份" width="150" align="center"></el-table-column>
          <el-table-column prop="notes" label="备注" width="150" align="center"></el-table-column>
        </el-table>
        <div style="display: flex;">
          <el-pagination
            @size-change="handleSizeChange"
            style="background: #ffffff;"
            @current-change="handleCurrentChange"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            :total="totalPage">
            <button @click="handleRefresh"><i class="el-icon-refresh" style="font-size: 17px; line-height: inherit"></i></button>
          </el-pagination>
          <div style="color: #606266; font-size: 14px; line-height: 32px;">已选中{{selectedRowsSize}}条数据</div>
        </div>
      </template>
      <span slot="footer" v-if="btnType === 2">
        <el-button size="small" :loading="submitBtnLoading" @click="handleClose">取消</el-button>
<!--        <el-button size="small" :loading="submitBtnLoading"  @click="handleStopDetection">停止检测</el-button>-->
        <el-button size="small" type="primary" :loading="submitBtnLoading" @click="handleConfirm">申请检测</el-button>
      </span>
    </el-dialog>

    <!-- 任务下达 - 风险或者不合格样本-->
    <el-dialog
      title="提示"
      :visible.sync="taskReleaseVisible1"
      width="450px"
      :close-on-click-modal="false"
      append-to-body
      @close="taskReleaseVisible1 = false">
      <div>
        <div><i class="el-icon-info" style="margin-right: 5px;"></i>所选样本中有风险或不合格的样本，是否确认进行检测?</div>
        <div style="display: flex; margin-top: 10px;">
          <label>检测备注：</label>
          <el-input
            v-model="remarks"
            placeholder="请填写检测备注（如有）"
            type="textarea"
            maxlength="200"
            :rows="2"
            show-word-limit
            style="width: 300px;"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="taskReleaseVisible1 = false">取 消</el-button>
        <el-button type="primary" size="small" @click="submit(1)">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 任务下达 - 合格-->
    <el-dialog
      title="提示"
      :visible.sync="taskReleaseVisible2"
      width="450px"
      :close-on-click-modal="false"
      append-to-body
      @close="taskReleaseVisible2 = false">
      <div>
        <div><i class="el-icon-info" style="margin-right: 5px;"></i>任务下达后不可撤销，确认继续吗?</div>
        <div style="display: flex; margin-top: 10px;">
          <label>检测备注：</label>
          <el-input
            v-model="remarks"
            placeholder="请填写检测备注（如有）"
            type="textarea"
            maxlength="200"
            :rows="2"
            show-word-limit
            style="width: 300px;"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="taskReleaseVisible2 = false">取 消</el-button>
        <el-button type="primary" size="small" @click="submit(1)">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '@/util/mixins'
import util from '@/util/util'
// import constants from "../../../../util/constants";
export default {
  name: 'singleCellQualityControlResultsDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    type: Number | String, // 1: illumina 2:MGI 3: 组织或核酸
    orderId: String | Number,
    btnType: Number, // 1：质控结果 2：申请检测
    note: String, // 订单备注
    isThird: Number // 是否第三方（擎科）1是0否
  },
  data () {
    return {
      form: {
        content: '',
        input: ''
      },
      selectedRows: new Map(),
      submitBtnLoading: false,
      formSubmit: {},
      searchOptions: {
        'fisStaging': '开启状态',
        'fname': '样本编号',
        'fgeneCodeList': '吉因加编号',
        'fspecies': '物种类型',
        'fdetectType': '产品名称',
        'fisBackUp': '是否备份'
      },
      statusOptions: {
        1: '暂存',
        0: '直接开启'
      },
      backUpMap: {
        0: '正常样本',
        1: '备份样本'
      },
      remarks: '',
      taskReleaseVisible1: false,
      taskReleaseVisible2: false,
      analysisProcessSelectDialogVisible: false, // 分析流程选择
      analysisProcessSelectDialogData: {}
    }
  },
  methods: {
    handleOpen () {
      this.tableData = []
      this.remarks = ''
      this.handleReset()
    },
    handleReset () {
      this.form = {
        content: '',
        input: ''
      }
      this.handleSearch()
    },
    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.clearMap()
      this.getData()
    },
    getData () {
      let input = this.form.input
      if (this.formSubmit.content === 'fgeneCodeList') {
        input = util.setGroupData(input, ',', true)
      }
      this.$ajax({
        url: '/order/get_lib_or_tissue_list',
        data: {
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          fsearch: {
            searchField: this.formSubmit.content,
            searchValue: input
          },
          orderId: this.orderId,
          type: 5
        },
        loadingDom: '.q-table'
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          console.log(rows)
          rows.forEach(v => {
            // const qualityControlResultsItem = this.qualityControlResultsObj[v.fsampleEvaluation] || {}
            let item = {
              id: v.fid,
              urgent: v.fisUrgent,
              // isUrgent: v.fdeliveryType === '极致交付', // 是否可加急
              // fsampleCode: v.fsampleCode,
              sampleName: v.fname,
              key: v.fname,
              sampleCode: v.fname,
              geneplusNum: v.fgeneCode,
              speciesType: v.fspecies,
              speciesTypeName: v.fspecies,
              organType: v.forganType,
              tissueSampleType: v.ftissueSampleType,
              tissueSampleStatus: v.ftissueSampleState,
              getSampleType: v.fsamplingMethod,
              expectCellNum: v.fexpectedCellsNumber,
              dataSize: v.fdataSize,
              productName: v.fdetectType,
              labType: v.fexperimentalLink,
              cDNADatasize: v.fcdnaDataSize,
              BCRDatasize: v.fbcrDataSize,
              TCRDatasize: v.ftcrDataSize,
              ATACDatasize: v.fatacDataSize,
              status: v.fisStaging + '',
              isBackup: v.fisBackUp, // 是否为备份
              statusName: this.statusOptions[v.fisStaging],
              isBackupName: this.backUpMap[v.fisBackUp],
              notes: v.fnote
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.handleEchoSelect()
        }
      })
    },
    // 下拉框发生改变时，清空input
    clearInput () {
      this.form.input = ''
    },
    // 点击行
    handleRowClickCheck (row, c) {
      if (!this.handleSelectable(row)) return
      this.handleRowClick(row)
    },
    // 数据是否可选(返回值为false则禁用)
    handleSelectable (row) {
      const item = row.realData
      // 仅允许选择「开启状态」为「暂存」的样本数据进行「申请检测」操作
      return [1].includes(+item.status)
    },
    // 确定
    async handleConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下达的项')
        return
      }
      // let datas = [...this.selectedRows.values()]
      // 所选样本中有风险或不合格的样本
      this.taskReleaseVisible2 = true
    },
    // 停止检测
    async handleStopDetection () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下达的项')
        return
      }
      let datas = [...this.selectedRows.values()]
      let isCanSend = datas.every(v => [2].includes(+v.status) && ['不合格', '风险'].includes(v.qualityControlResults))
      if (!isCanSend) {
        this.$message.error('请选择「未下单、不合格」，「未下单，风险」的数据！')
        return
      }
      // 所选样本中有风险或不合格的样本
      const isShow = datas.some(v => ['不合格', '风险'].includes(v.qualityControlResults))
      if (isShow) {
        await this.$confirm('停止检测后不可撤销，是否确认停止检测？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'info'
        })
      }
      this.submit(3)
    },
    submit (status) {
      this.taskReleaseVisible1 = false
      this.taskReleaseVisible2 = false

      let ids = [...this.selectedRows.keys()]
      let data = {
        fid: this.orderId,
        sampleIdList: ids,
        detectStatus: status // 检测状态 1申请检测/已下单 3停止检测
      }
      if (status === 1) {
        data.detectNote = this.remarks // 检测备注
      }
      this.submitBtnLoading = true
      this.$ajax({
        url: '/order/save_apply_inspection_unicellular',
        data: {
          ...data
        }
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    },
    // 申请分析
    handleApplyAnalysis () {
      this.$ajax({
        url: '/cos/check_is_analysis_sample',
        data: {
          forderId: this.orderId
        }
      }).then(async res => {
        if (res.statusCode === this.SUCCESS_CODE) {
          const data = res.data || {}
          // 是否有分析样本 1是0否
          if (!data.fisAnalysis) {
            this.$message.success('操作成功')
            this.$emit('dialogConfirmEvent')
            this.visible = false
            return
          }
          await this.$confirm('您好，下达任务的样本有分析环节，请点击【前往填写】填写分析信息收集表，或后续您可在订单查询-申请分析中进行填写。', '提示', {
            confirmButtonText: '前往填写',
            cancelButtonText: '稍后填写',
            type: 'warning'
          })
          this.analysisProcessSelectDialogData = {
            orderId: this.orderId,
            productNameList: data.fproductNameList || [],
            analysisProcessNameList: data.fanalysisProcessNameList || []
          }
          this.analysisProcessSelectDialogVisible = true

          this.$message.success('操作成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-operateBar {
  margin: 10px 0;
}
</style>
