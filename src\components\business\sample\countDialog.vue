<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="前端补录延期统计"
      width="90%"
      @open="handleOpen"
    >
      <span slot="title" class="dialog-footer">
        <span>前端补录延期统计</span>
        <el-date-picker v-model="year" size="mini" type="year" value-format="yyyy" placeholder="选择日期时间" @change="getData"></el-date-picker>
        <el-button size="mini" type="primary" @click="getData">查询</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="downloadCountRecords">下载</el-button>
      </span>
      <!--表格-->
      <el-table
        ref="table"
        :data="tableData"
        :height="400"
        size="mini"
        class="reservationTable"
        style="width: 100%">
<!--        <el-table-column prop="sampleNum" ></el-table-column>-->
        <el-table-column prop="sampleConfirmTime" label="到样时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleNum" label="样例编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="proName" label="产品/项目名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="hospital" label="送检单位" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="doctor" label="送检医生" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="salesManName" label="销售" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="factotumName" label="事务" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="preDeliverDate" label="预交付时间" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="firstMailTime" label="第一次邮件催补录时间" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="secondMailTime" label="第二次" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="thirdMailTime" label="第三次" min-width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'

export default {
  name: 'countDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    pvisible: {
      type: Boolean
    }
  },
  data () {
    return {
      loading: false,
      tableData: [],
      year: new Date().getFullYear().toString()
    }
  },
  methods: {
    handleOpen () {
      this.year = new Date().getFullYear().toString()
      this.getData()
    },
    // 信息补录统计数据 /sample/basic/statistical_make_up_delay
    getData () {
      this.$ajax({
        url: '/sample/basic/page_make_up_delay',
        method: 'post',
        loadingDom: '.reservationTable',
        data: {
          year: this.year,
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = []
          this.totalPage = res.data.total
          let data = res.data || {}
          let rows = data.rows || []
          rows.forEach(v => {
            let item = {
              sampleNum: v.sampleNum,
              name: v.name,
              proCode: v.proCode,
              proName: v.proName,
              hospital: v.hospital,
              doctor: v.doctor,
              salesManName: v.salesManName,
              factotumName: v.factotumName,
              sampleConfirmTime: v.sampleConfirmTime,
              preDeliverDate: v.preDeliverDate,
              firstMailTime: v.firstMailTime,
              secondMailTime: v.secondMailTime,
              thirdMailTime: v.thirdMailTime
            }
            item.realDate = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 下载记录
    downloadCountRecords () {
      this.loading = true
      this.$ajax({
        url: '/sample/basic/download_statistical_excel',
        method: 'get',
        data: {
          year: this.year
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true, `${this.year}年-前端补录延期统计`)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
