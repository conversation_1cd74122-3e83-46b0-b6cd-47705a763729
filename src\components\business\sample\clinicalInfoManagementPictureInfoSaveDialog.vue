<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="45%"
      @open="handleOpen"
    >
      <div>
        <div class="buttonGroup">
          <template v-if="type === 'sampleOrderImg'">
            <el-button type="primary" size="mini" @click="handleUpload('sampleOrderImg1')">上传送检单</el-button>
            <el-button type="primary" size="mini" @click="handleUpload('sampleOrderImg2')">上传知情同意书</el-button>
          </template>
          <template v-else>
            <el-button type="primary" size="mini" @click="handleUpload(null)">上传</el-button>
          </template>
          <el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
        </div>
        <div>
          <el-table
            ref="pictureTable"
            :data="tableData"
            class="pictureTable"
            size="mini"
            height="400"
            style="width: 100%" @select="handleSelect" @select-all="handleSelectAll"
            @row-click="handleRowClick">
            <el-table-column type="selection"></el-table-column>
            <el-table-column type="index" label="序号" width="60"></el-table-column>
            <el-table-column prop="fileName" label="文件" min-width="180" show-overflow-tooltip></el-table-column>
            <template v-if="type !== 'sampleOrderImg'">
              <el-table-column :key="type + 'nameSuffix'" prop="nameSuffix" label="命名后缀" min-width="180"></el-table-column>
              <el-table-column :key="type + 'nameSuffixEdit'" label="编辑后缀" min-width="100">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-edit" @click="handleEditNameSuffix(scope.row)"></el-button>
                </template>
              </el-table-column>
            </template>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <upload-dialog
      :pvisible.sync="uploadDialogVisible" :ptype="uploadDialogData.type" :psample-basic-id="uploadDialogData.sampleBasicId" @uploadDialogConfirmEvent="handleUploadDialogConfirm"
    ></upload-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import mixins from '../../../util/mixins'
import uploadDialog from './clinicalInfoManagementUploadDialog'
export default {
  name: 'clinicalInfoManagementPictureInfoSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    uploadDialog
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      title: '',
      type: '',
      sampleOrderImgTableData: [],
      idType: '', // 表格中id的key
      sampleBasicId: null,
      form: {},
      sortable: null,
      tableData: [],
      selectedRows: new Map(),
      uploadDialogVisible: false,
      uploadDialogData: {}
    }
  },
  methods: {
    getData () {
      let url = ''
      switch (this.type) {
        case 'followupImgNames':
          url = '/sample/clinical/get_followup_imgs'
          break
        case 'imgNames':
          url = '/sample/clinical/get_clinical_imgs'
          break
        case 'sampleOrderImg':
          url = ''
          break
      }
      if (url) {
        this.$ajax({
          method: 'get',
          loadingDom: '.pictureTable',
          url: url,
          data: {
            sampleBasicId: this.sampleBasicId
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.selectedRows.clear()
            this.tableData = result.data
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        this.selectedRows.clear()
        this.tableData = this.sampleOrderImgTableData
      }
    },
    handleOpen () {
      this.$nextTick(() => {
        this.type = this.pdata.type
        this.sampleOrderImgTableData = this.pdata.tableData
        this.sampleBasicId = this.pdata.sampleBasicId
        this.idType = ''
        switch (this.pdata.type) {
          case 'followupImgNames':
            this.title = '随访资料编辑'
            this.tableData = []
            this.idType = 'followupImgId'
            break
          case 'imgNames':
            this.title = '临床资料编辑'
            this.tableData = []
            this.idType = 'clinicalImgId'
            break
          case 'sampleOrderImg':
            this.title = '送检单/知情同意书编辑'
            this.tableData = []
            this.idType = 'sampleOrderImgId'
            break
        }
        this.getData()
        this.setSort()
      })
    },
    handleRowClick (row) {
      this.$refs.pictureTable.toggleRowSelection(row, !this.selectedRows.has(row.fileName))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.fileName) ? this.selectedRows.delete(row.fileName) : this.selectedRows.set(row.fileName, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fileName, row)
      })
    },
    handleConfirm () {
      let url = ''
      let data = {}
      if (this.type === 'followupImgNames') {
        url = '/sample/clinical/save_followup_imgs'
        data = {
          sampleBasicId: this.sampleBasicId,
          fileRows: this.tableData
        }
      } else if (this.type === 'imgNames') {
        url = '/sample/clinical/save_clinical_imgs'
        data = {
          sampleBasicId: this.sampleBasicId,
          clinicalImgList: this.tableData
        }
      } else if (this.type === 'sampleOrderImg') {
        url = ''
      }
      if (url) {
        this.$ajax({
          url: url,
          data: data
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('保存成功')
            this.$emit('pictureInfoSaveDialogConfirmEvent', this.type, this.tableData)
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        this.$emit('pictureInfoSaveDialogConfirmEvent', 'sampleOrderImg', this.tableData)
      }
    },
    handleUpload (type = null) {
      this.uploadDialogData = {
        type: type || this.type,
        sampleBasicId: this.sampleBasicId
      }
      this.uploadDialogVisible = true
    },
    handleDelete () {
      if (this.selectedRows.size > 0) {
        let rows = [...this.selectedRows.values()]
        let url = ''
        let data = {}
        switch (this.type) {
          case 'followupImgNames':
            url = '/sample/clinical/delete_followup_imgs'
            data = {
              followupImgList: rows
            }
            break
          case 'imgNames':
            url = '/sample/clinical/delete_clinical_imgs'
            data = {
              clinicalImgList: rows
            }
            break
          case 'sampleOrderImg':
            url = ''
            break
        }
        if (url) {
          this.$ajax({
            url: url,
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              let index = -1
              rows.forEach(v => {
                index = this.tableData.findIndex(vv => vv.fileAbsolutePath === v.fileAbsolutePath)
                if (index !== -1) {
                  this.tableData.splice(index, 1)
                }
              })
              this.$message.success('删除成功')
              this.selectedRows.clear()
            } else {
              this.$message.error(result.message)
            }
          })
        } else {
          let index = -1
          rows.forEach(v => {
            index = this.tableData.findIndex(vv => vv.fileName === v.fileName)
            this.tableData.splice(index, 1)
          })
          this.selectedRows.clear()
        }
      } else {
        this.$message.error('请选择一条数据')
      }
    },
    handleUploadDialogConfirm (newData) {
      newData.forEach(v => {
        this.tableData.push(v)
      })
      this.uploadDialogVisible = false
    },
    handleEditNameSuffix (row) {
      this.$prompt('请输入命名后缀', '修改命名后缀', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.nameSuffix
      }).then(({ value }) => {
        let index = this.tableData.findIndex(v => v.fileName === row.fileName)
        if (index !== -1) {
          this.$set(this.tableData[index], 'nameSuffix', value)
        }
      }).catch(() => {})
    },
    setSort () {
      const el = this.$refs.pictureTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function (dataTransfer) {
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          setTimeout(() => {
            let nI = evt.newIndex
            let oI = evt.oldIndex
            let tableData = JSON.parse(JSON.stringify(this.tableData))
            tableData.splice(nI, 0, ...tableData.splice(oI, 1))
            // console.log(tableData)
            this.tableData = []
            this.$nextTick(() => {
              this.tableData.push(...tableData)
            })
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  .buttonGroup{
    height: 40px;
    line-height: 40px;
  }
</style>
