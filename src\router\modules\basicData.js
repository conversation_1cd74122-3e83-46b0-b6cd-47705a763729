// 基础数据管理相关路由
export default [
  {
    path: '/business/view/processFlowManagement',
    meta: {
      title: '工序流程管理'
    },
    component: () => import('@/components/business/basicDataManagement/processFlowManagement.vue')
  },
  {
    path: '/business/view/processStepManagement',
    meta: {
      title: '工序步骤管理'
    },
    component: () => import('@/components/business/basicDataManagement/processStepManagement.vue')
  },
  {
    path: '/business/view/controlStandardManagement',
    meta: {
      title: '对照标准管理'
    },
    component: () => import('@/components/business/basicDataManagement/controlStandardManagement.vue')
  },
  {
    path: '/business/view/reportNodulusManagement',
    meta: {
      title: '报告小结配置'
    },
    component: () => import('@/components/business/basicDataManagement/reportNodulusManagement/index.vue')
  },
  {
    path: '/business/view/onlineProbeManagement',
    meta: {
      title: '探针在线管理'
    },
    component: () => import('@/components/business/basicDataManagement/onlineProbeManagement.vue')
  },
  {
    path: '/business/view/nucleicGradeConfigManagement',
    meta: {
      title: '核酸等级配置'
    },
    component: () => import('@/components/business/basicDataManagement/nucleicGradeConfigManagement/index.vue')
  },
  {
    path: '/business/view/abnormalDescConfigManagement',
    meta: {
      title: '异常描述配置'
    },
    component: () => import('@/components/business/basicDataManagement/abnormalDescConfigManagement/index.vue')
  },
  {
    path: '/business/view/sampleCancerTypeManagement',
    meta: {
      title: '样本癌种类型配置'
    },
    component: () => import('@/components/business/basicDataManagement/sampleCancerTypeManagement/index.vue')
  },
  {
    path: '/business/subpage/probeKanban',
    component: () => import('@/components/business/basicDataManagement/probeKanban.vue')
  },
  {
    path: '/business/subpage/probeOrderDetail',
    component: () => import('@/components/business/basicDataManagement/probeOrderDetail.vue')
  },
  {
    path: '/business/subpage/previewExcel',
    component: () => import('@/components/business/basicDataManagement/previewExcel.vue')
  }
]
