<template>
  <div>
    <div class="search-form">
      <el-form :model="form" size="mini" label-width="80px" inline style="display: flex;">
        <el-form-item label="申请单号">
          <el-input v-model.trim="formInput.order" size="mini" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model.trim="formInput.time"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <template v-if="$setAuthority('011004001', 'buttons')">
        <el-button type="primary" size="mini" @click="handleShowInAndOutDialog('in')">+ 入库申请</el-button>
      </template>
      <template v-if="$setAuthority('011004002', 'buttons')">
        <el-button type="primary" size="mini" @click="handleShowInAndOutDialog('out')">+ 出库申请</el-button>
      </template>
      <template v-if="$setAuthority('011004006', 'buttons')">
        <el-button type="primary" size="mini" @click="sampleSchedulingDialogVisible = true">+ 样本调度</el-button>
      </template>
      <template v-if="$setAuthority('011004007', 'buttons')">
        <el-button type="primary" size="mini" @click="handleToMoveStorage">+ 移库申请</el-button>
      </template>
      <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleResetForm">重置</el-button>
    </div>
    <el-table
      :data="tableData"
      :key="status"
      style="width: 100%;"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      class="table"
      @filter-change="handleFilterTable">
      <el-table-column type="index" width="70" label="序号"></el-table-column>
      <el-table-column
        prop="orderStatus"
        label="单号状态" width="120"
        column-key="orderStatus"
        :filters="progressFilterOptions"
        :filtered-value="orderStatusFilterValues"
      >
        <template slot-scope="scope">
          <span :style="{color: scope.row.orderStatus === '驳回' ? 'red' : ''}">{{scope.row.orderStatus}}</span>
          <el-popover
            :content="scope.row.rejectReason"
            v-if="scope.row.orderStatus === '驳回'"
            placement="bottom"
            width="200"
            trigger="hover">
            <i slot="reference" class="el-icon-warning-outline" style="color: red;font-size: 14px;"></i>
          </el-popover>
          <el-popover
            v-else-if="scope.row.haveRejectSample"
            placement="bottom"
            width="200"
            trigger="hover"
            content="存在被驳回样本，点击确认查看">
            <i slot="reference" class="el-icon-warning-outline" style="color: red;font-size: 14px;"></i>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip label="申请单号" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="handleModifyOrder(scope.row, 'sampleSearch', '订单详情')">{{scope.row.orderNum}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="lab" label="所属实验室" width="180"></el-table-column>
      <el-table-column prop="applicant" label="申请人" width="120"></el-table-column>
      <el-table-column prop="operator" label="操作人" width="120"></el-table-column>
      <el-table-column prop="applicationTime" label="申请时间" min-width="180"></el-table-column>
      <el-table-column prop="estimatedTime" label="预计完成时间" min-width="180"></el-table-column>
      <el-table-column label="操作" width="220" fixed="right">
        <template slot-scope="scope">
          <template>
            <el-button
              v-if="scope.row.orderStatus === '驳回'"
              type="text"
              @click="handleModifyOrder(scope.row, 'reject', '订单确认', scope.row.rejectReason)">确认</el-button>
            <el-button
              v-else-if="scope.row.haveRejectSample"
              type="text"
              @click="handleModifyOrder(scope.row, 'rejectSample', '订单详情', scope.row.rejectReason)">确认</el-button>
          </template>
          <template v-if="!scope.row.orderNum.includes('移库') && !scope.row.orderNum.includes('调度入库')">
            <template v-if="$setAuthority('011004003', 'buttons')">
              <el-button type="text" v-if="scope.row.orderStatus === '待领取'" @click="handleUndoOrder(scope)">撤销</el-button>
            </template>
            <template v-if="$setAuthority('011004004', 'buttons')">
              <el-button type="text" v-if="scope.row.orderStatus === '待领取'" @click="handleModifyOrder(scope.row, 'applicationForStorage', '修改')">修改</el-button>
            </template>
          </template>
          <template v-if="$setAuthority('011004005', 'buttons')">
            <el-button
              type="text"
              :loading="scope.row.downloading"
              @click="handleDownloadOrder(scope.row)">下载{{scope.row.downloading ? '中...' : ''}}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <modify-order-dialog
      :pvisible="modifyOrderDialogVisible"
      :title="modifyDialogTitle"
      :page="page"
      :order-id="currentId"
      :status-des="currentRejectDes"
      @dialogCloseEvent="modifyOrderDialogVisible = false"
      @dialogConfirmEvent="modifyDialogConfirm"></modify-order-dialog>
    <in-and-out-dialog
      :pvisible="inAndOutDialogVisible"
      :type="applicationType"
      @dialogCloseEvent="handleInAndOutDialogClose"></in-and-out-dialog>
    <sample-scheduling-dialog
      :pvisible.sync="sampleSchedulingDialogVisible"
      @dialogCloseEvent="getData"/>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import modifyOrderDialog from './modifyOrderDialog'
import inAndOutDialog from './InAndOutDialog'
import sampleSchedulingDialog from './applicationForStorageSampleSchedulingDialog'
export default {
  name: 'applicationForStorage',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    modifyOrderDialog,
    inAndOutDialog,
    sampleSchedulingDialog
  },
  mounted () {
    this.getData()
  },
  data () {
    return {
      form: {
        order: '',
        time: []
      },
      formInput: {
        order: '',
        time: []
      },
      currentId: '',
      currentRejectDes: '', // 当前驳回的原因
      page: 'applicationForStorage',
      modifyOrderDialogVisible: false,
      modifyDialogTitle: '修改',
      status: 'in', // 出入库， in || out
      progressFilterOptions: [
        {text: '待领取', value: '待领取'},
        {text: '处理中', value: '处理中'},
        {text: '已完成', value: '已完成'},
        {text: '部分完成', value: '部分完成'},
        {text: '驳回', value: '驳回'}
      ],
      orderStatusFilterValues: [],
      applicationType: '', // in || out, 出入库申请类型
      inAndOutDialogVisible: false,
      sampleSchedulingDialogVisible: false // 样本调度
    }
  },
  methods: {
    getData () {
      let url = ''
      if (this.status === 'in') {
        url = '/sample/order/get_inner_order_list'
      } else if (this.status === 'out') {
        url = '/sample/order/get_inner_order_list'
      }
      if (url) {
        let time = this.form.time || []
        this.$ajax({
          url: '/sample/order/get_all_order_list',
          data: {
            orderNumber: this.form.order,
            startDate: time[0],
            endDate: time[1],
            fsampleOrderStatus: this.orderStatusFilterValues,
            isApply: 0,
            pageRequest: {
              current: this.currentPage,
              size: this.pageSize
            }
          },
          loadingDom: '.table'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.totalPage = res.data.total
            let rows = res.data.records || []
            this.tableData = []
            rows.forEach(v => {
              let item = {
                id: v.fid,
                orderStatus: v.fsampleOrderStatus,
                orderNum: v.fsampleOrderNumber,
                lab: v.flab,
                applicant: v.fapplicantName,
                operator: v.foperatorName,
                applicationTime: v.fapplicantTime,
                estimatedTime: v.expectedCompleteTime,
                rejectReason: v.frejectRemark,
                haveRejectSample: v.haveRejectSample
              }
              this.tableData.push(item)
            })
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.form.order = this.formInput.order
      this.form.time = this.formInput.time
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.formInput = {
        order: '',
        time: []
      }
      this.handleSearch()
    },
    // 筛选事件触发
    handleFilterTable (val) {
      if (val.orderStatus) {
        this.orderStatusFilterValues = []
        val.orderStatus.forEach(item => {
          this.orderStatusFilterValues.push(item)
        })
        this.handleSearch()
      }
    },
    // 切换出入库
    handleLibaryChange (val) {
      this.orderStatusFilterValues = []
      this.handleResetForm()
    },
    // 撤销申请单
    handleUndoOrder ({row}) {
      this.$confirm('请确认是否撤销申请单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/sample/order/cancel_order',
          method: 'get',
          data: {
            orderNumber: row.orderNum
          },
          loadingDom: 'body',
          loadingObject: {
            text: '正在撤销',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('撤销成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 修改申请单
    handleModifyOrder (row, page, title, rejectReason) {
      this.currentId = row.orderNum
      this.page = page
      this.currentRejectDes = rejectReason || ''
      this.modifyDialogTitle = title
      this.modifyOrderDialogVisible = true
    },
    // 显示出入库申请弹窗
    handleShowInAndOutDialog (status) {
      this.applicationType = status
      this.inAndOutDialogVisible = true
    },
    // 到移库申请
    handleToMoveStorage () {
      util.openNewPage('/business/sub/moveStorageApplication')
    },
    // 下载
    handleDownloadOrder (row) {
      this.$set(row, 'downloading', true)
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${row.orderNum}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        row.downloading = false
      })
    },
    // 修改弹窗确认
    modifyDialogConfirm () {
      this.modifyOrderDialogVisible = false
      this.getData()
    },
    // 出入库申请弹窗关闭
    handleInAndOutDialogClose () {
      this.inAndOutDialogVisible = false
      this.handleSearch()
    }
  }
}
</script>

<style scoped lang="scss">
.form-container{
  display: flex;
  justify-content: space-between;
  margin: 20px 0 0 0;
  padding-bottom: 20px;
  border-bottom: 1px solid #ccc;
  .form{
    display: flex;
    .form-item{
      display: flex;
      align-items: center;
      margin-right: 20px;
      label{
        width: 5em;
        flex-shrink: 0;
      }
    }
  }
}
</style>
