<template>
  <div class="card-wrapper">
    <el-descriptions :label-style="{width: '120px'}" :column="3" class="desc" size="mini" border>
      <el-descriptions-item label="姓名">{{baseInfo.name}}</el-descriptions-item>
      <el-descriptions-item label="样本编号">{{baseInfo.sampleNum}}</el-descriptions-item>
      <el-descriptions-item label="送检单位">{{baseInfo.hospital}}</el-descriptions-item>
      <el-descriptions-item label="性别">{{baseInfo.sex}}</el-descriptions-item>
      <el-descriptions-item label="样本类型">{{baseInfo.sampleType}}</el-descriptions-item>
      <el-descriptions-item label="送检项目">{{baseInfo.sendProject}}</el-descriptions-item>
      <el-descriptions-item label="出生年月">{{baseInfo.birthday}}</el-descriptions-item>
      <el-descriptions-item label="样本采集日期">{{baseInfo.collectDate}}</el-descriptions-item>
      <el-descriptions-item label="送检医生">{{baseInfo.doctor}}</el-descriptions-item>
      <el-descriptions-item label="身份证/护照">{{baseInfo.idCard}}</el-descriptions-item>
      <el-descriptions-item label="样本接受日期">{{baseInfo.receiverDate}}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{baseInfo.telephone}}</el-descriptions-item>
      <el-descriptions-item label="报告日期">{{baseInfo.reportDate}}</el-descriptions-item>
      <el-descriptions-item label="个人肿瘤史">{{baseInfo.clinicalDiagnose }}</el-descriptions-item>
      <el-descriptions-item label="家族史">{{baseInfo.familyTumorHistory}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      baseInfo: {}
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_report_basic_data',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || {}
        this.baseInfo = {
          id: info.fid,
          name: info.name,
          sampleNum: info.sampleNum,
          doctor: info.doctor,
          hospital: info.hospital,
          // sex: info.sex === 1 ? '女' : info.sex === 0 ? '男' : null,
          sex: info.sex,
          sampleType: info.sampleType,
          birthday: info.birthday,
          collectDate: info.collectDate,
          receiverDate: info.receiverDate,
          sendProject: info.sendProject,
          idCard: info.idCard,
          telephone: info.telephone,
          reportDate: info.reportDate,
          clinicalDiagnose: info.clinicalDiagnose,
          drugHistory: info.drugHistory,
          familyTumorHistory: info.familyTumorHistory
        }
      }
    }
  }
}
</script>

<style scoped></style>
