<template>
  <el-dialog
    title="编辑数据量"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1200px"
    @open="handleOpen">
    <div>
      <div class="tips">
        <!-- 显示订单和项目的基本信息 -->
        <div>订单编号：{{orderCode}}</div>
        <div>项目编号：{{projectCode}}</div>
        <div>项目名称：{{projectName}}</div>
      </div>
      <vxe-table
        v-if="!isLabOrder"
        ref="tableRef"
        class="table"
        key="sampleTable"
        border
        resizable
        size="mini"
        auto-resize
        height="400px"
        :keep-source="false"
        show-overflow="tooltip"
        :data="tableData"
        :edit-rules="validRules"
        :row-config="{isHover: true}"
        :checkbox-config="{trigger: 'row'}"
        :valid-config="{autoPos: true, showMessage: true, msgMode: 'full'}"
        :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
      >
        <!-- 定义表格列配置 -->
        <vxe-table-column type="seq" title="序号" width="60">
        </vxe-table-column>
        <vxe-table-column title="吉因加编号" field="geneCode" min-width="120"></vxe-table-column>
        <vxe-table-column title="原始样本编号" field="oldSampleName" min-width="120"></vxe-table-column>
        <vxe-table-column title="样本类型" field="sampleType" min-width="120"></vxe-table-column>
        <vxe-table-column title="下单数据量" field="dataSize" min-width="120" :edit-render="{name: '$input', props: {clearable: true}}"></vxe-table-column>
        <vxe-table-column title="数据量单位" field="dataSizeUnit" min-width="120">
          <template #default="{ row }">
            <span>{{ row.dataSizeUnit }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-table
        v-if="isLabOrder"
        ref="tableRef"
        class="table"
        key="labTable"
        border
        resizable
        size="mini"
        auto-resize
        height="400px"
        :keep-source="false"
        show-overflow="tooltip"
        :data="labTableData"
        :edit-rules="validRules"
        :row-config="{isHover: true}"
        :checkbox-config="{trigger: 'row'}"
        :valid-config="{autoPos: true, showMessage: true, msgMode: 'full'}"
        :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
      >
        <!-- 定义表格列配置 -->
        <vxe-table-column type="seq" title="序号" width="60">
        </vxe-table-column>
        <vxe-table-column title="文库名" field="name" min-width="120"></vxe-table-column>
        <vxe-table-column title="下单数据量" field="dataSize" min-width="120" :edit-render="{name: '$input', props: {clearable: true}}"></vxe-table-column>
        <vxe-table-column title="数据量单位" field="dataSizeUnit" min-width="120">
          <template #default="{ row }">
            <span>{{ row.dataSizeUnit }}</span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <!-- 对话框操作按钮 -->
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {awaitWrap} from '@/util/util'
import {updateSampleMonitoringInfoList} from '@/api/dataMonitoringManagement/smapleMonitoringManagementApi'
import {getOrderDetail} from '../../../../../api/dataMonitoringManagement/smapleMonitoringManagementApi'

/**
 * 编辑数据量对话框组件
 * @mixins dialogBaseInfo - 对话框基础信息混合体
 * @prop {Array} ids - 样本ID数组
 */
export default {
  name: 'editDataDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    orderCode: {
      type: String,
      default: ''
    },
    orderType: {
      type: Number,
      default: null
    }
  },
  computed: {
    // 不已文库维度
    isLabOrder () {
      return false
    }
  },
  data () {
    return {
      loading: false, // 提交按钮加载状态
      tableData: [], // 表格数据
      labTableData: [], // 文库数据
      validRules: {
        dataSize: [
          // 必填 ，类型数字
          { required: true, message: '请输入下单数据量' },
          {pattern: /^[0-9]\d*(\.\d)?$/, message: '请输入一位小数', trigger: 'change'}
        ]
      },
      dataSizeList: [
        {
          value: 'G raw data',
          label: 'G raw data'
        },
        {
          value: 'M raw reads',
          label: 'M raw reads'
        }
      ],
      projectCode: '',
      projectName: '',
      form: {
        status: '' // 样本状态
      }
    }
  },
  methods: {
    /**
     * 对话框打开时加载表格数据
     */
    handleOpen () {
      this.getData()
    },
    changeCellEvent  (params) {
      const $table = this.$refs.tableRef
      if ($table) {
        $table.updateStatus(params)
      }
    },
    async getData () {
      const {res} = await awaitWrap(getOrderDetail({
        fidList: this.ids
      }, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const {fprojectCode, fprojectName, sampleMonitorList = []} = res.data
        // 获取订单详情
        this.projectCode = fprojectCode
        this.projectName = fprojectName
        this.tableData = []
        this.labTableData = []
        // 获取订单样本信息
        sampleMonitorList.forEach(item => {
          const sampleInfo = {
            fid: item.fid,
            geneCode: item.fgeneCode,
            oldSampleName: item.foldSampleName,
            sampleType: item.fsampleType,
            dataSize: item.fdataSize,
            dataSizeUnit: item.fdataSizeUnit
          }
          // 获取文库信息
          if (item.cosSampleSubBeanList && item.cosSampleSubBeanList.length > 0) {
            item.cosSampleSubBeanList.forEach(subItem => {
              this.labTableData.push({
                fid: subItem.fid,
                name: subItem.fname,
                dataSize: subItem.fdataSize,
                dataSizeUnit: subItem.fdataSizeUnit
              })
            })
          }
          this.tableData.push(sampleInfo)
        })
      }
    },
    setParams () {
      return {
        fidList: this.ids,
        sampleMonitorList: this.tableData.map(v => {
          return {
            fid: v.fid,
            fgeneCode: v.geneCode,
            foldSampleName: v.oldSampleName,
            fsampleType: v.sampleType,
            fdataSize: v.dataSize,
            fdataSizeUnit: v.dataSizeUnit
          }
        }),
        cosSampleSubBeanList: this.labTableData.map(v => {
          return {
            fid: v.fid,
            fname: v.name,
            fdataSize: v.dataSize,
            fdataSizeUnit: v.dataSizeUnit
          }
        })
      }
    },
    /**
     * 提交按钮点击事件处理函数
     * @async
     */
    async handleConfirm () {
      const $table = this.$refs.tableRef
      const errMap = await $table.fullValidate(true)
      if (errMap) {
        this.$message.error('请检查表格内容是否有误')
        return
      }
      const params = this.setParams()
      this.loading = true
      // 调用后端接口更新样本状态
      const { res } = await awaitWrap(updateSampleMonitoringInfoList(
        params
      ))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('修改成功')
        this.$emit('dialogConfirmEvent') // 触发父组件数据刷新
        this.visible = false // 关闭对话框
      }
      this.loading = false // 结束加载状态
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
