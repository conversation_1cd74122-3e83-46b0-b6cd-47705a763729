<template>
  <div>
    <el-dialog
      v-drag-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="异常登记"
      width="800px"
      @open="handleOpen">
      <div>
        <el-form ref="form" :model="form" label-suffix=":" :rules="rules" label-width="110px" inline>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="勾选样本例数" prop="sampleNums">
                {{sampleIds.length}}
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="异常描述" prop="unusualRemark">
                <el-input
                  v-model.trim="form.unusualRemark"
                  :rows="4"
                  type="textarea"
                  size="mini"
                  show-word-limit
                  maxlength="200"
                  style="width: 500px"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="图片说明" prop="picList">
                <el-upload
                  ref="upload"
                  :auto-upload="true"
                  :action="uploadUrl"
                  :file-list="form.picList"
                  :headers="headers"
                  :on-success="handleOnSuccess"
                  :on-error="handleOnError"
                  :on-progress="handleProgress"
                  :on-change="handleBeforeUpload"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  multiple
                  accept="image/jpg,image/jpeg,image/png"
                  list-type="picture-card">
                  <i class="el-icon-plus"></i>
                </el-upload>
                <Tips :size="10"></Tips>
                <el-dialog :visible.sync="dialogVisible"
                           title="图片预览"
                           width="450px"
                           append-to-body>
                  <img :src="dialogImageUrl" style="width:400px; object-fit: fill" alt="">
                </el-dialog>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否通知客户" prop="picList">
                <el-checkbox v-model.trim="form.isNotice" size="mini"  :true-label="1" :false-label="0">通知客户</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">{{form.isNotice ? '下一步' : '确 定'}}</el-button>
      </span>
    </el-dialog>
    <exception-notice-dialog
      :pvisible.sync="exceptionNoticeDialogVisible"
      :exception-ids="exceptionIds"
    />
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import Cookies from 'js-cookie'
import constants from '@/util/constants'
import ExceptionNoticeDialog from './exceptionNoticeDialog'
import {awaitWrap} from '../../../../util/util'
import {saveException} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'registerExceptionDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {ExceptionNoticeDialog},
  props: {
    sampleIds: {
      type: Array,
      default: () => []
    },
    type: { // 当前环节
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      loading: false,
      exceptionNoticeDialogVisible: false,
      form: {
        picList: [],
        isNotice: 1,
        sampleNums: '', // 已选样本数量
        unusualRemark: '' // 异常描述
      },
      exceptionIds: null, // 异常ID
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      dialogImageUrl: '',
      dialogVisible: false,
      headers: {
        token: Cookies.get('token')
      },
      rules: {
        unusualRemark: [
          { required: true, message: '请选择异常描述', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.$refs.form.resetFields()
        this.form.picList = []
        this.form.registerSample = ''
        this.form.isNotice = 1
        this.form.unusualRemark = ''
        this.form.sampleNums = this.sampleIds
      })
    },
    setParams () {
      let fileList = this.form.picList || []
      fileList = fileList.map(v => {
        return {
          group: v.group,
          path: v.path
        }
      })
      return {
        fidList: this.sampleIds,
        fworkflowId: this.type,
        fdescribe: this.form.unusualRemark,
        fisSentEmail: this.form.isNotice,
        fpicture: JSON.stringify(fileList)
      }
    },
    // 点击确认
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      const params = this.setParams()
      let {res = {}} = await awaitWrap(saveException(params))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('登记异常成功，后期请在异常处理中进行处理')
        this.exceptionIds = res.data || []
        this.visible = false
        if (this.form.isNotice) {
          this.exceptionNoticeDialogVisible = true
          return
        }
      }
      this.loading = false
    },
    handleRemove (file, fileList) {
      this.onProgress = false
      this.form.picList = [...fileList]
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 附件提交成功回调
    handleOnSuccessFile (res) {
      this.onProgress = false
      this.form.attachFile.push(res.data)
    },
    // 提交前的函数
    handleBeforeUpload (file, fileList) {
      let name = file.name
      if (fileList.length > 10) {
        this.$message.error('上传数量超出限制')
        fileList.pop()
        return false
      }
      if (!/\.(jpg|png|jpeg)$/.test(name)) {
        this.$message.error('只能上传jpg、png或jpeg的图片')
        fileList.pop()
        this.form.picList = fileList
        return false
      }
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件不能大于10M，请重新上传')
        fileList.pop()
        this.form.picList = fileList
        return false
      }
      this.form.picList = fileList
      return true
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.onProgress = false
      if (res && res.code === this.SUCCESS_CODE) {
        file.onlineUrl = res.data.absolutePath
        file.group = res.data.group
        file.path = res.data.path
        this.form.picList = [...fileList]
      }
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    }
  }
}
</script>

<style scoped>
.el-message-box{
  width: 570px;
}
.unusual-style{
  float: left;
  margin: 4px 4px 0 0;
  color: #F56C6C;
}
</style>
