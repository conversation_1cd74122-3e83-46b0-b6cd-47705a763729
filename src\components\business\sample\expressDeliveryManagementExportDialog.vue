<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :modal="false" :close-on-click-modal="false"
      :before-close="handleClose" v-drag-dialog
      title="导出"
      width="400px"
      @open="handleOpen"
    >
    <!--      导出选项-->
      <el-form :model="form" ref="form">
        <el-radio-group v-model.trim="form.isAll" prop="isAll">
          <el-radio :label="0" :disabled="isDisable" style="margin-bottom: 20px">导出所选数据（共{{selectNumber}}条）</el-radio>
          <el-radio :label="1">导出当前全部数据（共{{allNumber}}条）</el-radio>
        </el-radio-group>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'expressDeliveryManagementExportDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    selectNumber: {
      type: Number
    },
    allNumber: {
      type: Number
    }
  },
  data () {
    return {
      isDisable: false, // 表格是否显示数据
      form: {
        isAll: ''
      }
    }
  },
  methods: {
    // 清空表单状态
    handleOpen () {
      this.$nextTick(() => {
        if (this.selectNumber === 0) {
          this.isDisable = true
        }
        this.form = {
          isAll: ''
        }
      })
    },
    handleConfirm () {
      this.visible = false
      this.$emit('dialogConfirmEvent', this.form.isAll)
    }
  }
}
</script>

<style scoped>

</style>
