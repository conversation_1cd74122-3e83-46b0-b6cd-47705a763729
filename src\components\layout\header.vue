<template>
  <div>
    <div class="header">
      <div class="logo-container">
        <img :src="require('../../assets/logo.png')" alt="">
        <span>LIMS</span>
      </div>
      <div class="info-container">
        <div v-if="$route.path.indexOf('/business/view/') > -1">
          <label style="margin-right: 15px;font-size: 14px;">所属实验室</label>
          <el-select
            v-model="lab"
            size="mini"
            multiple
            @visible-change="handleVisibleChange"
            @change="handleLabChange">
            <el-option
              :key="item.value"
              :value="item.value"
              :label="item.label"
              v-for="item in labOptions"></el-option>
          </el-select>
        </div>
        <div style="margin-left: 20px;display: flex;align-items: center;">
          <el-avatar shape="circle" size="mini" icon="el-icon-user-solid"></el-avatar>
          <span style="margin-left: 10px;">{{username || '用户名'}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../util/util'
import Cookies from 'js-cookie'
export default {
  props: {
    currentLabOptions: {
      type: Array,
      default () { return [] }
    },
    currentlab: {
      type: Array,
      default () { return [] }
    }
  },
  created () {
    let lab = Cookies.get('flab')
    let labOptions = Cookies.get('labOptions')
    this.labSubmit = lab ? lab.split(',').map(v => { return +v }) : []
    this.lab = util.deepCopy(this.labSubmit)
    util.setSessionInfo('currentLab', this.lab)
    this.labOptions = labOptions ? JSON.parse(labOptions) : []
  },
  watch: {
    currentLabOptions: {
      handler: function (newVal) {
        this.labOptions = newVal
      },
      deep: true
    },
    currentlab (newVal) {
      if (newVal) {
        this.lab = newVal
        this.labSubmit = util.deepCopy(newVal)
      }
    }
  },
  computed: {
    username () {
      return this.$store.getters.getValue('userInfo').name
    },
    labText () {
      let r = []
      this.labOptions.forEach(v => {
        if (this.lab.indexOf(v.value)) r.push(v.label)
      })
      return r.join('，')
    }
  },
  data () {
    return {
      lab: [],
      labSubmit: [],
      labOptions: [
        // {label: '全部', value: 0},
        // {label: '北京实验室', value: 2},
        // // {label: '苏州实验室', value: 2},
        // {label: '深圳实验室', value: 1}
      ]
    }
  },
  methods: {
    handleClearBrowserCache (options) {
      const {
        clearLocal = true,
        clearSession = true,
        clearCookies = true,
        reloadAfter = true
      } = options || {}

      try {
        // 清理 localStorage
        if (clearLocal) {
          localStorage.clear()
        }

        // 清理 sessionStorage
        if (clearSession) {
          sessionStorage.clear()
        }

        // 清理 cookies
        if (clearCookies) {
          document.cookie.split(';').forEach(cookie => {
            const eqPos = cookie.indexOf('=')
            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
          })
        }

        // 清理缓存后刷新
        if (reloadAfter) {
          window.location.reload(true)
        }
      } catch (error) {
        console.error('缓存清理失败:', error)
      }
    },
    handleLabChange (val) {
      console.log(1111111)
      // let l = val.length
      // if (l > 0 && val[l - 1] === 0) {
      //   this.lab = [0]
      // } else if (val.length > 1 && val.includes(0)) {
      //   let i = val.indexOf(0)
      //   val.splice(i, 1)
      // }
      this.handleVisibleChange(false)
      // Cookies.set('flab', this.labSubmit.toString())
    },
    handleVisibleChange (show) {
      if (!show) {
        // if (this.lab.length === 3) { // 全选的情况
        //   this.lab = [0]
        //   if (this.lab.toString() !== this.labSubmit.toString()) {
        //     this.refreshPage()
        //   }
        // } else
        if (this.lab.length === 0) { // 一个都不选的情况
          this.lab = util.deepCopy(this.labSubmit)
        } else { // 选中部分的情况
          // 选中项没变化
          let lab = this.lab.sort()
          if (lab.toString() !== this.labSubmit.toString()) {
            this.refreshPage()
          }
        }
        util.setSessionInfo('currentLab', this.lab)
      }
    },
    refreshPage () {
      this.labSubmit = util.deepCopy(this.lab.sort())
      Cookies.set('flab', this.labSubmit.toString())
      if (this.$route.query.jmoz) {
        let url = location.origin + location.pathname
        window.location.href = url
      } else {
        window.history.go(0)
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .header{
    height: 64px;
    background: #00284D;
    color: #fff;
    padding: 0 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .logo-container{
      display: flex;
      align-items: center;
      font-size: 30px;
      img{
        height: 40px;
        width: auto;
        display: block;
        margin-right: 10px;
      }
    }
  .info-container{
    display: flex;
  }
  }
</style>
