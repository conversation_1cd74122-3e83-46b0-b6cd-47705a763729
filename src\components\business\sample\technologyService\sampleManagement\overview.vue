<template>
  <div class="page">
    <div class="operate-btns-group">
      <el-button
        v-if="$setAuthority('018003001', 'buttons')"
        size="mini" type="primary" @click="handleSampleTreatment">样本处理</el-button>
      <el-dropdown v-if="$setAuthority('018003002', 'buttons') || $setAuthority('018003003', 'buttons')" style="margin: 0 10px" @command="handleCommand">
        <el-button  :loading="loading" type="primary" plain size="mini">
          导出<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-if="$setAuthority('018003002', 'buttons')" :command="0">查询导出</el-dropdown-item>
          <el-dropdown-item v-if="$setAuthority('018003003', 'buttons')" :command="1">勾选导出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button type="primary" size="mini" plain @click="searchMessageVisible = true">查询</el-button>
      <el-button type="primary" size="mini" plain @click="handleReset">重置</el-button>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      height="calc(100vh - 74px - 40px - 42px - 32px)"
      :cell-style="handleRowStyle"
      class="table"
      size="mini"
      border
      style="width: 100%"
      @select="handleSelectTable"
      @row-click="handleRowClick"
      @select-all="handleSelectAll"
    >
      <el-table-column type="selection" fixed="left" width="55"></el-table-column>
      <el-table-column prop="orderNum"  label="申请单号" resizable min-width="140px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="link" @click="handleShowDetail(scope.row.id, scope.row.orderNum, scope.row.applyTypeName)">{{scope.row.orderNum}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="handleStatusName" label="处理状态" min-width="80" show-overflow-tooltip></el-table-column>
      <el-table-column prop="applyTypeName" label="申请类型" min-width="80" show-overflow-tooltip></el-table-column>
      <el-table-column prop="applyTime" label="申请时间" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="inspectionUnit" label="送检单位" min-width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="recipientInfoSummary" label="收件人信息" min-width="120" show-overflow-tooltip></el-table-column>

      <el-table-column prop="sampleCount" label="样本数量" min-width="80px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="applyNote" label="申请备注" min-width="220" show-overflow-tooltip></el-table-column>
      <el-table-column prop="handleNote" label="处理备注" min-width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.realData.handleNote" class="item" effect="dark" content="点击查看处理备注" placement="top">
            <span class="link" @click="handleShowNote(scope.row.id)">查看</span>
          </el-tooltip>
          <div v-else>
            {{scope.row.handleNote}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="expressInfo" label="快递信息" min-width="180">
        <template slot-scope="scope">
          <tooltips
            :key="index"
            :is-link="true"
            :txt-info="item.text"
            v-for="(item, index) in scope.row.expressInfo"
            @linkTo="handleShowExpress(item.code)"></tooltips>
        </template>
      </el-table-column>

      <el-table-column prop="handleTime" label="处理时间" min-width="180" show-overflow-tooltip></el-table-column>
    </el-table>
    <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh">
          <icon-svg icon-class="icon-refresh"/>
        </button>
      </el-pagination>
    </div>
    <sample-deal-dialog
      :pvisible.sync="sampleDialogVisible"
      :sample-deal-data="sampleDealData"
      @dialogConfirmEvent="handleSearch"
    ></sample-deal-dialog>
    <courier-number-dialog :pvisible.sync="expressVisible" :code="expressCode"></courier-number-dialog>
    <apply-detail-dialog :pvisible.sync="detailDialogVisible"
                         :order-num="orderNum"
                         :order-name="orderName"
                         :order-id="orderId"></apply-detail-dialog>
    <search-dialog
      :pvisible.sync="searchMessageVisible"
      @dialogConfirmEvent="handleSearch"/>
    <sample-deal-note-dialog
      :pvisible.sync="noteVisible"
      :order-id="orderId"
    ></sample-deal-note-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import util from '../../../../../util/util'
import mixins from '../../../../../util/mixins'
import searchDialog from './searchDialog'
import SampleDealDialog from './sampleDealDialog'
import applyDetailDialog from './applyDetailDialog'
import CourierNumberDialog from './courierNumberDialog'
import SampleDealNoteDialog from './sampleDealNoteDialog'

export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    SampleDealNoteDialog,
    SampleDealDialog,
    searchDialog,
    applyDetailDialog,
    CourierNumberDialog
  },
  created () {
    this.remoteMethod()
  },
  mounted () {
    this.getData()
  },
  data () {
    return {
      orderNum: '',
      orderCode: '',
      orderName: '',
      noteVisible: false,
      recipients: '',
      orderId: null, // 申请单ID
      applyTypeCode: null,
      applyTime: null,
      productionAreaId: null,
      selectedRows: new Map(),
      expressVisible: false,
      expressCode: '',
      form: {
      },
      statusOptions: {
        2: '已返样',
        3: '已销毁',
        4: '到期销毁',
        5: '延期保存'
      },
      tableData: [],
      sampleDialogVisible: false, // 样本处理弹窗
      detailDialogVisible: false,
      loading: false,
      searchMessageVisible: false, // 查询弹窗
      sampleCode: '',
      rules: {
        detail: [{required: true, message: '请选择', trigger: 'blur'}],
        inputMessage: [{ min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }]
      },
      sampleDealData: {}, // 样本处理数据
      searchDatas: {},
      list: [],
      detailList: [],
      sampleList: []
    }
  },
  methods: {
    setParam (data = {}) {
      let applyTime = data.applyTime || []
      let handleTime = data.handleTime || []
      let preReceiveTime = data.preReceiveTime || []
      return {
        orderNum: data.orderNum, // 申请单号
        inspectionUnit: data.inspectionUnit, // 送检单位
        applyType: data.applyType ? [data.applyType] : [], // 申请类型
        applyTimeStart: applyTime[0], // 申请时间
        applyTimeEnd: applyTime[1], // 申请时间
        handleStatus: data.handleStatus ? [data.handleStatus] : [], // 处理状态
        expressNum: data.expressNum, // 快递单号
        expressStatus: data.expressStatus ? [data.expressStatus] : [], // 返样快递状态
        preReceiveTimeStart: preReceiveTime[0], // 预计送达时间
        preReceiveTimeEnd: preReceiveTime[1], // 预计送达时间
        handleTimeStart: handleTime[0], // 处理时间
        handleTimeEnd: handleTime[1], // 处理时间
        receiver: data.receiver, // 收件人
        address: data.address, // 完整地址
        phone: data.phone // 收件人电话
      }
    },
    handleReset () {
      this.handleSearch({
        orderNum: '', // 申请单号
        inspectionUnit: '', // 送检单位
        applyType: '', // 申请类型
        applyTime: '', // 申请时间
        handleStatus: '', // 处理状态
        expressNum: '', // 快递单号
        expressStatus: '', // 返样快递状态
        preReceiveTime: '', // 预计送达时间
        handleTime: '', // 处理时间
        receiver: '', // 收件人
        address: '', // 完整地址
        phone: '' // 收件人电话
      })
    },
    // handleSearch (data = {}) {
    //   this.currentPage = 1
    //   this.getData(data)
    // },
    getData (data = {}) {
      this.form = data
      let params = this.setParam(data)
      this.$ajax({
        url: '/sample/t7_return/page_t7_return_order',
        data: {
          params: params,
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let expressInfo = this.setExpressInfo(v.expressInfo)
            let item = {
              id: v.orderId,
              orderNum: v.orderNum,
              orderCode: v.orderCode,
              sampleCount: v.sampleCount,
              inspectionUnit: v.inspectionUnit,
              applyTypeCode: v.applyTypeCode,
              applyTypeName: v.applyTypeName,
              applyTime: v.applyTime,
              applyNote: v.applyNote,
              handleStatusCode: v.handleStatusCode,
              handleStatusName: v.handleStatusName,
              handleTime: v.handleTime,
              handleNote: v.handleNote,
              handleUserName: v.handleUserName,
              receiver: v.receiver,
              province: v.province,
              city: v.city,
              region: v.region,
              address: v.address,
              completeAddr: v.completeAddr,
              phone: v.phone,
              productionAreaId: v.productionAreaId,
              recipientInfoSummary: v.recipientInfoSummary,
              fprojectCode: v.fprojectCode,
              expressInfo: expressInfo,
              recipients: v.frecipients,
              preReceiveTime: v.preReceiveTime
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.handleEchoSelect()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 设置快递信息
    setExpressInfo (express) {
      if (!express) return [{text: '-'}]
      // 切割多个快递
      let expressInfo = express.split(',') || []
      expressInfo = expressInfo.map(v => {
        return {
          text: v,
          code: v.split('/')[0]
        }
      })
      return expressInfo
    },
    handleShowNote (id) {
      this.noteVisible = true
      this.orderId = id
    },
    // 预览物流信息
    handleShowExpress (code) {
      this.expressCode = code
      this.expressVisible = true
    },
    handleShowDetail (id, num, name) {
      this.orderNum = num
      this.orderName = name
      this.orderId = id
      this.detailDialogVisible = true
    },
    // 选择导出类型
    handleCommand (command) {
      command === 0 ? this.handleExportAll() : this.handleExport()
    },
    // 查询导出
    handleExportAll () {
      if (this.totalPage > 1000) {
        this.$message.error('当前数据过多，请通过查询做数据筛选')
        return
      }
      let params = this.setParam(this.form)
      this.$confirm(`是否确认导出。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        this.$ajax({
          url: '/sample/t7_return/download_t7_return_order',
          data: params,
          responseType: 'blob'
        }).then(res => {
          util.readBlob(res.data).then(() => {
            util.downloadFile(res)
          }).catch(msg => {
            this.$message.error(msg)
          })
        }).finally(() => {
          this.loading = false
        })
      })
    },
    // 导出所选
    handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let keys = [...this.selectedRows.keys()]
      this.$confirm(`是否确认导出。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        this.$ajax({
          url: '/sample/t7_return/download_t7_return_order',
          data: {
            orderIdList: keys
          },
          responseType: 'blob'
        }).then(res => {
          util.readBlob(res.data).then(() => {
            util.downloadFile(res)
          }).catch(msg => {
            this.$message.error(msg)
          })
        }).finally(() => {
          this.loading = false
        })
      })
    },
    // 设置过期时间和字体颜色
    setExpireTime (timeNum) {
      let res = util.dateFormatter(timeNum) // 过期时间
      let intervalsTime = (Date.parse(timeNum) - new Date()) / 1000 / 60 / 60 / 24 // 过期时间截至目前为止的间隔时间
      let color = '#313030'
      let text = ''
      if (intervalsTime > 0 && intervalsTime < 30) { // 间隔时间小于1个月时
        color = '#f13232'
        text = '即将过期'
      }
      if (intervalsTime < 0) {
        color = '#f13232'
        text = '已过期'
      }
      return {res, color, text}
    },
    handleSearch (data) {
      this.searchMessageVisible = false
      this.currentPage = 1
      this.clearMap()
      this.getData(data)
    },
    // 点击样本处理按钮
    handleSampleTreatment () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择一条数据')
        return
      }
      const rows = [...this.selectedRows.values()]
      let row = rows[0]
      const hasNotReturnSample = rows.some(v => v.applyTypeName !== '申请返样')
      if (hasNotReturnSample) {
        if (this.selectedRows.size > 1) {
          this.$message.error('请选择一条记录进行处理')
          return
        }
      } else {
        if (rows.some(v => v.fprojectCode !== row.fprojectCode)) {
          this.$message.error('订单所属项目编号不同，请选择同一个项目编号的订单进行处理')
          return
        }
      }
      if (rows.some(row => row.handleStatusCode !== 1)) {
        this.$message.error('只能选择未处理的样本进行处理！')
        return
      }
      this.sampleDealData = {
        ids: rows.map(v => v.id),
        hasNotReturnSample: hasNotReturnSample,
        fprojectCode: row.fprojectCode,
        orderNumList: rows.map(row => row.orderNum),
        orderCode: row.orderCode,
        productionAreaId: row.productionAreaId,
        applyTypeCode: row.applyTypeCode,
        applyTime: row.applyTime,
        recipients: row.recipients
      }
      this.sampleDialogVisible = true
    },
    remoteMethod () {
      this.$ajax({
        url: '/sample/t7_return/list_apply_type'
      }).then(result => {
        if (result.code === '200') {
          this.list = result.data
        } else {
          this.$message.error(result.message)
        }
      })
      this.$ajax({
        url: '/sample/t7_return/list_handle_status'
      }).then(result => {
        if (result.code === '200') {
          this.detailList = result.data
        } else {
          this.$message.error(result.message)
        }
      })
      this.$ajax({
        url: '/sample/t7_return/list_cos_sample_status'
      }).then(result => {
        if (result.code === '200') {
          this.sampleList = result.data
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped>
.topsearch{
  float: right;
}
>>>.el-form-item__label{
  width: 120px;
}

.buttonGroup{
  margin: 10px 0;
  height: 40px;
  line-height: 40px;
}

.header-style {
  background: #eee;
}
</style>
