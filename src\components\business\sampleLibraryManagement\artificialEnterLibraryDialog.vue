<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      fullscreen
      width="80%"
      @open="handleOpen"
      :before-close="handleClose">
      <el-tabs v-model.trim="activeName">
        <el-tab-pane label="文件匹配入库" name="1">
          <div
              v-loading="loading"
              element-loading-text="正在上传文件"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)">
            <div class="path">
              <el-button size="mini" type="primary" @click="handleDown">下载入库单</el-button>
              <el-upload
                  class="upload"
                  drag
                  :on-success="handleOnSuccess"
                  :on-error="handleOnError"
                  :data="uploadParams"
                  :auto-upload="true"
                  :headers="headers"
                  :limit="1"
                  :file-list="fileList"
                  :before-upload="handleBeforeUpload"
                  :action="uploadUrl">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="实时定位入库" name="2">
          <div class="wrapper">
            <div class="path">
              <el-table
                :data="tableData"
                @select="handleSelect"
                @row-click="handleRowClick"
                style="width: 100%;"
                ref="table"
                max-height="300">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column label="样本状态" width="100" prop="fsampleStatus"></el-table-column>
                <el-table-column label="样本编号" width="150" prop="fsampleNumber">
                  <template slot-scope="scope">
                    <span>{{scope.row.fsampleNumber}}</span>
                    <i class="el-icon-document-copy" style="color: #409EFF;cursor: pointer;" @click.stop="handleCopy(scope.row.fsampleNumber)"></i>
                  </template>
                </el-table-column>
                <el-table-column label="原始样本编号" width="120" prop="foriginNum"></el-table-column>
                <el-table-column
                  label="样本类型"
                  width="180"
                  prop="fsampleType"
                  :filters="sampleTypeFilterOptions"
                  :filter-method="sampleTypeFilterMethod"
                ></el-table-column>
                <el-table-column label="管型" width="100" prop="ftubeType"></el-table-column>
                <el-table-column label="样本量" width="100" prop="fsampleAmount"></el-table-column>
                <el-table-column label="所属实验室" width="150" prop="flab"></el-table-column>
                <el-table-column label="存储温度" width="100" prop="ftemperature"></el-table-column>
                <el-table-column label="存储位置" min-width="180" prop="fsamplePlace">
                  <template slot-scope="scope">
                    <span>{{scope.row.fsamplePlace}}</span>
                    <i v-if="scope.row.fsamplePlace" class="el-icon-circle-close" style="color: #409EFF;cursor: pointer;" @click.stop="handleDelPos(scope.row)"></i>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="path">
              <div style="display: flex;align-items: center;background: #f2f2f2;padding: 10px 10px 10px 310px;">
                <div v-if="positionSet.label" style="display: flex;align-items: center;">
                  <label style="padding: 0 10px;">{{positionSet.label}}</label>
                  <el-input
                    ref="input"
                    v-model.trim="positionSet.value"
                    size="small"
                    style="width: 200px"
                    @blur="handleInput"
                    @keyup.enter.native="$event.target.blur()"></el-input>
                </div>
                <div style="font-weight: 600;margin-left: 20px;">已选位置：{{hasChoosePosText}}</div>
              </div>
              <box-choose
                ref="boxChoose"
                height="450px"
                :limit-sample-code="currentSamples"
                :current-pos-id="positionSet.label"
                :current-sample="currentChooseSample"
                @input="handleSetPos"
                @boxChange="handleBoxChange"
                @inputError="focusInput"
                @clickHole="handleSetPosSet"/>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util, {awaitWrap} from '../../../util/util'
import {delSamplePos, saveSamplePos} from '../../../api/enterLibraryManagement'
export default {
  name: 'artificialEnterLibraryDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Array
    }
  },
  watch: {
    pdata: {
      handler: function (newVal) {
        if (newVal) {
          this.tableData = newVal.filter(item => {
            return item.fsampleType !== '石蜡包埋组织'
          })
          this.downloadIds = newVal.map(v => v.fid)
        }
      },
      immediate: true
    }
  },
  computed: {
    enterLibraryOrder () {
      return this.$store.getters.getValue('enterLibraryOrder')
    },
    sampleTypeFilterOptions () {
      const arr = this.tableData.map(v => v.fsampleType)
      return [...new Set(arr)].map(v => {
        return {
          text: v,
          value: v
        }
      })
    },
    currentSamples () {
      return this.tableData.map(v => v.fsampleNumber)
    },
    hasChoosePosText () {
      const all = this.tableData.length
      const has = this.tableData.filter(v => v.fsamplePlace).length
      return `${has} / ${all}`
    },
    // 当前选中的样本
    currentChooseSample () {
      const values = Object.values(this.selectedRows)
      return values[0] ? values[0].fsampleNumber : ''
    }
  },
  data () {
    let loginId = util.getSessionInfo('loginId')
    let id = loginId ? util.decryptBase64(loginId) : '1'
    return {
      uploadUrl: constants.JS_CONTEXT + '/sample/order/import_sample_place_order',
      activeName: '1',
      uploadParams: {},
      headers: {
        'user-id': id
      },
      fileList: [],
      downloadIds: [], // 下载时候的ids，这个是不过滤石蜡包埋组织的
      loading: false,
      tableData: [],
      selectedRows: {},
      positionSet: {
        rowIndex: '',
        columnIndex: '',
        label: '', // 初始位置，可以输入的
        value: '',
        currentBox: null
      },
      keepLoginTimer: null
    }
  },
  methods: {
    handleOpen () {
      this.keepLogin()
    },
    /**
     * 这个弹窗的数据会很多，所以用户填写时间会很长
     * 现在打开这个弹窗后就开始计时，20分钟发一次请求
     * 用来保证只要这个弹窗开着，登录永远不会失效
     */
    keepLogin () {
      this.keepLoginTimer = setInterval(() => {
        this.$ajax({url: '/user/live_token'})
      }, 20 * 60 * 1000)
    },
    async handleCopy (text) {
      await util.copyText(text)
      this.$message.success('已复制到剪贴板')
    },
    // 删除已选的位置
    async handleDelPos (row) {
      const {res} = await awaitWrap(delSamplePos({
        fsampleNumber: row.fsampleNumber,
        fsampleInnerOrderNumber: this.enterLibraryOrder,
        fsampleAmount: row.fsampleAmount,
        fsamplePlace: row.fsamplePlace
      }, {
        loadingDom: '.wrapper'
      }))
      if (res.code && res.code === this.SUCCESS_CODE) {
        row.fsamplePlace = ''
        const data = res.data || {}
        row.fsampleStatus = data.fsampleStatus
        const ref = this.$refs.boxChoose
        // boxPos: { box: currentBox, pos: id }
        const box = ref.currentSelectBox || {}
        if (row.boxPos) {
          const box = ref.currentSelectBox || {}
          // 判断当前显示的是不是这个孔板，是的话就原始数据和当前数据都要删除
          if (box.id && box.id === row.boxPos.box.id) {
            ref.clearSampleCode([row.boxPos.pos])
            this.getBoxInitPos()
          }
          const targetBox = ref.findBox(row.boxPos.box)
          this.$set(targetBox.holeSampleNumberObj, row.boxPos.pos, {sampleCode: '', canInput: true, sampleId: ''})
        }
        if (box) {
          ref.handleUpdatePlate()
        }
      } else {
        this.$message.error(res.message)
      }
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.tableData = res.data || []
      } else {
        this.$showErrorDialog({
          tableData: res.data || []
        })
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 8) {
          this.loading = false
          this.$message.error('文件大小超过限制，无法上传')
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    },
    // 下载申请单
    handleDown () {
      let form = document.createElement('form')
      form.action = constants.JS_CONTEXT + '/sample/order/download_sample_place_template'
      form.method = 'post'
      form.id = 'form'
      // let ids = this.tableData.map(item => {
      //   return item.fid
      // })
      let submitData = {
        sampleIds: this.downloadIds.toString()
      }
      for (let key in submitData) {
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = submitData[key]
        form.appendChild(input)
      }
      document.body.appendChild(form)
      form.submit()
      form.parentNode.removeChild(form)
    },
    // 筛选样本类型
    sampleTypeFilterMethod (value, row) {
      return row.fsampleType === value
    },
    // 点击表格行
    handleRowClick (row) {
      this.$refs.table.clearSelection()
      let hasThisId = !!this.selectedRows[row.fid]
      this.$refs.table.toggleRowSelection(row, !hasThisId)
      this.selectedRows = {}
      if (!hasThisId) {
        this.$set(this.selectedRows, row.fid, row)
      }
    },
    // 选中行
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    // 设置当前点击的盒子
    handleSetPosSet (item) {
      this.positionSet = {
        rowIndex: item.row,
        columnIndex: item.column,
        label: item.id,
        value: '',
        currentBox: item.currentSelectBox
      }
    },
    // 获取当前盒子的第一个或者下一个
    getBoxInitPos (isNext = false) {
      const ref = this.$refs.boxChoose
      const box = ref.currentBox.boxes
      let fistItem = null
      let nextItem = null
      let firstRowIndex = '' // 第一个空位的横坐标
      let firstColumnIndex = '' // 第一个空位的纵坐标
      let nextRowIndex = '' // 下一个空位的横坐标
      let nextColumnIndex = '' // 下一个空位的纵坐标
      let hasCurrent = !!this.positionSet.label // 当前是不是已经选了一个
      for (let i = 0; i < box.length; i++) {
        const arr = box[i]
        for (let j = 0; j < arr.length; j++) {
          const v = arr[j]
          if (!v.sampleCode) {
            // 找下一个 如果横坐标相同，则对比纵坐标
            if (hasCurrent &&
              (i > this.positionSet.rowIndex ||
                (i === this.positionSet.rowIndex && j > this.positionSet.columnIndex)
              )) {
              nextItem = v
              nextRowIndex = i
              nextColumnIndex = j
            }
            // 拿第一个
            if (!fistItem) {
              fistItem = v
              firstRowIndex = i
              firstColumnIndex = j
            }
            if (fistItem && (nextItem || !hasCurrent)) break
          }
        }
        if (fistItem && (nextItem || !hasCurrent)) break
      }
      // 看想要哪个
      const item = (isNext && nextItem) ? nextItem : fistItem
      const rowIndex = (isNext && nextItem) ? nextRowIndex : firstRowIndex
      let columnIndex = (isNext && nextItem) ? nextColumnIndex : firstColumnIndex
      this.positionSet = {
        rowIndex: item ? rowIndex : '',
        columnIndex: item ? columnIndex : '',
        label: item ? item.id : '',
        value: '',
        currentBox: ref.currentSelectBox
      }
    },
    // 盒子改变
    handleBoxChange (isNext = false) {
      return new Promise(resolve => {
        setTimeout(() => {
          this.getBoxInitPos(isNext)
          resolve()
        })
      })
    },
    // 输入确定
    handleInput () {
      const ref = this.$refs.boxChoose
      const box = ref.currentBox.boxes
      const { rowIndex, columnIndex, value } = this.positionSet
      if (value) {
        const item = box[rowIndex][columnIndex]
        item.sampleCode = value
        ref.handleEnter(item, true)
      }
    },
    focusInput () {
      this.positionSet.value = ''
      this.$refs.input.focus()
    },
    // 保存样本位置接口参数
    setParams (sampleInfo, pos) {
      return {
        fsampleNumber: sampleInfo.fsampleNumber,
        fsampleInnerOrderNumber: this.enterLibraryOrder,
        fsampleAmount: sampleInfo.fsampleAmount,
        fsamplePlace: pos
      }
    },
    async handleSetPos ({ pos, id, sampleType, sampleCode, currentBox, needNext }) {
      const row = this.tableData.find(v => v.fsamplePlace === pos)
      if (row) {
        this.$message.error('该位置已经在表格中设置过，请修改')
        if (row.fsampleNumber !== sampleCode) {
          this.$refs.boxChoose.clearSampleCode([id])
        }
        this.positionSet.value = ''
        return
      }
      const item = this.tableData.find(v => sampleCode === v.fsampleNumber)
      if (!sampleType.includes(item.fsampleType)) {
        await this.$alert(`该位置只能存放${sampleType}`, '提示', {
          confirmButtonText: '确定并清空',
          type: 'error'
        })
        this.$refs.boxChoose.clearSampleCode([id])
        this.positionSet.value = ''
        return
      }
      const params = this.setParams(item, pos)
      const {res} = await awaitWrap(saveSamplePos(params))
      if (res.code && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.$set(item, 'fsamplePlace', pos)
        this.$set(item, 'fsampleStatus', data.fsampleStatus)
        this.$set(item, 'boxPos', { box: currentBox, pos: id })
        this.selectedRows = {}
        this.$refs.table.clearSelection()
        const ref = this.$refs.boxChoose
        const box = ref.findBox(currentBox)
        this.$set(box.holeSampleNumberObj, id, {sampleCode: sampleCode, canInput: true})
        this.$message.success('操作成功')
        await this.handleBoxChange(true)
        if (needNext) {
          this.focusInput()
        }
      } else {
        this.$message.error(res.msg)
        this.$refs.boxChoose.clearSampleCode([id])
        this.positionSet.value = ''
      }
    },
    saveSamplePosition () {
      return new Promise((resolve, reject) => {
        this.$ajax({
          url: '/sample/order/save_sample_place_info',
          method: 'get',
          data: {},
          loadingDom: 'body'
        }).then(res => {
          if (res.code === this.SUCCESS_CODE) {} else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 确认
    handleConfirm () {
      this.$emit('dialogConfirmEvent', this.tableData)
    },
    handleClose () {
      this.visible = false
      this.$emit('dialogCloseEvent')
      if (this.keepLoginTimer) {
        this.clearInterval(this.keepLoginTimer)
        this.keepLoginTimer = null
      }
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-table__header .el-checkbox {
    display: none;
  }
.path{
  & > *{
    margin-bottom: 15px;
  }
  .title{
    font-size: 18px;
    color: #000;
    font-weight: 600;
  }
  .upload{
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
}
.content{
  padding: 20px;
  min-width: 250px;
  .item{
    width: 250px;
    min-width: 200px;
    color: #fff;
    font-size: 14px;
    padding: 5px 10px;
    cursor: pointer;
    & > p{
      line-height: 1.5;
    }
    &:not(:last-child) {
      margin-bottom: 20px;
    }
  }
  .hole-container{
    padding: 10px;
    background: #EBEEF5;
    .hole-row{
      display: flex;
      margin-bottom: 15px;
      $w: 40px;
      .x-text{
        width: $w;
        margin-right: 20px;
        font-size: 14px;
        text-align: center;
      }
      .y-text{
        font-size: 14px;
        margin-right: 10px;
        width: 1em;
        line-height: $w;
      }
      .hole{
        width: $w;
        height: $w;
        background: #ccc;
        border-radius: 50%;
        margin-right: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
