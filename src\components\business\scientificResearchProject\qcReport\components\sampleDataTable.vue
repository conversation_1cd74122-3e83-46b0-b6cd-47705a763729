<template>
  <div>
    <div class="flex search">
      <div>
        <el-button v-if="$setAuthority('017001003', 'buttons')" type="primary" size="mini" @click="handleSetTemplate">生成报告</el-button>
<!--        <el-button type="primary" size="mini" @click="handleSetTemplate">发送报告</el-button>-->
      </div>
      <div class="flex">
        <el-select v-model.trim="searchType"
                   size="mini"
                   style="width: 144px!important"
                   clearable
                   placeholder="请选择">
          <el-option
            :key="index"
            :label="item.label"
            :value="item.value"
            v-for="(item, index) in optionsList">
          </el-option>
        </el-select>
        <el-select
          v-model.trim="searchValue"
          v-if="searchType === 'reportStatus'"
          size="mini"
          style="width: 256px"
          clearable
          placeholder="请选择"
          @keyup.enter.native="getData()">
          <el-option
            :key="index"
            :label="item.label"
            :value="item.value"
            v-for="(item, index) in statusOptions">
          </el-option>
        </el-select>
        <el-input
          v-model.trim="searchValue"
          v-else
          size="mini"
          placeholder="请输入"
          style="width: 256px"
          clearable
          @keyup.enter.native="getData()"></el-input>
      </div>
    </div>

    <el-table
      ref="table"
      :data="tableData"
      class="table detail"
      border
      height="calc(100vh - 65px - 80px - 40px - 20px - 43px - 32px)"
      @select="handleSelect"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="geneCode" label="吉因加编号" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="name" label="样本名称" width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sampleType" label="样本类型" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="extractType" label="提取类型" width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="reportStatusText" label="报告状态" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.rejectNote" class="item" effect="dark" placement="top-start">
            <span>{{scope.row.reportStatusText}}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="reportName" label="报告名称" width="140" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="!scope.row.reportName">{{scope.row.reportName}}</div>
          <el-button v-else type="text" @click="handleAudit(0, scope.row.reportId)">{{ scope.row.reportName}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <audit-report-dialog :pvisible.sync="aVisible" :type="type" :report-id="reportId" @dialogConfirmEvent="getData"></audit-report-dialog>
    <set-template-dialog :pvisible.sync="visible" :sample-ids="ids" @dialogConfirmEvent="getData()"></set-template-dialog>
  </div>
</template>

<script>
import setTemplateDialog from './setTemplateDialog'
import mixins from '../../../../../util/mixins'
import util from '../../../../../util/util'
import AuditReportDialog from './auditReportDialog'
export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    setTemplateDialog,
    AuditReportDialog
  },
  watch: {
    searchType: function (value, oldValue) {
      if (!value) {
        this.searchValue = ''
        this.getData()
      }
    }
  },
  data () {
    return {
      aVisible: false,
      type: '',
      reportId: '',
      searchType: '',
      searchValue: '',
      visible: false,
      ids: '',
      optionsList: [
        {
          label: '吉因加编号',
          value: 'geneSampleNum'
        }, {
          label: '样本名称',
          value: 'sampleName'
        }, {
          label: '样本类型',
          value: 'sampleType'
        }, {
          label: '提取类型',
          value: 'extractType'
        }, {
          label: '报告状态',
          value: 'reportStatus'
        }, {
          label: '报告名称',
          value: 'reportName'
        }
      ],
      statusOptions: [
        {
          label: '待审核',
          value: 0
        },
        {
          label: '审核通过',
          value: 1
        },
        {
          label: '驳回',
          value: 2
        },
        {
          label: '已发送',
          value: 3
        }
      ],
      status: {
        0: '待审核',
        1: '审核通过',
        2: '驳回',
        3: '已发送'
      },
      id: '',
      tableData: [],
      selectedRows: new Map()
    }
  },
  methods: {
    getData (id) {
      if (!id && !this.id) {
        this.$message.error('尚未勾选子订单')
        return
      }
      this.id = id || this.id
      let data = {
        orderId: this.id,
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      if (this.searchType) {
        data = {
          orderId: this.id,
          [this.searchType]: this.searchValue,
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      this.$ajax({
        url: '/order/report/get_sample_list_by_order',
        data: data,
        loadingDom: '.detail'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.records || []
          this.selectedRows = new Map()
          data.forEach((v) => {
            let item = {
              id: v.id,
              geneCode: v.geneCode,
              name: v.name,
              sampleType: v.sampleType,
              extractType: v.extractType,
              reportStatus: v.reportStatus,
              reportStatusText: this.status[v.reportStatus],
              rejectNote: v.rejectNote,
              reportName: v.reportName,
              reportId: v.reportId
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    /**
     * 查看或审核报告
     * @param type 0 查看 1 报告
     * @param reportId
     */
    handleAudit (type, reportId) {
      this.type = type
      this.reportId = reportId
      this.aVisible = true
    },
    handleSetTemplate () {
      if (this.selectedRows.size <= 0) {
        this.$message.error('至少选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()]
      let sampleNames = ''
      let status = false
      row.forEach(v => {
        if (v.reportStatus !== 2 && v.reportStatus !== '-') {
          sampleNames += v.name
          status = true
        }
      })
      if (status) {
        this.$message.error(sampleNames + '已存在待审核/已审核通过的报告，请勿重复勾选！')
        return
      }
      this.ids = row.map(v => v.id).join(',')
      this.visible = true
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped>
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search {
  padding: 10px;
}
</style>
