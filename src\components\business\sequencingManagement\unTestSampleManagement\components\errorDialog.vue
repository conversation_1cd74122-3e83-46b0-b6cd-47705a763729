<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="提示" :width="showTaskCode ? '1000px' : '800px'"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)">
      <template>
        <el-table
          :data="showTableData"
          stripe
          border
          max-height="400px"
        >
          <el-table-column prop="row" label="行数"></el-table-column>
          <el-table-column label="文库/吉因加编号" prop="fsampleName"></el-table-column>
          <el-table-column prop="ferrorContent" label="报错内容"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </template>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
export default {
  name: `errorDialog`,
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showTaskCode () {
      const data = this.tableData[0] || {}
      return data.ftaskCode
    },
    showTableData () {
      return this.tableData.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize + this.pageSize)
    }
  },
  data () {
    return {
      visible: false,
      currentPage: 1,
      pageSize: 30,
      downloadLoading: false,
      totalPage: 0
    }
  },
  methods: {
    handleClose () {
      this.visible = false
      this.tableData = []
      this.downloadLoading = false
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table td, .el-table th{
  padding: 6px 0;
}
.title{
  font-size: 15px;
  font-weight: 600;
  line-height: 40px;
}
</style>
