<template>
  <header>
    <div :class="{'fixed-header': headerFixed}" :style="{width: headerWidth}">
      <div class="header-content">
        <hamburger
          id="hamburger-container"
          :is-active="sidebar.opened"
          class="hamburger-container"
          @toggleClick="toggleSideBar" />
        <div class="header-right">
          <el-button size="mini" type="text" @click="handleClearBrowserCache()">清除缓存</el-button>
          <!-- <div class="area">
            <icon-svg icon-class="icon-dingwei"/>
            <span class="region-text">
              实验室：
              <el-select
                v-model="lab"
                size="mini"
                multiple
                collapse-tags
                @visible-change="handleVisibleChange"
                @change="handleLabChange">
                <el-option
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                  v-for="item in labOptions"></el-option>
              </el-select>
            </span>
          </div> -->
          <div style="display: flex;align-items: center;">
            <el-avatar :size="30">
              {{userInfo.name}}
            </el-avatar>
            <span class="header-text" style="padding: 0 5px;">{{userInfo.name}}</span>
<!--            <i class="el-icon-arrow-down el-icon&#45;&#45;right color-primary"></i>-->
          </div>
        </div>
      </div>
      <path-tabs></path-tabs>
<!--      <div style="height: 10px;"></div>-->
    </div>
    <div style="height: 74px;" v-if="headerFixed"></div>
  </header>
</template>

<script>

// import xx form 'xxx'
import PathTabs from '../PathTabs/index.vue'
import variables from '@/style/variables.scss'
import util from '../../../../util/util'
import Cookies from 'js-cookie'
// import { awaitWrap } from 'Util'
export default {
  name: 'index',
  components: {
    PathTabs
  },
  mounted () {
    this.$nextTick(() => {
      let lab = Cookies.get('flab')
      let labOptions = Cookies.get('labOptions')
      this.labSubmit = lab ? lab.split(',').map(v => { return +v }) : []
      this.lab = util.deepCopy(this.labSubmit)
      util.setSessionInfo('currentLab', this.lab)
      this.labOptions = labOptions ? JSON.parse(labOptions) : []
    })
  },
  computed: {
    sidebar () {
      return this.$store.getters.sidebar || {}
    },
    device () {
      return this.$store.getters.device || 'desktop'
    },
    menuWidth () {
      return this.device === 'desktop' ? (this.sidebar.opened ? variables.sideBarWidth : variables.sideHideBarWidth) : '0px'
    },
    headerWidth () {
      let w = '100%'
      if (this.headerFixed) {
        w = `calc(100% - ${this.menuWidth})`
      }
      return w
    },
    userInfo () {
      return this.$store.getters.userInfo || {}
    }
  },
  data () {
    return {
      lab: '',
      noticeNum: 0,
      labOptions: [],
      headerFixed: true
    }
  },
  methods: {
    toggleSideBar () {
      this.$store.dispatch('app/toggleSideBar')
    },
    handleCommand (command) {
      switch (command) {
        case 'logout':
          this.handleLogout()
          break
        case 'modifyPassword':
          this.showModifyPassword()
      }
    },
    async handleLogout () {
      await this.$confirm('确定退出系统?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    },
    showModifyPassword () {
      this.modifyPasswordDialogVisible = true
    },
    async handleClearBrowserCache (options) {
      const {
        clearLocal = true,
        clearSession = true,
        clearCookies = true,
        reloadAfter = true
      } = options || {}
      console.log('清除缓存选项:', {
        clearLocal,
        clearSession,
        clearCookies,
        reloadAfter
      })
      await this.$confirm('确定清除缓存?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      try {
        // 清理 localStorage
        if (clearLocal) {
          localStorage.clear()
        }

        // 清理 sessionStorage
        if (clearSession) {
          sessionStorage.clear()
        }

        // 清理 cookies
        if (clearCookies) {
          document.cookie.split(';').forEach(cookie => {
            const eqPos = cookie.indexOf('=')
            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
          })
        }

        // 清理缓存后刷新
        if (reloadAfter) {
          window.location.replace(window.location.href)
        }
      } catch (error) {
        console.error('缓存清理失败:', error)
      }
    },

    handleLabChange (val) {
      console.log(1111111)
      this.handleVisibleChange(false)
    },
    handleVisibleChange (show) {
      if (!show) {
        if (this.lab.length === 0) { // 一个都不选的情况
          this.lab = util.deepCopy(this.labSubmit)
        } else { // 选中部分的情况
          // 选中项没变化
          let lab = this.lab.sort()
          if (lab.toString() !== this.labSubmit.toString()) {
            this.refreshPage()
          }
        }
        util.setSessionInfo('currentLab', this.lab)
      }
    },
    refreshPage () {
      this.labSubmit = util.deepCopy(this.lab.sort())
      Cookies.set('flab', this.labSubmit.toString())
      if (this.$route.query.jmoz) {
        let url = location.origin + location.pathname
        window.location.href = url
      } else {
        window.history.go(0)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.fixed-header{
  position: fixed;
  right: 0;
  top: 0;
  z-index: 998;
  transition: width 0.3s;
  .header-content{
    display: flex;
    justify-content: space-between;
    height: 40px;
    background: #fff;
    .hamburger-container {
      line-height: 32px;
      height: 100%;
      cursor: pointer;
      transition: background .3s;
      -webkit-tap-highlight-color:transparent;
      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }
    .header-right{
      display: flex;
      align-items: center;
      height: 100%;
      padding-right: 20px;
      .area{
        font-size: 15px;
        font-weight: 600;
        margin-right: 20px;
      }
      .header-text{
        font-weight: 600;
      }
    }
  }
}
</style>
