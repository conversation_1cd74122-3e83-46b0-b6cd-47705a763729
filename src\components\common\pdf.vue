<template>
  <div style="width: 100%">
    <canvas
      :id="'the_canvas' + page"
      :key="page"
      v-for="page in pdf_pages"
      style="width: 100% !important; margin: auto; display: block"
    ></canvas>
  </div>
</template>
<script>
import PDFJS from 'pdfjs-dist'

export default {
  props: {
    url: {
      type: String
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.showPDF()
    })
  },
  watch: {
    url: function () {
      this.showPDF()
    }
  },
  data () {
    return {
      pdf_pages: [],
      pdfDoc: null,
      pageNum: 1,
      pageRendering: false,
      pageNumPending: null,
      scale: 0.9
    }
  },
  methods: {
    // 获取数据，并渲染
    async showPDF () {
      try {
        let url =
          this.url ||
          (this.$route.query.url && decodeURIComponent(this.$route.query.url))
        if (url) {
          this.pdfDoc = await PDFJS.getDocument({
            url: url,
            cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.2.228/cmaps/',
            cMapPacked: true
          }).promise
          this.pdf_pages = this.pdfDoc.numPages
          this.$nextTick(() => this.renderPage())
        }
      } catch (e) {
        this.pdf_pages = []
      }
    },
    // 分页展示pdf
    async renderPage (num = 1) {
      this.currentPage = num
      // 渲染pdf页
      const page = await this.pdfDoc.getPage(num)
      const canvas = document.getElementById('the_canvas' + num)
      // const canvas = document.getElementById("the_canvas");
      const ctx = canvas.getContext('2d')
      ctx.translate(0.5, 0.5)
      const dpr = window.devicePixelRatio || 1
      const bsr =
        ctx.webkitBackingStorePixelRatio ||
        ctx.mozBackingStorePixelRatio ||
        ctx.msBackingStorePixelRatio ||
        ctx.oBackingStorePixelRatio ||
        ctx.backingStorePixelRatio ||
        1
      const ratio = dpr / bsr
      console.log(ratio)
      const viewport = page.getViewport({scale: 2})
      canvas.width = viewport.width * ratio
      canvas.height = viewport.height * ratio
      canvas.style.width = '800px'
      this.pdf_div_width = viewport.width + 'px'
      canvas.style.height = (viewport.height / viewport.width) * 800 + 'px'
      ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
      const renderContext = {
        canvasContext: ctx,
        viewport: viewport
      }
      page.render(renderContext)
      if (this.pdf_pages > num) {
        setTimeout(() => {
          return this.renderPage(num + 1)
        })
      }
    }
  }
}
</script>

<style scoped>
.pdf-page {
}
</style>
