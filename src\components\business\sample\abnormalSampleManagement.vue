<template>
  <div>
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="80px"  size="mini">
        <el-form-item label="样例编号" prop="sampleCode">
          <el-input v-model.trim="form.sampleCode" clearable @clear="handleSearch"></el-input>
        </el-form-item>
        <el-form-item label="处理状态" prop="patientName">
          <el-select v-model="form.dealStatus" clearable @clear="handleSearch">
            <el-option :value="0" label="未处理"></el-option>
            <el-option :value="1" label="已处理"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <template v-if="false">
          <el-button type="primary" size="mini" @click="handleSampleSplit">处理</el-button>
        </template>
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
      </div>
      <div>
        <el-table
          ref="table"
          :data="tableData"
          height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
          class="reservationTable"
          style="width: 100%"
          @select="handleSelectTable"
          @row-click="handleRowClick">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="sampleCode" label="样例编号" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleStatusText" label="样本状态" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productName" label="产品名称" width="220" show-overflow-tooltip></el-table-column>
          <el-table-column prop="geneplusProjectCode" label="吉因加项目编号" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productionArea" label="生产片区" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tips" label="提示" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="suspensionReason" label="暂停/停测原因" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="oldSampleDate" label="原到样时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="oldDeliverDate" label="原交付时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="newSampleDate" label="新到样时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="newDeliverDate" label="新交付时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dealStatusText" label="处理状态" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dealMethods" label="处理措施" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dealPerson" label="处理人" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dealDate" label="处理时间" width="180" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      title="审批"
      width="30%">
      <label>处理措施：</label>
      <el-select v-model="dialogDealMethod" size="mini">
        <el-option :value="1" label="更新到样时间"></el-option>
        <el-option :value="2" label="不更新到样时间"></el-option>
      </el-select>
      <span slot="footer">
        <el-button size="mini">取消</el-button>
        <el-button size="mini" type="primary">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  name: 'patientInfoDetail',
  mixins: [mixins.tablePaginationCommonData],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        sampleCode: '',
        dealStatus: ''
      },
      tableHeight: 0,
      formSubmit: {}, // 提交的form
      dialogVisible: false,
      dialogDealMethod: ''
    }
  },
  methods: {
    getData () {
      this.$ajax({
        url: '/sample/basic/list_sample_exc',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {
            sampleNum: this.formSubmit.sampleCode,
            sampleExceptionStatus: this.formSubmit.dealStatus
          }
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sample_product_id,
              sampleCode: v.sample_num,
              sampleStatus: v.experiment_status,
              productName: v.product_name,
              geneplusProjectCode: v.fgeneplusProjectCode,
              productionArea: v.productionAreaName,
              tips: v.sample_exception_promp,
              suspensionReason: v.stop_reason,
              oldSampleDate: v.sample_confirm_time,
              oldDeliverDate: v.pre_deliver_date,
              newSampleDate: v.new_sample_confirm_time,
              newDeliverDate: v.new_pre_deliver_date,
              dealStatus: v.sample_exception_status,
              dealMethods: v.sample_exception_remark,
              dealPerson: v.sample_exception_user,
              dealDate: v.sample_exception_time
            }
            let sampleStatusText = {
              0: '待送样',
              1: '已寄送，送样中',
              2: '已到样',
              3: '暂停检测',
              4: '停止检测',
              5: '检测中',
              6: '停止检测-重送样',
              7: '停止检测-已重送样',
              8: '已发报告'
            }
            item.sampleStatusText = sampleStatusText[item.sampleStatus] || '-'
            item.dealStatusText = item.dealStatus === 0 ? '未处理' : item.dealStatus === 1 ? '已处理' : '-'
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // todo 没有功能说明和原型
    handleSampleSplit () {
      this.dialogVisible = true
      this.dialogDealMethod = ''
    },
    handleSearch () {
      this.formSubmit = {...this.form}
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.$refs.form.resetFields()
      this.handleSearch()
    },
    // 点击行
    handleRowClick (row) {
      if (!this.selectedRows.has(row.patientCode)) {
        this.$refs.table.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.patientCode))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.patientCode)
        ? this.selectedRows.delete(row.patientCode)
        : this.selectedRows.set(row.patientCode, row)
    }
  }
}
</script>

<style scoped lang="scss">
  .search{
    background-color: #ffffff;
    height: 60px;
    display: flex;
    align-items: center;
    /deep/ .el-form-item{
      margin-bottom: 0;
    }
  }
  .content{
    background-color: #ffffff;
    .buttonGroup{
      height: 45px;
      display: flex;
      align-items: center;
      margin: 0 20px;
    }
  }
  >>>.el-pagination{
    padding: 7px 2em;
  }
  /deep/ .el-table__header .el-checkbox {
    display: none;
  }
</style>
