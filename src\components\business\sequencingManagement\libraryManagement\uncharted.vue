<template>
  <div class="wrapper">
    <el-form
      ref="form"
      :model="form"
      :inline="true"
      label-width="100px"
      size="mini"
      @keyup.enter.native="handleSearch">
      <el-row>
        <el-col :span="6">
          <el-form-item label="项目管理">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单时间">
           <el-date-picker
             v-model.trim="form.date"
             type="daterange"
             style="width: 200px"
           ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="吉因加编号">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="片段质控完成">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="文库修饰类型">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单编号">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="检测人">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="标签">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="search">
      <el-dropdown>
        <el-button type="primary" size="mini">
          导出<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0">全部导出</el-dropdown-item>
          <el-dropdown-item :command="1">勾选导出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown class="fix-dropdown-margin" @command="handleCommandStock">
        <el-button type="primary" size="mini">
          出入库<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0">样本出库</el-dropdown-item>
          <el-dropdown-item :command="1">样本入库</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button type="danger" class="fix-dropdown-margin" size="mini" @click="handleRegisterException">登记异常</el-button>
      <el-button type="primary" size="mini" @click="handleInfoChange">信息变更</el-button>
      <el-button type="primary" size="mini" @click="handleUploadResult">导入结果</el-button>
      <el-button type="primary" size="mini" @click="handleBackFillResult">回填结果</el-button>
      <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleSearch">重置</el-button>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="120"></el-table-column>
        <el-table-column prop="sampleTime" label="到样时间" width="120"></el-table-column>
        <el-table-column prop="customerSampleNumber" label="客户样本编号" width="120"></el-table-column>
        <el-table-column prop="geneNumber" label="吉因加编号" width="120"></el-table-column>
        <el-table-column prop="libraryModificationType" label="文库修饰类型" width="120"></el-table-column>
        <el-table-column prop="storageLocation" label="存储位置" width="120"></el-table-column>
        <el-table-column prop="concentration" label="浓度(ng/ul)" width="120"></el-table-column>
        <el-table-column prop="volume" label="体积(ul)" width="120"></el-table-column>
        <el-table-column prop="averageFragmentSize" label="平均片段大小" width="120"></el-table-column>
        <el-table-column prop="libraryTotal" label="文库总量(ng)" width="120"></el-table-column>
        <el-table-column prop="qcResult" label="质检结果" width="120"></el-table-column>
        <el-table-column prop="remark" label="备注" width="120"></el-table-column>
        <el-table-column prop="tag" label="标签" width="120"></el-table-column>
        <el-table-column prop="detectionPerson" label="检测人" width="120"></el-table-column>
        <el-table-column prop="detectionTime" label="检测时间" width="120"></el-table-column>
        <el-table-column prop="orderNumber" label="订单编号" width="120"></el-table-column>
        <el-table-column prop="projectNumber" label="项目编号" width="120"></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <!--    样本入库-->
    <sample-warehouse-dialog
      :pvisible.sync="sampleWarehouseDialogVisible"
    />
    <register-unusual-dialog
      :pvisible.sync="registerExceptionVisible"
    />
    <info-change-dialog
      :type="type"
      :pvisible.sync="infoChangeDialogVisible"
    />
    <upload-result-dialog
      :pvisible.sync="uploadResultDialogVisible"
    />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import SampleWarehouseDialog from './components/sampleWarehouseDialog'
import RegisterUnusualDialog from './components/registerExceptionDialog'
import InfoChangeDialog from './components/infoChangeDialog'
import UploadResultDialog from './components/uploadResultDialog'

export default {
  name: 'uncharted',
  mixins: [mixins.tablePaginationCommonData],
  components: {UploadResultDialog, InfoChangeDialog, RegisterUnusualDialog, SampleWarehouseDialog},
  comments: {
    SampleWarehouseDialog
  },
  mounted () {
    this.$_setTbHeight(64 + 60 + 40 + 40 + 15 + 94 + 42 + 42)
  },
  data () {
    return {
      sampleWarehouseDialogVisible: false, // 样本入库弹窗
      registerExceptionVisible: false, // 登记异常弹窗
      infoChangeDialogVisible: false, // 信息变更弹窗
      uploadResultDialogVisible: false, // 导入结果弹窗
      type: '1',
      form: {},
      tableData: []
    }
  },
  methods: {
    handleSearch () {},
    // 选择导出类型
    handleCommand (command) {
      command === 0 ? this.handleExportAll() : this.handleExport()
    },
    // 导出全部
    handleExportAll () {
      if (this.totalPage > 1000) {
        this.$message.error('当前数据过多，请选择报告时间范围或增加筛选条件')
        return
      }
      this.downloadFileLoading = true
      this.$ajax({
        url: '',
        data: {
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadFileLoading = false
      })
    },
    // 导出所选
    handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rows = [...this.selectedRows.values()].map(item => item.realData)
      this.downloadFileLoading = true
      this.$ajax({
        url: '/sample/return/download_sample_return_info',
        data: rows,
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadFileLoading = false
      })
    },
    // 出入库
    handleCommandStock (command) {
      command === 0 ? this.handleOutboundDelivery() : this.handleWarehouse()
    },
    // 样本出库
    async handleOutboundDelivery () {
      const size = this.selectedRows.size
      // 例数
      const sampleCount = 20
      // 实际出库数
      const actualCount = 20
      if (size === 0) {
        this.$message.error('请选择需要出库的样本')
        return
      }
      const message = `勾选样本例数：${size} ，库内例数：${sampleCount}，实际出库例数：<span style="color: red">${actualCount}</span>，确认继续出库？`
      await this.$confirm(message, '样本出库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true
      })
      await this.handleSuccessOutboundDelivery({
        count: 20
      })
    },
    // 出库成功处理
    async handleSuccessOutboundDelivery (data) {
      const message = `本次出库样本例数：${data.count}`
      await this.$confirm(message, '样本出库', {
        confirmButtonText: '下载出库单',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true
      })
    },
    // 样本入库
    handleWarehouse () {
      this.sampleWarehouseDialogVisible = true
    },
    // 异常标记
    handleRegisterException () {
      this.registerExceptionVisible = true
    },
    // 信息变更
    handleInfoChange () {
      this.type = '1'
      this.infoChangeDialogVisible = true
    },
    // 导入结果
    handleUploadResult () {
      this.uploadResultDialogVisible = true
    },
    // 回填结果
    handleBackFillResult () {
      this.type = '2'
      this.infoChangeDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;
  .search {
    margin-bottom: 10px;
  }
}
</style>
