<template>
  <div class="wrapper">
    <!--    操作按钮组-->
    <div class="button-group">
      <el-button :loading="loading" v-if="!isPreview" type="primary" size="mini" @click="handleCreateReport">生成word报告</el-button>
    </div>
    <!--    内容区-->
    <div class="pathogens-list-wrapper">
      <el-tabs v-model="currentComponent"
               tab-position="left"
               style="height: 100%;">
        <el-tab-pane label="基本信息" name="basicInfo"></el-tab-pane>
        <el-tab-pane label="临床信息" name="clinicalInfo"></el-tab-pane>
        <el-tab-pane label="检测小结" name="testSummary"></el-tab-pane>
        <el-tab-pane label="检测结果列表" name="testResult"></el-tab-pane>
        <el-tab-pane label="病原体解释" name="pathogenInterpretation"></el-tab-pane>
        <el-tab-pane label="样本质控" name="sampleQuality"></el-tab-pane>
      </el-tabs>
      <div class="components-wrapper">
        <components :is="currentComponent"></components>
      </div>
      <choose-template-dialog :pvisible.sync="visible"></choose-template-dialog>
    </div>
  </div>
</template>

<script>
import basicInfo from './basicInfo' // 基本信息
import clinicalInfo from './clinicInfo' // 临床信息
import testSummary from './testSummary' // 检测小结
import testResult from './testResult' // 检测结果列表
import pathogenInterpretation from './pathogenInterpretation' // 病原体解释
import sampleQuality from './sampleQuality'
import chooseTemplateDialog from './componemts/chooseTemplateDialog' // 报告模版选择弹出
export default {
  components: {
    basicInfo,
    clinicalInfo,
    testSummary,
    testResult,
    pathogenInterpretation,
    sampleQuality,
    chooseTemplateDialog
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    isPreview () {
      return this.$store.getters.getValue('isPreview') || this.$route.query.isPreview
    }
  },
  data () {
    return {
      currentComponent: 'basicInfo',
      loading: false,
      visible: false,
      tableData: []
    }
  },
  methods: {
    async handleCreateReport () {
      this.visible = true
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-descriptions-item__cell {
  width: 120px;
}
.wrapper {
  .button-group {
    padding: 20px 10px;
  }
  .pathogens-list-wrapper {
    display: flex;
    height: calc(100vh - 232px);
    background: #fff;
    .components-wrapper {
      padding: 20px;
      width: calc(100% - 124px);
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
