import Axios from 'axios'
// import qs from 'qs'
import Cookies from 'js-cookie'
import constants from './constants'
import util from '../util/util'
import { Loading, Message } from 'element-ui'

const fileAxios = Axios.create({})

function fileAjax ({
  url,
  baseURL = constants.JS_CONTEXT,
  data = {},
  loadingDom,
  loadingObject
}) {
  let flab = Cookies.get('flab') ? Cookies.get('flab') : '0'
  let id = util.getSessionInfo('loginId')
  let config = {
    headers: {
      // 'Content-Type': 'multipart/form-data',
      'Content-Type': 'multipart/form-data',
      'user-id': id ? util.decryptBase64(id) : '1',
      flabNo: flab
    }
  }
  let formData = new FormData()
  for (let k in data) {
    let d = (data[k] === undefined || data[k] === null) ? '' : data[k]
    formData.append(k, d)
  }
  let loading
  if (loadingDom) {
    let obj = {}
    if (loadingObject && typeof loadingObject === 'object') obj = loadingObject
    loading = Loading.service({
      target: loadingDom,
      lock: true,
      ...obj
    })
  }
  return fileAxios.post(baseURL + url, formData, config).then(function (res) {
    // return Promise.resolve(res)
    return res.data
  }).catch(function (err) {
    Message({
      message: err.message || err.error || '错误',
      type: 'error'
    })
  }).finally(() => {
    if (loadingDom) {
      loading.close()
    }
  })
}

export default {fileAjax}
