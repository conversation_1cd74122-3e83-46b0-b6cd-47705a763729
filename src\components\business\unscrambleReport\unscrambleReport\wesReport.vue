<template>
  <div class="page">
    <div class="header">
      <div class="reportInfo">
        <el-row style="width: 100%;">
         <el-col :span="6">
           <div class="item">
             <el-avatar icon="el-icon-user-solid"></el-avatar>
             <span>{{sampleInfo.sampleCode}}</span>
             <span>{{isSingleSample === '1' ? '双样本' : '单样本'}}（{{sampleInfo.sampleType}}）</span>
           </div>
         </el-col>
         <!--<el-col :span="6">-->
           <!--<div class="item">-->
             <!--<span>检出癌种: {{sampleInfo.detectCancer}}</span>-->
           <!--</div>-->
         <!--</el-col>-->
         <el-col :span="6">
           <div class="item">
             <span>解读匹配癌种: {{getMatchCancers()}}</span><i class="el-icon-edit" style="cursor: pointer;" @click="handleEditMatchCancer"></i>
           </div>
         </el-col>
          <el-col :span="6">
            <div class="item">
              <span>样本癌种类型: {{sampleInfo.sampleCancerType}}</span>
            </div>
          </el-col>
         <el-col :span="6">
           <div class="item">
             <i class="el-icon-document"></i><el-button type="text" style="margin-left: 10px;" @click="handleOpenPatientDetail">患者详情</el-button>
           </div>
         </el-col>
        </el-row>
      </div>
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClickTab">
          <el-tab-pane label="检出基因变异" name="wesReportDetectionOfGeneVariation"></el-tab-pane>
          <el-tab-pane label="数据筛选" name="wesReportDataFilter"></el-tab-pane>
          <el-tab-pane label="解读结果" name="wesReportUnscrambleResult"></el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div class="components">
      <!--<keep-alive>-->
      <component :is="currentComponent"></component>
      <!--</keep-alive>-->
    </div>
    <wes-report-patient-info-dialog
      :pvisible="patientInfoVisible"
      :pdata="patientInfo"
      @patientInfoDialogCloseEvent="handlePatientInfoDialogClose"
    ></wes-report-patient-info-dialog>
    <wes-report-edit-match-cancer-dialog
      :pvisible="editMatchCancerVisible"
      :pdata="editMatchCancerData"
      @editMatchCancerDialogConfirmEvent="handleEditMatchCancerDialogConfirm"
      @editMatchCancerDialogCloseEvent="handleEditMatchCancerDialogClose"
    ></wes-report-edit-match-cancer-dialog>
  </div>
</template>

<script>
import wesReportEditMatchCancerDialog from './wesReportEditMatchCancerDialog'
import wesReportPatientInfoDialog from './wesReportPatientInfoDialog'
import wesReportUnscrambleResult from './wesReportUnscrambleResult'
import wesReportDataFilter from './wesReportDataFilter'
import wesReportDetectionOfGeneVariation from './wesReportDetectionOfGeneVariation'
export default {
  name: 'wesReport',
  components: {
    wesReportUnscrambleResult,
    wesReportDataFilter,
    wesReportDetectionOfGeneVariation,
    wesReportPatientInfoDialog,
    wesReportEditMatchCancerDialog
  },
  props: [],
  created () {
    this.getData()
  },
  watch: {},
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      activeName: 'wesReportDetectionOfGeneVariation',
      currentComponent: 'wesReportDetectionOfGeneVariation',
      sampleCode: '',
      sample: '',
      sampleType: '',
      cancer: '',
      detectCancer: '',
      matchCancerName: [],
      sampleInfo: {
        sampleCode: '',
        sampleType: '',
        detectCancer: '',
        result: ''
      },
      patientInfo: {
        patientName: '',
        age: '',
        sex: '',
        detectCancer: '',
        familyHistory: '',
        pipelineName: '',
        chip: '',
        clientName: '',
        sendTime: '',
        sendUnit: ''
      },
      isSingleSample: '',
      patientInfoVisible: false,
      editMatchCancerVisible: false,
      editMatchCancerData: []
    }
  },
  methods: {
    handleClickTab (tab, event) {
      let name = tab.name
      if (name !== this.currentComponent) {
        this.currentComponent = name
      }
    },
    handleEditMatchCancer () {
      this.editMatchCancerData = []
      this.matchCancerName.forEach(v => {
        this.editMatchCancerData.push({cancer: v.cancerTypeName, cancerId: v.cancerTypeId})
      })
      this.editMatchCancerVisible = true
    },
    handleEditMatchCancerDialogConfirm () {
      this.editMatchCancerVisible = false
      this.getData()
    },
    handleEditMatchCancerDialogClose () {
      this.editMatchCancerVisible = false
    },
    getData () {
      this.$store.commit({
        type: 'old/setValue',
        category: 'analysisRsId',
        analysisRsId: this.$route.query.oxym
      })
      this.$ajax({
        url: '/read/unscramble/get_sample_patient_info',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.matchCancerName = data.matchCancerName
          this.$store.commit({
            type: 'oldsetValue',
            category: 'matchCancer',
            matchCancer: data.matchCancerName
          })
          this.patientInfo = {
            patientName: data.patientInfo.fpatientName,
            age: data.patientInfo.fage,
            sex: data.patientInfo.fsex,
            detectCancer: data.patientInfo.fdetectCancer,
            familyHistory: data.patientInfo.ffamilyHistory,
            pipelineName: data.patientInfo.fpipelineName,
            chip: data.patientInfo.fchip,
            clientName: data.patientInfo.fclientName,
            sendTime: data.patientInfo.fsendTime,
            sendUnit: data.patientInfo.fsendUnit
          }
          this.sampleInfo = {
            sampleCode: data.sampleInfo.fsampleCode,
            sampleType: data.sampleInfo.fsampleType,
            sampleCancerType: data.sampleInfo.sampleCancerType,
            detectCancer: data.sampleInfo.fdetectCancer,
            result: data.sampleInfo.fresult
          }
          this.isSingleSample = data.isSingleSample
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleOpenPatientDetail () {
      this.patientInfoVisible = true
    },
    handlePatientInfoDialogClose () {
      this.patientInfoVisible = false
    },
    getMatchCancers () {
      let cancerName = this.matchCancerName.map(v => { return v.cancerTypeName })
      return cancerName.join(',')
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    .header{
      font-size: 14px;
      padding: 0 20px;
      height: 100px;
      >>>.el-tabs__header {
        margin: 0;
      }
      .reportInfo{
        height: 61px;
        display: flex;
        align-items: center;
        .item{
          height: 44px;
          display: flex;
          align-items: center;
          & > span{
            margin-right: 10px;
          }
        }
      }
    }
    .components{
      height: calc(100vh - 100px - 40px);
     >>>.el-table thead{
        font-size: 14px;
        font-weight: 500;
        color: #909399;
      }
      >>>.el-table th{
        background-color: rgba(0, 0, 0, 0);
      }
    }
  }
</style>
