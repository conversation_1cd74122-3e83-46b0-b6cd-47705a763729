# LIMS 前端开发文档

## 项目概述

LIMS（Laboratory Information Management System）是一个实验室信息管理系统的前端项目，基于 Vue.js 2.x 开发，主要用于管理实验室的样本、测序、订单、容器等业务流程。

### 技术栈
- **框架**: Vue.js 2.5.2
- **UI组件库**: Element UI 2.15.14
- **路由**: Vue Router 3.0.1
- **状态管理**: Vuex 3.0.1
- **构建工具**: Webpack 3.6.0
- **样式预处理**: Sass/SCSS
- **HTTP客户端**: Axios 0.18.0
- **表格组件**: VXE Table 3.7.7, Handsontable
- **图表库**: ECharts 4.7.0
- **其他**: Element UI扩展组件、PDF.js、Lottie动画等

## 项目结构

```
lims_frontend/
├── build/                 # 构建配置文件
├── config/                # 环境配置
├── src/
│   ├── api/               # API接口定义
│   │   ├── basicDataManagement/     # 基础数据管理API
│   │   ├── dataMonitoringManagement/ # 数据监控管理API
│   │   ├── deliveryManagement/      # 交付管理API
│   │   ├── enterLibraryManagement/  # 入库管理API
│   │   ├── sample/                  # 样本管理API
│   │   ├── sampleLibraryManagement/ # 样本库管理API
│   │   ├── sequencingManagement/    # 测序管理API
│   │   └── system/                  # 系统管理API
│   ├── assets/            # 静态资源
│   ├── components/        # 组件
│   │   ├── business/      # 业务组件
│   │   ├── common/        # 公共组件（自动全局注册）
│   │   ├── layout/        # 布局组件
│   │   └── system/        # 系统组件
│   ├── directives/        # 自定义指令
│   ├── plugins/           # 插件
│   ├── router/            # 路由配置
│   ├── store/             # Vuex状态管理
│   ├── style/             # 全局样式
│   ├── util/              # 工具函数
│   ├── App.vue            # 根组件
│   ├── main.js            # 入口文件
│   └── settings.js        # 系统设置
├── static/                # 静态文件
├── package.json           # 项目依赖
└── README.md             # 项目说明
```

## 核心业务模块

### 1. 样本管理模块
**路径**: `src/components/business/sample/`

**主要功能**:
- 样本信息管理 (`clinicalInfoManagement.vue`)
- 样本签收管理 (`sampleSigningManagement.vue`)
- 快递签收管理 (`expressDeliveryManagement.vue`)
- 返样管理 (`returnSampleManagement.vue`)
- 异常样本管理 (`abnormalSampleManagement.vue`)

**核心API**:
```javascript
// 样本基础API
import { getSampleLogs } from '@/api/sample/sample'

// 样本库管理API
import { fixPosition, getSampleBySampleCode, signOperate } from '@/api/sampleLibraryManagement/sampleSearch'
```

### 2. 测序管理模块
**路径**: `src/components/business/sequencingManagement/`

**主要功能**:
- 待测样本管理 (`unTestSampleManagement/`)
- 排单任务管理 (`scheduleTaskManagement/`)
- Pooling管理 (`pooling/`)
- 转化管理 (`translation/`)
- 环化管理 (`cyclization/`)
- MakeDNB管理 (`makeDNB/`)
- 异常处理 (`errorManagement/`)

**核心API**:
```javascript
// 测序管理主API
import { 
  getTaskList, 
  saveTaskList, 
  returnTask, 
  addSample, 
  getTaskDetail 
} from '@/api/sequencingManagement/sequencingManagementApi'

// 各流程专用API
import { getUnTestSampleList, setRemark, signSample } from '@/api/sequencingManagement/unTestSampleApi'
import { getMakeDNBList, downloadTask } from '@/api/sequencingManagement/makeDNBApi'
import { getCyclizationList } from '@/api/sequencingManagement/cyclizationApi'
import { getTransformList } from '@/api/sequencingManagement/transformApi'
```

### 3. 订单管理模块
**路径**: `src/components/business/orderManagement/`

**主要功能**:
- 订单审核 (`orderReview/`)
- 订单看板 (`orderKanban/`)
- 异常样本处理 (`sampleAbnormal/`)

### 4. 容器管理模块
**路径**: `src/components/business/containerManagement/`

**主要功能**:
- 容器概览 (`overview.vue`)
- 容器详情 (`containerDetail.vue`)
- 创建容器 (`createContainer.vue`)

### 5. 交付管理模块
**路径**: `src/components/business/deliveryManagement/`

**主要功能**:
- 交付订单管理 (`index.vue`)
- 交付配置管理 (`deliveryConfigManagement/`)

**核心API**:
```javascript
import { 
  getDeliverOrderList, 
  getSubDeliverOrderList, 
  exportOrderData, 
  updateDeliverStatus 
} from '@/api/deliveryManagement/index'
```

### 6. 样本库管理模块
**路径**: `src/components/business/sampleLibraryManagement/`

**主要功能**:
- 样本查询 (`sampleSearch.vue`)
- 出入库管理 (`turnoverLibraryManagement.vue`)
- 出入库申请 (`applicationForStorage.vue`)
- 异常样本管理 (`abnormalSampleManagement.vue`)
- 返样管理 (`backSampleManagement.vue`)

### 7. 基础数据管理模块
**路径**: `src/components/business/basicDataManagement/`

**主要功能**:
- 工序流程管理 (`processFlowManagement.vue`)
- 工序步骤管理 (`processStepManagement.vue`)
- 对照标准管理 (`controlStandardManagement.vue`)
- 在线探针管理 (`onlineProbeManagement.vue`)
- 探针看板 (`probeKanban.vue`)

### 8. 数据监控管理模块
**路径**: `src/components/business/dataMonitoringManagement/`

**主要功能**:
- 样本监控 (`smapleMonitoringManagement/`)
- 加测订单管理 (`repeatClinicManagement/`)

**核心API**:
```javascript
import { getSampleMonitoringList, exportSampleMonitoring } from '@/api/dataMonitoringManagement/smapleMonitoringManagementApi'
import { getAddTestList, auditAddTestInfo } from '@/api/sequencingManagement/repeatClinicManagementApi'
```

### 9. 单细胞管理模块
**路径**: `src/components/business/singleCell/`

**主要功能**:
- 解离管理 (`dissociation/`)
- 建库管理 (`buildLib/`)
- 样本监控 (`smapleMonitoringManagement/`)
- 数据交付 (`deliveryManagement/`)

### 10. 物料管理模块
**路径**: `src/components/business/materials/`

**主要功能**:
- 物料库存管理 (`materialInventoryManagement.vue`)
- 宣传品库存管理 (`publicityInventoryManagement.vue`)
- 物料申请管理 (`materialApplicationManagement.vue`)
- 发货管理 (`deliveryManagement.vue`)

## 技术特性

### 1. 组件自动注册
```javascript
// main.js - 公共组件自动全局注册
const globalCom = require.context('./components/common', false, /\.vue$/)
globalCom.keys().forEach(item => {
  let componentConfig = globalCom(item)
  let name = item.substring(2, item.indexOf('.vue'))
  Vue.component(
    name.charAt(0).toUpperCase() + name.slice(1),
    componentConfig.default
  )
})
```

### 2. 自定义指令
```javascript
// 全局指令注册
for (let key in Directives) {
  let { name, f } = Directives[key]
  Vue.directive(name, f)
}
```

**可用指令**:
- `v-drag-dialog`: 拖拽对话框
- `v-focus`: 自动聚焦
- `v-form-loading`: 表单加载状态
- `v-lazy-load`: 懒加载

### 3. 插件系统
- `ErrorDialog`: 错误提示对话框
- `SampleDetailDialog`: 样本详情对话框
- `sequencingErrorDialog`: 测序错误对话框
- `downLoadChooseDialog`: 下载选择对话框

### 4. 权限控制
```javascript
Vue.prototype.$setAuthority = function (code, type = 'modules') {
  let $myresource = Vue.prototype.$myresource || {
    modules: [],
    menus: [],
    buttons: []
  }
  return $myresource[type].indexOf(code) > -1 || constants.IS_TEST
}
```

### 5. HTTP请求封装
```javascript
// 使用封装的ajax方法
Vue.prototype.$ajax = myAjax.myAjax
Vue.prototype.$fileAjax = fileAjax.fileAjax

// 在组件中使用
this.$ajax({
  url: '/api/endpoint',
  method: 'post',
  data: params,
  loadingDom: '.table'
}).then(res => {
  if (res.code === this.SUCCESS_CODE) {
    // 处理成功响应
  }
})
```

## 开发规范

### 1. 组件开发
- 公共组件放置在 `components/common` 目录下，会自动全局注册
- 业务组件按照业务模块分类，放置在 `components/business` 相应目录下
- 组件命名采用 PascalCase 规范
- 组件 props 定义需包含类型和默认值

```javascript
// 推荐的组件props写法
props: {
  title: {
    type: String,
    default: ''
  },
  list: {
    type: Array,
    default: () => []
  }
}
```

### 2. API接口
- API按业务模块分类，放置在 `api` 目录下相应子目录中
- 接口定义遵循RESTful规范
- 使用封装的 `myAjax` 和 `fileAjax` 方法处理HTTP请求

```javascript
// API调用示例
import { getSampleList } from '@/api/sample/sampleManagement'

// 在组件methods中使用
methods: {
  async fetchSampleList() {
    try {
      const res = await getSampleList(params)
      if (res.code === this.SUCCESS_CODE) {
        this.sampleList = res.data
      }
    } catch (error) {
      console.error(error)
    }
  }
}
```

### 3. 路由配置
- 使用 Vue Router 进行路由管理
- 支持路由懒加载
- 路由守卫处理权限验证和页面标题设置

```javascript
// 路由懒加载示例
{
  path: '/sampleManagement',
  component: () => import('@/components/business/sample/sampleManagement.vue')
}
```

### 4. 状态管理
- 使用 Vuex 进行全局状态管理
- 按模块划分 store
- 提供 getters 便于组件访问状态

## 部署配置

### 开发环境
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

### 测试环境构建
```bash
npm run build:t
```

### 配置文件
- 开发环境: `config/dev.env.js`
- 生产环境: `config/prod.env.js`
- 运行时配置: `static/config.json`

```json
{
  "IP": "*************",
  "PORT": "2325",
  "PROJECT": "lims",
  "PROTOCOL": "http",
  "IS_TEST": true,
  "PROTOCOL_WEBSOCKET": "ws",
  "ITSM": "https://itsm.geneplus.org.cn",
  "iframeUrl": "http://*************:9900"
}
```

## 具体业务流程

### 1. 样本生命周期管理

#### 样本接收流程
1. **快递签收** (`expressDeliveryManagement.vue`)
   - 快递信息录入和管理
   - 样本到达确认
   - 异常快递处理

2. **样本签收** (`sampleSigningManagement.vue`)
   - 样本基本信息验证
   - 样本状态更新
   - 签收记录生成

3. **样本入库** (`sampleLibraryManagement/`)
   - 容器分配和位置管理
   - 样本存储信息记录
   - 库存状态更新

#### 样本处理流程
1. **临床信息管理** (`clinicalInfoManagement.vue`)
   - 患者基本信息录入
   - 临床病理信息管理
   - 家族史和用药史记录
   - 影像学检查结果
   - 免疫组化信息

2. **样本质控**
   - 样本质量评估
   - 核酸浓度检测
   - 样本合格性判断

3. **异常样本处理** (`abnormalSampleManagement.vue`)
   - 异常原因分类
   - 处理方案制定
   - 重新采样申请

### 2. 测序工作流程

#### 前处理阶段
1. **待测样本管理** (`unTestSampleManagement/`)
   - 样本排队管理
   - 优先级设置
   - 批次规划

2. **排单任务管理** (`scheduleTaskManagement/`)
   - 任务单生成
   - 工作量分配
   - 进度跟踪

#### 实验流程
1. **Pooling** (`pooling/`)
   - 样本混合比例计算
   - 文库浓度标准化
   - 质控检测

2. **转化** (`translation/`)
   - 文库转化处理
   - 转化效率监控
   - 质量评估

3. **环化** (`cyclization/`)
   - 环化反应条件设置
   - 反应进度监控
   - 产物质量检测

4. **MakeDNB** (`makeDNB/`)
   - DNB制备参数设置
   - 制备过程监控
   - 质量控制检测

#### 上机测序
1. **测序任务分配**
   - 测序仪资源调度
   - 测序参数配置
   - 运行状态监控

2. **异常处理** (`errorManagement/`)
   - 测序异常识别
   - 故障诊断分析
   - 重测方案制定

### 3. 订单管理流程

#### 订单生命周期
1. **订单接收**
   - 订单信息录入
   - 产品类型识别
   - 客户信息验证

2. **订单审核** (`orderReview/`)
   - 订单完整性检查
   - 技术可行性评估
   - 价格和交期确认

3. **订单执行**
   - 样本分配和调度
   - 实验流程执行
   - 进度跟踪管理

4. **订单交付** (`deliveryManagement/`)
   - 结果数据整理
   - 报告生成和审核
   - 交付方式选择

### 4. 科技服务流程

#### 科服订单处理 (`technologyService/`)
1. **订单类型分类**
   - Illumina文库订单
   - MGI文库订单
   - 组织/核酸订单
   - 单细胞订单

2. **订单审核流程**
   - 技术方案评估
   - 资源可用性检查
   - 风险评估分析

3. **样本管理** (`sampleConfirm/`)
   - 样本接收确认
   - 质量评估
   - 存储管理

4. **样本处理** (`sampleManagement/`)
   - 预处理流程
   - 质控检测
   - 进度跟踪

### 5. 单细胞分析流程

#### 单细胞实验流程 (`singleCell/`)
1. **解离** (`dissociation/`)
   - 组织解离参数设置
   - 细胞活性检测
   - 细胞计数和质控

2. **建库** (`buildLib/`)
   - 单细胞捕获
   - 文库构建
   - 质量评估

3. **样本监控** (`smapleMonitoringManagement/`)
   - 实验进度跟踪
   - 质控数据监控
   - 异常处理

4. **数据交付** (`deliveryManagement/`)
   - 数据质控分析
   - 结果文件整理
   - 交付确认

### 6. 容器和存储管理

#### 容器管理流程 (`containerManagement/`)
1. **容器创建** (`createContainer.vue`)
   - 容器类型选择
   - 规格参数设置
   - 位置编码分配

2. **容器配置**
   - 楼层设置 (`createContainerSetFloorDialog.vue`)
   - 货架配置 (`createContainerSetShelfDialog.vue`)
   - 盒子管理 (`createContainerSetBoxDialog.vue`)
   - 孔位分配 (`createContainerSetHoleDialog.vue`)

3. **存储管理**
   - 样本位置分配
   - 库存状态跟踪
   - 容量使用监控

### 7. 物料管理流程

#### 物料库存管理 (`materials/`)
1. **库存管理**
   - 物料入库 (`materialInventoryManagement.vue`)
   - 宣传品管理 (`publicityInventoryManagement.vue`)
   - 库存盘点

2. **申请流程**
   - 物料申请 (`materialApplicationManagement.vue`)
   - 审批流程
   - 发放记录

3. **发货管理** (`deliveryManagement.vue`)
   - 发货计划制定
   - 物流信息跟踪
   - 签收确认

## 数据流转和状态管理

### 1. 样本状态流转
```
接收 → 签收 → 入库 → 质控 → 实验 → 结果 → 交付 → 归档
```

### 2. 订单状态流转
```
创建 → 审核 → 执行 → 完成 → 交付 → 结算
```

### 3. 任务状态流转
```
待分配 → 已分配 → 执行中 → 已完成 → 已验收
```

## 关键技术实现

### 1. 表格组件使用

#### VXE Table 高级表格
```javascript
// 在组件中使用VXE Table
<vxe-table
  ref="table"
  :data="tableData"
  :loading="loading"
  border
  stripe
  height="400"
  @checkbox-change="handleSelectionChange">
  <vxe-column type="checkbox" width="60"></vxe-column>
  <vxe-column field="sampleCode" title="样本编号"></vxe-column>
  <vxe-column field="sampleName" title="样本名称"></vxe-column>
</vxe-table>
```

#### Handsontable 电子表格
```javascript
// Handsontable配置
import { HotTable } from '@handsontable/vue'

data() {
  return {
    hotSettings: {
      data: this.tableData,
      colHeaders: ['样本编号', '样本名称', '状态'],
      columns: [
        { data: 'sampleCode', type: 'text' },
        { data: 'sampleName', type: 'text' },
        { data: 'status', type: 'dropdown', source: ['正常', '异常'] }
      ],
      licenseKey: 'non-commercial-and-evaluation'
    }
  }
}
```

### 2. 文件上传和下载

#### 文件上传
```javascript
// 使用Element UI上传组件
<el-upload
  class="upload-demo"
  :action="uploadUrl"
  :on-success="handleUploadSuccess"
  :on-error="handleUploadError"
  :before-upload="beforeUpload">
  <el-button size="small" type="primary">点击上传</el-button>
</el-upload>

methods: {
  beforeUpload(file) {
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    if (!isExcel) {
      this.$message.error('只能上传Excel文件!')
      return false
    }
    return true
  },
  handleUploadSuccess(response) {
    if (response.code === this.SUCCESS_CODE) {
      this.$message.success('上传成功')
      this.getData()
    }
  }
}
```

#### 文件下载
```javascript
// 文件下载处理
async downloadFile(params) {
  try {
    const res = await this.$ajax({
      url: '/api/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })

    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '导出文件.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    this.$message.error('下载失败')
  }
}
```

### 3. 图表可视化

#### ECharts图表
```javascript
// 引入ECharts
import echarts from 'echarts'

mounted() {
  this.initChart()
},

methods: {
  initChart() {
    const chart = echarts.init(this.$refs.chart)
    const option = {
      title: { text: '样本统计' },
      xAxis: { type: 'category', data: ['正常', '异常', '待处理'] },
      yAxis: { type: 'value' },
      series: [{
        data: [120, 200, 150],
        type: 'bar'
      }]
    }
    chart.setOption(option)
  }
}
```

### 4. 权限控制实现

#### 按钮级权限控制
```javascript
// 在模板中使用权限控制
<el-button
  v-if="$setAuthority('SAMPLE_DELETE')"
  @click="handleDelete"
  type="danger">
  删除
</el-button>

// 在方法中检查权限
methods: {
  handleOperation() {
    if (!this.$setAuthority('SAMPLE_EDIT')) {
      this.$message.error('无操作权限')
      return
    }
    // 执行操作
  }
}
```

### 5. 异步数据处理

#### 使用async/await处理异步请求
```javascript
// 推荐的异步数据处理方式
async getData() {
  try {
    this.loading = true
    const [sampleRes, orderRes] = await Promise.all([
      this.getSampleList(),
      this.getOrderList()
    ])

    if (sampleRes.code === this.SUCCESS_CODE) {
      this.sampleList = sampleRes.data
    }

    if (orderRes.code === this.SUCCESS_CODE) {
      this.orderList = orderRes.data
    }
  } catch (error) {
    this.$message.error('数据加载失败')
  } finally {
    this.loading = false
  }
}
```

## 性能优化策略

### 1. 组件懒加载
```javascript
// 路由级别的懒加载
const SampleManagement = () => import('@/components/business/sample/sampleManagement.vue')

// 组件级别的懒加载
components: {
  AsyncComponent: () => import('./AsyncComponent.vue')
}
```

### 2. 表格虚拟滚动
```javascript
// 大数据量表格优化
<vxe-table
  :data="tableData"
  :scroll-y="{enabled: true, gt: 100}"
  height="400">
</vxe-table>
```

### 3. 图片懒加载
```javascript
// 使用自定义指令实现图片懒加载
<img v-lazy-load="imageUrl" alt="样本图片">
```

### 4. 防抖和节流
```javascript
// 搜索防抖
import { debounce } from 'lodash'

methods: {
  handleSearch: debounce(function(keyword) {
    this.searchKeyword = keyword
    this.getData()
  }, 300)
}
```

## 错误处理和日志

### 1. 全局错误处理
```javascript
// main.js中配置全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  // 发送错误日志到服务器
}

// HTTP请求错误处理
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 处理未授权
      router.push('/login')
    }
    return Promise.reject(error)
  }
)
```

### 2. 操作日志记录
```javascript
// 记录用户操作日志
methods: {
  async handleCriticalOperation() {
    try {
      await this.performOperation()
      this.logOperation('SAMPLE_DELETE', { sampleId: this.currentSample.id })
    } catch (error) {
      this.logError('OPERATION_FAILED', error)
    }
  },

  logOperation(action, data) {
    this.$ajax({
      url: '/system/log/operation',
      method: 'post',
      data: { action, data, timestamp: Date.now() }
    })
  }
}
```

## 测试策略

### 1. 单元测试
```javascript
// 使用Jest进行组件测试
import { shallowMount } from '@vue/test-utils'
import SampleList from '@/components/business/sample/SampleList.vue'

describe('SampleList.vue', () => {
  it('renders sample list correctly', () => {
    const wrapper = shallowMount(SampleList, {
      propsData: {
        samples: [
          { id: 1, name: 'Sample 1', status: 'normal' }
        ]
      }
    })
    expect(wrapper.find('.sample-item').exists()).toBe(true)
  })
})
```

### 2. 端到端测试
```javascript
// 使用Cypress进行E2E测试
describe('Sample Management', () => {
  it('should create new sample', () => {
    cy.visit('/business/view/clinicalInfoManagement')
    cy.get('[data-cy=add-sample-btn]').click()
    cy.get('[data-cy=sample-name-input]').type('Test Sample')
    cy.get('[data-cy=save-btn]').click()
    cy.contains('保存成功').should('be.visible')
  })
})
```

## 部署和运维

### 1. Docker部署
```dockerfile
# Dockerfile
FROM node:14-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 环境配置管理
```javascript
// 环境变量配置
const config = {
  development: {
    API_BASE_URL: 'http://localhost:3000',
    DEBUG: true
  },
  production: {
    API_BASE_URL: 'https://api.lims.com',
    DEBUG: false
  }
}

export default config[process.env.NODE_ENV]
```

### 3. 监控和告警
```javascript
// 性能监控
const performanceObserver = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.duration > 1000) {
      // 发送性能告警
      console.warn('Slow operation detected:', entry.name, entry.duration)
    }
  }
})
performanceObserver.observe({ entryTypes: ['measure'] })
```

## 项目改进建议

### 1. 技术栈升级
**当前问题**:
- Vue 2.x 版本较旧，缺少 Composition API 等新特性
- Webpack 3.x 构建速度较慢
- Element UI 版本较旧，部分组件性能不佳

**改进建议**:
```javascript
// 升级到 Vue 3 + Vite
// package.json
{
  "vue": "^3.3.0",
  "vite": "^4.0.0",
  "@vitejs/plugin-vue": "^4.0.0"
}

// 使用 Composition API 重构组件
import { ref, reactive, computed, onMounted } from 'vue'

export default {
  setup() {
    const tableData = ref([])
    const loading = ref(false)

    const filteredData = computed(() => {
      return tableData.value.filter(item => item.status === 'active')
    })

    onMounted(() => {
      fetchData()
    })

    return {
      tableData,
      loading,
      filteredData
    }
  }
}
```

### 2. 代码质量改进
**当前问题**:
- 缺少 TypeScript 类型检查
- 组件复用性不高
- 代码规范不统一

**改进建议**:
```typescript
// 引入 TypeScript
interface SampleData {
  id: number
  sampleCode: string
  sampleName: string
  status: 'normal' | 'abnormal' | 'pending'
  createTime: string
}

// 定义通用接口类型
interface ApiResponse<T> {
  code: string
  message: string
  data: T
}

// 组件 Props 类型定义
interface SampleListProps {
  samples: SampleData[]
  loading?: boolean
  onSelect?: (sample: SampleData) => void
}
```

### 3. 状态管理优化
**当前问题**:
- Vuex 使用不够规范
- 组件间通信复杂
- 缺少持久化存储

**改进建议**:
```javascript
// 使用 Pinia 替代 Vuex
import { defineStore } from 'pinia'

export const useSampleStore = defineStore('sample', {
  state: () => ({
    samples: [],
    currentSample: null,
    loading: false
  }),

  getters: {
    normalSamples: (state) => state.samples.filter(s => s.status === 'normal'),
    sampleCount: (state) => state.samples.length
  },

  actions: {
    async fetchSamples() {
      this.loading = true
      try {
        const response = await api.getSamples()
        this.samples = response.data
      } finally {
        this.loading = false
      }
    }
  },

  persist: true // 自动持久化
})
```

### 4. 组件架构优化
**当前问题**:
- 组件职责不清晰
- 重复代码较多
- 缺少组件文档

**改进建议**:
```javascript
// 创建可复用的业务组件
// components/business/common/SampleTable.vue
<template>
  <div class="sample-table">
    <div class="table-header">
      <slot name="header" :total="total">
        <h3>样本列表 ({{ total }})</h3>
      </slot>
    </div>

    <vxe-table
      :data="samples"
      :loading="loading"
      @selection-change="handleSelectionChange">
      <vxe-column type="checkbox" v-if="selectable"></vxe-column>
      <vxe-column
        v-for="column in columns"
        :key="column.field"
        :field="column.field"
        :title="column.title">
      </vxe-column>
      <vxe-column title="操作" v-if="$slots.actions">
        <template #default="{ row }">
          <slot name="actions" :row="row"></slot>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  name: 'SampleTable',
  props: {
    samples: { type: Array, default: () => [] },
    loading: { type: Boolean, default: false },
    selectable: { type: Boolean, default: true },
    columns: { type: Array, required: true }
  },
  computed: {
    total() {
      return this.samples.length
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    }
  }
}
</script>
```

### 5. 性能监控和优化
**当前问题**:
- 缺少性能监控
- 大数据量渲染卡顿
- 内存泄漏风险

**改进建议**:
```javascript
// 性能监控工具
class PerformanceMonitor {
  static measureComponent(componentName, fn) {
    const start = performance.now()
    const result = fn()
    const end = performance.now()

    if (end - start > 100) {
      console.warn(`Component ${componentName} took ${end - start}ms to render`)
    }

    return result
  }

  static trackMemoryUsage() {
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize } = performance.memory
      const usage = (usedJSHeapSize / totalJSHeapSize) * 100

      if (usage > 80) {
        console.warn(`High memory usage: ${usage.toFixed(2)}%`)
      }
    }
  }
}

// 在组件中使用
export default {
  mounted() {
    PerformanceMonitor.measureComponent('SampleList', () => {
      this.initializeComponent()
    })
  },

  beforeDestroy() {
    // 清理定时器和事件监听器
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.eventListener) {
      window.removeEventListener('resize', this.eventListener)
    }
  }
}
```

### 6. 安全性增强
**当前问题**:
- XSS 攻击防护不足
- 敏感数据暴露风险
- 权限控制粒度粗糙

**改进建议**:
```javascript
// XSS 防护
import DOMPurify from 'dompurify'

// 过滤用户输入
methods: {
  sanitizeInput(input) {
    return DOMPurify.sanitize(input)
  },

  // 安全的 HTML 渲染
  renderSafeHTML(html) {
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: ['b', 'i', 'em', 'strong'] })
  }
}

// 敏感数据处理
const sensitiveFields = ['password', 'token', 'secret']

function sanitizeLogData(data) {
  const sanitized = { ...data }
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '***'
    }
  })
  return sanitized
}
```

### 7. 国际化支持
**改进建议**:
```javascript
// 添加 vue-i18n
import VueI18n from 'vue-i18n'

const messages = {
  zh: {
    sample: {
      list: '样本列表',
      add: '添加样本',
      edit: '编辑样本'
    }
  },
  en: {
    sample: {
      list: 'Sample List',
      add: 'Add Sample',
      edit: 'Edit Sample'
    }
  }
}

const i18n = new VueI18n({
  locale: 'zh',
  messages
})

// 在组件中使用
<template>
  <div>
    <h1>{{ $t('sample.list') }}</h1>
    <el-button>{{ $t('sample.add') }}</el-button>
  </div>
</template>
```

## 总结

LIMS前端项目是一个功能完整的实验室信息管理系统，涵盖了样本管理、测序管理、订单管理等核心业务流程。项目采用Vue.js 2.x技术栈，具有良好的模块化结构和组件复用性。

**项目优势**:
1. 业务功能完整，覆盖实验室全流程管理
2. 组件化程度高，代码结构清晰
3. 权限控制完善，支持细粒度权限管理
4. 表格组件功能强大，支持复杂数据展示
5. 文件上传下载功能完善

**改进空间**:
1. 技术栈相对较旧，建议升级到Vue 3 + Vite
2. 缺少TypeScript类型检查，代码健壮性有待提升
3. 性能监控和错误处理机制需要完善
4. 组件复用性和文档化程度可以进一步提高
5. 安全性和国际化支持需要加强

通过持续的技术升级和架构优化，该项目可以更好地满足现代化实验室管理的需求，提供更好的用户体验和系统稳定性。
```
