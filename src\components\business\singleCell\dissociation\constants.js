// 初步审核质控结果项
export const qualityResultItems = [
  { label: '细胞悬液浓度', field: 'fcellSuspensionConcentration' },
  { label: '细胞总量', field: 'fcellTotal' },
  { label: '细胞活性%', field: 'fcytoactive' },
  { label: '结团率%', field: 'fcoalescenceRate' },
  { label: '有核细胞占比%', field: 'fproportionNucleatedCell' },
  { label: '碎片情况', field: 'ffragmentationCondition' },
  { label: '样本描述', field: 'fsampleDescribe' },
  { label: '检测结论', field: 'ftestSummary' },
  { label: '是否上机', field: 'fisEmbarkation' },
  { label: '是否去死细胞', field: 'fisDieCell' },
  { label: '是否去碎片', field: 'fisDefragment' },
  { label: '实验备注', field: 'fexperimentRemark' }
]
// 上机前质控结果项
export const beforeQualificationItems = [
  { label: '上机前-细胞悬液浓度(μl)', field: 'fbeforeBoardSuspensionConcentration' },
  { label: '上机前-细胞/细胞核总量', field: 'fbeforeBoardCount' },
  { label: '上机前-细胞活性/核完整性', field: 'fbeforeBoardCytocative' },
  { label: '上机前有核占比%', field: 'fbeforeBoardProportionNucleated' },
  { label: '上机前-细胞直径μm', field: 'fbeforeBoardDiameter' },
  { label: '上机前-结团率%', field: 'fbeforeBoardCakeRate' },
  { label: '上机前碎片情况', field: 'fbeforeBoardFragmentation' },
  { label: '样本描述', field: 'fsampleDescribe' },
  { label: '检测结论', field: 'ftestSummary' },
  { label: '实验备注', field: 'fexperimentRemark' }
]

export const beforeRequiredField = [
  // 上机前-细胞悬液浓度、上机前-细胞活性/核完整性、上机前-结团率%
  'fbeforeBoardSuspensionConcentration'
]

export const requiredField = [
  'fcellSuspensionConcentration'
]
