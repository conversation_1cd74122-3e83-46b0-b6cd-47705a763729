import Vue from 'vue'
import axios from 'axios'
import constants from '@/util/constants'
import util from '@/util/util'

/**
 * 全局路由守卫
 * @param {*} router Vue Router实例
 */
export default function setupGuards (router) {
  router.beforeEach((to, from, next) => {
    let title = ''
    let meta = to.meta || {}
    if (meta.title) {
      title = ' - ' + meta.title
    }
    document.title = 'lims' + title
    if (to.query.jmoz) {
      let id = to.query.jmoz.replace(/\$/g, '=')
      util.setSessionInfo('jmoz', id)
    }
    if (to.meta.pageTitle) document.title = to.meta.pageTitle
    if (Vue.prototype.$myresource === undefined) {
      Vue.prototype.$myresource = util.getSessionInfo('resource') || {
        modules: [],
        menus: [],
        buttons: []
      }
    }
    axios({
      url: '/static/config.json'
    }).then(function (result) {
      if (window.localStorage) {
        window.localStorage.setItem('IP', result.data.IP)
        window.localStorage.setItem('PORT', result.data.PORT)
        window.localStorage.setItem('PROTOCOL', result.data.PROTOCOL)
        window.localStorage.setItem('PROTOCOL_WEBSOCKET', result.data.PROTOCOL_WEBSOCKET)
        window.localStorage.setItem('PROJECT', result.data.PROJECT)
        constants.ITSM = result.data.ITSM
        constants.IFRAME_URL = result.data.iframeUrl
        constants.IS_TEST = result.data.IS_TEST
        constants.JS_CONTEXT = window.localStorage.getItem('PROTOCOL') + '://' +
          window.localStorage.getItem('IP') + ':' +
          window.localStorage.getItem('PORT') + '/' +
          window.localStorage.getItem('PROJECT')
        next()
      } else {
        alert('当前浏览器不支持localStorage，请升级版本')
      }
    }).catch(function (err) {
      alert(err)
    })
  })
}
