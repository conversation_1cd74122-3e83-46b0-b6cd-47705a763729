# LIMS前端系统

## 项目概述

LIMS（实验室信息管理系统）前端是一个基于Vue.js开发的Web应用，用于管理实验室的样本、流程、数据和报告等信息。系统提供从样本接收到结果交付的全流程管理，支持灵活的配置与扩展，满足不同实验室的个性化需求。

## 技术栈

- 前端框架：Vue.js 2.5.2
- UI框架：Element UI 2.15.14
- 状态管理：Vuex 3.0.1
- 路由管理：Vue Router 3.0.1
- HTTP请求：Axios 0.18.0
- 表格组件：vxe-table 3.7.7
- 日期处理：dayjs 1.10.4
- 图表库：echarts 4.7.0
- 构建工具：Webpack 3.6.0

## 功能特点

### 样本管理
- 样本接收与登记
- 样本信息管理（患者信息、临床信息等）
- 异常样本处理
- 返样管理

### 样本库存管理
- 出入库管理（入库、出库、移库）
- 容器管理（容器创建、配置、使用状态监控）
- 位置管理（样本位置跟踪与管理）

### 测序管理
- 样本上传与任务创建
- 流程处理（Pooling、转化、环化、makeDNB等）
- 结果回填
- 测序异常管理

### 基础数据管理
- 工序流程管理（流程模板、步骤配置）
- 控制标准管理
- 报告小结配置

### 交付管理
- 数据交付管理
- 报告生成与交付
- 交付流程跟踪

## 系统架构

```
src/
├── api/               // API接口目录，按业务模块划分
│   ├── basicDataManagement/    // 基础数据管理API
│   ├── deliveryManagement/     // 交付管理API
│   ├── sample/                 // 样本管理API
│   ├── sampleLibraryManagement/  // 样本库存管理API
│   ├── sequencingManagement/   // 测序管理API
│   └── system/                 // 系统管理API
├── assets/            // 静态资源
├── components/        // 组件目录
│   ├── business/      // 业务组件
│   ├── common/        // 公共组件（自动全局注册）
│   ├── layout/        // 布局组件
│   └── system/        // 系统组件
├── directives/        // 自定义指令
├── plugins/           // 插件
│   ├── errorDialog/            // 错误弹窗插件
│   ├── sampleDetailDialog/     // 样本详情弹窗插件
│   └── sequencingErrorDialog/  // 测序错误弹窗插件
├── router/            // 路由配置
├── store/             // Vuex状态管理
├── style/             // 样式文件
├── util/              // 工具函数
│   ├── ajax.js        // AJAX请求封装
│   ├── constants.js   // 常量定义
│   ├── fileAjax.js    // 文件上传请求封装
│   └── util.js        // 通用工具函数
├── App.vue            // 根组件
├── main.js            // 入口文件
└── settings.js        // 系统配置
```

## 业务流程

### 样本全生命周期管理流程
1. 样本接收：样本登记 → 基本信息录入 → 临床信息关联 → 接收确认
2. 样本入库：入库申请 → 位置分配 → 入库操作 → 入库确认
3. 样本处理：样本出库 → 实验流程处理（按工序流程执行）→ 结果回填
4. 样本测序：测序任务创建 → 流程配置 → 测序操作 → 测序数据回填
5. 数据分析：分析任务创建 → 分析执行 → 分析结果审核
6. 结果交付：报告生成 → 报告审核 → 数据包装 → 交付客户

### 异常处理流程
1. 异常发现：异常样本标记 → 异常登记 → 异常级别确定
2. 异常处理：处理方案制定 → 处理操作执行 → 处理结果记录
3. 异常确认：处理结果确认 → 异常关闭 / 返样处理

## 安装与使用

### 开发环境要求
- Node.js >= 6.0.0
- npm >= 3.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产环境
```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build:t
```

### 代码检查
```bash
npm run lint
```

## 权限控制

系统采用基于角色的权限控制，支持三级权限：
- 模块级权限：控制用户可访问的功能模块
- 操作级权限：控制用户在模块内可执行的操作（新增、修改、删除等）
- 数据级权限：控制用户可查看和操作的数据范围

### 权限使用方法
```javascript
// 在组件中判断权限
this.$setAuthority('code', 'modules') // 判断是否有模块权限
this.$setAuthority('code', 'menus')   // 判断是否有菜单权限
this.$setAuthority('code', 'buttons') // 判断是否有按钮操作权限

// 在模板中使用指令
<button v-auth="'button:add'">新增</button>
```

## 开发规范

### 命名规范
- 文件夹命名：采用小驼峰命名法，如 `sampleManagement`
- 组件命名：采用 PascalCase（大驼峰）命名法，如 `SampleDetail.vue`
- 变量命名：采用小驼峰命名法，如 `sampleData`
- 常量命名：采用全大写下划线分隔，如 `MAX_COUNT`

### 组件开发
- 公共组件放置在 `components/common` 目录下，会自动全局注册
- 业务组件按照业务模块分类，放置在 `components/business` 相应目录下
- 组件命名采用 PascalCase 规范
- 组件 props 定义需包含类型和默认值

```javascript
// 推荐的组件props写法
props: {
  title: {
    type: String,
    default: ''
  },
  list: {
    type: Array,
    default: () => []
  }
}
```

### API接口
- API按业务模块分类，放置在 `api` 目录下相应子目录中
- 接口定义遵循RESTful规范
- 使用封装的 `myAjax` 和 `fileAjax` 方法处理HTTP请求

```javascript
// API调用示例
import { getSampleList } from '@/api/sample/sampleManagement'

// 在组件methods中使用
methods: {
  async fetchSampleList() {
    try {
      const res = await getSampleList(params)
      if (res.code === this.SUCCESS_CODE) {
        this.sampleList = res.data
      }
    } catch (error) {
      console.error(error)
    }
  }
}
```

### 路由配置
- 路由配置在 `router/index.js` 中定义
- 业务路由放置在 `/business` 路径下
- 路由元信息中包含权限和标题等配置

```javascript
// 路由配置示例
{
  path: 'sampleSearch',
  meta: {
    title: '样本查询'
  },
  component: () => import('@/components/business/sampleLibraryManagement/sampleSearch.vue')
}
```

### 插件使用
系统包含多个自定义插件，使用方式如下：

```javascript
// 错误弹窗
this.$errorDialog.show({
  title: '错误提示',
  content: '操作失败，请重试'
})

// 样本详情弹窗
this.$sampleDetailDialog.show({
  sampleId: 'S20220501001'
})

// 测序错误弹窗
this.$sequencingErrorDialog.show({
  taskId: 'T20220501001'
})
```

### VXE-Table使用规范
VXE-Table是系统中使用的高性能表格组件，使用时应遵循以下规范：

```html
<vxe-table
  :data="tableData"
  :loading="loading"
  border
  stripe
  highlight-hover-row
  height="auto"
  @cell-click="handleCellClick">
  <vxe-column type="seq" width="60"></vxe-column>
  <vxe-column field="name" title="名称"></vxe-column>
  <vxe-column field="code" title="编号"></vxe-column>
  <vxe-column field="status" title="状态">
    <template v-slot="{ row }">
      <el-tag :type="row.status === '1' ? 'success' : 'danger'">
        {{ row.status === '1' ? '正常' : '异常' }}
      </el-tag>
    </template>
  </vxe-column>
</vxe-table>
```

## 部署与环境配置


### Nginx配置示例
```nginx
server {
    listen       80;
    server_name  lims.example.com;
    
    location / {
        root   /usr/share/nginx/html/lims;
        index  index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend-server:8080/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 常见问题

1. **为什么环化环节必须使用两步法？**
   - 根据实验要求，环化环节必须采用两步法处理，系统会在流程配置时自动验证并提示。

2. **如何配置新的工序流程模板？**
   - 进入"基础数据管理/工序流程管理"，点击"新增"按钮创建模板，然后配置工序步骤和参数。

3. **如何处理异常样本？**
   - 在"样本库存管理/异常样本管理"中，可以登记异常样本，选择处理方式，记录处理结果。

4. **表格数据导出失败怎么处理？**
   - 检查导出数据量是否过大（建议单次不超过5000条）
   - 确认浏览器是否拦截了下载操作
   - 检查网络连接是否正常

5. **如何启用开发模式下的模拟数据？**
   - 在 `util/constants.js` 中设置 `IS_TEST = true`
   - 重启开发服务器生效

## 版本管理与迭代计划

### 当前版本
- 版本号：v1.0.0
- 发布日期：2023-05-01
- 主要功能：样本管理、库存管理、测序管理、基础数据管理、交付管理

### 下一版本规划
- 版本号：v1.1.0
- 计划发布：2023-08-01
- 计划功能：
  - 数据可视化大屏展示
  - 批量导入导出优化
  - 测序流程自动化推进
  - 报告生成流程优化

