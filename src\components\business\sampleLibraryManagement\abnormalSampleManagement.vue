<template>
  <div>
    <div class="search-form">
      <el-form :model="form" size="mini" label-width="80px" inline style="display: flex;">
        <el-form-item label="样本单号">
          <el-input v-model.trim="formInput.sampleCode" size="mini" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model.trim="formInput.time"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleResetForm">重置</el-button>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%;"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      class="table"
      @filter-change="handleFilterTable">
      <el-table-column type="index" width="70" label="序号"></el-table-column>
      <el-table-column
        :filters="progressFilterOptions"
        :filtered-value="progressFilterValues" prop="progress"
        label="进度"
        width="100"
        column-key="progressStatus"></el-table-column>
      <el-table-column prop="sampleCode" label="样本编号" min-width="180"></el-table-column>
      <el-table-column label="申请单号" width="180" prop="applicationOrder">
        <template slot-scope="scope">
          <el-button type="text" @click="handleShowModifyDialog(scope.row.applicationOrder)">{{scope.row.applicationOrder}}</el-button>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="lab" label="所属实验室" width="140"></el-table-column>
      <el-table-column show-overflow-tooltip prop="applicant" label="申请人" width="100"></el-table-column>
      <el-table-column show-overflow-tooltip prop="applicationTime" label="申请时间" width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="operator" label="操作人" width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="estimatedTime" label="完成时间" width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="notes" label="备注" width="180"></el-table-column>
      <el-table-column show-overflow-tooltip label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <template v-if="$setAuthority('011006001', 'buttons')">
            <el-button
              :disabled="scope.row.progress !== '未处理'" type="text"
              @click="handleShowAbnormalSampleDealDialog(scope.row.id, scope.row.applicationOrder)">处理</el-button>
          </template>
          <el-button
            type="text"
            @click="handleShowSampleLogDialog(scope.row.id)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <modify-order-dialog
      :pvisible="modifyOrderDialogVisible"
      :order-id="currentApplicationOrder"
      title="订单详情"
      page="sampleSearch"
      @dialogCloseEvent="modifyOrderDialogVisible = false" />
    <sample-log-dialog
      :pvisible="sampleLogDialogVisible"
      :sample-id="currentSampleId"
      @dialogCloseEvent="sampleLogDialogVisible = false"/>
    <abnormal-sample-deal-dialog
      :pvisible="abnormalSampleDealDialogVisible"
      :sample-id="currentSampleId"
      :order-num="currentOrderNum"
      @dialogCloseEvent="abnormalSampleDealDialogVisible = false"
      @dialogConfirmEvent="handleAbnormalSampleDealDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import modifyOrderDialog from './modifyOrderDialog'
import sampleLogDialog from './sampleLogDialog'
import abnormalSampleDealDialog from './abnormalSampleDealDialog'
import util from '../../../util/util'
export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    modifyOrderDialog,
    sampleLogDialog,
    abnormalSampleDealDialog
  },
  mounted () {
    this.getData()
  },
  data () {
    return {
      form: {
        sampleCode: '',
        time: []
      },
      formInput: {
        sampleCode: '',
        time: []
      },
      currentApplicationOrder: '',
      modifyOrderDialogVisible: false,
      currentSampleId: '',
      currentOrderNum: '', // 当前的申请单号
      sampleLogDialogVisible: false,
      abnormalSampleDealDialogVisible: false,
      progressFilterOptions: [
        {text: '未处理', value: '未处理'},
        {text: '已处理', value: '已处理'}
      ],
      progressFilterValues: []
    }
  },
  methods: {
    getData () {
      let time = this.form.time || []
      this.$ajax({
        url: '/sample/get_exception_sample_info_list',
        data: {
          sampleNumber: this.form.sampleCode,
          startDate: time[0],
          endDate: time[1],
          fexceptionStatus: this.progressFilterValues,
          pageRequest: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              progress: v.fexceptionStatus,
              sampleCode: v.fsampleNumber,
              lab: v.flab,
              applicationOrder: v.orderNumber,
              applicant: v.applicatUser,
              operator: v.operationUser,
              applicationTime: v.foutboundApplicantTime,
              estimatedTime: v.exceptionCompleteTime,
              notes: v.fnotes
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.form.sampleCode = this.formInput.sampleCode
      this.form.time = this.formInput.time
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.formInput = {
        order: '',
        time: []
      }
      this.handleSearch()
    },
    // 筛选事件触发
    handleFilterTable (val) {
      if (val.progressStatus) {
        this.progressFilterValues = []
        val.progressStatus.forEach(item => {
          this.progressFilterValues.push(item)
        })
        this.handleSearch()
      }
    },
    // 展示单号详情
    handleShowModifyDialog (orderId) {
      this.currentApplicationOrder = orderId
      this.modifyOrderDialogVisible = true
    },
    // 展示样本日志弹窗
    handleShowSampleLogDialog (sampleId) {
      this.currentSampleId = sampleId
      this.sampleLogDialogVisible = true
    },
    // 展示处理异常样本弹窗
    handleShowAbnormalSampleDealDialog (sampleId, orderNum) {
      this.currentSampleId = sampleId
      this.currentOrderNum = orderNum
      this.abnormalSampleDealDialogVisible = true
    },
    // 处理功能回调
    handleAbnormalSampleDealDialogConfirm () {
      this.abnormalSampleDealDialogVisible = false
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
  .form-container{
    display: flex;
    justify-content: space-between;
    margin: 20px 0 0 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #ccc;
    .form{
      display: flex;
      .form-item{
        display: flex;
        align-items: center;
        margin-right: 20px;
        label{
          width: 5em;
          flex-shrink: 0;
        }
      }
    }
  }
</style>
