<template>
  <div style="display: flex">
    <el-upload
      ref="upload"
      :action="uploadUrl"
      :file-list="fileList"
      :headers="headers"
      :on-success="handleOnSuccess"
      :on-error="handleOnError"
      :on-progress="handleProgress"
      :before-upload="handleBeforeUpload"
      :on-preview="handlePictureCardPreview"
      :before-remove="handleRemove"
      :on-remove="() => handleOnSuccess()"
      :on-change="() => handleChange()"
      multiple
      accept="image/jpg,image/jpeg,image/png,zip,rar"
      list-type="picture-card"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <div v-if="fileList.length > 3" class="box">
      剩余{{fileList.length - 3}}张不做展示
    </div>
    <el-dialog :visible.sync="dialogVisible" title="图片预览" append-to-body>
      <img :src="dialogImageUrl" width="100%" alt="">
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import constants from '@/util/constants'

export default {
  model: {
    prop: 'fileList',
    event: 'change'
  },
  props: {
    fileList: Array
  },

  data () {
    return {
      headers: {
        token: Cookies.get('token')
      },
      files: [],
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  methods: {
    // 提交前的函数
    handleBeforeUpload (file) {
      let name = file.name
      let uploadFiles = this.$refs.upload.uploadFiles || [{size: 0}]
      if (uploadFiles.length > 100) {
        this.$message.error('一次性上传的文件数量超过系统限制，不允许上传')
        return
      }
      let {size} = uploadFiles.reduce((pre, next) => {
        let size = pre.size + next.size
        return {size: size}
      }, {size: 0})
      if (size > 1024 * 1024 * 50) {
        this.$message.error('一次性上传的文件大小超过系统限制，不允许上传')
        return false
      }
      if (!/\.(jpg|png|jpeg)$/.test(name)) {
        this.$message.error('只能上传jpg、png、jpeg格式文件')
        return false
      }
      if (file.size > 1024 * 1024 * 30) {
        this.$message.error('文件不能大于30M，请重新上传')
        return false
      }
      return true
    },
    setAttachFile (file) {
      let files = []
      let uploadFiles = this.$refs.upload.uploadFiles || [{size: 0}]
      uploadFiles.forEach(v => {
        if (v.response) {
          v.response.data.url = v.response.data.absolutePath
          v.response.data.size = v.size
          files.push(v.response.data)
        } else {
          v.url = v.absolutePath
          files.push(v)
        }
      })
      // 文件排序
      files.sort((pre, next) => {
        // 获取文件名称
        let sortByPreName = pre.name || pre.originalFileName
        let sortByNextName = next.name || next.originalFileName
        // 去除文件名称
        sortByPreName = sortByPreName.split('.')[0] || ''
        sortByNextName = sortByNextName.split('.')[0] || ''
        // 获取文件名最后三位
        sortByPreName = sortByPreName.substring(sortByPreName.length - 3, sortByPreName.length)
        sortByNextName = sortByNextName.substring(sortByNextName.length - 3, sortByNextName.length)
        // 按 - 切割字符传
        let sortByPre = sortByPreName.split('-') || []
        let sortByNext = sortByNextName.split('-') || []
        if (sortByPre.length < 2) {
          return 1
        }
        // next next非数字后排
        if (sortByNext.length < 2) {
          return -1
        }
        // 转数字 => 非数字 = NaN
        const sortPre = sortByPre[sortByPre.length - 1] * 1
        const sortNext = sortByNext[sortByNext.length - 1] * 1
        // pre pre非数字后排
        if (isNaN(sortPre)) {
          return 1
        }
        // next next非数字后排
        if (isNaN(sortNext)) {
          return -1
        }
        return sortPre - sortNext
      })
      return files
    },
    handleChange () {
      // let file = this.setAttachFile()
      // if (file) {
      //   this.$emit('change', file)
      // }
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.onProgress = false
      let files = this.setAttachFile(file)
      if (files) {
        this.$emit('change', files)
      }
      // if (res && res.code === this.SUCCESS_CODE) {
      //   let fileData = {}
      //   fileData.url = res.data.absolutePath
      //   fileData.group = res.data.group
      //   fileData.path = res.data.path
      //   fileData.name = res.data.originalFileName
      //   this.fileList.push(fileData)
      //   console.log(this.fileList, res.data, fileData)
      // }
    },
    async handleConfirmOption (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    async handleRemove (file) {
      this.onProgress = false
      if (file.status !== 'ready') {
        await this.handleConfirmOption('是否删除？')
      }
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
>>>.el-upload-list__item:nth-child(n + 4) {
  display: none !important;
}
>>> .el-upload--picture-card {
  margin: 0 8px 8px 0;
  float: left;
}
>>> .el-dialog__body {
  overflow: hidden;
}
.box {
  display: flex;
  padding: 30px;
  align-items: center;
  justify-content: center;
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  font-size: 18px;
  color: #c0ccda;
  cursor: pointer;
  text-align: center;
  vertical-align: top;
}
</style>
