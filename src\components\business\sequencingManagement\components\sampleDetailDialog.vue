<template>
  <el-dialog
    v-drag-dialog
    :close-on-click-modal="false"
    :visible.sync="visible"
    :before-close="handleClose"
    :title="title"
    width="600px"
    @open="handleOpen">
    <el-table
      ref="table"
      :data="tableData"
      class="table detail"
      size="mini"
      border
      height="50vh"
      style="width: 100%"
    >
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column label="项目名称" prop="projectName" min-width="120" show-overflow-tooltip/>
      <el-table-column label="实验样本 " prop="sampleName" min-width="120" show-overflow-tooltip/>
    </el-table>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {
  getSampleFlowDetailList, getSampleQcDetailList
} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'sampleDetailDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    title: {
      type: String,
      default: '查看样本详情'
    },
    taskId: {
      type: Number,
      default: null
    },
    workFlow: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async handleOpen () {
      if (this.workFlow === 0) {
        this.getQcCompleteList()
        return
      }
      const workFlowMap = {
        1: 1,
        2: 2,
        3: 4,
        4: 5,
        5: 1
      }
      let findex = ''
      if (this.workFlow === 1) findex = 1
      if (this.workFlow === 5) findex = 2
      const {res} = await awaitWrap(getSampleFlowDetailList({
        fid: this.taskId,
        fworkflowId: workFlowMap[this.workFlow],
        findex: findex
      }, {loadingDom: '.detail'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.tableData = []
        data.forEach(v => {
          const item = {
            fid: v.fid,
            projectName: v.fprojectName,
            sampleName: v.fsampleName
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 获取文库质控完成列表
    async getQcCompleteList () {
      const {res} = await awaitWrap(getSampleQcDetailList({
        fid: this.taskId
      }, {loadingDom: '.detail'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.tableData = []
        data.forEach(v => {
          const item = {
            fid: v.fid,
            projectName: v.fprojectName,
            sampleName: v.fsampleName
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
