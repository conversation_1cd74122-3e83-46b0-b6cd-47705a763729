<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"  :visible.sync="visible"
      :before-close="handleClose"
      title="修改产品工艺流程"
      width="70%">
      <div>
        <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="120px" label-suffix=":">
          <el-row>
           <el-col :span="12">
             <el-form-item label="工序名称">
               {{form.stepName}}
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="工序分类">
               {{form.stepTypeName}}
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="送样类型" prop="sendSampleType">
               <el-select v-model="form.sendSampleType" clearable multiple placeholder="请选择" style="width: 100%;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in sampleTypeList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="样本类型" prop="sampleType">
               <el-select v-model="form.sampleType" clearable multiple placeholder="请选择" style="width: 100%;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in sampleTypeList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="可用样本类型" prop="useSampleType">
               <el-select v-model="form.useSampleType" clearable multiple placeholder="请选择" style="width: 100%;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in sampleTypeList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="TAT" prop="tatTime">
               <el-input v-model.number="form.tatTime" maxlength="20" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="是否继承" prop="isInherit">
               <el-select v-model="form.isInherit" placeholder="请选择" style="width: 100%;">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in isInheritList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="12">
             <el-form-item label="权重序号" prop="weights">
               <el-input v-model.number="form.weights" maxlength="20" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'processFlowManagementSaveProcessDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.form = Object.assign({}, this.form, this.pdata)
        this.getSampleType()
      } else {
        this.tableData = []
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      form: {
        stepName: '',
        stepType: '',
        stepTypeName: '',
        stepCode: '',
        tatTime: '',
        sendSampleType: '',
        sendSampleTypeName: '',
        sampleType: '',
        sampleTypeName: '',
        useSampleType: '',
        useSampleTypeName: '',
        weights: '',
        isInherit: ''
      },
      sampleTypeList: [],
      isInheritList: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      rules: {
        sendSampleType: [
          {required: true, message: '请选择送样类型', trigger: ['blur', 'change']}
        ],
        sampleType: [
          {required: true, message: '请选择样本类型', trigger: ['blur', 'change']}
        ],
        useSampleType: [
          {required: true, message: '请选择可用样本类型', trigger: ['blur', 'change']}
        ],
        tatTime: [
          {required: true, message: '请选择TAT时间', trigger: 'blur'}
        ],
        isInherit: [
          {required: true, message: '请选择样是否继承', trigger: ['blur', 'change']}
        ],
        weights: [
          {required: true, message: '请输入权重序号', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    getSampleType () {
      this.$ajax({
        url: '/system/procedure/list_sample_type',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.sampleTypeList = []
          result.data.forEach(v => {
            this.sampleTypeList.push({
              label: v.dictValue,
              value: v.dictUniqueVal
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleClose () {
      this.$emit('processFlowManagementProcessSaveDialogCloseEvent')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let newRow = {
            stepName: this.form.stepName,
            stepType: this.form.stepType,
            stepTypeName: this.form.stepTypeName,
            stepCode: this.form.stepCode,
            tatTime: this.form.tatTime,
            sendSampleType: this.form.sendSampleType,
            sendSampleTypeName: [],
            sampleType: this.form.sampleType,
            sampleTypeName: [],
            useSampleType: this.form.useSampleType,
            useSampleTypeName: [],
            weights: this.form.weights,
            isInherit: this.form.isInherit
          }
          this.sampleTypeList.forEach(v => {
            if (newRow.sendSampleType.indexOf(v.value) !== -1) {
              newRow.sendSampleTypeName.push(v.label)
            }
            if (newRow.sampleType.indexOf(v.value) !== -1) {
              newRow.sampleTypeName.push(v.label)
            }
            if (newRow.useSampleType.indexOf(v.value) !== -1) {
              newRow.useSampleTypeName.push(v.label)
            }
          })
          newRow.sendSampleType = newRow.sendSampleType.join(',')
          newRow.sendSampleTypeName = newRow.sendSampleTypeName.join(',')
          newRow.sampleType = newRow.sampleType.join(',')
          newRow.sampleTypeName = newRow.sampleTypeName.join(',')
          newRow.useSampleType = newRow.useSampleType.join(',')
          newRow.useSampleTypeName = newRow.useSampleTypeName.join(',')
          this.$emit('processFlowManagementProcessSaveDialogConfirmEvent', newRow)
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
