<template>
  <el-dialog
    title="批量修改"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @opened="handleOpen">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-position="right"
      label-suffix=":"
      label-width="80px"
      class="demo-ruleForm">
      <div class="tips">
        已勾选<span style="color: red;">{{ ids.length }}</span>条数据
      </div>
      <el-form-item label="异常类型" prop="exceptionType">
        <el-select v-model="form.exceptionType" size="mini" clearable class="input-wrapper" placeholder="请选择">
          <el-option v-for="item in exceptionTypeList"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-if="form.exceptionType" label="标记备注" prop="note">
        <el-input v-model="form.note" type="textarea" size="mini" maxlength="50" clearable placeholder="请输入"></el-input>
      </el-form-item>

    </el-form>

    <span slot="footer" class="dialog-footer">
      <!-- 对话框操作按钮 -->
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import mixins from '@/util/mixins'
import {awaitWrap} from '@/util/util'
import {exceptionTypeList} from '@/components/business/singleCell/buildLib/dataFormate'
import {batchModifyBuildStatus} from '@/api/sequencingManagement/singleCell'
export default {
  name: 'batchModifyingDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    }
  },

  data () {
    return {
      form: {
        exceptionType: '',
        note: ''
      },
      loading: false,
      exceptionTypeList: exceptionTypeList,
      rules: {
        // exceptionType: [
        //   { required: true, message: '请选择异常类型', trigger: 'change' }
        // ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
    },
    setParams () {
      return {
        fnewLibIdList: this.ids,
        fexceptionRemark: this.form.exceptionType,
        fremark: this.form.exceptionType ? this.form.note : ''
        // 使用映射函数根据 this.form.type 的值来添加特定的属性
      }
    },

    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = this.setParams()
          this.loading = true
          const {res = {}} = await awaitWrap(batchModifyBuildStatus(params))
          if (res.code === this.SUCCESS_CODE) {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.$emit('dialogConfirmEvent')
            this.visible = false
          }
          this.loading = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.tips-wrapper {
  display: flex;
  align-items: center;
  margin: 5px;

  .color {
    color: #409eff;
    margin-right: 5px;
  }
}

.input-wrapper {
  width: 220px;
  margin: 0 5px;
}
</style>
