<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="处理"
      width="500px"
      @open="resetData">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="样本状态" prop="sampleStatus">
          <el-radio-group v-model.trim="form.sampleStatus">
            <el-radio :label="1">已找回</el-radio>
            <el-radio :label="2">未找到</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.sampleStatus !== 2" label="是否出库" prop="isStock">
          <el-radio-group v-model.trim="form.isStock">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="orderNum.includes('返样') && form.sampleStatus !== 2 && form.isStock === 1">
          <el-form-item label="是否返样" prop="needBack">
            <el-radio-group v-model.trim="form.needBack">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.needBack === 1" prop="trackingNum">
            <span slot="label">快递单号</span>
            <el-input v-model.trim="form.trackingNum"></el-input>
          </el-form-item>
        </template>
        <el-form-item prop="notes">
          <span slot="label">备&emsp;&emsp;注</span>
          <el-input v-model.trim="form.notes" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
          :loading="submitBtnLoading"
          size="mini"
          type="primary"
          @click="handleDialogSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'abnormalSampleDealDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleId: {
      type: String | Number
    },
    orderNum: String
  },
  data () {
    return {
      form: {
        sampleStatus: '',
        isStock: '',
        notes: '',
        needBack: '', // 是否需要返样
        trackingNum: '' // 快递单号
      },
      submitBtnLoading: false,
      rules: {
        sampleStatus: [
          { required: true, message: '样本状态必选', trigger: 'change' }
        ],
        isStock: [
          { required: true, message: '请选择是否出库', trigger: 'change' }
        ],
        needBack: [
          { required: true, message: '请选择是否返样', trigger: 'change' }
        ],
        trackingNum: [
          { required: true, message: '快递单号必填', trigger: 'blur' }
        ],
        notes: [
          { required: true, message: '备注必填', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    resetData () {
      // this.form = {
      //   sampleStatus: '',
      //   isStock: '',
      //   notes: ''
      // }
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.submitBtnLoading = false
      })
    },
    handleDialogSubmit () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitBtnLoading = true
          let data = {
            fsampleId: this.sampleId,
            sampleStatus: this.form.sampleStatus,
            isStock: this.form.isStock,
            fnotes: this.form.notes
          }
          if (this.orderNum.includes('返样')) {
            data.fisBack = this.form.needBack
            if (this.form.needBack) { // 当是的时候，需要填写快递单号
              data.frecipientCourierNum = this.form.trackingNum
            }
          }
          this.$ajax({
            url: '/sample/handle_sample_exception',
            data: data
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success('处理成功')
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.submitBtnLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
