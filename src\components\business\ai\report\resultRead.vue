<template>
  <div>
    <el-button type="primary" size="mini">编辑</el-button>
    <div class="card-wrapper desc" style="margin-top: 20px;">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column prop="diseaseName" label="基因名称" width="360"></el-table-column>
        <el-table-column prop="zygosity" label="纯合/杂合" width="360"></el-table-column>
        <el-table-column prop="resultRead" label="结果解读"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_result_read_list',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data || []
        this.tableData = []
        rows.forEach(v => {
          let item = {
            resultReadId: v.resultReadId,
            diseaseName: v.diseaseName,
            zygosity: v.zygosity,
            resultRead: v.resultRead
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    }
  }
}

</script>

<style scoped></style>
