<template>
  <div>
    <div style="margin:10px;">
      <el-button type="primary" size="mini" @click="handleFixSnv">修改</el-button>
    </div>
    <div class="card-wrapper">
      <el-table
        ref="table"
        :data="tableData"
        class="table"
        size="mini"
        border
        height="calc(100vh - 40px - 154px - 32px - 20px - 20px - 20px)"
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column show-overflow-tooltip label="基因名称" prop="gene" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="核苷酸改变" prop="nucleotideMutation" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="氨基酸改变" prop="aminoAcidMutation" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="变异意义" prop="mutationType" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="基因变异解析" prop="geneMutationAnalysis" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="遗传性肿瘤综合征" prop="relatedDisease" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="遗传方式" prop="geneticMode" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="Fr.1*" prop="ffr1" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="Fr.2*" prop="ffr2" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="Fr.3*" prop="ffr3" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="参考序列" prop="freferenceSeq" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="功能区域" prop="fexinId" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="纯合/杂合" prop="fzygosity" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="Function" prop="ffunction" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="rs号" prop="frsId" min-width="120px"></el-table-column>
        <el-table-column show-overflow-tooltip label="遗传几率" prop="fgeneticRate" min-width="120px"></el-table-column>
      </el-table>
    </div>
    <fix-snv-dialog :pdata="pdata" :pvisible.sync="fixSnvVisible" @dialogConfirmEvent="getData"></fix-snv-dialog>
  </div>
</template>

<script>

import FixSnvDialog from './fixSnvDialog'
import util from '../../../../util/util'
export default {
  components: {FixSnvDialog},
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: [],
      fixSnvVisible: false,
      selectedRows: new Map(),
      pdata: {}
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_report_h_ht_list',
        loadingDom: '.table',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data || []
        this.tableData = []
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            gene: v.fgene,
            nucleotideMutation: v.nucleotideMutation,
            aminoAcidMutation: v.aminoAcidMutation,
            mutationType: v.mutationType,
            relatedDisease: v.relatedDisease,
            geneticMode: v.geneticMode,
            ffr1: v.ffr1,
            ffr2: v.ffr2,
            ffr3: v.ffr3,
            freferenceSeq: v.referenceSeq,
            fexinId: v.fexinId,
            fzygosity: v.fzygosity,
            ffunction: v.ffunction,
            frsId: v.frsId,
            fgeneticRate: v.fgeneticRate,
            gene_mutation_analysis: v.gene_mutation_analysis,
            gene_mutation_analysis_cn: v.gene_mutation_analysis_cn,
            geneMutationAnalysis: v.geneMutationAnalysis,
            mutationDes: v.mutationDes,
            snvRids: v.snvRids
          }
          item.realData = JSON.parse(JSON.stringify(item))
          item.realData.snvRids = v.snvRids
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 修改Snv
    handleFixSnv () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let data = [...this.selectedRows.values()][0]
      this.pdata = JSON.parse(JSON.stringify(data))
      this.fixSnvVisible = true
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>
