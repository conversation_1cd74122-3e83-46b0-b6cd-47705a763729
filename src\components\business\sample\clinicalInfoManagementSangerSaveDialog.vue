<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :width="type !== 2 ? '65%' : '450px'" :close-on-click-modal="false"
      :before-close="handleClose"
      v-drag-dialog
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" :label-position="labelPosition" label-width="100px" size="mini" label-suffix=":" @submit.native.prevent>
          <template v-if="type === 0">
            <el-form-item label="先证者编号">{{form.referSampleNum}}</el-form-item>
            <el-table
              :data="tableData" height="300"
              style="width: 100%">
              <el-table-column prop="gene" label="基因" min-width="180" show-overflow-tooltip></el-table-column>
              <el-table-column prop="nucleotideMutation" label="碱基改变" min-width="180" show-overflow-tooltip></el-table-column>
              <el-table-column prop="aminoAcidMutation" label="氨基酸改变" min-width="200" show-overflow-tooltip></el-table-column>
              <el-table-column prop="referenceSequence" label="转录本" min-width="180" show-overflow-tooltip></el-table-column>
              <el-table-column prop="exon" label="功能区域" min-width="180" show-overflow-tooltip></el-table-column>
            </el-table>
          </template>
          <template v-else-if="type === 1">
            <el-row :gutter="10">
              <template v-for="(item, index) in form.sampleSangers">
                <div :key="'sangerInfo' + index">
                  <el-col :span="21">
                    <el-row :gutter="10">
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.gene'" :rules="geneRules" label="基因">
<!--                          <el-input v-model.trim="item.gene" clearable placeholder="请输入"></el-input>-->
                          <el-select-v2 v-model.trim="item.gene" :options="geneList" size="mini"
                                        clearable filterable placeholder="请选择" style="width: 100%"/>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.nucleotideMutation'" :rules="nucleotideMutationRules" label="碱基改变">
                          <el-input v-model.trim="item.nucleotideMutation" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.aminoAcidMutation'" :rules="aminoAcidMutationRules" label="氨基酸改变">
                          <el-input v-model.trim="item.aminoAcidMutation" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.referenceSequence'" :rules="referenceSequenceRules" label="转录本">
                          <el-input v-model.trim="item.referenceSequence" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.exon'" :rules="exonRules" label="功能区域">
                          <el-input v-model.trim="item.exon" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.chr'" label="Chr">
                          <el-input v-model.trim="item.chr" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.start'" label="Start">
                          <el-input v-model.trim="item.start" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.end'" label="End">
                          <el-input v-model.trim="item.end" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.ref'" label="Ref">
                          <el-input v-model.trim="item.ref" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.alt'" label="Alt">
                          <el-input v-model.trim="item.alt" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.status'" label="status">
                          <el-input v-model.trim="item.status" clearable placeholder="请输入" maxlength="100"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item :prop="'sampleSangers.' + index + '.statusRatio'" label="statusRatio">
                          <el-input v-model.trim="item.statusRatio" clearable placeholder="请输入" maxlength="100"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="3">
                    <el-form-item label="">
                      <el-button v-if="index === form.sampleSangers.length - 1" type="primary" icon="el-icon-plus" size="mini" circle @click="handleAdd"></el-button>
                      <el-button type="danger" icon="el-icon-delete" size="mini" circle @click="handleDelete(index)"></el-button>
                    </el-form-item>
                  </el-col>
                </div>
              </template>
            </el-row>
          </template>
          <template v-else>
            <el-form-item :rules="sangerSampleNumRules" label="样例编号" prop="sangerSampleNum">
              <el-input v-model.trim="form.sangerSampleNum" placeholder="请输入" style="width: 180px;"></el-input>
            </el-form-item>
          </template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">{{type === 0 ? '绑 定' : '确 定'}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
export default {
  name: 'clinicalInfoManagementTgrSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    myDatePicker
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      title: '',
      type: '',
      form: {},
      labelPosition: 'left',
      tableData: [],
      geneList: [],
      sangerSampleNumRules: [
        {required: true, message: '请输入需要复制的样例编号', trigger: 'blur'}
      ],
      geneRules: [
        {required: true, message: '请输入基因', trigger: 'blur'}
      ],
      nucleotideMutationRules: [
        {required: true, message: '请输入碱基改变', trigger: 'blur'}
      ],
      aminoAcidMutationRules: [
        {required: true, message: '请输入氨基酸改变', trigger: 'blur'}
      ],
      referenceSequenceRules: [
        {required: true, message: '请输入转录本', trigger: 'blur'}
      ],
      exonRules: [
        {required: true, message: '请输入功能区域', trigger: 'blur'}
      ]
    }
  },
  methods: {
    handleOpen () {
      this.type = this.pdata.type
      switch (this.pdata.type) {
        case 0:
          this.labelPosition = 'left'
          this.title = '绑定先证者位点信息'
          this.tableData = this.form.tableData || []
          break
        case 1:
          this.labelPosition = 'top'
          this.title = '添加位点信息'
          this.tableData = []
          break
        case 2:
          this.labelPosition = 'left'
          this.title = '复制位点信息'
          this.tableData = []
          break
      }
      this.form = Object.assign({}, this.form, this.pdata)
      this.getGeneList()
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    // 获取基因下拉选项
    getGeneList () {
      this.$ajax({
        url: '/sample/basic/get_gene_dictionary'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          const data = result.data || []
          this.geneList = data.map(item => {
            return {
              value: item,
              label: item
            }
          })
        }
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let url = ''
          let data = {}
          if (this.type === 0) {
            url = '/sample/sanger/save_refer_sample_info'
            data = {
              referSampleNum: this.form.referSampleNum,
              sangerSampleNum: this.form.sangerSampleNum,
              sampleSangerList: this.tableData
            }
          } else if (this.type === 2) {
            url = '/sample/sanger/copy_sanger_sample_info'
            data = {
              sangerSampleNum: this.form.sangerSampleNum, // 选择复制的样例编号
              sampleNum: this.form.sampleNum // 检测样例编号
            }
          } else {
            url = '/sample/sanger/multi_add'
            data = {
              sampleNum: this.form.sampleNum,
              sampleSangers: this.form.sampleSangers
            }
          }
          this.$ajax({
            url: url,
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('sangerSaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          })
        }
      })
    },
    handleAdd () {
      this.form.sampleSangers.push({
        gene: '',
        nucleotideMutation: '',
        aminoAcidMutation: '',
        referenceSequence: '',
        exon: '',
        chr: '',
        start: '',
        end: '',
        ref: '',
        alt: ''
      })
    },
    handleDelete (index) {
      this.form.sampleSangers.splice(index, 1)
      if (this.form.sampleSangers.length === 0) {
        this.handleAdd()
      }
    }
  }
}
</script>

<style scoped>

</style>
