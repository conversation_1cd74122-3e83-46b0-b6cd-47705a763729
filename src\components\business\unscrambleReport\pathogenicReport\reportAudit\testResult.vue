<template>
  <div>
    <test-result-list :table-data="result.bacteria" title="细菌检测结果"></test-result-list>
    <test-result-list :table-data="result.fungus" title="真菌检测结果"></test-result-list>
    <test-result-list :table-data="result.virus" title="病毒检测结果"></test-result-list>
    <test-result-list :table-data="result.parasite" title="寄生虫检测结果"></test-result-list>
    <test-result-list :table-data="result.specialPathogens" title="特殊病原检测结果（包含分枝杆菌、支原体/衣原体、立克次体等）"></test-result-list>
<!--    <test-result-list :tableData="result" title="疑似背景微生物检测结果"></test-result-list>-->
  </div>
</template>

<script>
import testResultList from './componemts/testResultList'
import util from '../../../../../util/util'
export default {
  components: {
    testResultList
  },
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      result: {}
    }
  },
  methods: {
    async getData () {
      let {code, data = {}} = await this.$ajax({
        url: '/read/pathogen/get_detect_result_list',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.result = {
          bacteria: this.formateTableData(data.fbacteria),
          fungus: this.formateTableData(data.ffungus),
          virus: this.formateTableData(data.fvirus),
          parasite: this.formateTableData(data.fparasite),
          specialPathogens: this.formateTableData(data.fspecialPathogens)
        }
      }
    },
    formateTableData (data) {
      if (!Array.isArray(data)) {
        return
      }
      let tableData = []
      data.forEach(v => {
        let item = {
          type: v.ftype,
          genusName: v.fgenusChinese,
          genusLatin: v.fgenusLatin,
          genusReadsNumber: v.fgenusReadsNumber,
          speciesName: v.fspeciesChinese,
          report: v.freport,
          speciesLatin: v.fspeciesLatin,
          speciesReadsNumber: v.fspeciesReadsNumber,
          coverage: this.formateNum(v.fcoverage),
          speciesRelativeAbundance: v.fspeciesRelativeAbundance && this.formateNum(v.fspeciesRelativeAbundance.split('%')[0])
        }
        item.realData = JSON.parse(JSON.stringify(item))
        util.setDefaultEmptyValueForObject(item)
        tableData.push(item)
      })
      return tableData
    },
    formateNum (number) {
      if (number < 0.01) {
        return '＜0.01%'
      }
      return Math.ceil(number * 100) / 100 + '%'
    }
  }
}
</script>

<style scoped></style>
