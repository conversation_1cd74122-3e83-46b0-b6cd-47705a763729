<template>
  <el-dialog
    append-to-body
    title="查看详情"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="90vw"
    @open="handleOpen">
    <div style="width: 100%;height: 100%; overflow: auto">
      <div class="dialog-wrapper">
        <el-empty v-if="tableData.length < 1" description="暂无数据"></el-empty>
        <el-table
          v-if="tableData.length > 0"
          ref="table"
          :data="[]"
          class="table-title"
          size="mini"
          border
          height="32px"
        >
          <el-table-column :resizable="false" prop="cosDeliverBatchCode" label="吉因加编号" min-width="120"></el-table-column>
          <el-table-column :resizable="false" prop="oriSampleName" label="原始样本名称" min-width="120"></el-table-column>
          <el-table-column :resizable="false" prop="oriSampleLibName" label="原始子文库名称" min-width="120"></el-table-column>
          <el-table-column :resizable="false" prop="libNum" label="杂交子文库" min-width="280"></el-table-column>
          <el-table-column :resizable="false" prop="deliveryTime" label="交付日期" min-width="150"></el-table-column>
          <el-table-column :resizable="false" prop="syncTime" label="投递时间" min-width="150"></el-table-column>
          <el-table-column :resizable="false" prop="walkthroughDateSize" label="上机数据量(G)" min-width="110"></el-table-column>
          <el-table-column :resizable="false" prop="dataSize" label="过滤前产量(G)" min-width="110"></el-table-column>
          <el-table-column :resizable="false" prop="equivalentDataSize" label="数据质控取值(G)" min-width="140"></el-table-column>
          <el-table-column :resizable="false" prop="q30" label="Q30(%)" min-width="120"></el-table-column>
          <el-table-column :resizable="false" prop="productType" label="项目类型" min-width="120"></el-table-column>
          <el-table-column :resizable="false" prop="buildType" label="建库类型" min-width="120"></el-table-column>
          <el-table-column :resizable="false" prop="deliverCount" label="交付次数" min-width="80"></el-table-column>
          <el-table-column :resizable="false" prop="dataRemark" label="备注" min-width="120"></el-table-column>
        </el-table>
        <div class="table-wrapper">
          <template v-for="(item, index) in tableData" >
            <div :key="item.cosDeliverBatchCode + index" class="title cosDeliverBatchCode">
              <span style="font-weight: bold">交付批次:</span>
              {{item.cosDeliverBatchCode}}
            </div>
            <el-table
              ref="table"
              :key="item.cosDeliverBatchCode + index + 'table'"
              :data="item.cosDetailBatchVoList"
              class="table"
              size="mini"
              :show-header="false"
              border
            >
              <el-table-column :resizable="false" prop="geneNum" label="吉因加编号" min-width="120"></el-table-column>
              <el-table-column :resizable="false" prop="oriSampleName" label="原始样本名称" show-overflow-tooltip min-width="120"></el-table-column>
              <el-table-column :resizable="false" prop="oriSampleLibName" label="原始子文库名称" show-overflow-tooltip min-width="120"></el-table-column>
              <el-table-column :resizable="false" prop="libNum" label="杂交子文库" min-width="300" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="deliveryTime" label="交付日期" min-width="150" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="syncTime" label="投递时间" min-width="150" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="walkthroughDateSize" label="上机数据量(G)" min-width="110" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="dataSize" label="过滤前产量(G)" min-width="110" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="equivalentDataSize" label="数据质控取值(G)" min-width="140"></el-table-column>
              <el-table-column :resizable="false" prop="q30" label="Q30(%)" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="productType" label="项目类型" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="buildType" label="建库类型" min-width="120" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="deliverCount" label="交付次数" min-width="80" show-overflow-tooltip></el-table-column>
              <el-table-column :resizable="false" prop="dataRemark" label="备注" min-width="120" show-overflow-tooltip></el-table-column>
            </el-table>
          </template>
        </div>

      </div>
    </div>

    <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {getSubDeliverOrderInfoList} from '../../../../api/deliveryManagement'

export default {
  name: 'orderDetailDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    subOrderIds: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async handleOpen () {
      this.tableData = []
      await this.getDetailInfo()
    },
    async getDetailInfo () {
      const {res} = await awaitWrap(getSubDeliverOrderInfoList({
        fcosDeliverOrderIdList: this.ids,
        fcosDeliverBatchDetailIdList: this.subOrderIds
      }, {loadingDom: '.dialog-wrapper'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        data.forEach(v => {
          const item = {
            cosDeliverBatchCode: v.fcosDeliverBatchCode || '',
            cosDetailBatchVoList: []
          }
          const cosDetailBatchVoList = v.cosDetailBatchVoList || []
          cosDetailBatchVoList.forEach(vv => {
            const info = {
              cosDeliverBatchCode: vv.fcosDeliverBatchCode,
              libNum: vv.flibNum,
              deliveryTime: vv.fdeliverTime,
              q30: vv.fq30,
              walkthroughDateSize: vv.fwalkthroughDateSize,
              equivalentDataSize: vv.fequivalentDataSize,
              dataSize: vv.fdataSize,
              productType: vv.fproductType,
              buildType: vv.fbuildType,
              dataRemark: vv.fdataRemark,
              deliverCount: vv.fdeliverCount,
              geneNum: vv.fgeneNum,
              oriSampleName: vv.foriSampleName,
              oriSampleLibName: vv.foriSampleLibName,
              syncTime: vv.fsyncTime
            }
            item.realData = JSON.parse(JSON.stringify(info))
            util.setDefaultEmptyValueForObject(info)
            item.cosDetailBatchVoList.push(info)
          })
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-wrapper {
  max-height: 50vh;
  position: relative;
  margin: auto;
  width: fit-content!important;
}

.table-wrapper {
  width: fit-content!important;
  max-height: calc(50vh - 32px);
  overflow-y: auto;
  //-ms-overflow-style: none;
  //scrollbar-width: none;
  /*滚动条的宽度*/
  & ::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  /*滚动条的滑块*/
  & ::-webkit-scrollbar-thumb {
    background-color: #999999;
    border-radius: 0;
  }
}
.title {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 999;
  background: #f2f2f2;
  padding: 5px 10px;
  font-size: 12px;
  color: #333;
}
</style>
