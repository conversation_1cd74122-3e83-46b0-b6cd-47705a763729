<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="100px"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="任务单编号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="任务单编号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>

        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.code" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>

    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('021004001', 'buttons')" type="primary" size="mini" @click="handleChooseTask">下载任务单</el-button>
      <el-button v-if="$setAuthority('021004007', 'buttons')" type="primary" plain size="mini" @click="handleInfoChange">信息变更</el-button>
      <el-button v-if="$setAuthority('021004002', 'buttons')" type="danger" plain class="fix-dropdown-margin" size="mini" @click="handleRegisterException">登记异常</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55" show-overflow-tooltip></el-table-column>
        <el-table-column type="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column label="环化后样本名称" prop="resultName" show-overflow-tooltip min-width="150"></el-table-column>
        <el-table-column label="实验样本 " prop="sampleName" show-overflow-tooltip min-width="150"></el-table-column>
        <el-table-column label="核酸/吉因加编号" prop="geneCode" show-overflow-tooltip min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.geneCode !== '查看样本详情'">{{scope.row.geneCode}}</span>
            <div v-else class="link" @click="handleDetail(scope.row)">{{scope.row.geneCode}}</div>
          </template>
        </el-table-column>
        <el-table-column label="原始样本名称" prop="oldSampleName" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="项目名称" prop="projectName" show-overflow-tooltip></el-table-column>
        <el-table-column prop="samplePosition" label="样本孔位" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originSamplePosition" label="原始样本定位" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="文库修饰类型" prop="libModificationType" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="文库类型" prop="libType" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="排单数据量/G" prop="size" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="浓度（ng/ul）" prop="concentration" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="输入量（ng）" prop="inputQuantity" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="取样本量（ul）" prop="sampleSize" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="TE（ul）" prop="te" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column label="环化浓度（ng/ul）" prop="productConcentration" show-overflow-tooltip min-width="140"></el-table-column>
        <el-table-column label="环化体积" prop="productVolumn" show-overflow-tooltip min-width="120"></el-table-column>
<!--        <el-table-column label="总量" prop="total" show-overflow-tooltip min-width="140"></el-table-column>-->
        <el-table-column label="产物定位" prop="resultPosition" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="targetPosition" label="目标孔位" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="targetMaterial" label="目标耗材" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="任务单编号" prop="taskCode" show-overflow-tooltip min-width="120"></el-table-column>
        <el-table-column prop="testPerson" label="检测人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reviewPerson" label="复核人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskSubmitTime" label="提交时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskSubmitNumber" label="提交批次号" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderPerson" label="下单人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderDate" label="下单时间" width="120" show-overflow-tooltip></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <choose-task-dialog
      :pvisible.sync="chooseTaskVisible"
      :type="type"
      work-id="4"
      workflow-status="1"
      @downloadTaskEvent="handleDownload"
      @returnResultEvent="handleReturnResult"
    />

    <!--异常登记-->
    <register-exception-dialog
      :pvisible.sync="registerExceptionVisible"
      :sample-ids="ids"
      :type="4"
      @dialogConfirmEvent="getData"
    />
    <!--    信息变更-->
    <info-change-dialog
      :pvisible.sync="infoChangeDialogVisible"
      :info="info"
      :type="1"
      :process-type="4"
      @dialogConfirmEvent="getData"
    />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import registerExceptionDialog from '../components/registerExceptionDialog' // 登记异常
import infoChangeDialog from '../components/infoChangeDialog' // 信息变更
import ChooseTaskDialog from '../components/chooseTaskDialog'
import {getCyclizationList, downloadTask} from '../../../../api/sequencingManagement/cyclizationApi' // 选择任务单

export default {
  name: 'processing',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ChooseTaskDialog,
    registerExceptionDialog,
    infoChangeDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 54 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      registerExceptionVisible: false,
      infoChangeDialogVisible: false,
      removeSampleVisible: false,
      uploadResultDialogVisible: false,
      chooseTaskVisible: false,
      backFillVisible: false,
      form: {
        taskCode: '', // 任务单编号
        time: [], // 提交时间
        projectName: '', // 项目名称
        sampleName: '', // 样本名称
        code: ''
      },
      taskIds: '', // 任务单ids
      type: '',
      removeInfo: [],
      formSubmit: {},
      info: [],
      ids: [{}],
      searchDialogVisible: false,
      tableData: []
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        fstatus: 1,
        ftaskCode: this.formSubmit.taskCode,
        fsubmitDateStart: time[0],
        fsubmitDateEnd: time[1],
        fprojectName: this.formSubmit.projectName,
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        fgeneCode: this.formSubmit.code,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getCyclizationList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.records || []
        rows.forEach(v => {
          const item = {
            id: v.fid,
            taskId: v.ftaskId,
            projectName: v.fprojectName,
            sampleId: v.fsampleId,
            geneCode: this.handleSetGeneCode(v), // 吉因加编号,
            geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
            geneInfo: {
              fgeneCode: v.fgeneCode,
              fnucleateCode: v.fnucleateCode
            },
            oldSampleName: v.foldSampleName,
            nucleateCode: v.fnucleateCode,
            status: v.fstatus,
            removeReason: v.fremoveReason,
            concentration: v.fconcentration,
            te: v.fte,
            sampleSize: v.fsampleSize,
            inputQuantity: v.finputQuantity,
            productConcentration: v.fproductConcentration,
            productVolumn: v.fproductVolumn,
            total: Math.round((util.mul(v.fproductConcentration, v.fproductVolumn)) * 100) / 100,
            resultName: v.fresultName,
            lastWorkflowId: v.flastWorkflowId,
            nextWorkflowId: v.fnextWorkflowId,
            sampleName: v.fsampleName,
            taskCode: v.ftaskCode,
            creator: v.fcreator,
            createTime: v.fcreateTime,
            libModificationType: v.flibModificationType,
            libType: v.flibType,
            size: v.fsize,
            testPerson: v.ftestPerson,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            taskSubmitNumber: v.fresultSubmitCode,
            orderPerson: v.forderPerson, // 下单人
            orderDate: v.forderDate, // 下单日期
            samplePosition: v.fsamplePosition, // 样本孔位
            originSamplePosition: v.foriginSamplePosition, // 原始样本定位
            volume: v.fvolume, // 体积
            resultPosition: v.fresultPosition,
            targetPosition: v.ftargetPosition, // 目标定位
            targetMaterial: v.ftargetMaterial // 目标耗材
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 下载任务单
    async handleChooseTask () {
      this.type = 'downloadTask'
      this.chooseTaskVisible = true
    },
    async handleDownload (ids = []) {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTask({fidList: ids, fstatus: 1}))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 异常标记
    handleRegisterException () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择样本！')
        return
      }
      this.ids = [...this.selectedRows.keys()]
      this.registerExceptionVisible = true
    },
    // 信息变更
    handleInfoChange () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择需要变更的数据！')
        return
      }
      this.info = [...this.selectedRows.values()].map(v => {
        return {
          ...v.realData
        }
      })
      this.infoChangeDialogVisible = true
    },
    // 移除样本
    handleRemoveSample () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择需要移除的数据！')
        return
      }
      this.removeInfo = [...this.selectedRows.values()]
      this.removeSampleVisible = true
    },
    // 导入结果
    handleUploadResult () {
      this.uploadResultDialogVisible = true
    },
    // 回填结果
    handleBackFillResult () {
      this.type = 'returnResult'
      this.chooseTaskVisible = true
    },
    handleReturnResult (ids) {
      this.taskIds = ids
      this.backFillVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;
  .btn-group {
    margin-bottom: 10px;
  }
}
</style>
