<template>
  <el-dialog
    title="批量修改"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @opened="handleOpen">
    <el-form
      ref="form"
      :model="form"
      label-position="right"
      label-suffix=":"
      label-width="80px"
      class="demo-ruleForm">
      <el-form-item label="异常描述" prop="note">
        <el-input v-model="form.note" type="textarea" size="mini" maxlength="200" clearable placeholder="请输入"></el-input>
      </el-form-item>

    </el-form>

    <span slot="footer" class="dialog-footer">
      <!-- 对话框操作按钮 -->
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import mixins from '@/util/mixins'
import {awaitWrap} from '@/util/util'
import {updateRemark} from '@/api/sequencingManagement/singleCell'
export default {
  name: 'batchModifyingDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number,
      default: null
    },
    note: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      form: {
        note: ''
      },
      loading: false
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.form.note = this.note
    },
    setParams () {
      return {
        fdissociationIdList: [this.id],
        fcosExceptionRemark: this.form.note
        // 使用映射函数根据 this.form.type 的值来添加特定的属性
      }
    },

    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = this.setParams()
          this.loading = true
          const {res = {}} = await awaitWrap(updateRemark(params))
          if (res.code === this.SUCCESS_CODE) {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.$emit('dialogConfirmEvent')
            this.visible = false
          }
          this.loading = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.tips-wrapper {
  display: flex;
  align-items: center;
  margin: 5px;

  .color {
    color: #409eff;
    margin-right: 5px;
  }
}

.input-wrapper {
  width: 220px;
  margin: 0 5px;
}
</style>
