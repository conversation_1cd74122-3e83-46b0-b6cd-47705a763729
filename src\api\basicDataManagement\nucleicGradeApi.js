import { myAjax } from '@/util/ajax'

/**
 * 分页获取核酸等级列表
 * @param {Object} params 查询参数
 * @param {Object} pageInfo 分页信息
 * @returns {Promise}
 */
export function getNucleicGradeList (data, options) {
  return myAjax({
    loadingDom: '.table',
    url: '/system/dna_level_config/get_dna_level_config_page',
    data: data,
    ...options
  })
}

/**
 * 获取核酸等级详情
 * @param {String|Number} id 核酸等级ID
 * @returns {Promise}
 */
export function getNucleicGradeDetail (data) {
  return myAjax({
    url: '/system/dna_level_config/get_dna_level_config_detail',
    data: data
  })
}

/**
 * 更新核酸等级
 * @param {Object} data 核酸等级数据
 * @returns {Promise}
 */
export function updateNucleicGrade (data) {
  return myAjax({
    url: '/system/dna_level_config/save_or_update_dna_level_config_detail',
    data
  })
}

/**
 * 删除核酸等级
 * @param {Array} ids 核酸等级ID数组
 * @returns {Promise}
 */
export function deleteNucleicGrade (data) {
  return myAjax({
    url: '/system/dna_level_config/delete_dna_level_config',
    data: data
  })
}

/**
 * 获取样本列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getSampleTypeListApi (data) {
  return myAjax({
    url: '/system/dna_level_config/get_dna_level_config_sample_type',
    data: data
  })
}
