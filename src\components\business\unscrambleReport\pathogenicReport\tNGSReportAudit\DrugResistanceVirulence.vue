<template>
  <div>
    <el-table
      :data="drugResistanceExplanationTableData"
      class="dataFilterTable"
      style="margin-bottom: 48px"
      border
    >
      <el-table-column prop="geneList" width="180" label="耐药基因"/>
      <el-table-column prop="drugGeneNote" label="耐药机制"/>
    </el-table>
    <el-table
      :data="toxicityExplanationTableData"
      class="dataFilterTable"
      border
    >
      <el-table-column prop="vfGene" width="180" label="毒力因子"/>
      <el-table-column prop="mechanismToxicity" label="毒性机制"/>
    </el-table>
  </div>
</template>

<script>
import util from '../../../../../util/util'

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      drugResistanceExplanationTableData: [],
      toxicityExplanationTableData: []
    }
  },
  methods: {
    // 获取病原体解释
    async getData () {
      let {code, data = []} = await this.$ajax({
        url: '/read/tngs/pathogen/get_resistance_toxicity_interpretation',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get',
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        console.log('haha')
        this.clinicInfo = []
        let drugResistanceExplanation = data.fdrugResistanceExplanation || []
        let toxicityExplanation = data.ftoxicityExplanation || []
        drugResistanceExplanation.forEach(v => {
          let item = {
            geneList: v.fgeneList,
            drugGeneNote: v.fdrugGeneNote
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.drugResistanceExplanationTableData.push(item)
        })
        toxicityExplanation.forEach(v => {
          let item = {
            vfGene: v.fvfGene,
            speciesDescription: v.fspeciesDescription,
            mechanismToxicity: v.fvirulenceFactors
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.toxicityExplanationTableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped></style>
