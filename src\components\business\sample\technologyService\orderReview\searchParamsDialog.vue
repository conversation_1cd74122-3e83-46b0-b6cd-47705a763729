<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :modal="false"
      :modal-append-to-body="false"
      :show-close="false"
      style="border:1px solid #d6dbe1;"
      @open="handleOpen">
      <template #title>
        <div class="search-custom-title-nav">
          <p class="title">查询</p>
          <div>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleSearch">确认</el-button>
          </div>
        </div>
      </template>
      <el-scrollbar class="search-main-wrap">
        <div>
          <div class="sub-title">查询条件</div>
          <el-form
            ref="form"
            :model="form"
            label-width="100px"
            label-suffix=":"
            size="mini"
            inline
            @keyup.enter.native="handleSearch"
          >
<!--            <el-form-item label="检测项目">-->
<!--              <el-input v-model.trim="form.detectType" clearable placeholder="请输入检测项目" class="input-width"></el-input>-->
<!--            </el-form-item>-->
            <el-form-item label="快递单号">
              <el-input v-model.trim="form.expressCode" clearable placeholder="请输入快递单号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="订单编号">
              <el-input v-model.trim="form.orderCode" clearable placeholder="请输入订单编号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="送检单位">
              <el-input v-model.trim="form.unit" clearable placeholder="请输入送检单位" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="项目编号">
              <el-input v-model.trim="form.projectCode" clearable placeholder="请输入项目编号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="项目名称">
              <el-input v-model.trim="form.projectName" clearable placeholder="请输入项目名称" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="预计送达时间">
              <el-date-picker
                v-model="form.etfArrivalTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="提交时间">
              <el-date-picker
                v-model="form.arrivalSampleTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="审核时间">
              <el-date-picker
                v-model="form.reviewTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="订单状态">
              <el-select
                v-model="form.auditStatus"
                clearable
                multiple
                filterable
                size="mini"
                class="input-width">
                <el-option
                  v-for="(value, key, index) in statusOptions"
                  :key="index"
                  :value="key"
                  :label="value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="快递状态">
              <el-select
                v-model="form.expressStatus"
                clearable
                multiple
                filterable
                size="mini"
                class="input-width">
                <el-option v-for="(value, index) in expressOptions" :key="index" :value="value" :label="value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="订单类型">
              <el-select
                v-model="form.orderType"
                clearable
                filterable
                multiple
                size="mini"
                class="input-width">
                <el-option v-for="(value, key, index) in typeOptions" :key="index" :value="key" :label="value"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="false" style="margin-top: 30px;">
          <div class="sub-title">常用查询</div>
          <div style="height: calc(100vh - 50px - 400px - 100px)">
            <el-tooltip :key="item.title" v-for="item in commonSearch" placement="bottom">
              <div slot="content">
                <span>检测项目：{{item.detectType}}</span><br/>
                <span>项目编号：{{item.projectCode}}</span><br/>
                <span>项目名称：{{item.projectName}}</span><br/>
                <span>订单编号：{{item.orderCode}}</span><br/>
                <span>送检单位：{{item.unit}}</span><br/>
                <span>提交时间：{{item.arrivalSampleTime}}</span><br/>
                <span>审核时间：{{item.reviewTime}}</span><br/>
              </div>
              <el-tag
                :disable-transitions="false"
                closable
                @close="handleTagClose(item)">
                {{item.title}}
              </el-tag>
            </el-tooltip>
            <el-tooltip v-if="!inputVisible && commonSearch.length < 10" placement="top">
              <div slot="content">
                <span>设置查询条件后，点击“+”，命名可保存常用查询条件</span>
              </div>
              <el-button
                size="mini"
                class="button-new-tag"
                @click="showInput">+</el-button>
            </el-tooltip>
            <el-input
              v-model="inputValue"
              ref="saveTagInput"
              v-else-if="commonSearch.length < 10"
              size="mini"
              class="input-new-tag"
              maxlength="8"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
          </div>
        </div>
      </el-scrollbar>
    </el-drawer>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../util/mixins'
import {commonData} from './commonData'
export default {
  name: `searchParamsPath`,
  mixins: [mixins.dialogBaseInfo, commonData],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      form: {
        detectType: '',
        projectCode: '',
        projectName: '',
        orderCode: '',
        unit: '',
        arrivalSampleTime: '',
        reviewTime: '',
        expressCode: '',
        etfArrivalTime: '',
        auditStatus: [],
        expressStatus: [],
        orderType: []
      },
      // 快递状态
      expressOptions: [
        '待揽收',
        '运输中',
        '已送达',
        '已取消'
      ],
      commonSearch: [
        {
          title: '常用1',
          detectType: '11',
          projectCode: '22',
          projectName: '33',
          orderCode: '44',
          unit: '55',
          arrivalSampleTime: '66',
          reviewTime: '77'
        }
      ], // 常用查询
      inputVisible: false,
      inputValue: ''
    }
  },
  methods: {
    handleOpen () {},
    handleReset () {
      this.form = {
        detectType: '',
        projectCode: '',
        projectName: '',
        orderCode: '',
        unit: '',
        arrivalSampleTime: '',
        reviewTime: '',
        expressCode: '',
        etfArrivalTime: '',
        auditStatus: [],
        expressStatus: [],
        orderType: []
      }
    },
    handleSearch () {
      this.$emit('dialogConfirmEvent', this.form)
      this.visible = false
    },
    // 删除常用查询
    async handleTagClose (tag) {
      await this.$confirm(`是否删除该常用查询条件?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.commonSearch.splice(this.commonSearch.indexOf(tag), 1)
    },
    // 常用查询输入框展示
    showInput () {
      // 获取对象中的key值
      let keys = Object.keys(this.form)
      // 遍历data，当data里面的key都没值时，返回true
      let noCondition = keys.every(k => {
        let v = this.form[k]
        return v !== 0 && !v
      })
      // 判断查询条件是否为空 为空，则提示
      if (noCondition) {
        this.$message.error('查询条件为空，请输入相关条件')
        return
      }
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    // 常用查询确认
    handleInputConfirm () {
      let inputValue = this.inputValue
      if (inputValue) {
        this.commonSearch.push({
          title: inputValue
        })
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>

<style scoped lang="scss">
.search-custom-title-nav {
  display: flex;
  justify-content: space-between;
  .title {
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    border-left: 4px solid #539fff;
  }
}
.search-main-wrap {
  margin: 0 30px;
  .sub-title {
    font-size: 16px;
    margin-bottom: 10px;
    color: #539fff;
  }
}
.el-tag {
  margin-left: 10px;
  margin-bottom: 5px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  margin-bottom: 5px;
  vertical-align: bottom;
}
.input-width{
  width: 310px;
}
</style>
