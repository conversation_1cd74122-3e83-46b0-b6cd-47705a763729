// 这是全局指令， 用在elementui 的form类型表单上
// 一般是el-select 或者级联选择器，且最好size是mini
// 为true时会展示菊花图，组织下拉框弹出

function f (el, binding) {
  if (binding.value) {
    if (!el.querySelector('.form-mask')) {
      let div = document.createElement('div')
      div.className = 'form-mask'
      div.onclick = function (ev) {
        ev.stopPropagation()
      }
      div.innerHTML = `<span style="padding-left: 15px"><i class="el-icon-loading" style="margin: auto;"></i> 加载中</span>`
      el.appendChild(div)
    }
  } else {
    let div = el.querySelector('.form-mask')
    if (div) {
      el.removeChild(div)
    }
  }
}
const FormLoading = {
  name: 'formLoading',
  f: {
    inserted: f,
    update: f
  }
}

export default FormLoading
