<template>
  <div style="padding: 10px 20px;">
    <h2>容器详细</h2>
    <div style="display: flex;height: 50px;align-items: center;justify-content: flex-end;">
      <el-button v-if="$setAuthority('011001003', 'buttons')" size="mini" type="primary" @click="handleToModifyContainer">设置容器</el-button>
      <!--<el-button size="mini" type="primary" @click="handleDelContainer">删除容器</el-button>-->
    </div>
    <div class="container">
      <div class="container-form">
        <div class="pie">
          <div id="pie" style="width: 350px;height:300px;"></div>
        </div>
        <div class="form">
          <div class="info-row">
            <p class="title">容器名称</p>
            <p class="value">{{containerBaseInfo.name}}</p>
          </div>
          <div class="info-row">
            <p class="title">存储温度</p>
            <p class="value">{{containerBaseInfo.temperature}}</p>
          </div>
          <div class="info-row">
            <p class="title">存储总容量</p>
            <p class="value">{{containerBaseInfo.total}}</p>
          </div>
          <div class="info-row">
            <p class="title">存储占比</p>
            <p class="value">{{containerBaseInfo.storagePercentage}}</p>
          </div>
          <div class="info-row">
            <p class="title">已用孔位</p>
            <p class="value">{{containerBaseInfo.usedHole}}</p>
          </div>
          <div class="info-row">
            <p class="title">剩余容量</p>
            <p class="value">{{containerBaseInfo.availableHole}}</p>
          </div>
        </div>
      </div>
      <div class="container-content">
        <div class="tips">
          <p>存储占比</p>
          <div class="tip-content">
            <template v-for="item in tips">
              <p :key="item.value" :style="{'border-color': setContainerBg(item.value)}">{{item.text}}</p>
            </template>
          </div>
        </div>
        <div class="container-main">
          <div class="path">
            <p>层</p>
            <el-scrollbar class="scroll-container">
              <div class="content floor">
                <template v-for="item in floor">
                  <div
                    :key="item.id"
                    :class="{'active-item': item.id === currentFloorId}"
                    :style="{background: setContainerBg(item.floorStoragePercentage)}"
                    class="item"
                    @click="getShelf(item.id)">
                    <p>{{item.name}} - {{item.floorNumber}}层</p>
                    <p>存储占比：{{item.floorStoragePercentage}}%</p>
                    <p>已存储样本数量：{{item.floorUsedHole}}</p>
                    <p>剩余可用孔位数量：{{item.floorAvailableHole}}</p>
                    <p>{{item.sampleType}}</p>
                  </div>
                </template>
              </div>
            </el-scrollbar>
          </div>
          <div class="path">
            <p>架</p>
            <el-scrollbar class="scroll-container">
              <div class="content shelf">
                <template v-if="currentFloorId" v-for="item in shelf">
                  <div
                    :key="item.id"
                    :class="{'active-item': item.id === currentShelfId}"
                    :style="{background: setContainerBg(item.shelfStoragePercentage)}"
                    class="item"
                    @click="getBox(item.id)">
                    <p>{{item.floorNumber}}层 - {{item.shelfNumber}}架</p>
                    <p>存储占比：{{item.shelfStoragePercentage}}%</p>
                    <p>已存储样本数量：{{item.shelfUsedHole}}</p>
                    <p>剩余可用孔位数量：{{item.shelfAvailableHole}}</p>
                    <p>{{item.sampleType}}</p>
                  </div>
                </template>
              </div>
            </el-scrollbar>
          </div>
          <div class="path">
            <p>盒</p>
            <el-scrollbar class="scroll-container">
              <div class="content box">
                <template v-if="currentShelfId" v-for="item in box">
                  <div
                    :key="item.id"
                    :class="{'active-item': item.id === currentBoxId}"
                    :style="{background: setContainerBg(item.boxStoragePercentage)}"
                    class="item"
                    @click="getHole(item)">
                    <p>{{item.boxNumber}}盒</p>
                    <p>存储占比：{{item.boxStoragePercentage}}%</p>
                    <p>已存储样本数量：{{item.boxUsedHole}}</p>
                    <p>剩余可用孔位数量：{{item.boxAvailableHole}}</p>
                    <p>{{item.sampleType}}</p>
                  </div>
                </template>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
    <div class="hole-main">
      <p>孔</p>
      <div class="content">
        <template v-if="currentFloorId && currentShelfId && currentBoxId && hole.row > 0 && hole.column > 0">
          <div class="hole-container">
            <div class="hole-row">
              <div class="y-text"></div>
              <div :key="item" v-for="item in hole.column" class="x-text">{{addZero(item)}}</div>
            </div>
            <div :key="v" v-for="v in hole.row" class="hole-row">
              <div class="y-text">{{numToUppercase(v)}}</div>
              <div
                :key="item"
                :style="{background: hole.holeDetail[numToUppercase(v) + addZero(item)] ? '#67C23A' : '#ccc'}"
                v-for="item in hole.column"
                class="hole">
                {{showHoleSampleInfoTitle(hole.holeDetail, numToUppercase(v) + addZero(item))}}
                <!--:style="{background: hole.hasUsed.indexOf(numToUppercase(v) + addZero(item)) > -1 ? '#67C23A' : '#ccc'}">-->
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <add-dialog
      :pvisible="addDialogVisible"
      :type="addType"
      :pdata="addDialogFloorData"
      @dialogCloseEvent="addDialogVisible = false"/>
  </div>
</template>

<script>
// import num from './components/cc'
import echarts from 'echarts'
import addDialog from './containerDetailAddDialog'
import constants from '../../../util/constants'
export default {
  name: 'containerDetail',
  components: {
    addDialog
  },
  mounted () {
    this.getContainerDetail()
    // this.getSampleType()
  },
  computed: {
    containerId () {
      return this.$store.getters.getValue('containerId')
    }
  },
  data () {
    return {
      containerBaseInfo: {},
      containerSampleTypePercentages: [], // 容器各样本类型占比
      tips: [
        {text: '0~20%', value: 10},
        {text: '20%~40%', value: 30},
        {text: '40%~60%', value: 50},
        {text: '60%~80%', value: 70},
        {text: '80%~99%', value: 90},
        {text: '100%', value: 100}
      ],
      containerTypeOptions: constants.CONTAINER_TYPE_OPTIONS,
      floor: [],
      shelf: [],
      box: [],
      hole: {
        row: 0,
        column: 0,
        hasUsed: []
      },
      currentFloorId: '',
      currentShelfId: '',
      currentBoxId: '',
      addDialogVisible: false,
      addType: '', // 添加类型
      addDialogFloorData: {}, // 添加层的数据
      sampleType: []
    }
  },
  filters: {
    showContainerType (val) {
      let item = this.containerTypeOptions.filter(v => {
        return v.value === val
      })[0]
      if (item) {
        return item.label
      } else {
        return ''
      }
    }
  },
  methods: {
    // 展示孔位的样本信息
    showHoleSampleInfoTitle (holeDetail, num) {
      if (holeDetail && holeDetail[num]) return `${holeDetail[num]}`
      return '空'
    },
    getContainerDetail () {
      this.$ajax({
        url: '/sample/container/get_container_by_id',
        method: 'get',
        data: {
          containerId: this.containerId
        },
        loadingDom: 'body'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let baseInfo = res.data.sampleContainer
          this.containerBaseInfo = {
            id: baseInfo.fid,
            lab: baseInfo.flab,
            name: `${baseInfo.fnickName ? baseInfo.fnickName + '-' : ''}${baseInfo.fname}`,
            type: baseInfo.ftype,
            temperature: baseInfo.ftemperature,
            total: baseInfo.ftotalCapacity,
            usedHole: baseInfo.fusedHole,
            storagePercentage: baseInfo.fstoragePercentage + '%',
            availableHole: baseInfo.favailableHole
          }
          if (baseInfo.ftype === 'C') {
            this.containerBaseInfo.total = '-'
            this.containerBaseInfo.usedHole = '-'
            this.containerBaseInfo.storagePercentage = '-'
            this.containerBaseInfo.availableHole = '-'
          }
          // 画环图
          let chartData = res.data.sampleTypePercentages || []
          if (chartData.length === 0) {
            chartData = [
              {
                sampleTypeName: baseInfo.ftotalCapacity ? '剩余容量' : '剩余占比',
                sampleCount: baseInfo.ftotalCapacity || 100
              }
            ]
          }
          this.containerSampleTypePercentages = []
          chartData.forEach(v => {
            let item = {
              name: v.sampleTypeName,
              value: v.sampleCount
            }
            this.containerSampleTypePercentages.push(item)
          })
          this.initPie()
          // 获取层
          this.floor = []
          let floor = res.data.sampleContainerFloors || []
          floor.forEach((v, i) => {
            let item = {
              id: v.fid,
              name: v.fcontainerName,
              floorAvailableHole: v.ffloorAvailableHole,
              floorNumber: v.ffloorNumber,
              floorStoragePercentage: v.ffloorStoragePercentage,
              floorTotalCapacity: v.ffloorTotalCapacity,
              floorUsedHole: v.ffloorUsedHole,
              sampleType: v.fsampleType
            }
            if (i === 0) {
              this.currentFloorId = v.fid
            }
            this.floor.push(item)
          })
          // 获取架
          this.shelf = []
          let shelf = res.data.sampleContainerShelfs || []
          shelf.forEach((v, i) => {
            let item = {
              id: v.fid,
              shelfNumber: v.fshelfNumber,
              floorNumber: v.ffloorNumber,
              sampleType: v.fsampleType,
              shelfStoragePercentage: v.fshelfStoragePercentage,
              shelfTotalCapacity: v.fshelfTotalCapacity,
              shelfUsedHole: v.fshelfUsedHole,
              shelfAvailableHole: v.fshelfAvailableHole
            }
            if (i === 0) {
              this.currentShelfId = v.fid
            }
            this.shelf.push(item)
          })
          // 获取盒
          this.box = []
          let box = res.data.sampleContainerBoxes || []
          box.forEach((v, i) => {
            let item = {
              id: v.fid,
              boxNumber: v.fboxNumber,
              boxUsedHole: v.fboxUsedHole,
              boxAvailableHole: v.fboxAvailableHole,
              holeTotalCount: v.fholeTotalCount,
              sampleType: v.fsampleType,
              boxStoragePercentage: v.fboxStoragePercentage,
              xSize: +v.fxSize,
              ySize: +v.fySize,
              hasUsedHole: v.fboxUsedHoleNumber ? v.fboxUsedHoleNumber.split(',') : '',
              holeSampleNumberList: v.holeSampleNumberList
            }
            if (i === 0) {
              this.currentBoxId = v.fid
              this.hole = {
                row: +v.fxSize,
                column: +v.fySize,
                hasUsed: v.fboxUsedHoleNumber ? v.fboxUsedHoleNumber.split(',') : '',
                boxId: v.fid,
                holeDetail: {}
              }
              if (v.holeSampleNumberList) {
                let holeDetail = {}
                v.holeSampleNumberList.forEach(vv => {
                  holeDetail[vv.holeNumber] = vv.sampleNumber
                })
                this.hole.holeDetail = holeDetail
              }
            }
            this.box.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取样本类型
    getSampleType () {
      this.$ajax({
        url: '/sample/sample_type/get_all_sample_type',
        data: {}
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.sampleType = []
          let types = res.data || []
          types.forEach(item => {
            let v = {
              value: item.sampleTypeName
            }
            this.sampleType.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    initPie () {
      let option = {
        title: {
          text: '容器整体占比'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}<br>占比 ({d}%)'
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '60%'],
            data: this.containerSampleTypePercentages,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      let myChart = echarts.init(this.$el.querySelector('#pie'))
      myChart.setOption(option)
    },
    // 获取架
    getShelf (floorId) {
      if (floorId === this.currentFloorId) return
      this.currentFloorId = floorId
      this.currentShelfId = ''
      this.currentBoxId = ''
      this.$ajax({
        url: '/sample/container/shelf/get_shelf_list',
        method: 'get',
        data: {
          floorId: floorId
        },
        loadingDom: '.shelf'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let rows = res.data || []
          this.shelf = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              shelfNumber: v.fshelfNumber,
              floorNumber: v.ffloorNumber,
              sampleType: v.fsampleType,
              shelfStoragePercentage: v.fshelfStoragePercentage,
              shelfTotalCapacity: v.fshelfTotalCapacity,
              shelfUsedHole: v.fshelfUsedHole,
              shelfAvailableHole: v.fshelfAvailableHole
            }
            this.shelf.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取盒
    getBox (shelfId) {
      if (shelfId === this.currentShelfId) return
      this.currentShelfId = shelfId
      this.currentBoxId = ''
      this.$ajax({
        url: '/sample/container/box/get_box_list',
        method: 'get',
        data: {
          shelfId: shelfId
        },
        loadingDom: '.box'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.box = []
          let rows = res.data || []
          rows.forEach((v, i) => {
            let item = {
              id: v.fid,
              boxNumber: v.fboxNumber,
              boxUsedHole: v.fboxUsedHole,
              boxAvailableHole: v.fboxAvailableHole,
              holeTotalCount: v.fholeTotalCount,
              sampleType: v.fsampleType,
              boxStoragePercentage: v.fboxStoragePercentage,
              xSize: +v.fxSize,
              ySize: +v.fySize,
              hasUsedHole: v.fboxUsedHoleNumber ? v.fboxUsedHoleNumber.split(',') : '',
              holeSampleNumberList: v.holeSampleNumberList
            }
            if (i === 0) {
              this.hole = {
                row: +v.fxSize,
                column: +v.fySize,
                hasUsed: v.fboxUsedHoleNumbers ? v.fboxUsedHoleNumbers.split(',') : '',
                boxId: v.fid,
                holeDetail: {}
              }
              if (v.holeSampleNumberList) {
                let holeDetail = {}
                v.holeSampleNumberList.forEach(vv => {
                  holeDetail[vv.holeNumber] = vv.sampleNumber
                })
                this.hole.holeDetail = holeDetail
              }
            }
            this.box.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 展示孔
    getHole (item) {
      console.log(item)
      // holeSampleNumberList
      this.currentBoxId = item.id
      this.hole = {
        row: +item.xSize,
        column: +item.ySize,
        hasUsed: item.hasUsedHole,
        boxId: item.id
      }
      if (item.holeSampleNumberList) {
        let holeDetail = {}
        item.holeSampleNumberList.forEach(vv => {
          holeDetail[vv.holeNumber] = vv.sampleNumber
        })
        this.hole.holeDetail = holeDetail
      }
    },
    setContainerBg (num) {
      if (Number.isNaN(Number(num))) return '#000'
      if (num >= 0 && num < 20) return '#00cc00'
      if (num >= 20 && num < 40) return '#33cccc'
      if (num >= 40 && num < 60) return '#3366ff'
      if (num >= 60 && num < 80) return '#ff9933'
      if (num >= 80 && num < 100) return '#ff0066'
      if (num >= 100) return '#990099'
    },
    // 到容器设置页面
    handleToModifyContainer () {
      this.$router.push('/business/sub/modifyContainer')
    },
    // 判断空对象
    isEmptyObj (obj) {
      return Object.keys(obj).length === 0
    },
    // 不到10补0
    addZero (num) {
      if (+num > 0 && +num < 10) {
        return 0 + '' + num
      }
      return num
    },
    // 数字转大写字母
    numToUppercase (num) {
      return String.fromCharCode(64 + num)
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  .container{
    display: flex;
    .container-form{
      width: 350px;
      flex-shrink: 0;
      .pie{
        border-bottom: 1px solid #ccc;
      }
      .form{
        display: flex;
        flex-wrap: wrap;
        padding: 10px;
        & > .info-row{
          width: 50%;
          margin-bottom: 10px;
          font-size: 14px;
          .title{
            font-weight: 600;
            color: #606266;
          }
          .value{
            margin-top: 5px;
            color: #303133;
          }
        }
      }
    }
    .container-content{
      width: auto;
      overflow-x: auto;
      .tips{
        .tip-content{
          display: flex;
          margin: 10px 0;
          & > p{
            border-left: 1em solid;
            margin-right: 20px;
            padding-left: 5px;
          }
        }
      }
      .container-main{
        display: flex;
        justify-content: space-between;
        .path{
          margin-right: 20px;
          & > p{
            font-weight: 600;
            line-height: 30px;
          }
          .content{
            padding: 20px;
            min-width: 250px;
            .item{
              width: 250px;
              min-width: 200px;
              color: #fff;
              font-size: 14px;
              padding: 5px 10px;
              cursor: pointer;
              & > p{
                line-height: 1.5;
              }
              &:not(:last-child) {
                margin-bottom: 20px;
              }
            }
            .hole-container{
              padding: 10px;
              background: #EBEEF5;
              $w: 200px;
              .hole-row{
                display: flex;
                margin-bottom: 15px;
                .x-text{
                  width: $w;
                  margin-right: 20px;
                  font-size: 14px;
                  text-align: center;
                }
                .y-text{
                  font-size: 14px;
                  margin-right: 10px;
                  width: 1em;
                }
                .hole{
                  width: $w;
                  height: 20px;
                  background: #ccc;
                  border-radius: 10px;
                  margin-right: 20px;
                }
              }
            }
          }
        }
      }
    }
  }
  .hole-main{
    & > p{
      font-weight: 600;
      line-height: 30px;
    }
    .content{
      max-width: 95vw;
      margin: auto;
      overflow-x: auto;
      background: #EBEEF5;
      .hole-container{
        padding: 10px;
        margin: 0 10px;
        $w: 120px;
        .hole-row{
          display: flex;
          margin-bottom: 15px;
          & > *{
            flex-shrink: 0;
          }
          .x-text{
            width: $w;
            margin-right: 20px;
            font-size: 14px;
            text-align: center;
          }
          .y-text{
            font-size: 14px;
            margin-right: 10px;
            width: 1em;
          }
          .hole{
            width: $w;
            height: 30px;
            background: #ccc;
            border-radius: 5px;
            margin-right: 20px;
            color: #fff;
            font-size: 14px;
            line-height: 30px;
            padding-left: 5px;
          }
        }
      }
    }
  }
  .scroll-container{
    height: 430px;
    overflow-x: hidden;
    border: 1px solid rgb(121,121,121);
  }
  .active-item{
    border: 3px dashed red;
  }
</style>
