<template>
  <div>
    <el-dialog
      title="申请检测"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="65%"
      v-drag-dialog
      @open="handleOpen"
      :before-close="handleClose">
      <nav class="dialog-operateBar">
        <div>
          <el-select
            v-model.trim="form.content"
            @clear="handleReset"
            @change="clearInput"
            size="mini"
            placeholder="请选择"
            :popper-append-to-body="false"
            style="width: 150px;"
            clearable>
            <template v-for="(v, k) in searchOptions">
              <el-option :key="k" :label="v" :value="k"></el-option>
            </template>
          </el-select>
          <template v-if="form.content === 'fdetectStatus'">
            <el-select
              v-model.trim="form.input"
              style="width: 250px;margin: 0 20px;"
              size="mini"
              clearable
              :poppr-append-to-body="false"
            >
              <el-option v-for="(v, k) in statusOptions" :key="k" :label="v" :value="k"></el-option>
            </el-select>
          </template>
          <template v-else-if="form.content === 'fqualityControlResults'">
            <el-select
              v-model.trim="form.input"
              style="width: 250px;margin: 0 20px;"
              size="mini"
              clearable
              :popper-append-to-body="false"
            >
              <el-option v-for="(v, k) in resultOptions" :key="k" :label="v" :value="k"></el-option>
            </el-select>
          </template>

          <template v-else>
            <el-input
              v-model.trim="form.input"
              style="width: 250px;margin: 0 20px;"
              size="mini"
              clearable
              @keyup.enter.native="handleSearch"
              :disabled="!form.content"
              placeholder="请输入"></el-input>
          </template>
          <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </div>
      </nav>
      <template v-if="visible">
        <el-table
          :data="tableData"
          class="q-table tableBox"
          ref="table"
          height="300px"
          style="width: 100%"
          stripe
          border
          :cell-style="handleRowStyle"
          @select="handleSelectTable"
          @row-click="handleRowClickCheck"
          @select-all="handleSelectAll">
          <template v-if="btnType === 2">
            <el-table-column type="selection" :selectable="handleSelectable" disabled="true"
                             width="50"></el-table-column>
          </template>
          <el-table-column type="index" width="50" label="序号"></el-table-column>
          <el-table-column prop="statusText" label="状态" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleName" label="样本名称" width="140" show-overflow-tooltip></el-table-column>
          <template v-if="[3].includes(+type)">
            <el-table-column prop="geneplusNum" label="吉因加编号" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="nucleicAcidNumber" label="核酸编号" min-width="180"
                             show-overflow-tooltip></el-table-column>
          </template>
          <el-table-column prop="qualityControlResultsText" label="质控结果/综合评估" width="140"
                           show-overflow-tooltip></el-table-column>
          <el-table-column prop="Qubit" label="Qubit浓度(ng/μl)" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="samplingVolume" label="体积(μl)" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="total" label="总量(ng)" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="notes" label="检测结果备注" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remarks" label="检测备注" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operator" label="操作人" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operationTime" label="操作时间" min-width="180"
                           show-overflow-tooltip></el-table-column>
        </el-table>
        <div style="display: flex;">
          <el-pagination
            @size-change="handleSizeChange"
            style="background: #ffffff;"
            @current-change="handleCurrentChange"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            :total="totalPage">
            <button @click="handleRefresh"><i class="el-icon-refresh" style="font-size: 17px; line-height: inherit"></i>
            </button>
          </el-pagination>
          <div style="color: #606266; font-size: 14px; line-height: 32px;">已选中{{ selectedRowsSize }}条数据</div>
        </div>
      </template>
      <span slot="footer" v-if="btnType === 2">
        <el-button size="mini" :loading="submitBtnLoading" @click="handleClose">取消</el-button>
        <el-button size="mini" :loading="submitBtnLoading" @click="handleStopDetection">停止检测</el-button>
        <el-button size="mini" type="primary" :loading="submitBtnLoading" @click="handleConfirm">任务下达</el-button>
      </span>
    </el-dialog>

    <!-- 任务下达 - 风险或者不合格样本-->
    <el-dialog
      title="提示"
      :visible.sync="taskReleaseVisible1"
      width="450px"
      :close-on-click-modal="false"
      append-to-body
      @close="taskReleaseVisible1 = false">
      <div>
        <div><i class="el-icon-info" style="margin-right: 5px;"></i>所选样本中有风险或不合格的样本，是否确认进行检测?
        </div>
        <div style="display: flex; margin-top: 10px;">
          <label>检测备注：</label>
          <el-input
            v-model.trim="remarks"
            placeholder="请填写检测备注（如有）"
            type="textarea"
            maxlength="200"
            :rows="2"
            show-word-limit
            style="width: 300px;"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="taskReleaseVisible1 = false">取 消</el-button>
        <el-button type="primary" size="mini" @click="submit(1)">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 任务下达 - 合格-->
    <el-dialog
      title="提示"
      :visible.sync="taskReleaseVisible2"
      width="450px"
      :close-on-click-modal="false"
      append-to-body
      @close="taskReleaseVisible2 = false">
      <div>
        <div><i class="el-icon-info" style="margin-right: 5px;"></i>任务下达后不可撤销，确认继续吗?</div>
        <div style="display: flex; margin-top: 10px;">
          <label>检测备注：</label>
          <el-input
            v-model.trim="remarks"
            placeholder="请填写检测备注（如有）"
            type="textarea"
            maxlength="200"
            :rows="2"
            show-word-limit
            style="width: 300px;"></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="taskReleaseVisible2 = false">取 消</el-button>
        <el-button type="primary" size="mini" @click="submit(1)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import num from './components/cc'
import mixins from '../../../../../util/mixins'
import util from '../../../../../util/util'
// import constants from "../../../../util/constants";
export default {
  name: 'qualityControlResultsDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    type: Number || String, // 1: illumina 2:MGI 3: 组织或核酸
    orderId: String || Number,
    orderCode: String || Number,
    note: String || Number,
    btnType: Number // 1：质控结果 2：申请检测
  },
  data () {
    return {
      form: {
        content: '',
        input: ''
      },
      selectedRows: new Map(),
      submitBtnLoading: false,
      formSubmit: {},
      searchOptions: {
        'fdetectStatus': '状态',
        'fqualityControlResults': '质控结果',
        'cs.fname': '样本名称',
        'fnucleateCode': '核酸编号'
      },

      statusOptions: {
        2: '未下单',
        1: '已下单',
        3: '停止检测'
      },

      resultOptions: {
        合格: '合格',
        风险: '风险',
        不合格: '不合格'
      },

      qualityControlResultsObj: {
        '合格': {text: '合格', key: 1},
        '风险': {text: '风险', key: 2},
        '不合格': {text: '不合格', key: 3}
      },
      remarks: '',
      taskReleaseVisible1: false,
      taskReleaseVisible2: false
    }
  },
  methods: {
    handleOpen () {
      this.tableData = []
      this.remarks = ''
      this.handleReset()
    },
    handleReset () {
      this.form = {
        content: '',
        input: ''
      }
      this.handleSearch()
    },

    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.clearMap()
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/order/get_control_result_list',
        data: {
          pageVO: {
            pageSize: this.pageSize,
            currentPage: this.currentPage
          },
          fsearch: {
            searchField: this.formSubmit.content,
            searchValue: this.formSubmit.input
          },
          orderId: this.orderId
        },
        loadingDom: '.q-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            const qualityControlResultsItem = this.qualityControlResultsObj[v.fsampleEvaluation] || {}
            let item = {
              id: v.fid,
              sampleId: v.fsampleId,
              status: v.fdetectStatus, // 1.已下单 2未下单 3停止检测
              statusText: this.statusOptions[v.fdetectStatus] || '',
              qualityControlResults: qualityControlResultsItem.key, // 质控结果(1.合格,2风险,3不合格)
              qualityControlResultsText: qualityControlResultsItem.text,
              sampleName: v.fname,
              geneplusNum: v.fgeneCode,
              operator: v.foperator,
              operationTime: v.foperatorTime,
              nucleicAcidNumber: v.fnucleateCode,
              Qubit: v.fqubitMmol,
              ftaskOrderNum: v.ftaskOrderNum,
              samplingVolume: v.fnucleateMl,
              total: v.famounts,
              notes: v.fnote,
              remarks: v.fdetectNote
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.handleEchoSelect()
        }
      })
    },
    // 下拉框发生改变时，清空input
    clearInput () {
      this.form.input = ''
    },
    // 点击行
    handleRowClickCheck (row, c) {
      if (!this.handleSelectable(row)) return
      this.handleRowClick(row)
    },

    // 数据是否可选(返回值为false则禁用)
    handleSelectable (row) {
      const item = row.realData
      return [2].includes(+item.status)
    },

    // 确定
    async handleConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下达的项')
        return
      }
      let datas = [...this.selectedRows.values()]
      let isCanSend = datas.every(v => [2].includes(+v.status) && [1, 2, 3].includes(+v.qualityControlResults))
      if (!isCanSend) {
        this.$message.error('请选择「未下单、不合格」，「未下单，风险」，「未下单，合格」的数据！')
        return
      }
      // 所选样本中有风险或不合格的样本
      const isShow = datas.some(v => [2, 3].includes(+v.qualityControlResults))
      this.remarks = this.note
      if (isShow) {
        this.taskReleaseVisible1 = true
      } else {
        this.taskReleaseVisible2 = true
      }
    },

    // 停止检测
    async handleStopDetection () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下达的项')
        return
      }
      let datas = [...this.selectedRows.values()]
      let isCanSend = datas.every(v => [2].includes(+v.status) && [2, 3].includes(+v.qualityControlResults))
      if (!isCanSend) {
        this.$message.error('请选择「未下单、不合格」，「未下单，风险」的数据！')
        return
      }

      // 所选样本中有风险或不合格的样本
      const isShow = datas.some(v => [2, 3].includes(+v.qualityControlResults))
      if (isShow) {
        await this.$confirm('停止检测后不可撤销，是否确认停止检测？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'info'
        })
      }
      this.submit(3)
    },

    submit (status) {
      this.taskReleaseVisible1 = false
      this.taskReleaseVisible2 = false
      const rows = [...this.selectedRows.values()]
      let cosQcResultBeanList = rows.map(v => {
        return {
          fgeneCode: v.geneplusNum,
          ftaskOrderNum: v.ftaskOrderNum
        }
      })
      let data = {
        orderCode: this.orderCode,
        cosQcResultBeanList: cosQcResultBeanList,
        detectStatus: status // 检测状态 1申请检测/已下单 3停止检测
      }
      if (status === 1) {
        data.detectNote = this.remarks // 检测备注
      }

      this.submitBtnLoading = true
      this.$ajax({
        url: '/order/save_apply_inspection',
        data: {
          ...data
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.submitBtnLoading = false
      })
    }
  }
}
</script>

<style scoped>
.dialog-operateBar {
  margin-bottom: 10px;
}
</style>
