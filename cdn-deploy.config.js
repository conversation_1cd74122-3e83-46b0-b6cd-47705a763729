/**
 * LIMS前端项目CDN部署配置
 * 修复版本 - 确保阿里云OSS配置完整
 */

module.exports = {
  // 项目基本信息
  projectName: 'lims-frontend',
  version: '2.1.0',
  environment: 'prod', // 必需字段：明确指定环境

  // 构建配置
  buildTool: 'auto', // 自动检测构建工具
  outputDir: 'dist',
  skipBuild: false,
  skipAnalyze: false,
  buildStrategy: 'enhanced',
  // CDN配置
  cdn: {
    provider: 'aliOSS',
    domain: 'cdn.geneplus.org.cn',
    basePath: 'lims-frontend/test/2.1.0', // 建议使用环境变量
    https: true,
    cacheControl: 'public, max-age=31536000',
    enableGzip: true,
    forceOverwrite: false
  },

  // 阿里云OSS配置 - 包含所有必需字段
  aliOSS: {
    // 必需字段1: accessKeyId
    accessKeyId: process.env.ALIOSS_ACCESS_KEY_ID || 'LTAI5t6Xju3qctunsgVVxUcX',

    // 必需字段2: accessKeySecret (之前可能缺失)
    accessKeySecret: process.env.ALIOSS_ACCESS_KEY_SECRET || '******************************',

    // 必需字段3: bucket
    bucket: process.env.ALIOSS_BUCKET || 'genereadonly',

    // 必需字段4: region
    region: process.env.ALIOSS_REGION || 'oss-cn-beijing',

    // 可选字段: endpoint (如果需要自定义端点)
    endpoint: process.env.ALIOSS_ENDPOINT || undefined
  },

  // 构建策略选项
  strategyOptions: {
    enableOptimization: true,
    enableMinification: true,
    enableTreeShaking: true,
    enableCodeSplitting: true,
    enableAssetOptimization: true,
    enableSourceMap: false,
    enableGzip: true,
    cdnPathReplacement: true
  },

  // 确保启用CDN路径替换
  strategyOptions: {
    cdnPathReplacement: true,
    enableOptimization: true
  },

  // 上传配置
  upload: {
    concurrency: 8,
    retryTimes: 3,
    timeout: 60000
  },

  // 日志配置
  logging: {
    level: 'info',
    enableFile: true,
    logDir: 'logs'
  }
};
