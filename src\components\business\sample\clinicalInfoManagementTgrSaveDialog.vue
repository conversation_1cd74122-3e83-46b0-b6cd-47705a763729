<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible" :modal="false"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="45%"
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" label-width="80px" size="mini" label-suffix=":">
          <template v-if="type === 'HPV'">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="检测日期">
                  <my-date-picker v-model="form.detectTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检测方法">
                  <el-input v-model.trim="form.detectMethod" maxlength="30" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检测结果">
                  <el-select v-model="form.testResult" placeholder="请选择">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in testResultList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="结果描述">
                  <el-input v-model.trim="form.resultDesc" :autosize="{minRows: 3}" type="textarea" maxlength="200" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else>
            <el-row :gutter="10">
             <el-col :span="12">
               <el-form-item label="检测日期">
                 <my-date-picker v-model="form.detectTime"></my-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="检测结果">
                 <el-select v-model="form.testResult" placeholder="请选择">
                   <el-option
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                     v-for="item in testResultList">
                   </el-option>
                 </el-select>
               </el-form-item>
             </el-col>
              <template v-for="(item, index) in form.bytgrDtl">
                <div :key="'bytgrDtl' + index">
                  <el-col :span="7">
                    <el-form-item label="指标">
                      <el-select v-model="item.detectIndex" clearable allow-create filterable placeholder="请选择">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in detectIndexList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="指标值">
                      <el-input v-model.trim="item.detectIndexValue" placeholder="请输入" @blur="handleDetectIndexValueBlur(index)" ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item label="单位">
                      <el-select v-model="item.detectUnit" clearable allow-create filterable placeholder="请选择">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in detectUnitList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label-width="0px">
                      <el-button v-if="index === form.bytgrDtl.length - 1" type="primary" icon="el-icon-plus" circle @click="handleAdd"></el-button>
                      <el-button type="danger" icon="el-icon-delete" circle @click="handleDelete(index)"></el-button>
                    </el-form-item>
                  </el-col>
                </div>
              </template>
            </el-row>
          </template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
export default {
  name: 'clinicalInfoManagementTgrSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    myDatePicker
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      title: '',
      type: '',
      form: {
        detectTime: ''
      },
      detectIndexList: [],
      detectUnitList: [],
      testResultList: [
        {
          label: '阴性',
          value: 0
        },
        {
          label: '阳性',
          value: 1
        },
        {
          label: '不详',
          value: 2
        }
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.type = this.pdata.type
        switch (this.pdata.type) {
          case 'HPV':
            this.form = {
              bytgrId: null,
              sampleBasicId: null,
              bytgrType: 1,
              bytgrFlag: '',
              detectTime: '',
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectMethod: '',
              resultDesc: '',
              testResult: ''
            }
            this.title = '病原体感染HPV'
            this.detectUnitList = []
            this.detectIndexList = []
            break
          case 'HBV':
            this.title = '病原体感染HBV'
            this.form = {
              bytgrId: null,
              sampleBasicId: null,
              bytgrType: 2,
              bytgrFlag: '',
              unknownReason: '',
              detectTime: '',
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectMethod: '',
              resultDesc: '',
              testResult: '',
              bytgrDtl: [
                {
                  bytgrDetailId: null,
                  bytgrId: null,
                  detectIndex: '',
                  detectIndexOther: '',
                  detectIndexValue: '',
                  detectUnit: '',
                  detectUnitOther: ''
                }
              ]
            }
            this.detectUnitList = [
              {
                label: 'S/CO',
                value: 'S/CO'
              },
              {
                label: 'S/N',
                value: 'S/N'
              },
              {
                label: 'IU/ml',
                value: 'IU/ml'
              },
              {
                label: 'ng/ml',
                value: 'ng/ml'
              },
              {
                label: 'NCU/ml',
                value: 'NCU/ml'
              },
              {
                label: 'mlU/ml',
                value: 'mlU/ml'
              }
            ]
            this.detectIndexList = [
              {
                label: 'HBSAg',
                value: 'HBSAg'
              },
              {
                label: 'HBSAb/抗-HBs',
                value: 'HBSAb/抗-HBs'
              },
              {
                label: 'HbeAg',
                value: 'HbeAg'
              },
              {
                label: 'HbeAb/抗-HBe',
                value: 'HbeAb/抗-HBe'
              },
              {
                label: 'HbcAb/抗-HBc',
                value: 'HbcAb/抗-HBc'
              }
            ]
            break
          case 'HCV':
            this.title = '病原体感染HCV'
            this.form = {
              bytgrId: null,
              sampleBasicId: null,
              bytgrType: 3,
              bytgrFlag: '',
              unknownReason: '',
              detectTime: '',
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectMethod: '',
              resultDesc: '',
              testResult: '',
              bytgrDtl: [
                {
                  bytgrDetailId: null,
                  bytgrId: null,
                  detectIndex: '',
                  detectIndexOther: '',
                  detectIndexValue: '',
                  detectUnit: '',
                  detectUnitOther: ''
                }
              ]
            }
            this.detectUnitList = [{
              label: 'S/CO',
              value: 'S/CO'
            }]
            this.detectIndexList = [
              {
                label: 'HCV-Ag',
                value: 'HCV-Ag'
              },
              {
                label: 'HCV-Ab',
                value: 'HCV-Ab'
              }
            ]
            break
          case 'EBV':
            this.title = '病原体感染EBV'
            this.form = {
              bytgrId: null,
              sampleBasicId: null,
              bytgrType: 4,
              bytgrFlag: '',
              unknownReason: '',
              detectTime: '',
              detectYear: '',
              detectMonth: '',
              detectDay: '',
              detectMethod: '',
              resultDesc: '',
              testResult: '',
              bytgrDtl: [
                {
                  bytgrDetailId: null,
                  bytgrId: null,
                  detectIndex: '',
                  detectIndexOther: '',
                  detectIndexValue: '',
                  detectUnit: '',
                  detectUnitOther: ''
                }
              ]
            }
            this.detectUnitList = [{
              label: 'S/CO',
              value: 'S/CO'
            }]
            this.detectIndexList = [
              {
                label: 'EBV-CA（IgG）',
                value: 'EBV-CA（IgG）'
              },
              {
                label: 'EBV-CA（IgM）',
                value: 'EBV-CA（IgM）'
              },
              {
                label: 'EBV-EA（IgG）',
                value: 'EBV-EA（IgG）'
              },
              {
                label: 'EBNA-IgG',
                value: 'EBNA-IgG'
              }
            ]
            break
        }
        this.form = Object.assign({}, this.form, this.pdata)
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      let url = '/sample/clinical/save_bytgr'
      let data = {}
      let detectTime = this.form.detectTime.split('-')
      switch (this.type) {
        case 'HPV':
          data = {
            bytgrId: this.form.bytgrId,
            sampleBasicId: this.form.sampleBasicId,
            bytgrType: this.form.bytgrType,
            bytgrFlag: 1,
            detectYear: detectTime[0] || '',
            detectMonth: detectTime[1] || '',
            detectDay: detectTime[2] || '',
            detectMethod: this.form.detectMethod,
            resultDesc: this.form.resultDesc,
            testResult: this.form.testResult
          }
          break
        default:
          data = {
            bytgrId: this.form.bytgrId,
            sampleBasicId: this.form.sampleBasicId,
            bytgrType: this.form.bytgrType,
            unknownReason: this.form.unknownReason,
            bytgrFlag: 1,
            detectYear: detectTime[0] || '',
            detectMonth: detectTime[1] || '',
            detectDay: detectTime[2] || '',
            detectMethod: this.form.detectMethod,
            resultDesc: this.form.resultDesc,
            bytgrDtl: this.form.bytgrDtl,
            testResult: this.form.testResult
          }
          break
      }
      this.loading = true
      this.$ajax({
        url: url,
        data: data
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('tgrSaveDialogConfirmEvent', this.type)
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    handleAdd () {
      this.form.bytgrDtl.push({
        bytgrDetailId: null,
        bytgrId: null,
        detectIndex: '',
        detectIndexOther: '',
        detectIndexValue: '',
        detectUnit: '',
        detectUnitOther: ''
      })
    },
    handleDelete (index) {
      this.form.bytgrDtl.splice(index, 1)
      if (this.form.bytgrDtl.length === 0) {
        this.handleAdd()
      }
    },
    handleDetectIndexValueBlur (index) {
      if (this.form.bytgrDtl[index].detectIndexValue && /^[0-9]+.?[0-9]*$/.test(this.form.bytgrDtl[index].detectIndexValue)) {
        this.$set(this.form.bytgrDtl[index], 'detectIndexValue', Number(this.form.bytgrDtl[index].detectIndexValue).toFixed(3))
      }
    }
  }
}
</script>

<style scoped>

</style>
