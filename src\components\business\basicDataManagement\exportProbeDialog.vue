<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="500px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item prop="cancerName" label="匹配癌种">
          <el-input v-model.trim="cancerName" size="mini" maxlength="20" disabled clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="name" label="核心探针名称">
          <el-input v-model.trim="coreName" size="mini" maxlength="20" disabled clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="probeSupplier" label="供应商">
          <el-select v-model.trim="form.probeSupplier" size="mini" placeholder="请选择">
            <el-option
              :key="item"
              :label="item"
              :value="item"
              v-for="item in supplierList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="fileList" label="核心探针名称">
          <el-upload
            :action="action"
            ref="upload"
            :data="{
              fid: id,
              fprobeName: coreName,
              fprobeSupplier: this.form.probeSupplier
            }"
            :auto-upload="false"
            :on-success="handleOnSuccess"
            :before-upload="handleBeforeUpload"
            :file-list="form.fileList"
            class="upload-demo">
            <el-button size="mini" type="primary">点击上传</el-button>
            <span slot="tip" class="el-upload__tip">只能上传zip压缩文件包</span>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number,
      required: false
    },
    coreName: {
      type: String
    },
    cancerName: {
      type: String
    }
  },
  data () {
    let validateFile = (rule, value, callback) => {
      if (this.$refs.upload.uploadFiles.length > 0) {
        callback()
      } else {
        callback(new Error('请选择文件'))
      }
    }
    return {
      title: '探针文件上传',
      loading: false,
      action: constants.JS_CONTEXT + '/system/probe/import_core_probe_data',
      form: {
        probeSupplier: '',
        fileList: []
      },
      supplierList: ['伯科', '吉因加'],
      rules: {
        fileList: [
          {validator: validateFile, trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    // 保存配置
    handleConfirm () {
      console.log(111111111111)
      this.$refs.form.validate(async valid => {
        if (valid) {
          console.log(1111, this.$refs.upload.uploadFiles)
          this.loading = true
          this.$refs.upload.submit()
        }
      })
    },
    // 校验是否上传过文件
    async handleCheck () {
      let result = await this.$ajax({
        url: '/system/probe/import_probe_before_check',
        data: {
          fid: this.id
        },
        method: 'get'
      })
      if (result.code === this.SUCCESS_CODE) {
        if (result.message === '探针数据已存在') {
          this.loading = false
          await this.$confirm(`已存在探针文件是否上传覆盖`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          this.loading = true
        }
        return false
      } else {
        return true
      }
    },
    handleOnSuccess (res) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('上传成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      } else {
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    // 上传前校验文件名和文件大小
    async handleBeforeUpload (file) {
      console.log(file)
      let isCheck = await this.handleCheck()
      if (isCheck) return
      let name = file.name
      let size = file.size
      if (!name.includes(this.name)) {
        this.$message.error('文件名必须包含个性化探针名称')
        this.loading = false
        return false
      }
      if (/\.(zip)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 500) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过500M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传zip文件')
        return false
      }
    }
  }
}
</script>

<style scoped>
/deep/ .el-input__inner {
  width: 300px;
}
</style>
