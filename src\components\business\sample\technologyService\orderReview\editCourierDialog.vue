<template>
  <el-dialog
    v-drag-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    title="修改快递单号"
    width="700px"
    @open="handleOpen">
    <el-form :model="formData" :rules="rules" size="mini" ref="formData" label-width="120px">
      <el-form-item label="订单编号">
        {{orderCode}}
      </el-form-item>
      <!--      快递单号-->
      <el-form-item label="快递单号" prop="expressCode">
        <el-input v-model.trim="formData.expressCode" placeholder="请输入快递单号" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
export default {
  name: 'editCourierDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    /**
     * 快递单号
     * */
    code: {
      type: String
    },

    /**
     * 订单编号
     * */
    orderId: {
      type: String
    },
    orderCode: {
      type: String
    }
  },
  data () {
    return {
      formData: {
        expressCode: ''
      },
      rules: {
        expressCode: [
          { required: true, message: '请输入快递单号', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.formData.expressCode = this.code
    },
    // 提交表单
    handleConfirm () {
      this.$refs.formData.validate(valid => {
        if (valid) {
          // 提交表单
          this.handleSave()
        }
      })
    },
    handleSave () {
      this.loading = true
      this.$ajax({
        url: '/order/update_express_code',
        method: 'post',
        data: {
          forderId: this.orderId,
          fexpressCode: this.formData.expressCode
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.visible = false
          this.$emit('dialogConfirmEvent')
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }

}
</script>
