<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :modal-append-to-body="false"
      :show-close="false"
      @open="handleOpen">
      <template #title>
        <div class="search-custom-title-nav">
          <p class="title">查询</p>
          <div>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleSearch">确认</el-button>
          </div>
        </div>
      </template>
      <div class="box">
        <div>
          <div class="sub-title">查询条件</div>
          <el-form
            ref="form"
            :model="form"
            label-width="100px"
            label-suffix=":"
            size="mini"
            @keyup.enter.native="handleSearch"
          >
            <el-form-item label="申请单号">
              <el-input v-model.trim="form.orderNum" clearable placeholder="请输入申请单号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="送检单位">
              <el-input v-model.trim="form.inspectionUnit" clearable placeholder="请输入送检单位" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="申请类型">
              <el-select v-model.trim="form.applyType" size="mini" class="input-width" clearable placeholder="请选择申请类型">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in applyTypeList">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申请时间">
              <el-date-picker
                v-model.trim="form.applyTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                size="mini"
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="收件人">
              <el-input v-model.trim="form.receiver" clearable placeholder="请输入收件人" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="收件人地址">
              <el-input v-model.trim="form.address" clearable placeholder="请输入收件人地址" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="收件人电话">
              <el-input v-model.trim="form.phone" clearable placeholder="请输入收件人电话" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="处理状态">
              <el-select v-model.trim="form.handleStatus" size="mini" class="input-width" clearable placeholder="请选择处理状态">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in dealList">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="快递单号">
              <el-input v-model.trim="form.expressNum" clearable placeholder="请输入快递单号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="快递状态">
              <el-select v-model.trim="form.expressStatus" size="mini" class="input-width" clearable placeholder="请选择快递状态">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in expressStatusList">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="预计送达时间">
              <el-date-picker
                v-model.trim="form.preReceiveTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                size="mini"
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="处理时间">
              <el-date-picker
                v-model.trim="form.handleTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                size="mini"
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="false" style="margin-top: 30px;">
          <div class="sub-title">常用查询</div>
          <div style="height: calc(100vh - 50px - 400px - 100px)">
            <el-tooltip :key="item.title" v-for="item in commonSearch" placement="bottom">
              <div slot="content">
                <span>Gene+编号：{{item.geneNumber}}</span><br/>
                <span>检测项目：{{item.testProject}}</span><br/>
                <span>项目名称：{{item.projectName}}</span><br/>
                <span>项目编号：{{item.projectCode}}</span><br/>
                <span>样本名称：{{item.sampleName}}</span><br/>
                <span>送检单位：{{item.inspectionDepartment}}</span><br/>
                <span>到样时间：{{item.arrivalSampleDate}}</span><br/>
                <span>订单编号：{{item.orderCode}}</span><br/>
                <span>样本状态：{{item.sampleState}}</span><br/>
                <span>申请状态：{{item.reviewState}}</span><br/>
                <span>申请时间：{{item.reviewTime}}</span><br/>
                <span>处理状态：{{item.detailState}}</span><br/>
                <span>处理时间：{{item.detailTime}}</span><br/>
              </div>
              <el-tag
                :disable-transitions="false"
                closable
                @close="handleTagClose(item)">
                {{item.title}}
              </el-tag>
            </el-tooltip>
            <el-tooltip v-if="!inputVisible && commonSearch.length < 10" placement="top">
              <div slot="content">
                <span>设置查询条件后，点击“+”，命名可保存常用查询条件</span>
              </div>
              <el-button
                size="mini"
                class="button-new-tag"
                @click="showInput">+</el-button>
            </el-tooltip>
            <el-input
              v-model="inputValue"
              ref="saveTagInput"
              v-else-if="commonSearch.length < 10"
              size="mini"
              class="input-new-tag"
              maxlength="8"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
          </div>
        </div>
      </div>

    </el-drawer>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../util/mixins'
export default {
  name: `searchParamsPath`,
  mixins: [mixins.dialogBaseInfo],
  created () {
    // this.remoteMethod()
  },
  mounted () {
    this.handleSearch()
    this.getExpressStatus()
    this.getApplyList()
    this.getHandleStatusList()
  },
  data () {
    return {
      form: {
        orderNum: '', // 申请单号
        inspectionUnit: '', // 送检单位
        applyType: '', // 申请类型
        applyTime: '', // 申请时间
        handleStatus: '', // 处理状态
        expressNum: '', // 快递单号
        expressStatus: '', // 返样快递状态
        preReceiveTime: '', // 预计送达时间
        handleTime: '', // 处理时间
        receiver: '', // 收件人
        address: '', // 完整地址
        phone: '' // 收件人电话
      },
      applyTypeList: [],
      expressStatusList: [],
      commonSearch: [], // 常用查询
      inputVisible: false,
      inputValue: '',
      list: [],
      dealList: [],
      sampleList: []
    }
  },
  methods: {
    // 打开查询弹窗时触发事件
    handleOpen () {
      this.commonSearch = []
      this.$ajax({
        method: 'post',
        url: '/system/page_query/list_query',
        data: {
          fpage: 'sampleManagement'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          for (let i = 0; i < result.data.length; i++) {
            let val = JSON.parse(result.data[i].fparam)
            val.fid = result.data[i].fid
            this.commonSearch.push(val)
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取申请状态列表
    getApplyList () {
      this.$ajax({
        url: '/sample/t7_return/list_apply_type'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.applyTypeList = data.map(v => {
            return {
              label: v.name,
              value: v.code
            }
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 重置
    handleReset () {
      this.form = {
        orderNum: '', // 申请单号
        inspectionUnit: '', // 送检单位
        applyType: '', // 申请类型
        applyTime: '', // 申请时间
        handleStatus: '', // 处理状态
        expressNum: '', // 快递单号
        expressStatus: '', // 返样快递状态
        preReceiveTime: '', // 预计送达时间
        handleTime: '', // 处理时间
        receiver: '', // 收件人
        address: '', // 完整地址
        phone: '' // 收件人电话
      }
    },
    handleSearch () {
      this.$emit('dialogConfirmEvent', this.form)
      this.visible = false
    },
    // 删除常用查询条件
    async handleTagClose (tag) {
      await this.$confirm(`是否删除该常用查询条件?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.$ajax({
        url: '/system/page_query/delete_query',
        method: 'get',
        data: {fids: tag.fid.toString()}
      }).then(result => {
        if (result.code === '200') {
          this.commonSearch.splice(this.commonSearch.indexOf(tag), 1)
          this.$message.success('删除成功')
        } else {
          this.$message.error(result.message)
        }
      })
    },
    showInput () {
      // 获取对象中的key值
      let keys = Object.keys(this.form)
      // 遍历data，当data里面的key都没值时，返回true
      let noCondition = keys.every((k, index) => {
        let v = this.form[k]
        return !v
      })
      // 判断查询条件是否为空 为空，则提示
      if (noCondition) {
        this.$message.error('查询条件为空，请输入相关条件')
        return
      }
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    // 获取快递状态
    getExpressStatus () {
      this.$ajax({
        method: 'post',
        url: '/sample/t7_return/list_express_status'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          data.forEach(v => {
            let item = {
              label: v.name,
              value: v.code
            }
            this.expressStatusList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取处理状态列表
    getHandleStatusList () {
      this.$ajax({
        url: '/sample/t7_return/list_handle_status'
      }).then(result => {
        if (result.code === '200') {
          let data = result.data || []
          this.dealList = data.map(v => {
            return {
              label: v.name,
              value: v.code
            }
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // remoteMethod () {
    //   this.$ajax({
    //     url: '/sample/t7_return/list_apply_type'
    //   }).then(result => {
    //     if (result.code === '200') {
    //       this.list = result.data
    //     } else {
    //       this.$message.error(result.message)
    //     }
    //   })
    //   this.$ajax({
    //     url: '/sample/t7_return/list_handle_status'
    //   }).then(result => {
    //     if (result.code === '200') {
    //       this.dealList = result.data
    //     } else {
    //       this.$message.error(result.message)
    //     }
    //   })
    //   this.$ajax({
    //     url: '/sample/t7_return/list_cos_sample_status'
    //   }).then(result => {
    //     if (result.code === '200') {
    //       this.sampleList = result.data
    //     } else {
    //       this.$message.error(result.message)
    //     }
    //   })
    // },
    // 常用查询条件输入框失焦
    handleInputConfirm () {
      let inputValue = this.inputValue
      if (inputValue) {
        const Data = {
          title: inputValue,
          geneNumber: this.form.geneCode,
          testProject: this.form.testItem,
          projectName: this.form.projectName,
          projectCode: this.form.projectCode,
          sampleName: this.form.sampleName,
          inspectionDepartment: this.form.inspectionDepartment,
          arrivalSampleDate: this.form.arrivalSampleDate,
          orderCode: this.form.orderCode,
          sampleState: this.form.sampleState,
          reviewState: this.form.applicationStatus,
          reviewTime: this.form.applicationTime,
          detailState: this.form.detailStatus,
          detailTime: this.form.detailTime
        }
        this.$ajax({
          method: 'post',
          url: '/system/page_query/save_query',
          data: {
            fpage: 'sampleManagement',
            fname: inputValue,
            fparam: JSON.stringify(Data)
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.commonSearch.push(Data)
          } else {
            this.$message.error(result.message)
          }
        })
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>

<style scoped lang="scss">
.box {
  margin: 20px;
}
.search-custom-title-nav {
  display: flex;
  justify-content: space-between;
  .title {
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    border-left: 4px solid #539fff;
  }
}
.search-main-wrap {
  margin: 0 30px;
}
.sub-title {
  //overflow: scroll;
  font-size: 16px;
  margin-bottom: 10px;
  color: #539fff;
}
.el-tag {
  margin-left: 10px;
  margin-bottom: 5px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  margin-bottom: 5px;
  vertical-align: bottom;
}
.input-width{
  width: 330px !important;
}
.el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item {
  margin-bottom: 12px;
}
>>>.el-drawer__body{
  overflow: scroll;
}
>>>.el-form-item__content{
  width: 70%;
}
</style>
