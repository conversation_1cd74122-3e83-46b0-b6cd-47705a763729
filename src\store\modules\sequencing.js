import {getSessionInfo, setSessionInfo} from '../../util/util'
const sampleInfo = getSessionInfo('sampleInfo')
const state = {
  sampleInfo: sampleInfo
}

const mutations = {
  SET_SAMPLE_INFO: (state, sampleInfo) => {
    state.sampleInfo = sampleInfo
    setSessionInfo('sampleInfo', sampleInfo)
  }
}

const actions = {
  setSampleInfo ({ commit }, sampleInfo) {
    return new Promise(resolve => {
      commit('SET_SAMPLE_INFO', sampleInfo)
      resolve()
    })
  }
}

export default {
  state,
  mutations,
  actions
}
