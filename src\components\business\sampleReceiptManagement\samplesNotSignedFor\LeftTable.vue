<template>
  <div>
    <el-table
      ref="leftTable"
      :data="tableData"
      style="width: 100%;"
      border
      :height="tbHeight"
      class="leftTable"
      row-key="id"
      highlight-selection-row
      @select="handleSelectTable"
      @select-all="handleSelectAll"
      @row-click="handleRowClick">
      <el-table-column type="selection" width="40" fixed reserve-selection></el-table-column>
      <el-table-column min-width="120" label="快递单号" prop="expressCode" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="120" label="订单编号" prop="orderCode" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            class="link"
            @click.stop="handleCheck(scope.row.realData, 3)">{{scope.row.orderCode}}</span>
        </template>
      </el-table-column>
      <el-table-column width="80" label="样本数量" prop="sampleNum" show-overflow-tooltip></el-table-column>
      <el-table-column width="80" label="签收序号" prop="index" show-overflow-tooltip></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
<!--      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>-->
    </el-pagination>
    <div class="footer-nav">
      当前选中<span>{{ chooseLength }}</span>条记录
      <el-button type="text" @click="handleClearChoose">清空</el-button>
    </div>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
import util from '../../../../util/util'
// import util from '@/util/util'
export default {
  name: 'leftTable',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    statusList: Array
  },
  mounted () {
    this.$_setTbHeight(74 + 65 + 21 + 32 + 42 + 32)
  },
  watch: {
    selectedRows: {
      handler (val) {
        const keys = Object.keys(val)
        this.chooseLength = keys.length
        this.$emit('select-change', keys)
      },
      deep: true
    }
  },
  data () {
    return {
      selectedRows: {},
      chooseLength: 0
    }
  },
  methods: {
    search () {
      this.resetTable()
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/experiment/sign/get_sign_order_list',
        data: {
          fstatusList: this.statusList,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        },
        loadingDom: '.leftTable'
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.records || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.forderCode,
              expressCode: v.fexpressCode,
              orderCode: v.forderCode,
              orderId: v.forderId,
              orderType: v.forderType,
              sampleNum: v.fsampleNum,
              index: v.fsignNumber
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          // if (this.currentPage === 1 && this.chooseLength === 0 && this.tableData.length > 0) {
          //   setTimeout(() => {
          //     const row = this.tableData[0]
          //     this.handleRowClick(row)
          //   })
          // }
        }
      })
    },
    // 查看订单详情 type 1编辑 2 只读 3 详情
    handleCheck (row, type) {
      console.log('查看订单详情', row.orderType, row.orderId, row.orderCode)
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type,
          orderId: row.orderId,
          status: 2,
          code: row.orderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (row.orderType === 5) path = '/business/subpage/technologyService/singleCell'
      if (path) util.openNewPage(path)
    },
    // 点击行
    handleRowClick (row, c) {
      const hasThis = !!this.selectedRows[row.id]
      this.$refs.leftTable.toggleRowSelection(row, !hasThis)
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      const hasThis = !!this.selectedRows[row.id]
      hasThis ? this.$delete(this.selectedRows, row.id) : this.$set(this.selectedRows, row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows = {}
      selection.forEach((row) => {
        this.$set(this.selectedRows, row.id, row)
      })
    },
    handleClearChoose () {
      const tableRef = this.$refs.leftTable
      tableRef.clearSelection()
      this.selectedRows = {}
    },
    // 情况所有数据
    resetTable () {
      this.currentPage = 1
      this.tableData = []
      this.handleClearChoose()
    }
  }
}
</script>

<style scoped lang="scss">
.footer-nav{
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #909399;
  span{
    font-weight: bold;
    font-size: 18px;
    padding: 0 5px;
    color: $color;
  }
}
</style>
