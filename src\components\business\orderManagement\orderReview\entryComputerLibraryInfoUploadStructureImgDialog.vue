<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      title="上传文库结构"
      width="30%"
      @open="handleOpen">
      <el-upload
        ref="upload"
        :action="uploadUrl"
        :headers="headers"
        :auto-upload="false"
        :limit="1"
        :on-success="handleOnSuccess"
        :on-error="handleOnError"
        :before-upload="handleBeforeUpload"
        accept="image/*">
        <el-button size="mini" type="primary">点击上传</el-button>
        <div slot="tip" class="el-upload__tip">只能上传图片文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import mixins from '../../../../util/mixins'
import constants from '../../../../util/constants'
import Cookies from 'js-cookie'
export default {
  name: 'entryComputerLibraryInfoUploadStructureImgDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      loading: false,
      headers: {
        token: Cookies.get('token')
      },
      uploadUrl: constants.JS_CONTEXT + '/order/upload_lib_file'
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
        // this.getSamplingPoint()
      })
    },
    // 点击确认
    handleConfirm () {
      if (this.$refs.upload.uploadFiles.length === 0) {
        this.$message.error('请选择图片文件')
        return
      }
      this.$refs.upload.submit()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      if (/\.(jpeg|jpg|png)$/.test(name)) {
        return true
      } else {
        this.loading = false
        this.$message.error('只能上传peg、jpg、png文件')
        return false
      }
    },
    // 提交成功
    handleOnSuccess (res) {
      console.log(res)
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.$emit('dialogConfirmEvent', res.data)
        this.visible = false
        this.$refs.upload.clearFiles()
      } else {
        this.$message.error(res.message)
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    }
  }
}
</script>

<style scoped>

</style>
