/**
 * 标注管理类
 * 负责管理OpenSeadragon标注插件的交互和数据同步
 */
import { clamp } from '@/util/util'

export default class AnnotationManager {
  /**
   * 构造函数
   * @param {Object} options 配置选项
   * @param {Object} options.viewer OpenSeadragon查看器实例
   * @param {String} options.mode 模式 ('view'|'edit')
   * @param {Function} options.onMarkerChange 标记变更回调
   * @param {Function} options.onSelectionChange 选择变更回调
   */
  constructor (options) {
    this.viewer = options.viewer
    this.mode = options.mode || 'view'
    this.onMarkerChange = options.onMarkerChange || (() => {})
    this.onSelectionChange = options.onSelectionChange || (() => {})

    this.annotations = []
    this.singleRectMode = false
    this.currentAnnotation = null
    this.isDragging = false
    this.isResizing = false
    this.isRotating = false

    this.initAnnotations()
  }

  /**
   * 初始化标注系统
   */
  initAnnotations () {
    if (!this.viewer) {
      console.error('【AnnotationManager】查看器实例不存在')
      return
    }

    // 创建SVG覆盖层用于标注
    this.createSvgOverlay()
    this.bindEvents()
  }

  /**
   * 创建SVG覆盖层
   */
  createSvgOverlay () {
    try {
      // 获取查看器容器
      const viewerElement = this.viewer.element
      // 创建SVG覆盖层
      this.svgOverlay = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
      this.svgOverlay.style.position = 'absolute'
      this.svgOverlay.style.top = '0'
      this.svgOverlay.style.left = '0'
      this.svgOverlay.style.width = '100%'
      this.svgOverlay.style.height = '100%'
      this.svgOverlay.style.pointerEvents = 'none'
      this.svgOverlay.style.zIndex = '1000'

      // 添加到查看器容器
      viewerElement.appendChild(this.svgOverlay)

      console.log('【AnnotationManager】SVG覆盖层已创建')
    } catch (e) {
      console.error('【AnnotationManager】创建SVG覆盖层失败:', e)
    }
  }

  /**
   * 绑定事件
   */
  bindEvents () {
    if (!this.viewer) return

    // 监听视口变化，更新标注位置
    this.viewer.addHandler('update-viewport', () => {
      this.updateAnnotationsPosition()
    })

    // 监听动画结束，更新标注位置
    this.viewer.addHandler('animation-finish', () => {
      this.updateAnnotationsPosition()
    })

    // 监听画布点击事件
    this.viewer.addHandler('canvas-click', (event) => {
      if (this.mode === 'edit' && !this.isDragging && !this.isResizing && !this.isRotating) {
        // 在编辑模式下，如果没有标注，可以创建新标注
        // 但由于是单选框模式，这里不需要处理
      }
    })
  }

  /**
   * 更新标注位置
   */
  updateAnnotationsPosition () {
    if (!this.svgOverlay || this.annotations.length === 0) return

    try {
      this.annotations.forEach(annotation => {
        this.updateAnnotationElement(annotation)
      })
    } catch (e) {
      console.error('【AnnotationManager】更新标注位置失败:', e)
    }
  }

  /**
   * 更新标注元素
   */
  updateAnnotationElement (annotation) {
    if (!annotation || !annotation.element) return

    try {
      const marker = annotation.marker

      // 将图像坐标转换为视口坐标
      const centerPoint = this.viewer.viewport.imageToViewportCoordinates(marker.cx, marker.cy)

      // 将视口坐标转换为像素坐标
      const pixelPoint = this.viewer.viewport.viewportToViewerElementCoordinates(centerPoint)

      // 计算缩放比例
      const zoom = this.viewer.viewport.getZoom()
      const imageZoom = this.viewer.world.getItemAt(0).viewportToImageZoom(zoom)

      // 计算选框尺寸
      const width = marker.sw * imageZoom
      const height = marker.sh * imageZoom

      // 更新矩形位置和尺寸
      const rect = annotation.element.querySelector('rect')
      if (rect) {
        rect.setAttribute('x', pixelPoint.x - width / 2)
        rect.setAttribute('y', pixelPoint.y - height / 2)
        rect.setAttribute('width', width)
        rect.setAttribute('height', height)
        rect.setAttribute('transform', `rotate(${marker.angle || 0} ${pixelPoint.x} ${pixelPoint.y})`)
      }

      // 更新控制点位置
      this.updateControlPoints(annotation, pixelPoint, width, height, marker.angle || 0)
    } catch (e) {
      console.error('【AnnotationManager】更新标注元素失败:', e)
    }
  }

  /**
   * 更新控制点位置
   */
  updateControlPoints (annotation, center, width, height, angle) {
    if (!annotation.controlPoints) return

    const halfWidth = width / 2
    const halfHeight = height / 2
    const rad = angle * Math.PI / 180

    // 计算旋转后的控制点位置
    const positions = [
      { x: -halfWidth, y: -halfHeight }, // 左上角
      { x: 0, y: -halfHeight }, // 上中
      { x: halfWidth, y: -halfHeight }, // 右上角
      { x: halfWidth, y: 0 }, // 右中
      { x: halfWidth, y: halfHeight }, // 右下角
      { x: 0, y: halfHeight }, // 下中
      { x: -halfWidth, y: halfHeight }, // 左下角
      { x: -halfWidth, y: 0 } // 左中
    ]

    positions.forEach((pos, index) => {
      if (annotation.controlPoints[index]) {
        // 应用旋转变换
        const rotatedX = pos.x * Math.cos(rad) - pos.y * Math.sin(rad)
        const rotatedY = pos.x * Math.sin(rad) + pos.y * Math.cos(rad)

        annotation.controlPoints[index].setAttribute('cx', center.x + rotatedX)
        annotation.controlPoints[index].setAttribute('cy', center.y + rotatedY)
      }
    })

    // 更新旋转控制点（在上方中心点上方20像素）
    if (annotation.rotateHandle) {
      const rotateX = 0 * Math.cos(rad) - (-halfHeight - 20) * Math.sin(rad)
      const rotateY = 0 * Math.sin(rad) + (-halfHeight - 20) * Math.cos(rad)

      annotation.rotateHandle.setAttribute('cx', center.x + rotateX)
      annotation.rotateHandle.setAttribute('cy', center.y + rotateY)
    }
  }

  /**
   * 设置模式
   * @param {String} mode 'view'|'edit'
   */
  setMode (mode) {
    this.mode = mode

    // 更新所有标注的交互状态
    this.annotations.forEach(annotation => {
      this.updateAnnotationInteraction(annotation)
    })
  }

  /**
   * 更新标注交互状态
   */
  updateAnnotationInteraction (annotation) {
    if (!annotation || !annotation.element) return

    const isEditable = this.mode === 'edit'

    // 设置指针事件
    annotation.element.style.pointerEvents = isEditable ? 'all' : 'none'

    // 显示/隐藏控制点
    if (annotation.controlPoints) {
      annotation.controlPoints.forEach(point => {
        point.style.display = isEditable ? 'block' : 'none'
      })
    }

    if (annotation.rotateHandle) {
      annotation.rotateHandle.style.display = isEditable ? 'block' : 'none'
    }
  }

  /**
   * 设置单选框模式
   * @param {Boolean} enabled 是否启用单选框模式
   */
  setSingleRectMode (enabled) {
    this.singleRectMode = enabled

    // 如果启用单选框模式，且有多个标注，只保留第一个
    if (enabled && this.annotations.length > 1) {
      const toKeep = this.annotations[0]
      const toRemove = this.annotations.slice(1)

      toRemove.forEach(annotation => {
        this.removeAnnotationElement(annotation)
      })

      this.annotations = [toKeep]
      this.onMarkerChange(this.getMarkers())
    }
  }

  /**
   * 清除所有标注
   */
  clearAnnotations () {
    this.annotations.forEach(annotation => {
      this.removeAnnotationElement(annotation)
    })

    this.annotations = []
    this.currentAnnotation = null
  }

  /**
   * 移除标注元素
   */
  removeAnnotationElement (annotation) {
    if (annotation && annotation.element && annotation.element.parentNode) {
      annotation.element.parentNode.removeChild(annotation.element)
    }
  }

  /**
   * 添加矩形标注
   * @param {Object} marker 标记对象
   */
  addRectAnnotation (marker) {
    try {
      // 如果是单选框模式，先清除现有标注
      if (this.singleRectMode) {
        this.clearAnnotations()
      }

      // 创建标注对象
      const annotation = {
        id: marker.id || 'default-selection',
        marker: { ...marker },
        element: null,
        controlPoints: [],
        rotateHandle: null
      }

      // 创建SVG元素
      this.createAnnotationElement(annotation)

      // 添加到标注列表
      this.annotations.push(annotation)
      this.currentAnnotation = annotation

      // 更新位置
      this.updateAnnotationElement(annotation)

      // 设置交互状态
      this.updateAnnotationInteraction(annotation)

      console.log('【AnnotationManager】添加矩形标注:', annotation)

      return annotation
    } catch (e) {
      console.error('【AnnotationManager】添加矩形标注失败:', e)
      return null
    }
  }

  /**
   * 创建标注元素
   */
  createAnnotationElement (annotation) {
    // 创建组元素
    const group = document.createElementNS('http://www.w3.org/2000/svg', 'g')
    group.id = `annotation-${annotation.id}`
    group.style.cursor = 'move'

    // 创建矩形
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
    rect.setAttribute('fill', 'rgba(0, 168, 255, 0.1)')
    rect.setAttribute('stroke', '#00a8ff')
    rect.setAttribute('stroke-width', '2')
    rect.style.cursor = 'move'

    group.appendChild(rect)

    // 创建控制点
    this.createControlPoints(annotation, group)

    // 创建旋转控制点
    this.createRotateHandle(annotation, group)

    // 绑定事件
    this.bindAnnotationEvents(annotation, group)

    // 添加到SVG覆盖层
    this.svgOverlay.appendChild(group)

    annotation.element = group
  }

  /**
   * 创建控制点
   */
  createControlPoints (annotation, group) {
    annotation.controlPoints = []

    // 创建8个控制点（4个角点 + 4个边中点）
    for (let i = 0; i < 8; i++) {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
      circle.setAttribute('r', '6')
      circle.setAttribute('fill', '#00a8ff')
      circle.setAttribute('stroke', '#fff')
      circle.setAttribute('stroke-width', '2')
      circle.style.cursor = i % 2 === 0 ? 'nw-resize' : 'n-resize' // 角点用对角线光标，边中点用直线光标

      // 绑定控制点事件
      this.bindControlPointEvents(circle, annotation, i)

      group.appendChild(circle)
      annotation.controlPoints.push(circle)
    }
  }

  /**
   * 创建旋转控制点
   */
  createRotateHandle (annotation, group) {
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
    circle.setAttribute('r', '6')
    circle.setAttribute('fill', '#ff6b6b')
    circle.setAttribute('stroke', '#fff')
    circle.setAttribute('stroke-width', '2')
    circle.style.cursor = 'crosshair'

    // 绑定旋转事件
    this.bindRotateEvents(circle, annotation)

    group.appendChild(circle)
    annotation.rotateHandle = circle
  }

  /**
   * 绑定标注事件
   */
  bindAnnotationEvents (annotation, element) {
    let startPoint = null
    let startMarker = null

    // 鼠标按下事件
    element.addEventListener('mousedown', (e) => {
      if (this.mode !== 'edit') return

      e.stopPropagation()
      e.preventDefault()

      this.isDragging = true

      // 获取鼠标在视口中的位置
      const rect = this.viewer.element.getBoundingClientRect()
      const clientX = e.clientX - rect.left
      const clientY = e.clientY - rect.top
      startPoint = this.viewer.viewport.pointFromPixel({ x: clientX, y: clientY })
      startMarker = { ...annotation.marker }

      // 添加全局事件监听器
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    })

    const handleMouseMove = (e) => {
      if (!this.isDragging || !startPoint) return

      e.stopPropagation()
      e.preventDefault()

      // 获取当前鼠标位置
      const rect = this.viewer.element.getBoundingClientRect()
      const clientX = e.clientX - rect.left
      const clientY = e.clientY - rect.top
      const currentPoint = this.viewer.viewport.pointFromPixel({ x: clientX, y: clientY })

      // 计算移动距离（视口坐标）
      const deltaX = currentPoint.x - startPoint.x
      const deltaY = currentPoint.y - startPoint.y

      // 转换为图像坐标
      const imageDelta = this.viewer.viewport.deltaPointsFromPixels({ x: deltaX, y: deltaY })

      // 更新标记位置
      annotation.marker.cx = startMarker.cx + imageDelta.x
      annotation.marker.cy = startMarker.cy + imageDelta.y

      // 更新显示
      this.updateAnnotationElement(annotation)

      // 触发回调
      this.onMarkerChange(this.getMarkers())
      this.onSelectionChange(annotation.marker)
    }

    const handleMouseUp = (e) => {
      if (!this.isDragging) return

      e.stopPropagation()
      e.preventDefault()

      this.isDragging = false
      startPoint = null
      startMarker = null

      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)

      // 触发最终回调
      this.onMarkerChange(this.getMarkers())
      this.onSelectionChange(annotation.marker)
    }
  }

  /**
   * 绑定控制点事件
   */
  bindControlPointEvents (circle, annotation, index) {
    let startPoint = null
    let startMarker = null

    circle.addEventListener('mousedown', (e) => {
      if (this.mode !== 'edit') return

      e.stopPropagation()
      e.preventDefault()

      this.isResizing = true

      // 获取鼠标在视口中的位置
      const rect = this.viewer.element.getBoundingClientRect()
      const clientX = e.clientX - rect.left
      const clientY = e.clientY - rect.top
      startPoint = this.viewer.viewport.pointFromPixel({ x: clientX, y: clientY })
      startMarker = { ...annotation.marker }

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    })

    const handleMouseMove = (e) => {
      if (!this.isResizing || !startPoint) return

      e.stopPropagation()
      e.preventDefault()

      // 获取当前鼠标位置
      const rect = this.viewer.element.getBoundingClientRect()
      const clientX = e.clientX - rect.left
      const clientY = e.clientY - rect.top
      const currentPoint = this.viewer.viewport.pointFromPixel({ x: clientX, y: clientY })

      // 计算新的尺寸
      this.updateAnnotationSize(annotation, startMarker, startPoint, currentPoint, index)

      // 更新显示
      this.updateAnnotationElement(annotation)

      // 触发回调
      this.onMarkerChange(this.getMarkers())
      this.onSelectionChange(annotation.marker)
    }

    const handleMouseUp = (e) => {
      if (!this.isResizing) return

      e.stopPropagation()
      e.preventDefault()

      this.isResizing = false
      startPoint = null
      startMarker = null

      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)

      // 触发最终回调
      this.onMarkerChange(this.getMarkers())
      this.onSelectionChange(annotation.marker)
    }
  }

  /**
   * 绑定旋转事件
   */
  bindRotateEvents (circle, annotation) {
    let startPoint = null
    let startAngle = 0

    circle.addEventListener('mousedown', (e) => {
      if (this.mode !== 'edit') return

      e.stopPropagation()
      e.preventDefault()

      this.isRotating = true

      // 获取鼠标在视口中的位置
      const rect = this.viewer.element.getBoundingClientRect()
      const clientX = e.clientX - rect.left
      const clientY = e.clientY - rect.top
      startPoint = this.viewer.viewport.pointFromPixel({ x: clientX, y: clientY })
      startAngle = annotation.marker.angle || 0

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    })

    const handleMouseMove = (e) => {
      if (!this.isRotating || !startPoint) return

      e.stopPropagation()
      e.preventDefault()

      // 获取当前鼠标位置
      const rect = this.viewer.element.getBoundingClientRect()
      const clientX = e.clientX - rect.left
      const clientY = e.clientY - rect.top
      const currentPoint = this.viewer.viewport.pointFromPixel({ x: clientX, y: clientY })

      // 计算旋转角度
      const centerPoint = this.viewer.viewport.imageToViewportCoordinates(annotation.marker.cx, annotation.marker.cy)

      const angle1 = Math.atan2(startPoint.y - centerPoint.y, startPoint.x - centerPoint.x)
      const angle2 = Math.atan2(currentPoint.y - centerPoint.y, currentPoint.x - centerPoint.x)

      let deltaAngle = (angle2 - angle1) * 180 / Math.PI
      let newAngle = startAngle + deltaAngle

      // 限制角度在0-90度范围内
      newAngle = clamp(newAngle, 0, 90)

      annotation.marker.angle = newAngle

      // 更新显示
      this.updateAnnotationElement(annotation)

      // 触发回调
      this.onMarkerChange(this.getMarkers())
      this.onSelectionChange(annotation.marker)
    }

    const handleMouseUp = (e) => {
      if (!this.isRotating) return

      e.stopPropagation()
      e.preventDefault()

      this.isRotating = false
      startPoint = null

      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)

      // 触发最终回调
      this.onMarkerChange(this.getMarkers())
      this.onSelectionChange(annotation.marker)
    }
  }

  /**
   * 更新标注尺寸
   */
  updateAnnotationSize (annotation, startMarker, startPoint, currentPoint, controlIndex) {
    // 计算移动距离
    const deltaX = currentPoint.x - startPoint.x
    const deltaY = currentPoint.y - startPoint.y

    // 转换为图像坐标
    const imageDelta = this.viewer.viewport.deltaPointsFromPixels({ x: deltaX, y: deltaY })

    // 根据控制点索引更新尺寸
    let newWidth = startMarker.sw
    let newHeight = startMarker.sh

    if (controlIndex % 2 === 0) {
      // 角点 - 同时调整宽度和高度
      if (controlIndex === 0 || controlIndex === 6) {
        // 左侧角点
        newWidth = Math.max(50, startMarker.sw - imageDelta.x * 2)
      } else {
        // 右侧角点
        newWidth = Math.max(50, startMarker.sw + imageDelta.x * 2)
      }

      if (controlIndex === 0 || controlIndex === 2) {
        // 上侧角点
        newHeight = Math.max(50, startMarker.sh - imageDelta.y * 2)
      } else {
        // 下侧角点
        newHeight = Math.max(50, startMarker.sh + imageDelta.y * 2)
      }
    } else {
      // 边中点 - 只调整一个维度
      if (controlIndex === 1 || controlIndex === 5) {
        // 上中/下中 - 调整高度
        if (controlIndex === 1) {
          newHeight = Math.max(50, startMarker.sh - imageDelta.y * 2)
        } else {
          newHeight = Math.max(50, startMarker.sh + imageDelta.y * 2)
        }
      } else {
        // 左中/右中 - 调整宽度
        if (controlIndex === 7) {
          newWidth = Math.max(50, startMarker.sw - imageDelta.x * 2)
        } else {
          newWidth = Math.max(50, startMarker.sw + imageDelta.x * 2)
        }
      }
    }

    annotation.marker.sw = newWidth
    annotation.marker.sh = newHeight
  }

  /**
   * 移动标注
   * @param {String} id 标注ID
   * @param {Number} dx X方向移动距离
   * @param {Number} dy Y方向移动距离
   */
  moveAnnotation (id, dx, dy) {
    const annotation = this.annotations.find(a => a.id === id)
    if (!annotation) return

    annotation.marker.cx += dx
    annotation.marker.cy += dy

    this.updateAnnotationElement(annotation)
    this.onMarkerChange(this.getMarkers())
    this.onSelectionChange(annotation.marker)
  }

  /**
   * 旋转标注
   * @param {String} id 标注ID
   * @param {Number} angle 角度
   */
  rotateAnnotation (id, angle) {
    const annotation = this.annotations.find(a => a.id === id)
    if (!annotation) return

    annotation.marker.angle = clamp(angle, 0, 90)

    this.updateAnnotationElement(annotation)
    this.onMarkerChange(this.getMarkers())
    this.onSelectionChange(annotation.marker)
  }

  /**
   * 获取所有标记
   * @returns {Array} 标记数组
   */
  getMarkers () {
    return this.annotations.map(annotation => ({ ...annotation.marker }))
  }

  /**
   * 销毁管理器
   */
  destroy () {
    // 清除所有标注
    this.clearAnnotations()

    // 移除SVG覆盖层
    if (this.svgOverlay && this.svgOverlay.parentNode) {
      this.svgOverlay.parentNode.removeChild(this.svgOverlay)
    }

    // 清理引用
    this.viewer = null
    this.svgOverlay = null
    this.annotations = []
    this.currentAnnotation = null
  }
}
