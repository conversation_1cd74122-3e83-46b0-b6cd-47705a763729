<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      title="处理备注"
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template>
          <el-form-item v-if="handleStatus" label="处理措施" prop="notes">
            {{handleStatus}}
          </el-form-item>
          <el-form-item v-if="handleNote" label="处理备注" prop="notes">
            {{handleNote}}
          </el-form-item>
          <el-form-item v-if="returnImgList.length > 0" label="图片说明">
            <img
              :key="index"
              :src="pic.ffileAbsolutePath"
              v-for="(pic, index) in returnImgList"
              style="width: 100px; height: 100px; margin: 2px;" @click="handlePictureCardPreview(pic.ffileAbsolutePath)"/>
            <el-dialog  :visible.sync="dialogVisible" title="图片预览" width="500px" append-to-body>
              <img :src="dialogImageUrl" width="100%" alt="">
            </el-dialog>
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: Number || String
    }
  },
  data () {
    return {
      statusOptions: {
        2: '已返样',
        3: '已销毁',
        4: '到期销毁',
        5: '延期保存'
      },
      handleStatus: '',
      handleNote: '',
      returnImgList: [],
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  methods: {
    handleOpen () {
      this.getRejectSeason()
    },
    getRejectSeason () {
      this.$ajax({
        url: '/sample/t7_return//get_t7_return_order_remark',
        method: 'get',
        data: {
          orderId: this.orderId
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.handleStatus = this.statusOptions[data.fhandleStatus]
          this.handleNote = data.fhandleNote
          this.returnImgList = data.returnImgList
        }
      })
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped></style>
