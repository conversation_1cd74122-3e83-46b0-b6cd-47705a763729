<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="样本科研导入"
      width="500px"
      @open="handleOpen">
      <div class="upLoad">
        <span>绘图文件：</span>
        <el-upload
          ref="upload"
          :disabled="disabled"
          :on-success="handleOnSuccess"
          :on-error="handleOnError"
          :data="uploadParams"
          :auto-upload="false"
          :limit="1"
          :file-list="fileList"
          :before-upload="handleBeforeUpload"
          :action="uploadUrl">
          <span class="btn"><i class="el-icon-upload"></i>上传文件</span>
          <span slot="tip" class="el-upload__tip">仅支持Excel</span>
        </el-upload>
      </div>
      <div style="margin-top: 10px;">
        <label style="font-size: 14px;">所属生产区：</label>
        <el-select v-model="uploadParams.productArea" size="mini">
          <el-option label="北京" value="1"></el-option>
          <el-option label="深圳" value="2"></el-option>
        </el-select>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
export default {
  name: 'sampleArrivalConfirmImportDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      uploadUrl: constants.JS_CONTEXT + '/sample/confirm/import_sample_sheet',
      uploadParams: {
        productArea: ''
      },
      fileList: [],
      loading: false,
      disabled: false
    }
  },
  methods: {
    handleOpen () {},
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res.statusCode === this.SUCCESS_CODE) {
        let data
        if (res.data.url) {
          data = res.data.url
        } else if (res.data.json) {
          data = res.data.json
        }
        this.$emit('dialogConfirmEvent', data, this.needlePlotParams.gene)
      } else {
        this.$msg.err(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        let vaildSize = size <= 10 * 1024 * 1024 * 8
        if (!vaildSize) {
          this.loading = false
          this.$msg.err('文件大小超过限制，无法上传')
        }
        return vaildSize
      } else {
        this.loading = false
        this.$msg.err('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$msg.err('上传出现错误')
    }
  }
}
</script>

<style scoped>

</style>
