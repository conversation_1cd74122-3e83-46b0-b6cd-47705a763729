<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="任务列表"
      width="1000px"
      @open="handleOpen">
      <div class="buttonGroup" style="margin: 10px 0;">
        <el-button type="primary" size="mini" :loading="pushDeliveryLoading" @click="handlePushDelivery">推送交付</el-button>
      </div>
      <el-table
        ref="table"
        :data="tableData"
        height="50vh"
        style="width: 100%;"
        class="table"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column prop="taskName" min-width="100" label="任务名称" show-overflow-tooltip></el-table-column>
        <el-table-column min-width="140" label="任务状态">
          <template slot-scope="scope">
            <span :class="scope.row.deliveryStatusClass">{{scope.row.deliveryStatusText}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sampleNum" label="样本数量" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="libNum" label="文库数量" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskCreateTime" label="任务生成时间" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sendTime" label="投递时间" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deliveryTime" label="交付时间" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="delieryPerson" label="投递人" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskRoute" label="任务路径" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" label="操作" min-width="100">
          <template slot-scope="scope">
            <span class="link" @click="handlePreview(scope.row.id)">查看样本</span>
          </template>
        </el-table-column>
      </el-table>
      <sample-list-dialog
        :pvisible="sampleListDialogVisible"
        :order-code="orderCode"
        :task-id="taskId"
        @dialogCloseEvent="sampleListDialogVisible = false"/>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import sampleListDialog from './sampleListDialog'
export default {
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {
    sampleListDialog
  },
  props: {
    pdata: {
      type: Object
    }
  },
  data () {
    return {
      sampleListDialogVisible: false,
      pushDeliveryLoading: false,
      uploadStatusLoading: false,
      orderCode: '',
      taskId: '',
      selectedRows: new Map(),
      statusOptions: {
        0: {
          text: '未投递',
          class: ''
        },
        1: {
          text: '交付中',
          class: 'pending-color'
        },
        4: {
          text: '分析任务提交失败',
          class: 'fail-color'
        },
        5: {
          text: '分析任务运行成功',
          class: 'success-color'
        },
        6: {
          text: '分析任务运行失败',
          class: 'fail-color'
        },
        7: {
          text: '云上传成功',
          class: 'success-color'
        },
        8: {
          text: '云上传失败',
          class: 'fail-color'
        },
        9: {
          text: '投递失败',
          class: 'fail-color'
        }
      }
    }
  },
  methods: {
    handleOpen () {
      this.sampleListDialogVisible = false
      this.selectedRows.clear()
      this.orderCode = this.pdata.orderCode
      this.getData()
    },
    // 推送交付
    async handlePushDelivery () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何任务')
        return
      }
      let rows = [...this.selectedRows.values()] || []
      if (rows.some(v => ![9, 6, 4, 7, 0].includes(v.taskState))) {
        this.$message.error('请勾选未投递/投递失败/分析任务提交失败/分析任务运行失败/云上传成功状态的任务推送交付！')
        return
      }
      await this.$confirm('确认继续交付吗，如需撤销请在吉云进行操作？', '推送交付', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      })
      const taskIdList = rows.map(v => v.id)
      this.pushDeliveryLoading = true
      this.$ajax({
        url: '/order/delivery/push_delivery_data',
        data: {
          taskIdList: taskIdList
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('投递成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.pushDeliveryLoading = false
      })
    },
    // 获取表格数据
    getData () {
      this.$ajax({
        url: '/order/delivery/get_sub_order_delivery_list',
        data: {
          subOrderSampleId: this.pdata.id
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = []
          let data = res.data || []
          data.forEach((v, i) => {
            let taskStateObj = this.statusOptions[v.taskState] || {
              text: '',
              class: ''
            }
            let item = {
              taskState: v.taskState,
              taskName: `任务${i + 1}`,
              deliveryStatusClass: taskStateObj.class,
              deliveryStatusText: taskStateObj.text,
              id: v.taskId,
              sampleNum: v.sampleNum,
              libNum: v.libNum,
              taskCreateTime: v.taskCreateTime,
              sendTime: v.sendTime,
              deliveryTime: v.deliveryTime,
              delieryPerson: v.delieryPerson,
              remark: v.remark,
              taskRoute: v.taskRoute
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 更新状态
    async handleUploadStatus () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何任务')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.warning('只能选择一个任务')
        return
      }
      let rows = [...this.selectedRows.values()] || []
      if (rows.some(v => [2, 20, 3].includes(v.taskState))) {
        this.$message.error('请勾选未交付/交付失败状态的任务！')
        return
      }
      const {value} = await this.$prompt('为关联交付数据结果，请填写吉云任务的申请单号？', '更新状态', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info',
        inputValidator: function (value) {
          if (!value || !value.trim()) {
            return '请填写吉云任务的申请单号'
          }
        }
      })
      await this.$confirm('确定把任务设置为已交付状态吗，更新后不能撤销！', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.uploadStatusLoading = true
      this.$ajax({
        url: '/order/delivery/push_delivery_data',
        data: {
          taskId: rows[0].id,
          jiyunApplyNum: value
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.uploadStatusLoading = false
      })
    },
    handlePreview (id) {
      this.taskId = id
      this.sampleListDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-dialog__body{
    padding: 0 20px 30px !important;
  }
  .delivery-color {
    color: $color
  }
  .success-color {
    color: $success-color
  }
  .fail-color {
    color: $fail-color
  }
</style>
