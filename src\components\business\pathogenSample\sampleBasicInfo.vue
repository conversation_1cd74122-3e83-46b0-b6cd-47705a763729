<template>
  <div>
    <el-form ref="form" :model="form" :disabled="isDisabled" :rules="rules" :inline-message="true" label-suffix=":" label-width="150px">
      <h4 class="model-title">样本信息</h4>
      <div class="flex">
        <el-form-item prop="sampleCode" label="样例编号">
          <el-input v-model.trim="form.sampleCode" v-if="!isDetail" size="mini" disabled></el-input>
          <span v-else>{{form.sampleCode}}</span>
        </el-form-item>
        <el-form-item prop="productName" label="产品/项目名称">
          <el-input v-model.trim="form.productName" v-if="!isDetail" size="mini" disabled></el-input>
          <span v-else>{{form.productName}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="sampleType" label="样本类型">
          <el-select v-model.trim="form.sampleType" v-if="!isDetail" size="mini" disabled></el-select>
          <span v-else>{{form.sampleType}}</span>
        </el-form-item>
        <el-form-item prop="sampleSize" label="采集量">
          <el-input v-model.trim="form.sampleSize" v-if="!isDetail" size="mini" disabled></el-input>
          <span v-else>{{form.sampleSize}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="collectDate" label="采集日期">
          <el-date-picker v-model.trim="form.collectDate" v-if="!isDetail" size="mini" disabled></el-date-picker>
          <span v-else>{{form.collectDate}}</span>
        </el-form-item>
        <el-form-item prop="inspectDate" label="送检日期">
          <el-date-picker
            v-model.trim="form.inspectDate"
            :disabled="isDisabled"
            v-if="!isDetail"
            value-format="yyyy-MM-dd"
            type="date"
            size="mini">
          </el-date-picker>
          <span v-else>{{form.inspectDate}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="sampleQuality" label="样本质量">
          <el-input v-model.trim="form.sampleQuality" v-if="!isDetail" size="mini" disabled></el-input>
          <span v-else>{{form.sampleQuality}}</span>
        </el-form-item>
        <el-form-item prop="receiverDate" label="接收日期">
          <el-date-picker v-model.trim="form.receiverDate" v-if="!isDetail" size="mini" disabled></el-date-picker>
          <span v-else>{{form.receiverDate}}</span>
        </el-form-item>
      </div>
      <h4 class="model-title">患者信息</h4>
      <div class="flex">
        <el-form-item prop="name" label="姓名">
          <el-input v-model.trim="form.name" :disabled="isDisabled" v-if="!isDetail" size="mini"></el-input>
          <span v-else>
            <desensitization :info="form.name" type="name" is-detail></desensitization>
          </span>
        </el-form-item>
        <el-form-item prop="sex" label="性别">
          <el-radio-group v-model="form.sex" v-if="!isDetail">
            <el-radio :label="0">男</el-radio>
            <el-radio :label="1">女</el-radio>
            <!--<el-radio :label="2">不详</el-radio>-->
          </el-radio-group>
          <span v-else>{{form.sex ? '女' : '男'}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="cardType" label="证件类型">
            <el-select v-model.trim="form.cardType" v-if="!isDetail" filterable allow-create clearable placeholder="请选择" style="width: 100%;" @change="handleCardTypeChange">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in cardTypeList">
              </el-option>
            </el-select>
          <span v-else>{{cardTypeText}}</span>
        </el-form-item>
        <el-form-item prop="identityNumber" label="证件号">
          <el-input v-model.trim="form.identityNumber" :disabled="isDisabled" v-if="!isDetail" size="mini" @blur="handleIdCardBlur"></el-input>
          <span v-else>
            <desensitization :info="form.identityNumber" type="idCard" is-detail></desensitization></span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="age" label="年龄">
          <el-input v-model.trim="form.age" :disabled="isDisabled" v-if="!isDetail" size="mini"></el-input>
          <span v-else>{{form.age}}</span>
        </el-form-item>
        <el-form-item prop="birthday" label="出生年月">
          <div class="flex">
            <my-date-picker v-model="form.birthday" :is-disabled="isDisabled" v-if="!isDetail"></my-date-picker>
            <span v-else>{{form.birthday}}</span>
          </div>
        </el-form-item>
      </div>
      <el-form-item prop="phone" label="联系电话">
        <el-input v-model.trim="form.phone" :disabled="isDisabled" size="mini"></el-input>
      </el-form-item>
      <h4 class="model-title">送检信息</h4>
      <div class="flex">
        <el-form-item prop="admissionNum" label="住院号">
          <el-input v-model.trim="form.admissionNum" :disabled="isDisabled" v-if="!isDetail" size="mini"></el-input>
          <span v-else>{{form.admissionNum}}</span>
        </el-form-item>
        <el-form-item prop="bedNum" label="床号">
          <el-input v-model.trim="form.bedNum" :disabled="isDisabled" v-if="!isDetail" size="mini"></el-input>
          <span v-else>{{form.bedNum}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="medicalRecordNumber" label="病历号">
          <el-input v-if="!isDetail" v-model.trim="form.medicalRecordNumber" size="mini" :disabled="isDisabled"></el-input>
          <span v-else>{{form.medicalRecordNumber}}</span>
        </el-form-item>
        <el-form-item prop="specimenNo" label="标本号">
          <el-input v-if="!isDetail" v-model.trim="form.specimenNo" size="mini" :disabled="isDisabled"></el-input>
          <span v-else>{{form.specimenNo}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="inspectionDepart" label="送检单位">
          <el-input v-model.trim="form.inspectionDepart" v-if="!isDetail" size="mini" disabled></el-input>
          <span v-else>{{form.inspectionDepart}}</span>
        </el-form-item>
        <el-form-item prop="insertOffice" label="送检科室">
          <el-input v-model.trim="form.insertOffice" v-if="!isDetail" size="mini" disabled></el-input>
          <span v-else>{{form.insertOffice}}</span>
        </el-form-item>
      </div>
      <el-form-item prop="insertDoctor" label="送检医生">
        <el-input v-model.trim="form.insertDoctor" v-if="!isDetail" size="mini" disabled></el-input>
        <span v-else>{{form.insertDoctor}}</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import util from '../../../util/util'

export default {
  name: 'sampleBasicInfo',
  components: {},
  props: {
    isDisabled: {
      type: Boolean,
      default: false
    },
    sampleBasicInfo: {
      type: Object,
      required: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    form: {
      handler (oldValue, newValue) {
        this.$emit('formChange', this.form)
      },
      deep: true
    },
    sampleBasicInfo: {
      handler (oldValue, newValue) {
        if (this.sampleBasicInfo && JSON.stringify(this.sampleBasicInfo) !== '{}') {
          this.form = this.sampleBasicInfo
          this.rules.idcard = this.rules.idcard || []
          if (this.rules.idcard[0]) {
            this.rules.idcard[0].required = this.form.cardType !== 0
          }
        }
      },
      deep: true
    }
  },
  computed: {
    cardTypeText () {
      const cardType = this.cardTypeList.find(v => v.value === this.form.cardType) || {}
      return this.form.cardType !== '-' ? cardType.label : '-'
    }
  },
  data () {
    const validateBirthday = (rule, value, callback) => {
      if (value.length === 4) {
        callback(new Error('请输入月份'))
      }
      callback()
    }
    // 校验证件号
    const validateIdCard = (rule, value, callback) => {
      const rules = {
        1: /^[0-9a-zA-Z]*$/g,
        2: /(^[EeKkGgDdSsPpHh]\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\d{7}$)/g,
        3: /^[a-zA-Z0-9]{7,21}$/g,
        4: /^[C]\d{8}$|^[C][a-hA-Hj-nJ-Np-zP-Z][0-9]{7}$/g
      }
      if ([0, 5].includes(this.form.cardType)) {
        callback()
        return
      }
      console.log([4, 5].includes(this.form.cardType), this.form.cardType, rules[this.form.cardType], this.form)
      if (!rules[this.form.cardType].test(this.form.identityNumber)) {
        callback(new Error('证件类型错误'))
      }
      callback()
    }
    return {
      form: {
        sampleCode: '',
        productName: '',
        sampleType: '',
        sampleSize: '',
        sampleNum: '',
        collectDate: '',
        inspectDate: '',
        sampleQuality: '',
        receiverDate: '',
        name: '',
        age: '',
        sex: '',
        identityNumber: '',
        cardType: '',
        birthday: '',
        admissionNum: '', // 住院号
        bedNum: '', // 床号
        phone: '',
        inspectionDepart: '', // 送检单位
        insertOffice: '', // 送检科室
        insertDoctor: '', // 送检医生
        medicalRecordNumber: '', // 病历号
        specimenNo: '' // 标本号
      },
      cardTypeList: [
        {
          value: 1,
          label: '居民身份证'
        },
        {
          value: 2,
          label: '护照'
        },
        {
          value: 3,
          label: '军官证'
        },
        {
          value: 4,
          label: '港澳台通行证'
        },
        {
          value: 5,
          label: '社保卡'
        },
        {
          value: 0,
          label: '其他'
        }
      ],
      rules: {
        name: [{ required: true, message: '请输入输入姓名', trigger: ['change', 'blur'] }],
        age: [{ required: true, message: '请输入输入年龄', trigger: ['change', 'blur'] }],
        identityNumber: [
          { required: true, message: '请输入证件号', trigger: ['change', 'blur'] },
          {validator: validateIdCard, trigger: 'blur'}
        ],
        cardType: [
          { required: true, message: '请输入证件类型', trigger: ['change', 'blur'] }
        ],
        birthday: [
          { required: true, message: '请输入出生年月', trigger: ['change', 'blur'] },
          { validator: validateBirthday, trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  methods: {
    // 根据身份增回显 出生年月等数据
    handleIdCardBlur () {
      let pass = util.identityNumberValid(this.form.identityNumber).pass
      if (pass) {
        let year = this.form.identityNumber.substring(6, 10)
        let month = this.form.identityNumber.substring(10, 12)
        let day = this.form.identityNumber.substring(12, 14)
        let sexType = Number(this.form.identityNumber.substring(16, 17))
        let sex = 0
        if (sexType % 2 === 0) {
          sex = 1
        }
        this.$set(this.form, 'birthday', `${year}-${month}-${day}`)
        this.form.age = util.getAge(this.form.birthday)
        this.$set(this.form, 'sex', sex)
      }
    },
    // 证件类型切换
    handleCardTypeChange () {
      this.rules.idcard[0].required = this.form.cardType !== 0
    }
  }
}
</script>

<style scoped>
.model-title {
  border-left: 3px solid deepskyblue;
  padding: 0 10px;
  margin: 10px 0;
}
.flex {
  display: flex;
  justify-content: space-between;
}

</style>
