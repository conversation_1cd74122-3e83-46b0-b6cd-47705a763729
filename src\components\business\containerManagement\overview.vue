<template>
  <el-scrollbar style="height: 100%">
    <!--图形部分-->
    <div class="chart-row">
      <div>
        <div id="pie" style="width: 100%;height:300px;"></div>
      </div>
      <div>
        <div id="bar" style="width: 100%;height: 300px"></div>
      </div>
    </div>
    <!--容器部分-->
    <div class="container-row">
      <h3 class="title">容器信息</h3>
      <div class="container-content">
        <!--工具条-->
        <scroll-pane :scroll-height="60">
          <div class="toolbar">
            <div class="func-btn-group">
              <template v-if="$setAuthority('011001001', 'buttons')">
                <el-button size="mini" type="primary" @click="toCreateContainerPage">新增容器</el-button>
              </template>
              <el-button-group>
                <el-button
                  :type="type === 'card' ? 'primary' : ''"
                  size="mini"
                  @click="type='card'"><icon-svg icon-class="icon-qiapian"></icon-svg></el-button>
                <el-button
                  :type="type === 'table' ? 'primary' : ''"
                  size="mini"
                  @click="type='table'"><icon-svg icon-class="icon-liebiao"></icon-svg></el-button>
              </el-button-group>
            </div>
            <div class="form-btn-group">
              <div class="label-input">
                <label>容器名称:</label>
                <el-input v-model="containerName" placeholder="请输入" style="width: 200px;" size="mini"></el-input>
              </div>
              <div>
                <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
                <el-button size="mini" @click="handleResetForm">重置</el-button>
              </div>
            </div>
          </div>
        </scroll-pane>
        <!--列表-->
        <div class="table-path">
          <div v-if="type === 'table'">
            <el-table
              :data="tableData"
              style="width: 100%;"
              height="350">
              <el-table-column type="index" width="70" label="序号"></el-table-column>
              <template v-for="item in tableLayout">
                <el-table-column
                  :key="item.prop"
                  :prop="item.prop"
                  :label="item.label"
                  min-width="180"></el-table-column>
              </template>
              <el-table-column label="操作" width="180" fixed="right" >
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="toContainerDetail(scope.row.id)">查看详情</el-button>
                  <el-button
                    v-if="$setAuthority('011001002', 'buttons')"
                    size="mini" @click="handleDelContainer(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="type === 'card'">
            <el-scrollbar class="card-container">
              <div v-if="tableData.length === 0" class="card-no-data">
                <p>暂无数据</p>
              </div>
              <div v-else class="card-row">
                <template v-for="item in tableData">
                  <div :key="item.id" class="card"  @click="toContainerDetail(item.id)">
                    <div>
                      <div>
                        <el-image :src="require('../../../assets/container.png')"></el-image>
                      </div>
                      <p>剩余孔位：{{item.availableHole}}</p>
                      <p>存储总容量：{{item.totalCapacity}}</p>
                    </div>
                    <div>
                      <p style="color: #FFB236;font-weight: 600;height: auto;line-height: 1.5;">{{item.name}}</p>
                      <p>{{showContainerType(item.type)}}</p>
                      <p>{{item.lab}}</p>
                      <p>温度：{{item.temperature}}</p>
                      <p>存储占比：{{item.storagePercentage}}</p>
                    </div>
                    <div>
                      <el-button
                        v-if="$setAuthority('011001002', 'buttons')" type="text"
                        size="mini"
                        @click.stop="handleDelContainer(item.id)">
                        <i class="el-icon-delete" style="font-size: 22px;"></i></el-button>
                    </div>
                  </div>
                </template>
                <!--占位，保证容器可以正常对齐-->
                <template v-for="item in tableData">
                  <i :key="'i' + item.id" style="display: block;width: 370px;"></i>
                </template>
              </div>
            </el-scrollbar>
          </div>
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
          </el-pagination>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
// import num from './components/cc'
import echarts from 'echarts'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import contants from '../../../util/constants'
import IconSvg from '../../common/iconSvg'
import ScrollPane from '../../common/scrollPane'
export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData],
  components: {ScrollPane, IconSvg},
  mounted () {
    this.init()
  },
  data () {
    return {
      containerName: '',
      containerNameSubmit: '',
      tableLayout: [
        {label: '所属实验室', prop: 'lab'},
        {label: '容器名称', prop: 'name'},
        {label: '容器类型', prop: 'type'},
        {label: '温度', prop: 'temperature'},
        {label: '存储总容量', prop: 'totalCapacity'},
        {label: '剩余孔位', prop: 'availableHole'},
        {label: '存储占比', prop: 'storagePercentage'}
      ],
      type: 'card', // 展示类型 table || card
      containerTypeOptions: contants.CONTAINER_TYPE_OPTIONS
    }
  },
  methods: {
    showErrorDialog () {
      this.$showErrorDialog({
        tableData: [{title: 1, actualContent: 2, errorReason: 3}]
      })
    },
    init () {
      this.getContainerStorageInfo()
      this.getSampleTypeStorageInfo()
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/sample/container/get_container_list',
        data: {
          pageRequest: {
            current: this.currentPage,
            size: this.pageSize
          },
          containerName: this.containerNameSubmit
        },
        loadingDom: '.table-path'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let data = Array.isArray(res.data.rows) ? res.data.rows : []
          this.tableData = []
          data.forEach(v => {
            let item = {
              id: v.fid,
              lab: v.flab,
              type: v.ftype,
              name: `${v.fnickName ? v.fnickName + '-' : ''}${v.fname}`,
              temperature: v.ftemperature,
              totalCapacity: v.ftotalCapacity,
              availableHole: v.favailableHole,
              storagePercentage: v.fstoragePercentage + '%',
              imgUrl: v.fpicture
            }
            if (v.ftype === 'C') {
              item.storagePercentage = '-'
              item.totalCapacity = '-'
              item.availableHole = '-'
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.containerNameSubmit = this.containerName
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.containerName = ''
      this.handleSearch()
    },
    showContainerType (val) {
      let item = this.containerTypeOptions.filter(v => {
        return v.value === val
      })[0]
      if (item) {
        return item.label
      } else {
        return '-'
      }
    },
    // 获取容器占比
    getContainerStorageInfo () {
      this.$ajax({
        url: '/sample/container/get_overall_storage_info',
        method: 'get',
        loadingDom: '#pie'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let resData = res.data || {}
          let data = [
            {value: resData.usedHoleAmount || 0, name: '已用孔位数'},
            {value: resData.availabledHoleAmount || 0, name: '可用孔位数'}
          ]
          this.initPie(data)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取各个样本存储占比
    getSampleTypeStorageInfo () {
      this.$ajax({
        url: '/sample/container/get_storage_percentage_for_sample_type',
        // data: {
        //   flabNo: 0
        // },
        method: 'get',
        loadingDom: '#bar'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          let sample = []
          let thisWeekStock = []
          let existingStock = []
          let maxStock = []
          data.forEach(item => {
            sample.push(item.sampleType)
            thisWeekStock.push(item.thisWeekStock)
            existingStock.push(item.existingStock)
            maxStock.push(item.maxStock)
          })
          this.initBar({sample, thisWeekStock, existingStock, maxStock})
        } else {
          this.$message.error(res.message)
        }
      })
    },
    initPie (data) {
      let option = {
        title: {
          text: '容器整体占比',
          textStyle: {
            color: '#409EFF'
          }
        },
        color: ['#FFB236', '#409EFF'],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}<br>占比 ({d}%)'
        },
        series: [
          {
            type: 'pie',
            radius: '70%',
            center: ['50%', '60%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      let myChart = echarts.init(this.$el.querySelector('#pie'))
      myChart.setOption(option)
    },
    initBar ({sample = [], thisWeekStock = [], existingStock = [], maxStock = []}) {
      let barWidth = 20
      let option = {
        title: {
          text: '各样本存储占比',
          textStyle: {
            color: '#409EFF'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: ['本周入库', '现存样本量', '最大存储量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: sample
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '本周入库',
            type: 'bar',
            barWidth: barWidth,
            data: thisWeekStock
          },
          {
            name: '现存样本量',
            type: 'bar',
            barWidth: barWidth,
            // stack: '容器',
            data: existingStock
          },
          {
            name: '最大存储量',
            type: 'bar',
            barWidth: barWidth,
            // stack: '容器',
            data: maxStock
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 10,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }
        ]
      }
      let myChart = echarts.init(this.$el.querySelector('#bar'))
      myChart.setOption(option)
    },
    toCreateContainerPage () {
      util.openNewPage('/business/sub/createContainer')
    },
    toContainerDetail (id) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'containerId',
        containerId: id
      })
      util.openNewPage('/business/sub/containerDetail')
    },
    // 删除容器
    handleDelContainer (id) {
      this.$confirm('此操作将永久删除该容器, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/sample/container/delete_container',
          method: 'get',
          data: {
            containerId: id
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.handleSearch()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  @mixin card{
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
    color: #303133;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    padding: 20px;
  }
  /*图形行*/
  .chart-row{
    height: 45%;
    & > div{
     @include card;
      div{
        width: 100%;
        height: 300px;
      }
    }
    display: flex;
    & > div:first-child{
      width: 28%;
      margin-right: 20px;
    }
    & > div:last-child{
      width: 72%;
    }
  }
  // 容器部分
  .container-row{
    height: 52%;
    @include card;
    margin-top: 20px;
    padding-bottom: 0;
    .title{
      font-size: 16px;
      font-weight: 600;
      color: $color;
    }
    .container-content{
      .toolbar{
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        & > div{
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
        }
        .func-btn-group{
          flex-shrink: 0;
          width: 300px;
        }
        /*功能按钮组*/
        & > .func-btn-group > .el-button{
          margin-right: 100px;
        }
        // 表单按钮组
        & > .form-btn-group > .label-input{
          label{
            font-weight: 600;
            font-size: 16px;
            margin-right: 20px;
          }
          margin-right: 50px;
        }
      }
      .table-path .card-container{
        height: 340px;
        min-height: 200px;
        .card-no-data{
          height: 400px;
          min-height: 200px;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          p{
            text-align: center;
            font-weight: 600;
          }
        }
        .card-row{
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
        }
        .card{
          display: flex;
          border: 1px solid #ccc;
          border-radius: 10px;
          padding: 5px 10px;
          font-size: 13px;
          margin-top: 15px;
          cursor: pointer;
          & > div{
            /*width: 80px;*/
            $w: 150px;
            &:not(:last-child){
              margin-right: 10px;
              width: 150px;
            }
            & > div{
              height: 80px;
              display: flex;
              align-items: center;
              /deep/ .el-image{
                height: 72px;
                display: block;
                img{
                  display: block;
                  height: 100%;
                  width: auto;
                }
              }
            }
            p{
              height: 30px;
              line-height: 30px;
            }
          }
        }
      }
    }
  }
</style>
