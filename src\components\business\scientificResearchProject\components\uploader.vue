<template>
  <div style="display: flex"
       v-loading="onProgress"
       element-loading-text="文件上传中..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(0, 0, 0, 0.8)">
    <el-upload
      ref="upload"
      :action="uploadUrl"
      :file-list="fileList"
      :headers="headers"
      :on-success="handleOnSuccess"
      :on-error="handleOnError"
      :on-progress="handleProgress"
      :before-upload="handleBeforeUpload"
      :on-preview="handlePictureCardPreview"
      :before-remove="handleRemove"
      :on-remove="() => handleOnSuccess()"
      :on-change="handleChange"
      multiple
      accept="image/jpg,image/jpeg,image/png,.pdf"
      list-type="picture-card"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <div v-if="fileList.length > 3" class="box">
      剩余{{ fileList.length - 3 }}张不做展示
    </div>
    <el-dialog :visible.sync="dialogVisible" title="图片预览" append-to-body>
      <div style="height: 50vh; overflow-y: auto">
        <img :src="dialogImageUrl" style="width: 100%; object-fit: contain" alt="">
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import constants from '@/util/constants'
import {handlePdfToImage} from '../../../../util/imgUtils'

export default {
  model: {
    prop: 'fileList',
    event: 'change'
  },
  props: {
    fileList: Array
  },

  data () {
    return {
      headers: {
        token: Cookies.get('token')
      },
      files: [],
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  methods: {
    // 提交前的函数
    async handleBeforeUpload (file) {
      let name = file.name
      this.onProgress = true
      // 限制文件数量
      let uploadFiles = this.$refs.upload.uploadFiles || [{size: 0}]
      if (uploadFiles.length > 200) {
        this.$message.error('一次性上传的文件数量超过系统限制，不允许上传')
        return Promise.reject(new Error('一次性上传的文件数量超过系统限制，不允许上传'))
      }
      // 限制文件大小
      let {size} = uploadFiles.reduce((pre, next) => {
        let size = pre.size + next.size
        return {size: size}
      }, {size: 0})
      if (size > 1024 * 1024 * 50) {
        this.$message.error('一次性上传的文件大小超过系统限制，不允许上传')
        return Promise.reject(new Error('一次性上传的文件大小超过系统限制，不允许上传'))
      }
      if (file.size > 1024 * 1024 * 30) {
        this.$message.error('文件不能大于30M，请重新上传')
        return Promise.reject(new Error('文件不能大于30M，请重新上传'))
      }
      // 限制文件格式
      if (!/\.(jpg|png|jpeg|pdf)$/.test(name)) {
        this.$message.error('只能上传jpg、png、jpeg、pdf格式文件')
        return Promise.reject(new Error('只能上传jpg、png、jpeg、pdf格式文件'))
      }
      // 处理pdf文件
      if (/\.(pdf)$/.test(name)) {
        let index = uploadFiles.findIndex(v => v.uid === file.uid)
        const raw = await handlePdfToImage(file)
        this.$refs.upload.uploadFiles[index].raw = raw
        this.$refs.upload.uploadFiles[index].name = raw.name
        this.$refs.upload.uploadFiles[index].url = URL.createObjectURL(raw)
        return raw
      }
      return Promise.resolve()
    },
    async setAttachFile () {
      let files = []
      let uploadFiles = this.$refs.upload.uploadFiles || [{size: 0}]
      files = uploadFiles.map(v => {
        if (v.response) {
          v.response.data.url = v.url
          v.response.data.size = v.size
          return v.response.data
        } else {
          return v
        }
      })
      // 文件排序
      files.sort((pre, next) => {
        // 获取文件名称
        let sortByPreName = pre.name || pre.originalFileName
        let sortByNextName = next.name || next.originalFileName
        // 去除文件名称
        sortByPreName = sortByPreName.split('.')[0] || ''
        sortByNextName = sortByNextName.split('.')[0] || ''
        // 获取文件名最后三位
        sortByPreName = sortByPreName.substring(sortByPreName.length - 3, sortByPreName.length)
        sortByNextName = sortByNextName.substring(sortByNextName.length - 3, sortByNextName.length)
        // 按 - 切割字符传
        let sortByPre = sortByPreName.split('-') || []
        let sortByNext = sortByNextName.split('-') || []
        if (sortByPre.length < 2) {
          return 1
        }
        // next next非数字后排
        if (sortByNext.length < 2) {
          return -1
        }
        // 转数字 => 非数字 = NaN
        const sortPre = sortByPre[sortByPre.length - 1] * 1
        const sortNext = sortByNext[sortByNext.length - 1] * 1
        // pre pre非数字后排
        if (isNaN(sortPre)) {
          return 1
        }
        // next next非数字后排
        if (isNaN(sortNext)) {
          return -1
        }
        return sortPre - sortNext
      })
      return files
    },
    handleChange (file) {
    },
    // 提交成功回调
    async handleOnSuccess (res, file, fileList) {
      let files = await this.setAttachFile(file)
      this.onProgress = false
      if (files) {
        this.$emit('change', files)
      }
    },
    async handleConfirmOption (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    // 提交失败回调
    handleOnError (err) {
      this.$message.error('上传出现错误')
      console.log('上传出现错误', err)
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    async handleRemove (file) {
      this.onProgress = false
      if (file.status !== 'ready') {
        await this.handleConfirmOption('是否删除？')
      }
    },
    // 图片预览
    handlePictureCardPreview (file) {
      console.log(file)
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
> > > .el-upload-list__item:nth-child(n + 4) {
  display: none !important;
}

> > > .el-upload--picture-card {
  margin: 0 8px 8px 0;
  float: left;
}

> > > .el-dialog__body {
  overflow: hidden;
}

.box {
  display: flex;
  padding: 30px;
  align-items: center;
  justify-content: center;
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  font-size: 18px;
  color: #c0ccda;
  cursor: pointer;
  text-align: center;
  vertical-align: top;
}
</style>
