<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    title="突变备注"
    width="800px"
    @open="handleOpen"
  >
    <el-form ref="form" :model="form" label-width="90px">
      <div>
        <div v-for="(item, index) in mutationNoteList" :key="index" class="mutation-note-wrapper">
          <el-checkbox v-model="form.checkList[index]" :true-label="1" :false-label="0" :label="item"></el-checkbox>
          <el-form-item label="补充说明:">
            <el-input v-model.trim="form.note[index]" :disabled="!form.checkList[index]" maxlength="100" class="form-width" size="mini" clearable></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
  </el-dialog>
</template>
<script>
import mixins from '../../../../util/mixins'

export default {
  name: 'mutationNoteDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    geneType: {
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false,
      // 图不可信、支持数不足、基线数据库不满足、污染来源、频率不足、其他原因
      mutationNoteList: ['图的可信度', '支持数', '基线数据库', '污染', '突变频率', '其他原因'],
      form: {
        checkList: [],
        note: []
      }
    }
  },
  methods: {
    handleOpen () {
      this.form = {
        checkList: new Array(6).fill(0),
        note: new Array(6).fill('')
      }
      this.getMutationNoteInfo()
    },
    getMutationNoteInfo () {
      this.$ajax({
        url: '/read/wesUnscramble/get_mutation_remark',
        data: {
          fid: this.id,
          geneType: this.geneType
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          const data = result.data
          // const checkKeys = ['isPictureNoReliable', 'isSupportsLack', 'isBasicDataBaseLack', 'isPollutionSource', 'isRateLack']
          const noteKeys = ['pictureNoReliable', 'supportsLack', 'basicDataBaseLack', 'pollutionSource', 'rateLack', 'otherReason']
          // checkKeys.forEach((item, index) => {
          //   this.form.checkList[index] = data[item]
          // })
          noteKeys.forEach((item, index) => {
            this.form.note[index] = data[item] || ''
            this.$set(this.form.checkList, index, data[item] || data[item] === '' ? 1 : 0)
          })
        }
      })
    },
    // 设置提交参数
    setParams () {
      return {
        fid: this.id,
        geneType: this.geneType,
        isPictureNoReliable: this.form.checkList[0],
        pictureNoReliable: this.form.checkList[0] ? this.form.note[0] : '',
        isSupportsLack: this.form.checkList[1],
        supportsLack: this.form.checkList[1] ? this.form.note[1] : '',
        isBasicDataBaseLack: this.form.checkList[2],
        basicDataBaseLack: this.form.checkList[2] ? this.form.note[2] : '',
        isPollutionSource: this.form.checkList[3],
        pollutionSource: this.form.checkList[3] ? this.form.note[3] : '',
        isRateLack: this.form.checkList[4],
        rateLack: this.form.checkList[4] ? this.form.note[4] : '',
        isOtherReason: this.form.checkList[5],
        otherReason: this.form.checkList[5] ? this.form.note[5] : ''
      }
    },
    handleConfirm () {
      this.loading = true
      this.$ajax({
        url: '/read/wesUnscramble/set_mutation_remark',
        data: {
          ...this.setParams()
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功！')
          this.$emit('dialogConfirmEvent')
          this.visible = false
          this.$refs.form.resetFields()
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>
.mutation-note-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*margin-bottom: 10px;*/
}
.form-width {
  width: 300px;
}
</style>
