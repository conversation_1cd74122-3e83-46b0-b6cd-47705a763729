import Vue from 'vue'
import Vuex from 'vuex'
import util from '../util/util'
import Cookies from 'js-cookie'

Vue.use(Vuex)

const state = {
  userInfo: {
    id: '',
    username: '',
    avatar: '',
    emailPrefix: ''
  },
  loginId: null, // 登录的id
  menuStatus: 0, // 默认展开
  deviceSize: 'normal', // normal or mini
  activeIndex: '/business/view/containerManagement',
  pathTabs: [],
  enterLibraryOrder: '', // 入库单号
  containerId: '', // 容器id
  forecastData: null, // 新增预测数据
  dxData: null, // 新增DX数据,
  mutationsData: null, // 新增遗传变异数据
  analysisRsId: null,
  matchCancer: null,
  isPreview: 0,
  libraryOperatingData: null,
  clinicalInfo: {
    sampleBasicId: null,
    sampleNum: null
  },
  templateId: '', // 模板id
  deliveryInfo: { // 物料模块中的确认快递、修改邮件
    id: null,
    type: null
  },
  applicationInfo: { // 物料\宣传品申请详情数据
    id: null,
    type: null,
    needReissue: false
  },
  pixelRatio: null,
  pathogenicType: 0, // 当前病原是什么类型，0-非tNGS，1-tNGS
  sampleQuality: [],
  needUpdated: []
}

const mutations = {
  setValue (state, payload) {
    if (payload.category === 'userInfo') {
      state.userInfo = payload.userInfo
      util.setSessionInfo('userInfo', payload.userInfo)
    } else if (payload.category === 'menuStatus') {
      state.menuStatus = payload.menuStatus
      Cookies.set('menuStatus', payload.menuStatus)
    } else if (payload.category === 'activeIndex') {
      state.activeIndex = payload.activeIndex
      util.setSessionInfo('activeIndex', payload.activeIndex)
    } else if (payload.category === 'deviceSize') {
      state.deviceSize = payload.deviceSize
    } else if (payload.category === 'pathTabs') {
      state.pathTabs = payload.pathTabs
    } else if (payload.category === 'enterLibraryOrder') {
      state.enterLibraryOrder = payload.enterLibraryOrder
      util.setSessionInfo('enterLibraryOrder', payload.enterLibraryOrder)
    } else if (payload.category === 'containerId') {
      state.containerId = payload.containerId
      util.setSessionInfo('containerId', payload.containerId)
    } else if (payload.category === 'loginId') {
      state.loginId = payload.loginId
      util.setSessionInfo('loginId', payload.loginId)
    } else if (payload.category === 'forecastData') {
      state.forecastData = payload.forecastData
      util.setSessionInfo('forecastData', payload.forecastData)
    } else if (payload.category === 'dxData') {
      state.dxData = payload.dxData
      util.setSessionInfo('dxData', payload.dxData)
    } else if (payload.category === 'mutationsData') {
      state.mutationsData = payload.mutationsData
      util.setSessionInfo('mutationsData', payload.mutationsData)
    } else if (payload.category === 'analysisRsId') {
      state.analysisRsId = payload.analysisRsId
      util.setSessionInfo('analysisRsId', payload.analysisRsId)
    } else if (payload.category === 'matchCancer') {
      state.matchCancer = payload.matchCancer
      util.setSessionInfo('matchCancer', payload.matchCancer)
    } else if (payload.category === 'clinicalInfo') {
      state.clinicalInfo = payload.clinicalInfo
      util.setSessionInfo('clinicalInfo', payload.clinicalInfo)
    } else if (payload.category === 'templateId') {
      state.templateId = payload.templateId
      util.setSessionInfo('templateId', payload.templateId)
    } else if (payload.category === 'deliveryInfo') {
      state.deliveryInfo = payload.deliveryInfo
      util.setSessionInfo('deliveryInfo', payload.deliveryInfo)
    } else if (payload.category === 'applicationInfo') {
      state.applicationInfo = payload.applicationInfo
      util.setSessionInfo('applicationInfo', payload.applicationInfo)
    } else if (payload.category === 'libraryOperatingData') {
      state.libraryOperatingData = payload.libraryOperatingData
      util.setSessionInfo('libraryOperatingData', payload.libraryOperatingData)
    } else if (payload.category === 'isPreview') {
      state.isPreview = payload.isPreview
      util.setSessionInfo('isPreview', payload.isPreview)
    } else if (payload.category === 'pathogenicType') {
      state.pathogenicType = payload.pathogenicType
      util.setSessionInfo('pathogenicType', payload.pathogenicType)
    } else if (payload.category === 'sampleQuality') {
      state.sampleQuality = payload.sampleQuality
      util.setSessionInfo('sampleQuality', payload.sampleQuality)
    } else if (payload.category === 'needUpdated') {
      state.needUpdated = payload.needUpdated
      util.setSessionInfo('needUpdated', payload.needUpdated)
    } else if (payload.category === 'pixelRatio') {
      state.pixelRatio = payload.pixelRatio
      util.setSessionInfo('pixelRatio', payload.pixelRatio)
    }
  }
}

const getters = {
  getValue (state) {
    return category => {
      if (category === 'userInfo') { // 用户信息
        let localData = util.getSessionInfo('userInfo')
        if (localData) {
          mutations.setValue(state, {
            category: 'userInfo',
            userInfo: localData
          })
        }
        return state.userInfo
      } else if (category === 'menuStatus') {
        let localData = Number(Cookies.get('menuStatus'))
        if (localData && state.menuStatus === 0) {
          mutations.setValue(state, {
            category: 'menuStatus',
            menuStatus: localData
          })
        }
        return state.menuStatus
      } else if (category === 'activeIndex') {
        let localData = util.getSessionInfo('activeIndex')
        if (localData) {
          mutations.setValue(state, {
            category: 'activeIndex',
            activeIndex: localData
          })
        }
        return state.activeIndex
      } else if (category === 'deviceSize') {
        return state.deviceSize
      } else if (category === 'pathTabs') {
        return state.pathTabs
      } else if (category === 'enterLibraryOrder') {
        let localData = util.getSessionInfo('enterLibraryOrder')
        if (localData) {
          mutations.setValue(state, {
            category: 'enterLibraryOrder',
            enterLibraryOrder: localData
          })
        }
        return state.enterLibraryOrder
      } else if (category === 'containerId') {
        let localData = util.getSessionInfo('containerId')
        if (localData) {
          mutations.setValue(state, {
            category: 'containerId',
            containerId: localData
          })
        }
        return state.containerId
      } else if (category === 'loginId') {
        let localData = util.getSessionInfo('loginId')
        if (localData) {
          mutations.setValue(state, {
            category: 'loginId',
            loginId: localData
          })
        }
        return state.loginId
      } else if (category === 'forecastData') {
        let localData = util.getSessionInfo('forecastData')
        if (localData && state.forecastData === null) {
          mutations.setValue(state, {
            category: 'forecastData',
            forecastData: localData
          })
        }
        return state.forecastData
      } else if (category === 'dxData') {
        let localData = util.getSessionInfo('dxData')
        if (localData && state.dxData === null) {
          mutations.setValue(state, {
            category: 'dxData',
            dxData: localData
          })
        }
        return state.dxData
      } else if (category === 'mutationsData') {
        let localData = util.getSessionInfo('mutationsData')
        if (localData && state.mutationsData === null) {
          mutations.setValue(state, {
            category: 'mutationsData',
            mutationsData: localData
          })
        }
        return state.mutationsData
      } else if (category === 'analysisRsId') {
        let localData = util.getSessionInfo('analysisRsId')
        if (localData && state.analysisRsId === null) {
          mutations.setValue(state, {
            category: 'analysisRsId',
            analysisRsId: localData
          })
        }
        return state.analysisRsId
      } else if (category === 'isPreview') {
        let localData = util.getSessionInfo('isPreview')
        if (localData && state.isPreview === null) {
          mutations.setValue(state, {
            category: 'isPreview',
            isPreview: localData
          })
        }
        return state.isPreview
      } else if (category === 'matchCancer') {
        let localData = util.getSessionInfo('matchCancer')
        if (localData && state.matchCancer === null) {
          mutations.setValue(state, {
            category: 'matchCancer',
            matchCancer: localData
          })
        }
        return state.matchCancer
      } else if (category === 'libraryOperatingData') {
        let localData = util.getSessionInfo('libraryOperatingData')
        if (localData && state.libraryOperatingData === null) {
          mutations.setValue(state, {
            category: 'libraryOperatingData',
            libraryOperatingData: localData
          })
        }
        return state.libraryOperatingData
      } else if (category === 'clinicalInfo') {
        let localData = util.getSessionInfo('clinicalInfo')
        if (localData) {
          mutations.setValue(state, {
            category: 'clinicalInfo',
            clinicalInfo: localData
          })
        }
        return state.clinicalInfo
      } else if (category === 'templateId') {
        let localData = util.getSessionInfo('templateId')
        if (localData) {
          mutations.setValue(state, {
            category: 'templateId',
            templateId: localData
          })
        }
        return state.templateId
      } else if (category === 'deliveryInfo') {
        let localData = util.getSessionInfo('deliveryInfo')
        if (localData && state.deliveryInfo.id === null) {
          mutations.setValue(state, {
            category: 'deliveryInfo',
            deliveryInfo: localData
          })
        }
        return state.deliveryInfo
      } else if (category === 'applicationInfo') {
        let localData = util.getSessionInfo('applicationInfo')
        if (localData && state.applicationInfo.id === null) {
          mutations.setValue(state, {
            category: 'applicationInfo',
            applicationInfo: localData
          })
        }
        return state.applicationInfo
      } else if (category === 'pathogenicType') {
        let localData = util.getSessionInfo('pathogenicType')
        if (localData && state.pathogenicType === null) {
          mutations.setValue(state, {
            category: 'pathogenicType',
            pathogenicType: localData
          })
        }
        return state.pathogenicType
      } else if (category === 'sampleQuality') {
        let localData = util.getSessionInfo('sampleQuality')
        if (localData && state.sampleQuality === null) {
          mutations.setValue(state, {
            category: 'sampleQuality',
            sampleQuality: localData
          })
        }
        return state.sampleQuality
      } else if (category === 'needUpdated') {
        let localData = util.getSessionInfo('needUpdated')
        if (localData && state.needUpdated === null) {
          mutations.setValue(state, {
            category: 'needUpdated',
            needUpdated: localData
          })
        }
        return state.needUpdated
      } else if (category === 'pixelRatio') {
        let localData = util.getSessionInfo('pixelRatio')
        if (localData && state.pixelRatio === null) {
          mutations.setValue(state, {
            category: 'pixelRatio',
            pixelRatio: localData
          })
        }
        return state.pixelRatio
      }
    }
  }
}

const store = new Vuex.Store({
  state: state,
  mutations: mutations,
  getters: getters
})

export default store
