<template>
  <el-dialog
    title="下单数据量"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    @opened="initTableData"
  >
    <div class="data-dialog">
      <el-table
        :data="tableData"
        border
        size="mini"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <el-table-column
          prop="orderTime"
          label="下单时间"
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="dataAmount"
          label="下单数据量/M"
          min-width="120"
          align="center"
        />
        <el-table-column
          prop="belongType"
          label="归属类型"
          min-width="120"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="libTypeSuffix"
          label="探针"
          min-width="120"
          align="center"
        />
      </el-table>

      <div class="total-info">
        <div class="total-item">
          <span class="label">客户下单总计:</span>
          <span class="value">{{ totalAmount }} M</span>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'
import { getOrderDetail } from '@/api/sequencingManagement/singleCell'
import util, { awaitWrap } from '@/util/util'

export default {
  name: 'DataDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    geneNum: {
      type: String,
      default: null
    },
    totalAmount: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async initTableData () {
      // 调用接口获取数据
      const params = {
        fgeneNum: this.geneNum
      }
      const { res = {} } = await awaitWrap(getOrderDetail(params))
      if (res.code === this.SUCCESS_CODE) {
        // 映射后端字段到前端字段
        this.tableData = (res.data || []).map(v => {
          const item = {
            orderTime: v.fcreateOrderTime,
            dataAmount: v.forderDataSize,
            belongType: v.forderType,
            libraryCode: v.flibNum,
            libTypeSuffix: v.flibTypeSuffix,
            geneCode: v.fgeneNum
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          return item
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.total-info {
  margin-top: 20px;
//   padding: 15px 0;
//   border-top: 1px solid #EBEEF5;
  .total-item {
    display: flex;
    justify-content: flex-end;
    font-size: 14px;
    .label {
      margin-right: 10px;
      font-weight: bold;
      color: #606266;
    }
    .value {
      font-weight: bold;
      color: #303133;
    }
  }
}
</style>
