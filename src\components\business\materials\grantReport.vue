<template>
  <div style="height: 100%;">
    <div ref="search" class="search-form">
      <el-form ref="form" :model="form" :inline="true" v-if="type !== '条件统计'" label-width="60px" size="mini" @keyup.enter.native="handleSearch">
        <el-form-item label="年份">
          <el-date-picker v-model.trim="form.year" clearable type="year" value-format="yyyy" placeholder="请选择年份"></el-date-picker>
        </el-form-item>
        <el-form-item v-if="type === '品类统计'" label="品类">
          <el-input v-model.trim="form.materials" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" @click="handleSearch">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-form v-if="type === '条件统计'" ref="form" :model="form" :inline="true" label-width="80px" size="mini" @keyup.enter.native="handleSearch">
        <el-form-item label="客户">
          <el-input v-model.trim="form.fcustomer" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model.trim="form.fapplicant" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="所属">
          <el-select v-model.trim="form.fbelongs" collapse-tags clearable multiple placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in belongList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="handleShowAdvanceSearch">高级查询</el-button>
          <el-button size="mini" @click="handleSearch">查询</el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </el-form-item>

        <div v-if="showAdvanceSearch">
          <el-form-item label="销售">
            <el-input v-model.trim="form.fsaleMan" clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="类别">
            <el-input v-model.trim="form.categoryName" clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="品类">
            <el-input v-model.trim="form.materialsName" clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker v-model.trim="form.time"
                            clearable type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="发货时间">
            <el-date-picker v-model.trim="form.sendTime"
                            clearable type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="批次号">
            <el-input v-model.trim="form.fratificationIssue" clearable placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="失效时间">
            <el-date-picker v-model.trim="form.fexpirationDate"
                            clearable type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="单价">
            <el-input v-model.trim="form.minFunitPrice" style="width: 40%" placeholder="请输入"></el-input>
            -
            <el-input v-model.trim="form.maxFunitPrice" style="width: 40%" placeholder="请输入"></el-input>
          </el-form-item>
        </div>

      </el-form>
    </div>
    <div class="content">
      <div class="buttonGroup operate-btns-group">
        <div>
          <el-button v-show="type === '条件统计'" type="primary" size="mini" @click="handlePublicityExport">导出统计</el-button>
          <el-button v-show="type === '品类统计'" type="primary" size="mini" @click="handleExportMaterialAnalysis">导出品类统计</el-button>
          <el-button v-show="type === '单量统计'" type="primary" size="mini" @click="handleExportApplyAnalysis">导出单量统计</el-button>
        </div>
        <div>
          <el-radio-group v-model="type" size="mini" @change="handleChangeType">
            <el-radio-button label="单量统计"></el-radio-button>
            <el-radio-button label="单量分析图"></el-radio-button>
            <el-radio-button label="品类统计"></el-radio-button>
            <el-radio-button label="条件统计"></el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="table">
        <template v-if="type === '单量分析图'">
          <div :style="`width: 100%; height: calc(100vh - 64px - 60px - 50px - 40px - 32px - ${searchHeight}px)`" ref="singleQuantityAnalysis"></div>
        </template>
        <template v-else>
          <el-table
            ref="table"
            :data="tableData"
            height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
            :empty-text="type === '条件统计' ? '请根据选择筛选条件统计' : '暂无数据'"
            size="mini"
            style="width: 100%;"
            @select="handleSelectTable"
            @row-click="handleRowClick"
            @select-all="handleSelectAll"
          >
            <template v-if="type === '单量统计'">
              <el-table-column key="0-clinical" label="临床" min-width="100" header-align="center">
                <el-table-column key="0-clinical-month" prop="moth" label="月份" width="80"></el-table-column>
                <el-table-column key="0-clinical-applyNum" prop="clinicalApplyNum" label="申请单量" min-width="80"></el-table-column>
                <el-table-column key="0-clinical-replaceNum" prop="clinicalReplaceNum" label="补发单量" min-width="80"></el-table-column>
              </el-table-column>
              <el-table-column key="0-factory" label="药厂" min-width="100" header-align="center">
                <el-table-column key="0-factory-month" prop="moth" label="月份" width="80"></el-table-column>
                <el-table-column key="0-factory-applyNum" prop="factoryApplyNum" label="申请单量" min-width="80"></el-table-column>
                <el-table-column key="0-factory-replaceNum" prop="factoryReplaceNum" label="补发单量" min-width="80"></el-table-column>
              </el-table-column>
              <el-table-column key="0-market" label="渠道" min-width="100" header-align="center">
                <el-table-column key="0-market-month" prop="moth" label="月份" width="80"></el-table-column>
                <el-table-column key="0-market-applyNum" prop="marketApplyNum" label="申请单量" min-width="80"></el-table-column>
                <el-table-column key="0-market-replaceNum" prop="marketReplaceNum" label="补发单量" min-width="80"></el-table-column>
              </el-table-column>
              <el-table-column key="0-total" label="总计" min-width="100" header-align="center">
                <el-table-column key="0-total-month" prop="moth" label="月份" width="80"></el-table-column>
                <el-table-column key="0-total-applyNum" prop="totalApplyNum" label="申请单量" min-width="80"></el-table-column>
                <el-table-column key="0-total-replaceNum" prop="totalReplaceNum" label="补发单量" min-width="80"></el-table-column>
              </el-table-column>
            </template>
            <template v-if="type === '品类统计'">
              <el-table-column prop="materialsName" label="品类" width="100" show-overflow-tooltip></el-table-column>
              <template v-for="item in monthList">
                <el-table-column :label="item + '月'" :key="item" min-width="80" header-align="center">
                  <el-table-column :prop="`${item}-num`" :key="`${item}-num`" label="数量" width="80"></el-table-column>
                  <el-table-column :key="`${item}-sequential`" label="环比" width="80">
                    <template slot-scope="scope">
                      <span :class="scope.row[item + '-sequential'].indexOf('-') === -1 ? 'rise' : 'decline'">{{scope.row[item + '-sequential']}}</span>
                    </template>
                  </el-table-column>
                </el-table-column>
              </template>
            </template>
            <template v-if="type === '条件统计'">
              <el-table-column type="selection"></el-table-column>
              <el-table-column prop="fapplyCode" label="申请单号" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fbelong" label="所属" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="materialsCategory" label="物料类别" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="materialsName" label="品类" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="materialsCode" label="物料编码" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fversion" label="版本号" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fratificationIssue" label="批次号" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fexpirationDate" label="失效时间" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fapplyNum" label="领取数量" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="unit" label="单位" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fapplyDate" label="申请日期" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fapplicant" label="申请人" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fcustomer" label="客户" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fapplyRegion" label="部门" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fuse" label="用途" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="freceiveway" label="领取方式" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fapplyRemark" label="申请备注" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fsendRemark" label="发货备注" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fexpressCode" label="快递单号" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="funitPrice" label="单价" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="amountOfMoney" label="金额" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fsendNum" label="发放数量" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="foperator" label="发放人" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="fsendTime" label="发货时间" width="100" show-overflow-tooltip></el-table-column>
            </template>
          </el-table>
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            v-if="type === '条件统计'"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
          </el-pagination>
        </template>
      </div>
    </div>
    <export-dialog
      :pvisible.sync="exportDialogVisible" :select-number="selectedRows.size" :all-number="totalPage"
      @dialogConfirmEvent="handleExport"
    ></export-dialog>
  </div>
</template>

<script>
import echarts from 'echarts'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  name: 'grantReport',
  mixins: [mixins.tablePaginationCommonData],
  components: {},
  props: [],
  mounted () {
    this.handleSearch()
    this.searchHeight = this.$refs.search.offsetHeight
    window.addEventListener('resize', this.$_resizeHandler)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.$_resizeHandler)
    })
  },
  data () {
    return {
      exportDialogVisible: false,
      downloadMaterialAnalysisLoading: false,
      downloadApplyAnalysisLoading: false,
      showAdvanceSearch: false, // 高级查询
      form: {
        fsaleMan: '',
        fcustomer: '',
        fapplicant: '',
        categoryName: '',
        fexpressCode: '',
        fstatus: '',
        fbelongs: '',
        materialsName: '',
        fexpirationDate: '',
        fratificationIssue: '',
        time: [],
        sendTime: [],
        year: '',
        materials: '',
        minFunitPrice: '',
        maxFunitPrice: ''
      },
      submitForm: {
        year: '',
        materials: ''
      },
      selectedRows: new Map(),
      belongList: [
        {
          value: 1,
          label: '临床(物料)'
        },
        {
          value: 2,
          label: '药厂'
        },
        {
          value: 3,
          label: '渠道'
        },
        {
          value: 4,
          label: '临床(宣传品)'
        },
        {
          value: 5,
          label: '病原(物料)'
        }
      ],
      type: '单量统计',
      singleQuantityAnalysis: null,
      searchHeight: '',
      monthList: []
    }
  },
  methods: {
    handleShowAdvanceSearch () {
      this.showAdvanceSearch = !this.showAdvanceSearch
      this.$nextTick(() => {
        // 更新视图后重新计算高度
        this.searchHeight = this.$refs.search.offsetHeight
      })
    },
    $_resizeHandler () {
      this.searchHeight = this.$refs.search.offsetHeight
      if (this.singleQuantityAnalysis) {
        this.singleQuantityAnalysis.resize()
      }
    },
    getData () {
      let url = ''
      let data = {}
      let method = ''
      console.log(this.type)
      if (this.type === '条件统计') {
        this.getConditionalStatisticData()
      } else {
        switch (this.type) {
          case '品类统计':
            method = 'post'
            url = '/materials/get_materials_analysis_data'
            data = {
              year: this.submitForm.year,
              materialsName: this.submitForm.materials.trim()
            }
            break
          default:
            method = 'get'
            url = '/materials/get_apply_analysis_data'
            data = {
              year: this.submitForm.year
            }
        }
        this.$ajax({
          url: url,
          loadingDom: '.table',
          method: method,
          data: data
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let data = result.data
            this.tableData = []
            this.monthList = []
            if (this.type === '品类统计') {
              let item = {}
              data.statistic.forEach((v, i) => {
                item = {
                  materialsName: v.materialsName
                }
                v.dataList.forEach(vv => {
                  if (i === 0) {
                    this.monthList.push(vv.month)
                  }
                  item[vv.month + '-num'] = vv.num
                  item[vv.month + '-sequential'] = vv.sequential
                })
                this.tableData.push(item)
              })
            } else if (this.type === '单量分析图') {
              this.singleQuantityAnalysis = echarts.init(this.$refs.singleQuantityAnalysis)
              let xValue = []
              let clinicalApplyValue = [] // 临床申请数量
              let factoryApplyValue = [] // 药厂申请数量
              let marketApplyValue = [] // 渠道申请数量
              let clinicalReplaceValue = [] // 临床补发数量
              let factoryReplaceValue = [] // 药厂补发数量
              let marketReplaceValue = [] // 沉市场补发数量
              data.clinical.forEach(v => {
                xValue.push(v.month)
                clinicalApplyValue.push(v.applyNum)
                clinicalReplaceValue.push(v.replaceNum)
              })
              data.factory.forEach(v => {
                factoryApplyValue.push(v.applyNum)
                factoryReplaceValue.push(v.replaceNum)
              })
              data.market.forEach(v => {
                marketApplyValue.push(v.applyNum)
                marketReplaceValue.push(v.replaceNum)
              })
              let option = {
                legend: {
                  data: ['临床', '药厂', '渠道']
                },
                tooltip: {
                  trigger: 'axis',
                  axisPointer: { // 坐标轴指示器，坐标轴触发有效
                    type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                  }
                },
                grid: [ // 用于调整X轴以及Y轴的位置
                  { // y轴
                    show: false,
                    bottom: '10%',
                    left: 10,
                    right: 50,
                    containLabel: true,
                    height: '35%'
                  },
                  { // x轴
                    show: false,
                    top: '50%',
                    left: 34,
                    right: 50,
                    height: '0%',
                    zlevel: 100
                  },
                  { // y轴
                    show: false,
                    top: '10%',
                    // left: 76,
                    left: 10,
                    right: 50,
                    containLabel: true,
                    height: '40%'
                  }
                ],
                xAxis: [
                  {
                    type: 'category',
                    position: 'bottom',
                    axisLine: {
                      show: false
                    },
                    axisTick: {
                      show: false
                    },
                    axisLabel: {
                      show: false
                    },
                    data: []

                  }, {
                    gridIndex: 1,
                    type: 'category',
                    position: 'center',
                    axisLine: {
                      show: true
                    },
                    axisTick: {
                      show: true
                    },
                    zlevel: 200,
                    axisLabel: {
                      show: true,
                      align: 'center',
                      textStyle: {
                        fontSize: 12
                      }
                    },
                    data: xValue
                  },
                  {
                    gridIndex: 2,
                    type: 'category',
                    position: 'top',
                    axisLine: {
                      show: false
                    },
                    axisTick: {
                      show: false
                    },
                    axisLabel: {
                      show: false
                    },
                    data: []
                  }
                ],
                yAxis: [
                  {
                    name: '补发数量',
                    type: 'value',
                    inverse: true, // echarts Y轴翻转属性, 往下的方向为正
                    position: 'left', // 位置属性
                    axisLabel: {
                      show: true,
                      textStyle: {
                        fontSize: 12
                      }
                    },
                    splitLine: {
                      show: true,
                      lineStyle: {
                        width: 1
                      }
                    }
                  },
                  {
                    gridIndex: 1, // 对应的是grid  通过grid设置X Y相对位置
                    show: false
                  },
                  {
                    name: '申请数量',
                    gridIndex: 2,
                    type: 'value',
                    position: 'left', // 双Y轴一个翻转一个不翻转
                    axisLabel: {
                      show: true,
                      textStyle: {
                        fontSize: 12
                      }
                    },
                    splitLine: {
                      show: true,
                      lineStyle: {
                        width: 1
                      }
                    }
                  }
                ],
                series: [
                  {
                    gridIndex: 0, // 坐标向上
                    name: '临床',
                    type: 'bar',
                    data: clinicalApplyValue,
                    xAxisIndex: 2,
                    yAxisIndex: 2,
                    label: {
                      normal: {
                        show: true
                      }
                    }
                  },
                  {
                    gridIndex: 2, // 坐标向下
                    name: '临床',
                    type: 'bar',
                    data: clinicalReplaceValue,
                    label: {
                      normal: {
                        show: true
                      }
                    }
                  },
                  {
                    gridIndex: 0, // 坐标向上
                    name: '药厂',
                    type: 'bar',
                    data: factoryApplyValue,
                    xAxisIndex: 2,
                    yAxisIndex: 2,
                    label: {
                      normal: {
                        show: true
                      }
                    }
                  },
                  {
                    gridIndex: 2, // 坐标向下
                    name: '药厂',
                    type: 'bar',
                    data: factoryReplaceValue,
                    label: {
                      normal: {
                        show: true
                      }
                    }
                  },
                  {
                    gridIndex: 0, // 坐标向上
                    name: '渠道',
                    type: 'bar',
                    data: marketApplyValue,
                    xAxisIndex: 2,
                    yAxisIndex: 2,
                    label: {
                      normal: {
                        show: true
                      }
                    }
                  },
                  {
                    gridIndex: 2, // 坐标向下
                    name: '渠道',
                    type: 'bar',
                    data: marketReplaceValue,
                    label: {
                      normal: {
                        show: true
                      }
                    }
                  }
                ]
              }
              this.singleQuantityAnalysis.setOption(option)
            } else {
              let map = new Map()
              data.clinical.forEach(v => {
                map.set(v.month, {
                  moth: v.month,
                  clinicalApplyNum: v.applyNum,
                  clinicalReplaceNum: v.replaceNum,
                  factoryApplyNum: '',
                  factoryReplaceNum: '',
                  marketApplyNum: '',
                  marketReplaceNum: '',
                  totalApplyNum: '',
                  totalReplaceNum: ''
                })
              })
              data.factory.forEach(v => {
                map.get(v.month).factoryApplyNum = v.applyNum
                map.get(v.month).factoryReplaceNum = v.replaceNum
              })
              data.market.forEach(v => {
                map.get(v.month).marketApplyNum = v.applyNum
                map.get(v.month).marketReplaceNum = v.replaceNum
              })
              data.total.forEach(v => {
                map.get(v.month).totalApplyNum = v.applyNum
                map.get(v.month).totalReplaceNum = v.replaceNum
              })
              this.tableData = [...map.values()]
            }
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.fid))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fid, row)
      })
    },
    // 获取条件统计数据
    getConditionalStatisticData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/materials/get_conditional_statistics_data',
        data: {
          ...this.submitForm,
          page: {
            size: this.pageSize,
            current: this.currentPage
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data.rows || []
          console.log(data.total, data)
          this.totalPage = result.data.total || 0
          this.tableData = []
          data.forEach(v => {
            let item = {
              fid: v.materialsid,
              fapplyCode: v.fapplyCode,
              fbelong: v.fbelong,
              materialsCategory: v.materialsCategory,
              materialsName: v.materialsName,
              materialsCode: v.materialsCode,
              fversion: v.fversion,
              fapplyNum: v.fapplyNum,
              unit: v.unit,
              fapplyDate: v.fapplyDate,
              fapplicant: v.fapplicant,
              fcustomer: v.fcustomer,
              fapplyRegion: v.fapplyRegion,
              fuse: v.fuse,
              freceiveway: v.freceiveway,
              fsendRemark: v.fsendRemark,
              fapplyRemark: v.fapplyRemark,
              fexpressCode: v.fexpressCode,
              funitPrice: v.funitPrice,
              amountOfMoney: v.amountOfMoney,
              fsendNum: v.fsendNum,
              foperator: v.foperator,
              fratificationIssue: v.fratificationIssue,
              fexpirationDate: v.fexpirationDate,
              fsendTime: v.fsendTime
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 切换分类
    handleChangeType () {
      this.searchHeight = this.$refs.search.offsetHeight
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
      if (this.type !== '条件统计') {
        this.handleSearch()
      } else {
        this.tableData = []
      }
    },
    handleSearch () {
      if (this.type === '条件统计') {
        let startTime = ''
        let endTime = ''
        let form = JSON.parse(JSON.stringify(this.form))
        if (form.time && form.time.length > 0) {
          startTime = form.time[0]
          endTime = form.time[1]
        }
        let sendStartTime = ''
        let sendEndTime = ''
        if (form.sendTime && form.sendTime.length > 0) {
          sendStartTime = form.sendTime[0]
          sendEndTime = form.sendTime[1]
        }

        let fexpirationDateStart = ''
        let fexpirationDateEnd = ''
        if (form.fexpirationDate && form.fexpirationDate.length > 0) {
          fexpirationDateStart = form.fexpirationDate[0]
          fexpirationDateEnd = form.fexpirationDate[1]
        }
        if (form.maxFunitPrice && form.maxFunitPrice === 0) {
          if (form.maxFunitPrice < form.minFunitPrice) {
            this.$message.error('最大单价不能大于最小单价')
            return
          }
        }
        const isNum = (form.maxFunitPrice && !util.isNumber(form.maxFunitPrice)) ||
          (form.minFunitPrice && !util.isNumber(form.minFunitPrice))

        if (isNum) {
          this.$message.error('单价必须是数字类型')
          return
        }

        this.submitForm = {
          fsaleMan: form.fsaleMan,
          fcustomer: form.fcustomer,
          fapplicant: form.fapplicant,
          categoryName: form.categoryName,
          fexpressCode: form.fexpressCode,
          fstatus: form.fstatus,
          fbelongs: form.fbelongs,
          materialsName: form.materialsName,
          fratificationIssue: form.fratificationIssue,
          minFunitPrice: form.minFunitPrice,
          maxFunitPrice: form.maxFunitPrice,
          startTime: startTime,
          endTime: endTime,
          fsendStartTime: sendStartTime,
          fsendEndTime: sendEndTime,
          fexpirationDateStart: fexpirationDateStart,
          fexpirationDateEnd: fexpirationDateEnd
        }
      } else {
        this.submitForm = JSON.parse(JSON.stringify({
          year: this.form.year,
          materials: this.form.materials
        }))
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        year: '',
        materials: ''
      }
      this.type = '单量统计'
      this.handleSearch()
    },
    handleExportMaterialAnalysis () {
      this.downloadMaterialAnalysisLoading = true
      this.$ajax({
        url: '/materials/export_materials_analysis_data',
        data: {
          year: this.submitForm.year,
          materialsName: this.submitForm.materials.trim()
        },
        responseType: 'blob'
      }).then(result => {
        const {data, headers} = result
        let fileName = headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1')
        // let fileName = '收款单信息.xlsx'
        let blob = new Blob([data], {type: headers['content-type']})
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) // 创建下载的链接
        downloadElement.href = href
        downloadElement.download = decodeURI(fileName) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href) // 释放blob对象
      }).catch(err => {
        console.log(err.response)
        this.$message.error(err.response.data.message)
      }).finally(() => {
        this.downloadMaterialAnalysisLoading = false
      })
    },
    handleExportApplyAnalysis () {
      this.downloadApplyAnalysisLoading = true
      this.$ajax({
        url: '/materials/export_apply_analysis_data',
        data: {
          year: this.submitForm.year
        },
        responseType: 'blob'
      }).then(result => {
        const {data, headers} = result
        let fileName = headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1')
        // let fileName = '收款单信息.xlsx'
        let blob = new Blob([data], {type: headers['content-type']})
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) // 创建下载的链接
        downloadElement.href = href
        downloadElement.download = decodeURI(fileName) // 下载后文件名
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href) // 释放blob对象
      }).catch(err => {
        console.log(err.response)
        this.$message.error(err.response.data.message)
      }).finally(() => {
        this.downloadApplyAnalysisLoading = false
      })
    },
    // 宣传品导出
    handlePublicityExport () {
      this.exportDialogVisible = true
    },
    // 导出数据
    handleExport (isAll) {
      this.exportLoading = true
      let ids = [...this.selectedRows.values()].map(v => v.fid)
      if (isAll) {
        ids = []
      }
      this.$ajax({
        url: '/materials/export_propaganda_material',
        data: {
          ...this.submitForm,
          materialsids: ids,
          type: isAll
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .search{
    display: flex;
    align-items: center;
  }
  .search >>>.el-form-item--mini{
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .content{
    //height: calc(100% - 100px);
    .buttonGroup{
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
    }
    .table{
      //height: calc(100% - 40px - 32px);
      .rise{
        color: #67C23A;
      }
      .decline{
        color: #F56C6C;
      }
    }
  }
</style>
