<template>
  <div class="page">
    <el-scrollbar class="scrollbar" style="height: calc(100% - 41px);">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <div class="materialInfo">
          <el-row>
            <el-col :span="4">
              <el-form-item label="销售">
                {{form.saleMan}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="申请区域">
                {{form.applyRegion}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="申请人">
                {{form.applicant}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="联系电话">
                {{form.phone}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="联系邮箱">
                {{form.informEmail}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="邮寄地址">
                {{form.sendAddr}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="详细地址">
                {{form.detailAddr}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="期望到达时间">
                {{form.expectReceiveTime}}
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="用途">
                {{form.purpose}}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="materialList">
          <div class="title">申请清单(合计金额: {{totalPrice}})</div>
          <el-table
            :data="form.materialPackageList" :span-method="spanMethod"
            size="mini"
            border
            class="applicationTable"
            style="width: 100%">
            <el-table-column label="类别" align="center" min-width="180">
              <template slot-scope="scope">
                {{scope.row.packageId ? scope.row.packageName : scope.row.category}}
              </template>
            </el-table-column>
            <el-table-column prop="materialsName" label="品类" align="center" min-width="180"></el-table-column>
            <el-table-column prop="spec" label="规格" align="center" width="180"></el-table-column>
            <el-table-column prop="unit" label="单位" align="center" width="80"></el-table-column>
            <el-table-column :formatter="handleFormatter" prop="materialsNum" label="数量" align="center" width="100"></el-table-column>
            <el-table-column prop="applyNum" label="申请数量" align="center" width="100"></el-table-column>
            <el-table-column prop="restStock" label="库存数量" align="center" width="100"></el-table-column>
            <el-table-column prop="funitPrice" label="物料单价" align="center" width="100"></el-table-column>
            <el-table-column prop="priceCount" label="金额" align="center" width="180"></el-table-column>
          </el-table>
        </div>
        <div v-if="applicationInfo.needReissue" class="reissueList">
          <div class="title">补发申请清单(合计金额: {{packageTotalPrice}})</div>
          <el-table
            :data="form.materialPackageReissueList" :span-method="spanMethod"
            size="mini"
            border
            class="reissueData"
            style="width: 100%">
            <el-table-column label="类别" align="center" min-width="180">
              <template slot-scope="scope">
                {{scope.row.packageId ? scope.row.packageName : scope.row.category}}
              </template>
            </el-table-column>
            <el-table-column prop="materialsName" label="品类" align="center" min-width="180"></el-table-column>
            <el-table-column prop="spec" label="规格" align="center" width="180"></el-table-column>
            <el-table-column prop="unit" label="单位" align="center" width="80"></el-table-column>
            <el-table-column :formatter="handleFormatter" prop="materialsNum" label="数量" align="center" width="80"></el-table-column>
            <el-table-column prop="applyNum" label="申请数量" align="center" width="180"></el-table-column>
            <el-table-column prop="funitPrice" label="物料单价" align="center" width="180"></el-table-column>
            <el-table-column prop="priceCount" label="金额" align="center" width="180"></el-table-column>
            <el-table-column prop="restStock" label="库存数量" align="center" width="180"></el-table-column>
          </el-table>
        </div>
        <div v-if="applicationInfo.needReissue" class="reissueApplyReason">
          <div class="title" style="color: red;">补发申请原因：{{form.replacementSendReasonText}}</div>
          <div>
            <div style="height: 35px; line-height: 35px;padding: 0 10px;">申请补发备注</div>
            <el-form-item label="">
              <el-input v-model="form.reissueNote" :disabled="true" :autosize="{minRows: 4}" clearable type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
            <div class="picList">
              <template v-for="item in form.freissueUrl">
                <div :key="item.fileAbsolutePath" class="picItem">
                  <el-image :src="item.fileAbsolutePath" style="width: 100%;"></el-image>
                </div>
              </template>
              <template v-for="item in form.freissueUrl.length">
                <i :key="item"></i>
              </template>
            </div>
          </div>
        </div>
        <div class="applyNote">
          <div class="title">申请备注</div>
          <div>
            <el-form-item label="">
              <el-input v-model="form.applyRemark" :disabled="true" :autosize="{minRows: 4}" clearable type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
          </div>
        </div>
        <div v-if="isbarcode" class="record">
          <div class="title">条码记录</div>
          <el-row :gutter="10">
            <div style="padding-left: 10px; margin: 10px 0;">
              条码使用人：{{[form.fsampleSale,form.fsampleCustomer].filter(v => v).join(';')}}
            </div>
            <div style="padding-left: 10px; margin: 10px 0;">条码号段：{{form.fminBarcode}} -{{form.fmaxBarcode}}</div>
          </el-row>
        </div>
      </el-form>
    </el-scrollbar>
    <div class="bottom">
      <el-button type="danger" size="mini" @click="handleClose">关 闭</el-button>
    </div>
  </div>
</template>

<script>
import util from '../../../util/util'

export default {
  name: 'applicationDetail',
  components: {},
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {
    totalPrice () {
      const materialPackageList = this.form.materialPackageList || []
      return materialPackageList.reduce((pre, next) => {
        const result = util.add(pre, next.priceCount)
        if (!result) return pre
        return result
      }, 0)
    },
    packageTotalPrice () {
      const materialPackageReissueList = this.form.materialPackageReissueList || []
      return materialPackageReissueList.reduce((pre, next) => {
        const result = util.add(pre, next.priceCount)
        if (!result) return pre
        return result
      }, 0)
    },
    isbarcode () {
      return this.form.materialPackageList.some(v => v.materialsName && v.materialsName.includes('条码')) ||
        this.form.materialPackageReissueList.some(v => v.materialsName && v.materialsName.includes('条码'))
    },
    applicationInfo () {
      return this.$store.getters.getValue('applicationInfo')
    }
  },
  data () {
    return {
      loading: false,
      furgent: 0,
      fautoPass: 0,
      rules: {},
      form: {
        fid: null,
        applicant: '',
        expectReceiveTime: '',
        applyRegion: '',
        informEmail: '',
        detailAddr: '',
        phone: '',
        saleMan: '',
        sendAddr: '',
        applyRemark: '',
        fauditNote: '',
        fisUrgent: 0,
        fisAutoBypass: 0,
        freissueUrl: [], // 补发图片数据
        replacementSendReason: '', // 补发申请原因id(1: 物料数量缺少 2: 物料品类缺少 3: 物料损坏 4: 物料发放错误)
        replacementSendReasonText: '', // 补发申请原因id(1: 物料数量缺少 2: 物料品类缺少 3: 物料损坏 4: 物料发放错误)
        reissueNote: '',
        materialPackageReissueList: [], // 补发信息数据
        materialPackageList: [], // 物料数据
        operationList: [] // 审核记录
      },
      reissueApplyReasonList: [
        {
          label: '物料数量缺少',
          value: '1'
        },
        {
          label: '物料品类缺少',
          value: '2'
        },
        {
          label: '物料损坏',
          value: '3'
        },
        {
          label: '物料发放错误',
          value: '4'
        }
      ],
      statusList: [
        {
          label: '通过',
          value: 2
        },
        {
          label: '驳回',
          value: 3
        }
      ],
      partialDeliveryDialogVisible: false,
      partialDeliveryDialogData: {}
    }
  },
  methods: {
    spanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 对类别列进行合并，行合并数为物料包里的品类数量
        return {
          rowspan: row.total,
          colspan: 1
        }
      } else if (columnIndex === 5) {
        // 对申请数量列进行行合并（只对物料包合并，散包不合并申请数量）
        // 通过物料包ID（packageId）进行判断是否为散包
        return {
          rowspan: row.packageId ? row.total : 1,
          colspan: 1
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1
        }
      }
    },
    getData () {
      this.$ajax({
        loadingDom: '.page',
        url: '/materials/get_apply_form_detail',
        method: 'get',
        data: {
          fid: this.applicationInfo.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          try {
            let data = result.data
            this.form = {
              fid: data.fid,
              applicant: data.applicant,
              fcreateTime: data.fcreateTime,
              saleMan: data.saleMan,
              expectReceiveTime: data.expectReceiveTime,
              applyRegion: data.applyRegion,
              purpose: data.purpose,
              informEmail: data.informEmail,
              detailAddr: data.detailAddr,
              fsampleSale: data.fsampleSale,
              fsampleCustomer: data.fsampleCustomer,
              fminBarcode: data.fminBarcode,
              fmaxBarcode: data.fmaxBarcode,
              phone: data.phone,
              sendAddr: data.sendAddr,
              applyRemark: data.applyRemark,
              fauditNote: data.fauditNote,
              fisUrgent: data.fisUrgent,
              fisAutoBypass: data.fisAutoBypass || 0,
              freissueUrl: data.freissueUrl ? JSON.parse(data.freissueUrl) : [], // 补发图片数据
              replacementSendReason: data.replacementSendReason, // 补发申请原因id(1: 物料数量缺少 2: 物料品类缺少 3: 物料损坏 4: 物料发放错误)
              replacementSendReasonText: this.getText(data.replacementSendReason, 'reissueApplyReasonList'), // 补发申请原因id(1: 物料数量缺少 2: 物料品类缺少 3: 物料损坏 4: 物料发放错误)
              reissueNote: data.reissueNote,
              materialPackageReissueList: [], // 补发信息数据
              materialPackageList: [], // 物料数据
              operationList: data.operationList // 审核记录
            }
            this.getRealTableData(data.applyForm || [], 'materialPackageList')
            this.getRealTableData(data.replacementApplyForm || [], 'materialPackageReissueList')
            this.getPriceCount()
          } catch (e) {
            console.log(e)
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 计算总额
    getPriceCount () {
      // 物料在申请单中是散料时：单价×申请数量
      // 物料在申请单中是物料包时：单价×数量×申请数量
      this.form.materialPackageList = this.form.materialPackageList.map(materialInfo => {
        let priceCount = util.mul(materialInfo.applyNum, materialInfo.funitPrice)
        materialInfo.packageName = materialInfo.packageName || ''
        if (materialInfo.packageId) {
          priceCount = util.mul(priceCount, materialInfo.materialsNum || 1)
        }
        materialInfo.funitPrice = materialInfo.funitPrice || '-'
        return {priceCount: priceCount.toString(), ...materialInfo}
      })
      this.form.materialPackageReissueList = this.form.materialPackageReissueList.map(materialInfo => {
        let priceCount = materialInfo.applyNum * materialInfo.funitPrice
        materialInfo.packageName = materialInfo.packageName || ''
        if (materialInfo.packageId) {
          priceCount = priceCount * (materialInfo.materialsNum || 1)
        }
        materialInfo.funitPrice = materialInfo.funitPrice || '-'
        return {priceCount, ...materialInfo}
      })
    },
    // map 结构，key是物料包ID(packageId),value是物料包下的物料数据
    getRealTableData (list, name) {
      let map = new Map()
      let id = null
      list.forEach(v => {
        id = v.packageId || v.categoryId
        if (map.has(id)) {
          map.get(id).push(v)
        } else {
          map.set(id, [v])
        }
      })
      let tableData = []
      // 将map数据转换成array格式
      let data = [...map.values()]
      // 对已经分类的数据进行再次处理，获取类别合并行数据
      data.forEach((v, i) => {
        v.forEach((vv, ii) => {
          // total 表示需要合并的行数，只有每个物料包的第一个品类有数据，剩余的均为0
          tableData.push({...vv, total: ii === 0 ? v.length : 0})
        })
      })
      // 使用set方法设置表格数据，避免页面数据不渲染的问题
      this.$set(this.form, name, tableData)
    },
    getText (value, listName) {
      let text = ''
      if (value) {
        let obj = this[listName].find(v => v.value === value)
        if (obj) {
          text = obj.label
        }
      }
      return text
    },
    handleClose () {
      this.$store.commit({
        type: 'old/setValue',
        category: 'applicationInfo',
        applicationInfo: {
          id: null,
          type: null,
          needReissue: false
        }
      })
      this.$alert('当前页面即将关闭', '关闭', {
        confirmButtonText: '确定',
        callback: action => {
          window.close()
        }
      })
      // alert('关闭页面')
    },
    handleFormatter (row, column, cellvalue) {
      if (column.property === 'materialsNum') {
        if (cellvalue === null || cellvalue === undefined) {
          return '-'
        } else {
          return cellvalue
        }
      }
    },
    getListString (value, list) {
      let label = ''
      let obj = this[list].find(v => value === v.value)
      if (obj) {
        label = obj.label
      }
      return label
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    margin: 0 10px;
    height: calc(100vh - 40px);
    .scrollbar >>>.el-scrollbar__wrap{
      overflow-y: scroll;
      overflow-x: hidden;
    }
    .materialInfo{
      padding-top: 10px;
    }
    .materialList{
      /*height: calc(100vh - 40px - 40px - 102px);*/
      >>>.el-table {
        th{
          background: #f2f2f2;
        }
        tbody tr {
          &:hover {
            td {
              background-color: transparent;
            }
          }
        }
        .el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item{
          margin-top: 14px;
          margin-bottom: 14px;
        }
      }
    }
    .reissueList{
      /*height: calc(100vh - 40px - 40px - 102px);*/
      >>>.el-table {
        th{
          background: #f2f2f2;
        }
        tbody tr {
          &:hover {
            td {
              background-color: transparent;
            }
          }
        }
      }
    }
    .picList{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .picItem{
        width: calc(100% / 3 - 10px);
      }
      i{
        width: calc(100% / 3 - 10px);
      }
    }
    .title{
      margin: 20px 0 10px;
      padding: 0 10px;
      height: 35px;
      line-height: 35px;
      border-bottom: 1px solid #DCDFE6;
    }
    .operation{
      /*margin-bottom: 40px;*/
      .recordItem{
        font-size: 14px;
        height: 40px;
        line-height: 40px;
      }
    }
    .bottom{
      width: calc(100% - 60px);
      position: absolute;
      z-index: 10;
      background-color: #ffffff;
      bottom: 21px;
      height: 40px;
      line-height: 40px;
      border-top: 1px solid #DCDFE6;
      text-align: right;
    }
  }
</style>
