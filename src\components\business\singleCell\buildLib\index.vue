<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="plainForm"
        :inline="true"
        label-width="120px"
        size="mini"
        label-suffix=":"
        @keyup.enter.native="handleSearch">
        <el-form-item label="生产序号">
          <el-input v-model.trim="plainForm.productionNumber" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="子订单编号">
          <el-input v-model.trim="plainForm.subOrderCode" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="项目编号">
          <el-input v-model.trim="plainForm.projectCode" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="plainForm.projectName" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="样本原始名称">
          <el-input v-model.trim="plainForm.sampleName" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="文库编号">
          <el-input v-model.trim="plainForm.libraryCode" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="异常状态">
          <el-select v-model="form.exceptionType" size="mini" clearable  class="form-width" placeholder="请选择">
            <el-option v-for="item in exceptionTypeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input
            v-model.trim="plainForm.geneCode"
            placeholder="请输入"
            class="form-width"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="生产状态">
          <el-select v-model.trim="form.fproductionStatus" placeholder="请选择" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in productStatusConfig" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否已补测" prop="fisAddTest">
          <el-input
            v-model.trim="form.fisAddTest"
            placeholder="请输入"
            class="form-width"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="是否达标">
          <el-select v-model.trim="form.fisQuality" placeholder="请选择" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建库完成时间">
          <el-date-picker
            v-model.trim="form.buildTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            clearable
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            :default-time="['00:00:00', '23:59:59']"
            class="form-long-width">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="mini"
        label-position="top"
        inline>
        <el-form-item label="生产序号">
          <el-input v-model.trim="form.productionNumber" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="子订单编号">
          <el-input v-model.trim="form.subOrderCode" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input
            v-model.trim="form.geneCode"
            type="textarea"
            :placeholder="placeholder"
            class="form-long-width"
            size="mini">
          </el-input>
        </el-form-item>
        <el-form-item label="项目编号">
          <el-input v-model.trim="form.projectCode" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="样本原始名称">
          <el-input v-model.trim="form.sampleName" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="文库编号">
          <el-input v-model.trim="form.libraryCode" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input v-model.trim="form.productName" type="textarea" :placeholder="placeholder" size="mini" clearable
                    class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="生产状态">
          <el-select v-model.trim="form.fproductionStatus" placeholder="请选择" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in productStatusConfig" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否已补测" prop="fisAddTest">
          <el-input
            v-model.trim="form.fisAddTest"
            placeholder="请输入"
            class="form-width"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="是否达标">
          <el-select v-model.trim="form.fisQuality" placeholder="请选择" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="异常状态">
          <el-select v-model="form.exceptionType" size="mini" clearable  class="form-width" placeholder="请选择">
            <el-option v-for="item in exceptionTypeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="到样日期">
          <el-date-picker
            v-model.trim="form.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            clearable
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            :default-time="['00:00:00', '23:59:59']"
            class="form-long-width">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="归属类型">
          <el-input v-model.trim="form.fcosQualityThresholdOrderType" placeholder="请输入" size="mini" clearable
                    class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="排序方式">
          <el-radio-group v-model="form.fsortField" size="mini">
            <el-radio v-for="(value, key) in sortFields" :key="key" :label="key" border>{{value}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </search-params-dialog>
    <div class="flex-wrapper">
      <div class="operate-btns-group">
        <template v-if="$setAuthority('024001001', 'buttons')">
          <el-button v-if="downloadLoading" type="primary" size="small" disabled><i class="el-icon-loading"></i>
            正在导出
          </el-button>
          <el-dropdown v-else @command="handleCommand" style="margin: 0 10px;">
            <el-button type="primary" size="mini">数据导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <el-button v-if="$setAuthority('024001004', 'buttons')" type="primary" size="mini" plain @click="handleFixInfo">信息修改</el-button>
        <el-button v-if="$setAuthority('024001004', 'buttons')" type="primary" size="mini" plain @click="handleBatchModify">异常编辑</el-button>
        <el-button v-if="$setAuthority('024001005', 'buttons')" type="primary" size="mini" plain @click="handleSignAddTest">补测标记</el-button>
        <el-button v-if="$setAuthority('024001002', 'buttons')" type="primary" plain size="mini" @click="handleImport">导入结果</el-button>
        <el-button v-if="$setAuthority('024001006', 'buttons')" type="primary" plain size="mini" @click="handleRebuildLib">重建库</el-button>
        <el-button v-if="$setAuthority('024001003', 'buttons')" type="primary" plain size="mini" @click="handleConfirmComputed">确认上机</el-button>
        <el-button v-if="$setAuthority('024001007', 'buttons')" type="primary" plain size="mini" @click="handleCancelLib">撤销建库</el-button>
        <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
        <el-button size="mini" @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
        </el-badge>
      </div>

      <div class="setting-wrapper">
        <el-popover
          v-model.trim="visible"
          placement="bottom-start"
          title="自定义列"
          width="360"
          trigger="manual"
        >
          <div>
            <el-table
              ref="table"
              :data="tableData"
              border
              size="mini"
              height="300px"
              style="width: 100%"
              row-key="id"
              @select="handleSelectTable"
              @row-click="handleRowClick"
              @select-all="handleSelectAll"
            >
              <el-table-column type="index" width="50" show-overflow-tooltip>
                <template slot-scope="scope">
                  <icon-svg icon-class="icon-tuozhuai" class="handle"></icon-svg>
                </template>
              </el-table-column>
              <el-table-column type="selection" min-width="55" show-overflow-tooltip></el-table-column>
              <el-table-column
                prop="title"
                label="列名"
                key="title"
                min-width="200">
                <template slot-scope="scope">
                  <div v-html="scope.row.title"></div>
                </template>
              </el-table-column>
            </el-table>
            <div class="operate-wrapper">
              <div class="operate-item" @click="handleResetTableConfig">恢复默认</div>
              <div class="operate-item" @click="handleCancel">关闭</div>
              <div class="operate-item" @click="handleSave">保存</div>
            </div>
          </div>
          <div slot="reference" @click="handleShowSetting">
            <el-card :body-style="{ padding: '5px'}" shadow="hover">
              <icon-svg style="font-size: 20px" icon-class="icon-shezhi"></icon-svg>
            </el-card>
          </div>
        </el-popover>
      </div>
    </div>

    <div class="content">
      <vxe-table
        ref="tableRef"
        border
        resizable
        :height="tbHeight"
        keep-source
        :key="tableKey"
        class="table"
        :data="tableList"
        size="mini"
        show-overflow
        :row-style="handleVxeRowStyle"
        :auto-resize="true"
        :edit-rules="validRules"
        :valid-config="{msgMode: 'full'}"
        :checkbox-config="{trigger: 'row'}"
        :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
        :scroll-y="{enabled: true}"
        @checkbox-all="handleSelectChange"
        @checkbox-change="handleSelectChange"
        @edit-closed="handleSaveNote"
      >
        <vxe-column fixed="left" type="checkbox" width="50"></vxe-column>
        <vxe-table-column fixed="left" type="seq" title="序号" width="60"></vxe-table-column>
        <template v-for="(item, index) in tableConfig">
          <vxe-table-column v-if="item.field === 'fexceptionRemark' && item.isShow" :key="item.field + index" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :width="item.width" :edit-render="item.render"
                            line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template #default="{ row }">
              <el-tooltip :content="row.fremark" :disabled="!row.realData.fremark" placement="top" effect="dark">
                <div :class="getWarningFlagClass(row.fexceptionRemark)">{{row.warningFlagText}}</div>
              </el-tooltip>
            </template>
          </vxe-table-column>
          <vxe-table-column v-else-if="item.field === 'fdifference' && item.isShow" :key="item.field + 'fdifference'" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :width="item.width" :edit-render="item.render"
                            line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template #default="{ row }">
              <el-tooltip :content="row.fdeplaneJson" :disabled="!row.realData.fdeplaneJson" placement="top" effect="dark">
                <div slot="content" v-html="row.fdeplaneJson"></div>
                <div >{{row.fdifference}}</div>
              </el-tooltip>
            </template>
          </vxe-table-column>
          <vxe-table-column v-else-if="item.field === 'fisQualityText' && item.isShow" :key="item.field + 'fisQualityText'" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :width="item.width" :edit-render="item.render"
                            line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template #default="{ row }">
              <span :style="`color: ${row.fisQuality === 0 ? '#F56C6C' : ''}`">{{row.fisQualityText}}</span>
            </template>
          </vxe-table-column>
          <!--          时间-->
          <vxe-table-column v-else-if="['fcdnaExperimentCompletionTime', 'flibFinishTime'].includes(item.field) && item.isShow" :key="item.field + 'time'" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :width="item.width" :edit-render="{}"
                            line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template #edit="{ row }">
              <vxe-input v-model="row.realData[item.field]" type="datetime" clearable placeholder="请选择日期" transfer @change="() => row[item.field] = row.realData[item.field] || '-'"></vxe-input>
            </template>
            <!-- <tooltips :txt-info="row.warningFlagText + ''" :class="getWarningFlagClass(row.warningFlag)"></tooltips> -->
          </vxe-table-column>
          <vxe-table-column v-else-if="item.isShow" :key="item.field" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :width="item.width" :edit-render="item.render" line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template v-if="item.render" #edit="{ row }">
              <vxe-input v-if="item.render.name === '$input'" v-model="row.realData[item.field]" type="text" clearable placeholder="请输入内容" transfer @change="() => row[item.field] = row.realData[item.field]"></vxe-input>
              <!--              下拉-->
              <vxe-select v-else-if="item.render.name === '$select'" v-model="row.realData[item.field]" clearable transfer @change="() => row[item.field] = row.realData[item.field] || '-'">
                <vxe-option v-for="(value) in item.render.options" :key="value.value" :label="value.label" :value="value.value"></vxe-option>
              </vxe-select>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <upload-result-dialog
      :pvisible.sync="uploadResultVisible"
      @dialogConfirmEvent="getData"></upload-result-dialog>
    <confirm-computed-dialog
      :pvisible.sync="confirmComputedVisible"
      :ids="ids"
      @dialogConfirmEvent="getData"
    ></confirm-computed-dialog>
    <batch-modifying-dialog
      :pvisible.sync="batchModifyingDialogVisible"
      :ids="ids"
      @dialogConfirmEvent="getData"></batch-modifying-dialog>
    <sign-add-test-dialog
      :pvisible.sync="signAddTestDialogVisible"
      :ids="ids"
      @dialogConfirmEvent="getData">
    </sign-add-test-dialog>

    <fix-info-dialog
      :pvisible.sync="fixInfoVisible"
      :ids="ids"
      :samples="samples"
      @dialogConfirmEvent="getData"
    ></fix-info-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import {tableConfig} from './tabelConfig'
import {awaitWrap, deepCopy, downloadFile, readBlob, setGroupData} from '../../../../util/util'
import Sortable from 'sortablejs'
import {
  exportBuildLibData,
  getBuildLibDataList,
  rebuildLibrary,
  cancelBuildLib, updateBuildInfo
} from '../../../../api/sequencingManagement/singleCell'
import {
  booleanOptions,
  dataFormating,
  productStatusConfig,
  exceptionTypeList
} from './dataFormate'
import UploadResultDialog from './components/uploadResultDialog.vue'
import confirmComputedDialog from './components/confirmComputedDialog.vue'
import BatchModifyingDialog from './components/batchModifyingDialog.vue'
import signAddTestDialog from './components/signAddTestDialog.vue'
import fixInfoDialog from './components/fixInfoDialog.vue'
import IconSvg from '../../../common/iconSvg.vue'

export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {IconSvg, BatchModifyingDialog, UploadResultDialog, confirmComputedDialog, signAddTestDialog, fixInfoDialog},
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
    this.setTableConfig()
  },
  data () {
    return {
      // 控制弹出框的可见性，默认为隐藏
      visible: false,
      // 控制搜索对话框的可见性，默认为隐藏
      searchDialogVisible: false,
      // 控制下载Excel时的加载状态，默认为隐藏
      downloadingExcelLoading: false,
      // 控制导入Excel时的加载状态，默认为隐藏
      uploadResultVisible: false,
      confirmComputedVisible: false,
      batchModifyingDialogVisible: false,
      signAddTestDialogVisible: false,
      fixInfoVisible: false,
      downloadLoading: false,
      // 选中项的ID数组，默认为空数组
      ids: [],
      samples: [],
      productStatusConfig: productStatusConfig,
      booleanOptions,
      isAddTestOption: {
        2: '无',
        ...booleanOptions
      },
      thresholdOrderTypes: ['首次下单', '吉云补测', '客户加测'],
      exceptionTypeList,
      placeholder: '精确查询，支持批量查询，批量查询时用中英文逗号（,/，）、空格（ ）、顿号（、）分隔',
      // 交付状态，默认为空
      deliverStatus: null,
      // 订单编号，默认为空
      orderCode: null,
      // 订单类型，默认为空
      orderType: null,
      productName: null,
      sortFields: {
        0: '生产序号',
        1: '到样日期'
      },
      // 表单数据对象，默认为空对象
      form: {
      },
      plainForm: {},
      formSubmit: {},
      plainFormSubmit: {},
      // 包lane数
      laneTotal: null,
      // 实际周期
      actualCycle: null,
      // 分页大小数组，默认为100、200、500
      pageSizes: [100, 200, 500],
      pageSize: 100,
      tableKey: Math.random(),
      // 表配置对象，由外部定义
      tableConfig: [],
      // 表数据数组，默认为空数组
      tableData: [],
      // 表列表数组，默认为空数组
      tableList: [],
      // 表单验证规则对象，默认为空对象
      validRules: {
      }
    }
  },
  methods: {
    setTableConfig () {
      const localStorageConfig = JSON.parse(localStorage.getItem('buildTableConfig'))
      // 获取基础配置修改时间
      // const localStorageConfigTime = (localStorageConfig[0] || {}).configFixTime || ''
      // const configTime = (tableConfig[0] || {}).configFixTime || ''
      this.tableConfig = localStorageConfig || tableConfig
      // if (localStorageConfigTime !== configTime) {
      //   this.$confirm('基础配置有更新，是否更新？', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(() => {
      //     localStorage.setItem('buildTableConfig', JSON.stringify(tableConfig))
      //     this.tableConfig = tableConfig
      //   }).catch(() => {
      //     this.tableConfig = localStorageConfig
      //   })
      // }
      this.tableKey = Math.random()
    },
    /**
     * 设置查询参数。
     * 此函数用于根据表单提交的信息，生成并返回一个包含各种查询条件的对象。
     * 这些条件包括时间范围、项目信息、样本信息、订单信息等，用于精确查询项目进度和状态。
     *
     * @returns {Object} 返回一个对象，其中包含了所有的查询参数。
     */
    setParams () {
      const plainFormSubmit = deepCopy(this.plainFormSubmit)
      const fgeneNumList = setGroupData(this.formSubmit.geneCode, '、', false)
      const fcosSubCodeList = setGroupData(this.formSubmit.subOrderCode, '、', false)
      const fprojectCodeList = setGroupData(this.formSubmit.projectCode, '、', false)
      const fprojectNameList = setGroupData(this.formSubmit.projectName, '、', false)
      const fcosSampleNameList = setGroupData(this.formSubmit.sampleName, '、', false)
      const fproductNameList = setGroupData(this.formSubmit.productName, '、', false)
      const flibNumList = setGroupData(this.formSubmit.libraryCode, '、', false)
      const fproductionNumberList = setGroupData(this.formSubmit.productionNumber, '、', false)
      // 构建并返回查询参数对象
      const time = this.formSubmit.time || []
      const buildTime = this.formSubmit.buildTime || []
      return {
        // 构建查询参数对象
        fconfirmTimeStart: time[0] || '',
        fconfirmTimeEnd: time[1] || '',
        flibFinishTimeStart: buildTime[0] || '',
        flibFinishTimeEnd: buildTime[1] || '',
        fexceptionRemark: this.formSubmit.exceptionType,
        fisQuality: this.formSubmit.fisQuality,
        fcosQualityThresholdOrderType: this.formSubmit.fcosQualityThresholdOrderType,
        fisAddTest: this.formSubmit.fisAddTest,
        fsortField: this.formSubmit.fsortField,
        fgeneNumList: [plainFormSubmit.geneCode, ...fgeneNumList].filter(v => v),
        fcosSubCodeList: [plainFormSubmit.subOrderCode, ...fcosSubCodeList].filter(v => v),
        fprojectCodeList: [plainFormSubmit.projectCode, ...fprojectCodeList].filter(v => v),
        fprojectNameList: [plainFormSubmit.projectName, ...fprojectNameList].filter(v => v),
        fcosSampleNameList: [plainFormSubmit.sampleName, ...fcosSampleNameList].filter(v => v),
        fproductNameList: [plainFormSubmit.productName, ...fproductNameList].filter(v => v),
        flibNumList: [plainFormSubmit.libraryCode, ...flibNumList].filter(v => v),
        fproductionNumberList: [plainFormSubmit.productionNumber, ...fproductionNumberList].filter(v => v),
        fproductionStatus: this.formSubmit.fproductionStatus || ''
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (this.pin) {
        this.shiftSelect(row)
      } else {
        this.startPoint = undefined// 清空多选起点
        this.endPoint = undefined// 清空多选终点
        this.selectedRows.has(row.id)
          ? this.selectedRows.delete(row.id)
          : this.selectedRows.set(row.id, row)
      }
      this.handleEchoSelect()
    },
    // 全选
    handleSelectAll (selection) {
      this.handleDelCurrentDataMap()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.selectedRowsSize = this.selectedRows.size
    },
    /**
     * 处理搜索操作。
     * 重置当前页码并调用获取数据的方法，以便加载新搜索结果。
     */
    handleSearch () {
      // 深拷贝表单提交的数据，确保不直接修改原数据
      this.formSubmit = deepCopy(this.form)
      this.plainFormSubmit = deepCopy(this.plainForm)
      this.currentPage = 1
      this.getData()
    },
    /**
     * 处理重置操作。
     * 将表单数据重置为初始状态，并调用搜索方法以刷新列表。
     */
    handleReset () {
      this.form = this.$options.data().form
      this.plainForm = this.$options.data().plainForm
      this.handleSearch()
    },
    /**
     * 异步获取样本监控列表的数据。
     *
     * 使用设置的参数进行请求，并对请求结果进行处理，将处理后的数据赋值给列表。
     * 使用awaitWrap封装请求以处理加载状态。
     */
    async getData () {
      const params = {
        ...this.setParams(), // 分页信息
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      const {res} = await awaitWrap(getBuildLibDataList(params, {
        loadingDom: '.table'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.totalPage = res.data.total
        this.tableList = dataFormating(res.data.records)
      }
    },
    setUpdateParams (field, cellValue, row) {
      return {
        fid: row.fid,
        [field]: cellValue
      }
    },
    async handleSaveNote ({row, column}) {
      let xTable = this.$refs.tableRef
      let field = column.property
      let cellValue = row.realData[field]
      // 判断单元格值是否被修改
      if (xTable.isUpdateByRow(row, field)) {
        // 校验格式 配置式
        if (field === 'productionNumber') {
          if (!/^[0-9a-zA-Z-]+$/.test(cellValue)) {
            this.$message.error('生产序号仅允许输入数字、大小写字母、-')
            row[field] = ''
            return
          }
        }
        // 最多50字符
        if (cellValue.length > 50) {
          this.$message.error('最多50字符')
          return
        }
        const params = this.setUpdateParams(field, cellValue, row)
        const {res = {}} = await awaitWrap(updateBuildInfo(params))
        if (res.code === this.SUCCESS_CODE) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          // 局部更新单元格为已保存状态
          await this.$refs.tableRef.reloadRow(row, null, field)
        } else {
          await this.getData()
        }
      }
    },
    // 选择导出类型
    handleCommand (command) {
      command === 1 ? this.handleExportAll() : this.handleExport()
    },
    async downloadExport (res) {
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    /**
     * 获取警告标记对应的样式类名
     * @param {number} type - 警告类型
     * @returns {string} 对应的CSS类名
     * 1: 暂停 - 橙色
     * 2-5: 各类终止状态 - 红色
     */
    getWarningFlagClass (type) {
      const warningClassMap = {
        0: 'warning-flag--pause', // 暂停
        1: 'warning-flag--terminate-pre', // 终止-解离前
        2: 'warning-flag--terminate-post', // 终止-解离后
        3: 'warning-flag--terminate-lib', // 终止-建库后
        4: 'warning-flag--terminate-seq' // 终止-上机后
      }
      return warningClassMap[type] || ''
    },
    // 按条件导出
    async handleExportAll () {
      const params = this.setParams()
      // await this.$confirm('是否确认导出查询数据？', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // })
      const fields = await this.$downLoadChoose('是否确认导出查询数据？', this.tableConfig)
      let flibExportFiledVos = null
      if (fields.length > 0) {
        flibExportFiledVos = fields
      }
      this.downloadLoading = true
      const {res} = await awaitWrap(exportBuildLibData({ flibExportFiledVos: flibExportFiledVos, ...params }))
      await this.downloadExport(res)
    },
    // 导出所选
    async handleExport () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rowsId = selectRecords.map(item => item.fid)
      // await this.$confirm('否确认导出选中数据？', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // })
      const fields = await this.$downLoadChoose('否确认导出选中数据？', this.tableConfig)
      let flibExportFiledVos = null
      if (fields.length > 0) {
        flibExportFiledVos = fields
      }
      this.downloadLoading = true
      const {res} = await awaitWrap(exportBuildLibData({
        fnewLibIdList: rowsId,
        flibExportFiledVos: flibExportFiledVos
      }))
      await this.downloadExport(res)
    },
    handleFixInfo () {
      let selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择至少一条样本')
        return
      }
      // 只能勾选“是否达标”字段值为“否”的样本。可多选，选多个样本就信息修改框里面展示。
      // 注意：如果勾选的样本的“是否达标”字段值不是”否“时，应该提示：”请勾选是否达标值为”否“的样本“
      if (selectedRecords.some(v => v.fisQuality)) {
        this.$message.error('请勾选是否达标值为”否“的样本')
        return
      }
      // 4.值针对文库类型为oligo的可以修改，如果勾选到非oligo文库的样本，不给在”信息修改“页面展示。
      selectedRecords = selectedRecords.filter(v => v.flibType === 'oligo')
      this.samples = selectedRecords
      this.fixInfoVisible = true
    },
    handleBatchModify () {
      // 获取选中的样本记录
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择至少一条样本')
        return
      }
      this.ids = selectedRecords.map(item => item.fid)
      this.batchModifyingDialogVisible = true
    },
    handleSignAddTest () {
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择至少一条样本')
        return
      }
      this.ids = selectedRecords.map(item => item.fid)
      this.signAddTestDialogVisible = true
    },
    // 导入结果
    handleImport () {
      this.uploadResultVisible = true
    },
    // 重建库
    async handleRebuildLib () {
      const selectedRows = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRows.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      // 1、展示重建库确认弹窗，点击「取消」，则不进行重建库操作。
      const message = `<b>重建库确认</b><br/>
      当前已选${selectedRows.length}条样本数据，确认重建库后系统将生成新的建库记录，需重新导入建库结果流入上机环节，确认重建库吗？`

      // 2、展示重建库确认弹窗，点击「确定」，按规生成新的建库数据。
      await this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
      // 3、发送重建库请求
      const selectedIds = selectedRows.map(row => row.fid)
      const { res } = await awaitWrap(rebuildLibrary({
        fnewLibIdList: selectedIds
      }))

      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('重建库成功')
        await this.getData() // 刷新数据
      }
    },
    // 确认上机
    async handleConfirmComputed () {
      const selectedRows = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRows.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      // 判断异常状态
      const hasException = selectedRows.some(row => row.fexceptionRemark === 1)
      if (hasException) {
        this.$message.error('当前所选数据中含有异常状态的数据，请检查！')
        return
      }
      const hasComputed = selectedRows.some(row => +row.fproductionStatus === 2)
      if (hasComputed) {
        // this.$message.error('当前所选数据中含有“已上机”的数据，请检查！')
        await this.$confirm('当前所选数据中含有“已上机”的数据, 请确认是否再次上机！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }

      this.ids = selectedRows.map(v => v.fid)
      // 1、展示确认上机弹窗，点击「取消」，则不进行确认上机操作。
      this.confirmComputedVisible = true
    },
    // 取消建库
    async handleCancelLib () {
      // 1、用户勾选（单选/多选）数据，点击「撤销建库」；
      //       2、系统校验所选数据及该吉因加编号的裂分数据-”是否已上机“状态
      // 状态为”否“，进行二次提示确认，点击【确定】后在建库环节物理删除所选数据及该吉因加编号的裂分数据，解离环节中该吉因加编号状态恢复为”未建库“，后续允许重新下达确认建库指令。
      const selectedRows = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRows.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      const hasComputed = selectedRows.some(row => +row.fproductionStatus === 2)
      if (hasComputed) {
        this.$message.error(`样本${selectedRows.map(v => v.fgeneNum).join(',')}已上机，无法撤回，请检查！`)
        return
      }
      const message = `
      <h3>撤销建库</h3>
      <div>
        当前已选${selectedRows.length}条样本，确认撤销建库后数据将被删除,如需继续建库请在解离环节重新确认建库
      </div>
      `
      await this.$confirm(message, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
      const { res } = await awaitWrap(cancelBuildLib({
        fnewLibIdList: selectedRows.map(v => v.fid)
      }))
      console.log(res, res.code === this.SUCCESS_CODE)
      if (res && res.code === this.SUCCESS_CODE) {
        console.log(res, res.code === this.SUCCESS_CODE, '11111')
        this.$message.success('操作成功')
        await this.getData()
      }
    },
    handleSelectChange () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      this.selectedRowsSize = selectRecords.length
    },
    // 恢复默认表格配置
    handleResetTableConfig () {
      this.tableConfig = tableConfig
      localStorage.setItem('buildTableConfig', JSON.stringify(this.tableConfig))
      this.tableKey = Math.random()
      this.visible = false
    },
    // 拖拽排序
    async initSort () {
      await this.$nextTick()
      const el = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      // 根据具体需求配置options配置项
      Sortable.create(el, {
        handle: '.handle', // handle's class
        onEnd: (evt) => { // 监听拖动结束事件
          try {
            // 交换元素的逻辑，避免直接使用splice，以提高性能
            const temp = this.tableData[evt.oldIndex]
            this.tableData[evt.oldIndex] = this.tableData[evt.newIndex]
            this.tableData[evt.newIndex] = temp
          } catch (error) {
            console.error('Error during sorting:', error)
            // 可以进一步处理异常，例如回滚操作、显示错误信息等
          }
        }
      })
    },
    // 保存表格配置
    handleSave () {
      this.tableConfig = this.tableData.map(item => {
        if (item.isCustomerField) {
          item.isShow = !!this.selectedRows.has(item.id)
        }
        return item
      })
      localStorage.setItem('buildTableConfig', JSON.stringify(this.tableConfig))
      // this.$refs.tableRef.refreshColumn()
      this.tableKey = Math.random()
      this.visible = false
    },
    // 取消表格配置
    handleCancel () {
      this.visible = false
    },
    // 显示表格配置
    handleShowSetting () {
      this.initSort()
      this.visible = !this.visible
      // 回显选中的列
      this.tableData = this.tableConfig.filter(item => item.isCustomerField)
      this.tableData.forEach(item => {
        if (item.isShow) {
          this.selectedRows.set(item.id, item)
        }
      })
      this.handleEchoSelect()
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;

  .btn-group {
    margin-bottom: 10px;
  }
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
}

.setting-wrapper {
  height: 32px;
}

.operate-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin-top: 12px;

  .operate-item {
    flex: 1;
    cursor: pointer;
  }

  .operate-item:hover {
    color: #409EFF;
  }
}

.time-tips-item {
  border-bottom: 1px solid #ebeef5;
  padding: 5px 0;
}

.time-tips-item:last-child {
  border-bottom: none;
}

.table-item {
  width: 100%;
  // 单行省略
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/ .vxe-body--column {
  padding: 0 !important;
}

.warning-flag {
  &--pause { color: #E6A23C; }        // 暂停 - 橙色
  // 所有终止状态使用相同的红色
  &--terminate {
    &-pre,
    &-post,
    &-lib,
    &-seq { color: #F56C6C; }         // 终止状态 - 红色
  }
}
</style>
