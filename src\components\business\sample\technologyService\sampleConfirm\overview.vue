<template>
  <div class="page">
    <div class="content">
      <div class="search-form">
        <el-form ref="form" :model="form" label-width="90px" label-suffix=":" size="mini" inline
          @keyup.enter.native="handleSearch">
          <el-form-item label="到样时间">
            <el-date-picker v-model.trim="form.arrivalSampleDate" :default-time="['00:00:00', '23:59:59']"
              type="datetimerange" clearable size="mini" prefix-icon="el-icon-date" range-separator="~"
              value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" class="input-width"
              style="width: 310px">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <search-params-dialog :pvisible.sync="searchDialogVisible" @reset="handleReset" @search="handleSearch">
        <el-form ref="form" :model="form" label-width="90px" label-suffix=":" size="mini" inline
          @keyup.enter.native="handleSearch">
          <el-form-item label="样本类型">
            <el-select v-model.trim="form.sampleType" filterable clearable size="mini" class="input-width">
              <el-option v-for="item in sampleTypes" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Gene+编号">
            <el-input v-model.trim="form.geneCode" clearable placeholder="请输入Gene+编号" class="input-width"></el-input>
          </el-form-item>
          <el-form-item label="样本名称">
            <el-input v-model.trim="form.sampleName" clearable placeholder="请输入样本名称" class="input-width"></el-input>
          </el-form-item>
          <el-form-item label="项目名称">
            <el-input v-model.trim="form.projectName" clearable placeholder="请输入项目名称" class="input-width"></el-input>
          </el-form-item>
          <el-form-item label="项目编号">
            <el-input v-model.trim="form.projectCode" clearable placeholder="请输入项目编号" class="input-width"></el-input>
          </el-form-item>
          <el-form-item label="订单编号">
            <el-input v-model.trim="form.orderCode" clearable placeholder="请输入订单编号" class="input-width"></el-input>
          </el-form-item>
          <el-form-item label="送检单位">
            <el-input v-model.trim="form.inspectionDepartment" clearable placeholder="请输入送检单位"
              class="input-width"></el-input>
          </el-form-item>
          <el-form-item label="到样状态">
            <el-select v-model.trim="form.sampleState" size="mini" class="input-width">
              <el-option :value="0" label="待确认"></el-option>
              <el-option :value="1" label="已确认"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="到样时间">
            <el-date-picker v-model.trim="form.arrivalSampleDate" :default-time="['00:00:00', '23:59:59']"
              type="datetimerange" clearable size="mini" prefix-icon="el-icon-date" range-separator="~"
              value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" class="input-width"
              style="width: 310px">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </search-params-dialog>
      <div class="operate-btns-group">
        <template v-if="$setAuthority('018002002', 'buttons')">
          <el-button type="primary" size="mini" plain @click="messageChangeVisible">信息修改</el-button>
        </template>
        <template v-if="$setAuthority('018002003', 'buttons')">
          <el-button type="primary" size="mini" plain @click="registerUnusualVisible">登记异常</el-button>
        </template>
        <template v-if="$setAuthority('018002008', 'buttons')">
          <el-button type="primary" size="mini" plain @click="handleSampleStockIn">样本入库</el-button>
        </template>
        <template v-if="$setAuthority('018002003', 'buttons')">
          <el-button type="primary" size="mini" plain @click="handleProduct">生产</el-button>
        </template>
        <template v-if="$setAuthority('018002006', 'buttons')">
          <el-button v-if="downloadingExcelLoading" type="primary" plain size="mini" disabled><i
              class="el-icon-loading"></i> 正在导出</el-button>
          <el-dropdown v-else @command="(command) => handleExport(command, 1)" style="margin: 0 10px;">
            <el-button type="primary" size="mini" plain>运营导出<i
                class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template v-if="$setAuthority('018002007', 'buttons')">
          <el-button v-if="downloadingExcelLoading" type="primary" plain size="mini" disabled><i
              class="el-icon-loading"></i> 正在导出</el-button>
          <el-dropdown v-else @command="(command) => handleExport(command, 2)" style="margin: 0 10px;">
            <el-button type="primary" size="mini" plain>产量导出<i
                class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template>
          <el-button v-if="$setAuthority('018002005', 'buttons')" type="primary" size="mini" plain
            @click="handleCount">数据统计</el-button>
        </template>
        <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
      </div>
      <div class="table">
        <el-table ref="table" :data="tableData" :height="tbHeight"
          :cell-style="handleRowStyle" class="table" size="mini" border style="width: 100%" @select="handleSelectTable"
          @row-click="handleRowClick" @select-all="handleSelectAll">
          <el-table-column type="selection" fixed="left"></el-table-column>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="ft7OrderCode" label="订单号" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="link" @click="handleCheck(scope.row.realData, 2)">{{ scope.row.ft7OrderCode }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="oldSampleName" label="样本名称" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="storageTime" label="到样时间" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleNum" label="样例编号" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="geneSampleNum" label="吉因加编号" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleType" label="样本类型" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注信息" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="ftubeType" label="管材类型" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="transportType" label="运输方式" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="confirmStatus" label="到样状态" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="isInnerText" label="入库状态" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fexceptionType" label="异常分类" width="180">
            <template slot-scope="scope">
              <div v-if="scope.row.fexceptionType === '-'">
                {{ scope.row.fexceptionType }}
              </div>
              <el-tooltip v-else class="item" effect="dark" content="点击查看异常说明" placement="top">
                <span class="link" @click="handleOpenReasonDialog(scope.row.sampleConfirmId)">{{
                  scope.row.fexceptionType }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="exceptionRemark" label="异常描述" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleDeal" label="异常处理措施" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="isCreateOrder" label="是否已生产" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="originalSampleExpirationTime" label="原始样本到期时间" min-width="140"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="nucleicAcidExtractExpirationTime" label="核酸提取物到期时间" min-width="140"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="t7ProjectName" label="项目名称" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="t7ProjectCode" label="项目编码" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="proName" label="产品名称" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="inspectionUnit" label="送检单位" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productionAreaName" label="生产片区" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fconfirmUserName" label="操作人" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fconfirmTime" label="操作时间" width="140" show-overflow-tooltip></el-table-column>
        </el-table>
        <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
          <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage"
            :total="totalPage" layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh">
              <icon-svg icon-class="icon-refresh" />
            </button>
          </el-pagination>
        </div>
      </div>
    </div>

    <sample-confirm-import-dialog :pvisible.sync="importDialogVisible" @dialogConfirmEvent="getData" />

    <unusual-reason-dialog :sample-confirm-id="sampleId" :pvisible.sync="unusualReasonDialogVisible" />

    <message-channge-dialog :pvisible.sync="messagesChangeVisible" :sample-confirm-id="sampleId"
      :storage-time="arriveTimes" :ftube-type="tubularProductType" :remark="postscript"
      @messageChangeDialogConfirmEvent="meeagechangeDialogEvent" />

    <!-- <search-dialog :pvisible.sync="searchMessageVisible" @dialogConfirmEvent="handleSearch" /> -->
    <register-unusual-dialog :pvisible.sync="registerUnusualVisibles" :samples-num="samplesNum"
      :is-only-single-order="isOnlySingleOrder"
      :sample-confirm-id="choseSamples" :chose-unusual-sample="choseUnusualSample"
      @unusualSaveDialogConfirmEvent="registerUnusualEvent" />
    <sample-entry-dialog :pvisible.sync="sampleEntryVisible" :sample-confirm-ids="sampleConfirmIds"
      :sample-table-data="sampleTableData" :temperature-options="temperatureOptions" :cast-options="castOptions"
      @dialogConfirmEvent="getData"></sample-entry-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
// import util from '../../../util/util'
import mixins from '../../../../../util/mixins'
import sampleConfirmImportDialog from './sampleConfirmImportDialog'
import messageChanngeDialog from './messageChanngeDialog'
import searchDialog from './searchDialog'
import registerUnusualDialog from './registerUnusualDialog'
import UnusualReasonDialog from './unusualReasonDialog'
import sampleEntryDialog from './sampleEntryDialog'
import util from '../../../../../util/util'
import CountDialog from './countDialog'

export default {
  name: 'overview',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    CountDialog,
    UnusualReasonDialog,
    sampleConfirmImportDialog,
    messageChanngeDialog,
    searchDialog,
    registerUnusualDialog,
    sampleEntryDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.form.arrivalSampleDate = [util.dateFormatter(new Date(), false) + ' 00:00:00', util.dateFormatter(new Date(), false) + ' 23:59:59']
    this.handleSearch()
  },
  data () {
    return {
      form: {
        geneCode: '', // Gene+编号
        sampleName: '', // 样本名称
        projectName: '', // 项目名称
        projectCode: '', // 项目编号
        orderCode: '', // 订单编号
        inspectionDepartment: '', // 送检单位
        arrivalSampleDate: '', // 到样时间
        sampleState: '' // 样本状态
      },
      isOnlySingleOrder: false,
      sampleTypes: [
        {label: '外周血', value: '外周血'},
        {label: '组织', value: '组织'},
        {label: '石蜡包埋组织', value: '石蜡包埋组织'},
        {label: '血浆', value: '血浆'},
        {label: '细胞', value: '细胞'},
        {label: '全基因组DNA', value: '全基因组DNA'},
        {label: '游离DNA', value: '游离DNA'},
        {label: '羊水', value: '羊水'},
        {label: '干血片', value: '干血片'},
        {label: '唾液', value: '唾液'},
        {label: '拭子', value: '拭子'},
        {label: '胸腹水', value: '胸腹水'},
        {label: '脑脊液', value: '脑脊液'},
        {label: 'RNA', value: 'RNA'},
        {label: '动物组织', value: '动物组织'},
        {label: '植物组织', value: '植物组织'},
        {label: '微生物组织', value: '微生物组织'},
        {label: '杂交文库', value: '杂交文库'},
        {label: '文库', value: '文库'}
      ],
      sampleEntryVisible: false, // 样本入库
      downloadingExcelLoading: false,
      unusualReasonDialogVisible: false, // 异常说明
      importDialogVisible: false, // 导入弹窗
      messagesChangeVisible: false, // 信息修改弹窗
      searchMessageVisible: false, // 查询弹窗
      registerUnusualVisibles: false, // 登记异常弹窗
      productionDialogVisible: false, // 生产弹窗
      countVisible: false, // 统计弹窗
      selectedRows: new Map(),
      // 0未入库 1待入库 2已入库
      innerStatus: {
        0: '未入库',
        1: '待入库',
        2: '已入库'
      },
      sampleConfirmIds: [],
      arriveTimes: '', // 到样时间
      sampleId: null, // 样本确认表id
      tubularProductType: '', // 管材类型
      postscript: '', // 备注信息
      tableData: [],
      samplesNum: 0, // 所选样本数量
      choseSamples: [], // 所选样本编号
      choseUnusualSample: [], // 所选异常的样本编号
      searchDatas: {},
      sampleTableData: [],
      temperatureOptions: [],
      castOptions: []
    }
  },
  methods: {
    setParam () {
      let params = {}
      let gene = this.formSubmit.geneCode.split(',').filter(v => v)
      if (gene.length <= 0) {
        gene = null
      }
      this.formSubmit.arrivalSampleDate = this.formSubmit.arrivalSampleDate || []
      params.type = 2
      params.geneSampleNumList = gene
      params.originNum = this.formSubmit.sampleName
      params.t7ProjectName = this.formSubmit.projectName
      params.t7ProjectCode = this.formSubmit.projectCode
      params.sampleType = this.formSubmit.sampleType
      params.t7OrderCode = this.formSubmit.orderCode
      params.inspectionUnit = this.formSubmit.inspectionDepartment
      params.storageTimeStart = this.formSubmit.arrivalSampleDate[0]
      params.storageTimeEnd = this.formSubmit.arrivalSampleDate[1]
      params.confirmStatusList = this.formSubmit.sampleState === '' ? [] : [this.formSubmit.sampleState]
      return params
    },
    getData (data) {
      const params = this.setParam()
      this.$ajax({
        url: '/sample/confirm/page_sample_confirm',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: params
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleConfirmId,
              ft7OrderCode: v.ft7OrderCode,
              oldSampleName: v.oldSampleName,
              storageTime: v.storageTime,
              geneSampleNum: v.geneSampleNum,
              sampleNum: v.geneSampleNum ? v.sampleNum : null,
              productionArea: v.productionAreaName,
              sampleType: v.sampleType,
              remark: v.remark,
              sampleCount: v.sampleCount,
              transportType: v.transportType,
              ftubeType: v.ftubeType,
              confirmStatus: v.confirmStatus === 0 ? '待确认' : '已确认',
              fexceptionType: v.fexceptionType,
              exceptionRemark: v.exceptionRemark,
              sampleDeal: v.sampleDeal,
              isInner: v.isInner,
              isInnerText: this.innerStatus[v.isInner],
              isCreateOrder: v.isCreateOrder === 0 ? '否' : '是',
              t7ProjectName: v.t7ProjectName,
              t7ProjectCode: v.t7ProjectCode,
              originalSampleExpirationTime: v.foriginalSampleExpirationTime,
              nucleicAcidExtractExpirationTime: v.fnucleicAcidExtractExpirationTime,
              proName: v.proName,
              inspectionUnit: v.inspectionUnit,
              productionAreaName: v.productionAreaName,
              productionAreaId: v.productionAreaId,
              fconfirmUserName: v.fconfirmUserName,
              fconfirmTime: v.fconfirmTime,
              sampleConfirmId: v.sampleConfirmId,
              orderId: v.ft7OrderId,
              cosOrderType: v.cosOrderType
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.handleEchoSelect()
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 查看 type 1编辑 2 只读
    handleCheck (row, type) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type, // type 1编辑 2 只读
          orderId: row.orderId,
          status: 2,
          code: row.ft7OrderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.cosOrderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.cosOrderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.cosOrderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (row.cosOrderType === 5) path = '/business/subpage/technologyService/singleCell'
      if (path) util.openNewPage(path)
    },
    // 打开异常说明
    handleOpenReasonDialog (id) {
      this.unusualReasonDialogVisible = true
      this.sampleId = id
    },
    // 信息修改
    messageChangeVisible () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.confirmStatus !== '已确认') {
        this.$message.error('请选择已确认到样状态的样本记录')
        return false
      }
      this.sampleId = row.sampleConfirmId
      this.arriveTimes = row.storageTime
      this.tubularProductType = row.ftubeType
      this.postscript = row.realData.remark
      this.messagesChangeVisible = true
    },
    // 点击登记异常
    registerUnusualVisible () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择至少一条样本记录')
        return false
      }
      let selectedRowsMsg = [...this.selectedRows.values()]
      this.choseSamples = []
      this.choseUnusualSample = []
      for (let i = 0; i < selectedRowsMsg.length; i++) {
        if (selectedRowsMsg[i].confirmStatus !== '已确认') {
          this.$message.error('请选择已确认到样状态的样本记录')
          return false
        }
        if (selectedRowsMsg[i].confirmStatus === '已确认') {
          this.choseSamples.push(selectedRowsMsg[i].sampleConfirmId)
        }
        if ((selectedRowsMsg[i].fexceptionType !== '-' && selectedRowsMsg[i].fexceptionType !== undefined) ||
          (selectedRowsMsg[i].exceptionRemark !== '-' && selectedRowsMsg[i].exceptionRemark !== undefined)) {
          this.choseUnusualSample.push(selectedRowsMsg[i].geneSampleNum)
        }
      }
      this.isOnlySingleOrder = [...this.selectedRows.values()].every(item => item.cosOrderType === 5)
      this.registerUnusualVisibles = true
      this.samplesNum = this.selectedRows.size
    },
    // 导出
    async handleExport (command, type) {
      // 没有选择查询项
      if (command === 2 && this.selectedRows.size === 0) {
        this.$message.error('请选择需要导出的数据')
        return
      }
      let message = ''
      // 提示
      command === 1 ? message = '是否确认导出查询数据？' : message = '是否确认导出选中数据？'
      await this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let params = null
      switch (command) {
        case 1:
          params = this.setParam()
          break
        case 2:
          params = {
            fidList: Array.from(this.selectedRows.keys())
          }
          break
        default:
          break
      }
      if (params) {
        this.downloadExcel({ ftype: type, params: { ...params } })
      }
    },
    // 导出Excel
    downloadExcel (params) {
      this.downloadingExcelLoading = true
      this.$ajax({
        url: '/sample/confirm/export_sample',
        data: params,
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadingExcelLoading = false
      })
    },
    // 生产
    async handleProduct () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let selectValues = [...this.selectedRows.values()]
      this.choseSamples = []
      for (let i = 0; i < selectValues.length; i++) {
        if (selectValues[i].confirmStatus !== '已确认') {
          this.$message.error('请选择已确认到样状态的样本记录')
          return false
        }
        if (selectValues[i].confirmStatus === '已确认') {
          this.choseSamples.push(selectValues[i].sampleConfirmId)
        }
      }
      await this.$confirm(`确认对所选样本下达生产指令吗?`, '生产', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.handleConfirm()
    },
    // 样本入库
    async handleSampleStockIn () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条样本记录')
        return
      }
      const rows = [...this.selectedRows.values()]
      // 判断片区
      const productionAreaName = rows[0].productionAreaName
      const isNotSample = rows.some(row => row.productionAreaName !== productionAreaName)
      if (isNotSample) {
        this.$message.error('所选样本片区必须相同！')
        return
      }

      const innerNotSample = rows.filter(row => row.isInner === 0) || []
      if (innerNotSample.length === 0) {
        this.$message.error('所选样本均已申请入库，请重新选择！')
        return
      }
      const innerSample = rows.filter(row => row.isInner !== 0) || []
      if (innerSample.length > 0) {
        const innerSampleNums = innerSample.map(v => v.geneSampleNum).join(',')
        this.$message.warning(innerSampleNums + '已申请入库，已自动跳过，请知悉！')
      }

      this.sampleConfirmIds = innerNotSample.map(v => v.id)
      this.$ajax({
        url: '/sample/confirm/check_can_batchIn_warehouse',
        data: {
          sampleConfirmIds: this.sampleConfirmIds.join(',')
        },
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || ''
          data = JSON.parse(data)
          this.temperatureOptions = data.temperatureList || []
          this.castOptions = data.tubeList || []
          this.sampleEntryVisible = true
          // 传递参数
          this.sampleTableData = innerNotSample.map(v => {
            return {
              sampleType: v.sampleType,
              geneSampleNum: v.geneSampleNum,
              oldSampleName: v.oldSampleName,
              sampleCount: v.sampleCount,
              productionAreaId: v.productionAreaId
            }
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击生产
    productionVisible () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let selectValues = [...this.selectedRows.values()]
      this.choseSamples = []
      for (let i = 0; i < selectValues.length; i++) {
        if (selectValues[i].confirmStatus !== '已确认') {
          this.$message.error('请选择已确认到样状态的样本记录')
          return false
        }
        if (selectValues[i].confirmStatus === '已确认') {
          this.choseSamples.push(selectValues[i].sampleConfirmId)
        }
      }
      this.productionDialogVisible = true
    },
    // 点击生产确定
    handleConfirm () {
      this.$ajax({
        method: 'get',
        url: '/sample/confirm/production_sample',
        data: {
          sampleConfirmIdList: this.choseSamples.toString()
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.productionDialogVisible = false
          this.$message.success('生产成功')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 数据统计
    handleCount () {
      this.countVisible = true
    },
    meeagechangeDialogEvent () {
      this.messagesChangeVisible = false
      this.getData()
    },
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.form.arrivalSampleDate = [util.dateFormatter(new Date(), false) + ' 00:00:00', util.dateFormatter(new Date(), false) + ' 23:59:59']
      this.handleSearch()
    },
    registerUnusualEvent () {
      this.registerUnusualVisibles = false
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
.input-width {
  width: 150px;
}

.buttonGroup {
  margin: 10px 0;
  height: 40px;
  line-height: 40px;
}

.table {
  height: calc(100vh - 230px);
}

.link {
  color: $color;
  cursor: pointer
}
</style>
