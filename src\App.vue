<template>
  <div id="app">
    <keep-alive>
      <router-view/>
    </keep-alive>
  </div>
</template>

<script>

export default {
  name: 'App',
  mounted () {
    // console.log(constants.AUTO_UPDATE_SCREEN_SIZE)
    // if (constants.AUTO_UPDATE_SCREEN_SIZE) {
    //   this.handleResetPixelRatio()
    // }
  },
  methods: {
    // async handleResetPixelRatio () {
    //   // const m = detectZoom()
    //   // this.setZoom()
    //   // window.addEventListener('resize', this.setZoom)
    //   // 用户初次进入或点击取消 且 设备像素比不为100
    //   // if (!this.$store.getters.getValue('pixelRatio') && m !== 100) {
    //   //   try {
    //   //     await this.$confirm('检测到您的设备像素比为' + m + '%，可能会导致页面显示模糊，是否重置设备像素比？', '提示', {
    //   //       confirmButtonText: '确定',
    //   //       cancelButtonText: '取消',
    //   //       type: 'warning'
    //   //     })
    //   //     this.$store.commit({
    //   //       type: 'setValue',
    //   //       category: 'pixelRatio',
    //   //       pixelRatio: true
    //   //     })
    //   //     this.setZoom()
    //   //   } catch (e) {
    //   //     // 用户不同意
    //   //     this.$store.commit({
    //   //       type: 'setValue',
    //   //       category: 'pixelRatio',
    //   //       pixelRatio: false
    //   //     })
    //   //   }
    //   // } else {
    //   //   this.setZoom()
    //   // }
    // },
    // setZoom () {
    //   const m = detectZoom()
    //   document.body.style.zoom = 100 / Number(m)
    //   Vue.prototype.zoomVh = window.innerHeight * (Number(m) / 100) + 'px'
    //   document.body.style.setProperty('--zoomVh', Vue.prototype.zoomVh)
    // }

  }
}
</script>

<style lang="scss">
  :root{
    --primary-color: #409EFF;
    --zoomVh: 100vh;
  }
  *{
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    /*滚动条的宽度*/
    & ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    /*滚动条的滑块*/
    & ::-webkit-scrollbar-thumb {
      background-color: #999999;
      border-radius: 5px;
    }
  }
  body {
    margin: 0;
    padding: 0;
  }
  .el-dialog__header,
  .el-drawer__header{
    background: #D6DBE1;
    padding: 20px;
    font-size: 16px;
    font-weight: 700;
    color: black;
  }
  .el-dialog__body{
    //padding: 40px 30px!important;
  }
  .el-drawer__body{
    padding: 12px 20px;
    .el-scrollbar__wrap{
      overflow: auto !important;
    }
  }
  .el-drawer__header{
    padding: 0 10px;
    margin-bottom: 0;
  }
  .link {
    color: $color;
    cursor: pointer
  }
  .el-table th{
    background: #f2f2f2!important;
  }
  .form-mask{
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    padding-right: 1px;
    z-index: 4;
    display: flex;
    /*justify-content: flex-end;*/
    align-items: center;
    background-color: #fff;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    color: rgb(192, 196, 204);
  }
  table {
    width: 100%;
  }
  .card-wrapper {
    padding: 10px;
    width: 100%;
  }
  .el-pagination{
    padding: 7px 2em !important;
  }
  .el-tooltip__popper {
     max-width: 600px;
  }
  .link {
    color: $color;
    cursor: pointer
  }
  .el-notification {
    width: 600px !important;
  }
  .fix-dropdown-margin {
    margin-left: 10px;
  }
  // 更多查询内部的长度
  .params-search-form{
    .form-width { // 短的form
      width: 250px;
    }
    .form-long-width{ // 长的form，比如日期选择器
      width: 510px;
    }
  }
</style>
