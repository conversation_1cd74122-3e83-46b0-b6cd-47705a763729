<template>
  <el-dialog
    title="导入结果"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="700px"
    @open="handleOpen">
    <el-form v-if="!isUpload" :model="form" label-suffix=":" :rules="rules" label-width="70px">
      <div>
        <el-upload
          ref="upload"
          :auto-upload="false"
          :file-list="fileList"
          :action="uploadUrl"
          :data="uploadParams"
          :before-upload="handleBeforeUpload"
          :on-change="handleChange"
          :on-success="handleOnSuccess"
          style="text-align: center;"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
            <br/>
            仅支持 xls、xlsx，只能上传1份小于10M的文件；
          </div>
        </el-upload>
      </div>

      <div class="flex-wrapper">
        <el-form-item label="操作人">
          <el-select
            v-model.trim="form.operator"
            placeholder="请选择"
            size="mini"
            style="width: 100%;"/>
        </el-form-item>
        <el-form-item label="复核人">
          <el-select
            v-model.trim="form.operator"
            placeholder="请选择"
            size="mini"
            style="width: 100%;"/>
        </el-form-item>
      </div>
    </el-form>
    <div v-if="isUpload" class="result">
      <div style="margin-bottom: 10px">
        请确认导入内容无误，提交后不可撤销：
      </div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="120"></el-table-column>
        <el-table-column prop="sampleTime" label="到样时间" width="120"></el-table-column>
        <el-table-column prop="customerSampleNumber" label="客户样本编号" width="120"></el-table-column>
        <el-table-column prop="geneNumber" label="吉因加编号" width="120"></el-table-column>
        <el-table-column prop="libraryModificationType" label="文库修饰类型" width="120"></el-table-column>
        <el-table-column prop="storageLocation" label="存储位置" width="120"></el-table-column>
        <el-table-column prop="concentration" label="浓度(ng/ul)" width="120"></el-table-column>
        <el-table-column prop="volume" label="体积(ul)" width="120"></el-table-column>
        <el-table-column prop="averageFragmentSize" label="平均片段大小" width="120"></el-table-column>
        <el-table-column prop="libraryTotal" label="文库总量(ng)" width="120"></el-table-column>
        <el-table-column prop="qcResult" label="质检结果" width="120"></el-table-column>
        <el-table-column prop="remark" label="备注" width="120"></el-table-column>
        <el-table-column prop="tag" label="标签" width="120"></el-table-column>
        <el-table-column prop="detectionPerson" label="检测人" width="120"></el-table-column>
        <el-table-column prop="detectionTime" label="检测时间" width="120"></el-table-column>
        <el-table-column prop="orderNumber" label="订单编号" width="120"></el-table-column>
        <el-table-column prop="projectNumber" label="项目编号" width="120"></el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button v-if="!isUpload" :disabled="loading" type="primary" size="mini" @click="handleNext">下一步</el-button>
      <el-button v-if="isUpload" :disabled="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  name: 'uploadResultDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  data () {
    return {
      isUpload: false,
      fileList: [],
      uploadUrl: '',
      uploadParams: {},
      form: {},
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.isUpload = false
    },
    handleBeforeUpload () {},
    handleChange () {},
    handleOnSuccess () {},
    // 下一步
    handleNext () {
      this.isUpload = true
      // todo 导入文件
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-upload-dragger {
  width: 100%;
  height: 100%;
  padding: 20px;
}
/deep/ .el-upload {
  width: 100%;
  height: 100%;
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
}

</style>
