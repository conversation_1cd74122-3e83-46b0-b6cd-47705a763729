<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :modal="true"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="生成报告"
      width="31%"
      @open="handleOpen"
    >
      <div class="wrapper">
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="报告模版" prop="templateName">
            <el-select v-model.trim="form.templateName" style="width: 90%" size="mini" placeholder="请选择">
              <el-option
                :key="index"
                :label="item"
                :value="item"
                v-for="(item, index) in templates">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="showElectrophoreticGelPic" label="上传电泳胶图">
            <el-upload
              :file-list="file4"
              :action="actionUrl"
              :data="{
                reportId: reportId,
                picType: 'electrophoreticGelPic'
              }"
              :on-success="handleSuccess"
              :before-remove="() => handleBeforeRemove('electrophoreticGelPic')"
              :before-upload="beforeImgUpload"
              class="upload-demo"
              drag
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传图片或zip文件，且不超过50M</div>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="showElectrophoreticTestPic" label="上传电泳检测结果">
            <el-upload
              :file-list="file4"
              :action="actionUrl"
              :data="{
                reportId: reportId,
                picType: 'electrophoreticTestPic'
              }"
              :on-success="handleSuccess"
              :before-remove="() => handleBeforeRemove('electrophoreticTestPic')"
              :before-upload="beforeImgUpload"
              class="upload-demo"
              drag
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传图片或zip文件，且不超过50M</div>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="showAilentCompleteTestPic" label="上传安捷伦完整性检测结果">
            <el-upload
              :file-list="file4"
              :action="actionUrl"
              :data="{
                reportId: reportId,
                picType: 'agilentCompleteTestPic'
              }"
              :on-success="handleSuccess"
              :before-remove="() => handleBeforeRemove('agilentCompleteTestPic')"
              :before-upload="beforeImgUpload"
              class="upload-demo"
              drag
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传图片或zip文件，且不超过50M</div>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="showPartTestPic" label="上传片段检测结果">
            <el-upload
              :file-list="file4"
              :action="actionUrl"
              :data="{
                reportId: reportId,
                picType: 'partTestPic'
              }"
              :on-success="handleSuccess"
              :before-remove="() => handleBeforeRemove('partTestPic')"
              :before-upload="beforeImgUpload"
              class="upload-demo"
              drag
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传图片或zip文件，且不超过50M</div>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="showQsepOrLabchipPic" label="上传Qsep电泳/Labchip 电泳图谱">
            <el-upload
              :file-list="file4"
              :action="actionUrl"
              :data="{
                reportId: reportId,
                picType: 'qsepOrLabchipPic'
              }"
              :on-success="handleSuccess"
              :before-remove="() => handleBeforeRemove('qsepOrLabchipPic')"
              :before-upload="beforeImgUpload"
              class="upload-demo"
              drag
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">只能上传图片或zip文件，且不超过50M</div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="createWordReport" >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'
import constants from '../../../../../util/constants'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleIds: {
      type: String
    }
  },
  computed: {
    // 电泳胶图
    showElectrophoreticGelPic () {
      return [
        'JYJSZ-SOP-CS-071-4 GMseq 组织和细胞gDNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-6 基因组DNA 质控报告(PCR-free 或 2项目）V1.0',
        'JYJSZ-SOP-CS-071-7 基因组DNA 质控报告 V1.0',
        'JYJSZ-SOP-CS-071-8 PCR产物 质控报告 V1.0'
      ].includes(this.form.templateName)
    },
    // 电泳检测结果
    showElectrophoreticTestPic () {
      return [
        'JYJSZ-SOP-CS-074-1 lncRNA质控报告 V1.0',
        'JYJSZ-SOP-CS-074-2 mRNA质控报告模板 V1.0',
        '单细胞项目RNA质控报告'
      ].includes(this.form.templateName)
    },
    // 安捷伦完整性检测结果
    showAilentCompleteTestPic () {
      return [
        'JYJSZ-SOP-CS-074-1 lncRNA质控报告 V1.0',
        'JYJSZ-SOP-CS-074-2 mRNA质控报告模板 V1.0',
        '单细胞项目RNA质控报告'
      ].includes(this.form.templateName)
    },
    // 片段检测结果
    showPartTestPic () {
      return [
        'JYJSZ-SOP-CS-071-1 cfDNA 质控报告 V1.0',
        'JYJSZ-SOP-CS-071-2 GMseq cfDNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-3 GMseq FFPE DNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-5 FFPE DNA质控报告 V1.0'
      ].includes(this.form.templateName)
    },
    // Qsep电泳/Labchip电泳图谱
    showQsepOrLabchipPic () {
      return [
        '文库报告模板V1.0'
      ].includes(this.form.templateName)
    }
  },
  data () {
    return {
      actionUrl: constants.JS_CONTEXT + '/order/report/import_report_result_pic',
      reportId: '',
      loading: false,
      file1: [],
      file2: [],
      file3: [],
      file4: [],
      file5: [],
      rules: {
        templateName: [
          {required: true, message: '请选择模版', trigger: ['change', 'blur']}
        ]
      },
      templates: [
        'JYJSZ-SOP-CS-071-1 cfDNA 质控报告 V1.0',
        'JYJSZ-SOP-CS-071-2 GMseq cfDNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-3 GMseq FFPE DNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-4 GMseq 组织和细胞gDNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-5 FFPE DNA质控报告 V1.0',
        'JYJSZ-SOP-CS-071-6 基因组DNA 质控报告(PCR-free 或 2项目）V1.0',
        'JYJSZ-SOP-CS-071-7 基因组DNA 质控报告 V1.0',
        'JYJSZ-SOP-CS-071-8 PCR产物 质控报告 V1.0',
        'JYJSZ-SOP-CS-074-1 lncRNA质控报告 V1.0',
        'JYJSZ-SOP-CS-074-2 mRNA质控报告模板 V1.0',
        '文库报告模板V1.0'
      ],
      form: {
        templateName: ''
      }
    }
  },
  methods: {
    handleOpen () {
      this.form.templateName = ''
      this.file1 = []
      this.file2 = []
      this.file3 = []
      this.file4 = []
      this.file5 = []
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
      this.initReport()
    },
    async handleClose () {
      await this.$confirm('关闭后上传的数据将被清空是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      let {code, message} = await this.$ajax({
        url: '/order/report/delete_report',
        data: {
          reportId: this.reportId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.visible = false
        this.$message.success('删除成功')
      } else {
        this.$message.error(message)
      }
    },
    // 生成报告
    async initReport () {
      let {code, data, message} = await this.$ajax({
        url: '/order/report/create_report_data',
        data: {
          sampleIds: this.sampleIds
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.reportId = data
      } else {
        this.visible = false
        this.$message.error(message)
      }
    },
    // 生成报告
    createWordReport () {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            let {code, message} = await this.$ajax({
              url: '/order/report/create_word_report',
              data: {
                reportId: this.reportId,
                templateName: this.form.templateName,
                sampleIds: this.sampleIds
              },
              method: 'get'
            })
            console.log(code)
            if (code === this.SUCCESS_CODE) {
              this.visible = false
              this.$emit('dialogConfirmEvent')
              this.$message.success('生成成功')
            } else {
              this.message.error(message)
            }
          } finally {
            this.loading = false
          }
        }
      })
    },
    /** ***********************图片上传相关接口**************************************/
    handleSuccess (response, file, fileList) {
      if (response.code !== this.SUCCESS_CODE) {
        // 删除上传失败的文件
        let index = 0
        for (const i in fileList) {
          if (fileList[i] === file) {
            index = i
            break
          }
        }
        // 移出当前文件对象
        fileList.splice(index, 1)
        this.$message.error(response.message)
      }
    },
    // 上传之前的处理函数
    beforeImgUpload (file) {
      const isImg = file.type.indexOf('image') > -1
      let name = file.name
      const isZip = /\.(zip|rar)$/i.test(name)
      if (file.size > 50 * 1024 * 1024) {
        this.$message.error(`文件: ${file.name} ,大小超过50M，无法上传`)
        return false
      }
      if (!isImg && !isZip) {
        this.$message.error('只能上传图片和压缩包！')
        return false
      }
      return isImg || isZip
    },
    handleBeforeRemove (type) {
      this.$ajax({
        url: '/order/report/remove_report',
        data: {
          reportId: this.reportId,
          picType: type
        },
        method: 'get'
      }).then(res => {
        let {code, message} = res
        if (code === this.SUCCESS_CODE) {
          // this.$message.success('删除成功')
        } else {
          this.$message.error(message)
        }
      })
    },
    // 浏览图片
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped>
.wrapper {
  max-height: 400px;
  overflow: auto;
}
</style>
