<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="结果上传"
      width="700px"
      @open="handleOpen">
      <el-form :model="form" label-position="top" label-width="80px" size="mini" inline>
        <el-form-item label="线下检测类型">
          <el-select v-model="form.detectType" size="mini">
            <el-option :value="1" label="MGMT启动因子甲基化"></el-option>
            <el-option :value="2" label="PD-L1 IHC SP263"></el-option>
            <el-option :value="3" label="PD-L1 IHC 22C3"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="form.detectType">
          <el-form-item label="检测公司">
            <el-select v-model="form.company">
              <el-option label="北京圣谷智汇医学检验所" value="1"></el-option>
              <el-option label="上海迪安医学检验所" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检测设备">
            <el-select v-model="form.equipment">
              <el-option label="Roche Ventana BenchMark Ultra autostainer" value="1"></el-option>
            </el-select>
          </el-form-item>
        </template>
        <template  v-if="form.detectType === 2 || form.detectType === 3">
          <el-form-item label="检测试剂">
            <el-select v-model="form.reagent">
              <el-option label="PD-L1 SP263, Ventana" value="1"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div class="upLoad">
        <span>绘图文件：</span>
        <el-upload
          ref="upload"
          :disabled="disabled"
          :on-success="handleOnSuccess"
          :on-error="handleOnError"
          :data="uploadParams"
          :auto-upload="false"
          :limit="1"
          :file-list="fileList"
          :before-upload="handleBeforeUpload"
          :action="uploadUrl">
          <span class="btn"><i class="el-icon-upload"></i>上传文件</span>
          <!--<span slot="tip" class="el-upload__tip">仅支持Excel</span>-->
        </el-upload>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary">确定</el-button>
        <el-button size="mini">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
export default {
  name: 'offlineDetectManagementUploadResultDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      form: {
        detectType: '',
        company: '',
        equipment: '',
        reagent: ''
      },
      uploadUrl: constants.JS_CONTEXT + '/sample/confirm/import_sample_sheet',
      uploadParams: {
        productArea: ''
      },
      fileList: [],
      loading: false,
      disabled: false,
      rules: {
        detectType: [
          { required: true, message: '线下检测类型', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {},
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res.statusCode === this.SUCCESS_CODE) {
        this.$emit('dialogConfirmEvent')
      } else {
        this.$msg.err(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        let vaildSize = size <= 10 * 1024 * 1024 * 8
        if (!vaildSize) {
          this.loading = false
          this.$msg.err('文件大小超过限制，无法上传')
        }
        return vaildSize
      } else {
        this.loading = false
        this.$msg.err('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$msg.err('上传出现错误')
    }
  }
}
</script>

<style scoped>

</style>
