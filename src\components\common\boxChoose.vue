<template>
  <div class="wrap" v-loading="getContainerLoading">
    <div>
      <div class="tip-text">当前位置：{{currentBox.showPosName}}</div>
      <div class="lab-filter">
        <label>实验室：</label>
        <el-select v-model="filterLab" size="mini" clearable>
          <el-option label="全部" value=""></el-option>
          <template v-for="lab in labList">
            <el-option :key="lab" :label="lab" :value="lab"></el-option>
          </template>
        </el-select>
      </div>
      <div class="tree-wrap" :style="{height: height}">
        <el-tree
            ref="tree"
            :props="defaultProps"
            node-key="id"
            lazy
            :load="getTreeData"
            :filter-node-method="filterTreeNode"
            @node-click="handleClickBox">
          <div slot-scope="{ node }">
            <div style="display: flex;align-items: center;" v-html="node.label"></div>
          </div>
        </el-tree>
      </div>
    </div>
    <div style="width: 100%;overflow: hidden;">
      <div style="display: flex;align-items: center;height: 40px;padding: 0 10px;">
        <el-checkbox
          v-if="showCheckAll"
          v-model="checkAll"
          label="全选"
          border
          size="mini"
          @change="handleChooseAll"></el-checkbox>
      </div>
      <div style="width: 100%;overflow: auto;background: #EBEEF5;" :style="{height: height}">
        <div v-if="currentBox.boxes.length !== 0" class="hole-wrap">
          <div class="hole-row">
            <div class="y-text"></div>
            <div v-for="item in currentBox.column" :key="item" class="x-text">{{addZero(item)}}</div>
          </div>
          <div class="hole-row" v-for="(v, i) in currentBox.boxes" :key="i">
            <div class="y-text">{{numToUppercase(i + 1)}}</div>
            <div
                class="hole"
                v-for="(vv, ii) in v"
                :key="vv.id"
                :style="{borderColor: currentChecked[vv.id] ? '#1687ff' : 'transparent'}"
                @click="handleChoose(vv)">
              <el-tooltip effect="dark" :content="vv.sampleCode" :disabled="!vv.sampleCode || vv.sampleCode.length < 14" placement="top">
                <div>
                  <input
                      v-model="vv.sampleCode"
                      type="text"
                      :id="vv.id"
                      readonly
                      :style="holeStyle(vv)"
                      @keyup.enter="handleEnter(vv)"
                      @blur="handleBlur(vv)"
                      @click="handleInputClick(vv, {row: i, column: ii})">
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

// import xx form 'xxx'
import util from '../../util/util'
export default {
  name: 'boxChoose',
  props: {
    limitSampleCode: {
      type: Array
    },
    height: {
      type: String,
      default: '300px'
    },
    // 是否可以编辑 true 能编辑 false 不能编辑 empty 仅没有样本的可以编辑
    canEdit: {
      type: String | Boolean,
      default: 'empty'
    },
    // 是否能够选中
    canChoose: {
      type: Boolean,
      default: false
    },
    currentSample: {
      type: String
    },
    currentPosId: { // 当前外部可以输入编号
      type: String
    }
  },
  mounted () {
    if (this.$route.query.lab) {
      this.filterLab = this.$route.query.lab
    }
  },
  watch: {
    currentSelectBox: {
      handler: function (newVal) {
        this.boxChange(newVal)
      }
    },
    filterLab (val) {
      this.$refs.tree.filter(val)
    }
  },
  computed: {
    showCheckAll () {
      return this.canChoose && this.currentBox.boxes.length !== 0
    },
    labList () {
      const labs = this.containerLists.map(v => v.lab)
      return [...new Set(labs)].filter(v => v)
    }
  },
  data () {
    return {
      filterLab: '',
      checkAll: false, // 是否全选
      getContainerLoading: false,
      containerLists: [],
      currentBoxNode: {},
      floorLists: [],
      shelfLists: [],
      boxLists: [],
      currentSelectBox: null, // 当前选择的盒子
      boxSelectedId: '',
      currentBox: { // 当前盒子
        boxes: [],
        column: 0,
        row: 0,
        sampleType: '',
        showPosName: '', // 展示的位置名称
        pos: '',
        containerInfo: {}
      },
      currentChecked: {},
      defaultProps: {
        isLeaf: 'isLeaf'
      },
      hasDoEnterEvent: false
    }
  },
  methods: {
    inputDisabled (item) {
      if (this.canEdit === 'empty') {
        return item.hasSample
      }
      return !this.canEdit
    },
    holeStyle (vv) {
      const style = {
        background: this.inputDisabled(vv) ? '#909399' : '#606266'
      }
      if (this.currentPosId === vv.id) {
        style.border = '2px solid #67C23A'
      } else {
        // 不同状态的border
        const borders = {
          move: '2px solid #67C23A',
          out: '2px dotted #E4E7ED'
        }
        const border = borders[vv.sampleOperateType]
        if (border) style.border = border
      }
      return style
    },
    // 过滤树
    filterTreeNode (value, data, node) {
      const l = node.level
      const lab = data.lab
      return !value || l !== 1 || (l === 1 && lab === value)
    },
    async getTreeData (node, resolve) {
      if (node.level === 0) {
        const data = await this.getContainer()
        // const data = [
        //   {id: 7, label: 'ss(SZB1)', children: []},
        //   {id: 8, label: 'ss(SZB3)', children: []}
        // ]
        return resolve(data)
      } else if (node.level === 1) {
        const data = await this.getFloor(node.data.id)
        return resolve(data)
      } else if (node.level === 2) {
        const data = await this.getShelf(node.data)
        return resolve(data)
      } else if (node.level === 3) {
        this.currentBoxNode = node.data
        const data = await this.getBox(node.data)
        return resolve(data)
      }
    },
    setContainerTreeData ({ containerId, floorId, shelfId }, list) {
      const container = this.containerLists.find(v => v.id === containerId)
      if (!floorId) {
        container.children = util.deepCopy(list)
        return
      }
      const floor = container.children.find(v => v.id === floorId)
      if (!shelfId) {
        floor.children = util.deepCopy(list)
        return
      }
      const shelf = floor.children.find(v => v.id === shelfId)
      shelf.children = util.deepCopy(list)
    },
    // 获取容器
    getContainer () {
      return new Promise(resolve => {
        this.containerLists = []
        this.getContainerLoading = true
        this.$ajax({
          url: '/sample/container/get_container_list',
          data: {
            pageRequest: {
              current: 1,
              size: 1000
            },
            flabNo: 0
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            let data = Array.isArray(res.data.rows) ? res.data.rows : []
            data.forEach(v => {
              let item = {
                id: v.fid,
                lab: v.flab,
                type: v.ftype,
                name: v.fname,
                label: v.fnickName ? `${v.fnickName}(${v.fname})` : v.fname,
                nickName: v.fnickName,
                temperature: v.ftemperature,
                totalCapacity: v.ftotalCapacity,
                availableHole: v.favailableHole,
                storagePercentage: v.fstoragePercentage,
                imgUrl: v.fpicture
              }
              this.containerLists.push(item)
            })
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          this.getContainerLoading = false
          setTimeout(() => {
            resolve(this.containerLists)
            this.$nextTick(() => {
              this.$refs.tree.filter(this.filterLab)
            })
          })
        })
      })
    },
    // 设置样本类型文字超长时候的展示
    setHtmlOverflowText (text) {
      return `<span style="display: inline-block; max-width: 12em;white-space: nowrap;text-overflow: ellipsis;overflow: hidden" title="${text}">${text}</span>`
    },
    // 获取层
    getFloor (id) {
      return new Promise(resolve => {
        this.floorLists = []
        this.shelfLists = []
        this.boxLists = []
        this.$ajax({
          url: '/sample/container/floor/get_floor_list',
          data: {
            containerId: id
          },
          method: 'get'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            let data = res.data || []
            this.floorLists = []
            data.forEach(v => {
              let item = {
                containerId: v.fcontainerId,
                id: v.fid,
                num: v.ffloorNumber,
                sampleType: v.fsampleType,
                canUseHoleNum: v.ffloorAvailableHole,
                label: `${v.ffloorNumber}层(${this.setHtmlOverflowText(v.fsampleType)}${v.ffloorAvailableHole === null ? '' : '，可用：' + v.ffloorAvailableHole})`
              }
              this.floorLists.push(item)
            })
            this.setContainerTreeData({containerId: id}, this.floorLists)
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          setTimeout(() => {
            resolve(this.floorLists)
          })
        })
      })
    },
    // 获取架
    getShelf (floor) {
      return new Promise(resolve => {
        this.shelfLists = []
        this.boxLists = []
        this.$ajax({
          url: '/sample/container/shelf/get_shelf_list',
          method: 'get',
          data: {
            floorId: floor.id
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            let rows = res.data || []
            this.shelfLists = []
            rows.forEach(v => {
              let item = {
                containerId: v.fcontainerId,
                floorId: v.ffloorId,
                id: v.fid,
                shelfNumber: v.fshelfNumber,
                floorNumber: v.ffloorNumber,
                sampleType: v.fsampleType,
                shelfStoragePercentage: v.fshelfStoragePercentage,
                shelfTotalCapacity: v.fshelfTotalCapacity,
                shelfUsedHole: v.fshelfUsedHole,
                shelfAvailableHole: v.fshelfAvailableHole
              }
              item.label = `${item.shelfNumber}架(${this.setHtmlOverflowText(item.sampleType)}${item.shelfAvailableHole === null ? '' : '，可用：' + item.shelfAvailableHole})`
              this.shelfLists.push(item)
            })
            this.setContainerTreeData({ containerId: floor.containerId, floorId: floor.id }, this.shelfLists)
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          setTimeout(() => {
            resolve(this.shelfLists)
          })
        })
      })
    },
    // 获取盒
    getBox (shelf) {
      return new Promise(resolve => {
        this.boxLists = []
        this.$ajax({
          url: '/sample/container/box/get_box_list',
          method: 'get',
          data: {
            shelfId: shelf.id
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.boxLists = []
            let rows = res.data || []
            rows.forEach(v => {
              let item = {
                containerId: v.fcontainerId,
                floorId: v.ffloorId,
                shelfId: v.fshelfId,
                id: v.fid,
                boxNumber: v.fboxNumber,
                boxUsedHole: v.fboxUsedHole,
                boxAvailableHole: v.fboxAvailableHole,
                holeTotalCount: v.fholeTotalCount,
                sampleType: v.fsampleType,
                boxStoragePercentage: v.fboxStoragePercentage,
                xSize: +v.fxSize,
                ySize: +v.fySize,
                hasUsedHole: v.fboxUsedHoleNumber ? v.fboxUsedHoleNumber.split(',') : '',
                holeSampleNumberObj: this.getHoleObj(v.holeSampleNumberList),
                pos: `${v.fcontainerInfo.fname}-${v.ffloorNumber}L-${v.fshelfNumber}S-${v.fboxNumber}B`,
                containerInfo: v.fcontainerInfo,
                isLeaf: true
              }
              item.showPosName = v.fcontainerInfo.fnickName ? `${v.fcontainerInfo.fnickName}(${item.pos})` : item.pos
              item.label = `${item.boxNumber}盒(${this.setHtmlOverflowText(item.sampleType)}${item.boxAvailableHole === null ? '' : '，可用：' + item.boxAvailableHole})`
              this.boxLists.push(item)
            })
            this.setContainerTreeData({ containerId: shelf.containerId, floorId: shelf.floorId, shelfId: shelf.id },
              this.boxLists)
          } else {
            this.$message.error(res.message)
          }
        }).finally(() => {
          setTimeout(() => {
            resolve(this.boxLists)
          })
        })
      })
    },
    // 将已经填入的样本数组改为 id: sampleCoded形式
    getHoleObj (list) {
      const r = {}
      if (list && Array.isArray(list)) {
        list.forEach(v => {
          r[v.holeNumber] = {
            sampleCode: v.sampleNumber,
            sampleId: v.sampleId
          }
        })
      }
      return r
    },
    // 点击盒
    handleClickBox (data, node) {
      if (node.isLeaf) {
        if (this.boxSelectedId !== data.id) {
          this.$emit('boxChange')
        }
        this.boxSelectedId = data.id
        this.currentSelectBox = data
      }
    },
    handleChooseAll () {
      this.currentChecked = {}
      if (this.checkAll) {
        this.currentBox.boxes.forEach(arr => {
          arr.forEach(v => {
            if (v.sampleCode) {
              this.$set(this.currentChecked, v.id, v)
            }
          })
        })
      }
    },
    // 选择孔位
    handleChoose (vv) {
      if (!this.canChoose || !vv.sampleCode) return
      if (this.currentChecked[vv.id]) {
        this.$delete(this.currentChecked, vv.id)
      } else {
        this.$set(this.currentChecked, vv.id, vv)
      }
      this.judgeHasChooseAll()
    },
    // 判断是否全选
    judgeHasChooseAll () {
      // 获取所有有样本编号的
      const all = this.currentBox.boxes.flat().filter(v => v.sampleCode)
      const keysLength = Object.keys(this.currentChecked).length
      this.checkAll = all.length === keysLength
    },
    setId (r, c) {
      return this.numToUppercase(r) + this.addZero(c)
    },
    // 不到10补0
    addZero (num) {
      if (+num > 0 && +num < 10) {
        return 0 + '' + num
      }
      return num
    },
    // 数字转大写字母
    numToUppercase (num) {
      return String.fromCharCode(64 + num)
    },
    handleBlur (item) {
      this.$nextTick(() => {
        if (!this.hasDoEnterEvent) {
          this.handleEnter(item, false)
        }
      })
    },
    // 点击输入框
    handleInputClick (vv, { row, column }) {
      if (!this.currentSample) {
        if (!vv.sampleCode) {
          this.$emit('clickHole', { row, column, id: vv.id, currentSelectBox: this.currentSelectBox })
        }
        return
      }
      vv.sampleCode = this.currentSample
      this.handleEnter(vv)
    },
    async handleEnter (item, needNext = false) {
      console.log(needNext)
      document.querySelector(`#${item.id}`).blur()
      if (!item.sampleCode) return
      this.hasDoEnterEvent = true
      setTimeout(() => {
        this.hasDoEnterEvent = false
      })
      if (this.limitSampleCode.length > 0 && !this.limitSampleCode.includes(item.sampleCode)) {
        await this.$alert(`${item.id}中输入的${item.sampleCode}不是上述表格存在的样本编号，请检查`, '提示', {
          confirmButtonText: '确定并清空',
          type: 'error'
        })
        if (needNext) {
          this.$emit('inputError')
        }
        this.clearSampleCode([item.id])
        return
      }
      this.currentBox.boxes.forEach(arr => {
        arr.forEach(v => {
          if (v.id !== item.id && v.sampleCode === item.sampleCode) {
            v.sampleCode = ''
            const box = this.findBox(this.currentSelectBox)
            console.log(box)
            if (box) {
              const posItem = box.holeSampleNumberObj[v.id] || {}
              posItem.sampleCode = ''
              posItem.sampleId = ''
            }
          }
        })
      })
      this.$emit('input', {
        pos: `${this.currentBox.pos}-${item.id}`,
        sampleType: this.currentBox.sampleType,
        sampleCode: item.sampleCode,
        id: item.id,
        needNext,
        currentBox: util.deepCopy(this.currentSelectBox)
      })
    },
    // 找到盒子
    findBox ({ containerId, floorId, shelfId, id }) {
      const container = this.containerLists.find(v => v.id === containerId)
      let floor = null
      let shelf = null
      let box = null
      if (container && container.children) {
        floor = container.children.find(v => v.id === floorId)
      }
      if (floor && floor.children) {
        shelf = floor.children.find(v => v.id === shelfId)
      }
      if (shelf && shelf.children) {
        box = shelf.children.find(v => v.id === id)
      }
      return box
    },
    boxChange (newVal) {
      let box = this.findBox(newVal)
      if (box) {
        this.currentChecked = {}
        this.checkAll = false
        this.setBoxes(box)
      }
    },
    setBoxes (box) {
      const { xSize, ySize, holeSampleNumberObj, sampleType, pos, showPosName, containerInfo = {} } = box
      this.currentBox.row = xSize
      this.currentBox.column = ySize
      this.currentBox.sampleType = sampleType
      this.currentBox.containerInfo = containerInfo
      this.currentBox.pos = pos
      this.currentBox.showPosName = showPosName
      this.currentBox.boxes = []
      for (let r = 1; r <= xSize; r++) {
        let item = []
        for (let c = 1; c <= ySize; c++) {
          let v = {
            id: this.setId(r, c),
            sampleCode: '',
            sampleId: '',
            hasSample: false,
            sampleOperateType: '' // 操作类型 move移入 out移出
          }
          const sampleObj = holeSampleNumberObj[v.id]
          if (sampleObj) { // 如果有就填入
            v.sampleCode = sampleObj.sampleCode
            v.sampleId = sampleObj.sampleId
            v.hasSample = !sampleObj.canInput
            v.sampleOperateType = sampleObj.sampleOperateType || ''
          }
          item.push(v)
        }
        this.currentBox.boxes.push(item)
      }
    },
    // 更新当前孔板数据
    async handleUpdatePlate () {
      await this.getBox(this.currentBoxNode)
      this.boxChange(this.currentSelectBox)
    },
    // 清空点位的样本
    async clearSampleCode (ids = []) {
      // 重新获取当前节点数据
      await this.handleUpdatePlate()
      this.currentBox.boxes.forEach(arr => {
        arr.forEach(v => {
          if (ids.includes(v.id)) {
            v.sampleCode = ''
            v.sampleId = ''
          }
        })
      })
    },
    // 为移库空位设置操作方法
    setSampleHoleOperateType (ids = [], type = 'out') {
      this.currentBox.boxes.forEach(arr => {
        arr.forEach(v => {
          if (ids.includes(v.id)) {
            v.sampleOperateType = type
          }
        })
      })
    },
    // 添加样本
    addSample (sampleList = []) {
      let l = sampleList.length
      console.log(sampleList)
      const emptyHoles = []
      const newPos = [] // 新位置
      let ll = 0 // 空位计数
      this.currentBox.boxes.forEach(arr => {
        arr.forEach(v => {
          if (!v.sampleId && ll < l) {
            ll = emptyHoles.push(v)
          }
        })
      })
      console.log(emptyHoles)
      sampleList.forEach((v, i) => {
        const vv = emptyHoles[i]
        vv.sampleCode = v.sampleCode
        vv.sampleId = v.sampleId
        vv.sampleOperateType = 'move'
        vv.notSave = true
        newPos.push(vv)
      })
      return newPos
    }
  }
}
</script>

<style scoped lang="scss">
  input:focus-visible{
    outline: none;
  }
.wrap{
  display: flex;
  align-items: start;
  .tree-wrap{
    min-width: 300px;
    padding-right: 10px;
    overflow: auto;
    margin-right: 20px;
    background: #f2f2f2;
  }
  .lab-filter{
    display: flex;
    height: 40px;
    align-items: center;
    font-size: 14px;
    padding-left: 10px;
  }
  .tip-text{
    font-size: 15px;
    line-height: 40px;
    padding: 0 10px;
    font-weight: 600;
    background: #f2f2f2;
  }
  .hole-wrap{
    $w: 150px; // 每个输入框的宽度
    $h: 30px; // 每个输入框的高度
    padding: 10px;
    background: #EBEEF5;
    .hole-row{
      display: flex;
      margin-bottom: 5px;
      margin-right: 10px;
      align-items: center;
      & > div{
        flex-shrink: 0;
        box-sizing: border-box;
      }
      .x-text{
        width: $w;
        margin-right: 5px;
        font-size: 16px;
        text-align: center;
      }
      .y-text{
        font-size: 16px;
        margin-right: 5px;
        width: 1em;
      }
      .hole{
        width: $w;
        margin-right: 5px;
        display: flex;
        position: relative;
        border: 2px solid;
        padding: 3px 5px;
        input{
          width: $w - 10px;
          border: 1px solid;
          padding: 0 5px;
          font-weight: 600;
          font-size: 16px;
          height: $h;
          color: #fff;
          background-repeat: no-repeat;
          background-position: 50%;
          background-size: 100% 100%,auto;
          box-sizing: border-box;
          text-align: right;
        }
        .icon-warning{
          position: absolute;
          top: 0;
          right: 0;
          color: red;
          z-index: 2;
        }
        .vNode{
          position: absolute;
          right: 5px;
          height: $h;
          width: 10px;
          opacity: 0;
          border-radius: 0 10px 10px 0;
          $t: 0.5s;
          transition: width $t, opacity $t;
          &:hover{
            width: $w - 5px;
            opacity: 1;
          }
          & > div{
            width: 100%;
            height: 100%;
            position: relative;
            &::after{
              content: attr(data-projectName);
              position: absolute;
              overflow: hidden;
              text-overflow:ellipsis;
              white-space: nowrap;
              line-height: $h;
              padding-left: 5px;
              display: block;
              width: 100%;
              height: 100%;
              left: 0;
              color: #fff;
            }
          }
        }
      }
    }
  }
}
</style>
