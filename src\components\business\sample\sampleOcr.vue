<template>
  <div>
    <div class="title">图片信息</div>
    <el-tabs v-model="imgsType" @tab-click="handleTabChange">
      <el-tab-pane :label="`送检单${getImgIndexAndLength('sampleOrderImg')}`" name="sampleOrderImg"></el-tab-pane>
      <el-tab-pane :label="`临床资料${getImgIndexAndLength('imgNames')}`" name="imgNames"></el-tab-pane>
      <el-tab-pane :label="`随访资料${getImgIndexAndLength('followupImgNames')}`" name="followupImgNames"></el-tab-pane>
      <el-tab-pane :label="`补充资料${getImgIndexAndLength('supplementaryImgs')}`" name="supplementaryImgs"></el-tab-pane>
      <el-tab-pane :label="`修改信息${supplementaryNote.length <= 0 ? '' : '('+fixInfoLength + '/' + supplementaryNote.length +')'}`" name="fixInfo"></el-tab-pane>
      <el-tab-pane label="送检单Excel" name="inspectionSheet"></el-tab-pane>
    </el-tabs>
    <div class="change-picture" v-if="!['fixInfo', 'inspectionSheet'].includes(imgsType)">
      <el-button style="z-index: 10000" size="mini" @click="handlePreviousPicture">上一张</el-button>
      <el-button size="mini" @click="handleNextPicture">下一张</el-button>
      <el-button size="mini" @click="handleImageAction('right')">右转<i class="el-icon-refresh-right"></i></el-button>
      <el-button size="mini" @click="handleImageAction('left')">左转<i class="el-icon-refresh-left"></i></el-button>
      <!-- <el-button size="mini" @click="handleImageReset">还原<i class="el-icon-full-screen"></i></el-button>-->
      <el-button v-if="!isDisabled && imgsType !== 'supplementaryImgs' && imgsType !== 'followupImgNames'" size="mini" @click="handleEditPicture">编辑</el-button>
      <el-button size="mini" :disabled="ocrDialogVisible" @click="handleText">文字识别</el-button>
    </div>
    <div v-if="!['fixInfo', 'inspectionSheet'].includes(imgsType)" class="picture">
      <div v-if="picSrc" class="image">
        <template v-if="picSrc.indexOf('.pdf') !== -1">
          <div style="width: 100%; height: 100%;display: flex; justify-content: center;align-items: center;">
            <div>
              <icon-svg icon-class="icon-pdf" style="font-size: 150px;color: #409EFF;"></icon-svg>
              <div style="text-align: center;">
                <el-button type="text" size="mini" @click="handleViewPdf(picSrc)">点击查看PDF文件</el-button>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
            <div class="preview-img">
              <vueCropper
                  ref="cropper"
                  :img="option.img"
                  :mode="option.mode"
                  :auto-crop="option.autoCrop"
                  :center-box="option.centerBox"
                  @imgLoad="imgLoad"
                  style="z-index:10000"
              ></vueCropper>
              <el-result v-if="imgLoading"  class="error-preview" icon="error" title="加载失败">
              </el-result>
          </div>
        </template>
      </div>
    </div>
    <div v-if="imgsType === 'fixInfo'">
      <el-table
          ref="table"
          :data="supplementaryNote"
          :height="400"
          style="width: 100%;"
      >
        <el-table-column type="index"></el-table-column>
        <el-table-column prop="date"></el-table-column>
        <el-table-column prop="content"></el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === 1" size="mini"  @click="handelStatus(0,scope.$index)">已处理</el-button>
            <el-button v-if="scope.row.status === 0" size="mini" type="primary" @click="handelStatus(1, scope.$index)">未处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
              <div v-if="imgsType === 'inspectionSheet'">
                <el-row class="row-style">
                  <el-col :span="12">样本编号：{{otherClinicalInfo.sampleNum}}</el-col>
                  <el-col :span="12">受检者姓名：{{otherClinicalInfo.patientName}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">性别：{{otherClinicalInfo.sex === 0 ? '男' : otherClinicalInfo.sex === 1 ? '女' : ''}}</el-col>
                  <el-col :span="12">证件号码：{{otherClinicalInfo.idcard}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">出生年月：{{otherClinicalInfo.birthday}}</el-col>
                  <el-col :span="12">产品名称：{{otherClinicalInfo.proName}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">样本类型：{{otherClinicalInfo.sampleType}}</el-col>
                  <el-col :span="12">癌种： {{otherClinicalInfo.cancer}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">样本采集时间：{{otherClinicalInfo.sampleCollectTime}}</el-col>
                  <el-col :span="12">组织样本采集时间：{{otherClinicalInfo.tissueCollectTime}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">采集部位：{{otherClinicalInfo.samplingArea}}</el-col>
                  <el-col :span="12">临床诊断结果：{{otherClinicalInfo.clinicalDiagnose}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">确诊年龄：{{otherClinicalInfo.diagnosisAge}}</el-col>
                  <el-col :span="12">家族史：{{otherClinicalInfo.familyHistory === 0 ? '无' : otherClinicalInfo.familyHistory === 1 ? '有' : '不详'}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">家族史备注：{{otherClinicalInfo.familyHistoryRemarks}}</el-col>
                  <el-col :span="12">输血史：{{otherClinicalInfo.bloodTransHistory === 0 ? '无' : otherClinicalInfo.bloodTransHistory === 1 ? '有' : '不详'}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">手术史：{{otherClinicalInfo.operateHis === 0 ? '无' : otherClinicalInfo.operateHis === 1 ? '有' : '不详'}}</el-col>
                  <el-col :span="12">用药史：{{otherClinicalInfo.drugHistory === 0 ? '无' : otherClinicalInfo.drugHistory === 1 ? '有' : '不详'}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">手术史(银丰)：{{ otherClinicalInfo.operationHistoryText }}</el-col>
                  <el-col :span="12">用药史(银丰)：{{ otherClinicalInfo.medicineHistoryText }}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">放疗史：{{otherClinicalInfo.otherapy === 0 ? '无' : otherClinicalInfo.otherapy === 1 ? '有' : '不详'}}</el-col>
                  <el-col :span="12">目前临床分期：{{otherClinicalInfo.clinicalStages}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">目前转移部位：{{otherClinicalInfo.cancerMetastasisSites}}</el-col>
                  <el-col :span="12">基因检测：{{otherClinicalInfo.geneTest === 0 ? '无' : otherClinicalInfo.geneTest === 1 ? '有' : '不详'}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">影像检查：{{otherClinicalInfo.iconography === 0 ? '无' : otherClinicalInfo.iconography === 1 ? '有' : '不详'}}</el-col>
                  <el-col :span="12">肿瘤标记物：{{otherClinicalInfo.cancerTag === 0 ? '无' : otherClinicalInfo.cancerTag === 1 ? '有' : '不详'}}</el-col>
                </el-row>
                <el-row class="row-style">
                  <el-col :span="12">备注：{{otherClinicalInfo.remark}}</el-col>
                </el-row>
              </div>
    <el-dialog
        title="识别结果"
        :visible.sync="ocrDialogVisible"
        :modal='false'
        v-drag-dialog
        :close-on-click-modal="false"
        width="250px"
        class="el-dialog-text"
        top="200px"
        :before-close="handleClose"
    >
      <div v-loading="loadingText" element-loading-text="文字识别中" style="margin-top:10px">
      </div>
      <div class="ocr-text">
        <el-scrollbar ref="scrollDiv">
          <p v-for="(item,index) in copyText" :key="index">{{item}}</p>
        </el-scrollbar>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="handleBegin">开始识别</el-button>
<!--        <el-button size="mini" icon="el-icon-close" @click="handleCancle">取消截图</el-button>-->
        <el-button size="mini" icon="el-icon-document-copy" @click="handlecopyText">复制</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import VueCropper from '../../common/vueCropper/src/vue-cropper.vue'
import {VueCropper} from 'vue-cropper'
import util from '../../../util/util'
export default {
  name: 'sampleImgInfo',
  components: {
    VueCropper
  },
  props: {
    imgType: {
      type: String
    },
    imgSrc: {
      type: String
    },
    imgNames: {
      type: Array
    }, // 临床资料
    sampleOrderImg: {
      type: Array
    }, // 送检单资源
    isDisabled: {
      type: Boolean,
      default: false
    },
    sampleBasicId: {
      type: Number
    },
    title: {
      type: String
    },
    followupImgNames: {
      type: Array
    }, // 随访资料
    supplementaryImgs: {
      type: Array
    }, // 补充资料
    supplementaryNote: {
      type: Array
    }, // 修改资料
    otherClinicalInfo: {
      type: Object
    } // 工业客户送检单
  },
  mounted () {
  },
  watch: {
    imgSrc: {
      handler (newValue) {
        console.log(newValue)
        this.picSrc = newValue
        this.option.img = newValue
      },
      immediate: true
    },
    imgType: {
      handler (newValue) {
        this.imgsType = newValue
      },
      immediate: true
    },
    picSrc (newValue) {
      console.log(newValue)
      this.option.img = newValue
    }
  },
  computed: {
    fixInfoLength () {
      return this.supplementaryNote.filter(item => item.status === 0).length
    }
  },
  data () {
    return {
      multiples: 100, // 放大或者缩小
      deg: 0, // 旋转的角度
      isDrag: false, // 是否开始拖拽
      startX: 0, // 鼠标的点击X轴
      startY: 0, // 鼠标的点击Y轴
      moveX: 0, // 鼠标移动的X轴
      moveY: 0, // 鼠标移动的Y轴
      endX: 0,
      endY: 0,
      imgLoading: false,
      pictureInfoSaveDialogVisible: false,
      pictureInfoSaveDialogData: {},
      picSrc: '', // 图片展示路径
      imgsType: '',
      option: {
        img: this.imgSrc,
        mode: 'cover',
        autoCrop: false,
        centerBox: false
      }, // 裁剪图片配置
      ocrDialogVisible: false, // 识别弹窗显示
      copyText: [], // 识别文字
      loadingText: false
    }
  },
  methods: {
    handelStatus (type, index) {
      this.supplementaryNote[index].status = type
      this.$emit('statusFixEvent', this.supplementaryNote)
    },
    // 图片失败
    imgLoad (data) {
      console.log(data === 'success')
      this.imgLoading = data !== 'success'
    },
    // 文字识别
    handleText () {
      this.option.centerBox = true
      this.copyText = []
      this.$refs.cropper.startCrop()
      this.ocrDialogVisible = true
    },
    // 开始识别
    handleBegin () {
      console.log(this.title)
      this.loadingText = true
      this.$refs.cropper.getCropData(data => {
        // 测试接口
        this.$ajax({
          method: 'post',
          url: '/sample/basic/volc_engine_ocr_service',
          data: {
            imageBase64: data.split(',')[1],
            specificLocation: `样本信息管理-${this.title}`
          }
        }).then(res => {
          console.log(res)
          if (res && res.code === this.SUCCESS_CODE) {
            this.copyText = res.data
          } else {
            this.$message.error('识别失败')
          }
        }).finally(() => {
          this.loadingText = false
        })
      })
    },
    // 复制文字
    handlecopyText () {
      util.copyText(this.copyText)
      this.$message.success('复制成功')
    },
    // 取消截图
    handleCancle () {
      this.$refs.cropper.clearCrop()
    },
    // 关闭识别弹窗
    handleClose () {
      this.option.centerBox = false
      this.$refs.cropper.stopCrop()
      this.$refs.cropper.clearCrop()
      this.ocrDialogVisible = false
    },
    // 切换上一张图片
    handlePreviousPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      switch (this.imgsType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === 0 ? this.picSrc = this.imgNames[this.imgNames.length - 1].fileAbsolutePath : this.picSrc = this.imgNames[index - 1].fileAbsolutePath
          break
        case 'followupImgNames':
          index = this.followupImgNames.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === 0 ? this.picSrc = this.followupImgNames[this.followupImgNames.length - 1].fileAbsolutePath : this.picSrc = this.followupImgNames[index - 1].fileAbsolutePath
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === 0 ? this.picSrc = this.sampleOrderImg[this.sampleOrderImg.length - 1].fileAbsolutePath : this.picSrc = this.sampleOrderImg[index - 1].fileAbsolutePath
          break
        case 'supplementaryImgs':
          index = this.supplementaryImgs.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === 0 ? this.picSrc = this.supplementaryImgs[this.supplementaryImgs.length - 1].fileAbsolutePath : this.picSrc = this.supplementaryImgs[index - 1].fileAbsolutePath
          break
      }
    },

    // 切换下一张图片
    handleNextPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      switch (this.imgsType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === this.imgNames.length - 1 ? this.picSrc = this.imgNames[0].fileAbsolutePath : this.picSrc = this.imgNames[index + 1].fileAbsolutePath
          break
        case 'followupImgNames':
          index = this.followupImgNames.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === this.followupImgNames.length - 1 ? this.picSrc = this.followupImgNames[0].fileAbsolutePath : this.picSrc = this.followupImgNames[index + 1].fileAbsolutePath
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === this.sampleOrderImg.length - 1 ? this.picSrc = this.sampleOrderImg[0].fileAbsolutePath : this.picSrc = this.sampleOrderImg[index + 1].fileAbsolutePath
          break
        case 'supplementaryImgs':
          index = this.supplementaryImgs.findIndex(v => this.picSrc === v.fileAbsolutePath)
          index === this.supplementaryImgs.length - 1 ? this.picSrc = this.supplementaryImgs[0].fileAbsolutePath : this.picSrc = this.supplementaryImgs[index + 1].fileAbsolutePath
          break
      }
    },

    // 编辑图片信息
    handleEditPicture () {
      this.pictureInfoSaveDialogData = {
        type: this.imgsType,
        sampleBasicId: this.sampleBasicId,
        tableData: this.imgsType === 'sampleOrderImg' ? JSON.parse(JSON.stringify(this.sampleOrderImg)) : [] // 送检单与知情同意书专用
      }
      console.log(this.pictureInfoSaveDialogData)
      this.$emit('fixImgEvent', {
        pictureInfoSaveDialogData: this.pictureInfoSaveDialogData,
        pictureInfoSaveDialogVisible: true
      })
    },
    // 图片的操作
    handleImageAction (value) {
      if (value === 'right') {
        this.$refs.cropper.rotateRight()
      } else {
        this.$refs.cropper.rotateLeft()
      }
    },
    // 获取图片索引
    getImgIndexAndLength (type) {
      let index = 0
      let length = 0
      switch (type) {
        case 'sampleOrderImg':
          length = this.sampleOrderImg.length
          if (this.imgsType === type) {
            index = this.sampleOrderImg.findIndex(v => v.fileAbsolutePath === this.picSrc)
          }
          break
        case 'imgNames':
          length = this.imgNames.length
          if (this.imgsType === type) {
            index = this.imgNames.findIndex(v => v.fileAbsolutePath === this.picSrc)
          }
          break
        case 'followupImgNames':
          length = this.followupImgNames.length
          if (this.imgsType === type) {
            index = this.followupImgNames.findIndex(v => v.fileAbsolutePath === this.picSrc)
          }
          break
        case 'supplementaryImgs':
          length = this.supplementaryImgs.length
          if (this.imgsType === type) {
            index = this.supplementaryImgs.findIndex(v => v.fileAbsolutePath === this.picSrc)
          }
          break
      }
      return length === 0 ? '' : `(${index + 1} / ${length})`
    },
    // 切换图片类型
    handleTabChange () {
      if (this.ocrDialogVisible) {
        this.handleClose()
      }
      this.deg = 0
      this.multiples = 100
      if (this.imgsType === 'fixInfo') {
        // this.supplementaryNote = this.form.supplementaryNote
      }
      switch (this.imgsType) {
        case 'imgNames':
          if (this.imgNames.length !== 0) {
            this.picSrc = this.imgNames[0].fileAbsolutePath || ''
          } else {
            this.picSrc = ''
          }
          break
        case 'followupImgNames':
          if (this.followupImgNames.length !== 0) {
            this.picSrc = this.followupImgNames[0].fileAbsolutePath || ''
          } else {
            this.picSrc = ''
          }
          break
        case 'sampleOrderImg':
          if (this.sampleOrderImg.length !== 0) {
            this.picSrc = this.sampleOrderImg[0].fileAbsolutePath || ''
          } else {
            this.picSrc = ''
          }
          break
        case 'supplementaryImgs':
          if (this.supplementaryImgs.length !== 0) {
            this.picSrc = this.supplementaryImgs[0].fileAbsolutePath || ''
          } else {
            this.picSrc = ''
          }
          break
      }
    },
    // 查看pdf文件
    handleViewPdf (url) {
      window.open(url, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.change-picture{
  position: relative;
  z-index: 2000;
  //width:65%
}
.el-dialog-text{
  right: -20px;
  left: auto;
  z-index: 1 !important;
}
.row-style {
  margin: 15px 25px 0;
}
.el-dialog__wrapper{
    //width: 30vw;
    //margin-left: 76vw;
}
.el-dialog__footer{
  position: relative;
  z-index:1000
}
.right{
  overflow-y: auto;
  flex: 1;
  padding: 0 5px;
  border-left: 1px solid #DCDFE6;
  height: 100%;
  .picture{
    margin: 10px auto;
    width: 90%;
    height: calc(100% - 54px - 28px - 20px - 40px);
    .image{
      overflow: auto;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
    }
  }
  .title{
    height: 30px;
    line-height: 30px;
    font-size: 13px;
  }
  .preview-img{
    position: relative;
    z-index: 1000;
    width:26vw;
    height:60vh;
    box-shadow: 0 0 10px #ccc;
  }
  .error-preview{
    position: absolute;
    z-index: 100000;
    top: 0;
    left: 0;
    width:26vw;
    height:60vh;
    box-shadow: 0 0 10px #ccc;
    background-color: rgba(0, 0, 0,0.8);
    >>>.el-result__title p{
      color: white;
    }
    }
  .ocr-text{
    height:45vh
  }
  .el-scrollbar{
    height:45vh
  }
}
</style>
