<template>
  <div>
    <div style="display: flex;">
     <div style="padding: 20px;border: 1px solid red;">
       <div>
         <el-button @click="handleShowSetFloorDialog">设置层</el-button>
       </div>
       <div>
         <template v-for="item in container.content">
           <div :class="{'active-item': floorActive(item.num)}" :key="item.num" class="item" @click="handleSelectFloor(item)">
             <p>{{item.num}}{{item.tag}}</p>
             <p>{{item.sampleType.toString()}}</p>
             <p>{{item.children.length > 0 ? '已' : '未'}}设置架</p>
           </div>
         </template>
       </div>
     </div>
      <div style="padding: 20px;border: 1px solid red;">
        <div>
          <el-button @click="handleShowSetShelfDialog">设置架</el-button>
        </div>
        <div>
          <template v-for="item in selectedFloor">
            <div :key="item.num">
              <template v-for="v in item.children">
                <div :key="v.num" class="item">
                  <p>{{item.num}}{{item.tag}}{{v.num}}{{v.tag}}</p>
                  <p>{{v.sampleType.toString()}}</p>
                  <p>{{v.children.length > 0 ? '已' : '未'}}设置盒</p>
                </div>
              </template>
            </div>
          </template>
        </div>
      </div>
    </div>
    <set-floor-dialog
      :pvisible="setFloorDialogVisible"
      :current-set-obj="currentSetObj"
      @dialogCloseEvent="setFloorDialogVisible = false"
      @dialogConfirmEvent="handleFloorConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import setFloorDialog from './setFloorDialog'
export default {
  name: 'test',
  components: {
    setFloorDialog
  },
  mounted () {
  },
  data () {
    return {
      container: {
        formInput: {},
        content: []
      },
      currentSetObj: {},
      setFloorDialogVisible: false,
      selectedFloor: []
    }
  },
  methods: {
    // 将输入值转换为数组
    changInputToArr (input, maxNum = 9) {
      let regx = /[1-9]([\s+,，\\-]*[0-9])*$/
      if (regx.test(input)) {
        input = input.replace(/\s+/g, ',').replace(/，/, ',').replace(/(\s+-\s+)|(\s+-)|(-\s+)/, '-')
        // 转换字符串为数组,去除空项
        let f = input.split(',').filter(item => { return item })
        let floorNumsSet = new Set()
        let numCorrect = true
        for (let i = 0; i < f.length; i++) {
          if (f[i].indexOf('-') > -1) {
            let fArr = f[i].split('-').filter(item => { return item })
            if (fArr.length !== 2) {
              this.$message.error('输入格式不正确')
              numCorrect = false
              break
            } else {
              let correctNum = fArr.every(v => {
                let num = +v
                return !Number.isNaN(num) && num > 0 && num <= maxNum
              })
              if (correctNum) {
                let arr = fArr.map(v => { return +v })
                let max = Math.max(...arr)
                let min = Math.min(...arr)
                if (max <= maxNum && min > 0) {
                  let foolA = []
                  do {
                    foolA.push(min)
                    min++
                  } while (min <= max)
                  foolA.forEach(item => {
                    floorNumsSet.add(item)
                  })
                } else {
                  this.$message.error(`请确保输入的值在1-${maxNum}之间`)
                  numCorrect = false
                  break
                }
              } else {
                this.$message.error('输入格式不正确')
                numCorrect = false
                break
              }
            }
          } else {
            let num = +f[i]
            if (!Number.isNaN(num) && num > 0 && num <= maxNum) {
              floorNumsSet.add(num)
            } else {
              this.$message.error('输入格式不正确')
            }
          }
        }
        if (numCorrect) {
          return [...floorNumsSet]
        } else {
          return false
        }
      } else {
        this.$message.error('盒子序号格式不正确')
        return false
      }
    },
    // 展示设置层弹窗
    handleShowSetFloorDialog () {
      this.currentSetObj = this.container
      this.setFloorDialogVisible = true
    },
    handleFloorConfirm (data, formInput) {
      this.container.content = data
      this.container.formInput.sampleTypeInput = formInput
      this.setFloorDialogVisible = false
    },
    floorActive (num) {
      return this.selectedFloor.some((item, i) => {
        return item.num === num
      })
    },
    // 选中层
    handleSelectFloor (floor) {
      let index = null
      this.selectedFloor.forEach((item, i) => {
        if (item.num === floor.num) {
          index = i
        }
      })
      if (index === null) {
        this.selectedFloor.push(floor)
      } else {
        this.selectedFloor.splice(index, 1)
      }
    }
    // // 设置架
    // handleShowSetShelfDialog () {
    //   if (this.) {}
    // }
  }
}
</script>

<style scoped lang="scss">
 .item{
   background: slategrey;
   padding: 10px;
   width: 200px;
   p{
     line-height: 2;
   }
   & + .item {
     margin-top: 20px;
   }
 }
  .active-item{
    border: 3px dashed blue;
  }
</style>
