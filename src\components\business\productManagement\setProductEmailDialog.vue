<script>
import mixins from '../../../util/mixins'
import EmailContent from './components/emailContent.vue'
import {awaitWrap} from '../../../util/util'
import {getProductEmailConfigInfo, saveProductEmailConfig} from '../../../api/system/productManagementApi'

/**
 * 设置产品邮件配置的组件
 * 使用mixins中的dialogBaseInfo混合项来复用对话框基本配置
 * @mixins mixins.dialogBaseInfo - 对话框基本配置的混合项
 */
export default {
  name: 'setProductEmail',
  mixins: [mixins.dialogBaseInfo],
  components: {EmailContent},
  props: {
    productId: {
      type: Number,
      default: null
    },
    productName: {
      type: String,
      default: ''
    },
    productCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      form: {
        // 产品名称
        productName: '',
        // 邮件主题
        emailSubject: '',
        // 邮件样例编号
        emailSampleCode: '',
        // 报告邮件名
        reportEmailName: '',
        // 是否发送补充报告
        isSendSupplementReport: '',
        // 是否发送补充Excel
        isSendSupplementExcel: '',
        // 是否发送生信文件
        isSendBioinformaticsFile: '',
        // 邮件内容对象
        emailContent: {
          // TMB状态报出(邮件)
          tmbStatusReport: '',
          // TMB值报出(邮件)
          tmbValueReport: '',
          // MSI报出(邮件)
          msiReport: '',
          // MSI癌肿选择(邮件)
          msiChoose: '',
          // 邮件开头内容
          emailContent: '',
          // 患者临床信息
          patientClinicalInfo: '',
          // 北肿项目关键信息摘要
          northInfo: '',
          // 检测结果小结
          testResult: '',
          // 肿瘤分子监测小结
          cancerMutation: '',
          // 检测结果比对表
          testResultCompare: '',
          // 检测结果比对表 排除项
          testResultCompareExcludeOptions: '',
          // 靶向相关变异
          variation: '',
          // 邮件结尾内容
          emailEndInfo: ''
        },
        // PI邮件内容对象
        piEmailContent: {
          // TMB状态报出(邮件)
          tmbStatusReport: '',
          // TMB值报出(邮件)
          tmbValueReport: '',
          // MSI报出(邮件)
          msiReport: '',
          // MSI癌肿选择(邮件)
          msiChoose: '',
          // 邮件开头内容
          emailContent: '',
          // 患者临床信息
          patientClinicalInfo: '',
          // 北肿项目关键信息摘要
          northInfo: '',
          // 检测结果小结
          testResult: '',
          // 肿瘤分子监测小结
          cancerMutation: '',
          // 检测结果比对表
          testResultCompare: '',
          // 检测结果比对表 排除项
          testResultCompareExcludeOptions: '',
          // 靶向相关变异
          variation: '',
          // 邮件结尾内容
          emailEndInfo: ''
        }
      },
      activeTab: '1', // 当前激活的标签页
      submitBtnLoading: false,
      fileNames: ['安诺文件名', 'PD-L1文件名', '送检方编号命名', '送检方编号命名-去除吉因加编号'], // 文件名列表
      booleanValue: ['是', '否'], // 布尔值选项
      emailSubjectList: ['安诺邮件主题'], // 邮件主题列表
      emailSampleCodeList: ['吉因加编号', '送检方编号'], // 邮件样例编号列表
      // 生信文件列表
      bioinformaticsFileList: ['北肿1021VCF', '北肿1021CSV', '北肿fusionCSV']
    }
  },
  methods: {
    /**
     * 打开对话框时加载邮件配置
     */
    handleOpen () {
      this.activeTab = '1'
      this.getEmailConfig()
    },
    /**
     * 获取产品邮件配置信息
     * 使用awaitWrap封装API调用，处理异步逻辑
     */ async getEmailConfig () {
      const {res} = await awaitWrap(getProductEmailConfigInfo({
        productCode: this.productCode
      }, {
        loadingDom: '.form-container'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        const {data = {}} = res
        const productEmail = data.productEmail || {}
        const productEmailPi = data.productEmailPi || {}
        this.form = {
          productName: data.fproductName || this.productName,
          emailSubject: data.emailSubjectRules,
          emailSampleCode: data.emailSampleNum,
          reportEmailName: data.emailReportNameRule,
          isSendSupplementReport: data.sendSupplementaryReport,
          isSendSupplementExcel: data.sendSupplementaryExcel,
          isSendBioinformaticsFile: data.sendShengxinFile && data.sendShengxinFile.split(','),
          emailContent: {
            tmbStatusReport: productEmail.tmbReportStateEmail,
            tmbValueReport: productEmail.tmbReportValueEmail,
            msiReport: productEmail.msiReportEmail,
            msiChoose: productEmail.msiReportEmailCancer && productEmail.msiReportEmailCancer.split(','),
            emailContent: productEmail.emailBeginning || '输出',
            patientClinicalInfo: productEmail.patientClinicalInformation || '受检者临床诊断&治疗史',
            northInfo: productEmail.beizhongInformatationSummary || '不输出',
            testResult: productEmail.testResultSummary || '输出',
            cancerMutation: productEmail.tumorMolecularSurveillanceSummary || '不输出',
            testResultCompareExcludeOptions: productEmail.testResultComparisonExcludeOptions && productEmail.testResultComparisonExcludeOptions.split(','),
            testResultCompare: productEmail.testResultComparison || '输出',
            variation: productEmail.targetAssociatedVariant || '输出',
            emailEndInfo: productEmail.emailEnding || '输出'
          },
          piEmailContent: {
            tmbStatusReport: productEmailPi.tmbReportStateEmail,
            tmbValueReport: productEmailPi.tmbReportValueEmail,
            msiReport: productEmailPi.msiReportEmail,
            msiChoose: productEmailPi.msiReportEmailCancer && productEmailPi.msiReportEmailCancer.split(','),
            emailContent: productEmailPi.emailBeginning || '输出',
            patientClinicalInfo: productEmailPi.patientClinicalInformation || '受检者临床诊断&治疗史',
            northInfo: productEmailPi.beizhongInformatationSummary || '不输出',
            testResult: productEmailPi.testResultSummary || '输出',
            cancerMutation: productEmailPi.tumorMolecularSurveillanceSummary || '不输出',
            testResultCompareExcludeOptions: productEmailPi.testResultComparisonExcludeOptions && productEmailPi.testResultComparisonExcludeOptions.split(','),
            testResultCompare: productEmailPi.testResultComparison || '输出',
            variation: productEmailPi.targetAssociatedVariant || '输出',
            emailEndInfo: productEmailPi.emailEnding || '输出'
          }
        }
      }
    },
    /**
     * 设置参数对象
     * 准备参数以供保存邮件配置使用
     * @returns {Object} - 包含产品邮件配置的参数对象
     */
    setParams () {
      const { productName,
        emailSubject,
        emailSampleCode,
        reportEmailName,
        isSendSupplementReport,
        isSendSupplementExcel,
        isSendBioinformaticsFile = [], emailContent, piEmailContent} = this.form
      return {
        productId: this.productId,
        productCode: this.productCode,
        fproductName: productName,
        emailSubjectRules: emailSubject,
        emailSampleNum: emailSampleCode,
        emailReportNameRule: reportEmailName,
        sendSupplementaryReport: isSendSupplementReport,
        sendSupplementaryExcel: isSendSupplementExcel,
        sendShengxinFile: isSendBioinformaticsFile && isSendBioinformaticsFile.join(','),
        productEmail: {
          productId: this.productId,
          productCode: this.productCode,
          fproductName: productName,
          tmbReportStateEmail: emailContent.tmbStatusReport,
          tmbReportValueEmail: emailContent.tmbValueReport,
          msiReportEmail: emailContent.msiReport,
          msiReportEmailCancer: emailContent.msiReport === '是' ? emailContent.msiChoose.join(',') : '',
          emailBeginning: emailContent.emailContent,
          patientClinicalInformation: emailContent.patientClinicalInfo,
          beizhongInformatationSummary: emailContent.northInfo,
          testResultSummary: emailContent.testResult,
          tumorMolecularSurveillanceSummary: emailContent.cancerMutation,
          testResultComparisonExcludeOptions: emailContent.testResultCompareExcludeOptions && emailContent.testResultCompareExcludeOptions.join(','),
          testResultComparison: emailContent.testResultCompare,
          emailEnding: emailContent.emailEndInfo,
          targetAssociatedVariant: emailContent.variation
        },
        productEmailPi: {
          productId: this.productId,
          productCode: this.productCode,
          fproductName: productName,
          tmbReportStateEmail: piEmailContent.tmbStatusReport,
          tmbReportValueEmail: piEmailContent.tmbValueReport,
          msiReportEmail: piEmailContent.msiReport,
          msiReportEmailCancer: piEmailContent.msiReport === '是' ? piEmailContent.msiChoose.join(',') : '',
          emailBeginning: piEmailContent.emailContent,
          patientClinicalInformation: piEmailContent.patientClinicalInfo,
          beizhongInformatationSummary: piEmailContent.northInfo,
          testResultSummary: piEmailContent.testResult,
          tumorMolecularSurveillanceSummary: piEmailContent.cancerMutation,
          testResultComparisonExcludeOptions: piEmailContent.testResultCompareExcludeOptions && piEmailContent.testResultCompareExcludeOptions.join(','),
          testResultComparison: piEmailContent.testResultCompare,
          emailEnding: piEmailContent.emailEndInfo,
          targetAssociatedVariant: piEmailContent.variation
        }
      }
    },
    /**
     * 保存产品邮件配置
     * 调用API保存邮件配置，并处理保存结果
     */
    async handleConfirm () {
      const params = this.setParams()
      this.submitBtnLoading = true
      const {res} = await awaitWrap(saveProductEmailConfig(params))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('保存成功')
        this.visible = false
        this.$emit('dialogConfirmEvent')
      }
      this.submitBtnLoading = false
    }
  }
}
</script>

<template>
  <el-dialog
    title="产品邮件配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :modal-append-to-body='false'
    :before-close="handleClose"
    width="1100px"
    append-to-body
    @open="handleOpen">
    <el-form
      ref="form"
      :model="form"
      size="mini"
      class="form-container"
      inline
      label-width="140px">
      <!--      产品名称-->
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model.trim="form.productName" clearable disabled/>
      </el-form-item>
      <!--      邮件主题规则-->
      <el-form-item label="邮件主题规则" prop="emailSubject">
        <el-select v-model.trim="form.emailSubject" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in emailSubjectList">
          </el-option>
        </el-select>
      </el-form-item>
      <!--      邮件样例编号-->
      <el-form-item label="邮件样例编号" prop="emailSampleCode">
        <el-select v-model.trim="form.emailSampleCode" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in emailSampleCodeList">
          </el-option>
        </el-select>
      </el-form-item>
      <!--      报告邮件名规则-->
      <el-form-item label="报告文件名规则" prop="reportEmailName">
        <el-select v-model.trim="form.reportEmailName" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in fileNames">
          </el-option>
        </el-select>
      </el-form-item>
      <!--      发送补充报告-->
      <el-form-item label="发送补充报告" prop="isSendSupplementReport">
        <el-select v-model.trim="form.isSendSupplementReport" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="item"
            v-for="(item, index) in booleanValue">
          </el-option>
        </el-select>
      </el-form-item>
      <!--      发送补充报告Excel-->
      <el-form-item label="发送补充报告Excel" prop="isSendSupplementExcel">
        <el-select v-model.trim="form.isSendSupplementExcel" clearable placeholder="请选择" style="width: 100%">
          <el-option
            :key="index"
            :label="item"
            :value="index"
            v-for="(item, index) in booleanValue">
          </el-option>
        </el-select>
      </el-form-item>
      <!--      发送生信文件-->
      <el-form-item label="发送生信文件" prop="isSendBioinformaticsFile">
        <div style="display: flex; align-items: center">
          <el-select v-model.trim="form.isSendBioinformaticsFile" clearable multiple collapse-tags placeholder="请选择" style="width: 100%">
            <el-option
              :key="index"
              :label="item"
              :value="item"
              v-for="(item, index) in bioinformaticsFileList">
            </el-option>
          </el-select>
          <el-tooltip class="item" effect="dark" content="需要提供抓取路径给开发，最终输出时才能用路径拿到文件"
                      placement="right">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>

      </el-form-item>

      <el-tabs v-model="activeTab" type="card" size="mini">
        <el-tab-pane label="正式邮件正文" name="1">
          <email-content v-model="form.emailContent"></email-content>
        </el-tab-pane>
        <el-tab-pane label="PI邮件正文" name="2">
          <email-content v-model="form.piEmailContent"></email-content>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleConfirm">确认
      </el-button>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
> > > .el-dialog {
  margin: 3vh auto !important;
}
</style>
