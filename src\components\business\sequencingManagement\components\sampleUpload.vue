<script>
import {awaitWrap, downloadFile, getSessionInfo, readBlob} from '../../../../util/util'
import {downloadTemplate, getTaskList} from '../../../../api/sequencingManagement/sequencingManagementApi'
import Cookies from 'js-cookie'
import constants from '../../../../util/constants'

export default {
  name: 'sampleUpload',
  props: {
    currentProcess: {
      type: Number,
      default: 1
    }
  },
  mounted () {
    if (this.areaList.findIndex(v => v.value === +localStorage.getItem('scheduleArea')) !== -1) {
      this.form.area = +localStorage.getItem('scheduleArea')
    }
    this.getTaskList()
  },
  computed: {
    uploaderParams () {
      if (this.form.type === 0) {
        return {
          fworkflowType: this.currentProcess,
          ftaskCode: this.form.taskCode
        }
      }
      return {
        fworkflowType: this.currentProcess,
        ftaskCode: '',
        fproductionArea: this.form.area
      }
    }
  },
  data () {
    return {
      isUpload: false,
      options: {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      },
      taskCodeList: [],
      uploadUrl: constants.JS_CONTEXT + '/experiment/schedule/add_sample_by_import',
      form: {
        type: 0,
        sequencingPlatform: '', // 测序平台
        sequencingType: '', // 测序类型
        makeDNB: '两步法', // makeDNB
        area: '', // 片区
        fileList: []
      },
      processTypes: [
        {label: '仅当前环节', value: '仅当前环节'},
        {label: '完整工序', value: '完整工序'}
      ],
      typeOptions: [
        {label: 'PE150', value: 'PE150'},
        {label: 'PE100', value: 'PE100'}
      ],
      makeDNBMethods: ['一步法', '两步法'],
      // 用户所属实验室排除苏州
      areaList: JSON.parse(Cookies.get('labOptions') || '')
        .filter(v => (getSessionInfo('currentLab') || [])
          .includes(v.value)).filter(v => v.label !== '苏州'),
      rules: {
        taskCode: [
          {required: true, message: '请选择任务单号', trigger: 'change'}
        ],
        sequencingPlatform: [
          {required: true, message: '请选择测序平台', trigger: 'change'}
        ],
        sequencingType: [
          {required: true, message: '请选择测序类型', trigger: 'change'}
        ],
        processType: [{required: true, message: '请选择工序类型', trigger: 'change'}],
        makeDNB: [
          {required: true, message: '请选择makeDNB', trigger: 'change'}
        ],
        area: [
          {required: true, message: '请选择片区', trigger: 'change'}
        ],
        fileList: [
          {required: true, message: '请选择文件', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    // 获取任务单列表
    async getTaskList () {
      const params = {
        pageVO: {
          currentPage: 1,
          pageSize: 100000
        }
      }
      let {res} = await awaitWrap(getTaskList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.taskCodeList = []
        const rows = data.rows || []
        rows.forEach(v => {
          const item = {
            label: v.fcode,
            value: v.fcode // 任务单号
          }
          this.taskCodeList.push(item)
        })
      }
    },
    // 排单地区变化
    handleScheduleAreaChange () {
      localStorage.setItem('scheduleArea', this.form.area)
    },
    handleValidForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.handleCloseLoading()
            this.$message.error('表单存在错误，请检查')
          }
        })
      })
    },
    // 导入样本进入下一步回填
    async handleUpload () {
      const fileList = this.$refs.upload.uploadFiles || []
      if (fileList.length < 1) {
        this.$message.error('请上传任务文件')
        this.handleCloseLoading()
        return
      }
      this.form.fileList = fileList
      await this.handleValidForm()
      if (this.currentProcess === 4 && this.form.makeDNB === '一步法') {
        this.$message.warning('环化环节必须两步法')
        this.handleCloseLoading()
        return
      }
      this.$refs.upload.submit()
    },
    handleCloseLoading () {
      this.$emit('closeLoadingEvent')
    },
    // 流程类型变更，改变makeDNB选项必填性
    handleProcessTypeChange () {
      //  选择“完整工序”/处于makeDNB环节的添加样本"请选择makeDNB"为必填，否则为非必填
      if (this.form.processType === '完整工序' || this.currentProcess === 5) {
        this.rules.makeDNB = [{required: true, message: '请选择makeDNB', trigger: 'change'}]
      } else {
        this.rules.makeDNB = []
      }
    },
    /** **********************************上传下载相关*********************************************************/
    async handleDownload () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTemplate(this.type))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.handleCloseLoading()
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.handleCloseLoading()
        return false
      }
    },
    // 文件改变时回调，判断文件是否替换
    async handleFileChange (file, fileList) {
      if (fileList.length < 2) return
      const { err } = await awaitWrap(this.$confirm('一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      err ? fileList.pop() : fileList.shift()
    },
    handleError () {
      this.handleCloseLoading()
      this.$message.error('导入失败')
    },
    async handleOnSuccess (res) {
      this.handleCloseLoading()
      if (res.code !== this.SUCCESS_CODE) {
        this.$message.error(res.message)
        return
      }
      this.$refs.upload.clearFiles()
      const data = res.data || {}
      const rows = data.rows || []
      const prompt = data.prompt || []
      const nonExtremeSampleTotal = data.nonExtremeSampleTotal || ''
      if (nonExtremeSampleTotal > 0) {
        const message = `导入样本中包含${data.nonExtremeSampleTotal}个非极测样本未被申请检测，确认继续导入吗？`
        await this.$confirm(message, '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })
      }
      if (prompt.length > 0) {
        this.$showSequencingErrorDialog({tableData: prompt})
      }
      if (rows.length < 1) {
        await this.$confirm('导入任务单中不存在有效样本，无法生成任务单，请知悉。', '提示', {
          cancelButtonText: '关闭',
          showConfirmButton: false,
          type: 'warning'
        })
        return
      }
      this.$emit('uploadSuccessEvent', {data: data.rows, form: this.form})
    }
  }
}
</script>

<template>
  <el-form ref="form" :model="form" label-suffix=":" inline :rules="rules" label-width="120px">
    <el-radio-group v-model="form.type" style="margin: 10px 0;">
      <el-radio :label="0">添加到已有任务单</el-radio>
      <el-radio :label="1">建立新任务单</el-radio>
    </el-radio-group>
    <div class="flex-wrapper">
      <template v-if="!form.type">
        <el-form-item key="taskCode" label="任务单号" prop="taskCode">
          <el-select-v2 v-model.trim="form.taskCode" :options="taskCodeList" size="mini" clearable filterable placeholder="请选择">
          </el-select-v2>
        </el-form-item>
      </template>
      <template v-if="form.type">
        <el-form-item key="sequencingType" label="测序类型" prop="sequencingType">
          <el-select
            v-model.trim="form.sequencingType"
            placeholder="请选择"
            clearable
            filterable
            size="mini"
            style="width: 100%;">
            <el-option
              v-for="item in typeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item key="area" label="所属片区" prop="area">
          <el-select v-model.trim="form.area" size="mini" clearable placeholder="请选择" @change="handleScheduleAreaChange">
            <el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="工序类别" prop="processType">
        <el-select v-model.trim="form.processType" size="mini" placeholder="请选择" @change="handleProcessTypeChange">
          <el-option v-for="item in processTypes" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item key="makeDNB" label="makeDNB" prop="makeDNB">
        <el-select
          v-model.trim="form.makeDNB"
          placeholder="请选择"
          clearable
          filterable
          size="mini"
          style="width: 100%;">
          <el-option
            v-for="item in makeDNBMethods"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
    </div>
    <div>
      <el-form-item label="任务文件上传" prop="fileList">
        <el-button type="text" size="mini" @click="handleDownload">文件模板下载</el-button>
        <el-upload
          ref="upload"
          :auto-upload="false"
          :file-list="fileList"
          :action="uploadUrl"
          :data="uploaderParams"
          :before-upload="handleBeforeUpload"
          :on-change="handleFileChange"
          :on-success="handleOnSuccess"
          :on-error="handleError"
          style="text-align: center; width: 520px"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
            <br/>
            仅支持 xls、xlsx，只能上传1份小于10M的文件；
          </div>
        </el-upload>
      </el-form-item>
    </div>
  </el-form>
</template>

<style scoped lang="scss">

</style>
