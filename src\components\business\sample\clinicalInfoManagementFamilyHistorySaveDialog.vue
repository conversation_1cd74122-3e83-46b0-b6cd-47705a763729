<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :modal="false"
      :close-on-click-modal="false" :before-close="handleClose"
      v-drag-dialog
      width="60%"
      @open="handleOpen"
    >
      <div style="max-height: 400px; overflow: auto;">
        <el-form ref="form" :model="form" label-width="110px" size="mini">
          <el-row>
            <template v-for="(item, index) in form.list">
              <div :key="'familyHistory' + index">
                <el-col :span="5" class="colHeight">
                  <el-form-item :prop="'list.' + index + '.relativesSampleNum'" :rules="relativesSampleNumRules" label="亲属样本编号">
                    <el-input v-model.trim="item.relativesSampleNum" clearable maxlength="30" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="5" class="colHeight">
                  <el-form-item :prop="'list.' + index + '.relationship'" :rules="relationshipRules" label="亲属关系" label-width="90px">
                    <el-input v-model.trim="item.relationship" clearable maxlength="10" placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="5" class="colHeight">
                  <el-form-item :prop="'list.' + index + '.age'" :rules="ageRules" label="确诊年龄" label-width="80px">
                    <el-input v-model.number="item.age" clearable placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6" class="colHeight">
                  <el-form-item :prop="'list.' + index + '.cancer'" :rules="cancerRules" label="诊断癌种" label-width="90px">
                    <el-select v-model="item.cancer" allow-create filterable multiple clearable collapse-tags placeholder="请选择" class="selectWidth">
                      <el-option
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        v-for="item in cancerList">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="3" class="colHeight">
                  <el-form-item label-width="10px">
                    <el-button v-if="index === form.list.length - 1" type="primary" icon="el-icon-plus" circle size="mini" @click="handleAdd"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini"  @click="handleDelete(index)"></el-button>
                  </el-form-item>
                </el-col>
              </div>
            </template>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'clinicalInfoManagementFamilyHistorySaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      title: '新增家族史',
      sampleBasicId: null,
      form: {
        list: [
          {
            familyHistoryId: null,
            sampleBasicId: null,
            relationship: '',
            age: '',
            cancer: [],
            cancerOther: '',
            relativesSampleNum: ''
          }
        ]
      },
      cancerList: [],
      relativesSampleNumRules: [],
      relationshipRules: [
        {required: true, message: '请输入亲属关系', trigger: 'blur'}
      ],
      cancerRules: [
        {required: true, message: '请选择癌种', trigger: ['blur', 'change']}
      ],
      ageRules: [
        {pattern: /^\d+$/, message: '格式错误', trigger: 'blur'}
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.getCancerList()
        this.sampleBasicId = this.pdata.sampleBasicId
        if (this.pdata.familyHistoryId) {
          this.title = '编辑家族史'
        } else {
          this.title = '新增家族史'
        }
        this.form.list = []
        this.form.list.push(this.pdata)
      })
    },
    getCancerList () {
      this.$ajax({
        url: '/cancertype/list_cancers_by_cancer_class',
        data: {
          cancerClass: 0
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.cancerList = []
          result.data.forEach(v => {
            this.cancerList.push({
              value: v.cancerTypeId,
              label: v.cancerTypeName
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let item = {}
          let cancer = []
          let cancerOther = []
          let data = {
            clinicalFamilyList: [],
            sampleBasicId: this.sampleBasicId
          }
          this.form.list.forEach(v => {
            cancer = []
            cancerOther = []
            item = {
              familyHistoryId: v.familyHistoryId,
              sampleBasicId: v.sampleBasicId,
              relationship: v.relationship,
              age: v.age,
              cancer: '',
              cancerOther: '',
              relativesSampleNum: v.relativesSampleNum
            }
            v.cancer.forEach(vv => {
              if (/^\d+$/.test(vv)) {
                cancer.push(vv)
              } else {
                cancerOther.push(vv)
              }
            })
            item.cancer = cancer.toString()
            item.cancerOther = cancerOther.toString()
            data.clinicalFamilyList.push(item)
          })
          this.loading = true
          this.$ajax({
            url: '/sample/clinical/save_family_history',
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('familyHistorySaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleAdd () {
      this.form.list.push({
        familyHistoryId: null,
        sampleBasicId: this.sampleBasicId,
        relationship: '',
        age: '',
        cancer: [],
        cancerOther: '',
        relativesSampleNum: ''
      })
    },
    handleDelete (index) {
      this.form.list.splice(index, 1)
      if (this.form.list.length === 0) {
        this.handleAdd()
      }
    }
  }
}
</script>

<style scoped>
  .selectWidth{
    width: 100%;
  }
  .colHeight{
    height: 51px;
  }
</style>
