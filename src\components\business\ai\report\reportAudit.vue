<template>
  <div>
    <div class="btn">
      <el-button size="mini" type="primary" @click="handleReport">生成Word报告</el-button>
      <span style="margin-left: 20px;">{{name}}</span>
    </div>
    <el-card>
      <div class="content">
        <div class="menu">
          <el-tabs v-model="activeName"
                   tab-position="left"
                   style="height: calc(100vh - 40px - 110px - 32px - 20px)">
            <el-tab-pane name="baseInfo" label="基本信息"></el-tab-pane>
            <el-tab-pane name="testSummary" label="检测小结"></el-tab-pane>
            <el-tab-pane name="cancerEarlyWarn" label="癌种预警检测"></el-tab-pane>
            <el-tab-pane name="qualityControlResults" label="质控结果"></el-tab-pane>
            <el-tab-pane name="plasmaMicrobe" label="血浆微生物"></el-tab-pane>
            <el-tab-pane name="risk" label="肿瘤遗传风险"></el-tab-pane>
            <el-tab-pane name="pathogenicDetail" label="致病突变详情"></el-tab-pane>
            <el-tab-pane name="resultRead" label="结果解读"></el-tab-pane>
            <el-tab-pane name="diseaseIntroduction" label="疾病简介"></el-tab-pane>
            <el-tab-pane name="cancerRisk" label="患癌风险"></el-tab-pane>
            <el-tab-pane name="geneticMutationMap" label="移码突变图谱"></el-tab-pane>
            <el-tab-pane name="nutritionMetabolism" label="营养代谢"></el-tab-pane>
            <el-tab-pane name="detectionResult" label="检测结果"></el-tab-pane>
          </el-tabs>
        </div>
        <div style="flex: 1; padding: 10px; overflow: auto;">
          <component :is="activeName"></component>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import baseInfo from './baseInfo'
import testSummary from './testSummary'
import cancerEarlyWarn from './cancerEarlyWarn'
import qualityControlResults from './qualityControlResults'
import plasmaMicrobe from './plasmaMicrobe'
import pathogenicDetail from './pathogenicDetail'
import resultRead from './resultRead'
import diseaseIntroduction from './diseaseIntroduction'
import cancerRisk from './cancerRisk'
import geneticMutationMap from './geneticMutationMap'
import nutritionMetabolism from './nutritionMetabolism'
import detectionResult from './detectionResult'
import risk from './risk'

export default {
  components: {
    baseInfo, // 基本信息
    testSummary, // 测试小结
    cancerEarlyWarn, // 癌种预警检测
    qualityControlResults, // 质控结果
    risk, // 患癌遗传风险
    plasmaMicrobe, // 血浆微生物
    pathogenicDetail, // 致病突变详情
    resultRead, // 结果解读
    diseaseIntroduction, // 疾病简介
    cancerRisk, // 患癌风险
    geneticMutationMap, // 移码突变图谱
    nutritionMetabolism, // 营养代谢
    detectionResult // 检测结果
  },
  mounted () {
    this.getProductInfo()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      activeName: 'baseInfo',
      name: ''
    }
  },
  methods: {
    // 生成报告
    async handleReport () {
      const {code, message} = await this.$ajax({
        url: '/read/bigAi/create_report',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.container'
      })
      if (code && code === this.SUCCESS_CODE) {
        this.$message.success('生成成功')
      } else {
        this.$message.error(message)
      }
    },
    // 获取产品信息
    async getProductInfo () {
      const {code, message, data} = await this.$ajax({
        url: '/read/bigAi/get_protypename_hresult',
        loadingDom: '.container',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        }
      })
      if (code && code === this.SUCCESS_CODE) {
        this.name = data
      } else {
        this.$message.error(message)
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-card__body {
  padding: 0;
}
.btn {
  margin: 10px;
}
.content {
  display: flex;
  height: calc(100vh - 40px - 110px - 32px - 20px);
  .menu {
    width: 130px;
  }
}
</style>
