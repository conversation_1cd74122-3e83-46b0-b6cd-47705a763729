<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="900px"
    @open="handleOpen">
    <el-form label-width="120px" style="width: 90%">
      <el-row>
        <el-col :span="12">
          <el-form-item label="基因名称">
            {{form.gene}}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="癌症类型">
            <el-input v-model.trim="form.accessType" size="mini" clearable placeholder="请输入癌症类型"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="药物名称">
            <el-input v-model.trim="form.drugName" size="mini" clearable placeholder="请输入药物名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="疗效相关性">
            <el-select v-model.trim="form.nucleotideMutationNumber" size="mini" style="width: 100%" clearable placeholder="请选择疗效相关性">
              <el-option
                :key="index"
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in means">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Object
    }
  },
  data () {
    return {
      title: '修改用药信息',
      loading: false,
      means: [
        {
          value: '0',
          label: '提示敏感'
        },
        {
          value: '1',
          label: '提示耐药或无效'
        },
        {
          value: '2',
          label: '研究结论不一致'
        },
        {
          value: '3',
          label: '没有关联'
        }, {
          value: '4',
          label: '未知'
        }
      ],
      form: {}
    }
  },
  methods: {
    handleOpen () {
      this.form = {}
      this.form = JSON.parse(JSON.stringify(this.pdata))
    },
    handleConfirm () {
      this.loading = true
      this.$ajax({
        url: '/read/bigAi/save_h_use_drug',
        data: {
          analysisRsId: this.analysisRsId,
          databaseId: this.form.databaseId,
          gene: this.form.gene,
          cancerClass: this.form.accessType,
          drugName: this.form.drugName,
          curativePertinence: this.form.nucleotideMutationNumber
        }
      }).then(res => {
        if (res.code && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped></style>
