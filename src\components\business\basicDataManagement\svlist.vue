<template>
  <div>
    <b class="title">体系SV</b>
    <el-table
      :data="tableDataSv"
      ref="table"
      border
      class="table"
      size="mini"
      height="35vh"
    >
      <el-table-column prop="geneSampleNum" min-width="100" label="样本编号"></el-table-column>
      <el-table-column prop="fisSelect" min-width="100" label="Is Select"></el-table-column>
      <el-table-column prop="fnumOfHot" min-width="100" label="NumOfHot"></el-table-column>
      <el-table-column prop="ffusionType" min-width="100" label="FusionType"></el-table-column>
      <el-table-column prop="fAlignGene" min-width="120" label="FAlignGene"></el-table-column>
      <el-table-column prop="fAlignTrancript" min-width="120" label="FAlignTrancript"></el-table-column>
      <el-table-column prop="fAlignExon" min-width="120" label="FAlignExon"></el-table-column>
      <el-table-column prop="ffusionInfo" min-width="120" label="FusionInfo"></el-table-column>
      <el-table-column prop="ffreq" min-width="120" label="Freq"></el-table-column>
      <el-table-column prop="fpos1" min-width="120" label="Pos1"></el-table-column>
      <el-table-column prop="fpos2" min-width="120" label="Pos2"></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :total="totalPage"
      style="background-color: #ffffff;width: 350px;"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
  </div>
</template>

<script>
import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.tablePaginationCommonData],
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableDataSv: [],
      pageSize: 20,
      pageSizes: [20, 50, 100]
    }
  },
  methods: {
    // sv列表数据
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_organize_analysis_sv_result',
        method: 'get',
        data: {
          fsarId: this.$route.query.fsarId,
          page: this.currentPage,
          rows: this.pageSize
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.totalPage = data.total
          let rows = data.records || []
          this.tableDataSv = []
          rows.forEach(v => {
            let item = {
              geneSampleNum: v.geneSampleNum,
              fisSelect: v.fisSelect,
              fnumOfHot: v.fnumOfHot,
              ffusionType: v.ffusionType,
              fAlignGene: v.falignGene,
              fAlignTrancript: v.falignTrancript,
              fAlignExon: v.falignExon,
              ffusionInfo: v.ffusionInfo,
              ffreq: v.ffreq,
              fpos1: v.fpos1,
              fpos2: v.fpos2
            }
            item.realData = item
            util.setDefaultEmptyValueForObject(item)
            this.tableDataSv.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.info-title {
  padding: 10px;
  background-color: #aaa;
  border: 1px solid #eee;
}
.title {
  height: 40px;
  line-height: 40px;
  padding: 10px;
}
</style>
