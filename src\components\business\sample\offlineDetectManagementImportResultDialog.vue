<template>
  <div>
    <el-dialog
            :close-on-click-modal="false"
            :visible.sync="visible"
            :before-close="handleClose"
            title="样本科研导入"
            width="500px"
            @open="handleOpen">
      <div class="upLoad">
        <el-upload
          ref="upload"
          :disabled="disabled"
          :on-success="handleOnSuccess"
          :on-error="handleOnError"
          :data="uploadParams"
          :auto-upload="false"
          :limit="2"
          :file-list="fileList"
          :before-upload="handleBeforeUpload"
          :action="uploadUrl">
          <el-button slot="trigger" size="mini" type="primary">选取文件</el-button>
          <div slot="tip" class="el-upload__tip">请选择Excel文件和图片压缩包zip文件</div>
        </el-upload>
      </div>
      <div style="margin-top: 10px;">
        <el-button @click="handleSubmit">上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
export default {
  name: 'sampleArrivalConfirmImportDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      uploadUrl: constants.JS_CONTEXT + '/sample/confirm/import_sample_sheet',
      uploadParams: {
      },
      fileList: [],
      loading: false,
      disabled: false
    }
  },
  methods: {
    handleOpen () {},
    // 提交
    handleSubmit () {
      let dom = this.$refs.upload
      let fileLists = dom.uploadFiles
      let hasXlsxFile = fileLists.some(item => {
        return /\.(xlsx|xls)$/i.test(item.name)
      })
      let hasZipFile = fileLists.some(item => {
        return /\.zip$/i.test(item.name)
      })
      if (!hasXlsxFile) {
        this.$message.error('缺少Excel文件')
        return
      }
      if (!hasZipFile) {
        this.$message.error('缺少图片ZIP文件')
        return
      }
      this.$refs.upload.submit()
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res.statusCode === this.SUCCESS_CODE) {
        let data
        if (res.data.url) {
          data = res.data.url
        } else if (res.data.json) {
          data = res.data.json
        }
        this.$emit('dialogConfirmEvent', data, this.needlePlotParams.gene)
      } else {
        this.$msg.err(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      return true
      // let name = file.name
      // let size = file.size
      // if (/\.(xlsx|xls)$/.test(name)) {
      //   let vaildSize = size <= 10 * 1024 * 1024 * 8
      //   if (!vaildSize) {
      //     this.loading = false
      //     this.$message.error('文件大小超过限制，无法上传')
      //   }
      //   return vaildSize
      // } else {
      //   this.loading = false
      //   this.$message.error('只能上传xlsx或xls文件')
      //   return false
      // }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    }
  }
}
</script>

<style scoped>

</style>
