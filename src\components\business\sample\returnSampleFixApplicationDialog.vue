<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="修改申请"
      width="500px"
      @open="handleOpen">
      <el-form :model="form" ref="form" :rules="rules" label-width="120px">
        <el-form-item label="返样类型" prop="returnType">
          <el-select v-model.trim="form.returnType"  size="mini" clearable>
            <el-option :key="item.code" :label="item.label" :value="item.value" v-for="item in returnTypes"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="返样要求" prop="returnSampleExplain">
          <el-input v-model.trim="form.returnSampleExplain" autosize type="textarea" size="mini" clearable maxlength="50" style="width: 250px;"></el-input>
        </el-form-item>
        <template v-if="form.returnType !== judgeState">
          <el-form-item label="返样收件人" prop="returnSampleReceiver">
            <el-input v-model.trim="form.returnSampleReceiver" size="mini" clearable maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="返样地址" prop="address">
            <el-cascader
              v-model="form.address"
              :options="regionList"
              :props ="{checkStrictly: true, value: 'code', label: 'name'}"
              clearable
              size="mini"
              placeholder="请选择">
            </el-cascader>
          </el-form-item>
          <el-form-item prop="addressDetail">
            <el-input v-model.trim="form.addressDetail" autosize type="textarea" size="mini" clearable maxlength="200" style="width: 250px;"></el-input>
          </el-form-item>

          <el-form-item label="返样收件人电话" prop="applyUserPhone">
            <el-input v-model.trim="form.applyUserPhone" size="mini" clearable></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
          :loading="submitBtnLoading"
          size="mini"
          type="primary"
          @click="handleConfirm">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number
    },
    experimentStatusName: {
      type: String
    }
  },
  computed: {
    // 计算随报告返样的编码
    judgeState () {
      console.log(this.returnTypes, this.returnTypes.filter(item => item.label === '随报告返样')[0])
      let judgeState = this.returnTypes.filter(item => item.label === '随报告返样')[0] || {}
      return judgeState.value
    }
  },
  data () {
    return {
      form: {
        returnType: '',
        address: '',
        addressDetail: '',
        returnSampleExplain: '',
        returnSampleCounty: '',
        applyUserPhone: ''
      },
      regionList: [],
      returnStatus: [],
      returnTypes: [],
      submitBtnLoading: false,
      rules: {
        returnType: [{
          required: true, message: '请选择返样类型', trigger: 'blur'
        }],
        address: [{
          required: true, message: '请选择返样地址', trigger: 'blur'
        }],
        addressDetail: [
          {
            required: true, message: '请输入详细地址', trigger: 'blur'
          }
        ],
        returnSampleReceiver: [{
          required: true, message: '请输入返样收件人', trigger: 'blur'
        }],
        applyUserPhone: [
          {required: true, message: '请输入返样收件人电话', trigger: 'blur'},
          {validator: util.validatePhone, trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        console.log(this.id)
        this.$refs.form.resetFields()
        this.form = {
          returnType: '',
          address: '',
          addressDetail: '',
          returnSampleExplain: '',
          returnSampleCounty: '',
          applyUserPhone: ''
        }
        this.getReturnTypes()
        this.getRegionList()
        this.getFormData()
      })
    },
    // 获取返样申请数据
    getFormData () {
      this.$ajax({
        url: '/sample/return/get_sample_return_detail',
        data: {
          fid: this.id
        },
        method: 'get'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || {}
          this.form = {
            returnType: data.freturnType * 1,
            returnSampleExplain: data.returnSampleExplain,
            returnSampleReceiver: data.returnSampleReceiver,
            address: [data.returnSampleProvince, data.returnSampleCity, data.returnSampleCounty],
            applyUserPhone: data.applyUserPhone,
            addressDetail: data.returnSampleAddress
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取返样类型列表
    getReturnTypes () {
      this.$ajax({
        url: '/sample/return/list_sample_return_type',
        methods: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.returnTypes = []
          data.forEach(item => {
            this.returnTypes.push({
              label: item.name,
              value: item.code
            })
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取地区列表
    getRegionList () {
      this.$ajax({
        url: '/sample/basic/get_all_provice_city_region'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.regionList = result.data
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 修改申请
    handleConfirm () {
      // 修改为“退单返样”时，样例状态必须为“暂停检测”或者“停止检测”，否则提示：“暂停或者停止检测的样本才能修改为退单返样。”
      console.log(this.judgeState)
      let type = this.returnTypes.filter(item => item.label === '退单返样')[0].value
      if (this.form.returnType === type && (this.experimentStatusName !== '暂停检测' && this.experimentStatusName !== '停止检测')) {
        this.$message.error('暂停或者停止检测的样本才能修改为退单返样')
        return
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitBtnLoading = true
          let data = {
            fid: this.id,
            freturnType: this.form.returnType,
            returnSampleExplain: this.form.returnSampleExplain
          }
          if (this.form.returnType !== this.judgeState) {
            data.returnSampleReceiver = this.form.returnSampleReceiver
            data.returnSampleAddress = this.form.addressDetail
            data.returnSampleProvince = this.form.address[0]
            data.returnSampleCity = this.form.address[1]
            data.returnSampleCounty = this.form.address[2]
            data.applyUserPhone = this.form.applyUserPhone
          }
          this.$ajax({
            url: '/sample/return/modify_sample_return',
            data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.visible = false
              this.$message.success('修改成功')
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.submitBtnLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
/deep/ .el-input--mini .el-input__inner {
  width: 250px;
}
/deep/ .el-input {
  width: 250px;
}
</style>
