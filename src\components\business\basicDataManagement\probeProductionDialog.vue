<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="编辑生产信息"
      width="1000px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item prop="fproductionBatch" label="生产批次">
              <el-input v-model.trim="form.fproductionBatch" size="mini" maxlength="50" clearable
                        placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="foperationTime" label="上机时间">
              <el-date-picker
                v-model.trim="form.foperationTime"
                align="right"
                type="date"
                size="mini"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item prop="foperationPerson" label="上机人员">
              <el-input v-model.trim="form.foperationPerson" size="mini" maxlength="50" clearable
                        placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="fproductLocation" label="产品位置">
              <el-input v-model.trim="form.fproductLocation" size="mini" maxlength="50" clearable
                        placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item prop="fdemand" label="需求量">
              <el-input v-model.trim="form.fdemand" size="mini" maxlength="50" clearable
                        placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="festimatedDeliveryDate" label="预计货期">
              <el-date-picker
                v-model.trim="form.festimatedDeliveryDate"
                align="right"
                type="date"
                size="mini"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                clearable>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item prop="fqualityInspection" label="质检情况">
              <el-select v-model.trim="form.fqualityInspection" size="mini" placeholder="请选择">
                <el-option
                  :key="item"
                  :label="item"
                  :value="item"
                  v-for="item in ['合格', '不合格']">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="fileList" label="附件质检表">
              <el-upload
                :action="action"
                ref="upload"
                :limit="1"
                :data="{fid: $route.query.id, ...form}"
                :auto-upload="false"
                :on-success="handleOnSuccess"
                :before-upload="handleBeforeUpload"
                :before-remove="handleRemove"
                :file-list="form.fileList"
                class="upload-demo">
                <el-button size="mini" type="primary">点击上传</el-button>
                <span slot="tip" class="el-upload__tip">可上传excel、zip、rar文件，且仅能上传一个</span>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    productionInfo: {
      type: Object,
      required: false
    }
  },
  data () {
    return {
      title: '新增癌种名称简称',
      loading: false,
      action: constants.JS_CONTEXT + '/system/probe/update_probe_production_data',
      form: {
        fproductionBatch: '',
        foperationTime: '',
        foperationPerson: '',
        fproductLocation: '',
        fileList: [],
        fdemand: '',
        festimatedDeliveryDate: '',
        fqualityInspection: '',
        fqualityAppendixFile: ''
      },
      rules: {

      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.form = JSON.parse(JSON.stringify(this.productionInfo)) || {}
        if (this.form.fqualityAppendixFileUrl) {
          this.form.fileList = [this.form.fqualityAppendixFileUrl]
        }
      })
    },
    // 保存配置
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.$refs.upload.uploadFiles.length > 0 && this.$refs.upload.uploadFiles[0].status !== 'success') {
            this.$refs.upload.submit()
          } else {
            this.$ajax({
              url: '/system/probe/update_probe_production_data',
              isFormData: true,
              data: {fid: this.$route.query.id, ...this.form}
            }).then(result => {
              if (result.code === this.SUCCESS_CODE) {
                this.$message.success('保存成功')
                this.visible = false
                this.$emit('dialogConfirmEvent')
              } else {
                this.$message.error(result.message)
              }
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    handleOnSuccess (res) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('上传成功')
        this.$refs.upload.clearFiles()
        this.$emit('dialogConfirmEvent')
        this.visible = false
      } else {
        this.loading = false
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    handleRemove () {
      this.form.fqualityAppendixFileUrl = null
      this.form.fqualityAppendixFileHttpUrl = null
      this.form.fileList = null
      this.$refs.upload.clearFiles()
    },
    // 上传前校验文件名和文件大小
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls|zip|rar)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 500) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过500M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传Excel文件')
        return false
      }
    }
  }
}
</script>

<style scoped>
/deep/ .el-input {
  width: 250px;
}
</style>
