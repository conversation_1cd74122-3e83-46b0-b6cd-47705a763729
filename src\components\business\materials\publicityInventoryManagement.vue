<template>
  <div style="height: 100%;">
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini" @keyup.enter.native="handleSearch">
        <el-form-item label="品类" label-width="60px">
          <el-input v-model.trim="form.name" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="物料类别">
          <el-select v-model="form.categoryId" clearable placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in materialCategoryList">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('013002001', 'buttons')" type="primary" size="mini" @click="handleSaveMaterial(0)">添加</el-button>
      <el-button v-if="$setAuthority('013002002', 'buttons')" type="primary" size="mini" @click="handleSaveMaterial(1)">编辑</el-button>
      <el-button v-if="$setAuthority('013002009', 'buttons')" type="primary" size="mini" @click="handleVisibleApplication">可见申请方式</el-button>
      <el-button v-if="$setAuthority('013002003', 'buttons')" type="primary" size="mini" @click="handleDelete">删除</el-button>
      <el-button v-if="$setAuthority('013002004', 'buttons')" type="primary" size="mini" @click="handleOutOfStock">出库</el-button>
      <el-dropdown v-if="$setAuthority('013002005', 'buttons')" size="mini" placement="bottom" style="margin: 0 10px;" @command="handleWarehousing">
        <el-button type="primary" size="mini">
          入库<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0" v-if="$setAuthority('013002006', 'buttons')">批量上传入库</el-dropdown-item>
          <el-dropdown-item :command="1" v-if="$setAuthority('013002007', 'buttons')">手动入库</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!--<el-button type="primary" size="mini" @click="handleWarehousing">入库</el-button>-->
      <el-button v-if="$setAuthority('013002008', 'buttons')" type="primary" size="mini" @click="handleCategoryDetail">类别管理</el-button>
      <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
      <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
    </div>
    <div class="content">
      <div class="table">
        <el-table
          ref="table"
          :data="tableData"
          size="mini"
          class="materialInventoryManagement"
          height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
          style="width: 100%"
          @select="handleSelect"
          @row-click="handleRowClick">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="code" label="物料编码" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="category" label="物料类别" min-width="120"></el-table-column>
          <el-table-column prop="name" label="品类" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="spec" label="规格" width="120"></el-table-column>
          <el-table-column prop="version" label="版本号" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unit" label="单位" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="stock" label="库存量" width="120"></el-table-column>
          <el-table-column prop="updater" label="更新人"  width="100"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="140"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <materials-save-dialog
      :pvisible.sync="materialsSaveDialogVisible"
      :pdata="materialsSaveDialogData"
      @materialsSaveDialogConfirmEvent="handleMaterialsSaveDialogConfirm"
    />
    <category-detail-dialog
      :pvisible.sync="categoryDetailDialogVisible" :ptype="categoryDetailDialogData.type"
      @categoryDetailDialogConfirmEvent="handleCategoryDetailDialogConfirm"
    ></category-detail-dialog>
    <warehousing-upload-dialog
      :pvisible.sync="warehousingUploadDialogVisible" :ptype="1"
      @warehousingUploadDialogConfirmEvent="handleWarehousingUploadDialogConfirm"
    ></warehousing-upload-dialog>
    <warehousing-save-dialog
      :pvisible.sync="warehousingSaveDialogVisible" :ptable-data="warehousingSaveDialogData.tableData" :ptype="warehousingSaveDialogData.type"
      @warehousingSaveDialogConfirmEvent="handleWarehousingSaveDialogConfirm"
    ></warehousing-save-dialog>
    <out-of-stock-save-dialog
      :pvisible.sync="outOfStockSaveDialogVisible" :ptype="outOfStockSaveDialogData.type"
      @outOfStockSaveDialogConfirmEvent="handleOutOfStockSaveDialogConfirm"
    ></out-of-stock-save-dialog>
    <visible-application-dialog
      :pvisible.sync="applicationData.visible"
      :id="applicationData.id"
      @dialogConfirmEvent="handleSearch"
    ></visible-application-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import materialsSaveDialog from './materialsSaveDialog'
import categoryDetailDialog from './categoryDetailDialog'
import warehousingUploadDialog from './warehousingUploadDialog'
import warehousingSaveDialog from './warehousingSaveDialog'
import outOfStockSaveDialog from './outOfStockSaveDialog'
import visibleApplicationDialog from './visibleApplicationDialog'

export default {
  name: 'publicityInventoryManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    visibleApplicationDialog, // 可见申请方式弹窗
    materialsSaveDialog,
    categoryDetailDialog,
    warehousingUploadDialog,
    warehousingSaveDialog,
    outOfStockSaveDialog
  },
  props: [],
  mounted () {
    this.handleSearch()
    this.getCategoryList()
  },
  watch: {},
  computed: {},
  data () {
    return {
      form: {
        name: '',
        categoryId: ''
      },
      submitForm: {
        name: '',
        categoryId: ''
      },
      selectedRow: new Map(),
      materialCategoryList: [],
      materialsSaveDialogVisible: false, // 物料弹框
      materialsSaveDialogData: {},
      categoryDetailDialogVisible: false, // 类别管理弹框
      categoryDetailDialogData: {},
      warehousingSaveDialogVisible: false, // 入库清单弹框
      warehousingSaveDialogData: {},
      warehousingUploadDialogVisible: false, // 导入入库文件弹框
      warehousingUploadDialogData: {},
      outOfStockSaveDialogVisible: false, // 出库弹框
      outOfStockSaveDialogData: {},
      applicationData: { // 可见申请方式弹窗
        visible: false,
        id: 0
      }
    }
  },
  methods: {
    getCategoryList () {
      this.$ajax({
        method: 'get',
        url: '/materials/get_category_list',
        data: {
          type: 1
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.materialCategoryList = []
          result.data.forEach(v => {
            this.materialCategoryList.push({
              label: v.name,
              value: v.categoryId
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getData () {
      console.log('getData')
      this.$ajax({
        loadingDom: '.materialInventoryManagement',
        url: '/materials/get_materials_stock_list',
        data: {
          name: this.submitForm.name,
          categoryId: this.submitForm.categoryId,
          type: 1, // 0为物料库存, 1为宣传品库存
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.selectedRow.clear()
          this.tableData = []
          this.totalPage = data.total
          let item = {}
          data.rows.forEach(v => {
            item = {
              fid: v.fid,
              code: v.code,
              name: v.name,
              spec: v.spec,
              version: v.version,
              upDownSize: v.upDownSize,
              unit: v.unit,
              stock: v.stock,
              category: v.category,
              categoryId: v.categoryId,
              ratificationIssue: v.fratificationIssue,
              expirationDate: v.fexpirationDate,
              updater: v.updater,
              updateTime: v.updateTime
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 添加可见申请方式
    handleVisibleApplication () {
      if (this.selectedRow.size === 0) {
        this.$message.error('请选择宣传品物料')
        return
      }
      let id = [...this.selectedRow.values()][0].fid
      this.applicationData.id = id
      this.applicationData.visible = true
    },
    handleSearch () {
      this.submitForm = JSON.parse(JSON.stringify(this.form))
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        name: '',
        categoryId: ''
      }
      this.currentPage = 1
      this.handleSearch()
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row) {
      this.$refs.table.clearSelection()
      this.$refs.table.toggleRowSelection(row, !this.selectedRow.has(row.fid))
      let hasThisId = this.selectedRow.has(row.fid)
      this.selectedRow.clear()
      if (!hasThisId) {
        this.selectedRow.set(row.fid, row)
      }
    },
    handleMaterialsSaveDialogConfirm () {
      this.materialsSaveDialogVisible = false
      this.getData()
    },
    handleSaveMaterial (type) {
      if (type === 0) {
        this.materialsSaveDialogData = {
          fid: null,
          name: '',
          spec: '',
          unit: '',
          version: '',
          stock: 0,
          categoryId: '',
          upDownSize: '',
          type: 1
        }
        this.materialsSaveDialogVisible = true
      } else {
        if (this.selectedRow.size === 1) {
          let row = JSON.parse(JSON.stringify([...this.selectedRow.values()][0]))
          this.materialsSaveDialogData = {
            fid: row.fid,
            code: row.code,
            name: row.name,
            spec: row.spec,
            unit: row.unit,
            version: row.version,
            stock: row.stock,
            categoryId: row.categoryId,
            upDownSize: row.upDownSize,
            ratificationIssue: row.ratificationIssue,
            expirationDate: row.expirationDate,
            type: 1
          }
          this.materialsSaveDialogVisible = true
        } else {
          this.$message.error('请选择一条数据')
        }
      }
    },
    handleDelete () {
      if (this.selectedRow.size === 1) {
        this.$confirm(`是否移除该物料信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$ajax({
            // loadingDom: '.configurationTable',
            url: '/materials/delete_materials',
            method: 'get',
            data: {
              fid: [...this.selectedRow.values()][0].fid
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('删除成功')
              this.handleSearch()
            } else {
              this.$message.error(result.message)
            }
          })
        }).catch(err => {
          console.log(err)
        })
      } else {
        this.$message.error('请选择一条数据')
      }
    },
    handleCategoryDetail () {
      this.categoryDetailDialogData = {
        type: 1
      }
      this.categoryDetailDialogVisible = true
    },
    handleCategoryDetailDialogConfirm () {
      this.categoryDetailDialogVisible = false
      this.getData()
    },
    handleWarehousing (type) {
      if (type === 1) {
        this.warehousingSaveDialogData = {
          tableData: [],
          type: 1
        }
        this.warehousingSaveDialogVisible = true
      } else {
        this.warehousingUploadDialogVisible = true
      }
    },
    handleOutOfStock () {
      this.outOfStockSaveDialogData = {
        type: 1
      }
      this.outOfStockSaveDialogVisible = true
    },
    handleWarehousingSaveDialogConfirm () {
      this.warehousingSaveDialogVisible = false
      this.getData()
    },
    handleOutOfStockSaveDialogConfirm () {
      this.outOfStockSaveDialogVisible = false
      this.getData()
    },
    handleWarehousingUploadDialogConfirm (data) {
      this.warehousingUploadDialogVisible = false
      this.warehousingSaveDialogData = {
        tableData: data,
        type: 1
      }
      this.warehousingSaveDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
  .search{
    height: 48px;
  }
  .search >>>.el-form-item--mini{
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .content{
    height: calc(100% - 48px);
    .buttonGroup{
      height: 40px;
      line-height: 40px;
    }
    .table{
      height: calc(100% - 50px);
    }
    .table >>>.el-table__header .el-checkbox {
      display: none;
    }
  }
</style>
