<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="600px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="fileList">
          <el-upload
            ref="upload"
            :data="{isCover: true}"
            :auto-upload="false"
            :action="action"
            :limit="1"
            :file-list="form.fileList"
            :on-success="handleOnSuccess"
            :before-upload="handleBeforeUpload"
            drag
            style="width: 100%"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">仅支持Excel文件
<!--              <el-button type="text" style="margin-left: 100px;" @click="handleDownload">探针储位模板下载</el-button>-->
            </div>
          </el-upload>
        </el-form-item>

      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util from '../../../util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  data () {
    let validateFile = (rule, value, callback) => {
      if (this.$refs.upload.uploadFiles.length > 0) {
        callback()
      } else {
        callback(new Error('请选择文件'))
      }
    }
    return {
      title: '探针储位上传',
      loading: false,
      action: constants.JS_CONTEXT + '/system/probe/import_probe_storage_location',
      form: {
        fileList: []
      },
      supplierList: ['伯科', '吉因加'],
      rules: {
        fileList: [
          {validator: validateFile, trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    // 保存配置
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          let file = this.$refs.upload.uploadFiles[0].raw
          if (!await this.handleBeforeUpload(file)) {
            this.$refs.upload.clearFiles()
            this.loading = false
            return
          }

          this.$ajax({
            url: '/system/probe/import_probe_storage_location',
            isFormData: true,
            data: {isCover: true, file: this.$refs.upload.uploadFiles[0].raw}
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
          // this.$refs.upload.submit()
        }
      })
    },
    // 校验是否上传过文件
    async handleCheck (file) {
      let result = await this.$ajax({
        url: '/system/probe/import_probe_storage_before_check',
        data: {
          file: file
        },
        isFormData: true
      })
      if (result.code === this.SUCCESS_CODE) {
        if (result.message !== '检查成功') {
          this.loading = false
          await this.$confirm(`探针${result.message}已存在探针储位，是否需要覆盖更新`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          this.loading = true
          return true
        }
        return true
      } else {
        this.$message.error(result.message)
        return false
      }
    },
    // 探针储位模板下载
    handleDownload () {
      this.$ajax({
        url: `/system/probe/download_template`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    handleOnSuccess (res) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('上传成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      } else {
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    // 上传前校验文件名和文件大小
    async handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 500) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过500M，无法上传`)
          this.$refs.upload.abort(file)
          return false
        } else {
          const result = await this.handleCheck(file)
          console.log(result)
          if (!result) this.$refs.upload.abort(file)
          return result
        }
      } else {
        this.loading = false
        this.$message.error('只能上传Excel文件')
        this.$refs.upload.abort(file)
        return false
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-upload-dragger {
  width: 550px !important;
}

</style>
