<template>
  <div>
    <el-dialog
            :visible.sync="visible"
            :close-on-click-modal="false"
            :before-close="handleClose"
            title="选择项目"
            width="60%"
            @open="handleOpen">
      <nav class="operateBar">
        <div>
          <el-select
                  v-model="form.content"
                  size="mini"
                  placeholder="请选择"
                  style="width: 150px;"
                  clearable
                  @clear="handleReset"
                  @change="clearInput">
            <template v-for="(v, k) in searchOptions">
              <el-option :key="k" :label="v.label" :value="k"></el-option>
            </template>
          </el-select>
          <template v-if="form.content === 'fproject_code' || form.content === 'fproject_name'">
            <el-input
                    v-model="form.input"
                    :disabled="!form.content"
                    style="width: 250px;margin: 0 20px;"
                    size="mini"
                    clearable
                    placeholder="请输入"
                    @clear="handleReset"
                    @keyup.enter.native="handleSearch"></el-input>
          </template>
          <template v-else>
            <el-input
                    v-model="form.input"
                    :disabled="!form.content"
                    style="width: 250px;margin: 0 20px;"
                    size="mini"
                    clearable
                    placeholder="请输入"
                    @clear="handleReset"
                    @keyup.enter.native="handleSearch"></el-input>
          </template>
          <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
        </div>
      </nav>
      <div style="padding: 0 20px;">
        <el-table
                ref="table"
                :data="tableData"
                class="table"
                height="400px"
                row-key="id"
                @select="handleSelect"
                @row-click="handleRowClick">
          <el-table-column type="selection" width="50"></el-table-column>
          <el-table-column prop="projectCode"  label="项目编号" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="projectName" label="项目名称" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="customer" label="客户姓名" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="hospital" label="送检单位" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sale" label="销售姓名" width="140" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
                :page-sizes="pageSizes"
                :page-size="pageSize"
                :current-page.sync="currentPage"
                :total="totalPage"
                style="background: #ffffff;"
                layout="total, sizes, prev, pager, next, jumper, slot"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
        </el-pagination>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../../util/mixins'
import util from '../../../../../../util/util'
export default {
  name: 'entryBaseInfoProjectDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  data () {
    return {
      selectedRows: new Map(),
      form: {
        content: '',
        input: ''
      },
      formSubmit: {},
      searchOptions: {
        fproject_code: {label: '项目编号'},
        fproject_name: {label: '项目名称'}
      }
    }
  },
  methods: {
    handleOpen () {
      this.tableData = []
      this.selectedRows = new Map()
      this.handleReset()
    },
    getData () {
      let data = {
        projectCode: '',
        projectName: '',
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      if (this.formSubmit.content === 'fproject_code') {
        data.projectCode = this.formSubmit.input
      }
      if (this.formSubmit.content === 'fproject_name') {
        data.projectName = this.formSubmit.input
      }
      this.$ajax({
        url: '/order/get_project_list',
        data: data,
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data
          this.totalPage = data.total
          let rows = data.rows || []
          this.tableData = []
          this.selectedRows = new Map()
          rows.forEach(v => {
            let item = {
              id: v.fid,
              projectName: v.projectName,
              projectCode: v.projectCode,
              customer: v.customerName,
              hospital: v.sendUnit,
              sale: v.saleMan,
              salePhone: v.salePhone,
              saleEmail: v.saleEmail
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleReset () {
      this.form = {
        content: '',
        input: ''
      }
      this.handleSearch()
    },
    // 下拉框发生改变时，清空input
    clearInput () {
      this.form.input = ''
    },
    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData()
    },
    handleConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择项目')
        return
      }
      this.$emit('dialogConfirmEvent', ...this.selectedRows.values())
      this.visible = false
    },
    // 点击行
    handleRowClick (row) {
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      if (!this.selectedRows.has(row.id)) {
        this.$refs.table.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.selectedRows.has(row.id)
        ? this.selectedRows.delete(row.id)
        : this.selectedRows.set(row.id, row)
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-table__header .el-checkbox {
    display: none;
  }
</style>
