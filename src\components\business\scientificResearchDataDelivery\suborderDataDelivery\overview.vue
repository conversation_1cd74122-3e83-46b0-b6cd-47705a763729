<template>
  <div style="height: 100%;">
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="100px" size="mini" @keyup.enter.native="handleSearch">
        <el-form-item label="子订单编号">
          <el-input v-model.trim="form.orderCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="项目编号">
          <el-input v-model.trim="form.projectCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-select v-model="form.sign" clearable placeholder="请选择">
            <el-option label="暂存" value="暂存"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <el-button type="primary" size="mini" :loading="temporaryLoading" @click="handleTemporary">暂存</el-button>
        <el-button type="primary" size="mini" :loading="revocationTemporaryLoading" @click="handleRevocationTemporary">撤销暂存</el-button>
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button type="primary" plain size="mini" @click="handleReset">重置</el-button>
      </div>
      <!--表格-->
      <div>
        <el-table
          ref="table"
          :data="tableData"
          :cell-style="handleRowStyle"
          class="table"
          size="mini"
          border
          style="width: 100%"
          row-key="id"
          height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
          @select="handleSelectTable"
          @row-click="handleRowClick"
          @select-all="handleSelectAll">
          <el-table-column type="selection" width="45" :reserve-selection="true" fixed="left"></el-table-column>
          <el-table-column label="标签" width="80">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.sign !== '-'" style="margin-right: 5px;" size="mini">{{scope.row.sign}}</el-tag>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="子订单编号" width="160" show-overflow-tooltip>
            <template slot-scope="scope">
                <span
                  class="link"
                  @click="handleCheck(scope.row, 2)">
                  {{scope.row.orderCode}}
                </span>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" label="项目名称" min-width="220" show-overflow-tooltip></el-table-column>
          <el-table-column prop="projectCode" label="项目编号" min-width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="offlineSample" label="已下机样本" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deliverFailSample" min-width="140" show-overflow-tooltip>
            <template slot="header" slot-scope="scope">
              已交付/<span class="fail-color">失败</span>/总样本
            </template>
            <template slot-scope="scope">
              <template v-if="scope.row.deliverFailSampleArr !== '-'">
                {{scope.row.deliverFailSampleArr[0]}}/<span class="fail-color">{{scope.row.deliverFailSampleArr[1]}}</span>/{{scope.row.deliverFailSampleArr[2]}}
              </template>
              <span v-else>{{scope.row.deliverFailSampleArr}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="deliverFailTask" min-width="140" show-overflow-tooltip>
            <template slot="header" slot-scope="scope">
              已交付/<span class="fail-color">失败</span>/总任务
            </template>
            <template slot-scope="scope">
              <template v-if="scope.row.deliverFailTaskArr !== '-'">
                {{scope.row.deliverFailTaskArr[0]}}/<span class="fail-color">{{scope.row.deliverFailTaskArr[1]}}</span>/{{scope.row.deliverFailTaskArr[2]}}
              </template>
              <span v-else>{{scope.row.deliverFailTaskArr}}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <span
                v-if="scope.row.isHaveTask === 0"
                class="link"
                @click="handlePreview(scope.row)">
                查看任务
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh">
              <icon-svg icon-class="icon-refresh"/>
            </button>
          </el-pagination>
        </div>
      </div>
    </div>
    <task-list-dialog
      :pvisible="taskListDialogVisible"
      :pdata="taskData"
      @dialogCloseEvent="taskListDialogVisible = false"/>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import taskListDialog from './taskListDialog'
export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    taskListDialog
  },
  props: [],
  mounted () {
    this.handleSearch()
  },
  watch: {},
  computed: {},
  data () {
    return {
      selectedRows: new Map(),
      taskListDialogVisible: false,
      taskData: {},
      temporaryLoading: false,
      revocationTemporaryLoading: false,
      filterList: [
        { text: '暂存', value: '暂存' }
      ],
      form: {
        orderCode: '',
        projectName: '',
        projectCode: '',
        sign: ''
      },
      submitForm: {},
      tableData: []
    }
  },
  methods: {
    // 暂存
    async handleTemporary () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何数据')
        return
      }
      let rows = [...this.selectedRows.values()] || []
      if (rows.some(v => v.sign === '暂存')) {
        this.$message.error('请勾选非暂存数据！')
        return
      }
      await this.$confirm('暂存后不会自动交付，可点击【撤销暂存】系统继续轮询并自动交付该子订单任务。确定继续暂存吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      })
      this.handleRevocation(rows, 'temporaryLoading')
    },
    // 撤销暂存
    async handleRevocationTemporary () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何数据')
        return
      }
      let rows = [...this.selectedRows.values()] || []
      if (rows.some(v => v.sign !== '暂存')) {
        this.$message.error('请勾选暂存数据！')
        return
      }
      await this.$confirm('撤销后系统继续轮询并自动交付该子订单任务。确定继续撤销暂存吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      })
      this.handleRevocation(rows, 'revocationTemporaryLoading')
    },
    // 暂存非暂存接口处理
    handleRevocation (rows, loading) {
      const orderCodes = rows.map(v => v.orderCode)
      const data = {
        sign: loading === 'temporaryLoading' ? '暂存' : '',
        orderCodes: orderCodes
      }
      this[loading] = true
      this.$ajax({
        url: '/order/delivery/staging_or_cancel_storage',
        data: data
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.handleSearch()
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this[loading] = false
      })
    },
    handlePreview (row) {
      this.taskData = row
      this.taskListDialogVisible = true
    },
    // 查看 type 1编辑 2 只读
    handleCheck (row, type) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type, // type 1编辑 2 只读
          orderId: row.id,
          status: 2,
          code: row.orderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (path) util.openNewPage(path)
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row, rowIndex}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff !important', padding: 0, height: '24px'}
      }
      return {padding: 0, height: '24px'}
    },
    // 查询
    handleSearch () {
      this.submitForm = util.deepCopy(this.form)
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    // 重置
    handleReset () {
      this.form = {
        orderCode: '',
        projectName: '',
        projectCode: '',
        sign: ''
      }
      this.handleSearch()
    },
    // 获取表格数据
    getData () {
      this.$ajax({
        url: '/order/delivery/get_sub_order_delivery_data',
        data: {
          productionAreas: util.getSessionInfo('currentLab') || [], // 片区id
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          ...util.deepCopy(this.submitForm)
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.rows || []
          data.forEach((v) => {
            let item = {
              sign: v.sign,
              id: v.subOrderSampleId,
              orderCode: v.orderCode,
              projectName: v.projectName,
              projectCode: v.projectCode,
              offlineSample: v.offlineSample ? v.offlineSample : '',
              deliverFailSample: v.deliverFailSample,
              deliverFailSampleArr: v.deliverFailSample ? v.deliverFailSample.split('/') : '',
              deliverFailTask: v.deliverFailTask,
              deliverFailTaskArr: v.deliverFailTask ? v.deliverFailTask.split('/') : '',
              isHaveTask: v.isHaveTask,
              orderType: v.ftype
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.handleEchoSelect()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .search{
    height: 48px;
  }
  .search >>>.el-form-item--mini{
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .fail-color {
    color: $fail-color
  }
  .content{
    height: calc(100% - 48px);
    .buttonGroup{
      height: 40px;
      line-height: 40px;
    }
    .table{
      height: calc(100% - 50px);
    }
  }
</style>
