<template>
  <fragment>
    <el-submenu v-if="pmenu.children && pmenu.children.length > 0" :index="pmenu.title">
      <template slot="title">
        <icon-svg :icon-class="pmenu.icon" class="icon" style="width: 24px;"/>
        <span slot="title">{{pmenu.title}}</span>
      </template>
      <navigation-menu v-for="(item, index) in pmenu.children" :pmenu="item" :key="index"></navigation-menu>
    </el-submenu>
    <el-menu-item v-else :index="pmenu.url">
      <icon-svg :icon-class="pmenu.icon" style="width: 14px;"/>
      <span slot="title">{{pmenu.title}}</span>
    </el-menu-item>
  </fragment>
</template>

<script>
// import util from '@/util/util'
import FixiOSBug from './FixiOSBug'
export default {
  name: 'navigationMenu',
  mixins: [FixiOSBug],
  props: [
    'pmenu'
  ],
  methods: {
    toPage (path) {
      this.$router.push(path)
    }
  }
}
</script>

<style scoped>
.icon{
  transition: font-size .3s!important;
}
</style>
