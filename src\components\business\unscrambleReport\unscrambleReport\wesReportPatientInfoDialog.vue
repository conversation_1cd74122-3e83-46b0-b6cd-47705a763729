<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="用户信息"
      width="60%">
      <div>
        <el-form ref="form" :model="form" label-width="120px" label-suffix="：" size="mini">
          <el-row>
            <el-col :span="8">
              <el-form-item label="受检者">
                <desensitization :info="form.patientName" type="name" is-detail></desensitization>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年龄">
                {{form.age}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别">
                {{form.sex}}
              </el-form-item>
            </el-col>
            <!--<el-col :span="8">-->
              <!--<el-form-item label="检测癌种">-->
                <!--{{form.detectCancer}}-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col :span="8">
              <el-form-item label="家族史">
                {{form.familyHistory}}
              </el-form-item>
            </el-col>
            <!--<el-col :span="8">-->
              <!--<el-form-item label="信息分析流程">-->
                <!--{{form.pipelineName}}-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="8">-->
              <!--<el-form-item label="芯片号">-->
                <!--{{form.chip}}-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="8">-->
              <!--<el-form-item label="客户名称">-->
                <!--{{form.clientName}}-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col :span="8">
              <el-form-item label="送检日期">
                {{form.sendTime}}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="送检单位">
                {{form.sendUnit}}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger" size="mini" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wesReportPatientInfoDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.form = this.pdata
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      form: {
        patientName: '',
        age: '',
        sex: '',
        familyHistory: '',
        pipelineName: '',
        chip: '',
        clientName: '',
        sendTime: '',
        sendUnit: ''
      }
    }
  },
  methods: {
    handleClose () {
      this.$emit('patientInfoDialogCloseEvent')
    }
  }
}
</script>

<style scoped>

</style>
