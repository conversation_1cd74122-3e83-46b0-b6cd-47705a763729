<template>
  <div>
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini">
        <el-form-item label="患者编号" prop="patientCode">
          <el-input v-model.trim="form.patientCode"></el-input>
        </el-form-item>
        <el-form-item label="患者姓名" prop="patientName">
          <el-input v-model.trim="form.patientName"></el-input>
        </el-form-item>
        <el-form-item label="证件号" prop="idCard">
          <el-input v-model.trim="form.idCard"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('003006001', 'buttons')" :loading="loading" type="primary" size="mini" @click="handleSampleSplit">样本拆分</el-button>
        <el-button v-if="$setAuthority('003006002', 'buttons')" type="primary" plain size="mini" @click="handleDealPatientForce">患者关注点</el-button>
        <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
        <el-button size="mini" @click="handleReset">重置</el-button>
      </div>
      <div>
        <el-table
          ref="table"
          :data="tableData"
          height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
          class="reservationTable"
          style="width: 100%"
          @select="handleSelectTable"
          @row-click="handleRowClick">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="patientCode" label="患者编号" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="name" label="姓名" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.name" type="name"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="sex" label="性别" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="birthday" label="出生年月" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cardType" label="证件类型" width="220" show-overflow-tooltip></el-table-column>
          <el-table-column prop="idCard" label="证件号" width="220" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.idCard" type="idCard"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="national" label="民族" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="times" label="检查次数" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="phone" label="手机号" width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.phone" type="phone"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="familyTel" label="家庭联系电话" width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.familyTel" type="phone"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="province" label="省" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="city" label="市" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="region" label="区" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="address" label="详细地址" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="patientFocus" label="患者关注点" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="patientUpdator" label="患者关注点编辑人" min-width="140"
                           show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <patient-split-sample-dialog
      :pvisible.sync="showSplitSampleDialogVisible"
      :table-data="splitSampleTableData"
      @dialogConfirm="handlePatientSplitSampleCodeDialogConfirm"
    />
    <edit-patient-force-dialog
      :pvisible.sync="showPatientFocusDialogVisible"
      :patient-code="patientCode"
      :patient-focus="patientFocus"
      @dialogConfirm="getData"
    />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import patientSplitSampleDialog from '../patientSplitSampleDialog.vue'
import EditPatientForceDialog from './components/editPatientForceDialog.vue'

export default {
  name: 'patientInfoDetail',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    EditPatientForceDialog,
    patientSplitSampleDialog
  },
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        patientCode: '',
        patientName: '',
        idCard: ''
      },
      tableHeight: 0,
      loading: false,
      showPatientFocusDialogVisible: false,
      patientCode: '',
      patientFocus: '', // 患者关注点
      formSubmit: {}, // 提交的form
      splitSampleTableData: [],
      showSplitSampleDialogVisible: false
    }
  },
  methods: {
    $_setTbHeight () {
      // let h1 = document.documentElement.clientHeight - 1
      this.tableHeight = this.zoomVh - 64 - 50 - 20 - 60 - 45 - 42 - 20
    },
    getData () {
      const types = {
        0: '其他',
        1: '居民身份证',
        2: '护照',
        3: '军官证',
        4: '港澳通行证',
        5: '社保卡'
      }
      this.$ajax({
        url: '/sample/patient/get_patient_list',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {
            patientID: this.formSubmit.patientCode,
            name: this.formSubmit.patientName,
            idCard: this.formSubmit.idCard
          }
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              patientCode: v.patientID,
              name: v.name,
              sex: v.sex === 1 ? '女' : v.sex === 0 ? '男' : null,
              birthday: v.birthday,
              cardType: types[v.cardType],
              idCard: v.idcard,
              national: v.nation,
              times: v.detectionTimes,
              phone: v.contactTel,
              familyTel: v.familyContactType,
              province: v.province,
              city: v.city,
              region: v.region,
              address: v.contactAddr,
              patientUpdator: v.fpatientUpdator,
              patientFocus: v.fpatientFocus
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 没有功能说明和原型
    handleSampleSplit () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要进行拆分的样例')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.loading = true
      this.$ajax({
        url: '/sample/patient/split_patient_id',
        data: {
          patientId: row.patientCode,
          status: 1
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.splitSampleTableData = []
          let data = res.data || []
          // if (data.length === 0) {
          //   this.$message.error('患者信息较少,无需拆分')
          //   return
          // }
          data.forEach(item => {
            let v = {
              id: item.sampleBasicId,
              patientCode: item.patientID,
              sampleCode: item.sampleNum,
              name: item.name,
              idCard: item.idCard,
              sex: item.sex === 1 ? '女' : item.sex === 0 ? '男' : null
            }
            v.realData = util.deepCopy(v)
            util.setDefaultEmptyValueForObject(v)
            this.splitSampleTableData.push(v)
          })
          this.showSplitSampleDialogVisible = true
        } else {
          console.log(res)
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 患者关注点
    async handleDealPatientForce () {
      // 选择一条数据
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0] || {}
      // 获取患者编号，关注点
      this.patientCode = row.realData.patientCode
      this.patientFocus = row.realData.patientFocus
      // 判断关注点是否为空
      if (this.patientFocus) {
        const message = `患者编号：${this.patientCode} 患者关注点已被修改，是否继续编辑`
        await this.$confirm(message, '温馨提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确认',
          type: 'warning'
        })
      }
      this.showPatientFocusDialogVisible = true
    },
    handleSearch () {
      this.formSubmit = {...this.form}
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.$refs.form.resetFields()
      this.handleSearch()
    },
    // 组件确认弹窗
    handlePatientSplitSampleCodeDialogConfirm () {
      this.getData()
    },
    // 点击行
    handleRowClick (row) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (!this.selectedRows.has(row.patientCode)) {
        this.$refs.table.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.patientCode))
      this.selectedRows.has(row.patientCode)
        ? this.selectedRows.delete(row.patientCode)
        : this.selectedRows.set(row.patientCode, row)
    }
  }
}
</script>

<style scoped lang="scss">
.search {
  background-color: #ffffff;
  height: 60px;
  display: flex;
  align-items: center;

  /deep/ .el-form-item {
    margin-bottom: 0;
  }
}

.content {
  background-color: #ffffff;

  .buttonGroup {
    height: 45px;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }
}

> > > .el-pagination {
  padding: 7px 2em;
}

/deep/ .el-table__header .el-checkbox {
  display: none;
}
</style>
