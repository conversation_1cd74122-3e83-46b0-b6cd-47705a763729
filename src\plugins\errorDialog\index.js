import ExportDialogPlugin from './index.vue'

const ErrorDialog = {}

ErrorDialog.install = function (Vue) {
  let Dialogcom = Vue.extend(ExportDialogPlugin)
  let instance = new Dialogcom()
  Vue.prototype.$showErrorDialog = function ({tableData = []}) {
    instance.visible = true
    instance.tableData = tableData
  }
  instance.$mount(document.createElement('div'))
  document.body.appendChild(instance.$el)
}

export default ErrorDialog
