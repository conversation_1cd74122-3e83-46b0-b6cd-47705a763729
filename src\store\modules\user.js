import Cookies from 'js-cookie'
import { getSessionInfo, setSessionInfo, removeSessionInfo } from 'Util'

const userInfo = getSessionInfo('userInfo') || {
  id: '',
  account: '',
  department: '',
  departmentId: '',
  name: '',
  avatar: '',
  pwdOverdueTime: '', // 密码过期时间
  needResetPwd: false
}

const menuList = getSessionInfo('menuList') || []
const pathList = getSessionInfo('pathList') || []
const btnList = getSessionInfo('btnList') || []

const state = {
  token: Cookies.get('token'),
  userInfo: userInfo,
  menuList: menuList, // 菜单列表
  pathList: pathList, // 所有page url的集合
  btnList: btnList // 所有按钮code的集合
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    Cookies.set('token', token)
  },
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
    setSessionInfo('userInfo', userInfo)
  },
  REMOVE_TOKEN (state) {
    state.token = ''
    Cookies.remove('token')
  },
  REMOVE_USER_INFO (state) {
    state.userInfo = {
      id: '',
      account: '',
      department: '',
      departmentId: '',
      name: '',
      avatar: '',
      pwdOverdueTime: '', // 密码过期时间
      needResetPwd: false
    }
    removeSessionInfo('userInfo')
  },
  SET_RESOURCE (state, { menuList, pathList, btnList }) {
    state.menuList = menuList
    state.pathList = pathList
    state.btnList = btnList
    setSessionInfo('menuList', menuList)
    setSessionInfo('pathList', pathList)
    setSessionInfo('btnList', btnList)
  },
  REMOVE_RESOURCE (state) {
    state.menuList = []
    state.pathList = []
    state.btnList = []
    removeSessionInfo('menuList')
    removeSessionInfo('pathList')
    removeSessionInfo('btnList')
  }
}

const actions = {
  setLoginData ({ commit }, { userInfo, token, resources = {} }) {
    return new Promise(resolve => {
      commit('SET_USER_INFO', userInfo)
      commit('SET_TOKEN', token)
      commit('SET_RESOURCE', resources)
      resolve()
    })
  },
  loginOut ({ commit }) {
    return new Promise(resolve => {
      commit('REMOVE_TOKEN')
      commit('REMOVE_USER_INFO')
      commit('REMOVE_RESOURCE')
      resolve()
    })
  }
}

export default {
  state,
  mutations,
  actions
}
