<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose" title="修改步骤配置"
      width="600px">
      <div>
        <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="140px">
          <el-form-item label="工序名称" prop="stepName">
            <el-input v-model="form.stepName" disabled placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="工序分类" prop="stepType">
            <el-input v-model="form.stepTypeName" disabled placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="送样类型" prop="sendSampleType">
            <el-select v-model="form.sendSampleType" multiple clearable placeholder="请选择" style="width: 100%;">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in sampleTypeList">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="样本类型" prop="sampleType">
            <el-select v-model="form.sampleType" multiple clearable placeholder="请选择" style="width: 100%;">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in sampleTypeList">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工序可用样本类型" prop="useSampleType">
            <el-select v-model="form.useSampleType" multiple clearable placeholder="请选择" style="width: 100%;">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in sampleTypeList">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="TAT时间" prop="tatTime">
            <el-input v-model="form.tatTime" clearable placeholder="请选择" style="width: 100%;"></el-input>
          </el-form-item>
          <el-form-item label="是否继承" prop="isInherit">
            <el-select v-model="form.isInherit" clearable placeholder="请选择" style="width: 100%;">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in isInheritList">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'processStepManagementSaveDialog',
  components: {},
  props: {
    pvisible: {
      type: Boolean,
      default: false,
      required: true
    },
    pdata: {
      type: Object,
      default: function () {
        return {
          stepCode: '',
          stepName: '',
          stepType: '',
          stepTypeName: '',
          sendSampleType: [],
          sampleType: [],
          useSampleType: [],
          tatTime: '',
          isInherit: 1
        }
      },
      required: true
    }
  },
  mounted () {
  },
  watch: {
    pvisible (val) {
      this.visible = val
      if (val) {
        this.form = Object.assign({}, this.form, this.pdata)
        this.getSampleType()
      } else {
        this.form = {
          stepCode: '',
          stepName: '',
          stepType: '',
          stepTypeName: '',
          sendSampleType: [],
          sampleType: [],
          useSampleType: [],
          tatTime: '',
          isInherit: 1
        }
      }
    }
  },
  computed: {},
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      form: {
        stepCode: '',
        stepName: '',
        stepType: '',
        stepTypeName: '',
        sendSampleType: [],
        sampleType: [],
        useSampleType: [],
        tatTime: '',
        isInherit: 1
      },
      sampleTypeList: [],
      isInheritList: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      rules: {
        stepName: [],
        stepTypeName: [],
        sendSampleType: [
          {required: true, message: '请选择送样类型', trigger: ['blur', 'change']}
        ],
        sampleType: [
          {required: true, message: '请选择样本类型', trigger: ['blur', 'change']}
        ],
        useSampleType: [
          {required: true, message: '请选择可用样本类型', trigger: ['blur', 'change']}
        ],
        tatTime: [
          {required: true, message: '请选择TAT时间', trigger: 'blur'}
        ],
        isInherit: [
          {required: true, message: '请选择样是否继承', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    getSampleType () {
      this.$ajax({
        url: '/system/procedure/list_sample_type',
        data: {}
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.sampleTypeList = []
          result.data.forEach(v => {
            this.sampleTypeList.push({
              label: v.dictValue,
              value: v.dictUniqueVal
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleClose () {
      this.$emit('processStepManagementSaveDialogCloseEvent')
      this.form = {
        stepCode: '',
        stepName: '',
        stepType: '',
        stepTypeName: '',
        sendSampleType: [],
        sampleType: [],
        useSampleType: [],
        tatTime: '',
        isInherit: 1
      }
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = {
            stepCode: this.form.stepCode,
            sendSampleType: this.form.sendSampleType.join(','),
            sendSampleTypeName: this.handleFindLabel(this.form.sendSampleType),
            sampleType: this.form.sampleType.join(','),
            sampleTypeName: this.handleFindLabel(this.form.sampleType),
            useSampleType: this.form.useSampleType.join(','),
            useSampleTypeName: this.handleFindLabel(this.form.useSampleType),
            tatTime: this.form.tatTime,
            isInherit: this.form.isInherit
          }
          this.loading = true
          this.$ajax({
            url: '/system/procedure/edit_step_data',
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('processStepManagementSaveDialogConfirmEvent')
              this.form = {
                stepCode: '',
                stepName: '',
                stepType: '',
                stepTypeName: '',
                sendSampleType: [],
                sampleType: [],
                useSampleType: [],
                tatTime: '',
                isInherit: 1
              }
              this.$refs.form.resetFields()
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleFindLabel (arr) {
      let labelList = []
      this.sampleTypeList.forEach(v => {
        if (arr.indexOf(v.value) !== -1) {
          labelList.push(v.label)
        }
      })
      return labelList.join(',')
    }
  }
}
</script>

<style scoped>

</style>
