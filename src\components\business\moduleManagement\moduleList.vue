<template>
  <div  style="box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);">
    <div class="search">
      <scroll-pane :scroll-height="40">
        <el-form ref="form" :model="form" :inline="true" label-width="80px"  size="mini">
          <el-form-item label="模块名称">
            <el-input v-model="form.moduleName"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="open">
            <el-select v-model="form.status" clearable>
              <el-option :value="0" label="启用"></el-option>
              <el-option :value="1" label="禁用"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="handleSearch()">查询</el-button>
            <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </scroll-pane>
    </div>
    <div class="content">
      <div class="buttonGroup">
        <template v-if="$setAuthority('002010004', 'buttons')">
          <el-button type="primary" size="mini" @click="handleAdd(false)">新增模块</el-button>
        </template>
      </div>
      <div>
        <el-table
          ref="table"
          :data="tableData"
          class="template-table"
          height="calc(100vh - 40px - 30px - 12px - 30px - 70px - 45px - 32px)"
          style="width: 100%"
          @select="handleSelectTable"
          @row-click="handleRowClick">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="moduleCode" label="模块编码" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="moduleName" label="模块名称" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <div style="display: flex;align-items: center;">
                <span :style="{background: scope.row.status === 0 ? 'green' : 'red'}" class="point"></span>
                <span>{{scope.row.statusText}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="moduleDirections" label="模块说明" min-width="250" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createInfo" label="创建信息" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="modifyInfo" label="修改信息" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="180">
            <template slot-scope="scope">
              <template v-if="$setAuthority('002010005', 'buttons')">
                <el-button type="text" size="mini" @click.stop="handleAdd(scope.row)">编辑</el-button>
              </template>
              <template v-if="$setAuthority('002010007', 'buttons')">
                <el-button v-if="scope.row.status === 1" type="text" size="mini" style="color: #67C23A" @click.stop="handleSetStatus(scope.row, 0)">启用</el-button>
              </template>
              <template v-if="$setAuthority('002010008', 'buttons')">
                <el-button v-if="scope.row.status === 0" type="text" size="mini" style="color: #E6A23C" @click.stop="handleSetStatus(scope.row, 1)">禁用</el-button>
              </template>
              <template v-if="$setAuthority('002010006', 'buttons')">
                <el-button type="text" size="mini" style="color: red;" @click.stop="handleDelete(scope.row)">删除</el-button>
              </template></template>
          </el-table-column>
        </el-table>
        <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <edit-module-dialog
      :pvisible.sync="editModuleDialogInfo.visible"
      :title="editModuleDialogInfo.title"
      :module-info="editModuleDialogInfo.moduleInfo"
      @dialogConfirmEvent="handleEditModuleDialogConfirm"
    />
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import editModuleDialog from './editModuleDialog'
export default {
  name: 'detectionAppointment',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    editModuleDialog
  },
  props: {
    moduleId: {
      type: String | Number,
      default: null
    }
  },
  mounted () {
  },
  watch: {
    moduleId: {
      handler: function (val) {
        this.tableData = []
        this.form = {
          moduleName: '',
          status: ''
        }
        this.handleSearch(val)
      },
      immediate: true
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        moduleName: '',
        status: ''
      },
      formSubmit: {},
      editModuleDialogInfo: {
        title: '新建模板',
        moduleInfo: null,
        visible: false
      }
    }
  },
  methods: {
    getData (moduleId = this.moduleId) {
      this.$ajax({
        url: '/system/model/search_model',
        data: {
          fmodelCategoryId: moduleId,
          fmodelName: this.formSubmit.moduleName,
          state: this.formSubmit.status,
          page: this.currentPage,
          rows: this.pageSize
        },
        loadingDom: '.template-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.selectedRows.clear()
          let rows = res.data.records || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              moduleCode: v.modelCode,
              moduleName: v.modelName,
              status: v.state,
              statusText: v.state === 0 ? '启用' : v.state === 1 ? '禁用' : '-',
              moduleDirections: v.description,
              createTime: v.createTime,
              creator: v.creator,
              updateTime: v.updateTime,
              modifier: v.modifier,
              moduleType: v.modelType,
              fileType: v.fileType,
              fileName: v.modelPath
            }
            item.realData = {...item}
            util.setDefaultEmptyValueForObject(item)
            item.createInfo = `${item.creator} | ${item.createTime}`
            item.modifyInfo = `${item.modifier} | ${item.updateTime}`
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSearch (val) {
      if (!val) val = this.moduleId
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData(val)
    },
    // 重置
    handleReset () {
      this.form = {moduleName: '', status: ''}
      this.handleSearch()
    },
    // 增加模板
    handleAdd (row) {
      if (row) {
        this.editModuleDialogInfo = {
          title: '编辑模块',
          visible: true
        }
        let data = row.realData
        this.editModuleDialogInfo.moduleInfo = data
      } else {
        this.editModuleDialogInfo = {
          title: '新建模块',
          moduleInfo: null,
          visible: true
        }
      }
    },
    // 删除模板
    handleDelete (row) {
      this.$confirm('此操作将永久删除模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/system/model/delete_model',
          method: 'get',
          data: {
            fid: row.id
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 启用|禁用模板
    handleSetStatus (row, status) {
      this.$ajax({
        url: '/system/model/model_setState',
        method: 'get',
        data: {
          fid: row.id,
          state: status
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 编辑弹窗确认事件
    handleEditModuleDialogConfirm () {
      this.getData()
    },
    // 点击行
    handleRowClick (row) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (!this.selectedRows.has(row.id)) {
        this.$refs.table.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.selectedRows.has(row.id)
        ? this.selectedRows.delete(row.id)
        : this.selectedRows.set(row.id, row)
    }
  }
}
</script>

<style scoped>
  .search{
    background-color: #ffffff;
    height: 60px;
    display: flex;
    align-items: center;
  }
  .search >>>.el-form-item{
    margin-bottom: 0;
  }
  .content{
    background-color: #ffffff;
  }
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }
  >>>.el-pagination{
    padding: 7px 2em;
  }
  /deep/ .el-table__header .el-checkbox {
    display: none;
  }
  .point{
    display: inline-block;
    width: 0.4em;
    height: 0.4em;
    background: black;
    border-radius: 50%;
    margin-right: 3px;
  }
</style>
