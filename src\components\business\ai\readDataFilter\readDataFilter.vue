<template>
  <div class="container">
    <div class="btn">
      <el-button size="mini" type="primary" @click="handleReport">生成在线报告</el-button>
    </div>
    <el-card>
      <div class="content">
        <div class="menu">
          <el-tabs v-model="activeName"
                   tab-position="left"
                   style="height: calc(100vh - 40px - 110px - 32px - 20px)">
            <el-tab-pane name="snv" label="SNV"></el-tab-pane>
            <el-tab-pane name="cnv" label="CNV"></el-tab-pane>
            <el-tab-pane name="drugInfo" label="用药信息"></el-tab-pane>
            <el-tab-pane name="diseaseBackground" label="疾病背景"></el-tab-pane>
            <el-tab-pane name="risk" label="患癌风险"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="wrapper">
          <component :is="activeName"></component>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import snv from './snv'
import cnv from './cnv'
import drugInfo from './drugInfo'
import diseaseBackground from './diseaseBackground'
import risk from './risk'

export default {
  components: {
    snv,
    cnv,
    risk,
    drugInfo,
    diseaseBackground
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      activeName: 'snv'
    }
  },
  methods: {
    async handleReport () {
      const {code, message} = await this.$ajax({
        url: '/read/bigAi/create_online_result',
        loadingDom: '.container',
        data: {
          analysisRsId: this.analysisRsId * 1
        }
      })
      if (code && code === this.SUCCESS_CODE) {
        this.$message.success('生成在线报告成功')
      } else {
        this.$message.error(message)
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-card__body {
  padding: 0;
}
.btn {
  margin: 10px;
}
.content {
  display: flex;
  height: calc(100vh - 40px - 110px - 32px - 20px);
  .menu {
    width: 98px;
  }
  .wrapper {
    width: calc(100% - 98px);
    height: 100%;
    padding: 10px;
  }
}
</style>
