<template>
  <div>
    <h4 class="title">产品目录</h4>
    <div class="wrap tree-container">
      <el-input
        v-model.trim="searchName"
        size="mini"
        placeholder="请输入关键字进行过滤"
        clearable
        class="input-width"></el-input>
      <el-tree
        ref="tree"
        class="tree"
        :data="treeData"
        size="mini"
        show-checkbox
        node-key="productDirectoryId"
        :default-expand-all="true"
        :props="defaultProps"
        @check="handleNodeCheck">
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
        >
        <span
          :id="data.productDirectoryId"
          :class="{ 'highlight': isMatch(data.productDirectoryName) }">
          {{ node.label }}
        </span>
      </span>

      </el-tree>
    </div>
  </div>
</template>

<script>

export default {
  name: 'productCatalogTree',
  mounted () {
    this.handleSearch()
  },
  watch: {
    searchName (val) {
      setTimeout(() => {
        if (val) {
          // 1. 搜索时先折叠所有节点
          this.collapseAll()

          // 2. 查找所有匹配关键字的节点
          const matchNodes = this.findMatchNodes(this.treeData, val)
          if (matchNodes.length) {
            // 3. 展开匹配节点的所有父节点
            matchNodes.forEach(node => {
              this.expandParentNodes(node)
            })

            // 4. 滚动到第一个匹配的节点
            this.$nextTick(() => {
              const node = document.getElementById(matchNodes[0].productDirectoryId)
              if (node) {
                node.scrollIntoView({ behavior: 'smooth', block: 'start' })
              }
            })
          }
        } else {
          // 清空搜索时，清除选中状态并展开所有节点
          this.$refs.tree.setCheckedKeys([])
          this.expandAll()
        }
      }, 100)
    }
  },
  data () {
    return {
      searchName: '',
      treeData: [],
      defaultProps: {
        children: 'productDirectoryChildrenList',
        label: 'productDirectoryName'
      }
    }
  },
  methods: {
    handleSearch (form) {
      if (form) {
        this.form = form
      }
      this.currentPage = 1
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/system/product/get_all_product_directory',
        method: 'get',
        loadingDom: '.tree',
        data: {}
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          /**
           * 树形数据结构
           * {
           *   fid,
           *   productDirectoryName,
           *   fparentId,
           *   childrenList
           * }
           */
          this.treeData = res.data || []
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 节点选中
    handleNodeCheck (data, tree) {
      this.$emit('catalogChange', tree.checkedKeys)
    },
    // 递归查找所有匹配关键字的节点
    findMatchNodes (nodes, keyword) {
      let matches = []
      for (const node of nodes) {
        // 检查当前节点是否匹配
        if (node.productDirectoryName.toLowerCase().indexOf(keyword.toLowerCase()) !== -1) {
          matches.push(node)
        }
        // 递归检查子节点
        if (node.productDirectoryChildrenList && node.productDirectoryChildrenList.length) {
          const childMatches = this.findMatchNodes(node.productDirectoryChildrenList, keyword)
          matches = matches.concat(childMatches)
        }
      }
      return matches
    },
    // 判断节点名称是否匹配搜索关键字
    isMatch (name) {
      if (!this.searchName) return false
      return name.toLowerCase().indexOf(this.searchName.toLowerCase()) !== -1
    },
    // 展开指定节点的所有父节点
    expandParentNodes (node) {
      const parent = this.findParentNode(this.treeData, node.productDirectoryId)
      if (parent) {
        // 展开找到的父节点
        this.$refs.tree.store.nodesMap[parent.productDirectoryId].expanded = true
        // 递归展开更上层的父节点
        this.expandParentNodes(parent)
      }
    },
    // 递归查找指定节点的父节点
    findParentNode (nodes, id, parent = null) {
      for (const node of nodes) {
        if (node.productDirectoryId === id) {
          return parent
        }
        if (node.productDirectoryChildrenList && node.productDirectoryChildrenList.length) {
          const found = this.findParentNode(node.productDirectoryChildrenList, id, node)
          if (found) return found
        }
      }
      return null
    },
    // 折叠树的所有节点
    collapseAll () {
      Object.values(this.$refs.tree.store.nodesMap).forEach(node => {
        node.expanded = false
      })
    },
    // 展开树的所有节点
    expandAll () {
      Object.values(this.$refs.tree.store.nodesMap).forEach(node => {
        node.expanded = true
      })
    }
  }
}
</script>

<style scoped lang="scss">
.wrap{
  padding: 10px;
}
.title{
  line-height: 40px;
  background: #eff2fb;
  padding: 0 10px;
  border-radius: 5px;
}
.tree {
  height: calc(100vh - 40px - 30px - 24px - 40px - 16px - 20px - 32px);
  overflow-y: auto;
}
/deep/ .el-tree {
  width: 100%;
}

/deep/ .el-tree > .el-tree-node {
  display: inline-block;
  min-width: 100%;
}

.highlight {
  background-color: #e6f1fc;
  padding: 5px;
  border-radius: 2px;
}
// 设置树形组件节点的定位和左内边距
.tree-container /deep/ .el-tree-node {
  position: relative;
  padding-left: 13px;
}

// 设置树形组件节点的 before 伪类的样式
.tree-container /deep/ .el-tree-node:before {
  width: 1px;
  height: 100%;
  content: '';
  position: absolute;
  top: -38px;
  bottom: 0;
  left: 0;
  right: auto;
  border-width: 1px;
  border-left: 1px solid #b8b9bb;
}

// 设置树形组件节点的 after 伪类的样式
.tree-container /deep/ .el-tree-node:after {
  width: 13px;
  height: 13px;
  content: '';
  position: absolute;
  left: 0;
  right: auto;
  top: 12px;
  bottom: auto;
  border-width: 1px;
  border-top: 1px solid #b8b9bb;
}

// 设置树形组件首节点的左边框不显示
.tree-container /deep/ .el-tree > .el-tree-node:before {
  border-left: none;
}

// 设置树形组件首节点的顶部边框不显示
.tree-container /deep/ .el-tree > .el-tree-node:after {
  border-top: none;
}

// 设置树形组件末节点的 before 伪类的高度
.tree-container /deep/ .el-tree .el-tree-node:last-child:before {
  height: 50px;
}

// 设置树形组件节点字体大小、以及取消左内边距
.tree-container /deep/ .el-tree .el-tree-node__content {
  color: #000;
  font-size: 14px;
  padding-left: 0 !important;
}

// 设置树形组件孩子节点左内边距
.tree-container /deep/ .el-tree .el-tree-node__children {
  padding-left: 11.5px;
}

// 设置树形组件复选框左右外边距
.tree-container /deep/ .el-tree .el-tree-node__content > label.el-checkbox {
  margin: 0 5px 0 5px !important;
}

// 设置树形组件展开图标定位、图层、内边距
.tree-container /deep/ .el-tree .el-tree-node__expand-icon {
  position: relative;
  z-index: 99;
}

// 设置树形组件叶子节点的默认图标不显示
.tree-container /deep/ .el-tree .el-tree-node__expand-icon.is-leaf {
  display: none;
}

// 设置树形组件叶子节点的横线
.tree-container /deep/ .el-tree .leaf-node-line {
  width: 23px;
  height: 13px;
  content: '';
  position: absolute;
  left: 13px;
  right: auto;
  top: 12px;
  bottom: auto;
  border-width: 1px;
  border-top: 1px solid #b8b9bb;
}

// 设置树形组件有叶子节点的左外边距
.tree-container /deep/ .el-tree .el-tree-node__content:has(.is-leaf){
  // color: aqua;
  margin-left: 24px !important;
}
</style>
