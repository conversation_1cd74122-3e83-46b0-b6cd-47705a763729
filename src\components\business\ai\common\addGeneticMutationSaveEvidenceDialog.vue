<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      title="证据等级"
      width="30%">
      <el-form ref="form" :model="form" label-width="80px" size="mini">
        <template v-for="(item, index) in form.evidenceList">
          <el-form-item :key="index" :prop="`evidenceList.${index}.evidence`" :rules="evidenceRules" label="证据等级">
            <el-select v-model="item.evidence" placeholder="请选择">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in evidenceOptionsList">
              </el-option>
            </el-select>
            <el-button v-if="index === form.evidenceList.length - 1" type="primary" size="mini" @click="handleAddEvidence">新增</el-button>
            <el-button type="primary" size="mini" @click="handleDeleteEvidence(index)">删除</el-button>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'addGeneticMutationSaveEvidenceDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.getEvidenceList(this.pdata)
      } else {
        this.form.evidenceList = [{evidence: ''}]
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      form: {
        evidenceList: [
          {
            evidence: ''
          }
        ]
      },
      evidenceRules: [
        {required: true, message: '请选择证据等级', trigger: ['change', 'blur']}
      ],
      evidenceOptionsList: [
        {
          label: 'PVS1',
          value: 'PVS1'
        },
        {
          label: 'PS1',
          value: 'PS1'
        },
        {
          label: 'PS2',
          value: 'PS2'
        },
        {
          label: 'PS2_Very Strong',
          value: 'PS2_Very Strong'
        },
        {
          label: 'PS3',
          value: 'PS3'
        },
        {
          label: 'PS3_Moderate',
          value: 'PS3_Moderate'
        },
        {
          label: 'PS3_Supporting',
          value: 'PS3_Supporting'
        },
        {
          label: 'PS4',
          value: 'PS4'
        },
        {
          label: 'PS4_Very Strong',
          value: 'PS4_Very Strong'
        },
        {
          label: 'PS4_Moderate',
          value: 'PS4_Moderate'
        },
        {
          label: 'PS4_Supporting',
          value: 'PS4_Supporting'
        },
        {
          label: 'PM1',
          value: 'PM1'
        },
        {
          label: 'PM2',
          value: 'PM2'
        },
        {
          label: 'PM3',
          value: 'PM3'
        },
        {
          label: 'PM4',
          value: 'PM4'
        },
        {
          label: 'PM5',
          value: 'PM5'
        },
        {
          label: 'PM6',
          value: 'PM6'
        },
        {
          label: 'PP1',
          value: 'PP1'
        },
        {
          label: 'PP1_Very Strong',
          value: 'PP1_Very Strong'
        },
        {
          label: 'PP1_Strong',
          value: 'PP1_Strong'
        },
        {
          label: 'PP1_Moderate',
          value: 'PP1_Moderate'
        },
        {
          label: 'PP2',
          value: 'PP2'
        },
        {
          label: 'PP3',
          value: 'PP3'
        },
        {
          label: 'PP4',
          value: 'PP4'
        },
        {
          label: 'PP5',
          value: 'PP5'
        },
        {
          label: 'PP6',
          value: 'PP6'
        },
        {
          label: 'BA1',
          value: 'BA1'
        },
        {
          label: 'BS1',
          value: 'BS1'
        },
        {
          label: 'BS2',
          value: 'BS2'
        },
        {
          label: 'BS2_Moderate',
          value: 'BS2_Moderate'
        },
        {
          label: 'BS2_Supporting',
          value: 'BS2_Supporting'
        },
        {
          label: 'BS3',
          value: 'BS3'
        },
        {
          label: 'BS3_Moderate',
          value: 'BS3_Moderate'
        },
        {
          label: 'BS3_Supporting',
          value: 'BS3_Supporting'
        },
        {
          label: 'BS4',
          value: 'BS4'
        },
        {
          label: 'BS4_Moderate',
          value: 'BS4_Moderate'
        },
        {
          label: 'BS4_Supporting',
          value: 'BS4_Supporting'
        },
        {
          label: 'BP1',
          value: 'BP1'
        },
        {
          label: 'BP2',
          value: 'BP2'
        },
        {
          label: 'BP3',
          value: 'BP3'
        },
        {
          label: 'BP4',
          value: 'BP4'
        },
        {
          label: 'BP5',
          value: 'BP5'
        },
        {
          label: 'BP6',
          value: 'BP6'
        },
        {
          label: 'BP7',
          value: 'BP7'
        }
      ]
    }
  },
  methods: {
    getEvidenceList (data) {
      this.form.evidenceList = []
      if (data) {
        data.forEach(v => {
          this.form.evidenceList.push({
            evidence: v
          })
        })
      } else {
        this.handleAddEvidence()
      }
    },
    handleClose () {
      this.$emit('saveEvidenceDialogCloseEvent')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = this.form.evidenceList.map(v => v.evidence)
          this.$emit('saveEvidenceDialogConfirmEvent', data)
          this.$refs.form.resetFields()
        }
      })
    },
    handleAddEvidence () {
      this.form.evidenceList.push({evidence: ''})
    },
    handleDeleteEvidence (index) {
      this.form.evidenceList.splice(index, 1)
      if (this.form.evidenceList.length === 0) {
        this.handleAddEvidence()
      }
    }
  }
}
</script>

<style scoped>

</style>
