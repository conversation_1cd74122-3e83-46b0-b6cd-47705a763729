<template>
  <div>
    <el-dialog
      :title="title"
      :modal="false"
      :visible.sync="visible" :close-on-click-modal="false"
      :width="dialogWidth"
      :before-close="handleClose"
      v-drag-dialog
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="mini" label-suffix=":">
          <template v-if="type === 'operateHis'">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="第几次手术">
                  <el-select v-model="form.times" clearable filterable placeholder="请选择" class="selectWidth">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in timesList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="colHeight">
                <el-form-item label="手术日期">
                  <my-date-picker v-model="form.operateTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手术名称" prop="operateName">
                  <el-input v-model.trim="form.operateName" maxlength="100" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手术病理分型">
                  <el-input v-model.trim="form.pathologicalType" maxlength="50" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="手术病理分期">
                  pT<el-select v-model="form.ptnmPt" clearable placeholder="请输入" style="width: 100px;">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in tList">
                    </el-option>
                  </el-select>
                  N<el-select v-model="form.ptnmN" clearable placeholder="请输入" style="width: 100px;">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in nList">
                    </el-option>
                  </el-select>
                  M<el-select v-model="form.ptnmM" clearable placeholder="请输入" style="width: 100px;">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in mList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="淋巴结转移">
                  <el-select v-model="form.lymphTransfer" clearable placeholder="请选择" style="width: 120px">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in trueOrFalseList">
                    </el-option>
                  </el-select>
                  <el-input v-model="form.lymphNum1" type="number" min="0" placeholder="请输入" style="width: 80px;"></el-input>
                  <el-input v-model="form.lymphNum2" type="number" min="0" placeholder="请输入" style="width: 80px;"></el-input>
                  <el-input v-model.trim="form.lymphRange" placeholder="请输入" maxlength="50" style="width: 120px;"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else-if="type === 'otherapy'">
            <el-row :gutter="10">
             <el-col :span="12">
               <el-form-item label="剂量" label-width="80px">
                 <el-input v-model.trim="form.dosage" maxlength="50" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="部位" label-width="80px" prop="position">
                 <el-input v-model.trim="form.position" maxlength="500" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="疗程" label-width="80px">
                 <el-input v-model.trim="form.course" maxlength="50" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="次数" label-width="80px">
                 <el-input v-model.number="form.times" min="1" max="255" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
             <el-col :span="12" class="colHeight">
               <el-form-item label="起始时间" label-width="80px">
                 <my-date-picker v-model="form.startTime"></my-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="12" class="colHeight">
               <el-form-item label="结束时间" label-width="80px" prop="endTime">
                 <my-date-picker v-model="form.endTime"></my-date-picker>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item label="疗效" label-width="80px">
                 <el-select v-model="form.effect" clearable placeholder="请选择" class="selectWidth">
                   <el-option
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                     v-for="item in effectList">
                   </el-option>
                 </el-select>
               </el-form-item>
             </el-col>
             <el-col :span="12" v-if="form.effect === '其他'">
               <el-form-item label="其他疗效" label-width="80px" prop="otherEffect">
                 <el-input v-model.trim.trim="form.otherEffect" maxlength="20" placeholder="请输入"></el-input>
               </el-form-item>
             </el-col>
            </el-row>
          </template>
          <template v-else-if="type === 'drugHistory'">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="方案类型" label-width="90px">
                  <el-select v-model="form.drugPlanType" clearable placeholder="请选择" class="selectWidth">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in drugPlanTypeList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <div class="placeholder"></div>
              </el-col>
              <el-col :span="12" class="colHeight">
                <el-form-item label="起始时间" label-width="90px">
                  <my-date-picker v-model="form.startTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="colHeight">
                <el-form-item label="结束时间" label-width="90px" prop="endTime">
                  <my-date-picker v-model="form.endTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <template v-for="(item, index) in form.planName">
                <el-col :span="12" :key="'name' + index" class="colHeight">
                  <el-form-item :prop="'planName.'+ index + '.value'" :rules="rules.planName" label="方案名称" label-width="90px">
                    <el-input v-model.trim="item.value" clearable placeholder="请输入"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :key="'placeholder' + index" class="colHeight">
                  <el-form-item label-width="0">
                    <el-button v-if="index === form.planName.length - 1" type="primary" icon="el-icon-plus" circle size="mini" @click="handleAdd('drugHistory')"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="handleDelete(index, 'drugHistory')"></el-button>
                  </el-form-item>
                </el-col>
              </template>
              <el-col :span="6">
                <el-form-item label="首次疗效" label-width="90px">
                  <el-select v-model="form.firstEffect" clearable placeholder="请选择" class="selectWidth">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in effectList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="form.firstEffect === '其他'">
                <el-form-item label="首次其他疗效" label-width="110px" prop="otherfirstEffect">
                  <el-input v-model.trim="form.otherfirstEffect" :disabled="form.firstEffect !== '其他'" clearable maxlength="20" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="10" class="colHeight">
                <el-form-item label="评估时间" label-width="90px">
                  <my-date-picker v-model="form.firstEffectTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <div v-if="form.firstEffect !== '其他'" class="placeholder"></div>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最佳疗效" label-width="90px">
                  <el-select v-model="form.bestEffect" clearable placeholder="请选择" class="selectWidth">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in effectList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="form.bestEffect === '其他'">
                <el-form-item label="最佳其他疗效" label-width="110px" prop="otherBestEffect">
                  <el-input v-model.trim="form.otherBestEffect" :disabled="form.bestEffect !== '其他'" clearable maxlength="20" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="10" class="colHeight">
                <el-form-item label="评估时间" label-width="90px">
                  <my-date-picker v-model="form.bestEffectTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <div v-if="form.bestEffect !== '其他'" class="placeholder"></div>
              </el-col>
              <el-col :span="6">
                <el-form-item label="目前疗效" label-width="90px">
                  <el-select v-model="form.effect" clearable placeholder="请选择" class="selectWidth">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in effectList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="form.effect === '其他'">
                <el-form-item label="目前其他疗效" label-width="110px" prop="otherEffect">
                  <el-input v-model.trim="form.otherEffect" :disabled="form.effect !== '其他'" clearable maxlength="20" placeholder="请输入"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="10" class="colHeight">
                <el-form-item label="评估时间" label-width="90px">
                  <my-date-picker v-model="form.effectTime"></my-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <div v-if="form.effect !== '其他'" class="placeholder"></div>
              </el-col>
            </el-row>
          </template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
export default {
  name: 'clinicalInfoManagementTgrSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    myDatePicker
  },
  props: {
    pdata: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    let _this = this
    let endTimeValidator = function (rule, value, callback) {
      if (_this.form.startTime) {
        if (value) {
          if (new Date(value) < new Date(_this.form.startTime)) {
            callback(new Error('结束时间应当晚于开始时间'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      loading: false,
      dialogWidth: '50%',
      title: '',
      type: '',
      form: {},
      rules: {
        operateName: [
          {required: true, message: '请输入手术名称', trigger: 'blur'}
        ],
        position: [
          {required: true, message: '请输入部位', trigger: 'blur'}
        ],
        otherEffect: [
          {required: true, message: '请输入其他疗效', trigger: 'blur'}
        ],
        otherfirstEffect: [
          {required: true, message: '请输入首次其他疗效', trigger: 'blur'}
        ],
        otherBestEffect: [
          {required: true, message: '请输入最佳其他疗效', trigger: 'blur'}
        ],
        planName: [
          {required: true, message: '请输入方案名称', trigger: 'blur'}
        ],
        endTime: [
          {validator: endTimeValidator, trigger: 'change'}
        ]
      },
      timesList: [
        {
          label: 1,
          value: 1
        },
        {
          label: 2,
          value: 2
        },
        {
          label: 3,
          value: 3
        },
        {
          label: 4,
          value: 4
        },
        {
          label: 5,
          value: 5
        },
        {
          label: 6,
          value: 6
        },
        {
          label: 7,
          value: 7
        },
        {
          label: 8,
          value: 8
        },
        {
          label: 9,
          value: 9
        },
        {
          label: 10,
          value: 10
        }
      ],
      tList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: 'is',
          label: 'is'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        },
        {
          value: '4',
          label: '4'
        },
        {
          value: 'X',
          label: 'X'
        }
      ],
      nList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        },
        {
          value: 'X',
          label: 'X'
        }
      ],
      mList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: 'X',
          label: 'X'
        }
      ],
      trueOrFalseList: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 1,
          label: '是'
        }
      ],
      effectList: [
        {
          label: 'PD',
          value: 'PD'
        },
        {
          label: 'SD',
          value: 'SD'
        },
        {
          label: 'PR',
          value: 'PR'
        },
        {
          label: 'CR',
          value: 'CR'
        },
        {
          label: '其他',
          value: '其他'
        }
      ],
      drugPlanTypeList: [
        {
          label: '靶向',
          value: '靶向'
        },
        {
          label: '免疫',
          value: '免疫'
        },
        {
          label: '化疗',
          value: '化疗'
        },
        {
          label: '多靶向抑制剂',
          value: '多靶向抑制剂'
        },
        {
          label: '抗血管药物',
          value: '抗血管药物'
        },
        {
          label: '内分泌',
          value: '内分泌'
        },
        {
          label: '其他',
          value: '其他'
        },
        {
          label: '靶向+免疫',
          value: '靶向+免疫'
        },
        {
          label: '化疗+免疫',
          value: '化疗+免疫'
        },
        {
          label: '化疗+靶向',
          value: '化疗+靶向'
        }
      ],
      hasList: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        this.type = this.pdata.type
        switch (this.pdata.type) {
          case 'operateHis':
            this.title = '手术史'
            this.dialogWidth = '55%'
            this.form = {
              operateHistoryId: null,
              sampleBasicId: '',
              times: '',
              operateYear: '',
              operateMonth: '',
              operateDay: '',
              operateName: '',
              operateTime: '',
              ptnmPt: '',
              ptnmN: '',
              ptnmM: '',
              pathologicalType: '',
              lymphTransfer: '',
              lymphNum1: '',
              lymphNum2: '',
              lymphRange: '',
              isFollowup: '',
              followupYear: '',
              followupMonth: '',
              followupDay: '',
              fupdateTime: ''
            }
            break
          case 'otherapy':
            this.title = '放疗史'
            this.dialogWidth = '55%'
            this.form = {
              radiotherapyId: null,
              sampleBasicId: '',
              dosage: '',
              position: '',
              course: '',
              times: '',
              startYear: '',
              startMonth: '',
              startDay: '',
              startTime: '',
              endYear: '',
              endMonth: '',
              endDay: '',
              endTime: '',
              effect: '',
              otherEffect: '',
              isFollowup: '',
              followupYear: '',
              followupMonth: '',
              followupDay: '',
              fupdateTime: ''
            }
            break
          case 'drugHistory':
            this.title = '用药史'
            this.dialogWidth = '55%'
            this.form = {
              useDrugId: null,
              sampleBasicId: '',
              planName: [{value: ''}],
              treatCourse: '',
              startYear: '',
              startMonth: '',
              startDay: '',
              startTime: '',
              endYear: '',
              endMonth: '',
              endDay: '',
              endTime: '',
              effect: '',
              otherEffect: '',
              formType: '',
              drugPlanType: '',
              effectTime: '',
              bestEffect: '',
              bestEffectTime: '',
              otherBestEffect: '',
              firstEffect: '',
              firstEffectTime: '',
              otherfirstEffect: '',
              isFollowup: '',
              followupYear: '',
              followupMonth: '',
              followupDay: '',
              followupDayTime: '',
              fupdateTime: ''
            }
            break
        }
        this.form = Object.assign({}, this.form, this.pdata)
        this.$refs.form.resetFields()
      })
    },
    handleAdd (type) {
      switch (type) {
        case 'drugHistory':
          this.form.planName.push({
            value: ''
          })
          break
      }
    },
    handleDelete (index, type) {
      switch (type) {
        case 'drugHistory':
          this.form.planName.splice(index, 1)
          if (this.form.planName.length === 0) {
            this.handleAdd('drugHistory')
          }
          break
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let url = ''
          let data = {}
          let operateTime = this.form.operateTime ? this.form.operateTime.split('-') : []
          let followupTime = this.form.followupTime ? this.form.followupTime.split('-') : []
          let startTime = this.form.startTime ? this.form.startTime.split('-') : []
          let endTime = this.form.endTime ? this.form.endTime.split('-') : []
          switch (this.type) {
            case 'operateHis':
              url = '/sample/clinical/save_operate_his'
              data = {
                operateHistoryId: this.form.operateHistoryId,
                sampleBasicId: this.form.sampleBasicId,
                times: this.form.times,
                operateYear: operateTime[0] || '',
                operateMonth: operateTime[1] || '',
                operateDay: operateTime[2] || '',
                operateName: this.form.operateName,
                ptnmPt: this.form.ptnmPt,
                ptnmN: this.form.ptnmN,
                ptnmM: this.form.ptnmM,
                pathologicalType: this.form.pathologicalType,
                lymphTransfer: this.form.lymphTransfer,
                lymphNum1: this.form.lymphNum1,
                lymphNum2: this.form.lymphNum2,
                lymphRange: this.form.lymphRange,
                isFollowup: this.form.isFollowup,
                followupYear: followupTime[0] || '',
                followupMonth: followupTime[1] || '',
                followupDay: followupTime[2] || ''
              }
              break
            case 'otherapy':
              url = '/sample/clinical/save_radiotherapy'
              data = {
                radiotherapyId: this.form.radiotherapyId,
                sampleBasicId: this.form.sampleBasicId,
                dosage: this.form.dosage,
                position: this.form.position,
                course: this.form.course,
                times: this.form.times,
                startYear: startTime[0] || '',
                startMonth: startTime[1] || '',
                startDay: startTime[2] || '',
                endYear: endTime[0] || '',
                endMonth: endTime[1] || '',
                endDay: endTime[2] || '',
                effect: this.form.effect,
                otherEffect: this.form.otherEffect,
                isFollowup: this.form.isFollowup,
                followupYear: followupTime[0] || '',
                followupMonth: followupTime[1] || '',
                followupDay: followupTime[2] || ''
              }
              break
            case 'drugHistory':
              url = '/sample/clinical/save_use_drug'
              data = {
                useDrugId: this.form.useDrugId,
                sampleBasicId: this.form.sampleBasicId,
                planName: this.form.planName.map(v => v.value).toString(),
                treatCourse: this.form.treatCourse,
                startYear: startTime[0] || '',
                startMonth: startTime[1] || '',
                startDay: startTime[2] || '',
                endYear: endTime[0] || '',
                endMonth: endTime[1] || '',
                endDay: endTime[2] || '',
                effect: this.form.effect,
                otherEffect: this.form.otherEffect,
                type: 1,
                drugPlanType: this.form.drugPlanType,
                effectTime: this.form.effectTime,
                bestEffect: this.form.bestEffect,
                bestEffectTime: this.form.bestEffectTime,
                otherBestEffect: this.form.otherBestEffect,
                firstEffect: this.form.firstEffect,
                firstEffectTime: this.form.firstEffectTime,
                otherfirstEffect: this.form.otherfirstEffect,
                isFollowup: this.form.isFollowup,
                followupYear: followupTime[0] || '',
                followupMonth: followupTime[1] || '',
                followupDay: followupTime[2] || ''
              }
              break
          }
          this.$ajax({
            url: url,
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('treatmentSituationSaveDialogConfirmEvent', this.type)
            } else {
              this.$message.error(result.messsage)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  .selectWidth{
    width: 100%;
  }
  .placeholder{
    height: 51px;
    width: 100%;
  }
  .colHeight{
    height: 51px;
  }
</style>
