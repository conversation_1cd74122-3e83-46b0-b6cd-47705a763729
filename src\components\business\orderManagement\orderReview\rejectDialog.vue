<template>
  <div>
    <el-dialog
            :visible.sync="visible"
            :close-on-click-modal="false"
            :before-close="handleClose"
            title="驳回订单"
            width="600px"
            @open="handleOpen">
      <el-form>
        <el-form-item label="驳回理由">
          <el-input v-model="reason" type="textarea" placeholder="请输入驳回理由"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
export default {
  name: 'rejectDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      reason: ''
    }
  },
  methods: {
    handleOpen () {
      this.reason = ''
    },
    handleConfirm () {
      if (!this.reason) {
        this.$message.error('请输入驳回理由')
        return
      }
      this.$emit('dialogConfirmEvent', this.reason)
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
