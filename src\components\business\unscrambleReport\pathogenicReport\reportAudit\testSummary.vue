<template>
  <div>
    <el-table
      ref="table"
      :data="tableData"
      class="dataFilterTable"
    >
      <el-table-column align="center" label="微生物检出情况及检出序列数(单位：条)" >
        <el-table-column prop="bacteria" label="细菌" show-overflow-tooltip>
          <template slot-scope="scope">
            <div :key="index" v-for="(item, index) in scope.row.bacteria">
              {{item}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="fungus" label="真菌" show-overflow-tooltip>
          <template slot-scope="scope">
            <div :key="index" v-for="(item, index) in scope.row.fungus">
              {{item}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="virus" label="病毒"  show-overflow-tooltip>
          <template slot-scope="scope">
            <div :key="index" v-for="(item, index) in scope.row.virus">
              {{item}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="parasite" label="寄生虫"  show-overflow-tooltip>
          <template slot-scope="scope">
            <div :key="index" v-for="(item, index) in scope.row.parasite">
              {{item}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="specialPathogens" label="特殊病原体"  show-overflow-tooltip>
          <template slot-scope="scope">
            <div :key="index" v-for="(item, index) in scope.row.specialPathogens">
              {{item}}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../../../util/util'

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    pathogenicType () {
      return this.$store.getters.getValue('pathogenicType')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 获取检测小结列表
    async getData () {
      let {code, data = []} = await this.$ajax({
        url: '/read/pathogen/get_online_report_test_summary',
        data: {
          analysisRsId: this.analysisRsId,
          version: this.pathogenicType
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        data = [data]
        data.forEach(v => {
          let item = {
            bacteria: v.fbacteria,
            fungus: v.ffungus,
            virus: v.fvirus,
            parasite: v.fparasite,
            specialPathogens: v.fspecialPathogens
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped></style>
