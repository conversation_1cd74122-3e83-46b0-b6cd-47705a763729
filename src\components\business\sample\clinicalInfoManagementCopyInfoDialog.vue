<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="请选择复制的样例编号"
      width="800px"
      @open="handleOpen"
    >
      <el-form :model="form" label-width="90px" size="mini" inline>
        <el-form-item label="样例编号">
          <el-input v-model="form.sampleNum"></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getData">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="tableData"
        ref="copyTable"
        class="table"
        height="300px"
        style="width: 100%;"
        @select="handleSelectTable"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45"></el-table-column>
        <el-table-column prop="sampleNum" label="样本编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="proName" label="产品/项目编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="confirmDate" label="到样日期" min-width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleCopy">确认复制</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'clinicalInfoManagementCopyInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleNum: {
      type: String
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      tableData: [],
      loading: false,
      form: {
        name: '',
        sampleNum: ''
      }
    }
  },
  methods: {
    handleOpen () {
      this.tableData = []
      this.selectedRows.clear()
      this.form = {
        name: '',
        sampleNum: ''
      }
    },
    getData () {
      this.$ajax({
        url: '/sample/clinical/get_sample_infos',
        data: {
          sampleNum: this.form.sampleNum,
          name: this.form.name
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = res.data || []
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleCopy () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择目标样本')
        return
      }
      let id = [...this.selectedRows.keys()][0]
      this.loading = true
      this.$ajax({
        url: '/sample/clinical/copy_clinical_info',
        data: {
          sourceId: id,
          targetId: this.sampleNum
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('复制成功')
          this.$emit('dialogConfirm')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 点击行
    handleRowClick (row) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (!this.selectedRows.has(row.sampleBasicId)) {
        this.$refs.copyTable.clearSelection()
        this.selectedRows.clear()
      }
      this.$refs.copyTable.toggleRowSelection(row, !this.selectedRows.has(row.sampleBasicId))
      this.selectedRows.has(row.sampleBasicId)
        ? this.selectedRows.delete(row.sampleBasicId)
        : this.selectedRows.set(row.sampleBasicId, row)
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-table__header .el-checkbox {
    display: none;
  }
</style>
