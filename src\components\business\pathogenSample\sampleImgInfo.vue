<template>
  <div>
    <div class="title">图片信息</div>
    <el-tabs v-model="imgType" @tab-click="handleTabChange">
      <el-tab-pane :label="`送检单${getImgIndexAndLength('sampleOrderImg')}`" name="sampleOrderImg"></el-tab-pane>
      <el-tab-pane :label="`临床资料${getImgIndexAndLength('imgNames')}`" name="imgNames"></el-tab-pane>
    </el-tabs>
    <div v-if="imgType !== 'fixInfo'">
      <el-button size="mini" @click="handlePreviousPicture">上一张</el-button>
      <el-button size="mini" @click="handleNextPicture">下一张</el-button>
      <!--<el-button size="mini" @click="handleMagnify">放大</el-button>-->
      <!--<el-button size="mini" @click="handleShrink">缩小</el-button>-->
      <!--<el-button size="mini" @click="handleRotate">旋转</el-button>-->
      <el-button size="mini" @click="handleImageAction('zoomIn')">放大 <i class="el-icon-zoom-in"></i></el-button>
      <el-button size="mini" @click="handleImageAction('zoomOut')">缩小<i class="el-icon-zoom-out"></i></el-button>
      <el-button size="mini" @click="handleImageAction('anticlocelise')">旋转<i class="el-icon-refresh-left"></i></el-button>
      <el-button size="mini" @click="handleImageReset">还原<i class="el-icon-full-screen"></i></el-button>
      <el-button v-if="!isDisabled && imgType !== 'supplementaryImgs' && imgType !== 'followupImgNames'" size="mini" @click="handleEditPicture">编辑</el-button>
    </div>
    <div v-if="imgType !== 'fixInfo'" class="picture">
      <!--<el-image :src="imgSrc" :preview-src-list="imgSrcList">-->
      <!--<div slot="placeholder" class="imageSlot">-->
      <!--加载中<span class="dot">...</span>-->
      <!--</div>-->
      <!--&lt;!&ndash;<div slot="error" class="imageSlot">&ndash;&gt;-->
      <!--&lt;!&ndash;<i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;</div>&ndash;&gt;-->
      <!--</el-image>-->
      <div v-if="imgSrc" class="image">
        <template v-if="imgSrc.indexOf('.pdf') !== -1">
          <div style="width: 100%; height: 100%;display: flex; justify-content: center;align-items: center;">
            <div>
              <icon-svg icon-class="icon-pdf" style="font-size: 150px;color: #409EFF;"></icon-svg>
              <div style="text-align: center;">
                <el-button type="text" size="mini" @click="handleViewPdf(imgSrc)">点击查看PDF文件</el-button>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div
            ref="imgDiv"
            v-loading="imgLoading"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)">
            <img
              ref="img"
              :src="imgSrc"
              :style="imgStyle"
              alt=""
              style="display: block;object-fit: contain"
              @load="handleImgLoad"
              @error="handleImgError">
            <!--<img :src="imgSrc" alt="" ref="img" @load="handleImgLoad" :style="`transform: rotate(${deg}deg);`">-->
          </div>
        </template>
      </div>
    </div>
    <div v-if="imgType === 'fixInfo'">
      <el-table
        ref="table"
        :data="supplementaryNote"
        :height="400"
        style="width: 100%;"
      >
        <el-table-column type="index"></el-table-column>
        <el-table-column prop="date"></el-table-column>
        <el-table-column prop="content"></el-table-column>
        <el-table-column>
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === 1" size="mini"  @click="handelStatus(0,scope.$index)">已处理</el-button>
            <el-button v-if="scope.row.status === 0" size="mini" type="primary" @click="handelStatus(1, scope.$index)">未处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

export default {
  name: 'sampleImgInfo',
  components: {
  },
  props: {
    imgType: {
      type: String
    },
    imgSrc: {
      type: String
    },
    imgNames: {
      type: Array
    }, // 临床资料
    sampleOrderImg: {
      type: Array
    }, // 送检单资源
    isDisabled: {
      type: Boolean,
      default: false
    },
    sampleBasicId: {
      type: Number
    }
  },
  mounted () {
  },
  watch: {
    imgSrc () {
      this.$nextTick(_ => {
        const $img = this.$refs.img
        if (!$img.complete) {
          this.imgLoading = true
        }
      })
    }
  },
  computed: {
    imgStyle () {
      const { scale, deg, offsetX, offsetY, enableTransition, maxLength } = this.transform
      return {
        transform: `rotate(${deg}deg)`,
        transition: enableTransition ? 'all .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`,
        width: scale * maxLength + 'px',
        height: scale * maxLength + 'px'
      }
    }
  },
  data () {
    return {
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
        maxLength: 300 // 宽和高的两者比较长的那部分
      },
      multiples: 100, // 放大或者缩小
      deg: 0, // 旋转的角度
      isDrag: false, // 是否开始拖拽
      startX: 0, // 鼠标的点击X轴
      startY: 0, // 鼠标的点击Y轴
      moveX: 0, // 鼠标移动的X轴
      moveY: 0, // 鼠标移动的Y轴
      endX: 0,
      endY: 0,
      imgWidth: 0,
      imgHeight: 0,
      imgLoading: true,
      pictureInfoSaveDialogVisible: false,
      pictureInfoSaveDialogData: {}
    }
  },
  methods: {
    handlePictureInfoSaveDialogConfirm () {},
    // 切换上一张图片
    handlePreviousPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      this.handleImageReset()
      switch (this.imgType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.imgNames[this.imgNames.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.imgNames[index - 1].fileAbsolutePath
            }
          }
          break
        case 'followupImgNames':
          index = this.followupImgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.followupImgNames[this.followupImgNames.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.followupImgNames[index - 1].fileAbsolutePath
            }
          }
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.sampleOrderImg[this.sampleOrderImg.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.sampleOrderImg[index - 1].fileAbsolutePath
            }
          }
          break
        case 'supplementaryImgs':
          index = this.supplementaryImgs.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.supplementaryImgs[this.supplementaryImgs.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.supplementaryImgs[index - 1].fileAbsolutePath
            }
          }
          break
      }
    },

    // 切换下一张图片
    handleNextPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      this.handleImageReset()
      switch (this.imgType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.imgNames.length - 1) {
              this.imgSrc = this.imgNames[0].fileAbsolutePath
            } else {
              this.imgSrc = this.imgNames[index + 1].fileAbsolutePath
            }
          }
          break
        case 'followupImgNames':
          index = this.followupImgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.followupImgNames.length - 1) {
              this.imgSrc = this.followupImgNames[0].fileAbsolutePath
            } else {
              this.imgSrc = this.followupImgNames[index + 1].fileAbsolutePath
            }
          }
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.sampleOrderImg.length - 1) {
              this.imgSrc = this.sampleOrderImg[0].fileAbsolutePath
            } else {
              this.imgSrc = this.sampleOrderImg[index + 1].fileAbsolutePath
            }
          }
          break
        case 'supplementaryImgs':
          index = this.supplementaryImgs.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.supplementaryImgs.length - 1) {
              this.imgSrc = this.supplementaryImgs[0].fileAbsolutePath
            } else {
              this.imgSrc = this.supplementaryImgs[index + 1].fileAbsolutePath
            }
          }
          break
      }
    },

    // 编辑图片信息
    handleEditPicture () {
      this.pictureInfoSaveDialogData = {
        type: this.imgType,
        sampleBasicId: this.sampleBasicId,
        tableData: this.imgType === 'sampleOrderImg' ? JSON.parse(JSON.stringify(this.sampleOrderImg)) : [] // 送检单与知情同意书专用
      }
      this.$emit('fixImgEvent', {
        pictureInfoSaveDialogData: this.pictureInfoSaveDialogData,
        pictureInfoSaveDialogVisible: true
      })
    },
    // 图片的操作
    handleImageAction (action, options = {}) {
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.5,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      }
      const { transform } = this
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.5) {
            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3))
          }
          break
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3))
          break
        case 'clocelise':
          transform.deg += rotateDeg
          break
        case 'anticlocelise':
          transform.deg -= rotateDeg
          break
      }
      transform.enableTransition = enableTransition
    },
    // 还原图片
    handleImageReset () {
      this.transform = {
        ...this.transform,
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      }
    },
    // 获取图片索引
    getImgIndexAndLength (type) {
      let index = 0
      let length = 0
      switch (type) {
        case 'sampleOrderImg':
          length = this.sampleOrderImg.length
          if (this.imgType === type) {
            index = this.sampleOrderImg.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
        case 'imgNames':
          length = this.imgNames.length
          if (this.imgType === type) {
            index = this.imgNames.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
        case 'followupImgNames':
          length = this.followupImgNames.length
          if (this.imgType === type) {
            index = this.followupImgNames.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
        case 'supplementaryImgs':
          length = this.supplementaryImgs.length
          if (this.imgType === type) {
            index = this.supplementaryImgs.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
      }
      return length === 0 ? '' : `(${index + 1} / ${length})`
    },
    // 切换图片类型
    handleTabChange () {
      this.deg = 0
      this.multiples = 100
      if (this.imgType === 'fixInfo') {
        this.supplementaryNote = this.form.supplementaryNote
      }
      switch (this.imgType) {
        case 'imgNames':
          if (this.imgNames.length !== 0) {
            this.imgSrc = this.imgNames[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
        case 'followupImgNames':
          if (this.followupImgNames.length !== 0) {
            this.imgSrc = this.followupImgNames[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
        case 'sampleOrderImg':
          if (this.sampleOrderImg.length !== 0) {
            this.imgSrc = this.sampleOrderImg[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
        case 'supplementaryImgs':
          if (this.supplementaryImgs.length !== 0) {
            this.imgSrc = this.supplementaryImgs[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
      }
    },
    // 图片放大
    handleMagnify () {
      if (this.multiples >= 300) {
        this.multiples = 300
      } else {
        this.multiples += 25
      }
      this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      this.$refs.img.style.maxHeight = '100%'
      this.$refs.img.style.height = ''
      this.$refs.img.style.maxWidth = ''
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },

    // 图片缩小
    handleShrink () {
      if (this.multiples <= 25) {
        this.multiples = 25
      } else {
        this.multiples -= 25
      }
      this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      this.$refs.img.style.maxHeight = '100%'
      this.$refs.img.style.height = ''
      this.$refs.img.style.maxWidth = ''
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },

    // 图片旋转
    handleRotate () {
      this.deg += 90
      if (this.deg >= 360) {
        this.deg = 0
      }
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },
    handleViewPdf (url) {
      window.open(url, '_blank')
    },
    handleImgLoad () {
      this.imgLoading = false
      this.transform.maxLength = this.$refs.imgDiv.clientWidth
    },
    handleImgError (e) {
      this.imgLoading = false
      e.target.alt = '加载失败'
    }
  }
}
</script>

<style scoped lang="scss">
.right{
  border-left: 1px solid #DCDFE6;
  padding: 0 5px;
  flex: 1;
  overflow-y: auto;
  height: 100%;
.picture{
  margin: 10px auto;
  width: 90%;
  height: calc(100% - 54px - 28px - 20px - 40px);
.image{
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: auto;
}
}
.title{
  height: 30px;
  line-height: 30px;
  font-size: 13px;
}
}
</style>
