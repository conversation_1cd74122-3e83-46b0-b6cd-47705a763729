<template>
  <div class="wrapper">
    <el-tabs v-model.trim="active" @tab-click="handleClick">
      <el-tab-pane label="未完成" name="1">
<!--        <uncharted></uncharted>-->
      </el-tab-pane>
      <el-tab-pane label="已完成" name="2">
<!--        <charted></charted>-->
      </el-tab-pane>
    </el-tabs>
    <transition name="fade">
      <component :is="activeComponents"></component>
    </transition>
  </div>
</template>

<script>
import uncharted from './uncharted'
import charted from './charted'
export default {
  name: 'index',
  components: {
    uncharted,
    charted
  },
  data () {
    return {
      activeComponents: 'uncharted',
      active: '1'
    }
  },
  methods: {
    handleClick () {
      const components = {
        1: 'uncharted',
        2: 'charted'
      }
      this.activeComponents = components[this.active]
    }
  }
}
</script>

<style scoped lang="scss">
.fade-enter,.fade-leave-to{
  opacity: 0;
  transition: all .5s;
}
.fade-enter-to,.fade-leave{
  opacity: 1;
  transition: all .5s;
}
.fade-enter-active,.fade-leave-active{
  transition: all .5s;
}
// 设置滑动过渡必须给每个组件设定宽度
.wrapper {
  width: 100%;
  overflow: hidden;
  // min-height: 100vh;
  // background: chartreuse;
}
</style>
