<template>
  <div class="page">
    <header class="header">
      <div class="title">在线图片选择</div>
      <div class="ops">
        <el-button size="mini" @click="enterView" :disabled="!currentImage">进入查看</el-button>
        <el-button size="mini" type="primary" @click="enterEdit" :disabled="!currentImage">进入编辑</el-button>
      </div>
    </header>

    <section class="body">
      <div class="left">
        <gallery-grid
          ref="grid"
          :multiple="true"
          @select-change="onSelectChange"
          @open="onOpen"
        />
      </div>
      <div class="right">
        <div class="panel">
          <div class="section">
            <div class="kv">已选数量：<b>{{ selectedIds.length }}</b></div>
            <div class="kv">当前：<b>{{ currentImage ? (currentImage.name || currentImage.title || idOf(currentImage)) : '-' }}</b></div>
          </div>
          <div class="section">
            <el-button size="mini" type="primary" :disabled="!currentImage" @click="enterView">在新页面查看</el-button>
            <el-button size="mini" :disabled="selectedIds.length===0" @click="batchOpen">批量查看（逐一）</el-button>
          </div>
          <div class="section tip">
            提示：双击缩略图可直接进入图像查看。
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import GalleryGrid from './GalleryGrid.vue'

export default {
  name: 'OnlineImageSelectorPage',
  components: { GalleryGrid },
  data () {
    return {
      selectedIds: [],
      currentImage: null
    }
  },
  methods: {
    idOf (img) {
      return img && (img.id || img.image_id || img._id || img.uuid || img.ID)
    },
    onSelectChange (ids) {
      this.selectedIds = ids
    },
    onOpen (img) {
      this.currentImage = img
      this.enterView()
    },
    enterView () {
      if (!this.currentImage) return
      const id = this.idOf(this.currentImage)
      this.$router.push({
        path: '/business/sub/onlineImage/viewer',
        query: { id, mode: 'view' }
      })
    },
    enterEdit () {
      if (!this.currentImage) return
      const id = this.idOf(this.currentImage)
      this.$router.push({
        path: '/business/sub/onlineImage/viewer',
        query: { id, mode: 'edit' }
      })
    },
    batchOpen () {
      if (this.selectedIds.length === 0) return
      const id = this.selectedIds[0]
      this.$router.push({
        path: '/business/sub/onlineImage/viewer',
        query: { id, mode: 'view' }
      })
    }
  }
}
</script>

<style scoped>
.page{
  height:100%;
  display:grid;
  grid-template-rows:auto 1fr;
  gap:8px;
  background:#fff;
  color:#303133;
}
.header{
  display:flex;
  align-items:center;
  justify-content:space-between;
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  padding:8px 12px;
}
.title{font-weight:600}
.body{
  display:grid;
  grid-template-columns:1fr minmax(260px, 300px);
  gap:8px;
  height:100%;
  min-height:0;
}
.left{min-width:0}
.right{min-width:0}
.panel{
  height:100%;
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  padding:8px;
  display:flex;
  flex-direction:column;
  gap:8px;
}
.section{
  background:#fff;
  border:1px dashed #dcdfe6;
  border-radius:4px;
  padding:8px;
}
.kv{color:#606266}
.tip{color:#909399;font-size:12px}
</style>
