<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="900px"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" inline>

        <el-form-item prop="name" label="对照标准">
          <el-input v-model.trim="form.name" size="mini" clearable maxlength="20"></el-input>
        </el-form-item>
        <el-form-item prop="notes" label="说明">
          <el-input v-model.trim="form.notes" size="mini" clearable maxlength="50"></el-input>
        </el-form-item>

        <!--表格-->
        <el-table
          ref="table"
          :data="form.tableData"
          class="table">
          <el-table-column label="质控项" prop="itemName" min-width="100" show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="判断标准" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-select v-model.trim="scope.row.judgeStandard"  clearable size="mini" @change="handleJudgeStandardChange(scope.$index)">
                <el-option :key="index"
                           :value="item.value"
                           :label="item.label"
                           v-for="(item, index) in standardList">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="上限" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
                <el-input
                  v-model.trim="scope.row.upperLimit"
                  :disabled="!scope.row.judgeStandard"
                  clearable
                  size="mini"
                  @blur="handlePrice(scope.$index, 'upperLimit')" ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="下限" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
                <el-input
                  v-model.trim="scope.row.lowerLimit"
                  :disabled="!scope.row.judgeStandard"
                  clearable
                  size="mini"
                  @blur="handlePrice(scope.$index, 'lowerLimit')"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number
    },
    title: {
      type: String
    }
  },
  data () {
    return {
      form: {
        name: '',
        notes: '',
        tableData: [{}]
      },
      loading: false,
      standardList: [{
        label: '范围',
        value: '范围'
      }, {
        label: '等于',
        value: '等于'
      }, {
        label: '不等于',
        value: '不等于'
      }
      ],
      rules: {
        name: [{required: true, message: '请输入对照标准', trigger: ['blur', 'change']}]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.form = {
          name: '',
          notes: '',
          tableData: [{}]
        }
        this.id ? this.getStandardInfo() : this.getStandardQcItems()
      })
    },
    // 获取对照标准信息
    getStandardInfo () {
      this.$ajax({
        url: '/system/control_standard/get_standard_info',
        data: {
          fid: this.id
        },
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.form.name = data.fname
          this.form.notes = data.fnote
          this.form.tableData = []
          let standardDetails = data.standardDetails || []
          standardDetails.forEach(v => {
            this.form.tableData.push({
              itemName: v.fqcItem,
              judgeStandard: v.foperation,
              upperLimit: v.fmaxValue,
              lowerLimit: v.fminValue
            })
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取质控项列表
    getStandardQcItems () {
      this.$ajax({
        url: '/system/control_standard/get_standard_qc_items'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.form.tableData = []
          data.forEach(v => {
            this.form.tableData.push({
              itemName: v.itemName,
              judgeStandard: '',
              upperLimit: '',
              lowerLimit: ''
            })
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleJudgeStandardChange (index) {
      if (this.form.tableData[index].judgeStandard === '') {
        this.$set(this.form.tableData[index], 'upperLimit', '')
        this.$set(this.form.tableData[index], 'lowerLimit', '')
      }
    },
    // 输入金额/^\d+\.\d+$/
    handlePrice (index, key) {
      let val = this.form.tableData[index][key]
      if (isNaN(val * 100) || val === '') {
        this.form.tableData[index][key] = ''
      } else {
        if (val < 0) {
          this.form.tableData[index][key] = 0.00
        }
        if (val >= 1000000000) {
          this.form.tableData[index][key] = 1000000000.00
        }
        if ((val * 100) % 1 !== 0 || val.toString().split('.')[1] === '00') { // 判断最多只能输入两位小数
          this.form.tableData[index][key] = Number(val.toString().match(/^\d+(?:\.\d{0,2})?/))
        }
        this.form.tableData[index][key] = Number(this.form.tableData[index][key]).toFixed(2)
      }
    },
    // 确认校验
    handleConfirm () {
      let tableData = this.form.tableData || []
      let len = tableData.length
      for (let i = 0; i < len; i++) {
        let item = tableData[i]
        if (item.judgeStandard === '范围') {
          if (!(item.upperLimit) && !(item.lowerLimit)) {
            this.$message.error('无法保存，原因：判断标准为范围时，上限或下限为必填')
            return
          }
        }
        if (item.judgeStandard === '等于' || item.judgeStandard === '不等于') {
          if (!item.upperLimit) {
            this.$message.error('无法保存，原因：判断标准为等于或不等于时，上限为必填')
            return
          }
        }
        if (item.upperLimit && item.lowerLimit && (item.upperLimit * 1 < item.lowerLimit * 1)) {
          this.$message.error(`无法保存，原因：质控项${item.itemName}上限值小于下限值`)
          return
        }
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.handleSave()
        }
      })
    },
    // 保存信息
    handleSave () {
      this.loading = true
      let data = {
        fid: this.id,
        fname: this.form.name,
        fnote: this.form.notes
      }
      let tableData = this.form.tableData || []
      data.standardDetails = []
      tableData.forEach(v => {
        data.standardDetails.push({
          fqcItem: v.itemName,
          foperation: v.judgeStandard,
          fmaxValue: v.upperLimit,
          fminValue: v.lowerLimit
        })
      })
      this.$ajax({
        url: '/system/control_standard/save_control_standard',
        data
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped></style>
