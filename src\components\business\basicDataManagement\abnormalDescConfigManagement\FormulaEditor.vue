<template>
  <div class="formula-editor-container">
    <!-- 编辑区域 -->
    <div class="formula-input-area">
      <div
        class="custom-editor"
        @click="!disabled && focusEditor"
        @dragover="handleDragOver"
        @drop="handleDrop"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave">
        <div class="editor-content" ref="editorContent" :contenteditable="!disabled" @input="handleEditorInput" @paste="handlePaste" @keydown="handleKeyDown"></div>
        <div class="editor-placeholder" v-show="!formulaText && !disabled">请在此处编辑异常描述，点击下方变量添加</div>
      </div>
    </div>

    <!-- 变量选择区域 -->
    <div class="formula-variables-area" v-if="!disabled">
      <div class="tips">温馨提示：双击或拖动下方变量，可将对应变量添加到上方异常描述编辑框中。</div>

      <!-- 搜索框 -->
      <div class="search-bar">
        <el-input
          placeholder="搜索变量"
          v-model="searchKey"
          size="mini"
          prefix-icon="el-icon-search"
          clearable
          class="search-input">
        </el-input>
      </div>

      <!-- 变量分类栏 -->
      <!-- <div class="category-tabs">
        <div
          v-for="(group) in variableGroups"
          :key="group.title"
          :class="['category-tab', activeCategory === group.title ? 'active' : '']"
          @click="activeCategory = group.title">
          {{ group.title }}
        </div>
      </div> -->
      <!-- 变量显示区域 -->
      <div class="variables-list">
        <div
          v-for="(variable, index) in filteredVariables"
          :key="index"
          class="variable-item"
          draggable="true"
          @dragstart="handleDragStart(variable, $event)"
          @click="selectVariable(variable)">
          {{ variable.name }}
        </div>
      </div>

      <!-- 最近使用 -->
      <div class="recent-used" v-if="recentVariables.length > 0">
        <div class="recent-title">最近使用</div>
        <div class="recent-items">
          <div
            v-for="(variable, index) in recentVariables"
            :key="'recent-' + index"
            class="recent-item"
            draggable="true"
            @dragstart="handleDragStart(variable, $event)"
            @click="selectVariable(variable)">
            {{ variable.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormulaEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    variableGroupsData: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  mounted () {
    // 初始挂载时设置初始分类
    if (this.activeCategory === '' && this.variableGroups.length > 0) {
      this.activeCategory = this.variableGroups[0].title
    }

    // 初始渲染
    this.renderEditor()
  },
  watch: {
    value: {
      handler (newVal) {
        if (newVal !== this.formulaText) {
          this.formulaText = newVal || ''
          this.$nextTick(() => {
            this.renderEditor()
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    // 变量组，优先使用传入的数据
    variableGroups () {
      return this.variableGroupsData.length > 0 ? this.variableGroupsData : this.defaultGroups
    },
    // 当前选中分类的变量
    currentCategoryVariables () {
      const group = this.variableGroups.find(g => g.title === this.activeCategory)
      return group ? group.variables : []
    },
    // 根据搜索关键词过滤变量
    filteredVariables () {
      if (!this.searchKey) {
        return this.currentCategoryVariables
      }

      const searchLower = this.searchKey.toLowerCase()
      if (this.activeCategory) {
        return this.currentCategoryVariables.filter(v =>
          v.name.toLowerCase().includes(searchLower)
        )
      } else {
        // 搜索所有分类
        let result = []
        this.variableGroups.forEach(group => {
          result = result.concat(
            group.variables.filter(v =>
              v.name.toLowerCase().includes(searchLower)
            )
          )
        })
        return result
      }
    }
  },
  data () {
    return {
      formulaText: '',
      searchKey: '',
      activeCategory: '',
      recentVariables: [],
      draggedVariable: null,
      isDraggingOver: false,
      // 默认变量组
      defaultGroups: [
        {
          title: '样本信息',
          variables: [
            { name: '样本量', type: 'sampleAmount', value: 'sampleAmount' },
            { name: '样本组编', type: 'sampleGroup', value: 'sampleGroup' },
            { name: '提取用量', type: 'extractionAmount', value: 'extractionAmount' },
            { name: '剩余量', type: 'remainingAmount', value: 'remainingAmount' }
          ]
        },
        {
          title: '核酸信息',
          variables: [
            { name: '核酸总量', type: 'nucleicTotalAmount', value: 'nucleicTotalAmount' },
            { name: '核酸浓度', type: 'nucleicConcentration', value: 'nucleicConcentration' },
            { name: '核酸等级', type: 'nucleicGrade', value: 'nucleicGrade' },
            { name: 'DNA总量', type: 'dnaTotalAmount', value: 'dnaTotalAmount' }
          ]
        },
        {
          title: '检验结果',
          variables: [
            { name: '检验完成度', type: 'inspectionCompleteness', value: 'inspectionCompleteness' },
            { name: '检验等级', type: 'inspectionGrade', value: 'inspectionGrade' },
            { name: '等级质控', type: 'gradeQC', value: 'gradeQC' },
            { name: '异常告知报告', type: 'abnormalReport', value: 'abnormalReport' }
          ]
        }
      ]
    }
  },
  methods: {
    // 清除缓存并刷新编辑器内容 - 保留现有数据
    clearCache () {
      // 不再清空formulaText，只清空DOM并重新渲染
      this.$nextTick(() => {
        if (this.$refs.editorContent) {
          this.$refs.editorContent.innerHTML = ''
          this.renderEditor()
        }
      })
    },

    // 提供给外部调用的刷新方法
    refresh () {
      this.$nextTick(() => {
        // 重新渲染编辑器内容
        this.renderEditor()
        // 如果不是禁用状态，设置焦点
        if (!this.disabled) {
          this.focusEditor()
        }
      })
    },

    // 渲染编辑器内容
    renderEditor () {
      if (!this.$refs.editorContent) return

      const editorContent = this.$refs.editorContent
      editorContent.innerHTML = '' // 确保清空旧内容

      // 解析公式文本
      let text = this.formulaText || ''
      let currentIndex = 0
      const regex = /\{\{([^}]+)\}\}/g
      let match

      while ((match = regex.exec(text)) !== null) {
        // 添加变量前的文本
        if (match.index > currentIndex) {
          const textBefore = text.substring(currentIndex, match.index)
          // 处理文本中的换行符
          const lines = textBefore.split('\n')
          for (let i = 0; i < lines.length; i++) {
            const textNode = document.createTextNode(lines[i] || '')
            editorContent.appendChild(textNode)
            // 除了最后一行，每行后面添加一个换行
            if (i < lines.length - 1) {
              editorContent.appendChild(document.createElement('br'))
            }
          }
        }

        // 添加变量标签
        const variableValue = match[1]
        const variableName = this.getVariableName(variableValue)

        const varElement = document.createElement('span')
        varElement.className = 'variable-tag'
        varElement.setAttribute('data-value', variableValue)
        varElement.textContent = variableName
        // 设置为不可编辑
        varElement.setAttribute('contenteditable', 'false')
        // 应用样式
        varElement.style.backgroundColor = '#ecf5ff'
        varElement.style.color = '#409EFF'
        varElement.style.border = '1px solid #d9ecff'
        varElement.style.borderRadius = '3px'
        varElement.style.padding = '0 6px'
        varElement.style.margin = '0 2px'
        varElement.style.display = 'inline-block'
        varElement.style.height = '22px'
        varElement.style.lineHeight = '20px'
        varElement.style.fontSize = '12px'
        varElement.style.fontWeight = '500'
        varElement.style.verticalAlign = 'middle'
        varElement.style.whiteSpace = 'nowrap'
        varElement.style.userSelect = 'none'
        varElement.style.cursor = 'default'

        editorContent.appendChild(varElement)

        currentIndex = match.index + match[0].length
      }

      // 添加剩余文本
      if (currentIndex < text.length) {
        const remainingText = text.substring(currentIndex)
        // 处理剩余文本中的换行符
        const lines = remainingText.split('\n')
        for (let i = 0; i < lines.length; i++) {
          const textNode = document.createTextNode(lines[i] || '')
          editorContent.appendChild(textNode)
          // 除了最后一行，每行后面添加一个换行
          if (i < lines.length - 1) {
            editorContent.appendChild(document.createElement('br'))
          }
        }
      }

      console.log('Rendered content with line breaks:', editorContent.innerHTML)
    },

    // 获取变量名称
    getVariableName (value) {
      for (const group of this.variableGroups) {
        for (const variable of group.variables) {
          if (variable.value === value) {
            return variable.name
          }
        }
      }
      return value
    },

    // 选择变量
    selectVariable (variable) {
      if (variable.needInput) {
        this.$emit('need-input', variable)
        return
      }

      // 创建变量标签并插入到编辑区域
      const varElement = document.createElement('span')
      varElement.className = 'variable-tag'
      varElement.setAttribute('data-value', variable.value)
      varElement.textContent = variable.name

      // 设置为不可编辑
      varElement.setAttribute('contenteditable', 'false')

      // 直接应用样式
      varElement.style.backgroundColor = '#ecf5ff'
      varElement.style.color = '#409EFF'
      varElement.style.border = '1px solid #d9ecff'
      varElement.style.borderRadius = '3px'
      varElement.style.padding = '0 6px'
      varElement.style.margin = '0 2px'
      varElement.style.display = 'inline-block'
      varElement.style.height = '22px'
      varElement.style.lineHeight = '20px'
      varElement.style.fontSize = '12px'
      varElement.style.fontWeight = '500'
      varElement.style.verticalAlign = 'middle'
      varElement.style.whiteSpace = 'nowrap'
      varElement.style.userSelect = 'none'
      varElement.style.cursor = 'default'

      // 获取当前选区，如果有选区就在选区位置插入，否则在末尾插入
      const selection = window.getSelection()
      const editorContent = this.$refs.editorContent

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        if (editorContent.contains(range.commonAncestorContainer)) {
          // 在选区位置插入
          range.deleteContents()
          range.insertNode(varElement)

          // 移动光标到变量标签后面
          range.setStartAfter(varElement)
          range.setEndAfter(varElement)
          selection.removeAllRanges()
          selection.addRange(range)
        } else {
          // 在末尾添加
          editorContent.appendChild(varElement)
          this.setCaretToEnd()
        }
      } else {
        // 在末尾添加
        editorContent.appendChild(varElement)
        this.setCaretToEnd()
      }

      // 更新并保存
      this.updateFormula()
      this.updateRecentVariables(variable)
      this.focusEditor()
    },

    // 设置光标到末尾
    setCaretToEnd () {
      const editorContent = this.$refs.editorContent
      const range = document.createRange()
      const selection = window.getSelection()

      range.selectNodeContents(editorContent)
      range.collapse(false)
      selection.removeAllRanges()
      selection.addRange(range)
    },

    // 从编辑器内容更新公式
    updateFormula () {
      if (!this.$refs.editorContent) return

      const editorContent = this.$refs.editorContent
      let formula = ''

      // 遍历所有子节点
      for (let i = 0; i < editorContent.childNodes.length; i++) {
        const node = editorContent.childNodes[i]

        if (node.nodeType === Node.TEXT_NODE) {
          // 文本节点直接添加
          formula += node.textContent
        } else if (node.nodeType === Node.ELEMENT_NODE && node.classList && node.classList.contains('variable-tag')) {
          // 变量节点添加为 {{变量值}}
          const value = node.getAttribute('data-value')
          formula += `{{${value}}}`
        } else if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'BR') {
          // 处理换行符
          formula += '\n'
        }
      }

      console.log('Formula text with line breaks:', formula)
      this.formulaText = formula
      this.$emit('input', formula)
      this.$emit('change', formula)
    },

    // 编辑器获取焦点
    focusEditor () {
      this.$refs.editorContent.focus()
    },

    // 处理编辑器输入事件
    handleEditorInput () {
      this.updateFormula()
    },

    // 处理粘贴事件
    handlePaste (e) {
      e.preventDefault()
      const text = (e.clipboardData || window.clipboardData).getData('text')

      if (text) {
        // 将文本插入到光标位置
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          range.deleteContents()

          // 处理粘贴文本中的换行
          const lines = text.split('\n')
          const fragment = document.createDocumentFragment()

          for (let i = 0; i < lines.length; i++) {
            const textNode = document.createTextNode(lines[i])
            fragment.appendChild(textNode)

            // 除了最后一行，每行后面添加换行
            if (i < lines.length - 1) {
              fragment.appendChild(document.createElement('br'))
            }
          }

          // 一次性插入所有内容
          range.insertNode(fragment)

          // 移动光标到插入内容的末尾
          range.collapse(false)
          selection.removeAllRanges()
          selection.addRange(range)
        }

        this.updateFormula()
      }
    },

    // 处理键盘事件，主要处理删除操作和回车键
    handleKeyDown (e) {
      // 处理回车键，确保创建BR标签
      if (e.key === 'Enter') {
        e.preventDefault()

        // 插入换行符
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          const br = document.createElement('br')

          range.deleteContents()
          range.insertNode(br)

          // 在换行符后添加一个空白文本节点，确保光标能正确定位到下一行
          const textNode = document.createTextNode('\u200B') // 使用零宽空格
          range.setStartAfter(br)
          range.insertNode(textNode)

          // 移动光标到空白文本节点后，确保光标在新行上
          range.setStartAfter(textNode)
          range.setEndAfter(textNode)
          selection.removeAllRanges()
          selection.addRange(range)

          this.updateFormula()
          return
        }
      }

      // 如果是删除键且当前选区为空（即光标位置）
      if ((e.key === 'Backspace' || e.key === 'Delete') && window.getSelection) {
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)

          // 如果有选中内容，让浏览器正常处理
          if (!range.collapsed) {
            // 检查选中内容是否包含变量标签，如果是则需要阻止默认行为并手动处理
            const selectedNodes = this.getSelectedNodes()
            for (const node of selectedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE &&
                  (node.classList.contains('variable-tag') || node.getAttribute('data-value'))) {
                // 变量标签被选中，删除选中内容并更新
                e.preventDefault()
                range.deleteContents()
                this.updateFormula()
                return
              }
            }

            // 如果没有包含变量标签，让浏览器处理后更新
            setTimeout(() => this.updateFormula(), 0)
            return
          }

          // 处理光标位置紧邻变量标签的情况
          const container = range.startContainer
          const offset = range.startOffset

          // 向后删除
          if (e.key === 'Delete') {
            // 检查光标后面是否是变量标签或换行符
            let nextNode = null

            if (container.nodeType === Node.TEXT_NODE && offset === container.textContent.length) {
              // 光标在文本末尾，检查下一个节点
              nextNode = container.nextSibling
            } else if (container.nodeType === Node.ELEMENT_NODE && offset < container.childNodes.length) {
              // 光标在元素中间，检查子节点
              nextNode = container.childNodes[offset]
            }

            if (nextNode && nextNode.nodeType === Node.ELEMENT_NODE) {
              if (nextNode.classList && (nextNode.classList.contains('variable-tag') || nextNode.getAttribute('data-value'))) {
                // 下一个节点是变量标签，删除它
                e.preventDefault()
                nextNode.remove()
                this.updateFormula()
                return
              } else if (nextNode.tagName === 'BR') {
                // 下一个节点是换行符，删除它
                e.preventDefault()
                nextNode.remove()
                this.updateFormula()
                return
              }
            }
          }

          // 向前删除
          if (e.key === 'Backspace') {
            // 检查光标前面是否是变量标签或换行符
            let prevNode = null

            if (container.nodeType === Node.TEXT_NODE && offset === 0) {
              // 光标在文本开头，检查前一个节点
              prevNode = container.previousSibling
            } else if (container.nodeType === Node.ELEMENT_NODE && offset > 0) {
              // 光标在元素中间，检查子节点
              prevNode = container.childNodes[offset - 1]
            }

            if (prevNode && prevNode.nodeType === Node.ELEMENT_NODE) {
              if (prevNode.classList && (prevNode.classList.contains('variable-tag') || prevNode.getAttribute('data-value'))) {
                // 前一个节点是变量标签，删除它
                e.preventDefault()
                prevNode.remove()
                this.updateFormula()
                return
              } else if (prevNode.tagName === 'BR') {
                // 前一个节点是换行符，删除它
                e.preventDefault()
                prevNode.remove()
                this.updateFormula()
                return
              }
            }
          }
        }
      }

      // 默认情况，让浏览器处理后更新公式
      setTimeout(() => this.updateFormula(), 0)
    },

    // 获取选中的所有节点
    getSelectedNodes () {
      const nodes = []
      const selection = window.getSelection()

      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const container = range.commonAncestorContainer

        // 如果公共祖先是文本节点，直接返回它
        if (container.nodeType === Node.TEXT_NODE) {
          nodes.push(container)
          return nodes
        }

        // 遍历范围内的所有节点
        const walker = document.createTreeWalker(
          container,
          NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_TEXT,
          {
            acceptNode: function (node) {
              return NodeFilter.FILTER_ACCEPT
            }
          },
          false
        )

        let node = walker.currentNode

        while (node) {
          if (selection.containsNode(node, true)) {
            nodes.push(node)
          }
          node = walker.nextNode()
        }
      }

      return nodes
    },

    // 更新最近使用的变量
    updateRecentVariables (variable) {
      // 从最近列表中移除相同的变量（如果存在）
      const index = this.recentVariables.findIndex(v => v.type === variable.type)
      if (index !== -1) {
        this.recentVariables.splice(index, 1)
      }

      // 添加到最前面
      this.recentVariables.unshift(variable)

      // 限制最近使用的数量
      if (this.recentVariables.length > 10) {
        this.recentVariables.pop()
      }
    },

    // 开始拖拽变量
    handleDragStart (variable, event) {
      this.draggedVariable = variable
      // 设置拖拽数据
      event.dataTransfer.setData('text/plain', variable.name)
      event.dataTransfer.effectAllowed = 'copy'
    },

    // 拖拽变量进入编辑区域
    handleDragEnter (event) {
      event.preventDefault()
      if (this.disabled) return
      this.isDraggingOver = true
      event.currentTarget.classList.add('dragging-over')
    },

    // 拖拽变量在编辑区域上方移动
    handleDragOver (event) {
      event.preventDefault()
      if (this.disabled) return
      event.dataTransfer.dropEffect = 'copy'
    },

    // 拖拽变量离开编辑区域
    handleDragLeave (event) {
      if (this.disabled) return
      // 确保只在真正离开编辑区域时触发，而不是进入子元素时
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.isDraggingOver = false
        event.currentTarget.classList.remove('dragging-over')
      }
    },

    // 拖拽释放变量到编辑区域
    handleDrop (event) {
      event.preventDefault()
      if (this.disabled || !this.draggedVariable) return

      event.currentTarget.classList.remove('dragging-over')
      this.isDraggingOver = false

      // 获取释放位置
      const selection = window.getSelection()
      let range

      // 尝试获取最接近的放置位置
      if (document.caretRangeFromPoint) {
        // Chrome, Safari
        range = document.caretRangeFromPoint(event.clientX, event.clientY)
      } else if (event.rangeParent) {
        // Firefox
        range = document.createRange()
        range.setStart(event.rangeParent, event.rangeOffset)
      }

      // 如果获取到有效的放置位置
      if (range) {
        // 设置选区到放置位置
        selection.removeAllRanges()
        selection.addRange(range)
      } else {
        // 如果无法确定放置位置，则放在编辑器末尾
        this.setCaretToEnd()
      }

      // 添加变量到放置位置
      this.selectVariable(this.draggedVariable)
      this.draggedVariable = null
    },

    // 组件激活时确保内容刷新但不清除数据
    activated () {
      // 只刷新显示，不清除数据
      this.refresh()
    },

    // 组件更新时确保内容刷新
    updated () {
      if (this.value !== this.formulaText) {
        this.formulaText = this.value || ''
        this.renderEditor()
      }
    }
  }
}
</script>

<style scoped>
.formula-editor-container {
  display: flex;
  flex-direction: column;
  max-height: 450px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.formula-input-area {
  padding: 12px;
  background-color: #fff;
  border-bottom: 1px solid #e8eaec;
}

.custom-editor {
  position: relative;
  min-height: 120px;
  max-height: 200px;
  padding: 10px;
  border: 1px solid #e0e3e9;
  border-radius: 4px;
  background-color: #f9fafc;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
  font-family: 'Courier New', monospace;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.custom-editor.dragging-over {
  border-color: #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
  background-color: #f0f7ff;
}

.editor-content {
  min-height: 120px;
  outline: none;
  word-break: break-word;
}

.editor-placeholder {
  position: absolute;
  top: 10px;
  left: 10px;
  color: #909399;
  pointer-events: none;
}

.variable-tag {
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  margin: 0 2px;
  background-color: #ecf5ff;
  color: #409EFF;
  border: 1px solid #d9ecff;
  border-radius: 3px;
  cursor: default;
  white-space: nowrap;
  font-size: 13px;
  line-height: 22px;
  height: 24px;
  box-sizing: border-box;
  vertical-align: middle;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  user-select: none;
}

.variable-tag:hover {
  background-color: #d8ebff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.variable-tag:before {
  content: '${';
  color: #909399;
  font-size: 11px;
  margin-right: 2px;
  opacity: 0.8;
}

.variable-tag:after {
  content: '}';
  color: #909399;
  font-size: 11px;
  margin-left: 2px;
  opacity: 0.8;
}

.formula-variables-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  padding: 12px;
}

.search-bar {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.search-input>>>input {
  border-radius: 16px;
  background-color: #fff;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e3e9;
}

.category-tab {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  background-color: #fff;
  color: #606266;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
}

.category-tab.active {
  background-color: #409EFF;
  color: #fff;
  border-color: #409EFF;
}

.category-tab:hover:not(.active) {
  color: #409EFF;
  border-color: #c6e2ff;
}

.variables-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
  flex: 1;
  overflow-y: auto;
  padding: 4px;
}

.variable-item {
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
  background-color: #ecf5ff;
  color: #409EFF;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
  white-space: nowrap;
  border: 1px solid #d9ecff;
}

.variable-item:hover {
  background-color: #409EFF;
  color: #fff;
  border-color: #409EFF;
}

.variable-item[draggable="true"] {
  cursor: grab;
}

.variable-item[draggable="true"]:active {
  cursor: grabbing;
}

.recent-used {
  border-top: 1px solid #e0e3e9;
  padding-top: 10px;
  margin-top: auto;
}

.recent-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.recent-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.recent-item {
  padding: 2px 10px;
  background-color: #f4f4f5;
  color: #909399;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  border: 1px solid #e6e6eb;
}

.recent-item:hover {
  background-color: #909399;
  color: #fff;
}

.recent-item[draggable="true"] {
  cursor: grab;
}

.recent-item[draggable="true"]:active {
  cursor: grabbing;
}

/* 滚动条样式 */
.variables-list::-webkit-scrollbar,
.custom-editor::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.variables-list::-webkit-scrollbar-thumb,
.custom-editor::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.variables-list::-webkit-scrollbar-track,
.custom-editor::-webkit-scrollbar-track {
  background: #f5f7fa;
}
</style>
