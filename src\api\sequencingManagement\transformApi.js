import { myAjax } from '@/util/ajax'

/**
 * 获取转化列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205698&itf=5799177
 * @param options
 * @returns {*}
 */
export function getTransformList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/transform/query_data',
    data: data,
    ...options
  })
}

/**
 * 下载任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205698&itf=5799170'
 * @param options
 * @returns {*}
 */
export function downloadTask (data, options = {}) {
  return myAjax({
    url: '/experiment/transform/export_task',
    responseType: 'blob',
    data: data,
    ...options
  })
}
