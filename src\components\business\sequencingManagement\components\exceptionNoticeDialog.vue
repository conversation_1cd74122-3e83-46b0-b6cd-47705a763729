<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleCloseDialog"
      append-to-body
      width="60vw"
      @open="handleOpen">
      <div class="wrapper">
        <div class="tips" style="width: 50vw; margin: 0 auto 10px;">
          <div v-if="source === 0">登记异常成功，后期请在异常处理中进行处理。</div>
          <div>异常样本包含多个原始样本名称（经过混样的）不允许发送通知邮件，请针对未混样的异常样本填写并发送客户的通知邮件：</div>
        </div>
        <template>
            <div class="content">
              <div>
                <h3 v-if="!isDetail" style="text-align: center">订单编号</h3>
                <el-tabs v-if="!isDetail" v-model.trim="activeOrder" tab-position="left" :before-leave="handleTapsChange">
                  <el-tab-pane v-for="(item, index) in orderCodes" :key="index" :name="item">
                    <span slot="label" :style="`color: ${emailForm[item].isSend ? 'green' : ''}`">{{item}}</span>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <div class="right">
                <div v-if="!isDetail" class="project-info">
                  <div style="margin-right: 30px">项目名称：{{emailInfo.projectName}}</div>
                  <div style="margin-right: 30px">订单编号：{{emailInfo.orderCode}}</div>
                  <div style="margin-right: 30px">勾选样本数量：{{emailInfo.sampleNums}}</div>
                </div>
                <div v-if="emailInfo.emailTitle">
                  <email-info v-model.trim="emailInfo" ref="emailInfo" :is-detail="isDetail" ></email-info>
                </div>
              </div>
            </div>
          </template>
      </div>
      <span v-if="!isDetail" slot="footer">
        <el-button size="mini" @click="handleClose">关闭弹窗</el-button>
        <el-button size="mini" @click="handleRemove">移除</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">发送</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import xx form 'xxx'
import mixins from '@/util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {getEmailDetail, getEmailInfo, sendEmail} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: `exceptionNoticeDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    exceptionIds: {
      type: Array,
      default: () => []
    },
    isDetail: {
      type: Boolean,
      default: null
    },
    sampleIds: {
      type: Array,
      default: () => []
    },
    source: {
      type: Number,
      default: 0 //  0: 登记异常 1: 发送邮件
    }
  },
  data () {
    return {
      activeOrder: '',
      title: '',
      loading: false,
      orderCodes: [],
      emailForm: {
      },
      emailInfo: {}
    }
  },
  methods: {
    handleOpen () {
      !this.isDetail ? this.getEmailInfo() : this.getEmailDetail()
      this.activeOrder = ''
      this.emailInfo = {}
    },
    async getEmailDetail () {
      let {res} = await awaitWrap(getEmailDetail({
        fid: this.exceptionIds[0]
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.title = '异常通知邮件'
        this.emailInfo = {
          fexceptionIds: data.fexceptionIds,
          emailTitle: data.fsubject,
          emailContent: data.fcontent,
          projectName: data.fproject,
          orderCode: data.forderCode,
          inbox: data.fccEmail,
          sendEmail: data.femail,
          sampleNums: data.fexceptionIds && data.fexceptionIds.split(',').length,
          isSend: false,
          attachFile: JSON.parse(data.files)
        }
      }
    },
    // 获取邮件信息
    async getEmailInfo () {
      let {res} = await awaitWrap(getEmailInfo({
        fexceptionIdList: this.exceptionIds
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.orderCodes = data.map(v => v.forderCode)
        this.emailForm = {}
        this.title = `异常通知邮件（${this.orderCodes.length}封）`
        data.forEach(v => {
          this.emailForm[v.forderCode] = {
            fexceptionIds: v.fexceptionIds,
            emailTitle: `【样本异常通知】-项目编号-${v.fproject}-${v.forderCode}-${util.dateFormatter(new Date(), false)}`,
            emailContent: '',
            projectName: v.fproject,
            orderCode: v.forderCode,
            inbox: [...new Set(v.fccEmailList.split(','))].join(','),
            sendEmail: v.femailList,
            sampleNums: v.fexceptionIds && v.fexceptionIds.split(',').length,
            isSend: false,
            attachFile: []
          }
        })
        this.activeOrder = this.orderCodes[0]
      }
    },
    handleTapsChange (activeName, oldActiveName) {
      this.$set(this, 'emailInfo', this.emailForm[activeName] || {})
      return true
    },
    handleCloseDialog () {
      this.visible = false
      if (this.isDetail) {
        return
      }
      this.$emit('dialogCloseEvent')
    },
    async handleClose () {
      // 【关闭弹窗】-点击时校验是否所有邮件均已发送，
      const notSendOrder = this.orderCodes.filter(v => !this.emailForm[v].isSend)
      if (notSendOrder < 1) {
        this.$message.success('邮件全部发送完成，后期请在异常处理中进行处理！')
        this.visible = false
        this.$emit('dialogCloseEvent')
      } else {
        await this.$confirm(`订单${notSendOrder.join(',')}未发送邮件给客户，后续如需继续发送邮件，需在上机-异常处理中重新操作。确认继续关闭弹窗吗？`, '提示', {
          cancelButtonText: '取消',
          confirmButtonText: '确定',
          type: 'warning'
        })
        this.visible = false
        this.$emit('dialogCloseEvent')
      }
    },
    async handleRemove () {
      await this.$confirm('移除后如需继续发送邮件，需在上机-异常处理中重新操作。确认继续移除吗？', '提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
      this.orderCodes = this.orderCodes.filter(v => v !== this.activeOrder)
      if (this.orderCodes.length < 1) {
        this.$message.warning('所有订单都已移除，请重新选择')
        this.visible = false
        return
      }
      this.$message.success('移除成功')
      this.activeOrder = this.orderCodes[0]
    },
    setParams () {
      return {
        fid: this.exceptionId,
        fexceptionIds: this.emailInfo.fexceptionIds,
        fsubject: this.emailInfo.emailTitle,
        fcontent: this.emailInfo.emailContent,
        files: this.emailInfo.attachFile,
        femailList: this.emailInfo.sendEmail,
        fccEmailList: this.emailInfo.inbox
      }
    },
    handleConfirm () {
      if (this.emailInfo.isSend) {
        this.$message.error('该邮件已发送，请勿重复发送！')
        return
      }
      this.$refs.emailInfo.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          const params = this.setParams()
          let {res} = await awaitWrap(sendEmail(params))
          if (res && res.code === this.SUCCESS_CODE) {
            this.emailForm[this.activeOrder].isSend = true
            this.emailInfo.isSend = true
            this.$message.success('邮件发送成功！')
          }
          this.loading = false
        } else {
          this.$message.error('表单存在错误请检查！')
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-width {
  width: 600px;
}

.email-content {
  padding-top: 15px;
  height: 50vh;
  overflow: auto;
  border: 1px solid #efefef;
}

.img {
  width: 150px;
  height: 150px;
  margin: 5px;
}
.content {
  display: flex;
  .left {
    width: 10vw;
  }
  .right {
    margin: auto;
    width: 50vw;
    height: 60vh;
    overflow: auto;
    padding: 20px;
    border: 1px solid #eeeeee;
  }
}
/deep/ .el-tabs__item.is-active {
  color: #000000;
}

.el-dialog__wrapper {
  /deep/.el-dialog__body {
    padding: 10px 20px !important;
  }
}

.project-info {
  display: flex;
  margin-bottom: 20px;
}
</style>
