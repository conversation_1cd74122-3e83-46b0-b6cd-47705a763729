// base color
$primary-color: #409EFF;
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;
$page-bg: #eff2fb; // 页面背景

// sidebar
$menuText:#bfcbd9;
$menuActiveText:#409EFF;
$subMenuActiveText:#f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg:#304156;
$menuHover:#263445;
$sidebarTitle: #ffffff;

$menuLightBg:#ffffff;
$menuLightHover:#f0f1f5;
$sidebarLightTitle: #001529;

$subMenuBg:#1f2d3d;
$subMenuHover:#001528;

$sideBarWidth: 200px;
$sideHideBarWidth: 60px; // 当菜单收起来的时候的宽度

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $primary-color;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  menuLightBg: $menuLightBg;
  menuLightHover: $menuLightHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  sideHideBarWidth: $sideHideBarWidth;
  sidebarTitle: $sidebarTitle;
  sidebarLightTitle: $sidebarLightTitle;
  pageBg: $page-bg;
  green: $green;
  yellow: $yellow;
  pink: $pink;
}
