<template>
  <div class="wrapper">
    <div class="search">
      <el-form
        ref="form"
        :model="form"
        inline
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <search-params-dialog
          :pvisible.sync="searchDialogVisible"
          @reset="handleReset"
          @search="handleSearch">
          <el-form
            ref="form"
            class="params-search-form"
            :model="form"
            label-width="80px"
            label-suffix=":"
            size="small"
            label-position="top"
            inline>
            <el-form-item label="实验样本 ">
              <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"/>
            </el-form-item>
            <el-form-item label="项目名称">
              <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"/>
            </el-form-item>
            <el-form-item label="文库修饰类型">
              <el-select v-model.trim="form.type" class="form-width" clearable placeholder="请输入">
                <el-option v-for="item in libModifyType" :key="item" :label="item" :value="item"/>
              </el-select>
            </el-form-item>
          </el-form>
        </search-params-dialog>
      </el-form>
      <div>
        <el-button plain type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button plain size="mini" @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button plain size="mini" type="primary" @click="searchDialogVisible = true">更多查询</el-button>
        </el-badge>
      </div>
    </div>
    <div class="content">
      <div class="title">样本列表</div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="项目名称" prop="projectName" min-width="120" show-overflow-tooltip/>
        <el-table-column label="实验样本" prop="sampleName" min-width="120" show-overflow-tooltip/>
        <el-table-column label="核酸/吉因加编号" prop="geneCode" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.geneCode !== '查看样本详情'">{{ scope.row.geneCode }}</span>
            <div v-else class="link" @click="handleDetail(scope.row)">{{ scope.row.geneCode }}</div>
          </template>
        </el-table-column>
        <el-table-column label="原始样本名称" prop="oldSampleName" min-width="120" show-overflow-tooltip/>
        <el-table-column label="工序流程" prop="processFlow" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="link" @click="handleShowDetail(scope.row)">{{ scope.row.processFlow }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户下单数据量/G" prop="orderDataVolumeSize" min-width="120" show-overflow-tooltip/>
        <el-table-column label="排单数据量/G" prop="dataVolumeSize" min-width="120" show-overflow-tooltip/>
        <el-table-column label="文库修饰类型" prop="libModificationType" min-width="120" show-overflow-tooltip/>
        <el-table-column label="文库类型" prop="libType" min-width="120" show-overflow-tooltip/>
        <el-table-column label="makeDNB" prop="makeDNB" min-width="120" show-overflow-tooltip/>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {getSampleInfoList} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'sampleList',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    taskId: {
      type: Number,
      default: null
    }
  },
  mounted () {
    this.$_setTbHeight(90 + 24 + 47 + 42 + 54 + 10 + 47 + 10)
  },
  data () {
    return {
      taskWidth: 39,
      sampleWidth: 59,
      searchDialogVisible: false,
      // 文库修饰类型
      libModifyType: [
        'illumina-未磷酸化',
        'illumina-已磷酸化',
        'MGI-未环化-单',
        'MGI-未环化-双',
        'illumina-已环化',
        'MGI-已环化'
      ],

      form: {},
      tableData: []
    }
  },
  methods: {
    // 查询
    handleSearch () {
      this.formSubmit = {...this.form}
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    // 重置
    handleReset () {
      this.form = {...this.$options.data().form}
      this.handleSearch()
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) {
        return '查看样本详情'
      }
      return code
    },
    getParams () {
      return {
        ftaskId: this.taskId,
        fprojectName: this.formSubmit.projectName,
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        flibModificationType: this.formSubmit.type,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getSampleInfoList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.rows || []
        const processFlow = {
          1: 'pooling',
          2: '转化',
          3: 'pooling',
          4: '环化',
          5: 'makeDNB'
        }
        rows.forEach(v => {
          const process = v.fprocessFlow || ''
          const flows = process.split(',')
          const rowText = flows.map(v => processFlow[v]).join('-')
          const item = {
            id: v.fid,
            taskId: v.ftaskId, // 任务单id，
            projectName: v.fprojectName, // 项目名称
            orderCode: v.forderCode, // 订单编号
            taskCode: v.ftaskCode, // 任务单编号
            geneCode: this.handleSetGeneCode(v), // 吉因加编号,
            geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
            geneInfo: {
              fgeneCode: v.fgeneCode,
              fnucleateCode: v.fnucleateCode
            },
            nucleateCode: v.fnucleateCode, // 核酸编码
            oldSampleName: v.foldSampleName, // 原始样本名称
            sampleName: v.fsampleName, // 样本名称
            status: v.fstatus, // 状态
            orderDataVolumeSize: v.forderDataVolumeSize, // 订单数据量大小
            dataVolumeSize: v.fdataVolumeSize, // 数据量大小
            libModificationType: v.flibModificationType, // 文库修饰类型
            libType: v.flibType, // 文库类型
            concentration: v.fconcentration, // 浓度
            volume: v.fvolume, // 体积
            processFlow: rowText, // 流程
            makeDNB: v.fmakeDnb, // makeDNB法
            productionArea: v.fproductionArea // 生产区域
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    async handleShowDetail (row) {
      const {id, taskId} = row
      await this.$store.dispatch('setSampleInfo', row)
      window.open('/business/subpage/sequencingManagement/sampleProcessDetail?sampleId=' + id + '&taskId=' + taskId, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;

  .search {
    display: flex;
    margin-top: 10px;
  }

  .btn-group {
    margin-bottom: 10px;
  }

  .content {
    .title {
      font-size: 14px;
      font-weight: bold;
      color: #000;
      margin: 5px 0;
    }
  }
}
</style>
