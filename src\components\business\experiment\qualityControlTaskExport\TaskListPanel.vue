<template>
  <div class="task-list-panel">
    <div class="search-area">
      <el-form size="mini" :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="任务单编号">
          <el-input v-model="searchForm.ftaskOrderNum" placeholder="请输入任务单编号" clearable></el-input>
        </el-form-item>
        <el-form-item label="任务单类型">
          <el-select v-model="searchForm.ftaskOrderTypeList" multiple collapse-tags placeholder="请选择任务单类型" clearable>
            <el-option v-for="item in taskTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务单时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            clearable>
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="task-list">
      <div class="table-header">
        <div class="title">任务单列表</div>
        <div class="batch-actions">
          <el-button type="primary" size="small" @click="handleBatchAdd" :disabled="selectedTasks.length === 0">批量添加</el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        border
        size="mini"
        style="width: 100%"
        :height="tbHeight"
        @selection-change="handleSelectionChange"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" min-width="55"></el-table-column>
        <el-table-column prop="ftaskOrderNum" label="任务单编号" min-width="120"></el-table-column>
        <el-table-column prop="ftaskOrderType" label="任务单类型" min-width="120"></el-table-column>
        <el-table-column prop="ftaskOrderNumCount" label="任务量" min-width="80"></el-table-column>
        <el-table-column prop="ftaskOrderStatus" label="任务单状态" min-width="100"></el-table-column>
        <el-table-column prop="ftaskCreateTime" label="创建时间" min-width="150"></el-table-column>
        <el-table-column label="操作" min-width="80" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click.stop="handleAdd(scope.row)">添加</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import mixins from '@/util/mixins'

export default {
  name: 'TaskListPanel',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    // 已添加的任务单ID数组，用于过滤
    addedTaskIds: {
      type: Array,
      default: () => []
    }
  },
  created () {
    this.getData()
  },
  watch: {
    // 监听已添加任务单ID变化，使用本地过滤而不是重新请求
    addedTaskIds: {
      handler () {
        this.updateLocalFiltering()
      },
      deep: true
    }
  },
  data () {
    // 获取7天前的日期
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    sevenDaysAgo.setHours(12, 0, 0, 0)

    // 获取当天23:59:59的日期
    const today = new Date()
    today.setHours(23, 59, 59, 999)

    return {
      searchForm: {
        ftaskOrderNum: '',
        ftaskOrderTypeList: [],
        dateRange: [sevenDaysAgo, today]
      },
      taskTypeOptions: [
        { value: '全基因组DNA提取', label: '全基因组DNA提取' },
        { value: 'FFPE DNA提取', label: 'FFPE DNA提取' },
        { value: '组织RNA提取', label: '组织RNA提取' },
        { value: '核酸检测', label: '核酸检测' }
      ],
      selectedTasks: [],
      // 存储所有的任务数据，用于本地过滤
      allTasksData: [],
      // 过滤后的数据（应用搜索条件和已添加过滤后）
      filteredData: []
    }
  },
  methods: {
    // mixins要求的getData方法
    getData () {
      this.loading = true

      // 使用mockData进行开发测试
      setTimeout(() => {
        // 获取所有模拟数据并保存
        this.allTasksData = this.generateMockData()

        // 应用过滤条件
        this.updateLocalFiltering()

        this.loading = false
      }, 800) // 模拟网络延迟
    },

    // 本地过滤数据，不重新请求
    updateLocalFiltering () {
      // 如果还没有获取过数据，不执行过滤
      if (!this.allTasksData || this.allTasksData.length === 0) return

      // 过滤掉已添加到右侧的任务单，并只显示待处理状态的任务单
      let filteredData = this.allTasksData.filter(item =>
        item.ftaskOrderStatus === '待处理' &&
        !this.addedTaskIds.includes(item.id)
      )

      // 根据搜索条件过滤数据
      if (this.searchForm.ftaskOrderNum) {
        filteredData = filteredData.filter(item =>
          item.ftaskOrderNum.includes(this.searchForm.ftaskOrderNum)
        )
      }

      if (this.searchForm.ftaskOrderTypeList.length > 0) {
        filteredData = filteredData.filter(item =>
          this.searchForm.ftaskOrderTypeList.includes(item.ftaskOrderType)
        )
      }

      // 保存过滤后的总数据
      this.filteredData = filteredData

      // 分页处理
      this.updatePagination()
    },

    // 更新分页数据
    updatePagination () {
      const startIndex = (this.currentPage - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      this.tableData = this.filteredData.slice(startIndex, endIndex)
      this.totalPage = this.filteredData.length
    },

    // 生成模拟数据
    generateMockData () {
      const taskTypes = ['全基因组DNA提取', 'FFPE DNA提取', '组织RNA提取', '核酸检测']
      const statuses = ['待处理', '处理中', '已完成']
      const mockData = []

      for (let i = 1; i <= 50; i++) {
        const taskType = taskTypes[Math.floor(Math.random() * taskTypes.length)]
        const status = i <= 35 ? '待处理' : statuses[Math.floor(Math.random() * statuses.length)] // 前35个设为待处理

        mockData.push({
          id: `task_${i}`,
          ftaskOrderId: `task_${i}`, // 确保ID字段一致
          ftaskOrderNum: `QC${String(i).padStart(6, '0')}`,
          ftaskOrderType: taskType,
          ftaskOrderNumCount: Math.floor(Math.random() * 50) + 1,
          ftaskOrderStatus: status,
          ftaskCreateTime: this.getRandomDate(),
          ftaskCreateUser: `操作员${Math.floor(Math.random() * 10) + 1}`,
          ftaskDescription: `${taskType}任务单描述信息`,
          ftaskPriority: Math.random() > 0.7 ? '高' : Math.random() > 0.4 ? '中' : '低'
        })
      }

      return mockData
    },

    // 生成随机日期（最近30天内）
    getRandomDate () {
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const randomTime = thirtyDaysAgo.getTime() + Math.random() * (now.getTime() - thirtyDaysAgo.getTime())
      return new Date(randomTime).toLocaleString('zh-CN')
    },

    // 搜索
    handleSearch () {
      this.currentPage = 1
      this.updateLocalFiltering()
    },

    // 重置搜索条件
    resetSearch () {
      // 获取7天前的日期
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      sevenDaysAgo.setHours(12, 0, 0, 0)

      // 获取当天23:59:59的日期
      const today = new Date()
      today.setHours(23, 59, 59, 999)

      this.searchForm = {
        ftaskOrderNum: '',
        ftaskOrderTypeList: [],
        dateRange: [sevenDaysAgo, today]
      }
      this.handleSearch()
    },

    // 表格选择变化
    handleSelectionChange (selection) {
      this.selectedTasks = selection
    },

    // 单个添加任务单
    handleAdd (row) {
      this.$emit('add-task', row)
      if (this.selectedRows.has(row.id)) {
        this.selectedRows.delete(row.id)
      }
      console.log('已添加的任务单：', this.selectedRows, this.selectedRows.has(row.id))
      // 从当前表格数据中移除
      this.tableData = this.tableData.filter(item => item.id !== row.id)
      // 已选择map移除
      this.selectedRows.delete(row.id)
    },

    // 批量添加任务单
    handleBatchAdd () {
      this.$emit('batch-add-tasks', this.selectedTasks)
      this.clearMap()
      // 从当前表格数据中移除已添加的任务单
      const addedIds = this.selectedTasks.map(task => task.id)
      addedIds.forEach(id => this.selectedRows.delete(id))
      this.tableData = this.tableData.filter(item => !addedIds.includes(item.id))
      // 清空选中状态
      this.$refs.table.clearSelection()
    },

    // 处理分页大小变化
    handleSizeChange (size) {
      this.pageSize = size
      this.updatePagination()
    },

    // 处理页码变化
    handleCurrentChange (page) {
      this.currentPage = page
      this.updatePagination()
    },

    // 刷新数据
    handleRefresh () {
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.task-list-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-area {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;

    ::v-deep .search-form {
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 20px;

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
        }

        .el-input {
          .el-input__inner {
            border-radius: 6px;
            transition: all 0.3s ease;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }
        }

        .el-select {
          .el-input__inner {
            border-radius: 6px;
          }
        }

        .el-date-editor {
          border-radius: 6px;
        }
      }

      .el-button {
        border-radius: 6px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.el-button--primary {
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }

        &:not(.el-button--primary) {
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .task-list {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f2f5;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background-color: #409eff;
        border-radius: 2px;
      }
    }

    .batch-actions {
      .el-button {
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.el-button--primary {
          &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          &:disabled {
            opacity: 0.6;
          }
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f2f5;
    text-align: right;

    ::v-deep .el-pagination {
      .el-pager li {
        border-radius: 4px;
        margin: 0 2px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }

      .btn-prev, .btn-next {
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }

  // 表格样式优化
  ::v-deep .el-table {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-table__header {
      th {
        background-color: #fafbfc;
        color: #606266;
        font-weight: 600;
        border-bottom: 2px solid #ebeef5;
      }
    }

    .el-table__body {
      tr {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f8f9ff;
        }

        td {
          border-bottom: 1px solid #f5f7fa;
        }
      }
    }

    .el-button--text {
      color: #409eff;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        color: #66b1ff;
        transform: translateY(-1px);
      }
    }
  }

  // 加载动画
  ::v-deep .el-loading-mask {
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.9);

    .el-loading-spinner {
      .path {
        stroke: #409eff;
      }

      .el-loading-text {
        color: #409eff;
        font-weight: 500;
      }
    }
  }
}
</style>
