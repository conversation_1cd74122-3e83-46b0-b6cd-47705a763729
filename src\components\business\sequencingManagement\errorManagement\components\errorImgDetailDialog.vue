<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      title="驳回说明"
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template>
          <el-form-item label="图片说明" @submit.native.prevent>
            <img
              :key="index"
              :src="pic"
              v-for="(pic, index) in pictures"
              alt="异常说明"
              style="width: 100px; height: 100px; margin: 2px; cursor: pointer;" @click="handlePictureCardPreview(pic)"/>
            <el-dialog  :visible.sync="dialogVisible" title="图片预览" width="500px" append-to-body>
              <div style="max-height: 500px; overflow: auto">
                <img :src="dialogImageUrl" width="100%" alt="">
              </div>
            </el-dialog>
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    pictures: {
      type: Array
    }
  },
  data () {
    return {
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  methods: {
    handlePictureCardPreview (url) {
      this.dialogImageUrl = url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped></style>
