<template>
  <div>
    <el-form :model="form" label-width="120px">
      <el-form-item props="gene" label="基因名称">{{form.gene}}</el-form-item>
      <el-form-item props="gene" label="遗传方式">
        <el-input v-model="form.geneticMode"  size="mini" placeholder="请输入遗传方式"></el-input>
      </el-form-item>
      <el-form-item props="relatedDisease" label="涉及疾病名称">
        <el-input v-model="form.relatedDisease"  size="mini" placeholder="请输入涉及疾病名称"></el-input>
        <el-input v-model="form.relatedDiseaseEn" size="mini" class="input-margin" placeholder="请输入涉及疾病名称(英文)"></el-input>
      </el-form-item>
      <el-form-item label="癌症类型">
        <div>{{form.cancerClass}}</div>
<!--        <el-input v-model="form.cancerClass" size="mini" placeholder="请输入癌症类型"></el-input>-->
        <el-input v-model="form.cancerClassEn" size="mini" class="input-margin" placeholder="请输入癌症类型(英文)"></el-input>
      </el-form-item>
      <el-form-item label="优先級">
        <el-select v-model="form.priority" size="mini" placeholder="请选择优先级">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="基因功能描述">
        <el-input v-model="form.geneDescription" :autosize="{ minRows: 4, maxRows: 4}" size="mini"  type="textarea" placeholder="请输入基因功能描述"></el-input>
        <el-input v-model="form.geneDescriptionEn" :autosize="{ minRows: 4, maxRows: 4}" size="mini"  type="textarea" class="input-margin" placeholder="请输入基因功能描述(英文)"></el-input>
      </el-form-item>
      <el-form-item label="疾病背景">
        <el-input v-model="form.geneBackground" :autosize="{ minRows: 8, maxRows: 8}" size="mini"  type="textarea" placeholder="请输入疾病背景"></el-input>
        <el-input v-model="form.geneBackgroundEn" :autosize="{ minRows: 8, maxRows: 8}" size="mini"  type="textarea" class="input-margin" placeholder="请输入疾病背景(英文)"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    pdata: {
      type: Object
    }
  },
  watch: {
    pdata: function () {
      this.init()
    }
  },
  data () {
    return {
      form: {}
    }
  },
  methods: {
    init () {
      console.log(this.pdata)
      this.form = this.pdata
    }
  }
}
</script>

<style scoped>
.input-margin {
  margin-top: 10px;
}
</style>
