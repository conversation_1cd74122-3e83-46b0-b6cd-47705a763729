<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      title="报告审核"
      width="80vw"
      @open="handleOpen"
    >
      <div class="flex">
        <div class="flex-item">已选报告： 共计1份</div>
        <div class="flex-item">报告类型： {{type}}</div>
      </div>

      <el-table
        ref="table"
        :data="tableData"
        :cell-style="{padding: 0, height: '24px'}"
        size="mini"
        class="qc-table"
        border
        style="width: 100%"
        height="400px">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="qcResult" label="质控结果" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="!isFix">{{scope.row.qcResult}}</span>
            <el-select
              v-model.trim="scope.row.qcResult"
              v-else
              size="mini"
              clearable
              filterable
              placeholder="请选择质控结果"
            >
              <el-option
                :key="index"
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in results"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="nucleateCode" label="核酸编号" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleType" label="样本类型" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskOrderType" label="任务单类型" min-width="100" show-overflow-tooltip></el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" v-if="qcReportType === 1" size="mini" type="primary" plain @click="handleClose">关 闭</el-button>
        <el-button :loading="loading" v-if="qcReportType === 2" size="mini" type="primary" plain @click="handleClose">取 消</el-button>
        <el-button :loading="loading" v-if="!isFix && qcReportType === 2" size="mini" type="primary" plain @click="handleFix">修 改</el-button>
        <el-button :loading="loading" v-if="!isFix && qcReportType === 2" size="mini" type="primary" plain @click="handleConfirm(20)">驳 回</el-button>
        <el-button :loading="loading" v-if="!isFix && qcReportType === 2" size="mini" type="primary" @click="handleConfirm(1)">通 过</el-button>
        <el-button :loading="loading" v-if="!isFix && qcReportType === 2" size="mini" type="primary" @click="handleSendReport">通过并发放报告</el-button>
        <el-button :loading="loading" v-if="isFix && qcReportType === 2" size="mini" @click="handleSave">保存</el-button>
      </span>

      <!--报告发送-->
      <send-report-dialog
        :pvisible.sync="sendReportVisible"
        :ids="[subOrderId]"
        :type="1"
        :report-name="type"
        :order-code="orderCode"
        @dialogConfirmEvent="handleClose"
        @dialogCloseEvent="handleClose"
      />
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util from '@/util/util'
import SendReportDialog from './sendReportDialog'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {
    SendReportDialog
  },
  props: {
    subOrderId: {
      type: Number
    },
    qcReportType: {
      type: Number
    },
    orderCode: {
      type: String
    },
    type: {
      type: String
    }
  },
  data () {
    return {
      isFix: false,
      ids: [],
      results: [
        {
          label: '1级',
          value: '1级'
        },
        {
          label: '2级',
          value: '2级'
        },
        {
          label: '3级',
          value: '3级'
        },
        {
          label: '4级',
          value: '4级'
        }
      ],
      loading: false,
      sendReportVisible: false,
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.isFix = false
      this.getData()
      this.handleFixResults()
    },
    // 修改质控列表选项
    handleFixResults () {
      if (this.type !== '文库报告') {
        this.results = [
          {
            label: '1级',
            value: '1级'
          },
          {
            label: '2级',
            value: '2级'
          },
          {
            label: '3级',
            value: '3级'
          },
          {
            label: '4级',
            value: '4级'
          }
        ]
        return
      }
      this.results = [
        {
          label: '合格',
          value: '合格'
        },
        {
          label: '不合格',
          value: '不合格'
        },
        {
          label: '风险',
          value: '风险'
        }
      ]
    },
    // 获取表格数据
    getData () {
      this.$ajax({
        url: '/order/report/get_qc_report_sample_list',
        data: {
          fid: this.subOrderId
        },
        loadingDom: '.qc-table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = []
          let data = res.data || []
          data.forEach((v) => {
            let item = {
              id: v.fid,
              qcResult: v.fqcResult,
              nucleateCode: v.fnucleateCode,
              sampleType: v.fsampleType,
              projectCode: v.fprojectCode,
              projectName: v.fprojectName,
              taskOrderType: v.ftaskOrderType
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 修改表格质控结果列为输入框
    handleFix () {
      this.isFix = true
    },
    async handleClose () {
      if (this.isFix) {
        await this.handleConfirmOption('是否放弃修改？“取消”后未保存的数据将丢失')
        this.isFix = false
        this.getData()
        return
      }
      this.visible = false
      this.$emit('dialogCloseEvent')
    },
    async handleConfirmOption (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    // 保存质控结果
    async handleSave () {
      if (this.tableData.some(v => !v.qcResult)) {
        this.$message.error('质控结果必填')
        return
      }
      let data = this.tableData.map(v => {
        return {
          fid: v.id,
          fqcResult: v.qcResult
        }
      })
      await this.handleConfirmOption('是否确认修改？')
      this.loading = true
      this.$ajax({
        url: '/order/report/save_qc_result',
        data: {
          freportId: this.subOrderId,
          resultBeanList: data
        },
        loadingDom: '.qc-table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.isFix = false
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    async handleConfirm (status) {
      let message = ''
      status === 1 ? message = '是否确认通过审核？' : message = '是否确认驳回报告？'
      await this.handleConfirmOption(message)
      this.loading = true
      this.$ajax({
        url: '/order/report/save_qc_report_status',
        data: {
          fidList: [this.subOrderId],
          fstatus: status
        },
        loadingDom: '.qc-table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          status === 1 ? message = '审核成功' : message = '驳回成功'
          this.$message.success(message)
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 发放报告
    async handleSendReport () {
      await this.handleConfirmOption('是否确认通过审核？')
      this.loading = true
      this.$ajax({
        url: '/order/report/save_qc_report_status',
        data: {
          fidList: [this.subOrderId],
          fstatus: 1
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('审核成功')
          this.sendReportVisible = true
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  margin: 0 0 10px;
  .flex-item {
    flex: 1;
  }
}
</style>
