<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="确认完成"
      width="80%"
      @open="handleOpen">
      <div v-loading="loading">
        <el-upload
          :on-success="handleOnSuccess"
          :on-error="handleOnError"
          :data="{orderNumber: orderId}"
          :auto-upload="true"
          :headers="headers"
          :limit="1"
          :file-list="fileList"
          :before-upload="handleBeforeUpload"
          :action="uploadUrl">
          <el-button type="primary" size="mini">批量导入</el-button>
        </el-upload>
        <el-table
          :data="tableData"
          ref="sampleTable"
          class="table"
          style="width: 100%;"
          height="400"
          @select="handleSelectTable"
          @select-all="handleSelectAll"
          @row-click="handleRowClick">
          <el-table-column :selectable="tableCanSelect" type="selection" width="55"></el-table-column>
          <el-table-column label="样本状态" width="100" prop="fsampleStatus"></el-table-column>
          <el-table-column label="样本编号" width="180" prop="fsampleNumber"></el-table-column>
          <el-table-column label="样本类型" min-width="220" prop="fsampleType"></el-table-column>
          <el-table-column label="管型" width="100" prop="ftubeType"></el-table-column>
          <el-table-column label="样本量" width="100" prop="fsampleAmount"></el-table-column>
          <el-table-column label="所属实验室" width="150" prop="flab"></el-table-column>
          <el-table-column label="存储温度" width="100" prop="ftemperature"></el-table-column>
          <el-table-column label="存储位置" min-width="180">
            <template slot-scope="scope">
              <el-input
                v-model.trim="currentEditPlace"
                v-if="scope.$index === currentEditIndex"
                size="mini"></el-input>
              <span v-else>{{scope.row.fsamplePlace}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="scope.$index !== currentEditIndex"
                size="mini"
                @click.stop="handleToModifyPlace(scope.$index,scope.row.fsamplePlace)">修改</el-button>
              <el-button
                v-else
                size="mini"
                type="primary"
                @click.stop="handleConfirmModifyPlace(scope.row)">确认</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="line-height: 2.5;">
          共{{tableData.length}}条,已选{{hasChooseNum}}条
        </div>
        <div style="padding: 10px 0;border-top: 1px solid #ccc;">
          <p style="font-weight: 600;line-height: 2.5;">确定已经存满的石蜡柜位置</p>
          <el-checkbox-group
                  v-model.trim="paraffinCabinetLocationCheck">
            <el-checkbox :label="v" :key="v" v-for="v in paraffinCabinetLocationLists">{{v}}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import constants from '../../../util/constants'
import mixins from '../../../util/mixins'
export default {
  name: 'confirmCompleteInnerLibraryApplicationDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    orderId: {
      type: String | Number
    }
  },
  data () {
    let loginId = util.getSessionInfo('loginId')
    let id = loginId ? util.decryptBase64(loginId) : ''
    return {
      uploadUrl: constants.JS_CONTEXT + '/sample/order/import_sample_place_order',
      headers: {
        'user-id': id
      },
      fileList: [],
      tableData: [],
      currentPosition: '',
      paraffinCabinetLocationLists: [],
      paraffinCabinetLocationCheck: [],
      selectedRows: new Map(),
      hasChooseNum: 0, // 已选行数
      loading: false,
      currentEditIndex: '', // 当前编辑表格的行数
      currentEditPlace: ''// 当前编辑的位置
    }
  },
  methods: {
    handleOpen () {
      this.paraffinCabinetLocationLists = []
      this.paraffinCabinetLocationCheck = []
      this.hasChooseNum = 0
      this.currentEditIndex = ''
      this.currentEditPlace = ''
      this.tableData = []
      if (this.orderId) {
        this.$ajax({
          url: '/sample/order/get_sample_list_by_order_number',
          method: 'get',
          data: {
            orderNumber: this.orderId
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.tableData = res.data || []
            this.$nextTick(() => {
              // this.$refs.sampleTable.toggleAllSelection()
              this.selectedRows.clear()
              this.tableData.forEach(item => {
                console.log(item.fid)
                this.$refs.sampleTable.toggleRowSelection(item, item.fsampleStatus === '处理中')
                if (item.fsampleStatus === '处理中') {
                  this.selectedRows.set(item.fid, item)
                }
              })
              this.hasChooseNum = this.tableData.length
              this.getParaffinCabinetLocationLists()
            })
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 可否选中
    tableCanSelect (row) {
      return row.fsampleStatus === '处理中'
    },
    // 提交成功回调
    handleOnSuccess (res) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let tableDataMap = new Map()
        this.tableData.forEach(v => {
          tableDataMap.set(v.fid, v)
        })
        let data = res.data || []
        data.forEach(v => {
          if (tableDataMap.has(v.fid)) {
            tableDataMap.get(v.fid).fsamplePlace = v.fsamplePlace
          }
        })
      } else {
        this.$showErrorDialog({
          tableData: res.data || []
        })
      }
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 8) {
          this.loading = false
          this.$message.error('文件大小超过限制，无法上传')
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
      this.hasChooseNum = this.selectedRows.size
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fid, row)
      })
      this.hasChooseNum = this.selectedRows.size
    },
    handleRowClick (row, c) {
      if (row.fsampleStatus === '处理中') {
        this.$refs.sampleTable.toggleRowSelection(row, !this.selectedRows.has(row.fid))
        this.handleSelectTable(undefined, row)
      }
    },
    // 修改
    handleToModifyPlace (index, data) {
      console.log(index, data)
      this.currentEditIndex = index
      this.currentEditPlace = data
    },
    // 确认修改
    handleConfirmModifyPlace (row) {
      if (!this.currentEditPlace) {
        this.$message.error('位置不能为空')
        return
      }
      if (this.currentEditPlace === row.fsamplePlace) {
        this.currentEditPlace = ''
        this.currentEditIndex = ''
        return
      }
      this.$ajax({
        url: 'sample/alter_sample_place',
        data: {
          sampleId: row.fid,
          fsamplePlace: this.currentEditPlace
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          row.fsamplePlace = res.data
          this.currentEditPlace = ''
          this.currentEditIndex = ''
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleFocus (row) {
      this.currentPosition = row.fsamplePlace
    },
    handleBlur (row) {
      if (!row.fsamplePlace) {
        row.fsamplePlace = this.currentPosition
      }
      this.getParaffinCabinetLocationLists()
    },
    // 获取石蜡柜位置列表
    getParaffinCabinetLocationLists () {
      this.paraffinCabinetLocationLists = []
      let dataSet = new Set()
      this.tableData.forEach(item => {
        if (item.fsamplePlace && item.fsampleType === '石蜡包埋组织') {
          dataSet.add(item.fsamplePlace)
        }
      })
      this.paraffinCabinetLocationLists.push(...dataSet)
      this.judgeChecked()
    },
    // 判断这个选中项是否存在于table中
    judgeChecked () {
      if (this.paraffinCabinetLocationCheck.length > 0) {
        this.paraffinCabinetLocationCheck = this.paraffinCabinetLocationCheck.filter(v => {
          return this.paraffinCabinetLocationLists.indexOf(v) > -1
        })
      }
    },
    // 确认
    handleDialogConfirm () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要提交的行')
        return
      }
      let data = [...this.selectedRows.values()]
      let hasAllSet = data.every(item => {
        return item.fsamplePlace
      })
      if (!hasAllSet) {
        this.$message.error('选择的行存在储存位置为空的情况')
        return
      }
      this.loading = true
      this.$ajax({
        url: '/sample/order/confirm_complete_order',
        data: {
          orderNumber: this.orderId,
          sampleInfoList: data,
          paraffinCabinetPlace: this.paraffinCabinetLocationCheck
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('提交成功')
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
