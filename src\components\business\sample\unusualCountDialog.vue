<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="异常样本"
      width="90%"
      @open="handleOpen"
    >
      <span slot="title" class="dialog-footer title-dialog">
        <span style="margin-right: 30px">异常样本</span>
        <el-form inline>
          <el-form-item label="标记异常时间">
            <el-date-picker
              v-model="time"
              size="mini"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              placeholder="选择日期时间"
              @change="getData"></el-date-picker>
          </el-form-item>
        </el-form>
        <el-button size="mini" type="primary" @click="getData">查询</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="downloadCountRecords">下载</el-button>
      </span>
      <!--表格-->
      <el-table
        ref="table"
        :data="tableData"
        :height="400"
        size="mini"
        class="reservationTable"
        style="width: 100%">
        <el-table-column prop="sampleNum" label="样例编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="makeupStatus" label="补录状态" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="proName" label="产品/项目名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="hospitalName" label="送检单位" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="doctor" label="送检医生" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="salesManName" label="销售" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="factotumName" label="事务" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="exceptionNote" label="异常备注" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="exceptionTime" label="标记异常时间" min-width="150" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'

export default {
  name: 'unusualCountDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    pvisible: {
      type: Boolean
    }
  },
  data () {
    return {
      loading: false,
      tableData: [],
      time: [],
      infoSupplement: {
        0: '未补录',
        1: '补录中',
        4: '审核通过',
        2: '驳回',
        3: '待审',
        7: '样本暂存'
      }
    }
  },
  methods: {
    handleOpen () {
      let today = new Date()
      let beforeDay = today.setDate(today.getDate() - 3).valueOf()
      beforeDay = util.dateFormatter(beforeDay)
      today = util.dateFormatter(new Date())
      this.time = [beforeDay, today]
      this.getData()
    },
    // 信息补录统计数据
    getData () {
      let time = this.time || []
      this.$ajax({
        url: '/sample/basic/get_exception_sample_list',
        method: 'post',
        loadingDom: '.reservationTable',
        data: {
          exceptionStartTime: time[0],
          exceptionEndTime: time[1],
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.tableData = []
          this.totalPage = res.data.total
          let data = res.data || {}
          let rows = data.rows || []
          rows.forEach(v => {
            let item = {
              sampleNum: v.sampleNum,
              name: v.name,
              makeupStatus: this.infoSupplement[v.clinicalMkSta],
              proName: v.proName,
              hospitalName: v.hospitalName,
              doctor: v.doctor,
              orderEntryPersonName: v.orderEntryPersonName,
              salesManName: v.salesManName,
              factotumName: v.factotumName,
              exceptionNote: v.fexceptionNote,
              exceptionTime: v.fexceptionTime
            }
            item.realDate = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    },
    // 下载记录
    downloadCountRecords () {
      this.loading = true
      let time = this.time || []
      this.$ajax({
        url: '/sample/basic/download_exception_sample',
        method: 'post',
        data: {
          exceptionStartTime: time[0],
          exceptionEndTime: time[1]
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>
.title-dialog {
  display: flex;
  align-items: center;
}
/deep/ .el-form-item {
   margin-bottom: 0 !important;
}
</style>
