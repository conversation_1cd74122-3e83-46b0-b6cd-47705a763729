<template>
  <div class="">
    <div class="params-search-form">
      <el-form v-model="form" size="mini" label-width="100px" label-suffix=":" @keyup.enter.native="handleSearch" inline>
        <el-form-item label="吉因加编号">
          <el-input v-model="form.geneCode" class="form-width" placeholder="请输入吉因加编号" clearable></el-input>
        </el-form-item>

        <el-form-item label="文库编号">
          <el-input v-model="form.libNum" class="form-width" placeholder="请输入文库编号" clearable></el-input>
        </el-form-item>

        <el-form-item label="样本原始名称">
          <el-input v-model="form.oriSampleName" class="form-width" placeholder="请输入样本原始名称" clearable></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div class="operate-wrapper">
      <div class="operate-btns-group">
        <el-button size="mini" type="primary" plain @click="handleSearch">查询</el-button>
        <el-button size="mini" type="primary" plain @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">高级查询</el-button>
        </el-badge>
      </div>
      <search-params-dialog
        :pvisible.sync="searchDialogVisible"
        @reset="handleResetAdvance"
        @search="handleSearch">
        <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline
        @keyup.enter.native="handleSearch">
        <el-form-item label="吉因加编号">
          <el-input v-model="form.geneCode" class="form-width" placeholder="请输入吉因加编号" clearable></el-input>
        </el-form-item>

        <el-form-item label="文库编号">
          <el-input v-model="form.libNum" class="form-width" placeholder="请输入文库编号" clearable></el-input>
        </el-form-item>

        <el-form-item label="样本原始名称">
          <el-input v-model="form.oriSampleName" class="form-width" placeholder="请输入样本原始名称" clearable></el-input>
        </el-form-item>

        <el-form-item label="项目编号">
          <el-input v-model="form.projectCode" class="form-width" placeholder="请输入项目编号" clearable></el-input>
        </el-form-item>

        <el-form-item label="订单编号">
          <el-input v-model="form.orderCode" class="form-width" placeholder="请输入订单编号" clearable></el-input>
        </el-form-item>

        <el-form-item label="交付状态">
          <el-select v-model="form.deliverStatus" class="form-width" placeholder="请选择交付状态" clearable>
            <el-option v-for="(key, value) in orderStatusOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作人">
          <el-input v-model="form.deliverCreator" class="form-width" placeholder="请输入操作人" clearable></el-input>
        </el-form-item>

        <el-form-item label="交付订单">
          <el-input v-model="form.deliverOrderCode" class="form-width" placeholder="请输入交付订单" clearable></el-input>
        </el-form-item>

        <el-form-item label="交付时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="下达时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        </el-form>
      </search-params-dialog>
    </div>

    <div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :row-class-name="handleClassName"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="expand" width="30">
          <template slot-scope="props">
            <div style="margin-bottom: 10px">
              <!-- ���订单列表 -->
              <sub-order-list :ref="props.row.id" :id="props.row.id" :params="params"
                              @subOrderSelectedChange="handleSubOrderSelectedChange"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55"/>
        <el-table-column label="订单编号" prop="orderCode" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.orderCode" class="link"
                  @click="handleToOrderDetail(scope.row)">{{ scope.row.orderCode }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="交付订单" prop="cosQualityDeliverCode" min-width="120" show-overflow-tooltip/>
        <el-table-column label="交付方式" prop="deliverTypeText" min-width="80" show-overflow-tooltip/>
        <el-table-column label="交付状态" prop="deliverStatusText" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="+scope.row.deliverStatus === 1" style="color: #409EFF">{{ scope.row.deliverStatusText }}</span>
            <span v-else-if="+scope.row.deliverStatus === 2" style="color: #67C23A">{{ scope.row.deliverStatusText }}</span>
            <span v-else-if="+scope.row.deliverStatus === 4 || +scope.row.deliverStatus === 3" style="color: red">
              {{ scope.row.deliverStatusText }}
              <el-tooltip
                v-if="scope.row.note"
                placement="top"
                effect="dark"
                :content="scope.row.note"
              >
                <i class="el-icon-info"></i>
              </el-tooltip>
            </span>
            <span v-else>{{ scope.row.deliverStatusText }}</span>
          </template>
        </el-table-column>
        <el-table-column label="交付时间" prop="deliverTime" min-width="140" show-overflow-tooltip/>
        <el-table-column label="交付链接" prop="deliverLink" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.realData.deliverLink" class="link"
                  @click="handleOpenLink(scope.row.deliverLink)">
                  <el-tooltip class="item" effect="dark" content="点击复制" placement="right-start">
                    <span @click="handleCopy(scope.row.deliverLink)">{{scope.row.deliverLink}}</span>
                  </el-tooltip>

                </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="交付清单" prop="deliverList" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.deliverList">质控报告</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="下达时间" prop="syncTime" min-width="140" show-overflow-tooltip/>
      </el-table>

      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util, {awaitWrap, computeObjectValidKeyNum} from '@/util/util'
import {getDeliverOrderList} from '@/api/sequencingManagement/singleCell/index'
import subOrderList from './components/SubOrderList'

export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    subOrderList
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.params-search-form')
    this.handleSearch()
  },
  computed: {
    searchParamsKeyNum () {
      return computeObjectValidKeyNum(this.advanceForm, [])
    }
  },
  data () {
    return {
      orderType: '',
      params: {},
      searchDialogVisible: false,
      orderDetailDialogVisible: false,
      signDataVisible: false,
      dataCountVisible: false,
      subOrderLength: 0,
      loading: false,
      tableData: [],
      ids: [], // 选中订单id列表
      subOrderIds: [], // 选中子订单id列表
      placeholder: '请输入, 批量查询用逗号分隔，不区分中英文逗号， 或直接粘粘Excel中整行或整列数据',
      orderStatusOptions: { // 订单状态
        0: '未交付',
        1: '已下达',
        2: '已交付',
        3: '下达失败',
        4: '交付失败'
      },
      orderTagOptions: {
        0: '监控',
        1: '不监控'
      },
      addTestingOrderOptions: {
        0: '否',
        1: '是'
      },
      deliveryMethods: {
        1: '手动交付',
        0: '自动交付'
      },
      exportLoading: false,
      subOrderSelectRow: new Map(),
      advanceForm: {
        allSample: false,
        orderCode: '',
        projectName: '',
        orderStatus: '',
        geneCode: '',
        chipNum: '',
        sampleName: '',
        dataQc: '',
        time: [],
        deliveryTime: [],
        deliverBatchCode: '',
        deliverOrderCode: '',
        orderStatusList: [],
        subLibStatusList: [],
        orderTagList: []
      },
      form: {
        orderCode: '',
        projectName: '',
        orderStatus: '',
        projectCode: '',
        geneCode: '',
        chipNum: '',
        experimentTypeList: [],
        deliverTypeList: [],
        deliveryMethodList: [],
        orderStatusList: [],
        subLibStatusList: []
      }
    }
  },
  methods: {
    formatter (data = '') {
      return data.replace(/，/g, ',').replace(/\n/g, ',').replace(/\s+/g, ',').split(',')
    },
    // 设置查询参数
    setParams () {
      const time = this.form.time || []
      const issueTime = this.form.issueTime || []
      return {
        fgeneNumList: util.setGroupData(this.form.geneCode, '、', false),
        fcosSampleNameList: util.setGroupData(this.form.oriSampleName, '、', false),
        flibNum: this.form.libNum || '',
        fprojectCode: this.form.projectCode || '',
        fcosOrderCode: this.form.orderCode || '',
        fdeliverType: this.form.deliverType || '',
        fdeliverTimeStart: time[0] || '',
        fdeliverTimeEnd: time[1] || '',
        fdeliverOrderIssueTimeStart: issueTime[0] || '',
        fdeliverOrderIssueTimeEnd: issueTime[1] || '',
        fdeliverCreator: this.form.deliverCreator || '',
        // 交付订单
        fcosQualityDeliverCode: this.form.deliverOrderCode,
        // 交付状态
        fdeliverStatus: this.form.deliverStatus,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    // 查询
    async getData () {
      const {res} = await awaitWrap(getDeliverOrderList(this.setParams(), {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        const records = data.records || []
        this.totalPage = data.total || 0
        this.tableData = []
        this.clearMap()
        records.forEach(v => {
          const item = {
            id: v.fid,
            orderCode: v.fcosOrderCode,
            cosQualityDeliverCode: v.fcosQualityDeliverCode,
            deliverType: v.fdeliverType,
            deliverTypeText: this.deliveryMethods[v.fdeliverType],
            deliverStatus: v.fdeliverStatus,
            deliverStatusText: this.orderStatusOptions[v.fdeliverStatus],
            deliverTime: v.fdeliverTime,
            deliverLink: v.fdeliverLink,
            orderId: v.fcosOrderId,
            deliverDetail: v.fdeliverDetail || '质控报告',
            syncTime: v.fsyncTime,
            note: v.fnote,
            creator: v.fcreator
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    handleSearch () {
      this.formSubmit = JSON.parse(JSON.stringify(this.form))
      this.advanceFormSubmit = JSON.parse(JSON.stringify(this.advanceForm))
      this.currentPage = 1
      this.getData()
    },
    handleResetAdvance () {
      this.advanceForm = this.$options.data().advanceForm
      this.handleSearch()
    },
    handleReset () {
      this.form = this.$options.data().form
      this.advanceForm = this.$options.data().advanceForm
      this.subOrderSelectRow = new Map()
      this.handleSearch()
    },
    // 展开表格
    handleExpandChange (row, expandedRows) {
      if (expandedRows.length > 0) {
        this.params = this.setParams()
        // 处理全部样本
        if (this.advanceForm.allSample) {
          this.params.ffcNumList = []
        }
        this.params.pageVO = null
        this.$nextTick(async () => {
          await this.$refs[row.id].getData(row.id)
          if (this.$refs.table) {
            if (this.$refs.table) this.$refs.table.doLayout()
          }
        })
      }
    },
    handleToOrderDetail (row) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: 2, // type 1编辑 2 只读
          orderId: row.orderId,
          status: 2,
          code: row.orderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.cosOrderType === '1') path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.cosOrderType === '2') path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.cosOrderType === '3') path = '/business/subpage/technologyService/entryTissueOrder'
      if (row.cosOrderType === '5') path = '/business/subpage/technologyService/singleCell'
      if (path) util.openNewPage(path)
    },
    // 过滤出订单未选中的子订单
    getSubOrder () {
      const ids = [...this.selectedRows.keys()]
      const subOrderList = []
      this.subOrderSelectRow.forEach((value, key) => {
        if (!ids.includes(key)) {
          subOrderList.push(value)
        }
      })
      return subOrderList.flat()
    },
    handleCopy (text) {
      util.copyText(text).then(() => {
        this.$message.success('已复制到剪贴板')
      })
    },
    // 子订单选择改变
    handleSubOrderSelectedChange ({key, data}) {
      this.subOrderSelectRow.set(key, data)
    }
  }
}
</script>

<style scoped lang="scss">
.operate-wrapper {
  display: flex;
  align-items: center;

  .btn-wrapper {
    margin-left: 20px;
  }
}

.blue {
  color: $color
}

.green {
  color: $success-color
}

.red {
  color: $fail-color
}
</style>
