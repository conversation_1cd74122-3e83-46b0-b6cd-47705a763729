<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      title="批量审核"
      width="500px"
      @open="handleOpen"
    >
      <el-form
        ref="form"
        label-width="100px"
        label-suffix=":"
        size="mini"
        >
        <el-form-item label="已选报告">
          共计{{ids.length}}份
        </el-form-item>
        <el-form-item label="审核结果">
          <el-button :plain="active !== 1" type="primary" size="mini" @click="handleAudit(1)">通过</el-button>
          <el-button :plain="active !== 20" type="primary" size="mini" @click="handleAudit(20)">驳回</el-button>
          <el-button :plain="active !== 0" type="primary" size="mini" @click="handleAudit(0)">通过并发放报告</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>

      <!--报告发送-->
      <send-report-dialog
        :pvisible.sync="sendReportVisible"
        :ids="ids"
        :type="1"
        :report-name="reportName"
        :order-code="orderCode"
        :project-code="projectCode"
        :project-name="projectName"
        @dialogConfirmEvent="handleSendEmail"
        @dialogCloseEvent="handleSendEmail"
      />
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import SendReportDialog from './sendReportDialog'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {
    SendReportDialog
  },
  props: {
    ids: {
      type: Array
    },
    reportName: {
      type: String
    },
    projectInfos: {
      type: Array
    },
    orderCode: {
      type: String
    }
  },
  data () {
    return {
      loading: false,
      active: null,
      projectCode: '',
      projectName: '',
      sendReportVisible: false
    }
  },
  methods: {
    handleOpen () {
      this.active = null
    },
    handleAudit (active) {
      this.active = active
    },
    async handleSendReport () {
      // 则系统需要校验当前用户所选的记录其“项目编号”是否相同
      this.projectCode = this.projectInfos[0].projectCode
      if (this.projectInfos.some(v => v.projectCode !== this.projectCode)) {
        this.$message.error('所选报告不属于同一个项目，无法一起发放')
        return
      }
      let orderCode = this.projectInfos[0].orderCode
      if (this.projectInfos.some(v => v.orderCode !== orderCode)) {
        this.$message.error('所选报告不属于同一个订单，无法一起发放')
        return
      }
      this.projectName = this.projectInfos[0].projectName
      await this.handleConfirmOption('是否确认通过审核？')
      this.loading = true
      this.$ajax({
        url: '/order/report/save_qc_report_status',
        data: {
          fidList: this.ids,
          fstatus: 1
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('审核成功')
          this.visible = false
          this.sendReportVisible = true
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    async handleConfirmOption (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    handleSendEmail () {
      this.visible = false
      this.$emit('dialogConfirmEvent')
    },
    async handleConfirm () {
      if (!this.active && this.active !== 0) {
        this.$message.error('请选择审核结果')
        return
      }
      if (!this.active) {
        await this.handleSendReport()
        return
      }
      let message = ''
      this.active === 1 ? message = '是否确认通过审核？' : message = '是否确认驳回报告？'
      await this.handleConfirmOption(message)
      this.loading = true
      this.$ajax({
        url: '/order/report/save_qc_report_status',
        data: {
          fidList: this.ids,
          fstatus: this.active
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.active === 1 ? message = '审核成功' : message = '驳回成功'
          this.$message.success(message)
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }

}
</script>

<style scoped></style>
