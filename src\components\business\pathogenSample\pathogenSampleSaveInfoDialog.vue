<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose" width="98%"
      top="20px"
      @open="handleOpen">
      <div class="infoContent">
        <div class="left">
          <!--  样本基本信息-->
          <sample-basic-info :ref="sampleBasicInfo" :sample-basic-info="sampleBasicInfo" :is-disabled="isDisabled" style="margin-bottom: 50px" @formChange="handleBasicInfoChange"/>
          <h4 class="model-title">临床信息</h4>
          <!--  临床信息-->
          <sample-clinic-info ref="sampleClinicInfo" :pathogen-clinical="pathogenClinical" :is-disabled="isDisabled" @formChange="handleClinicInfoChange"/>
        </div>
        <div class="right">
<!--          <sample-img-info :sample-basic-id="sampleBasicId"-->
<!--                           :img-src="imgSrc"-->
<!--                           :img-type="imgType"-->
<!--                           :img-names="imgNames"-->
<!--                           :sample-order-img="sampleOrderImg"-->
<!--                           @fixImgEvent="handleFixImg"></sample-img-info>-->
          <etiology-ocr
              :sample-basic-id="sampleBasicId"
              :img-src="imgSrc"
              :img-type="imgType"
              :img-names="imgNames"
              :sample-order-img="sampleOrderImg"
              :title="title"
              @fixImgEvent="handleFixImg"
          >

          </etiology-ocr>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <template v-if="type === 1">
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(1)">保存</el-button>
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(3)">补录完成</el-button>
          <el-button type="danger" size="mini" @click="handleClose">关闭</el-button>
        </template>
        <template v-else-if="type === 2">
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(4)">审核通过</el-button>
          <el-button type="danger" size="mini" @click="handleReject">驳回</el-button>
        </template>
        <!--修改存疑按钮组-->
        <template v-else-if="type === 3">
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(3)">保存</el-button>
          <el-button type="danger" size="mini" @click="handleClose">关闭</el-button>
        </template>
      </span>
    </el-dialog>
    <picture-info-save-dialog
      :pvisible.sync="pictureInfoSaveDialogVisible" :pdata="pictureInfoSaveDialogData" @pictureInfoSaveDialogConfirmEvent="handlePictureInfoSaveDialogConfirm"
    ></picture-info-save-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import sampleBasicInfo from './sampleBasicInfo' // 基本信息
import sampleClinicInfo from './sampleClinicInfo' // 临床信息
import sampleImgInfo from './sampleImgInfo'
import pictureInfoSaveDialog from '../sample/clinicalInfoManagementPictureInfoSaveDialog'
import axios from 'axios'
import etiologyOcr from '../sample/etiologyOcr.vue'
// import {setDefaultEmptyValueForObject} from 'util'

export default {
  name: 'pathogenSampleSaveInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    etiologyOcr,
    sampleBasicInfo,
    sampleClinicInfo,
    sampleImgInfo,
    pictureInfoSaveDialog
  },
  props: {
    type: {
      type: Number,
      default: 0,
      required: true
    },
    sampleBasicId: {
      type: [Number, String],
      default: '',
      required: true
    },
    sampleNum: {
      type: String,
      default: '',
      required: true
    }
  },
  watch: {},
  computed: {},
  data () {
    return {
      title: '',
      saveLoading: false,
      isDisabled: false, // 是否禁用：查看详情页禁用 补录，存疑不禁用
      sampleBasicInfo: {}, // 样本基本信息
      pathogenClinical: {},
      keepLoginTimer: null,
      imgType: 'sampleOrderImg',
      imgSrc: '',
      imgNames: [], // 临床资料
      sampleOrderImg: [], // 送检单资源
      pictureInfoSaveDialogVisible: false,
      pictureInfoSaveDialogData: {}
    }
  },
  methods: {
    handleOpen () {
      this.keepLogin()
      this.$nextTick(() => {
        this.CancelToken = axios.CancelToken
        this.axiosSource = this.CancelToken.source()
        this.saveLoading = false
        this.activeNames = '1'
        let disabled = false
        switch (this.type) {
          case 1:
            this.title = '信息补录-' + this.sampleNum
            break
          case 2:
            this.title = '信息审核-' + this.sampleNum
            disabled = true
            break
          case 3:
            this.title = '存疑修改-' + this.sampleNum
            break
        }
        this.isDisabled = disabled
        this.getBasicInfoData()
      })
    },
    /**
     * 这个弹窗的数据会很多，所以用户填写时间会很长
     * 现在打开这个弹窗后就开始计时，20分钟发一次请求
     * 用来保证只要这个弹窗开着，登录永远不会失效
     */
    keepLogin () {
      this.keepLoginTimer = setInterval(() => {
        this.$ajax({url: '/user/live_token'})
      }, 20 * 60 * 1000)
    },
    // 基础信息绑定
    handleBasicInfoChange (basicInfo) {
      this.sampleBasicInfo = basicInfo
    },
    // 临床信息绑定
    handleClinicInfoChange (ClinicInfo) {
      this.pathogenClinical = ClinicInfo
    },
    // 保存
    handleConfirm (status = null, needSync = 0) {
      this.$refs[Object.keys(this.$refs)[0]].$refs.form.validate(async valid => {
        if (valid) {
          let pathogenClinical = this.pathogenClinical
          let data = {
            needSync: needSync,
            supplementSampleInformationRequest: {
              sampleBasicId: this.sampleBasicId,
              name: this.sampleBasicInfo.name,
              sex: this.sampleBasicInfo.sex,
              idcard: this.sampleBasicInfo.identityNumber,
              cardType: this.sampleBasicInfo.cardType,
              birthday: this.sampleBasicInfo.birthday,
              inspectionUnit: this.sampleBasicInfo.inspectionUnitId,
              inspectionTime: this.sampleBasicInfo.inspectDate,
              contactTel: this.sampleBasicInfo.phone,
              // insertOffice: this.sampleBasicInfo.departments,
              fadmissionNum: this.sampleBasicInfo.admissionNum,
              fbedNum: this.sampleBasicInfo.bedNum,
              sampleOrderUrl: JSON.stringify(this.sampleOrderImg),
              insertDoctor: this.sampleBasicInfo.insertDoctor,
              fmedicalRecordNumber: this.sampleBasicInfo.medicalRecordNumber,
              fspecimenNo: this.sampleBasicInfo.specimenNo
            },
            saveClinicalOtherInfoRequest: {
              clinicalMkSta: status
            },
            sampleClinical: {
              sampleBasicId: this.sampleBasicId,
              sampleClinicalId: this.sampleClinicalId,
              fage: this.sampleBasicInfo.age
            },
            clinicalPathogeny: {
              fid: pathogenClinical.fid,
              fsampleBasicId: this.sampleBasicId,
              fclinicalDiagnose: pathogenClinical.clinicDisease, // 临床疾病
              finfectionSymptom: pathogenClinical.symptomDesc, // 感染症状描述
              fwhiteBloodCellCount: pathogenClinical.WBC, // 白细胞计数
              flymphocyteCount: pathogenClinical.LYM, // 淋巴细胞
              fneutrophilRatio: pathogenClinical.N, // 中粒性细胞
              fcReactiveProtein: pathogenClinical.CRP, // c反应蛋白
              fprocalcitonin: pathogenClinical.PCT, // 降钙素原
              fisCulture: pathogenClinical.cultivateResult, // 培养结果
              fcultureResults: pathogenClinical.cultivateResultNote,
              fisMicroscopic: pathogenClinical.mirrorResult, // 镜像结果
              fmicroscopicResults: pathogenClinical.mirrorResultNote,
              fisGlucanDetection: pathogenClinical.GTest, // 1,3-β-D-葡聚糖检测（G实验）
              fglucanDetection: pathogenClinical.GTestNote,
              fisGalactomannanDetection: pathogenClinical.GMTest, // 半乳糖甘露醇聚糖抗原检测（GM实验）
              fgalactomannanDetection: pathogenClinical.GMTestNote,
              fisPcr: pathogenClinical.PCR, // PCR
              fpcr: pathogenClinical.PCRNote,
              fisAntibody: pathogenClinical.antigen, // 抗原/抗体
              fantibody: pathogenClinical.antigenNote,
              fisOthersResult: pathogenClinical.otherResult, // 其他检测结果
              fothersResult: pathogenClinical.otherResultNote,
              ffocusPathogens: pathogenClinical.focusPathogens, // 重点关注病原
              fantiInfectiveMedication: pathogenClinical.medicalInfo, // 抗感染用药信息
              flastDrugsTime: pathogenClinical.lastMedicalInfo, // 送检前最近一次用抗感染药时间
              ftreatmentStartTime: pathogenClinical.startCue // 开始治疗时间
            }
          }
          if (this.type === 3 && needSync !== 1) {
            this.$prompt('请输入存疑备注', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputValidator: function (value) {
                if (!value || !value.trim()) {
                  return '请输入存疑备注'
                }
              }
            }).then(({value}) => {
              data.saveClinicalOtherInfoRequest.doubtRemark = value
              data.supplementSampleInformationRequest.doubtRemark = value
              this.submitSampleInfo(needSync, data)
            }).catch(() => {
            })
          } else {
            if (this.type === 1 || needSync === 1) {
              this.submitSampleInfo(needSync, data)
            } else {
              data = {
                sampleBasicId: this.sampleBasicId,
                clinicalMkSta: 4,
                makeupStatus: 4,
                rejectRemark: ''
              }
              this.audit(status, data)
            }
          }
        } else {
          this.$message.error('请填写必填项内容')
        }
      })
    },
    // 关闭弹窗
    handleClose () {
      this.visible = false
      clearInterval(this.keepLoginTimer)
      this.keepLoginTimer = null
      this.$emit('dialogCloseEvent')
      if (this.axiosSource) {
        this.axiosSource.cancel('手动取消')
        this.axiosSource = null
        this.CancelToken = null
      }
    },
    // 图片回调
    handleFixImg (data) {
      this.pictureInfoSaveDialogData = {
        type: data.pictureInfoSaveDialogData.type,
        sampleBasicId: data.pictureInfoSaveDialogData.sampleBasicId,
        tableData: data.pictureInfoSaveDialogData.tableData
      }
      this.pictureInfoSaveDialogVisible = data.pictureInfoSaveDialogVisible
    },
    // 编辑完成图片信息后函数
    handlePictureInfoSaveDialogConfirm (type, data = []) {
      this.pictureInfoSaveDialogVisible = false
      this[type] = data
      this.imgSrc = this[type].length > 0 ? this[type][0].fileAbsolutePath : ''
    },
    // 樣本审核
    audit (status, data) {
      this.saveLoading = true
      this.$ajax({
        url: '/sample/basic/audit_sample_info',
        data: data,
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          if (status === 1) {
            this.getBasicInfoData()
          } else {
            this.$emit('saveInfoDialogConfirmEvent')
          }
        } else {
          this.$message.error(result.message)
        }
      }).catch().finally(() => {
        this.saveLoading = false
      })
    },
    // 审核驳回
    handleReject () {
      this.$prompt('请输入驳回备注', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: function (value) {
          if (value) {
            if (value.length > 50) {
              return '驳回备注50字以内'
            }
          } else {
            return '请输入驳回备注'
          }
        }
      }).then(({ value }) => {
        this.$ajax({
          url: '/sample/basic/audit_sample_info',
          data: {
            sampleBasicId: this.sampleBasicId,
            clinicalMkSta: 2,
            makeupStatus: 2,
            rejectRemark: value
          },
          cancelToken: this.axiosSource.token
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('操作成功！')
            this.$emit('saveInfoDialogConfirmEvent')
          } else {
            this.$message.error(result.message)
          }
        })
      }).catch()
    },
    // 样本提交
    submitSampleInfo (needSync, data) {
      this.saveLoading = true
      this.$ajax({
        url: '/sample/basic/save_sample',
        data: data,
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          if (needSync === 1) {
            this.$message.success('信息同步成功')
          } else {
            this.$message.success('保存成功')
            this.$emit('saveInfoDialogConfirmEvent')
          }
        } else {
          this.$message.error(result.message)
        }
      }).catch().finally(() => {
        this.saveLoading = false
      })
    },
    // 获取信息补录基本字段
    getBasicInfoData () {
      this.$ajax({
        loadingDom: '.infoContent',
        url: '/sample/basic/get_clinical_info',
        data: {
          sampleBasicId: this.sampleBasicId,
          sampleNum: this.sampleNum
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          console.log('data', data)
          let pathogenClinical = data.pathogenyClinical || {}
          if (data && JSON.stringify(data) !== '{}') {
            try {
              this.sampleBasicInfo = {
                sampleCode: this.sampleNum,
                productName: data.productNames || data.projectName,
                sampleType: data.basic.sampleType,
                sampleSize: data.basic.sampleCount,
                collectDate: data.basic.sampleCollectDate,
                inspectDate: data.inspection.inspectionTime,
                sampleQuality: data.basic.sampleQualify,
                receiverDate: data.basic.sampleConfirmTime,
                name: data.basic.name,
                age: data.basic.fage,
                sex: data.basic.sex,
                identityNumber: data.basic.idcard,
                cardType: data.basic.cardType,
                birthday: data.basic.birthday,
                admissionNum: data.basic.fadmissionNum, // 住院号
                bedNum: data.basic.fbedNum, // 床号
                phone: data.basic.contactTel, // 联系电话
                inspectionDepart: data.inspection.inspectionUnitName, // 送检单位
                inspectionUnitId: data.inspection.inspectionUnitId,
                insertOffice: data.inspection.departments, // 送检科室
                insertDoctor: data.inspection.doctor, // 送检医生
                doctorCode: data.inspection.doctorCode,
                medicalRecordNumber: data.inspection.fmedicalRecordNumber,
                specimenNo: data.inspection.fspecimenNo
              } // 基础信息
              // this.$set(this.sampleBasicInfo, 'realData', JSON.parse(JSON.stringify(this.sampleBasicInfo)))
              // setDefaultEmptyValueForObject(this.sampleBasicInfo)
              this.pathogenClinical = {
                fid: pathogenClinical.fid,
                clinicDisease: pathogenClinical.fclinicalDiagnose, // 临床疾病
                symptomDesc: pathogenClinical.finfectionSymptom, // 感染症状描述
                WBC: pathogenClinical.fwhiteBloodCellCount, // 白细胞计数
                LYM: pathogenClinical.flymphocyteCount, // 淋巴细胞
                N: pathogenClinical.fneutrophilRatio, // 中粒性细胞
                CRP: pathogenClinical.fcReactiveProtein, // c反应蛋白
                PCT: pathogenClinical.fprocalcitonin, // 降钙素原
                cultivateResult: pathogenClinical.fisCulture || 0, // 培养结果
                mirrorResult: pathogenClinical.fisMicroscopic || 0, // 镜像结果
                GTest: pathogenClinical.fisGlucanDetection || 0, // 1,3-β-D-葡聚糖检测（G实验）
                GMTest: pathogenClinical.fisGalactomannanDetection || 0, // 半乳糖甘露醇聚糖抗原检测（GM实验）
                PCR: pathogenClinical.fisPcr || 0, // PCR
                antigen: pathogenClinical.fisAntibody || 0, // 抗原/抗体
                otherResult: pathogenClinical.fisOthersResult || 0, // 其他检测结果
                cultivateResultNote: pathogenClinical.fcultureResults,
                mirrorResultNote: pathogenClinical.fmicroscopicResults,
                GTestNote: pathogenClinical.fglucanDetection,
                GMTestNote: pathogenClinical.fgalactomannanDetection,
                PCRNote: pathogenClinical.fpcr,
                antigenNote: pathogenClinical.fantibody,
                otherResultNote: pathogenClinical.fothersResult,
                focusPathogens: pathogenClinical.ffocusPathogens, // 重点关注病原
                medicalInfo: pathogenClinical.fantiInfectiveMedication, // 抗感染用药信息
                startCue: pathogenClinical.ftreatmentStartTime, // 开始治疗时间
                lastMedicalInfo: pathogenClinical.flastDrugsTime // 送检前最近一次用抗感染药时间
              }
              // this.$set(this.pathogenClinical, 'realData', JSON.parse(JSON.stringify(this.pathogenClinical)))
              // setDefaultEmptyValueForObject(this.pathogenClinical)
              this.sampleClinicalId = data.clinical.sampleClinicalId
              this.imgNames = data.imgNames || []
              this.sampleOrderImg = data.inspection.fileInfo || []
              this.imgSrc = this.sampleOrderImg.length === 0 ? '' : this.sampleOrderImg[0].fileAbsolutePath
              this.imgType = 'sampleOrderImg'
            } catch (e) {
              console.log(e)
            }
          }
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
>>>.el-dialog{
  margin: 0 20px 20px;
}
.model-title {
  border-left: 3px solid deepskyblue;
  padding: 0 10px;
  margin: 10px 0;
}
.infoContent{
  height: calc(100vh - 20px - 40px - 44px - 53px - 90px);
  display: flex;
  .left{
    padding: 0 5px;
    width: 55%;
    overflow-y: auto;
  }
  .right{
    border-left: 1px solid #DCDFE6;
    padding: 0 5px;
    flex: 1;
    overflow-y: auto;
    height: 100%;
    .picture{
      margin: 10px auto;
      width: 90%;
      height: calc(100% - 54px - 28px - 20px - 40px);
      .image{
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        overflow: auto;
      }
    }
    .title{
      height: 30px;
      line-height: 30px;
      font-size: 13px;
    }
  }
}
/*去除输入框圆角*/
/deep/ .el-input__inner {
  border-radius: 0!important;
}
/*设置相对定位作为错误提示的父级盒子&&解决输入框因为行高问题导致下移*/
/deep/ .el-form-item__content {
  position: relative;
}
/*表单项宽度*/
/deep/ .el-form-item {
  width: 48%;
}
/*调整错误提示的位置*/
/deep/ .el-form-item__error--inline {
  position: absolute;
  top: 8px;
  right: 5px;
}
/deep/ .el-form-item.is-error .el-input__inner, .el-form-item.is-error .el-input__inner:focus, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner:focus, .el-message-box__input input.invalid, .el-message-box__input input.invalid:focus {
  background: pink;
}
/*label提示样式修改*/
/deep/ .el-form-item__label {
  color: #409EFF;
}
/deep/ .el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 100%;
}
/deep/ .el-select {
  width: 100%;
}
</style>
