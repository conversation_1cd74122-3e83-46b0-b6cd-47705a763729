<template>
  <div>
    <el-button type="primary" @click="save">
      保存
    </el-button>
    <el-button type="primary" @click="updateResult">
      更新结果
    </el-button>
    <el-table
      ref="table"
      :data="tableData"
      class="dataFilterTable"
      style="margin-top: 24px"
      border
    >
      <el-table-column prop="qualityControlIndicators"  label="质控指标"></el-table-column>
      <el-table-column prop="qualityControlResults" label="质控结果">
        <template slot-scope="scope">
          <el-input v-model="scope.row.realData.qualityControlResults" @change="(data) => changeInputData(data, scope.row.realData.id)"/>
        </template>
      </el-table-column>
      <el-table-column prop="qualityControlStandards" label="质控标准"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../../../util/util'
import {Message} from 'element-ui'

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    sampleQuality () {
      return this.$store.getters.getValue('sampleQuality')
    },
    needUpdatedStore () {
      return this.$store.getters.getValue('needUpdated')
    }
  },
  data () {
    return {
      tableData: [],
      needUpdated: [],
      needUpdatedQualityControlResults: []
    }
  },
  methods: {
    // 获取样本质控列表
    async getData () {
      if (!this.sampleQuality.length) {
        let {code, data = []} = await this.$ajax({
          url: '/read/tngs/pathogen/get_sample_quality_control',
          data: {
            analysisRsId: this.analysisId
          },
          method: 'get',
          loadingDom: '.dataFilterTable'
        })
        if (code === this.SUCCESS_CODE) {
          this.tableData = []
          data.forEach(v => {
            let item = {
              id: v.id,
              qualityControlIndicators: v.fqualityControlIndicators,
              qualityControlResults: v.fqualityControlResults,
              qualityControlStandards: v.fqualityControlStandards
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      } else {
        this.tableData = this.sampleQuality
      }
    },
    changeInputData (qcResult, id) {
      this.needUpdated.push({qcResult, id})
      this.$store.commit({
        type: 'old/setValue',
        category: 'needUpdated',
        needUpdated: this.needUpdated
      })
    },
    save () {
      this.needUpdated.forEach(i => {
        const updateObjIndex = this.tableData.findIndex(o => o.id === i.id)
        if (typeof updateObjIndex === 'number') {
          this.tableData[updateObjIndex].qualityControlResults = i.qcResult
        }
      })
      this.$store.commit({
        type: 'old/setValue',
        category: 'sampleQuality',
        sampleQuality: this.tableData
      })
      Message({
        message: '保存成功！',
        type: 'success',
        duration: 3000
      })
    },
    async updateResult () {
      await this.$ajax({
        url: '/read/update/sample_quality_result',
        data: {
          analysisId: this.analysisId,
          sampleQualityControlDTOList: [...this.needUpdatedStore]
        },
        method: 'post',
        loadingDom: '.dataFilterTable'
      })
      Message({
        message: '更新成功！',
        type: 'success',
        duration: 3000
      })
      this.$store.commit({
        type: 'old/setValue',
        category: 'needUpdated',
        needUpdated: []
      })
    }
  }
}
</script>

<style scoped></style>
