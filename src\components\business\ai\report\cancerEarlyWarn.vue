<template>
  <div class="desc">
    <div class="card-wrapper">
      <el-descriptions :label-style="{width: '120px'}" :column="1" class="margin-top" size="mini" border>
        <el-descriptions-item label="癌种预警监测">
          <div v-html="cancerEarlyWarningDetection"></div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="card-wrapper" style="margin-top: 20px;">
      <el-descriptions
        :label-style="{width: '120px'}"
        :column="2" v-if="testResult.cancerEarlyWarningDetection !== '低风险'"
        class="margin-top"
        title="患癌部位溯源"
        size="mini"
        border>
        <el-descriptions-item :label="testResult.top1">
          <span style="color: #ea5514">{{testResult.top1Cancer}}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="testResult.top2">
          <span style="color: #f39800">{{testResult.top2Cancer}}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    cancerEarlyWarningDetection () {
      let risk = [
        {
          aiScore: 0.9,
          tips: `<span style="color: #ea5514">高风险:</span>您本次检测的Ai-score值为<span style="color: #ea5514">${this.testResult.aiScore}</span>，提示患癌<span style="color: #ea5514">高风险</span>，
                  患病风险较高器官为<span style="color: #ea5514">${this.testResult.riskOrgan}</span>。`
        },
        {
          aiScore: 0.6,
          tips: `<span style="color: #f39800">中风险:</span>您本次检测的Ai-score值为<span style="color: #f39800">${this.testResult.aiScore}</span>，提示患癌<span style="color: #f39800">中风险</span>，
                  患病风险较高器官为<span style="color: #f39800">${this.testResult.riskOrgan}。</span>`
        },
        {
          aiScore: 0,
          tips: `<span style="color: #00ab80">低风险:</span>您本次检测的Ai-score值为${this.testResult.aiScore}，提示患癌<span style="color: #00ab80">低风险</span>。`
        }
      ]
      let tips = ''
      risk.forEach((v) => {
        if (this.testResult.aiScore >= v.aiScore && tips === '') {
          tips = v.tips
        }
      })
      return tips
    }
  },
  data () {
    return {
      testResult: {}
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_cancer_early_warning_surveillance',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || {}
        this.testResult = {
          id: info.fid,
          aiScore: info.aiScore || 0.1,
          cancerEarlyWarningDetection: info.cancerEarlyWarningDetection,
          riskOrgan: info.riskOrgan
        }
        if (info.cancerEarlyWarningDetection !== '低风险') {
          this.testResult = {
            top1: info.top1,
            top1Cancer: info.top1Cancer,
            top2: info.top2,
            top2Cancer: info.top2Cancer,
            ...this.testResult
          }
        }
      }
    }
  }
}
</script>

<style scoped></style>
