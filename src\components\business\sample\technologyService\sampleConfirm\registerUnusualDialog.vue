<template>
  <div>
    <el-dialog
      v-drag-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      title="异常登记"
      width="800px"
      @open="handleOpen">
      <div>
        <el-form :model="form" :rules="rules" label-width="110px" inline>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="已选样本" prop="sampleNums">
                {{form.sampleNums}}
              </el-form-item>
            </el-col>
            <el-col :span="24" prop="registerSample">
              <el-form-item label="异常分类" prop="registerSample">
                <el-select v-model.trim="form.registerSample" size="mini" style="width: 500px" clearable>
                  <el-option :key="item" :label="item" :value="item" v-for="item in category"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="异常描述" prop="unusualRemark">
                <el-input
                  v-model.trim="form.unusualRemark"
                  :rows="4"
                  type="textarea"
                  size="mini"
                  show-word-limit
                  maxlength="100"
                  style="width: 500px"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="图片说明" prop="picList">
                <el-upload
                  ref="upload"
                  :auto-upload="false"
                  :action="uploadUrl"
                  :file-list="form.picList"
                  :headers="headers"
                  :on-success="handleOnSuccess"
                  :on-error="handleOnError"
                  :on-progress="handleProgress"
                  :on-change="handleBeforeUpload"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  multiple
                  accept="image/jpg,image/jpeg,image/png"
                  list-type="picture-card">
                  <i class="el-icon-plus"></i>
                </el-upload>
                <Tips :size="10"></Tips>
                <el-dialog :visible.sync="dialogVisible"
                           title="图片预览"
                           width="450px"
                           append-to-body>
                  <img :src="dialogImageUrl" style="width:400px; object-fit: fill" alt="">
                </el-dialog>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否推送" prop="picList">
                <el-checkbox v-model="form.isSendToT7" size  :true-label="1" :false-label="0"> 同时推送至科服客户端</el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :disabled="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import Cookies from 'js-cookie'
import constants from '@/util/constants'
// import util from '../../../../../util/util'
export default {
  name: 'registerUnusualDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleConfirmId: Array, // 所选样本id
    samplesNum: Number, // 所选样本数量
    isOnlySingleOrder: Boolean, // 是否只有单细胞
    choseUnusualSample: Array // 所选异常的样本编号
  },
  data () {
    return {
      loading: false,
      form: {
        picList: [],
        isSendToT7: 1,
        sampleNums: '', // 已选样本数量
        registerSample: '', // 异常分类
        unusualRemark: '' // 异常描述
      },

      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      dialogImageUrl: '',
      dialogVisible: false,
      headers: {
        token: Cookies.get('token')
      },
      category: constants.EXCEPT_OPTION_LIST,
      rules: {
        registerSample: [
          { required: true, message: '请选择异常分类', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      if (this.isOnlySingleOrder) {
        this.category = ['到样温度异常', '样本外观异常', '采集容器错误', '送样量异常', '运输异常', '冰融', '超温', '超时', '管材不符', '其他']
      } else {
        this.category = ['到样温度异常', '样本外观异常', '采集容器错误', '送样量异常', '运输异常', '其他']
      }
      console.log(this.category, this.isOnlySingleOrder)
      this.form.picList = []
      this.form.registerSample = ''
      this.form.isSendToT7 = 1
      this.form.unusualRemark = ''
      this.form.sampleNums = this.samplesNum
    },
    // 登记异常
    unusualEvent () {
      this.loading = true
      this.$ajax({
        url: '/sample/confirm/register_exception',
        data: {
          sampleIdList: this.sampleConfirmId,
          exceptionType: this.form.registerSample,
          exceptionRemark: this.form.unusualRemark,
          isSendToT7: this.form.isSendToT7,
          uploadfile: this.form.picList.map(v => v.raw)
        },
        isFormData: true
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('unusualSaveDialogConfirmEvent')
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 点击确认
    handleConfirm () {
      // if (this.registerSample === '') {
      //   this.$message.warning('请选择异常分类')
      //   return
      // }
      // if (this.unusualRemark.replace(/^\s+|\s+$/g, '').length > 100) {
      //   this.$message.warning('异常描述请勿超过100个字符')
      //   return
      // }
      if (this.choseUnusualSample.length !== 0) {
        let data = this.choseUnusualSample.toString()
        this.$confirm(`所选样本：${data} 已存在异常信息，是否进行更新？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.unusualEvent()
        })
      } else {
        this.unusualEvent()
      }
    },
    handleRemove (file, fileList) {
      this.onProgress = false
      this.form.picList = [...fileList]
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 附件提交成功回调
    handleOnSuccessFile (res) {
      this.onProgress = false
      console.log(res)
      this.form.attachFile.push(res.data)
    },
    // 提交前的函数
    handleBeforeUpload (file, fileList) {
      let name = file.name
      if (fileList.length > 10) {
        this.$message.error('上传数量超出限制')
        fileList.pop()
        return false
      }
      if (!/\.(jpg|png|jpeg)$/.test(name)) {
        this.$message.error('只能上传jpg、png或jpeg的图片')
        fileList.pop()
        this.form.picList = fileList
        return false
      }
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件不能大于10M，请重新上传')
        fileList.pop()
        this.form.picList = fileList
        return false
      }
      this.form.picList = fileList
      return true
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.onProgress = false
      if (res && res.code === this.SUCCESS_CODE) {
        file.onlineUrl = res.data.absolutePath
        file.group = res.data.group
        file.path = res.data.path
      }
      this.form.picList = [...fileList]
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    }
  }
}
</script>

<style scoped>
.el-message-box{
  width: 570px;
}
.unusual-style{
  float: left;
  margin: 4px 4px 0 0;
  color: #F56C6C;
}
</style>
