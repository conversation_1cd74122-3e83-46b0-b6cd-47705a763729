<template>
  <el-dialog
    title="自动审核"
    :visible.sync="visible"
    class="auto-audit-dialog"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @opened="handleOpen">
    <div class="auto-audit-container">
      <!-- 输入体积区域 -->
      <div class="input-section">
        <label>{{ auditType === 0 ? '初步质控体积:' : '上机前体积:' }}</label>
        <el-input v-if="auditType === 0" v-model.trim="volume" size="mini" clearable placeholder="请输入初步质控体积" class="volume-input" @blur="handleCalculate">
          <template slot="append">μl</template>
        </el-input>
        <el-input v-else v-model.trim="beforeVolume" size="mini" clearable placeholder="请输入上机前体积" class="volume-input" @blur="handleCalculate">
          <template slot="append">μl</template>
        </el-input>
        <!-- <el-button type="primary" size="mini">计算</el-button> -->
      </div>

      <!-- 质控结果区域 -->
      <div class="quality-result-section">
        <div class="section-title">
          质控结果
        </div>
        <div class="quality-result-grid">
          <div class="grid-item" v-for="(item, index) in qualityResultItems" :key="index">
            <div class="item-label" :class="`${requiredField.includes(item.field) ? 'required' : ''}`">{{ item.label }}</div>
            <div v-if="!isQualityResultEditing">
              <div v-if="isCustomerField(item.field)" class="item-value">
                {{ showBooleanText(formData[item.field]) }}
              </div>
              <div v-else-if="['fcellTotal', 'fbeforeBoardCount'].includes" class="item-value" >{{ formreate(formData[item.field]) }}</div>
              <div v-else class="item-value" >{{ formData[item.field] }}</div>

            </div>
           <div v-else>
            <el-select
              v-if="isCustomerField(item.field)"
              v-model="formData[item.field]"
              size="mini"
              clearable
              filterable
              placeholder="请选择"
              class="edit-input"
              >
              <el-option
                :key="1"
                :label="'是'"
                :value="1">
              </el-option>
              <el-option
                :key="0"
                :label="'否'"
                :value="0">
              </el-option>
            </el-select>
            <el-select
              v-else-if="item.field === 'ftestSummary'"
              v-model="formData[item.field]"
              size="mini"
              clearable
              filterable
              placeholder="请选择"
              class="edit-input"
              @change="handleTestSummaryChange"
              >
                <el-option v-for="item in resultOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            <el-input
              v-else
              v-model="formData[item.field]"
              size="mini"
              maxlength="50"
              placeholder="请输入"
              class="edit-input"
              @change="handleInputChange"
            >
            </el-input>
           </div>

          </div>
        </div>
      </div>

      <!-- 质控通知信息区域 -->
      <div v-if="showQualityInfo" id="quality-info-section" class="quality-info-section">
        <div class="section-title">
          质控通知信息
        </div>
        <div id="capture">
        <div class="quality-info-grid">
          <div class="info-row" v-for="(row, rowIndex) in qualityInfoRows" :key="rowIndex">
            <div class="info-item" v-for="(item, itemIndex) in row" :key="itemIndex">
              <div class="item-label">{{ item.label }}:</div>
              <div class="item-value" v-if="!isQualityInfoEditing">{{ formData[item.field] }}<span>{{ item.unit }}</span></div>
            </div>
          </div>
        </div>

        <!-- 图片显示区域 -->
        <div class="image-section">
          <div class="section-title">
            图片信息
          </div>
          <template v-if="formData.fmethod === 'AOPI计数'">
            <div v-if="imageList && imageList.length >= 4"  class="image-grid">
            <div class="image-item" v-for="(i, index) in 4" :key="index">
              <div class="image-placeholder" @click="handleImagePreview(imageList[index], index)">
                <img v-if="imageList[index].url" :src="imageList[index].url" :alt="imageList[index].name" class="cell-image">
                <div v-else class="empty-image">
                  <i class="el-icon-picture-outline"></i>
                  <span>暂无图片</span>
                </div>
                <!-- 预览图标 -->
                <div v-if="imageList[index].url" class="preview-overlay">
                  <i class="el-icon-zoom-in"></i>
                </div>
              </div>
              <div class="image-label">{{ imageList[index].name }}</div>
            </div>
          </div>
          <div v-if="imageList && imageList.length >= 7" class="image-grid">
            <div class="image-item" v-for="(i, index) in 3" :key="index">
              <div  class="image-placeholder" @click="handleImagePreview(imageList[index + 4], index)">
                <img v-if="imageList[index + 4].url" :src="imageList[index + 4].url" :alt="imageList[index + 4].name" class="cell-image">
                <div v-else class="empty-image">
                  <i class="el-icon-picture-outline"></i>
                  <span>暂无图片</span>
                </div>
                <div v-if="imageList[index + 4].url" class="preview-overlay">
                  <i class="el-icon-zoom-in"></i>
                </div>
              </div>
              <div class="image-label">{{ imageList[index + 4].name }}</div>
            </div>
          </div>
          </template>
          <template v-else>
            <div v-if="imageList && imageList.length >= 2"  class="image-grid">
            <div class="image-item" v-for="(i, index) in 2" :key="index">
              <div class="image-placeholder" @click="handleImagePreview(imageList[index], index)">
                <img v-if="imageList[index].url" :src="imageList[index].url" :alt="imageList[index].name" class="cell-image">
                <div v-else class="empty-image">
                  <i class="el-icon-picture-outline"></i>
                  <span>暂无图片</span>
                </div>
                <!-- 预览图标 -->
                <div v-if="imageList[index].url" class="preview-overlay">
                  <i class="el-icon-zoom-in"></i>
                </div>
              </div>
              <div class="image-label">{{ imageList[index].name }}</div>
            </div>
            </div>
          </template>
        </div>
        </div>

        <!-- 备注区域 -->
        <div v-if="formData.fbeforeBoardCount" class="remark-section">
          <div class="section-title">备注信息</div>
          <!-- <div v-html="remark"></div> -->
          <el-input
            type="textarea"
            v-model="remark"
            :rows="4"
          >
          </el-input>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button v-if="!showQualityInfo" :loading="loading" type="primary" size="mini" @click="handleConfirm">保存</el-button>
      <el-button v-else type="primary" :loading="loading" size="mini" @click="handleConfirm">保存并发送质控通知</el-button>
    </span>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="imagePreviewVisible"
      :close-on-click-modal="false"
      :modal="false"
      width="60%"
      :before-close="closeImagePreview"
      class="image-preview-dialog">
      <div class="image-preview-container">
        <div class="image-preview-main">
          <img
            v-if="currentPreviewImage.url"
            :src="currentPreviewImage.url"
            :alt="currentPreviewImage.name"
            class="preview-image">
          <div v-else class="no-image">
            <i class="el-icon-picture-outline"></i>
            <p>暂无图片</p>
          </div>
        </div>

        <!-- 图片信息 -->
        <div class="image-info">
          <h4>{{ currentPreviewImage.name }}</h4>
        </div>

        <!-- 图片导航 -->
        <div class="image-navigation" v-if="imageList.length > 1">
          <el-button
            @click="prevImage"
            :disabled="currentImageIndex === 0"
            icon="el-icon-arrow-left"
            size="small">
            上一张
          </el-button>
          <span class="image-counter">
            {{ currentImageIndex + 1 }} / {{ imageList.length }}
          </span>
          <el-button
            @click="nextImage"
            :disabled="currentImageIndex === imageList.length - 1"
            icon="el-icon-arrow-right"
            size="small">
            下一张
          </el-button>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeImagePreview">关闭</el-button>
        <el-button
          v-if="currentPreviewImage.url"
          type="primary"
          size="mini"
          @click="downloadImage">
          下载图片
        </el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import { awaitWrap, mul } from '@/util/util'
import {
  getAutoAuditDetail,
  saveAutoAuditData
} from '@/api/singleCell/dissociationApi'
import ScreenshotCapturer from '@/util/capturer'
import {qualityResultItems, beforeQualificationItems} from '../constants'

export default {
  name: 'AutoAuditDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: [Number, String],
      default: null
    },
    showQualityInfo: {
      type: Boolean,
      default: false
    },
    auditType: {
      type: Number,
      default: 0
    }
  },
  computed: {
    isCustomerField () {
      const noRequireField = [
        'fisDieCell',
        'fisDefragment',
        'fisEmbarkation'
      ]
      return field => {
        return noRequireField.includes(field)
      }
    }
  },
  data () {
    return {
      loading: false,
      volume: '', // 对应 fvolume
      beforeVolume: '', // 对应 fbeforeVolume
      remark: '', // 对应 ftestSummary
      isQualityResultEditing: true, // 是否正在编辑质控结果
      isQualityInfoEditing: false, // 是否正在编辑质控通知信息
      requiredField: [
      ],
      resultOptions: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
        { label: 'D', value: 'D' },
        { label: '符合', value: '符合' },
        { label: '不符合', value: '不符合' }
      ],
      // 质控结果数据
      formData: {
        fvolume: '', // 体积
        fbeforeVolume: '', // 上机前体积
        fcellSuspensionConcentration: '', // 细胞悬液浓度
        fcellTotal: '', // 细胞总量
        fcytoactive: '', // 细胞活率
        fcoalescenceRate: '', // 结团率
        fproportionNucleatedCell: '', // 有核细胞比例
        ffragmentationCondition: '', // 碎片情况
        fsampleDescribe: '', // 样本描述
        ftestSummary: '', // 检测结论
        fisEmbarkation: '', // 是否上机
        fisDieCell: '', // 是否去死细胞
        fisDefragment: '', // 是否去碎片
        fexperimentRemark: '', // 实验备注
        fmethod: '', // 方法
        fdetectionConclusion: '', // 检测结论 (A/B/C/D)
        foriginalSampleCode: '', // 原始样本编号
        fexcelFileName: '', // Excel文件名
        // 上机前数据
        fbeforeBoardSuspensionConcentration: '', // 上机前悬液浓度
        fbeforeBoardCount: '', // 上机前计数
        fbeforeBoardCytocative: '', // 上机前细胞活率
        fbeforeBoardProportionNucleated: '', // 上机前有核比例
        fbeforeBoardDiameter: '', // 上机前直径
        fbeforeBoardCakeRate: '', // 上机前结团率
        fbeforeBoardFragmentation: '', // 上机前碎片
        fcosSampleName: '',
        // 质控通知信息
        fliveCellConcentration: '', // 活细胞浓度
        fdeathCellConcentration: '', // 死细胞浓度
        flivingCellsCount: '', // 活细胞个数
        fdeathCellsCount: '', // 死细胞个数
        fallCellsCount: '' // 总细胞个数
      },
      qualityResultItems: [
      ],
      qualityInfoRows: [
        [
          { label: '细胞活率', field: 'fbeforeBoardCytocative' },
          { label: '总细胞浓度', field: 'fbeforeBoardSuspensionConcentration', unit: '个/μl' }
        ],
        [
          { label: '活细胞浓度', field: 'fliveCellConcentration', unit: '个/μl' },
          { label: '死细胞浓度', field: 'fdeathCellConcentration', unit: '个/μl' }
        ],
        [
          { label: '总细胞个数', field: 'fallCellsCount', unit: '' },
          { label: '活细胞个数', field: 'flivingCellsCount', unit: '' }
        ],
        [
          { label: '死细胞个数', field: 'fdeathCellsCount', unit: '' },
          { label: '平均直径', field: 'fbeforeBoardDiameter', unit: 'μm' }
        ],
        [
          { label: '结团率', field: 'fbeforeBoardCakeRate' },
          { label: '有核率', field: 'fbeforeBoardProportionNucleated' }
        ]
      ],
      imageList: [],
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewImage: {},
      currentImageIndex: 0
    }
  },
  methods: {
    handleOpen () {
      this.resetFormData()
      this.getDetailData()
      // this.isQualityResultEditing = false
      this.isQualityInfoEditing = false
      if (!this.auditType) {
        this.qualityResultItems = qualityResultItems
      } else {
        this.qualityResultItems = beforeQualificationItems
      }
    },
    resetFormData () {
      this.volume = ''
      this.beforeVolume = ''
      this.remark = ''
      this.imageList = []
      // 重置表单数据
      Object.keys(this.formData).forEach(key => {
        this.formData[key] = ''
      })
    },
    async getDetailData () {
      const { res } = await awaitWrap(getAutoAuditDetail({ fid: this.id }, {
        loadingDom: '.auto-audit-dialog'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.fillFormData(res.data)
      }
    },
    fillFormData (data) {
      if (data.fimageList && Array.isArray(data.fimageList)) {
        this.imageList = data.fimageList.map(item => ({
          name: item.ffileType,
          url: item.ffileBase
        }))
      } else {
        this.imageList = []
      }
      this.volume = data.fvolume
      this.beforeVolume = data.fbeforeVolume
      // 填充表单数据
      Object.keys(this.formData).forEach(key => {
        if (data[key] !== undefined) {
          this.formData[key] = data[key]
        }
      })
      if (this.auditType) {
        this.remark = this.generateQualityControlText()
      }
    },
    showBooleanText (v) {
      const booleanMap = {
        0: '否',
        1: '是'
      }
      return booleanMap[v] || ''
    },
    async handleCalculate () {
      const volume = this.auditType ? this.beforeVolume : this.volume
      if (!volume) {
        this.$message.warning('请输入体积')
        return
      }
      // 只能输入数字
      if (!/^\d+(\.\d{1,2})?$/.test(volume)) {
        this.$message.error(`请输入正确的${this.auditType ? '上机前' : ''}体积，最多两位小数`)
        return
      }
      const concentration = !this.auditType
        ? this.formData.fcellSuspensionConcentration
        : this.formData.fbeforeBoardSuspensionConcentration
      if (this.auditType) {
        this.formData.fbeforeBoardCount = mul(concentration, this.beforeVolume)
        this.formData.fbeforeVolume = this.beforeVolume
        this.remark = this.generateQualityControlText()
        return
      }
      this.formData.fcellTotal = mul(concentration, this.volume)
      this.formData.fvolume = this.volume

      this.remark = this.generateQualityControlText()
    },
    handleTestSummaryChange () {
      this.remark = this.generateQualityControlText()
    },
    handleInputChange () {
      this.remark = this.generateQualityControlText()
    },
    toggleQualityResultEdit () {
      this.isQualityResultEditing = !this.isQualityResultEditing
      if (!this.isQualityResultEditing) {
        this.$message.success('质控结果编辑完成')
      }
    },
    async handleConfirm () {
      if (!this.auditType) {
        // 校验体积
        if (!this.volume || !/^\d+(\.\d{1,2})?$/.test(this.volume)) {
          this.$message.warning('请输入体积，最多两位小数')
          return
        }
        if (!this.formData.fcellTotal) {
          this.$message.warning('请输入体积计算结果')
          return
        }
      }
      if (this.auditType) {
        if (!this.beforeVolume || !/^\d+(\.\d{1,2})?$/.test(this.beforeVolume)) {
          this.$message.warning('请输入上机前体积，最多两位小数')
          return
        }
        if (!this.formData.fbeforeBoardCount) {
          this.$message.warning('请输入上机前体积计算结果')
          return
        }
        if (!/^\d+(\.\d{1,2})?$/.test(this.formData.fcellSuspensionConcentration)) {
          this.$message.error('请输入正确的细胞悬液浓度，最多两位小数')
          await this.getData()
          return
        }
        if (!/^\d+(\.\d{1,2})?$/.test(this.formData.fbeforeBoardSuspensionConcentration)) {
          this.$message.error('请输入正确的上机前-细胞悬液浓度，最多两位小数')
          await this.getData()
          return
        }
      }

      this.loading = true
      try {
        let base64Image = ''
        if (this.showQualityInfo) {
          const element = document.getElementById('capture')
          // const canvas = await html2canvas(element)
          const capturer = new ScreenshotCapturer(element)
          const canvas = await capturer.capture()
          base64Image = await canvas.toDataURL('image/png')
        }
        // 构建提交数据
        const submitData = {
          fid: this.id,
          fvolume: this.formData.volume,
          fbeforeVolume: this.formData.beforeVolume,
          ftype: this.auditType,
          ffileBase64: base64Image,
          fdingdingMessage: this.remark.replace('<br/>', ''),
          ...this.formData
        }

        // 保存数据的API调用
        const { res } = await awaitWrap(saveAutoAuditData(submitData))
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        }
      } finally {
        this.loading = false
      }
    },
    // 获取提交数据的方法
    getSubmitData () {
      return {
        fid: this.id,
        fvolume: this.volume,
        fbeforeVolume: this.beforeVolume,
        ftestSummary: this.remark,
        ...this.formData
      }
    },

    formatCellTotal (total) {
      if (!total) return '未知'
      const numTotal = parseFloat(total.toString().replace(/[^\d.]/g, ''))
      if (numTotal >= 50000) {
        const wan = Math.floor(numTotal / 10000)
        return `${wan}w`
      } else {
        return numTotal.toLocaleString()
      }
    },
    // 根据质控结果自动生成文本
    generateQualityControlText () {
      const {
        fcosSampleName,
        fbeforeBoardCytocative,
        fbeforeBoardCakeRate,
        fcellTotal,
        fbeforeBoardCount,
        ftestSummary,
        fmethod,
        fexperimentRemark
      } = this.formData
      console.log(this.formData)

      // 3. 生成检测结论
      const generateConclusion = () => {
        const conclusion = ftestSummary
        const cellActivity = parseFloat(fbeforeBoardCytocative.toString().replace('%', '') || '0')
        const coalescenceRate = parseFloat(fbeforeBoardCakeRate.toString().replace('%', '') || '0')
        const cellTotal = fbeforeBoardCount

        if (conclusion === 'A' || conclusion === 'B') {
          return `细胞悬液等级${conclusion}，符合质控，可以上机，请确认是否上机`
        } else if (conclusion === 'C') {
          const abnormalDescriptions = []
          console.log('cellTotal:', cellTotal)
          console.log('cellActivity:', cellActivity)
          console.log('coalescenceRate:', coalescenceRate)

          // 检查细胞总量
          if (cellTotal >= 20000 && cellTotal < 30000) {
            abnormalDescriptions.push('细胞总量少')
          }

          // 检查细胞活率
          if (cellActivity >= 75 && cellActivity < 80) {
            abnormalDescriptions.push('细胞活率低')
          }

          // 检查结团率
          if (coalescenceRate > 20 && coalescenceRate <= 30) {
            abnormalDescriptions.push('结团率偏高')
          }

          const abnormalDesc = abnormalDescriptions.length > 0
            ? abnormalDescriptions.join('，')
            : ''
          console.log(abnormalDesc, '异常描述')
          return `细胞悬液等级C，${abnormalDesc} ${abnormalDesc ? '，' : ''}符合质控，可风险上机，请确认是否上机`
        } else if (conclusion === 'D') {
          return '细胞悬液等级D，不符合质控，请确认是否风险上机'
        } else {
          return '检测结论未知，请确认检测结论'
        }
      }

      // 4. 组装最终文本
      const finalText = `${fcosSampleName}：${fmethod}，细胞活性${fbeforeBoardCytocative}，结团率${fbeforeBoardCakeRate}，细胞总量${this.formatCellTotal(this.auditType ? fbeforeBoardCount : fcellTotal)}个，${generateConclusion()}。\n${fexperimentRemark || ''}`
      return finalText
    },
    formreate (num) {
      if (!num) return ''
      if (isNaN(num)) return num
      const numValue = parseFloat(num.toString().replace(/[^\d.]/g, ''))
      if (numValue >= 50000) {
        const wan = Math.floor(numValue / 10000)
        return `${wan}w`
      } else {
        return numValue.toLocaleString()
      }
    },

    // 图片预览相关方法
    handleImagePreview (image, index) {
      if (!image.url) {
        this.$message.info('暂无图片可预览')
        return
      }

      this.currentPreviewImage = image
      this.currentImageIndex = index
      this.imagePreviewVisible = true
    },

    // 上一张图片
    prevImage () {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
        this.currentPreviewImage = this.imageList[this.currentImageIndex]
      }
    },

    // 下一张图片
    nextImage () {
      if (this.currentImageIndex < this.imageList.length - 1) {
        this.currentImageIndex++
        this.currentPreviewImage = this.imageList[this.currentImageIndex]
      }
    },

    // 获取图片类型
    getImageType (url) {
      if (!url) return '未知'

      const parts = url.split('.')
      const extension = parts.length > 0 ? parts.pop().toLowerCase() : ''
      const typeMap = {
        'jpg': 'JPEG图片',
        'jpeg': 'JPEG图片',
        'png': 'PNG图片',
        'gif': 'GIF图片',
        'bmp': 'BMP图片',
        'webp': 'WebP图片',
        'svg': 'SVG图片'
      }

      return typeMap[extension] || '图片文件'
    },

    // 下载图片
    downloadImage () {
      if (!this.currentPreviewImage.url) {
        this.$message.error('无法下载，图片不存在')
        return
      }

      try {
        const link = document.createElement('a')
        link.href = this.currentPreviewImage.url
        link.download = `${this.currentPreviewImage.name}_${Date.now()}.jpg`
        link.target = '_blank'

        // 如果是base64图片，需要特殊处理
        if (this.currentPreviewImage.url.startsWith('data:')) {
          // 创建blob对象
          const arr = this.currentPreviewImage.url.split(',')
          const mime = arr[0].match(/:(.*?);/)[1]
          const bstr = atob(arr[1])
          let n = bstr.length
          const u8arr = new Uint8Array(n)

          while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
          }

          const blob = new Blob([u8arr], { type: mime })
          const url = window.URL.createObjectURL(blob)
          link.href = url

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          // 释放URL对象
          window.URL.revokeObjectURL(url)
        } else {
          // 普通URL图片
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }

        this.$message.success('图片下载成功')
      } catch (error) {
        console.error('下载图片失败:', error)
        this.$message.error('下载图片失败')
      }
    },

    // 关闭图片预览
    closeImagePreview (done) {
      this.imagePreviewVisible = false
      this.currentPreviewImage = {}
      this.currentImageIndex = 0
      if (done) {
        done()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.auto-audit-container {
  max-height: 55vh;
  overflow: auto;
  padding: 0 10px;
  .input-section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;

    .volume-input {
      flex: 1;
    }
  }

  .section-title {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .edit-btn {
      color: #409eff;
      padding: 0;
      font-size: 12px;
    }
  }

  .quality-result-section {
    margin-bottom: 20px;
  }

  .quality-result-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;

    .grid-item {
      background-color: #f5f7fa;
      padding: 8px;
      text-align: center;

      .item-label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 5px;
      }

      .item-value {
        font-size: 14px;
        color: #303133;
      }

      .edit-input {
        width: 100%;
        /deep/ .el-input__inner {
          height: 28px;
          line-height: 28px;
          font-size: 12px;
          padding: 0 8px;
        }
      }
    }
  }

  .quality-info-grid {
    margin-bottom: 15px;
    padding: 0 10px;

    .info-row {
      display: flex;
      margin-bottom: 20px;

      .info-item {
        flex: 1;
        display: flex;
        align-items: center;
        margin-right: 10px;

        &:last-child {
          margin-right: 0;
        }

        .item-label {
          font-size: 12px;
          color: #606266;
          margin-bottom: 5px;
        }

        .item-value {
          font-size: 14px;
          color: #303133;
          background-color: #f5f7fa;
          padding: 8px;
          flex: 1;
          border-radius: 4px;
        }

        .edit-input {
          /deep/ .el-input__inner {
            height: 32px;
            line-height: 32px;
            font-size: 12px;
            padding: 0 8px;
          }
        }
      }
    }
  }

  .image-grid {
    display: flex;

    .image-item {
      text-align: center;

      .image-placeholder {
        height: 80px;
        background-color: #dcdfe6;
        margin-bottom: 5px;
      }

      .image-label {
        font-size: 12px;
        color: #606266;
      }
    }
  }

  .image-section {
    margin-top: 20px;

    .image-grid {
       display: flex;
       width: 100%;
       margin-bottom: 30px;

      .image-item {
        flex: 1;
        text-align: center;

        .image-placeholder {
          border: 1px solid #dcdfe6;
          height: 100%;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f7fa;
          position: relative;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

            .preview-overlay {
              opacity: 1;
            }
          }

          .cell-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 3px;
          }

          .empty-image {
            width: 100%;
            height: 100%;
            background-color: #c0c4cc;
            border-radius: 3px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #909399;
            font-size: 12px;

            i {
              font-size: 24px;
              margin-bottom: 4px;
            }
          }

          .preview-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            opacity: 0;
            transition: opacity 0.3s ease;

            i {
              color: white;
              font-size: 20px;
            }
          }
        }

        .image-label {
          font-size: 12px;
          color: #606266;
          font-weight: 500;
        }
      }
    }
  }

  .auto-generate-section {
    margin-bottom: 20px;

    .config-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-top: 10px;

      .config-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .config-label {
          font-size: 12px;
          color: #606266;
          white-space: nowrap;
          min-width: 80px;
        }

        .el-input,
        .el-select {
          flex: 1;
        }

        .generate-btn {
          width: 100%;
        }
      }

      // 让自动生成按钮占据整行
      .config-item:last-child {
        grid-column: 1 / -1;
        justify-content: center;

        .generate-btn {
          width: 200px;
        }
      }
    }
  }

  .remark-section {
    margin-bottom: 10px;
  }

  .required::before {
    content: '*';
    color: red;
    margin-right: 4px;
  }
}

// 图片预览对话框样式
.image-preview-dialog {
  .el-dialog {
    margin-top: 5vh !important;
    max-height: 90vh;
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 65vh;
    overflow-y: auto;
  }

  .image-preview-container {
    .image-preview-main {
      text-align: center;
      margin-bottom: 20px;

      .preview-image {
        max-width: 100%;
        max-height: 50vh;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        object-fit: contain;
      }

      .no-image {
        height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        background-color: #f5f7fa;
        border-radius: 8px;
        border: 2px dashed #dcdfe6;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }

        p {
          font-size: 16px;
          margin: 0;
        }
      }
    }

    .image-info {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
      }

      p {
        margin: 5px 0;
        color: #606266;
        font-size: 14px;

        .info-label {
          font-weight: 500;
          margin-right: 8px;
        }
      }
    }

    .image-navigation {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;

      .image-counter {
        color: #606266;
        font-size: 14px;
        min-width: 60px;
        text-align: center;
      }
    }
  }
}

.quality-info-section {
  padding: 0 5px
}
.quality-result-section {
  padding: 0 5px
}

#capture {
  padding-bottom: 10px;
}
</style>
