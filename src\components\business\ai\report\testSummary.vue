<template>
  <div class="card-wrapper">
    <el-descriptions :label-style="{width: '120px'}" :column="1" class="desc" size="mini" border>
      <el-descriptions-item label="癌种预警监测">
        <div v-html="cancerEarlyWarningDetection"></div>
      </el-descriptions-item>
      <el-descriptions-item label="血浆微生物">
        <div v-html="plasmaMicrobes"></div>
      </el-descriptions-item>
      <el-descriptions-item label="肿瘤遗传风险">
        <div v-html="cancerGeneticRiskIndex"></div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    cancerEarlyWarningDetection () {
      let risk = [
        {
          aiScore: 0.9,
          tips: `<span style="color: #ea5514">高风险:</span>您本次检测的Ai-score值为<span style="color: #ea5514">
                        ${this.testResult.aiScore}</span>，提示患癌<span style="color: #ea5514">高风险</span>，
                  患病风险较高器官为<span style="color: #ea5514">${this.testResult.riskOrgan}</span>。`
        },
        {
          aiScore: 0.6,
          tips: `<span style="color: #f39800">中风险:</span>您本次检测的Ai-score值为<span style="color: #f39800">
                  ${this.testResult.aiScore}</span>，提示患癌<span style="color: #f39800">中风险</span>，
                  患病风险较高器官为<span style="color: #f39800">${this.testResult.riskOrgan}。</span>`
        },
        {
          aiScore: 0,
          tips: `<span style="color: #00ab80">低风险:</span>您本次检测的Ai-score值为${this.testResult.aiScore}，提示患癌<span style="color: #00ab80">低风险</span>。`
        }
      ]
      let tips = ''
      risk.forEach((v) => {
        if (this.testResult.aiScore >= v.aiScore && tips === '') {
          tips = v.tips
        }
      })
      return tips
    },
    plasmaMicrobes () {
      if (this.testResult.plasmaMicrobes === '阳性') {
        return `<span style="color: #ea5514">阳性:</span>本次血浆微生物检测<span style="color: #ea5514">${this.testResult.plasmaMicrobesData}</span>`
      } else {
        return `<span style="color: #00ab80">阴性：</span>本次血浆微生物检测未检出相关病原微生物。`
      }
    },
    cancerGeneticRiskIndex () {
      if (this.testResult.cancerGeneticRisk === '阳性') {
        return `<span style="color: #ea5514">阳性:</span>${this.testResult.cancerGeneticRiskIndex}`
      } else {
        return `<span style="color: #00ab80">阴性：</span>未检出遗传性肿瘤相关的致病或疑似致病突变。`
      }
    }
  },
  data () {
    return {
      testResult: {}
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_test_result',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || {}
        this.testResult = {
          id: info.fid,
          aiScore: info.aiScore || 0.1,
          cancerEarlyWarningDetection: info.cancerEarlyWarningDetection,
          riskOrgan: info.riskOrgan,
          plasmaMicrobes: info.plasmaMicrobes,
          plasmaMicrobesData: info.plasmaMicrobesData,
          cancerGeneticRisk: info.cancerGeneticRisk,
          cancerGeneticRiskIndex: info.cancerGeneticRiskIndex
        }
      }
    }
  }
}
</script>

<style scoped></style>
