<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="100px"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="任务单编号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="任务单编号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.code" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>

    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('021002001', 'buttons')" type="primary" size="mini" @click="handleChooseTask">下载任务单</el-button>
      <el-button v-if="$setAuthority('021002002', 'buttons')" type="primary" plain size="mini" @click="handleInfoChange">信息变更</el-button>
      <el-button v-if="$setAuthority('021002007', 'buttons')" type="danger" plain class="fix-dropdown-margin" size="mini" @click="handleRegisterException">登记异常</el-button>
      <el-button v-if="$setAuthority('021002003', 'buttons')" type="danger" plain size="mini" @click="handleRemoveSample">移除样本</el-button>
      <el-button v-if="$setAuthority('021002004', 'buttons')" type="primary" plain size="mini" @click="handleAddSample">添加样本</el-button>
      <el-button v-if="$setAuthority('021002005', 'buttons')" type="primary" plain size="mini" @click="handleUploadResult">导入结果</el-button>
      <el-button v-if="$setAuthority('021002006', 'buttons')" type="primary" plain size="mini" @click="handleBackFillResult">回填结果</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        :row-class-name="handleClassName"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55" show-overflow-tooltip></el-table-column>
        <el-table-column prop="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column prop="samplePosition" label="样本孔位" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originSamplePosition" label="原始样本定位" width="100" show-overflow-tooltip></el-table-column>

        <el-table-column prop="sampleName" label="实验样本 " width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneCode" label="核酸/吉因加编号" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.geneCode !== '查看样本详情'">{{scope.row.geneCode}}</span>
            <div v-else class="link" @click="handleDetail(scope.row)">{{scope.row.geneCode}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="oldSampleName" label="原始样本名称" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="libraryModificationType" label="文库修饰类型" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="libraryType" label="文库类型" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="size" label="排单数据量/G" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="concentration" label="浓度（ng/ul）" width="120" show-overflow-tooltip></el-table-column>
<!--        <el-table-column prop="mixingMultiple" label="混合倍数" width="120" show-overflow-tooltip></el-table-column>-->
        <el-table-column prop="mixingAmount" label="混合量" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dilutionMultiple" label="稀释倍数" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="dilutionConcentration" label="稀释后浓度" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="mixingVolume" label="混合体积" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="totalMix" label="混合总量（ng）" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="totalMixingVolume" label="混合总体积（ul）" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productConcentration" label="pooling浓度" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="poolingLibraryName" label="pooling文库名称" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="targetPosition" label="目标孔位" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="targetMaterial" label="目标耗材" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskCode" label="任务单编号" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderPerson" label="下单人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderDate" label="下单时间" width="120" show-overflow-tooltip></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <choose-task-dialog
      :pvisible.sync="chooseTaskVisible"
      :type="type"
      work-id="1"
      workflow-status="0"
      @downloadTaskEvent="handleDownload"
      @returnResultEvent="handleReturnResult"
    />

    <!--异常登记-->
    <register-exception-dialog
      :pvisible.sync="registerExceptionVisible"
      :sample-ids="ids"
      @dialogConfirmEvent="getData"
    />
    <!--    信息变更-->
    <info-change-dialog
      :pvisible.sync="infoChangeDialogVisible"
      :info="info"
      :type="0"
      :process-type="1"
      @dialogConfirmEvent="getData"
    />
    <!--    删除样本-->
    <remove-sample-dialog
      :pvisible.sync="removeSampleVisible"
      :remove-info="removeInfo"
      :type="1"
      @dialogConfirmEvent="getData"
    />

    <add-sample-dialog
      :pvisible.sync="addSampleDialogVisible"
      @dialogConfirmEvent="getData"
    />

    <add-uploader-sample-dialog
      :pvisible.sync="addUploadSampleDialogVisible"
      @dialogConfirmEvent="getData"
    />

    <upload-result-dialog
      :pvisible.sync="uploadResultDialogVisible"
      @dialogConfirmEvent="getData"
    />

    <back-fill-dialog
      :pvisible.sync="backFillVisible"
      :task-ids="taskIds"
      @dialogConfirmEvent="getData"
    />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import registerExceptionDialog from '../components/registerExceptionDialog' // 登记异常
import infoChangeDialog from '../components/infoChangeDialog' // 信息变更
import RemoveSampleDialog from '../components/removeSampleDialog' // 移除样本
import UploadResultDialog from '../components/uploadResultDialog' // 导入结果
import ChooseTaskDialog from '../components/chooseTaskDialog'
import {downloadTask, getPoolingList} from '../../../../api/sequencingManagement/poolingApi'
import BackFillDialog from '../components/backFillDialog'
import AddSampleDialog from '../components/addSampleDialog' // 选择任务单
import AddUploaderSampleDialog from '../components/addUploaderSampleDialog.vue'

export default {
  name: 'processing',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    AddSampleDialog,
    AddUploaderSampleDialog,
    BackFillDialog,
    ChooseTaskDialog,
    UploadResultDialog,
    RemoveSampleDialog,
    registerExceptionDialog,
    infoChangeDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 54 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      registerExceptionVisible: false,
      infoChangeDialogVisible: false,
      removeSampleVisible: false,
      addSampleDialogVisible: false,
      addUploadSampleDialogVisible: false,
      uploadResultDialogVisible: false,
      chooseTaskVisible: false,
      backFillVisible: false,
      form: {
        taskCode: '', // 任务单编号
        projectName: '', // 项目名称
        time: [],
        sampleCode: '', // 样本编号
        code: '' // 吉因加编号
      },
      taskIds: [], // 任务单ids
      type: '',
      removeInfo: [],
      formSubmit: {},
      info: [],
      ids: [{}],
      searchDialogVisible: false,
      tableData: []
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        ftaskCode: this.formSubmit.taskCode,
        forderDateStart: time[0],
        forderDateEnd: time[1],
        fprojectName: this.formSubmit.projectName,
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        fgeneCode: this.formSubmit.code,
        ftype: 0,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getPoolingList(params, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.records || []
        let isHeightLight = false
        let index = 0
        rows.forEach((v, i) => {
          // 判断是否切换高亮 （pooling名称）
          if (i !== 0 && (v.fresultName !== rows[i - 1].fresultName)) {
            isHeightLight = !isHeightLight
            index = 0
          }
          index++
          const item = {
            id: v.fid,
            index: index,
            poolingLibName: v.fpoolingLibName, // pooling文库名称
            isHeightLight: isHeightLight,
            projectName: v.fprojectName, // 项目名称
            sampleName: v.fsampleName, // 样本名称
            oldSampleName: v.foldSampleName, // 原始样本名称
            geneCode: this.handleSetGeneCode(v), // 吉因加编号,
            geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
            geneInfo: {
              fgeneCode: v.fgeneCode,
              fnucleateCode: v.fnucleateCode
            },
            libraryModificationType: v.flibModificationType, // 建库类型
            libraryType: v.flibType, // 建库方法
            size: v.fsize, // 数据量
            mixingMultiple: v.fmixingMultiple, // 混样倍数
            mixingAmount: v.fmixingAmount, // 混样份数
            concentration: v.fconcentration, // 浓度
            dilutionMultiple: v.fdilutionMultiple, // 稀释倍数
            dilutionConcentration: v.fdilutionConcentration, // 稀释后浓度
            mixingVolume: v.fmixingVolume, // 混合体积
            totalMix: v.ftotalMix, // 混合总量（ng）
            totalMixingVolume: v.ftotalMixingVolume, // 混合总体积（ul）
            productConcentration: v.fproductConcentration, // 提提浓度
            poolingLibraryName: v.fresultName,
            taskCode: v.ftaskCode, // 任务单号
            orderPerson: v.forderPerson, // 下单人
            orderDate: v.forderDate, // 下单日期
            samplePosition: v.fsamplePosition, // 样本孔位
            originSamplePosition: v.foriginSamplePosition, // 原始样本定位
            volume: v.fvolume, // 体积
            targetPosition: v.ftargetPosition, // 目标定位
            targetMaterial: v.ftargetMaterial // 目标耗材
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 下载任务单
    async handleChooseTask () {
      this.type = 'downloadTask'
      this.chooseTaskVisible = true
    },
    async handleDownload (ids = []) {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTask({ ftaskids: ids, ftype: 0 }))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 异常标记
    handleRegisterException () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择样本！')
        return
      }
      this.ids = [...this.selectedRows.keys()]
      this.registerExceptionVisible = true
    },
    // 信息变更
    handleInfoChange () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择需要变更的数据！')
        return
      }
      this.info = [...this.selectedRows.values()].map(v => {
        return {
          ...v.realData
        }
      })
      this.infoChangeDialogVisible = true
    },
    // 添加样本
    handleAddSample () {
      // this.addSampleDialogVisible = true
      this.addUploadSampleDialogVisible = true
    },
    // 移除样本
    handleRemoveSample () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择需要移除的数据！')
        return
      }
      this.removeInfo = [...this.selectedRows.values()]
      this.removeSampleVisible = true
    },
    // 导入结果
    handleUploadResult () {
      this.uploadResultDialogVisible = true
    },
    // 回填结果
    handleBackFillResult () {
      this.type = 'returnResult'
      this.chooseTaskVisible = true
    },
    handleReturnResult (ids) {
      this.taskIds = ids
      this.backFillVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;
  .btn-group {
    margin-bottom: 10px;
  }
}
</style>
