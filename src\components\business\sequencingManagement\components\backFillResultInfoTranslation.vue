<template>
  <div>
    <el-form :model="form" ref="form" size="mini" label-suffix=":" inline :rules="rules" label-width="80px">
      <el-form-item label="检测人" prop="detector">
        <el-select v-model.trim="form.detector" filterable multiple collapse-tags clearable placeholder="请选择检测人">
          <el-option v-for="(item, index) in detectorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="复核人" prop="auditor">
        <el-select v-model.trim="form.auditor" filterable clearable multiple collapse-tags placeholder="请选择复核人">
          <el-option v-for="(item, index) in auditorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <vxe-table
      ref="tableRef"
      border
      resizable
      height="300px"
      keep-source
      show-overflow
      :data="tableData"
      size="mini"
      :edit-rules="validRules"
      :valid-config="{msgMode: 'full'}"
      :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
    >
<!--      <vxe-column type="seq" title="序号" width="60"></vxe-column>-->
      <vxe-column field="samplePosition" show-overflow title="样本孔位" width="120" :edit-render="renderConfig"/>
      <vxe-column field="originSamplePosition" show-overflow title="原始样本定位" width="120" :edit-render="renderConfig"/>
<!--      <vxe-column field="fprojectName" title="项目名称" width="80"></vxe-column>-->
      <vxe-column field="fsampleName" title="实验样本 " width="80"></vxe-column>
      <vxe-column field="geneCode" title="核酸/吉因加编号" width="110">
        <template #default="{ row }">
          <span v-if="row.geneCode !== '查看样本详情'">{{row.geneCode}}</span>
          <div v-else class="link" @click="handleDetail(row)">{{row.geneCode}}</div>
        </template>
      </vxe-column>
      <vxe-column field="foldSampleName" title="原始样本名称" width="110"></vxe-column>
<!--      <vxe-column field="fsize" title="排单数据量/G" width="120"></vxe-column>-->
      <vxe-column field="fconcentration" title="浓度（ng/ul)" width="120"></vxe-column>
      <vxe-column field="fnf" title="NF（ul）" width="120" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="fsampleSize" title="取样本量（ul）" width="130" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="finputQuantity" title="输入量（ng）" width="130" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="fcycle" title="cycle数" width="120" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="fresultName" title="转化后样本名称" width="140">
      </vxe-column>
      <vxe-column field="ftransformConcentration" title="转化浓度（ng/ul）" width="180" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="ftransformVolume" title="转化体积" width="130" :edit-render="renderConfig">
      </vxe-column>
      <vxe-column field="resultPosition" show-overflow title="产物定位" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="targetPosition" show-overflow title="目标孔位" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="targetMaterial" show-overflow title="目标耗材" width="140" :edit-render="renderConfig"></vxe-column>
      <vxe-column field="ftaskCode" title="任务单编号" min-width="130"></vxe-column>
    </vxe-table>
    <div class="tips">
      <span style="margin-right: 10px; color: #409EFF">样本总数: {{tableData.length}}</span>
      提交后数据如需修改，请点击【信息变更】，确认继续提交？
    </div>
  </div>
</template>

<script>
import {awaitWrap} from '../../../../util/util'
import {getTestOrCheck, saveResult} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'backFillResultDialog',
  props: {
    info: {
      type: Array,
      default: () => []
    },
    type: {
      type: Number,
      default: null
    }
  },
  mounted () {
    this.init()
  },
  watch: {
    info: {
      handler: function () {
        this.init()
      },
      deep: true
    }
  },
  data () {
    const validateInput = ({ cellValue }) => {
      if (!cellValue) {
        return new Error('请输入输入量')
      }
      console.log(/^\d+$/.test(cellValue), cellValue)
      // 判断是否是纯数字
      // eslint-disable-next-line no-useless-escape
      if (/^[\d.]+$/.test(cellValue)) {
        if (!/^[0-9]\d*(\.\d{1,2})?$/.test(cellValue)) {
          return new Error('请输入两位小数')
        }
      }
    }
    return {
      form: {
        detector: '', // 检测人
        auditor: '' // 审核人
      },
      detectorOptions: [],
      auditorOptions: [],
      tableData: [],
      renderConfig: {name: '$input', props: {clearable: true}},
      rules: {
        detector: [{required: true, message: '请输入检测人', trigger: 'change'}],
        auditor: [{required: true, message: '请输入复核人', trigger: 'change'}]
      },
      validRules: {
        fnf: [
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {required: true, message: '请输入', trigger: 'change'}
        ],
        fsampleSize: [
          { pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {required: true, message: '请输入', trigger: 'change'}
        ],
        finputQuantity: [
          {validator: validateInput}
        ],
        fcycle: [
          {
            pattern: /^[0-9]\d*(\.\d)?$/, message: '请输入一位小数', trigger: 'change' },
          {required: true, message: '请输入', trigger: 'change'}
        ],
        ftransformVolume: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {required: true, message: '请输入', trigger: 'change'}

        ],
        ftransformConcentration: [
          {
            pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change' },
          {required: true, message: '请输入', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    init () {
      this.tableData = []
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
      this.getTestOrCheckPerson(1, 'detectorOptions')
      this.getTestOrCheckPerson(2, 'auditorOptions')
      this.info.forEach(v => {
        const item = {
          fid: v.fid || '',
          ftestPerson: v.ftestPerson || '',
          fcheckPerson: v.fcheckPerson || '',
          fprojectName: v.fprojectName || '',
          fsampleName: v.fsampleName || '',
          geneCode: this.handleSetGeneCode(v), // 吉因加编号,
          geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
          geneInfo: {
            fgeneCode: v.fgeneCode,
            fnucleateCode: v.fnucleateCode
          },
          fnucleateCode: v.fnucleateCode || '',
          fgeneCode: v.fgeneCode,
          foldSampleName: v.foldSampleName || '',
          fsize: v.fsize,
          fconcentration: v.fconcentration || '',
          fnf: v.fnf || '',
          fsampleSize: v.fsampleSize || '',
          fcycle: v.fcycle || '',
          fdilutionConcentration: v.fdilutionConcentration,
          ftransformConcentration: v.ftransformConcentration || '',
          finputQuantity: v.finputQuantity || '',
          ftransformVolume: v.ftransformVolume || '',
          fresultName: v.fresultName || '',
          ftaskCode: v.ftaskCode,
          resultPosition: v.fresultPosition,
          samplePosition: v.fsamplePosition, // 样本孔位
          originSamplePosition: v.foriginSamplePosition, // 原始样本定位
          volume: v.fvolume, // 体积
          targetPosition: v.ftargetPosition, // 目标定位
          targetMaterial: v.ftargetMaterial // 目标耗材
        }
        this.tableData.push(item)
      })
    },
    async getTestOrCheckPerson (roleType, key) {
      const {res} = await awaitWrap(getTestOrCheck({roleType}))
      if (res && res.code === this.SUCCESS_CODE) {
        this[key] = []
        const data = res.data || []
        data.forEach(v => {
          const item = {
            label: v.frealName,
            value: v.fid
          }
          this[key].push(item)
        })
      }
    },
    handleValidForm () {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('表单存在错误，请检查'))
          }
        })
      })
    },
    handleValidate () {
      const $table = this.$refs.tableRef
      return new Promise(async (resolve, reject) => {
        const valid = await $table.fullValidate(true)
        if (valid) {
          this.$message.error('表格存在错误，请检查')
          reject(new Error('表格存在错误，请检查'))
        } else {
          resolve()
        }
      })
    },
    setParams () {
      const list = []
      this.tableData.forEach(v => {
        const item = {
          fid: v.fid,
          ftestPerson: v.ftestPerson,
          fcheckPerson: v.fcheckPerson,
          fprojectName: v.fprojectName,
          fsampleName: v.fsampleName,
          fnucleateCode: v.fnucleateCode,
          fgeneCode: v.fgeneCode,
          fsize: v.fsize,
          fconcentration: v.fconcentration,
          fnf: v.fnf,
          fsampleSize: v.fsampleSize,
          fcycle: v.fcycle,
          fdilutionConcentration: v.fdilutionConcentration,
          finputQuantity: v.finputQuantity,
          ftransformVolume: v.ftransformVolume,
          ftransformConcentration: v.ftransformConcentration,
          fresultName: v.fresultName,
          ftaskCode: v.ftaskCode,
          fresultPosition: v.resultPosition,
          fsamplePosition: v.samplePosition, // 样本孔位
          foriginSamplePosition: v.originSamplePosition, // 原始样本定位
          ftargetPosition: v.targetPosition, // 目标定位
          ftargetMaterial: v.targetMaterial // 目标耗材
        }
        list.push(item)
      })
      return {
        tsTransformDataDTOList: list,
        ftestPerson: this.form.detector,
        fcheckPerson: this.form.auditor
      }
    },
    async handleSubmit () {
      await Promise.all([this.handleValidate(), this.handleValidForm()])
      const params = this.setParams()
      if (params.tsTransformDataDTOList.some(v => v.ftransformConcentration === '0')) {
        await this.$confirm('导入结果内存在浓度为0的数据，请确认数据是否正确？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      if (params.fcheckPerson.some(v => params.ftestPerson.findIndex(vv => v === vv) !== -1)) {
        this.$message.error('检测人不能与复核人相同')
        return
      }
      const {res} = await awaitWrap(saveResult(params, this.type))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('回填成功，请在已完成页面查看回填结果。')
        return true
      } else {
        if (res && res.data) {
          this.$showSequencingErrorDialog({tableData: res.data, isShowButton: false})
        } else {
          this.$message.error(res.message)
        }
      }
      this.loading = false
    }
  }
}
</script>

<style scoped>

</style>
