<template>
  <div>
    <nav class="operateBar">
      <div>
        <template>
          <el-button
            :loading="downloadOrderLoading"
            size="medium"
            type="primary"
            @click="handleDownloadOrder">{{downloadOrderLoading ? '正在下载' : '订单下载'}}</el-button>
        </template>
        <template>
          <el-button
            :loading="downloadSampleTableLoading"
            size="medium"
            type="primary"
            @click="handleDownloadSampleTable">{{downloadSampleTableLoading ? '正在下载' : '到样表下载'}}</el-button>
        </template>
      </div>
      <div>
        <el-select
            v-model="form.content"
            size="medium"
            placeholder="请选择"
            style="width: 150px;"
            clearable
            @clear="handleReset"
            @change="clearInput">
          <template v-for="(v, k) in searchOptions">
            <el-option :key="k" :label="v.label" :value="k"></el-option>
          </template>
        </el-select>
        <template v-if="form.content === 'status'">
          <el-select
            v-model="form.input"
            size="medium"
            placeholder="请选择"
            style="width: 250px;margin: 0 20px;"
            clearable
            @clear="handleReset">
            <template v-for="item in statusOptionsSearch">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </template>
        <template v-else-if="form.content">
          <el-input
                  v-model="form.input"
                  :disabled="!form.content"
                  style="width: 250px;margin: 0 20px;"
                  size="medium"
                  clearable
                  placeholder="请输入"
                  @clear="handleReset"
                  @keyup.enter.native="handleSearch"></el-input>
        </template>
        <template v-else>
          <el-input
                  v-model="form.input"
                  :disabled="!form.content"
                  style="width: 250px;margin: 0 20px;"
                  size="medium"
                  clearable
                  placeholder="请输入"
                  @clear="handleReset"
                  @keyup.enter.native="handleSearch"></el-input>
        </template>
        <el-button size="medium" type="primary" @click="handleSearch">查询</el-button>
      </div>
    </nav>
    <div style="padding: 0 20px;">
      <el-table
        ref="table"
        :data="tableData"
        class="table"
        height="calc(100vh - 65px - 45px - 20px - 76px - 42px - 20px - 15px)"
        row-key="id"
        @select="handleSelect"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column label="订单编号" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
            <!--<el-button type="text" @click="handleCheck(scope.row.realData, 2, false)">{{scope.row.orderNum}}</el-button>-->
            <span
              style="color: var(--primary-color);font-size: 14px;cursor: pointer"
              @click="handleCheck(scope.row.realData, 2, false)">{{scope.row.orderNum}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderTypeText" label="订单类型" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="detectType" label="检测类型" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderStatusText" label="订单状态" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleNum" label="样本数量" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="util" label="送检单位" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="submitDate" label="提交日期" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <template v-if="scope.row.realData.orderStatus === 1">
              <el-button type="text" @click.stop="handleCheck(scope.row, 1, false)">审核</el-button>
              <el-button type="text" @click.stop="handleCheck(scope.row, 2, true)">编辑</el-button>
            </template>
            <template v-else-if="scope.row.realData.orderStatus === 2">
              <el-button type="text" @click.stop="handleCheck(scope.row, 2, true)">编辑</el-button>
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
              :page-sizes="pageSizes"
              :page-size="pageSize"
              :current-page.sync="currentPage"
              :total="totalPage"
              style="background: #ffffff;"
              layout="total, sizes, prev, pager, next, jumper, slot"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
      </el-pagination>
    </div>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
export default {
  name: 'orderReview',
  mixins: [mixins.tablePaginationCommonData],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      downloadOrderLoading: false,
      downloadSampleTableLoading: false,
      form: {
        content: '',
        input: ''
      },
      formSubmit: {},
      searchOptions: {
        orderCode: {label: '订单编号'},
        // ftype: {label: '订单类型'},
        // fcreateTime: {label: '暂存日期'},
        projectCode: {label: '项目编号'},
        projectName: {label: '项目名称'},
        detectType: {label: '检测类型'},
        status: {label: '订单状态'}
      },
      typeOptions: {
        1: '上机文库样本订单',
        2: '组织核酸样本订单'
      },
      detectTypeOptions: {},
      statusOptions: {
        // 0: '草稿',
        1: '待审核',
        2: '已审核',
        // 3: '已撤回',
        4: '驳回'
        // 5: '驳回后撤回'
      },
      statusOptionsSearch: [
        // {label: '草稿', value: 0},
        {label: '待审核', value: 1},
        {label: '已审核', value: 2},
        // {label: '已撤回', value: 3}
        {label: '驳回', value: 4}
        // {label: '驳回后撤回', value: 5}
      ],
      qualityControlResultsDialogVisible: false,
      qualityControlResultsDialogData: {
        type: 1,
        orderId: '',
        btnType: 1 // 1：质控结果 2：申请检测
      }
    }
  },
  methods: {
    getData () {
      let data = {
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      if (this.formSubmit.content) {
        data[this.formSubmit.content] = this.formSubmit.input
      }
      this.$ajax({
        url: '/order/get_order_audit_list',
        data: data,
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows
          this.tableData = []
          this.selectedRows.clear()
          rows.forEach(v => {
            let item = {
              id: v.orderId,
              orderNum: v.orderCode,
              orderType: v.type,
              orderTypeText: this.typeOptions[v.type],
              detectType: v.detectType,
              orderStatus: v.status,
              orderStatusText: this.statusOptions[v.status] || null,
              sampleNum: v.sampleCount,
              waitDetectNum: v.pendingDetection,
              childOrderNum: v.childOrderNumber,
              projectCode: v.projectCode,
              projectName: v.projectName,
              customerName: v.customerName,
              util: v.sendUnit,
              submitDate: v.submitTime
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            item.rejectReason = v.rejectReason
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleReset () {
      this.form = {
        content: '',
        input: ''
      }
      this.handleSearch()
    },
    // 下拉框发生改变时，清空input
    clearInput () {
      this.form.input = ''
    },
    handleSearch () {
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData()
    },
    // 查看
    handleCheck (row, type, editMode) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type,
          orderId: row.id,
          editMode: editMode
        }
      })
      let path = ''
      if (row.orderType === 1) path = `/business/sub/orderLibraryDetail?code=${row.orderNum}`
      if (row.orderType === 2) path = `/business/sub/orderTissueDetail?code=${row.orderNum}`
      if (path) util.openNewPage(path)
    },
    // 删除
    handleDelete (row) {
      this.$confirm('确定删除该条订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/cos/delete_order',
          data: {
            fid: row.id
          }
        }).then(res => {
          if (res.statusCode === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 撤回
    handleRevoke (row) {
      this.$confirm('该订单已提交审核，是否确认撤回?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/cos/delete_order',
          data: {
            fid: row.id
          }
        }).then(res => {
          if (res.statusCode === this.SUCCESS_CODE) {
            this.$message.success('撤回成功！请在「订单异常」模块处理被撤回的订单')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 显示质控结果弹窗
    handleShowQualityControlResultsDialog (row) {
      console.log(row)
      this.qualityControlResultsDialogData = {
        type: row.orderType,
        orderId: row.id
      }
      this.qualityControlResultsDialogVisible = true
    },
    // 下载
    handleDownloadOrder () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下载的数据')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.downloadOrderLoading = true
      this.$ajax({
        url: '/order/download_order',
        data: {
          orderIds: ids.toString()
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadOrderLoading = false
      })
    },
    // 下载到样表
    handleDownloadSampleTable () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要下载的数据')
        return
      }
      let isRightStauts = [...this.selectedRows.values()].every((v) => {
        return v.orderStatus === 2
      })
      if (!isRightStauts) {
        this.$message.error('只能下载已审核的到样表')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.downloadSampleTableLoading = true
      this.$ajax({
        url: '/order/download_receive_sample_table',
        data: {
          orderIds: ids.toString()
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadSampleTableLoading = false
      })
    },
    // 日期组件没有清空事件，所以使用change事件
    handleDateChange (val) {
      if (!val) {
        this.handleReset()
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
