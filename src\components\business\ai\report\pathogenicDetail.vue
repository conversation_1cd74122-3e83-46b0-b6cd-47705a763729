<template>
  <div class="desc">
    <!--突变详情-->
    <div class="card-wrapper">
      <el-table
        :data="mutationDetailTb"
        border
        style="width: 100%">
        <el-table-column prop="gene" label="基因" width="180"></el-table-column>
        <el-table-column prop="transcript" label="转录本" width="180"></el-table-column>
        <el-table-column prop="exinId" label="功能区域"></el-table-column>
        <el-table-column prop="mutationName" label="突变名称">
          <template slot-scope="scope">
            <div v-html="scope.row.mutationName"></div>
          </template>
        </el-table-column>
        <el-table-column prop="zygosity" label="纯合/杂合"></el-table-column>
        <el-table-column prop="mutationType" label="变异意义"></el-table-column>
        <el-table-column prop="geneticCancer" label="遗传性肿瘤综合征/遗传方式"></el-table-column>
      </el-table>
    </div>
    <!--基因变异分析-->
    <div class="card-wrapper" style="margin-top: 20px;">
      <h3>基因变异解析</h3>
      <el-table
        :data="analysisOfGeneVariation"
        border
        style="width: 100%">
        <el-table-column prop="gene" label="基因" width="180"></el-table-column>
        <el-table-column prop="mutationName" label="突变名称">
          <template slot-scope="scope">
            <div v-html="scope.row.mutationName"></div>
          </template>
        </el-table-column>
        <el-table-column prop="geneMutationAnalysis" label="基因变异解析">
          <template slot-scope="scope">
            <div v-html="scope.row.geneMutationAnalysis"></div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--参考文献-->
    <div class="card-wrapper" style="margin-top: 20px;">
      <h3>参考文献</h3>
      <div :key="index" v-for="(doc, index) in analysisDocs">
        <div style="margin: 10px 0;">{{doc}}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      analysisDocs: [],
      analysisOfGeneVariation: [],
      mutationDetailTb: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_pathogenic_variation_details',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        console.log(data)
        const info = data || {}
        const analysisDocs = info.analysisDocs || []
        const analysisOfGeneVariation = info.analysisOfGeneVariation || []
        const mutationDetailTb = info.mutationDetailTb || []
        this.analysisDocs = analysisDocs
        this.analysisOfGeneVariation = analysisOfGeneVariation
        this.mutationDetailTb = mutationDetailTb
      }
    }
  }
}
</script>

<style scoped></style>
