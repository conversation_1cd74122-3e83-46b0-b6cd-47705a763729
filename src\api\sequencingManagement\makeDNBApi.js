import { myAjax } from '@/util/ajax'

/**
 * 获取pooling列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205700&itf=5799142
 * @param options
 * @returns {*}
 */
export function getMakeDNBList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/makeDNB/get_make_dnb_list',
    data: data,
    ...options
  })
}

/**
 * 下载任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799132
 * @param options
 * @returns {*}
 */
export function downloadTask (data, options = {}) {
  return myAjax({
    url: '/experiment/makeDNB/export_task',
    responseType: 'blob',
    data: data,
    ...options
  })
}
/**
 * 下载任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205700&itf=5799157
 * @param options
 * @returns {*}
 */
export function downloadScheduler (data, options = {}) {
  return myAjax({
    url: '/experiment/makeDNB/export_tabulate',
    responseType: 'blob',
    data: data,
    ...options
  })
}

/**
 * 获取上机测序配置
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205700&itf=5799193
 * @param options
 * @returns {*}
 */
export function getConfig (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/makeDNB/get_sequence_configuration',
    data: data,
    ...options
  })
}

/**
 * 测序上机配置
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205700&itf=5799157
 * @param options
 * @returns {*}
 */
export function saveConfig (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/makeDNB/sequence_configuration',
    data: data,
    ...options
  })
}
