<template>
  <div style="height: 100%;">
    <div class="btn">
      <el-button size="mini" type="primary" @click="handleDelete">删除</el-button>
      <el-button size="mini" type="primary" @click="handleSignReport">标记报出</el-button>
      <el-button size="mini" type="primary" @click="handleSignNoReport">取消报出</el-button>
      <el-button type="primary" size="mini" @click="handleMutationNote(1)">突变备注</el-button>
    </div>
    <div class="card-wrapper">
      <el-table
        :data="tableData"
        ref="table"
        class="table"
        size="mini"
        border
        height="calc(100vh - 40px - 110px - 32px - 42px - 40px - 20px - 32px)"
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick"
        @expand-change="handleExpandChange">
        <el-table-column type="expand" fixed="left">
          <template slot-scope="props">
            <div style="margin-left: 100px">
              <el-table
                :data="props.rows"
                ref="table"
                class="table"
                size="mini"
                border
                style="width: 100%;"
                @select="handleSelect"
                @select-all="handleSelectAll"
                @row-click="handleRowClick">
                <el-table-column label="Gene Symbol" prop="geneSymbol" min-width="120px"></el-table-column>
                <el-table-column label="pHGVS" prop="phgvs" min-width="120px"></el-table-column>
                <el-table-column label="ExIn_ID" prop="exinId" min-width="120px"></el-table-column>
                <el-table-column label="Transcript" prop="transcript" min-width="120px"></el-table-column>
                <el-table-column label="1000G EAS AF" prop="1000gAsnAf" min-width="120px"></el-table-column>
                <el-table-column label="ExAC EAS AF" prop="exacEasAf" min-width="120px"></el-table-column>
                <el-table-column label="GAD AF" prop="gadAf" min-width="120px"></el-table-column>
                <el-table-column label="GAD HomoAlt Count" prop="gadHomeAltCount" min-width="180px"></el-table-column>
                <el-table-column label="rsID" prop="rsId" min-width="120px"></el-table-column>
                <el-table-column label="ClinVar Significant" prop="clinVarID" min-width="180px"></el-table-column>
                <el-table-column label="A.Ratio" prop="aRatio" min-width="120px"></el-table-column>
                <el-table-column label="Ens Polyphen2HumVar" prop="ensPolyphen2humvarScore" min-width="120px"></el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column label="比对结果" prop="isRead" min-width="120px">
          <template slot-scope="scope">
           <div  v-if="scope.row.isRead === '是' && scope.row.geneticMutationReviewStatus !== 1">已解读</div>
           <div  v-if="scope.row.isRead === '是' && scope.row.geneticMutationReviewStatus === 1" style="color: #FFAA00">已解读，待复核</div>
           <div v-if="scope.row.isRead === '否'" style="color: #F56C6C">未解读</div>
           <div v-if="scope.row.isRead === '作图'">未解读(画图用)</div>
           <div v-if="scope.row.isRead === '未参与'" style="color: #409EFF">未参与</div>
          </template>
        </el-table-column>
        <el-table-column label="inReport" prop="inReport" min-width="120px">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleToReport(scope.row.readsPlot)">{{scope.row.inReport}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="isReport" prop="report" min-width="120px"></el-table-column>
        <el-table-column label="最后修改时间" prop="latestUpdateTime" min-width="140px"></el-table-column>
        <el-table-column label="ClinVar ID" prop="clinVarID" min-width="120px"></el-table-column>
        <el-table-column label="ClinVar Significant" prop="clinVarSignificant" min-width="180px" show-overflow-tooltip></el-table-column>
        <el-table-column label="Gene Symbol" prop="geneSymbol" min-width="120px">
          <template slot-scope="scope">
            <div v-if="!scope.row.geneticMutationUrl">{{scope.row.geneSymbol}}</div>
            <el-button v-else size="mini" type="text" @click="handleOpenNewPage(scope.row.geneticMutationUrl)">{{scope.row.geneSymbol}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="cHGVS" prop="chgvs" min-width="120px"></el-table-column>
        <el-table-column label="pHGVS" prop="phgvs" min-width="120px"></el-table-column>
        <el-table-column label="Zygosity" prop="zygosity" min-width="120px"></el-table-column>
        <el-table-column label="ExIn_ID" prop="exinId" min-width="120px"></el-table-column>
        <el-table-column label="Function" prop="function" min-width="120px"></el-table-column>
        <el-table-column label="Transcript" prop="transcript" min-width="120px"></el-table-column>
        <el-table-column label="1000G AF" prop="1000gAf" min-width="120px"></el-table-column>
        <el-table-column label="1000G EAS AF" prop="1000gAsnAf" min-width="120px"></el-table-column>
        <el-table-column label="ExAC AF" prop="exacAf" min-width="120px"></el-table-column>
        <el-table-column label="ExAC EAS AF" prop="exacEasAf" min-width="120px"></el-table-column>
        <el-table-column label="ExAC EAS HomoAlt Count" prop="exacEasHomoAltCount" min-width="120px"></el-table-column>
        <el-table-column label="GAD AF" prop="gadAf" min-width="120px"></el-table-column>
        <el-table-column label="GAD EAS AF" prop="gadEasAf" min-width="120px"></el-table-column>
        <el-table-column label="GAD HomoAlt Count" prop="gadHomeAltCount" min-width="180px"></el-table-column>
        <el-table-column label="Panel AlleleFreq" prop="panelAlleleFreq" min-width="180px"></el-table-column>
        <el-table-column label="rsID" prop="rsId" min-width="120px"></el-table-column>
        <el-table-column label="A.depth" prop="aDepth" min-width="180px"></el-table-column>
        <el-table-column label="A.Ratio" prop="aRatio" min-width="120px"></el-table-column>
        <el-table-column label="Ens SIFT" prop="ensSiftScore" min-width="120px"></el-table-column>
        <el-table-column label="Ens Polyphen2HumVar" prop="ensPolyphen2humvarScore" min-width="180px"></el-table-column>
        <el-table-column key="4-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
          <template slot-scope="scope">
            <el-tooltip placement="top">
              <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
              <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
      <mutation-note-dialog
        :gene-type="geneType"
        :id="id"
        :pvisible.sync="mutationNoteVisible"
        @dialogConfirmEvent="getData"
      ></mutation-note-dialog>
    </div>
  </div>
</template>

<script>
import util from '../../../../util/util'
import mixins from '../../../../util/mixins'
import mutationNoteDialog from './mutationNoteDialog'

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    mutationNoteDialog
  },
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: [],
      geneType: '1',
      id: null,
      mutationNoteVisible: false, // 突变备注弹窗
      selectedRows: new Map()
    }
  },
  methods: {
    async getData () {
      this.tableData = await this.getSnvList()
    },
    // 获取snv数据
    async getSnvList () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_big_ai_snvs',
        data: {
          analysisRsId: this.analysisRsId || this.$route.query.oxym,
          currentPage: this.currentPage,
          pageSize: this.pageSize
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.totalPage = data.total
        this.selectedRows = new Map()
        let tableData = []
        rows.forEach(v => {
          let item = {
            isRead: v.fisRead,
            inReport: v.finReport,
            report: v.freport,
            fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
            latestUpdateTime: v.flatestUpdateTime,
            geneSymbol: v.fgeneSymbol,
            chgvs: v.fchgvs,
            geneticMutationReviewStatus: v.geneticMutationReviewStatus,
            phgvs: v.fphgvs,
            zygosity: v.fzygosity,
            exinId: v.fexinId,
            function: v.ffunction,
            transcript: v.ftranscript,
            '1000gAf': v.f1000gAf,
            '1000gAsnAf': v.f1000gAsnAf,
            exacAf: v.fexacAf,
            exacEasAf: v.fexacEasAf,
            geneticMutationUrl: v.geneticMutationUrl,
            ensSiftScore: v.fensSiftScore,
            ensPolyphen2humvarScore: v.fensPolyphen2humvarScore,
            exacEasHomoAltCount: v.fexacEasHomoAltCount,
            gadAf: v.fgadAf,
            gadEasAf: v.fgadEasAf,
            gadHomeAltCount: v.fgadHomeAltCount,
            panelAlleleFreq: v.fpanelAlleleFreq,
            rsId: v.frsId,
            clinVarID: v.fclinVarID,
            clinVarSignificant: v.fclinVarSignificant,
            aDepth: v.faDepth,
            aRatio: v.faRatio,
            readsPlot: v.freadsPlot,
            id: v.fid
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          tableData.push(item)
        })
        return tableData
      }
    },
    // 设置突变备注
    handleMutationNote (geneType) {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      this.$ajax({
        url: '/read/bigAi/is_mutation_remark',
        data: {
          analysisRsId: this.analysisRsId || this.$route.query.oxym
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.geneType = geneType
          this.id = [...this.selectedRows.keys()][0]
          this.mutationNoteVisible = true
        }
      })
    },
    // 删除snv数据
    handleDelete () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      let id = row.id
      this.$ajax({
        url: '/read/bigAi/delete_h_snv',
        data: {
          fid: id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleOpenNewPage (page) {
      window.open(page, '_blank')
    },
    // 展开行
    handleExpandChange (row, expandedRows) {
      console.log(row, expandedRows)
    },
    // 标记报出
    handleSignReport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      let row = [...this.selectedRows.values()]
      let ids = row.map(v => v.id)
      this.$ajax({
        url: '/read/unscramble/sign_report',
        data: {
          fids: ids.join(','),
          type: 4
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('标记报出成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 取消标记报出
    handleSignNoReport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      let row = [...this.selectedRows.values()]
      let ids = row.map(v => v.id)
      this.$ajax({
        url: '/read/unscramble/not_sign_report',
        data: {
          fids: ids.join(','),
          type: 4
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('取消报出成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 跳转到报告页面
    handleToReport (path) {
      window.open(path, '_blank')
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
