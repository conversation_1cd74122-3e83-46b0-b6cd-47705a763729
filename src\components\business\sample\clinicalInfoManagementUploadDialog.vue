<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="文件上传"
      width="30%"
      @open="handleOpen"
    >
      <el-upload
        ref="upload"
        :on-success="handleOnSuccess"
        :on-error="handleOnError"
        :on-change="handleChange"
        :data="uploadParams"
        :http-request="uploadFile"
        :headers="headers"
        :auto-upload="false"
        :file-list="fileList"
        :before-upload="handleBeforeUpload"
        :action="uploadUrl"
        multiple>
        <el-button size="mini" type="primary">点击上传</el-button>
        <span slot="tip" style="margin-left: 10px; font-size: 12px; color: #F56C6C;">只允许上传图片或PDF文件，且文件不能超过10M</span>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util from '../../../util/util'
export default {
  name: 'clinicalInfoManagementUploadDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    ptype: {
      type: String,
      default: '',
      required: true
    },
    psampleBasicId: {
      type: [Number, String],
      default: '',
      required: true
    }
  },
  watch: {},
  computed: {},
  data () {
    return {
      type: '',
      picNum: '',
      sampleBasicId: '',
      uploadUrl: constants.JS_CONTEXT + '/sample/clinical/upload_pic_or_pdf',
      loading: false,
      fileList: [],
      headers: {
        'user-id': util.decryptBase64(util.getSessionInfo('loginId'))
      },
      uploadParams: {
        sampleBasicId: null,
        picNum: ''
      },
      fileData: new FormData()
    }
  },
  methods: {
    handleOpen () {
      this.type = this.ptype
      this.sampleBasicId = this.psampleBasicId
      switch (this.type) {
        case 'followupImgNames':
          this.uploadParams = {
            sampleBasicId: this.psampleBasicId,
            picNum: 3
          }
          this.fileList = []
          break
        case 'imgNames':
          this.uploadParams = {
            sampleBasicId: this.psampleBasicId,
            picNum: 4
          }
          this.fileList = []
          break
        case 'sampleOrderImg1':
          this.uploadParams = {
            sampleBasicId: this.psampleBasicId,
            picNum: 1
          }
          this.fileList = []
          break
        case 'sampleOrderImg2':
          this.uploadParams = {
            sampleBasicId: this.psampleBasicId,
            picNum: 2
          }
          this.fileList = []
          break
      }
    },
    uploadFile (file) {
      this.fileData.append('file', file.file) // append增加数据
    },
    handleConfirm () {
      if (this.fileList.length > 0) {
        let valid = this.fileList.every(v => {
          if (/\.(png|jpg|jpeg|zip|rar|pdf)$/i.test(v.name)) {
            if (v.size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
              console.log(v.size)
              this.$message.error(`文件: ${name} ,大小超过10M，无法上传`)
              return false
            }
            return true
          } else {
            this.$message.error('只能上传图片或压缩文件')
            return false
          }
        })
        if (!valid) {
          this.$message.error('请检查，上传文件大小不能超过10MB!')
        } else {
          this.submitUpload()
        }
      } else {
        this.$message.error('请上传文件')
      }
    },
    submitUpload () {
      this.loading = true
      let data = {
        file: this.fileList.map(v => v.raw),
        sampleBasicId: this.uploadParams.sampleBasicId,
        picNum: this.uploadParams.picNum
      }
      this.$ajax({
        url: '/sample/clinical/upload_pic_or_pdf',
        data: data,
        isFormData: true
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let fileAbsolutePaths = data.fileAbsolutePath ? data.fileAbsolutePath.split(',') : []
          let paths = data.path ? data.path.split(',') : []
          let groups = data.group ? data.group.split(',') : []
          let fileNames = data.fileNames ? data.fileNames.split(',') : []
          let tableData = []
          let item = {}
          fileAbsolutePaths.forEach((v, i) => {
            item = {
              path: paths[i],
              group: groups[i],
              fileName: fileNames[i],
              fileAbsolutePath: fileAbsolutePaths[i]
            }
            tableData.push(item)
          })
          this.$emit('uploadDialogConfirmEvent', tableData)
        } else {
          this.$message.error(result.message)
        }
        this.$refs.upload.clearFiles()
      }).finally(() => {
        this.loading = false
      })
      // let fieldData = this.uploadData.fieldData // 缓存，注意，fieldData不要与fileData看混
      // if (this.fileList.length === 0) {
      //   this.$message({message: '请先选择文件', type: 'warning'})
      // } else {
      //   const isLt10M = this.fileList.every(file => file.size / 1024 / 1024 < 10)
      //   if (!isLt10M) {
      //     this.$message.error('请检查，上传文件大小不能超过100MB!')
      //   } else {
      //     this.fileData = new FormData() // new formData对象
      //     this.$refs.upload.submit() // 提交调用uploadFile函数
      //     // this.fileData.append('pathId', fieldData.id) // 添加机构id
      //     // this.fileData.append('loginToken', this.loginToken) // 添加token
      //     // post(this.baseUrlData.url_02 + ':8090/personality/uploadExcel', this.fileData).then((response) => {
      //     //   if (response.data.code === 0) {
      //     //     this.$message({
      //     //       message: '上传成功',
      //     //       type: 'success'
      //     //     })
      //     //     this.fileList = []
      //     //   } else {
      //     //     this.$message({
      //     //       message: response.data.desc,
      //     //       type: 'error'
      //     //     })
      //     //   }
      //     // })
      //   }
      // }
    },
    handleOnSuccess (res, file, fileList) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data
        let fileAbsolutePaths = data.fileAbsolutePath ? data.fileAbsolutePath.split(',') : []
        let paths = data.path ? data.path.split(',') : []
        let groups = data.group ? data.group.split(',') : []
        let fileNames = data.fileNames ? data.fileNames.split(',') : []
        let tableData = []
        let item = {}
        fileAbsolutePaths.forEach((v, i) => {
          item = {
            path: paths[i],
            group: groups[i],
            fileName: fileNames[i],
            fileAbsolutePath: fileAbsolutePaths[i]
          }
          tableData.push(item)
        })
        this.$emit('uploadDialogConfirmEvent', tableData)
      } else {
        this.$message.error(res.message)
      }
      this.$refs.upload.clearFiles()
    },
    handleOnError () {
      this.loading = false
    },
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(png|jpg|jpeg|zip|rar)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过10M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传图片或压缩文件')
        return false
      }
    },
    handleChange (file, fileList) {
      let existFile = fileList.slice(0, fileList.length - 1).find(f => f.name === file.name)
      if (existFile) {
        this.$message.error('当前文件已经存在!')
        fileList.pop()
      }
      this.fileList = fileList
    }
  }
}
</script>

<style scoped>

</style>
