<template>
  <div class="card-wrapper">
    <el-descriptions :label-style="{width: '120px'}" :column="1" class="desc" size="mini" border>
      <el-descriptions-item label="致病突变">
        {{risk.pathogenicMutationCount}}
      </el-descriptions-item>
      <el-descriptions-item label="疑似致病突变">
        {{risk.suspectedTherapeuticMutationCount}}
      </el-descriptions-item>
      <el-descriptions-item label="临床意义未明变异">
        {{risk.mutationsOfUnknownClinicalSignificanceCount}}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      risk: {}
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_cancer_genetic_risk',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || {}
        this.risk = {
          id: info.fid,
          pathogenicMutationCount: info.pathogenicMutationCount,
          suspectedTherapeuticMutationCount: info.suspectedTherapeuticMutationCount,
          mutationsOfUnknownClinicalSignificanceCount: info.mutationsOfUnknownClinicalSignificanceCount
        }
      }
    }
  }
}
</script>

<style scoped></style>
