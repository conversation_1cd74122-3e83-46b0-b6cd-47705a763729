<template>
  <el-dialog :title="title" :visible.sync="visible" width="1200px" :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="handleClose" class="abnormal-desc-dialog" @opened="handleOpen">
    <el-form ref="form" :model="form" :rules="rules" :disabled="type === 3" label-width="110px" size="mini">
      <!-- 标准说明 -->
      <div class="form-section">
        <div class="section-title">标准说明</div>
        <el-form-item label="异常描述标准" prop="descName" class="required-label">
          <el-input v-model.trim="form.descName" placeholder="请输入异常描述标准" maxlength="20" show-word-limit
            size="mini" clearable></el-input>
        </el-form-item>
        <el-form-item label="说明" prop="notes">
          <el-input v-model.trim="form.notes" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入说明"
            maxlength="500" show-word-limit size="mini" clearable>
          </el-input>
        </el-form-item>
      </div>

      <!-- 异常描述配置 -->
      <div class="form-section">
        <div class="section-title">异常描述配置</div>
        <div class="tips-container">
          <div class="tips-title">
            温馨提示：异常描述配置时，不同工序下，异常描述内容不同。且需要根据核酸等级质控输出对应配置内容，样本首次提取和重提取的描述内容也有差异，故而需要区分工序、等级和样本检测情况进行配置。</div>
        </div>

        <div v-for="(standard, standardIndex) in form.standards" :key="'standard-' + standardIndex"
          class="standard-container">
          <div class="standard-header">
            <span>标准 {{ standardIndex + 1 }}</span>
            <div class="standard-actions">
              <el-button v-if="standardIndex === form.standards.length - 1 && form.standards.length < 5" type="primary"
                icon="el-icon-plus" circle size="mini" @click="addStandard" class="circle-button"></el-button>
              <el-button v-if="form.standards.length > 1" type="danger" icon="el-icon-delete" circle size="mini"
                @click="removeStandard(standardIndex)" class="circle-button"></el-button>
            </div>
          </div>
          <div class="standard-content">
            <div class="standard-content-item">
            <el-form-item label="工序环节">
              <el-select v-model="standard.processSteps" multiple placeholder="请选择工序环节" collapse-tags style="width: 300px" size="mini" clearable>
              <el-option
                v-for="item in processStepOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="核酸等级质控">
            <el-select v-model="standard.grades" multiple placeholder="请选择核酸等级质控" collapse-tags style="width: 300px" size="mini" clearable>
              <el-option
                    v-for="item in gradeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否重提">
            <el-select v-model="standard.reExtraction" placeholder="请选择是否重提" style="width: 100%" size="mini" clearable>
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          </div>
          </div>

          <el-form-item label="异常描述">
            <div class="formula-display-container" @click="openDescEditor(standardIndex)">
              <formula-editor
                  ref="formulaEditor"
                  style="width: 100%"
                  v-model="standard.abnormalDesc"
                  :variable-groups-data="variableGroups"
                  :disabled="true"
                ></formula-editor>
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="mini">取 消</el-button>
      <el-button v-if="type !== 3" type="primary" @click="submitForm" size="mini">确 定</el-button>
    </span>

    <!-- 异常描述编辑器弹窗 -->
    <el-dialog title="异常描述编辑" :visible.sync="editorVisible" width="800px" append-to-body :close-on-click-modal="false" @opened="handleEditorDialogOpened">
      <formula-editor
        ref="editorFormula"
        v-model="currentDesc"
        :variable-groups-data="variableGroups"
        @need-input="showTextInputDialog"
      ></formula-editor>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editorVisible = false" size="mini">取 消</el-button>
        <el-button  type="primary" @click="saveDesc" size="mini">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 文本变量输入弹窗 -->
    <el-dialog title="文本变量输入" :visible.sync="textInputVisible" width="400px" append-to-body
      :close-on-click-modal="false">
      <el-form :model="textInputForm" :rules="textInputRules" ref="textInputForm" label-width="80px">
        <el-form-item label="文本内容" prop="text">
          <el-input v-model="textInputForm.text" placeholder="请输入文本内容" maxlength="30" show-word-limit size="mini" clearable>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="textInputVisible = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="mini">确 定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import { getAbnormalDescDetail, updateAbnormalDesc, getVarListAoi } from '../../../../api/basicDataManagement/abnormalDescApi'
import FormulaEditor from './FormulaEditor.vue'
import { awaitWrap } from '../../../../util/util'
export default {
  mixins: [mixins.dialogBaseInfo],
  components: {
    FormulaEditor
  },
  props: {
    pvisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: [String, Number],
      default: null
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  computed: {
    filteredVariableGroups () {
      return this.variableGroups.map(group => {
        return {
          ...group,
          variables: group.variables.filter(variable =>
            variable.name.toLowerCase().includes(this.variableSearchKey.toLowerCase())
          )
        }
      })
    }
  },
  data () {
    return {
      form: {
        descName: '',
        notes: '',
        standards: [this.getDefaultStandard()]
      },
      rules: {
        descName: [
          { required: true, message: '请输入异常描述标准', trigger: 'blur' }
        ]
      },
      title: '',
      // 异常描述编辑器相关
      editorVisible: false,
      currentStandardIndex: 0,
      currentDesc: '核酸提取:{{extractionAmount}}{{remainingAmount}}',
      // 文本变量输入相关
      textInputVisible: false,
      textInputForm: {
        text: ''
      },
      textInputRules: {
        text: [
          { required: true, message: '请输入文本内容', trigger: 'blur' }
        ]
      },
      processStepOptions: [
        { label: 'FFPE DNA提取', value: 'FFPE DNA提取' },
        { label: '组织RNA提取', value: '组织RNA提取' },
        { label: '组织DNARNA提取', value: '组织DNARNA提取' },
        { label: '全基因组DNA提取', value: '全基因组DNA提取' },
        { label: '血浆游离DNA提取', value: '血浆游离DNA提取' },
        { label: '核酸检测', value: '核酸检测' }
      ],
      gradeOptions: [
        { label: '合格', value: '合格' },
        { label: '不合格', value: '不合格' },
        { label: '风险', value: '风险' }
      ],
      // 变量组
      variableGroups: [
        {
          title: '基本变量',
          variables: [
          ]
        },
        {
          title: '核酸相关',
          variables: [
          ]
        },
        {
          title: '质控相关',
          variables: [
          ]
        }
      ],
      variableSearchKey: '',
      activeVarTab: '基本变量',
      recentVariables: []
    }
  },
  methods: {
    handleOpen () {
      this.resetForm()
      const titles = {
        1: '复制新增',
        2: '修改',
        3: '查看详情'
      }
      this.getVarList()
      this.title = titles[this.type] || '新增'
      this.id && this.getDetailData()
    },
    // 获取默认的标准对象
    getDefaultStandard () {
      return {
        processSteps: [],
        grades: [],
        reExtraction: null,
        abnormalDesc: ''
      }
    },
    // 获取变量
    async getVarList () {
      const {res} = await awaitWrap(getVarListAoi())
      if (res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.variableGroups[0].variables = data.map(v => {
          return { name: v.chineseName, type: v.englishName, value: v.englishName }
        })
      }
    },
    // 添加标准
    addStandard () {
      if (this.form.standards.length < 5) {
        this.form.standards.push(this.getDefaultStandard())
      } else {
        this.$message.warning('最多只能添加5个标准')
      }
    },
    // 移除标准
    removeStandard (index) {
      this.$confirm('是否删除此标准?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form.standards.splice(index, 1)
      }).catch(() => { })
    },
    // 打开异常描述编辑器
    openDescEditor (standardIndex) {
      if (this.type === 3) return
      this.currentStandardIndex = standardIndex
      // 获取当前标准的异常描述，确保非空，但保留原始数据格式
      this.currentDesc = this.form.standards[standardIndex].abnormalDesc || ''
      this.editorVisible = true
      // 在下一个渲染周期刷新编辑器，但不清除数据
      this.$nextTick(() => {
        // 仅刷新展示用的编辑器
        const formulaEditors = this.$refs.formulaEditor
        if (formulaEditors && formulaEditors.length) {
          formulaEditors.forEach(editor => {
            if (editor && typeof editor.refresh === 'function') {
              editor.refresh()
            }
          })
        }

        // 刷新弹窗中编辑器
        const editorFormula = this.$refs.editorFormula
        if (editorFormula && typeof editorFormula.refresh === 'function') {
          editorFormula.refresh()
        }
      })
    },
    // 显示文本输入对话框
    showTextInputDialog () {
      this.textInputVisible = true
    },
    // 确认文本输入
    handleConfirm () {
      this.$refs.textInputForm.validate(valid => {
        if (valid) {
          this.currentDesc += this.textInputForm.text
          this.textInputVisible = false
          this.textInputForm.text = ''
        }
      })
    },
    // 保存异常描述
    saveDesc () {
      // 保存数据到表单，保留原始格式
      this.form.standards[this.currentStandardIndex].abnormalDesc = this.currentDesc
      this.editorVisible = false
    },
    // 获取详情
    async getDetailData () {
      const {res} = await awaitWrap(getAbnormalDescDetail({
        fexceptionDescriptionId: this.id,
        fisCopy: this.type === 1 ? 1 : 0
      }))
      if (res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.form = {
          descName: data.fexceptionDescriptionStandard || '',
          notes: data.fdescription || '',
          standards: data.fexceptionDescriptionConfigModelList && data.fexceptionDescriptionConfigModelList.length
            ? data.fexceptionDescriptionConfigModelList.map(item => ({
              processSteps: item.forderStep ? item.forderStep.split(',').filter(v => v) : [],
              grades: item.flevelQc ? item.flevelQc.split(',').filter(v => v) : [],
              reExtraction: item.fisReExtract,
              abnormalDesc: item.fexceptionDescription || ''
            })) : [this.getDefaultStandard()]
        }
      }
    },
    // 重置表单
    resetForm () {
      this.form = {
        descName: '',
        notes: '',
        standards: [this.getDefaultStandard()]
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    // 提交表单
    submitForm () {
      this.handleValidForm().then(async () => {
        try {
          // 在提交前处理数据，去除前后空格
          const processedStandards = this.form.standards.map(standard => {
            return {
              ...standard,
              abnormalDesc: standard.abnormalDesc ? standard.abnormalDesc.trim() : ''
            }
          })

          // 处理数据，准备提交
          const params = {
            fexceptionDescriptionStandard: this.form.descName.trim(),
            fdescription: this.form.notes.trim(),
            fexceptionDescriptionConfigModelList: processedStandards.map(standard => ({
              forderStep: standard.processSteps.join(','),
              flevelQc: standard.grades.join(','),
              fisReExtract: standard.reExtraction,
              fexceptionDescription: standard.abnormalDesc
            })),
            fexceptionDescriptionId: this.type === 2 ? this.id : null
          }
          const result = await updateAbnormalDesc(params)
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('保存成功')
            this.visible = false
            this.$emit('dialogConfirmEvent')
          }
        } catch (error) {
          this.$message.error(error.message || '保存失败')
        }
      })
    },
    // 处理编辑器弹窗完全打开后的事件
    handleEditorDialogOpened () {
      // 在编辑器弹窗完全打开后刷新编辑器，但不清除数据
      this.$nextTick(() => {
        // 刷新弹窗中编辑器
        const editorFormula = this.$refs.editorFormula
        if (editorFormula) {
          if (typeof editorFormula.refresh === 'function') {
            editorFormula.refresh()
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.abnormal-desc-dialog {
  max-height: 90vh;
}

.abnormal-desc-dialog>>>.el-dialog {
  margin-top: 5vh !important;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  border-radius: 4px;
}

.abnormal-desc-dialog>>>.el-dialog__body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  max-height: calc(90vh - 150px);
}

.abnormal-desc-dialog>>>.el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

.abnormal-desc-dialog>>>.el-dialog__footer {
  padding: 10px 20px;
  border-top: 1px solid #EBEEF5;
}

.abnormal-desc-dialog>>>.el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.form-section {
  margin-bottom: 20px;
}

.section-title {
  position: relative;
  font-size: 15px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

.tips-container {
  background-color: #f8f8f8;
  padding: 10px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.tips-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 13px;
  color: #303133;
}

.standard-container {
  border: 1px dashed #ccc;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.standard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
}

.standard-actions {
  display: flex;
  gap: 8px;
}

.required-label>>>.el-form-item__label:before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

.circle-button {
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  border-radius: 50% !important;
  min-width: 28px !important;
}

/* 表单项样式优化 */
.abnormal-desc-dialog>>>.el-input__inner,
.abnormal-desc-dialog>>>.el-textarea__inner {
  border-radius: 3px;
  border-color: #DCDFE6;
}

/* 让标签居中对齐 */
.abnormal-desc-dialog>>>.el-form-item__label {
  line-height: 28px;
  color: #606266;
}

.abnormal-desc-dialog>>>.el-form-item {
  margin-bottom: 18px;
}

/* 滚动条样式优化 */
.abnormal-desc-dialog>>> ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.abnormal-desc-dialog>>> ::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.abnormal-desc-dialog>>> ::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 公式展示容器样式 */
.formula-display-container {
  position: relative;
  width: 100%;
  min-height: 32px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.formula-display-container:hover {
  border-color: #409EFF;
}

.edit-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  padding: 3px;
  border: none;
  background: transparent;
}

.edit-button:hover {
  color: #409EFF;
}

/* 禁用状态的公式编辑器样式 */
.disabled-formula-editor {
  width: 100%;
  padding: 5px 30px 5px 10px;
}

.disabled-formula-editor >>> .formula-editor-container {
  border: none;
  background: transparent;
  min-height: 22px;
  padding: 0;
}

.disabled-formula-editor >>> .editable-area {
  cursor: pointer;
  background: transparent;
  padding: 0;
}

.disabled-formula-editor >>> .variable-tag {
  background-color: #E6F1FC;
  color: #409EFF;
  border: 1px solid #B3D8FF;
  margin: 0 2px;
  padding: 1px 5px;
  border-radius: 3px;
  display: inline-flex;
  align-items: center;
  height: 22px;
}

.disabled-formula-editor >>> .editor-toolbar {
  display: none;
}

.standard-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.standard-content-item {
  display: flex;
  gap: 15px;
}
</style>
