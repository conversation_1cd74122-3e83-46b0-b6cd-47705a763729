<template>
  <div>
    <el-scrollbar style="overflow-x: hidden">
      <div class="module">
        <div class="module-title-bar">
          <div>
            <p class="min-title">项目信息</p>
          </div>
        </div>
        <div class="content">
          <el-form
            ref="projectInfoForm"
            :model="projectInfo"
            :rules="rules"
            :show-message="false"
            label-position="right"
            label-width="110px"
            size="mini"
            inline>
            <el-form-item ref="project" label="项目编号" prop="projectCode">
              <template v-if="readOnly">
                <el-tooltip :content="projectInfo.projectCode" v-if="projectInfo.projectCode" class="item" effect="dark" placement="top">
                  <p class="form-width p">{{projectInfo.projectCode}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <div v-else class="form-width" style="display: flex;">
                <el-input
                  v-model="projectInfo.projectCode"
                  readonly
                  placeholder="请选择（必填）"
                  class="form-width"
                ></el-input>
                <el-button size="mini" type="primary" style="flex-shrink: 0;" @click="projectDialogVisible = true">选择</el-button>
              </div>
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
              <el-tooltip :disabled="!projectInfo.projectName" :content="projectInfo.projectName" class="item" effect="dark" placement="top">
                <p v-if="readOnly" class="form-width p">{{projectInfo.projectName || '-'}}</p>
                <template v-else>
                  <el-input :value="projectInfo.projectName" placeholder="系统自动根据项目编号填入" disabled class="form-width"></el-input>
                </template>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="送检单位" prop="unit">
              <el-tooltip :disabled="!projectInfo.unit" :content="projectInfo.unit" class="item" effect="dark" placement="top">
                <p v-if="readOnly" class="form-width p">{{projectInfo.unit || '-'}}</p>
                <el-input :value="projectInfo.unit" v-else placeholder="系统自动根据项目编号填入" disabled class="form-width"></el-input>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="客户姓名" prop="customer">
              <template v-if="readOnly">
                <el-tooltip :content="projectInfo.customer" v-if="projectInfo.customer" class="item" effect="dark" placement="top">
                  <p class="form-width p">{{projectInfo.customer}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <el-input v-model="projectInfo.customer" v-else placeholder="请输入（必填）" maxlength="50" class="form-width"></el-input>
            </el-form-item>
            <el-form-item ref="email" label="客户邮箱" prop="email">
              <template v-if="readOnly">
                <el-tooltip :content="projectInfo.email" v-if="projectInfo.email" class="item" effect="dark" placement="top">
                  <p class="form-width p">{{projectInfo.email}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <div v-else class="form-width" style="display: flex;">
                <el-input v-model="projectInfo.email" placeholder="请编辑（必填）" disabled class="form-width"></el-input>
                <el-button size="mini" type="primary" style="flex-shrink: 0;" @click="editEmailDialogVisible = true">编辑</el-button>
              </div>
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <template v-if="readOnly">
                <el-tooltip :content="projectInfo.phone" v-if="projectInfo.phone" class="item" effect="dark" placement="top">
                  <p class="form-width p">{{projectInfo.phone}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <el-input v-model="projectInfo.phone" v-else placeholder="请输入（必填）" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="销售联系人" prop="sale">
              <p v-if="readOnly" class="form-width p">
                <tooltips :txt-info="projectInfo.sale || '-'"></tooltips>
              </p>
              <el-input v-model="projectInfo.sale" v-else placeholder="请输入（必填）" maxlength="15" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="销售联系电话" prop="salePhone">
              <p v-if="readOnly" class="form-width p">
                <tooltips :txt-info="projectInfo.salePhone || '-'"></tooltips>
              </p>
              <el-input v-model="projectInfo.salePhone" v-else placeholder="请输入" maxlength="15" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="销售联系邮箱" prop="saleEmail">
              <p v-if="readOnly" class="form-width p">
                <tooltips :txt-info="projectInfo.saleEmail || '-'"></tooltips>
              </p>
              <el-input v-model="projectInfo.saleEmail" v-else placeholder="请输入" maxlength="50" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="送检日期" prop="sendDate">
              <p v-if="readOnly" class="form-width p">{{sendTime}}</p>
              <el-date-picker
                v-model="projectInfo.sendDate"
                v-else
                type="date"
                class="form-width"
                value-format="yyyy-MM-dd"
                placeholder="请选择（必填）">
              </el-date-picker>
            </el-form-item>
<!--            <el-form-item label="样本数量" prop="sampleCount">-->
<!--              <p v-if="readOnly" class="form-width p">{{projectInfo.sampleCount || '-'}}</p>-->
<!--              <el-input v-model="projectInfo.sampleCount" v-else placeholder="请输入" class="form-width"></el-input>-->
<!--            </el-form-item>-->
            <el-form-item label="送检人地址" prop="address">
              <p v-if="readOnly" class="form-width p">
                <tooltips :txt-info="projectInfo.address || '-'"></tooltips>
              </p>
              <el-input v-model="projectInfo.address" v-else placeholder="请输入（必填）" maxlength="200" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="订单所属片区" prop="area" style="margin-bottom: 5px;">
              <p v-if="readOnly" class="form-width p">{{areaOptions[projectInfo.area] || '-'}}</p>
              <el-select v-model="projectInfo.area" v-else class="form-width" placeholder="请选择（必填）" >
                <el-option
                  :key="k"
                  :label="v"
                  :value="k"
                  v-for="(v, k) in areaOptions">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="module">
        <div class="module-title-bar">
          <div>
            <p class="min-title">快递信息</p>
          </div>
        </div>
        <div class="content">
          <el-form  ref="courierInfo" :model="courierInfo" :rules="rules" :inline="true" :show-message="false" label-position="right" label-width="110px" size="mini">
            <el-form-item label="快递公司" prop="courierCompany">
              <p v-if="readOnly" class="form-width p">{{courierInfo.courierCompany || '-'}}</p>
              <el-select v-model="courierInfo.courierCompany" v-else class="form-width" placeholder="请选择（必填）">
                <el-option
                  :key="k"
                  :label="v"
                  :value="k"
                  v-for="(v, k) in courierCompanyLists">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="courierInfo.courierCompany === '其他'" label="其他运输公司" prop="otherCourierCompany">
              <template v-if="readOnly">
                <el-tooltip :content="courierInfo.otherCourierCompany" v-if="courierInfo.otherCourierCompany" class="item" effect="dark" placement="top">
                  <p class="p form-width">{{courierInfo.otherCourierCompany}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <el-input v-model="courierInfo.otherCourierCompany" v-else placeholder="请输入（必填）" maxlength="20" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="快递单号" prop="trackingNum">
              <template v-if="readOnly">
                <el-tooltip :content="courierInfo.trackingNum" v-if="courierInfo.trackingNum" class="item" effect="dark" placement="top">
                  <p class="form-width p">{{courierInfo.trackingNum}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <el-input v-model="courierInfo.trackingNum" v-else placeholder="请输入（必填）" maxlength="50" class="form-width"></el-input>
            </el-form-item>
            <el-form-item label="运输方式" prop="transportMethod" style="margin-bottom: 15px;">
              <p v-if="readOnly" class="form-width p">{{courierInfo.transportMethod || '-'}}</p>
              <el-select v-model="courierInfo.transportMethod" v-else class="form-width" placeholder="请选择（必填）">
                <el-option
                  :key="k"
                  :label="v"
                  :value="k"
                  v-for="(v, k) in transportMethodLists">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="courierInfo.transportMethod === '其他'" label="其他运输方式" prop="otherTransportMethod" style="margin-bottom: 15px;">
              <template v-if="readOnly">
                <el-tooltip :content="courierInfo.otherTransportMethod" v-if="courierInfo.otherTransportMethod" class="item" effect="dark" placement="top">
                  <p class="p form-width">{{courierInfo.otherTransportMethod}}</p>
                </el-tooltip>
                <p v-else class="form-width p">-</p>
              </template>
              <el-input v-model="courierInfo.otherTransportMethod" v-else placeholder="请输入（必填）" maxlength="20" class="form-width"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div v-if="false" class="module">
        <div class="module-title-bar">
          <div>
            <p class="min-title">操作记录</p>
          </div>
        </div>
        <div class="content">
          <el-form
            label-position="right"
            label-width="120px"
            size="mini"
            inline>
            <el-form-item v-if="operateInfo.status === 0" label="最近修改">
              <el-tooltip :content="operateInfo.updateTime" v-if="operateInfo.updateTime" class="item" effect="dark" placement="top">
                <p class="form-width p">{{operateInfo.updateTime}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </el-form-item>
            <el-form-item v-if="[1, 2, 4].indexOf(operateInfo.status) > -1" label="提交日期">
              <el-tooltip :content="operateInfo.submitTime" v-if="operateInfo.submitTime" class="item" effect="dark" placement="top">
                <p class="form-width p">{{operateInfo.submitTime}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </el-form-item>
            <el-form-item v-if="[2].indexOf(operateInfo.status) > -1" label="审核日期">
              <el-tooltip :content="operateInfo.reviewDate" v-if="operateInfo.reviewDate" class="item" effect="dark" placement="top">
                <p class="form-width p">{{operateInfo.reviewDate}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </el-form-item>
            <el-form-item v-if="[3].indexOf(operateInfo.status) > -1" label="撤回日期">
              <el-tooltip :content="operateInfo.revokeDate" v-if="operateInfo.revokeDate" class="item" effect="dark" placement="top">
                <p class="form-width p">{{operateInfo.revokeDate}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </el-form-item>
            <el-form-item v-if="[4].indexOf(operateInfo.status) > -1" label="驳回日期">
              <el-tooltip :content="operateInfo.rejectDate" v-if="operateInfo.rejectDate" class="item" effect="dark" placement="top">
                <p class="form-width p">{{operateInfo.rejectDate}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </el-form-item>
            <el-form-item label="操作人">
              <el-tooltip :content="operateInfo.updator" v-if="operateInfo.updator" class="item" effect="dark" placement="top">
                <p class="form-width p">{{operateInfo.updator}}</p>
              </el-tooltip>
              <p v-else class="form-width p">-</p>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-scrollbar>
    <edit-email-dialog
      :pvisible.sync="editEmailDialogVisible"
      :input-email="projectInfo.email"
      @dialogConfirmEvent="handleEditEmailDialogConfirm"/>
    <project-dialog
      :pvisible.sync="projectDialogVisible"
      @dialogConfirmEvent="handleProjectDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import editEmailDialog from './entryBaseInfoEditEmailDialog'
import projectDialog from './entryBaseInfoProjectDialog'
import {dateFormatter} from '../../../../../../util/util'
export default {
  name: 'entryBaseInfo',
  components: {
    editEmailDialog,
    projectDialog
  },
  props: {
    readOnly: Boolean,
    orderId: String | Number,
    projectData: Object, // 项目信息
    courierData: Object, // 快递信息
    operateData: Object // 线上的数据，之前前端填好了的
  },
  mounted () {
    this.getAreaOptions()
  },
  watch: {
    projectData: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.projectInfo = {...newVal}
        }
      },
      deep: true
    },
    courierData: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.courierInfo = {...newVal}
        }
      },
      deep: true
    },
    operateData: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.operateInfo = {...newVal}
        }
      },
      deep: true
    },
    libraryOperatingData: {
      handler: function (newVal) {
        if (newVal) {
          // this.readOnly = newVal.type === 2
          // this.orderId = newVal.orderId
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    libraryOperatingData () {
      return this.$store.getters.getValue('libraryOperatingData')
    },
    sendTime () {
      return dateFormatter(this.projectInfo.sendDate, false)
    }
  },
  data () {
    return {
      orderCode: '', // 订单编号
      childOrderId: '', // 子订单Id
      editEmailDialogVisible: false,
      projectDialogVisible: false,
      projectInfo: {
        projectCode: '',
        projectName: '',
        unit: '',
        customer: '',
        email: '',
        phone: '',
        sale: '',
        salePhone: '',
        saleEmail: '',
        sampleCount: '', // 样本数量
        sendDate: '',
        address: '',
        area: ''
      },
      courierInfo: {
        courierCompany: '', // 快递公司
        otherCourierCompany: '',
        trackingNum: '', // 快递单号
        transportMethod: '', // 运输方式
        otherTransportMethod: '' // 运输方式
      },
      operateInfo: {
        // 0: '草稿', 1: '待审核',  2: '已审核', 3: '已撤回',  4: '驳回',
        status: '', // 订单状态
        updateTime: '', // 最近修改
        updator: '', // 操作人
        submitTime: '', // 提交日期
        reviewDate: '', // 审核日期
        revokeDate: '', // 撤回日期
        rejectDate: '' // 驳回日期
      },
      areaLoading: false,
      areaOptions: {}, // 片区列表
      courierCompanyLists: {
        // '中集冷云': '中集冷云',
        // '城市映急': '城市映急',
        '顺丰快递': '顺丰快递',
        '四八同城': '四八同城',
        '生生物流': '生生物流',
        '其他': '其他'
      },
      transportMethodLists: {
        '干冰': '干冰',
        '冰袋': '冰袋',
        '常温': '常温',
        '其他': '其他'
      },
      rules: {
        projectCode: [
          {required: true, message: '项目编号必选', trigger: 'blur'}
        ],
        customer: [
          {required: true, message: '客户名称必填', trigger: 'blur'}
        ],
        email: [
          {required: true, message: '客户邮箱必填', trigger: 'blur'}
        ],
        phone: [
          {required: true, message: '联系电话必填', trigger: 'blur'}
        ],
        sale: [
          {required: true, message: '销售联系人必填', trigger: 'blur'}
        ],
        salePhone: [],
        saleEmail: [],
        sampleCount: [], // 样本数量
        sendDate: [
          {required: true, message: '送检日期必选', trigger: 'blur'}
        ],
        address: [
          {required: true, message: '送检人地址必填', trigger: 'blur'}
        ],
        area: [
          {required: true, message: '送检实验室必填', trigger: 'change'}
        ],
        courierCompany: [
          {required: true, message: '请选择快递公司', trigger: 'change'}
        ],
        otherCourierCompany: [
          {required: true, message: '请输入其他快递方式', trigger: 'blur'}
        ],
        trackingNum: [
          {required: true, message: '请输入快递单号', trigger: 'blur'}
        ],
        transportMethod: [
          {required: true, message: '请选择运送方式', trigger: 'change'}
        ],
        otherTransportMethod: [
          {required: true, message: '请输入其他运输方式', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    // 获取生产片区
    getAreaOptions () {
      this.areaLoading = true
      this.$ajax({
        url: '/order/get_productionArea_list',
        method: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.areaOptions = {}
          data.forEach(v => {
            this.$set(this.areaOptions, v.productionAreaCode, v.productionAreaName)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.areaLoading = false
      })
    },
    // 清除部分表单的验证
    clearValidate (field = []) {
      field.forEach(v => {
        if (this.$refs[v]) {
          this.$refs[v].clearValidate()
        }
      })
    },
    // 修改邮箱弹窗确认
    handleEditEmailDialogConfirm (emails) {
      this.projectInfo.email = emails
      this.clearValidate(['email'])
    },
    // 选择项目弹窗确认
    handleProjectDialogConfirm (project) {
      console.log(project)
      let d = project.realData
      this.projectInfo.projectCode = d.projectCode
      this.projectInfo.projectName = d.projectName
      this.projectInfo.unit = d.hospital
      this.projectInfo.customer = d.customer
      this.projectInfo.sale = d.sale
      this.projectInfo.salePhone = d.salePhone
      this.projectInfo.saleEmail = d.saleEmail
      this.clearValidate(['project'])
    },
    handleClose () {
      let msg = this.readOnly ? '确认关闭吗' : '已填写的数据将会清空，是否放弃本次操作？'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        window.close()
      })
    },
    // 校验
    validForm () {
      let msgEmail = '客户邮箱必填'
      let validEmailInfo = new Promise((resolve, reject) => {
        if (!this.projectInfo.email) {
          reject(msgEmail)
        }
        resolve()
      })
      let msg = '存在必填字段未填写，请检查'
      let validProjectInfo = new Promise((resolve, reject) => {
        this.$refs.projectInfoForm.validate(valid => {
          valid ? resolve() : reject(msg)
        })
      })
      let validCourierInfo = new Promise((resolve, reject) => {
        this.$refs.courierInfo.validate(valid => {
          valid ? resolve() : reject(msg)
        })
      })
      return Promise.all([validEmailInfo, validProjectInfo, validCourierInfo])
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-scrollbar__wrap{
  overflow-x: hidden;
}
//.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>/deep/ .el-form-item__label:before,
//.el-form-item.is-required:not(.is-no-asterisk)>/deep/ .el-form-item__label:before{
//  content: none !important;
//}
.el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item {
  margin-bottom: 5px;
}
.page{
  width: 80%;
  min-width:1000px;
  margin: auto;
  padding: 10px 20px;
}
.title{
  margin-right: 30px;
  font-size: 20px;
  font-weight: 600;
}
$form_width: 250px;
.form-width{
  width: $form_width;
}
.el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 250px;
}
// 2个长度，就是form-width的长度加上padding的12px + border的2px
.form-width-long{
  width: 2 * $form_width + 12px + 2px;
}
.p{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.module{
  background: #fff;
  margin: 0;
  .module-title-bar{
    @extend .operateBar;
    height: 25px;
    .min-title{
      @extend .title;
      font-size: 16px;
    }
  }
  .content{
    padding: 0 20px 5px;
  }
}
.bottom {
  margin-bottom: 0;
}
</style>
