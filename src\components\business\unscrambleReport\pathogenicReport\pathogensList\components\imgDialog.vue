<template>
  <div>
    <el-dialog :visible="visible" :close-on-click-modal="false" :close-on-press-escape="false"
               title="Deplot链接图"
               width="700px"
               @close="handleClose"
               @open="handleOpen">
      <el-image :src="img" fit="contain" style="width: 100%; height:200px;"/>
    </el-dialog>
  </div>
</template>

<script>

import mixins from '../../../../../../util/mixins'

export default {
  name: 'wesReportDetectionOfGeneVariationImportDialog',
  mixins: [mixins.dialogBaseInfo],
  props: ['importDialog', 'taxId'],
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      img: ''
    }
  },
  methods: {
    handleOpen () {
      this.getImg()
    },
    async getImg () {
      let {code, data} = await this.$ajax({
        url: '/read/pathogen/get_deplot_png_base64',
        data: {
          taxId: this.taxId,
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.img = 'data:image/png;base64,' + data
      }
    }
  }
}
</script>

<style scoped>

</style>
