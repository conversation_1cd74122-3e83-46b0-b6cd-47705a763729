<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      append-to-body
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        v-if="visible"
        :model="form"
        class="form"
        :rules="rules"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template>
          <div class="email-content">
            <el-form-item label="已选报告">
              共计{{ ids.length }}份
            </el-form-item>
            <el-form-item label="邮件标题" prop="emailTitle">
              <el-input
                v-model.trim="form.emailTitle"
                maxlength="200"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="邮件正文" prop="emailContent">
              <div class="form-width">
                <edit
                  :value="form.emailContent"
                  @input="handleEmailContentInput"/>
              </div>
            </el-form-item>
            <el-form-item label="附件文件">
              <el-upload
                ref="upload"
                :action="uploadUrl"
                :headers="headers"
                :file-list="form.attachFile"
                :on-remove="handleRemove"
                :on-error="handleOnError"
                class="upload-demo"
                multiple>
                <el-button size="mini" icon="el-icon-plus">添加附件</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item label="收件邮箱" prop="inbox">
              <el-input
                v-model.trim="form.inbox"
                :rows="2"
                maxlength="1000"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="抄送邮箱" prop="sendEmail">
              <el-input
                v-model.trim="form.sendEmail"
                maxlength="1000"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
          </div>
        </template>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
import constants from '@/util/constants'
import Cookies from 'js-cookie'
import util from '@/util/util'
import {setEmailContent} from './setEmailInfo'

export default {
  name: `modificationDescriptionDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array
    },
    type: {
      type: Number // 类型 1： 审核成功并发放 2：发送报告
    },
    orderCode: {
      type: String
    },
    reportName: {
      type: String
    }
  },
  data () {
    return {
      title: '报告发放',
      step: 1, // 步骤
      form: {
        emailTitle: '', // 邮件标题
        emailContent: '', // 邮件正文
        attachFile: [], // 附件文件
        inbox: '', // 收件邮箱
        sendEmail: '' // 寄件邮箱
      },
      day: '',
      month: '',
      sampleNum: '',
      projectCode: '',
      projectName: '',
      projectType: '',
      orderType: '', // 订单类型  1: 'illumina文库订单',2: 'MGI文库订单',3: '组织核酸样本订单'
      isAutoDetect: '',
      qualifiedNum: '',
      riskNum: '',
      qualifiedResultNumber: '',
      disqualifieResultNumber: '',
      loading: false,
      headers: {
        token: Cookies.get('token')
      },
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false,
      linkUrl: constants.IFRAME_URL + '/main/abnormalOrder?orderCode=' + this.orderCode,
      rules: {
        emailTitle: [
          {required: true, message: '此项为必填', trigger: 'blur'}
        ],
        emailContent: [
          {required: true, message: '此项为必填', trigger: 'blur'}
        ],
        inbox: [
          {required: true, message: '此项为必填', trigger: 'blur'},
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ],
        sendEmail: [
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(async () => {
        this.$refs.form.resetFields()
        this.form = {
          emailTitle: '', // 邮件标题
          emailContent: '', // 邮件正文
          attachFile: [], // 附件文件
          inbox: '', // 收件邮箱
          sendEmail: '' // 寄件邮箱
        }
        await this.getEmailInfo()
        this.setEmailType()
      })
    },
    // 获取邮件信息
    async getEmailInfo () {
      let res = await this.$ajax({
        url: '/order/report/get_report_file_and_email',
        data: {
          fidList: this.ids
        },
        loadingDom: '.form'
      })
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data || {}
        this.form.inbox = data.femail
        this.form.attachFile = []
        let fileList = data.fileList || []
        fileList = fileList.map(v => JSON.parse(v))
        this.form.attachFile = this.form.attachFile.concat(fileList)
        this.form.sendEmail = data.fccEmail
        this.day = data.farriveSampleDay
        this.month = data.farriveSampleMonth
        this.qcResultNumber = data.fqcResultNumber
        this.qualifiedResultNumber = data.fqualifiedResultNumber
        this.disqualifieResultNumber = data.fdisqualifieResultNumber
        this.projectCode = data.fprojectCode
        this.isAutoDetect = data.fisAutoDetect
        this.qualifiedNum = data.fqualifiedNum
        this.sampleName = data.fsampleName
        this.riskNum = data.friskNum
        this.projectType = data.fprojectType
        this.fexperimentType = data.fexperimentType
        this.projectName = data.fprojectName
        this.sampleNum = data.fsampleNum
        this.orderType = data.forderType
      } else {
        this.$message.error(res.message)
      }
    },
    // 设置邮件类型
    setEmailType () {
      if (+this.orderType === 3) {
        this.setEmailInfo()
        return
      }
      if (+this.orderType === 5) {
        this.setSingleCellEmailInfo()
        return
      }
      this.setOtherEmailInfo()
    },
    setParams () {
      const emailConfig = {
        emailType: this.orderType !== 3 ? '1' : '0',
        autoFlow: this.fexperimentType
      }
      const emailContentInfo = {
        projectName: this.projectName,
        month: this.month,
        day: this.day,
        sampleNum: this.sampleNum,
        normalCase: this.qualifiedNum,
        orderCode: this.orderCode,
        riskCase: this.riskNum,
        projectType: this.projectType
      }
      return {
        emailConfig,
        emailContentInfo
      }
    },
    setOtherEmailInfo () {
      // 去除pdf附件
      this.form.attachFile = this.form.attachFile.filter(v => v.path.includes('pdf'))
      // 判断是否是非极致项目
      let title = `${this.projectCode} - ${this.projectName}`
      title = `【库检报告】` + '-' + title
      this.form.emailTitle = title + '-' + this.orderCode + '-' + util.dateFormatter(new Date(), false, null, '')
      const {
        emailConfig,
        emailContentInfo
      } = this.setParams()
      this.form.emailContent = setEmailContent(emailConfig, emailContentInfo)
    },
    setSingleCellEmailInfo () {
      // 只保留pdf附件
      this.form.attachFile = this.form.attachFile.filter(v => !v.path.includes('pdf'))
      let title = `${this.projectCode} - ${this.projectName}`
      const isRnaQc = this.reportName === '单细胞项目RNA质控报告'
      this.form.emailTitle = `【${isRnaQc ? '单细胞项目RNA质控报告' : '解离报告'}】` + '-' + title + '-' + this.orderCode + '-' +
        util.dateFormatter(new Date(), false, null, '')
      this.form.emailContent = setEmailContent({isSingle: 1, isRnaQc: isRnaQc}, {
        projectCode: this.projectCode, projectName: this.projectName, sampleName: this.sampleName, orderCode: this.orderCode

      })
    },
    // 设置邮件信息
    setEmailInfo () {
      let title = `${this.projectCode} - ${this.projectName}`
      const reportName = this.reportName.includes('文库报告') ? '库检报告' : '检测报告'
      this.ids.length > 1 ? title = '样本质控报告-' + title : title = `【${reportName}】` + '-' + title
      this.form.emailTitle = title + '-' + this.orderCode + '-' +
        util.dateFormatter(new Date(), false, null, '')
      const {emailConfig, emailContentInfo} = this.setParams()
      this.form.emailContent = setEmailContent(emailConfig, emailContentInfo)
    },
    handleEmailContentInput (val) {
      this.form.emailContent = val
    },
    async handleConfirmOption (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    async handleClose () {
      await this.handleConfirmOption('是否放弃操作？“取消”后邮件数据将不会被系统保存')
      this.visible = false
      this.$emit('dialogCloseEvent')
    },
    setAttachFile () {
      let files = []
      let uploadFiles = this.$refs.upload.uploadFiles || []
      uploadFiles.forEach(v => {
        if (v.response) {
          let data = v.response.data
          data.name = data.originalFileName
          files.push(data)
        } else {
          files.push(v)
        }
      })
      return files
    },
    async handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          let files = this.setAttachFile() || []
          if (files.length === 0) {
            this.$message.error('缺少附件，请上传')
            return
          }
          await this.handleConfirmOption('是否确认发放报告？')
          this.loading = true
          this.$ajax({
            url: '/order/report/save_qc_report_status',
            data: {
              fidList: this.ids,
              fstatus: 10,
              fsubject: this.form.emailTitle,
              fcontent: this.form.emailContent.replace(/\n/g, '<br/>'),
              femail: this.form.inbox,
              fccEmail: this.form.sendEmail,
              files: files
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              let message = '发放成功'
              this.$message.success(message)
              this.$emit('dialogConfirmEvent', this.form)
              this.visible = false
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件不能大于10M，请重新上传')
        return false
      }
      return true
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    handleRemove () {
      this.onProgress = false
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.form-width {
  width: 600px;
}

.email-content {
  padding-top: 15px;
  height: 50vh;
  overflow: auto;
  border: 1px solid #efefef;
}

.img {
  width: 150px;
  height: 150px;
  margin: 5px;
}
</style>
