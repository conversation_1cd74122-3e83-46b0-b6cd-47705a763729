<template>
  <el-dialog
    title="修改状态"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="500px"
    @opened="handleOpen">
    <div>
      <div class="tips">
        <i class="el-icon-warning-outline status"></i>
        请对已选择的{{ids.length}}条样本数据的交付状态进行修改：
      </div>
      <el-form ref="form" :model="form" label-suffix=":">
        <el-form-item label="交付状态" prop="status">
          <el-select v-model="form.status" size="mini" clearable>
            <template  v-for="(value, key) in statusOptions">
              <el-option
                v-if="+key !== deliverStatus"
                :key="key"
                :label="value"
                :value="key"
              />
            </template>

          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {awaitWrap} from '@/util/util'
import {fixSampleMonitoringStatus} from '@/api/dataMonitoringManagement/smapleMonitoringManagementApi'
import {deliverStatusConfig} from '../dataFormate'

/**
 * 修改样本监测状态对话框组件
 * 使用el-dialog组件实现一个可复用的对话框，用于修改已选择样本数据的交付状态。
 *
 * @mixins mixins.dialogBaseInfo - 引入基础对话框信息混合体
 * @prop {Array} ids - 样本数据的ID数组，用于指定要修改状态的样本数据
 */
export default {
  name: 'fixStatusDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    deliverStatus: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false, // 控制提交按钮的加载状态
      statusOptions: deliverStatusConfig,
      form: {
        status: '' // 用于存储选中的交付状态
      }
    }
  },
  methods: {
    /**
     * 对话框打开时获取表格数据
     * 目前未在代码中实现具体功能，可能用于加载下拉列表选项等
     */
    handleOpen () {
      this.$refs.form.resetFields()
    },
    /**
     * 处理状态修改确认操作
     * 提交表单数据并根据返回结果提示用户操作是否成功
     */
    async handleConfirm () {
      // 确认判断
      await this.$confirm(`数据修改后暂停自动更新，是否确认对选中的${this.ids.length}条数据的交付状态进行修改？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.loading = true // 开始加载
      const { res } = await awaitWrap(fixSampleMonitoringStatus({
        fidList: this.ids,
        fdeliverStatus: +this.form.status
      })) // 调用API修改样本监测状态
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('修改成功') // 提示修改成功
        this.$emit('dialogConfirmEvent') // 触发父组件数据刷新
        this.visible = false // 关闭对话框
      }
      this.loading = false // 结束加载
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  display: flex;
  align-items: center;
  .status {
    color: #FEC171;
    font-size: 20px;
    margin-right: 5px;
  }
}

</style>
