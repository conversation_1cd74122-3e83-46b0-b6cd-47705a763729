import { getSessionInfo, setSessionInfo, removeSessionInfo } from 'Util'
import constants from 'Constants'

const index = getSessionInfo('activeIndex') || ''
const pathTabs = getSessionInfo('pathTabs') || []

const state = {
  activeIndex: index,
  pathTabs: pathTabs
}

const mutations = {
  // tabs: {path, title}
  ADD_PATH_TABS (state, tabs) {
    const hasThisPath = state.pathTabs.some(v => v.path === tabs.path)
    if (!hasThisPath) {
      state.pathTabs.push(tabs)
      setSessionInfo('pathTabs', state.pathTabs)
    }
  },
  REMOVE_TABS (state, path) {
    let index = -1
    const l = state.pathTabs.length
    for (let i = 0; i < l; i++) {
      const v = state.pathTabs[i]
      if (v.path === path) {
        index = i
        break
      }
    }
    if (index > -1) {
      state.pathTabs.splice(index, 1)
      setSessionInfo('pathTabs', state.pathTabs)
    }
  },
  REMOVE_ALL_TABS (state) { // 清空所有tabs
    state.pathTabs = []
    removeSessionInfo('pathTabs')
  },
  CHANGE_ACTIVE_INDEX (state, index) { // 改变index
    state.activeIndex = index
    setSessionInfo('activeIndex', index)
  },
  CLEAR_ACTIVE_INDEX (state) {
    state.activeIndex = constants.DEFAULT_ACTIVE_PATH
    removeSessionInfo('activeIndex')
  }
}

const actions = {
  addTabs ({ commit, state }, tabs) {
    return new Promise(resolve => {
      commit('ADD_PATH_TABS', tabs)
      commit('CHANGE_ACTIVE_INDEX', tabs.path)
      resolve({
        tabs: state.pathTabs,
        activeIndex: state.activeIndex
      })
    })
  },
  delPath ({ commit, state }, path) {
    return new Promise(resolve => {
      if (path) {
        commit('REMOVE_TABS', path)
        if (path === state.activeIndex) {
          commit('CHANGE_ACTIVE_INDEX', state.pathTabs.length > 0 ? state.pathTabs[0].path : constants.DEFAULT_ACTIVE_PATH)
        }
      } else {
        commit('REMOVE_ALL_TABS')
        commit('CLEAR_ACTIVE_INDEX')
      }
      resolve({
        tabs: state.pathTabs,
        activeIndex: state.activeIndex
      })
    })
  }
}

export default {
  state,
  mutations,
  actions
}
