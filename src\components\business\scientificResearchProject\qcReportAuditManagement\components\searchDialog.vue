<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :show-close="false"
    >

      <template #title>
        <div class="search-custom-title-nav">
          <p class="title">高级查询</p>
          <div>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleSearch">确认</el-button>
          </div>
        </div>
      </template>

      <div class="box">
        <div>
          <el-form
            ref="form"
            :model="form"
            label-width="100px"
            label-suffix=":"
            size="mini"
            inline
            @keyup.enter.native="handleSearch">
            <el-form-item label="项目编号">
              <el-input
                v-model.trim="form.fprojectCodeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="订单编号">
              <el-input
                v-model.trim="form.forderCodeList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="审核人">
              <el-input
                v-model.trim="form.fauditorList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="操作人">
              <el-input
                v-model.trim="form.fcreatorList"
                :rows="5"
                :placeholder="placeholder"
                type="textarea"
                size="mini"
                clearable
                class="input-width"
                autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="报告类型">
              <el-select
                v-model="form.ftypeList"
                filterable
                clearable
                collapse-tags
                multiple
                size="mini"
                style="width: 350px">
                <el-option
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  v-for="(item, index) in templates"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="生成时间">
              <el-date-picker
                v-model="form.fcreateTime"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                size="mini"
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="审核时间">
              <el-date-picker
                v-model="form.fauditTime"
                type="datetimerange"
                clearable
                size="mini"
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="报告状态">
              <el-checkbox-group v-model.trim="form.freportStatusList">
                <el-checkbox
                  :key="index"
                  :label="item.value"
                  v-for="(item, index) in statusList">{{item.label}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="数据范围">
              <el-radio
                v-model="form.fdataRange" :key="index"
                :label="item.value"
                v-for="(item, index) in rangeList"
                border
              >
                {{item.label}}
              </el-radio>
            </el-form-item>
          </el-form>
        </div>
      </div>

    </el-drawer>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../util/mixins'
import util from '@/util/util'
export default {
  name: `searchParamsPath`,
  mixins: [mixins.dialogBaseInfo],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      form: {
        fprojectCodeList: '',
        forderCodeList: '',
        fcreatorList: '',
        fauditorList: '',
        ftypeList: [],
        fcreateTime: [],
        fauditTime: [],
        freportStatusList: [20, 31],
        fdataRange: 1
      },
      templates: [
        {
          label: 'cfDNA 质控报告',
          value: 1
        },
        {
          label: 'GMseq cfDNA质控报告',
          value: 2
        },
        {
          label: 'GMseq FFPE DNA质控报告',
          value: 3
        },
        {
          label: 'GMseq 组织和细胞gDNA质控报告',
          value: 4
        },
        {
          label: 'FFPE DNA质控报告',
          value: 5
        },
        {
          label: '基因组DNA 质控报告（PCR-free 或 2项目）',
          value: 6
        },
        {
          label: '基因组DNA 质控报告',
          value: 7
        },
        {
          label: 'PCR产物 质控报告',
          value: 8
        },
        {
          label: 'lncRNA质控报告',
          value: 9
        },
        {
          label: 'mRNA质控报告',
          value: 10
        },
        {
          label: '宏转录组（粪便）样本RNA质控报告',
          value: 11
        },
        {
          label: '原核生物RNA质控报告',
          value: 12
        },
        {
          label: '文库报告',
          value: 13
        },
        {
          label: 'GMseq（低起始量）gDNA质控报告',
          value: 14
        },
        {
          label: 'lncRNA质控报告-新鲜组织细胞RNA',
          value: 15
        },
        //  17: '细胞质控报告',
        {
          label: '细胞质控报告',
          value: 17
        },
        //   18: '细胞核质控报告',
        {
          label: '细胞核质控报告',
          value: 18
        },
        // 19: '单细胞项目RNA质控报告'
        {
          label: '单细胞项目RNA质控报告',
          value: 19
        },
        {
          label: 'Gene+-SOP-SEQ-022-17 BS甲基化 cfDNA质控报告',
          value: 20
        },
        {
          label: 'Gene+-SOP-SEQ-022-18 GM甲基化 cfDNA质控报告',
          value: 21
        },
        {
          label: 'Gene+-SOP-SEQ-022-19 GM-panel甲基化 cfDNA质控报告8分钟前俊诚13:38',
          value: 22
        }
      ],
      statusList: [
        {
          label: '待审核',
          value: 31
        },
        {
          label: '审核通过',
          value: 1
        },
        {
          label: '已发送',
          value: 10
        },
        {
          label: '被驳回',
          value: 20
        }
      ],
      rangeList: [
        {
          label: '我审核的报告',
          value: 0
        },
        {
          label: '全部报告',
          value: 1
        }
      ],
      placeholder: '请输入, 批量查询用逗号分隔，不区分中英文逗号， 或直接粘粘Excel中整行或整列数据'
    }
  },
  methods: {
    setParams () {
      let data = util.deepCopy(this.form)
      let keys = ['forderNumList', 'forderCodeList', 'fcreatorList', 'fauditorList', 'fprojectCodeList']
      keys.forEach(v => {
        data[v] = this.formateData(this.form[v])
      })
      return data
    },
    formateData (data) {
      return data ? data.replace(/，/g, ',').replace(/\n/g, ',').replace(/\s+/g, ',').split(',') : []
    },
    // 重置
    handleReset () {
      this.form = {
        fprojectCodeList: '',
        forderCodeList: '',
        fcreatorList: '',
        fauditorList: '',
        ftypeList: [],
        fcreateTime: [],
        fauditTime: [],
        freportStatusList: [20, 31],
        fdataRange: 1
      }
      this.handleSearch()
    },
    handleSearch () {
      this.$emit('dialogConfirmEvent', this.setParams())
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
.search-custom-title-nav {
  display: flex;
  justify-content: space-between;
  .title {
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    border-left: 4px solid #539fff;
  }
}
.search-main-wrap {
  margin: 0 30px;
}
.sub-title {
  overflow: scroll;
  font-size: 16px;
  margin-bottom: 10px;
  color: #539fff;
}
.el-tag {
  margin-left: 10px;
  margin-bottom: 5px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  margin-bottom: 5px;
  vertical-align: bottom;
}
.input-width{
  width: 350px;
}
.el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item {
  margin-bottom: 12px;
}
>>>.el-drawer__body{
  overflow: scroll;
}
>>>.el-form-item__content{
  width: calc(100% - 100px);
}
//>>>.el-select {
//  width: 400px !important;
//}
</style>
