// cover some element-ui styles
.el-dialog.is-fullscreen {
  width: 100% !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  height: 100% !important;
  overflow: auto !important;
}
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

//.el-table {
//  display: flex;
//  flex-direction: column;
//}
//.el-table__header-wrapper {
//  overflow: visible !important;
//}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}
.el-pagination{
  padding: 0 5px;
  //margin-top: 12px;
}
.el-button {
  //display: flex;
  min-width: 60px;
  span {
    display: inline-block;
    text-align-last: justify;
  }
}
.el-button--text {
  min-width: 0;
}

.el-upload__input {
  display: none;
}
.el-upload__tip{
  padding-left: 10px;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.mini-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.el-tooltip__popper{
  max-width: 500px;
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 10vh auto 0 !important;
}
.el-dialog .is-fullscreen {
  margin: 0 !important;
}
.el-badge {
  margin-left: 5px;
}

//弹窗
.el-dialog__header,
.el-drawer__header{
  background: #eff2fb;
  padding: 12px 20px;
  .el-dialog__title{
    color: #444;
    font-size: 16px;
    font-weight: 600;
  }
}
.el-dialog__body {
  padding: 12px 20px !important;
}
.el-dialog__footer{
  border-top: 1px solid #EBEEF5;
  padding: 12px 20px;
  .el-button + .el-button {
    margin-left: 20px;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-input__inner{
  padding: 0 8px;
}
.el-form-item__label{
  padding: 0 8px 0 0 !important;
}
.el-button--mini{
  padding: 7px 8px;
}
.el-table td,
.el-table th{
  padding: 3px 0!important;
}
.el-table th{
  background: #f2f2f2!important;
  font-weight: 600;
  color: #333;
}
.el-table__cell .el-button {
  padding: 0 0;
}
.el-drawer__body{
  padding: 12px !important;
  .el-scrollbar__wrap{
    overflow: auto !important;
  }
}
.el-drawer__header{
  padding: 0 10px;
  margin-bottom: 0 !important;
}

// 修改vxe-table 中下拉选项的层级，
// 防止被其他元素遮挡
.vxe-select--panel {
  z-index: 99999!important;
}
.vxe-table--tooltip-wrapper {
  z-index: 10000 !important;
}

.el-table .height-light-row {
  background: #eee;
}

.vxe-table--render-default
.vxe-body--column
.col--valid-error
.vxe-cell--valid-error-hint
.vxe-cell--valid-error-msg {
  background: transparent;
}

// 修复el-dropdown的margin
.operate-btns-group{
  .el-dropdown {
    margin: 0 10px 0 0 !important;
  }
  .el-badge {
    margin-left: 0 !important;
  }
}

.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis, .vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis, .vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis, .vxe-table--render-default.vxe-editable.size--mini .vxe-body--column {
  height: 32px !important;
  line-height: 32px !important;
}

.vxe-select--panel {
  z-index: 999999 !important;
}
