<template>
  <div>
    <el-dialog
      :visible.sync="visible" :before-close="handleClose" title="入库清单"
      width="60%" @open="handleOpen">
      <div class="dialogContent">
        <div class="buttonGroup">
          <el-button type="primary" size="mini" @click="handleAddRow">添加数据行</el-button>
        </div>
        <el-form ref="form" :model="form" label-width="80px" label-position="top" size="mini">
          <el-table
            :data="form.tableData"
            size="mini"
            height="400px"
            style="width: 100%">
            <el-table-column prop="code" label="物料编号" min-width="120"></el-table-column>
            <el-table-column label="物料类别" min-width="120">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.category'" :rules="categoryRules" label="">
                  <el-select v-model="scope.row.category" clearable placeholder="请选择" @change="value => handleCategoryChange(value, scope.$index, scope.row.timestamp)">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in categoryList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="品类" min-width="120">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.material'" :rules="materialRules" label="">
                  <el-select v-model="scope.row.material" :disabled="scope.row.category === ''" clearable placeholder="请选择" @change="value => handleMaterialChange(value, scope.$index, scope.row.timestamp)">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in scope.row.materialList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="规格" min-width="120">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.spec'" :rules="specRules" label="">
                  <el-select v-model="scope.row.spec" :disabled="scope.row.material === ''" clearable placeholder="请选择" @change="value => handleSpecChange(value, scope.$index, scope.row)">
                    <el-option
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      v-for="item in scope.row.specList">
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="version" label="版本号" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="unit" label="单位" min-width="80" show-overflow-tooltip></el-table-column>
            <el-table-column label="入库数量" width="120">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.stock'" :rules="stockRules" label="">
                  <el-input v-model.number="scope.row.stock" clearable placeholder="请输入"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.$index)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确认入库</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'warehousingDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {},
  props: {
    ptype: {
      type: Number,
      default: 0,
      required: true
    },
    ptableData: {
      type: Array,
      default: function () {
        return []
      },
      required: false
    }
  },
  mounted () {
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      type: '',
      form: {
        tableData: []
      },
      categoryRules: [
        {required: true, message: '请选择', trigger: ['change', 'blur']}
      ],
      materialRules: [
        {required: true, message: '请选择', trigger: ['change', 'blur']}
      ],
      specRules: [
        {required: true, message: '请选择', trigger: ['change', 'blur']}
      ],
      stockRules: [
        {required: true, message: '请输入', trigger: 'blur'},
        {pattern: /^\d+$/, message: '格式错误', trigger: 'blur'}
      ],
      categoryList: [],
      materialList: []
    }
  },
  methods: {
    getCategoryList () {
      this.$ajax({
        method: 'get',
        url: '/materials/get_category_list',
        data: {
          type: this.type
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.categoryList = []
          result.data.forEach(v => {
            this.categoryList.push({
              label: v.name,
              value: v.categoryId
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getMaterialList () {
      this.$ajax({
        method: 'get',
        url: '/materials/get_materials_by_category',
        data: {
          categoryId: 1
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.materialList = []
          result.data.forEach(v => {
            this.materialList.push({
              label: v.name,
              value: v.name
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          tableData: [],
          attribute: ''
        }
        this.type = this.ptype
        this.$refs.form.resetFields()
        if (this.ptableData.length !== 0) {
          this.ptableData.forEach(v => {
            this.form.tableData.push({
              ...v,
              material: v.name
            })
          })
        }
        this.getCategoryList()
      })
    },
    handleAddRow () {
      this.form.tableData.push({
        code: '',
        category: '',
        material: '',
        materialList: [],
        spec: '',
        specList: [],
        version: '',
        unit: '',
        stock: '',
        timestamp: (new Date()).valueOf()
      })
    },
    handleDelete (index) {
      this.form.tableData.splice(index, 1)
      if (this.form.tableData.length === 0) {
        this.handleAddRow()
      }
    },
    handleCategoryChange (value, index, timestamp) {
      this.$set(this.form.tableData[index], 'material', '')
      this.$set(this.form.tableData[index], 'materialList', [])
      this.$set(this.form.tableData[index], 'spec', '')
      this.$set(this.form.tableData[index], 'specList', [])
      this.$set(this.form.tableData[index], 'code', '')
      this.$set(this.form.tableData[index], 'unit', '')
      this.$set(this.form.tableData[index], 'version', '')
      if (value) {
        this.$ajax({
          url: '/materials/get_materials_by_category',
          method: 'get',
          data: {
            categoryId: value
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let dataIndex = this.form.tableData.findIndex(v => v.timestamp === timestamp)
            if (dataIndex !== -1) {
              if (result.data) {
                let item = {}
                let list = []
                result.data.forEach(v => {
                  item = {
                    label: v.name,
                    value: v.name
                  }
                  list.push(item)
                })
                this.$set(this.form.tableData[dataIndex], 'materialList', list)
              }
            }
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleMaterialChange (value, index, timestamp) {
      this.$set(this.form.tableData[index], 'spec', '')
      this.$set(this.form.tableData[index], 'specList', [])
      this.$set(this.form.tableData[index], 'code', '')
      this.$set(this.form.tableData[index], 'unit', '')
      this.$set(this.form.tableData[index], 'version', '')
      if (value) {
        this.$ajax({
          url: '/materials/get_spec_by_materials_name',
          method: 'get',
          data: {
            materialsName: value
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let dataIndex = this.form.tableData.findIndex(v => v.timestamp === timestamp)
            if (dataIndex !== -1) {
              if (result.data) {
                let item = {}
                let list = []
                result.data.forEach(v => {
                  item = {
                    label: v.spec,
                    value: v.spec,
                    unit: v.unit
                  }
                  list.push(item)
                })
                this.$set(this.form.tableData[dataIndex], 'specList', list)
              }
            }
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleSpecChange (value, index, row) {
      this.$set(this.form.tableData[index], 'code', '')
      this.$set(this.form.tableData[index], 'unit', '')
      this.$set(this.form.tableData[index], 'version', '')
      if (value) {
        this.$ajax({
          method: 'get',
          url: '/materials/get_materials_by_name_and_spec',
          data: {
            name: row.material,
            spec: value
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let dataIndex = this.form.tableData.findIndex(v => v.timestamp === row.timestamp)
            if (dataIndex !== -1) {
              if (result.data) {
                this.$set(this.form.tableData[index], 'code', result.data.code)
                this.$set(this.form.tableData[index], 'unit', result.data.unit)
                this.$set(this.form.tableData[index], 'version', result.data.version)
              }
            }
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let list = []
          let item = {}
          this.form.tableData.forEach(v => {
            item = {
              code: v.code,
              stock: v.stock
            }
            list.push(item)
          })
          this.loading = true
          this.$ajax({
            url: '/materials/save_in_stock',
            data: {
              storageList: list,
              type: this.type
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('入库成功')
              this.$emit('warehousingSaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .dialogContent{
    >>>.el-table{
      .el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item{
        margin-bottom: 14px;
        margin-top: 14px;
      }
    }
    .buttonGroup{
      text-align: right;
      height: 40px;
      line-height: 40px;
    }
  }
  >>>.el-dialog__body{
    padding: 10px 20px;
  }
</style>
