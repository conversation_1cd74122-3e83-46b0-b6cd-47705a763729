<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :before-close="handleClose"
      width="50%"
      @open="handleDialogOpen">
      <div>
        <el-table
          :data="tableData" class="configurationTable"
          size="mini"
          height="300px"
          style="width: 100%">
          <el-table-column prop="productName" label="产品/项目名称" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button v-if="$setAuthority('002005004', 'buttons')" type="text" size="mini" @click="handleDelete(scope.row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'processFlowManagementProjectConfigurationDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.title = this.pdata.name + '配置详情'
        this.procedureTemplateId = this.pdata.id
        // this.getData()
      } else {
        this.title = ''
        this.procedureTemplateId = null
        this.tableData = []
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      title: '',
      procedureTemplateId: null,
      tableData: []
    }
  },
  methods: {
    handleDialogOpen () {
      if (this.procedureTemplateId) {
        this.getData()
      }
    },
    getData () {
      this.$ajax({
        loadingDom: '.configurationTable',
        method: 'get',
        url: '/system/procedure/list_product_project',
        data: {
          fid: this.procedureTemplateId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.tableData = []
          result.data.forEach(v => {
            this.tableData.push({
              productId: v.productId,
              productName: v.productName,
              type: v.type
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleClose () {
      this.$emit('processFlowManagementProjectConfigurationDialogCloseEvent')
    },
    handleDelete (row) {
      this.$confirm(`是否移除该产品/项目?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          loadingDom: '.configurationTable',
          url: '/system/procedure/remove_product_project',
          data: {
            fid: this.procedureTemplateId,
            productId: row.productId,
            type: row.type
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('移除成功')
            let index = this.tableData.findIndex(v => v.productId === row.productId)
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(result.message)
          }
        })
      }).catch(err => {
        console.log(err)
      })
    }
  }
}
</script>

<style scoped>
</style>
