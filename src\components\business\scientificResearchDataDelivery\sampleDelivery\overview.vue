<template>
  <div style="height: 100%;">
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="100px" size="mini" @keyup.enter.native="handleSearch">
        <el-form-item label="子文库编号">
          <el-input v-model.trim="form.subLibNum" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="子订单编号">
          <el-input v-model.trim="form.subOrderCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="交付状态">
          <el-select v-model="form.deliveryStatus" multiple collapse-tags clearable placeholder="请选择">
            <el-option
              :key="k"
              :label="v.text"
              :value="k"
              v-for="(v, k) in deliveryStatusOptions">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button type="primary" plain size="mini" @click="handleReset">重置</el-button>
      </div>
      <!--表格-->
      <div>
        <el-table
          ref="table"
          :data="tableData"
          :cell-style="handleRowStyle"
          class="table"
          size="mini"
          border
          style="width: 100%"
          height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
        >
          <el-table-column prop="subLibNum" label="子文库编号" min-width="260" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleDataSize" label="数据量" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="子订单编号" width="160" show-overflow-tooltip>
            <template slot-scope="scope">
                <span
                  class="link"
                  @click="handleCheck(scope.row, 2)">
                  {{scope.row.subOrderCode}}
                </span>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" label="项目名称" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="projectCode" label="项目编号" min-width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="oldSubLibName" label="原始子文库名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="oldSampleName" label="原始名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dnaNum" label="DNA编号" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="qcFinishTime" label="下机拆分完成时间" min-width="160" show-overflow-tooltip></el-table-column>
          <el-table-column label="任务状态" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <span :class="scope.row.deliveryStatusClass">{{scope.row.deliveryStatusText}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="failReason" label="备注" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="q20" label="Q20/下机" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="q30" label="Q30/下机" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dataSize" label="Base（G）/下机" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deliveryTime" label="交付时间" min-width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskName" label="任务路径" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="noDeliveryReason" label="未自动交付原因" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sequenceTime" label="上机时间" min-width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="finishTime" label="下机时间" min-width="160" show-overflow-tooltip></el-table-column>
        </el-table>
        <div style="display: flex; align-items: center;font-size: 13px;">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh">
              <icon-svg icon-class="icon-refresh"/>
            </button>
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
export default {
  mixins: [mixins.tablePaginationCommonData],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        subLibNum: '',
        subOrderCode: '',
        projectName: '',
        deliveryStatus: []
      },
      submitForm: {},
      deliveryStatusOptions: {
        0: {
          text: '未投递',
          class: ''
        },
        1: {
          text: '交付中',
          class: 'pending-color'
        },
        4: {
          text: '分析任务提交失败',
          class: 'fail-color'
        },
        5: {
          text: '分析任务运行成功',
          class: 'success-color'
        },
        6: {
          text: '分析任务运行失败',
          class: 'fail-color'
        },
        7: {
          text: '云上传成功',
          class: 'success-color'
        },
        8: {
          text: '云上传失败',
          class: 'fail-color'
        },
        9: {
          text: '投递失败',
          class: 'fail-color'
        }
      },
      taskStatusOptions: {
        10: {
          text: '已完成',
          class: ''
        },
        2: {
          text: '失败',
          class: 'fail-generate'
        },
        30: {
          text: '处理中',
          class: 'generate'
        }
      },
      tableData: []
    }
  },
  methods: {
    // 查看 type 1编辑 2 只读
    handleCheck (row, type) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: type, // type 1编辑 2 只读
          orderId: row.id,
          status: row.orderStatus,
          code: row.subOrderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (path) util.openNewPage(path)
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row, rowIndex}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff !important', padding: 0, height: '24px'}
      }
      return {padding: 0, height: '24px'}
    },
    // 查询
    handleSearch () {
      this.submitForm = util.deepCopy(this.form)
      this.currentPage = 1
      this.getData()
    },
    // 重置
    handleReset () {
      this.form = {
        subLibNum: '',
        subOrderCode: '',
        projectName: '',
        deliveryStatus: []
      }
      this.handleSearch()
    },
    // 获取表格数据
    getData () {
      this.$ajax({
        url: '/order/delivery/get_sample_delivery',
        data: {
          productionAreas: util.getSessionInfo('currentLab') || [], // 片区id
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          ...util.deepCopy(this.submitForm)
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.rows || []
          data.forEach((v) => {
            let deliveryStatus = this.deliveryStatusOptions[v.deliverStatus] || {
              text: '',
              class: ''
            }
            let item = {
              deliverStatus: v.deliverStatus,
              deliveryStatusClass: deliveryStatus.class,
              deliveryStatusText: deliveryStatus.text,
              id: v.id,
              subLibNum: v.subLibNum,
              subOrderCode: v.subOrderCode,
              projectName: v.projectName,
              projectCode: v.projectCode,
              oldSubLibName: v.oldSubLibName,
              oldSampleName: v.oldSampleName,
              dnaNum: v.dnaNum,
              q20: v.fq20,
              q30: v.fq30,
              dataSize: v.dataSize,
              deliveryTime: v.deliveryTime,
              taskName: v.taskName,
              noDeliveryReason: v.noDeliveryReason,
              sequenceTime: v.sequenceTime,
              finishTime: v.finishTime,
              orderType: v.orderType,
              orderStatus: v.orderStatus,
              qcFinishTime: v.qcFinishTime,
              failReason: v.failReason,
              sampleDataSize: v.sampleDataSize
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .search{
    height: 48px;
  }
  .search >>>.el-form-item--mini{
    margin-bottom: 10px;
    margin-top: 10px;
  }
  .pending-color {
    color: $color
  }
  .success-color {
    color: $success-color
  }
  .fail-color {
    color: $fail-color
  }
  .content{
    height: calc(100% - 48px);
    .buttonGroup{
      height: 40px;
      line-height: 40px;
    }
    .table{
      height: calc(100% - 50px);
    }
    .table >>>.el-table__header .el-checkbox {
      display: none;
    }
  }
</style>
