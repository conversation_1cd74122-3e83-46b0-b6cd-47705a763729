<template>
  <div class="infoContent">
    <div class="left">
      <!--  样本基本信息-->
      <sample-basic-info
        :ref="sampleBasicInfo"
        :sample-basic-info="sampleBasicInfo"
        :is-detail="true"
        :is-disabled="isDisabled"
        style="margin-bottom: 50px"
        />
      <!--  样本临床信息-->
      <h4 class="model-title">临床信息</h4>
      <!--  临床信息-->
      <sample-clinic-info
        ref="sampleClinicInfo"
        :pathogen-clinical="pathogenClinical"
        :is-detail="true"
        :is-disabled="isDisabled"
        />
    </div>
    <div class="right">
      <sample-img-info
        :sample-basic-id="sampleBasicId"
        :img-src="imgSrc"
        :img-type="imgType"
        :img-names="imgNames"
        :sample-order-img="sampleOrderImg"
        :is-disabled="isDisabled">
      </sample-img-info>
    </div>
  </div>
</template>

<script>
import sampleBasicInfo from './sampleBasicInfo' // 基本信息
import sampleClinicInfo from './sampleClinicInfo' // 临床信息
import sampleImgInfo from './sampleImgInfo'
import util from '../../../util/util' // 图片信息

export default {
  name: 'pathogenSampleInfo',
  components: {
    sampleBasicInfo,
    sampleClinicInfo,
    sampleImgInfo
  },
  mounted () {
    this.getBasicInfoData()
  },
  computed: {
    clinicalInfo: function () {
      return this.$store.getters.getValue('clinicalInfo')
    }
  },
  data () {
    return {
      sampleBasicId: 0,
      sampleNum: '',
      title: '',
      saveLoading: false,
      isDisabled: true, // 是否禁用：查看详情页禁用 补录，存疑不禁用
      sampleBasicInfo: {}, // 样本基本信息
      pathogenClinical: {},
      imgType: 'sampleOrderImg',
      imgSrc: '',
      imgNames: [], // 临床资料
      sampleOrderImg: [] // 送检单资源
    }
  },
  methods: {
    // 获取信息补录基本字段
    getBasicInfoData () {
      this.sampleBasicId = this.clinicalInfo.sampleBasicId
      this.sampleNum = this.clinicalInfo.sampleNum
      this.$ajax({
        loadingDom: '.infoContent',
        url: '/sample/basic/get_clinical_info',
        data: {
          sampleBasicId: this.sampleBasicId,
          sampleNum: this.sampleNum
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let pathogenClinical = data.pathogenyClinical || {}
          if (data && JSON.stringify(data) !== '{}') {
            try {
              this.sampleBasicInfo = {
                sampleCode: this.sampleNum,
                productName: data.productNames || data.projectName,
                sampleType: data.basic.sampleType,
                sampleSize: data.basic.sampleCount,
                collectDate: data.basic.sampleCollectDate && data.basic.sampleCollectDate.slice(0, 10),
                inspectDate: data.inspection.inspectionTime && data.inspection.inspectionTime.slice(0, 10),
                sampleQuality: data.basic.sampleQualify,
                receiverDate: data.basic.sampleConfirmTime && data.basic.sampleConfirmTime.slice(0, 10),
                name: data.basic.name,
                age: data.basic.fage,
                sex: data.basic.sex,
                identityNumber: data.basic.idcard,
                cardType: data.basic.cardType,
                birthday: data.basic.birthday && data.basic.birthday.slice(0, 10),
                admissionNum: data.basic.fadmissionNum, // 住院号
                medicalRecordNumber: data.inspection.fmedicalRecordNumber,
                specimenNo: data.inspection.fspecimenNo,
                bedNum: data.basic.fbedNum, // 床号
                phone: data.basic.contactTel, // 联系电话
                inspectionDepart: data.inspection.inspectionUnitName, // 送检单位
                inspectionUnitId: data.inspection.inspectionUnitId,
                insertOffice: data.inspection.departments, // 送检科室
                insertDoctor: data.inspection.doctor, // 送检医生
                doctorCode: data.inspection.doctorCode
              } // 基础信息
              this.$set(this.sampleBasicInfo, 'realData', JSON.parse(JSON.stringify(this.sampleBasicInfo)))
              util.setDefaultEmptyValueForObject(this.sampleBasicInfo)
              this.pathogenClinical = {
                fid: pathogenClinical.fid,
                clinicDisease: pathogenClinical.fclinicalDiagnose, // 临床疾病
                symptomDesc: pathogenClinical.finfectionSymptom, // 感染症状描述
                WBC: pathogenClinical.fwhiteBloodCellCount, // 白细胞计数
                LYM: pathogenClinical.flymphocyteCount, // 淋巴细胞
                N: pathogenClinical.fneutrophilRatio, // 中粒性细胞
                CRP: pathogenClinical.fcReactiveProtein, // c反应蛋白
                PCT: pathogenClinical.fprocalcitonin, // 降钙素原
                cultivateResult: pathogenClinical.fisCulture || 0, // 培养结果
                mirrorResult: pathogenClinical.fisMicroscopic || 0, // 镜像结果
                GTest: pathogenClinical.fisGlucanDetection || 0, // 1,3-β-D-葡聚糖检测（G实验）
                GMTest: pathogenClinical.fisGalactomannanDetection || 0, // 半乳糖甘露醇聚糖抗原检测（GM实验）
                PCR: pathogenClinical.fisPcr || 0, // PCR
                antigen: pathogenClinical.fisAntibody || 0, // 抗原/抗体
                otherResult: pathogenClinical.fisOthersResult || 0, // 其他检测结果
                cultivateResultNote: pathogenClinical.fcultureResults,
                mirrorResultNote: pathogenClinical.fmicroscopicResults,
                GTestNote: pathogenClinical.fglucanDetection,
                GMTestNote: pathogenClinical.fgalactomannanDetection,
                PCRNote: pathogenClinical.fpcr,
                antigenNote: pathogenClinical.fantibody,
                otherResultNote: pathogenClinical.fothersResult,
                focusPathogens: pathogenClinical.ffocusPathogens, // 重点关注病原
                medicalInfo: pathogenClinical.fantiInfectiveMedication, // 抗感染用药信息
                startCue: pathogenClinical.ftreatmentStartTime && pathogenClinical.ftreatmentStartTime.slice(0, 10), // 开始治疗时间
                lastMedicalInfo: pathogenClinical.flastDrugsTime && pathogenClinical.flastDrugsTime.slice(0, 10) // 送检前最近一次用抗感染药时间
              }
              this.$set(this.pathogenClinical, 'realData', JSON.parse(JSON.stringify(this.pathogenClinical)))
              util.setDefaultEmptyValueForObject(this.pathogenClinical)
              this.sampleClinicalId = data.clinical.sampleClinicalId
              this.imgNames = data.imgNames || []
              this.sampleOrderImg = data.inspection.fileInfo || []
              this.imgSrc = this.sampleOrderImg.length === 0 ? '' : this.sampleOrderImg[0].fileAbsolutePath
              this.imgType = 'sampleOrderImg'
            } catch (e) {
              console.log(e)
            }
          }
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.model-title {
  border-left: 3px solid deepskyblue;
  padding: 0 10px;
  margin: 10px 0;
}
.infoContent{
  padding: 20px;
  height: calc(100vh - 64px - 20px - 20px);
  display: flex;
.left{
  padding: 0 5px;
  width: 55%;
  overflow-y: auto;
}
.right{
  border-left: 1px solid #DCDFE6;
  padding: 0 5px;
  flex: 1;
  overflow-y: auto;
  height: 100%;
.picture{
  margin: 10px auto;
  width: 90%;
  height: calc(100% - 54px - 28px - 20px - 40px);
.image{
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: auto;
}
}
.title{
  height: 30px;
  line-height: 30px;
  font-size: 13px;
}
}
}
/*去除输入框圆角*/
/deep/ .el-input__inner {
  border-radius: 0!important;
}
/*设置相对定位作为错误提示的父级盒子&&解决输入框因为行高问题导致下移*/
/deep/ .el-form-item__content {
  position: relative;
}
/*表单项宽度*/
/deep/ .el-form-item {
  width: 48%;
}
/*调整错误提示的位置*/
/deep/ .el-form-item__error--inline {
  position: absolute;
  top: 8px;
  right: 5px;
}
/deep/ .el-form-item.is-error .el-input__inner, .el-form-item.is-error .el-input__inner:focus, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner:focus, .el-message-box__input input.invalid, .el-message-box__input input.invalid:focus {
  background: pink;
}
/*label提示样式修改*/
/deep/ .el-form-item__label {
  color: #409EFF;
}
/deep/ .el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 100%;
}
/deep/ .el-select {
  width: 100%;
}
</style>
