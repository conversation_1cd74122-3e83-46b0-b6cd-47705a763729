<template>
  <div style="box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);">
    <div class="search">
      <scroll-pane :scroll-height="40">
        <el-form ref="form" :model="form" :inline="true" label-width="80px"  size="mini">
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model.trim="form.templateName"
              clearable
              @keyup.enter.native="handleSearch()"
              @clear="handleSearch()" ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="open">
            <el-select v-model="form.status" clearable>
              <el-option label="禁用" value="1"></el-option>
              <el-option label="启用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="handleSearch()">查询</el-button>
            <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </scroll-pane>
    </div>
    <div class="content">
      <div class="buttonGroup">
        <template v-if="$setAuthority('002009004', 'buttons')  || true">
          <el-button type="primary" size="mini" @click="handleAdd(false)">新增模板</el-button>
        </template>
        <template v-if="$setAuthority('002009009', 'buttons')">
          <el-button type="primary" size="mini" @click="handleToTemplateConfigPage">模板配置</el-button>
        </template>
        <template v-if="$setAuthority('002009010', 'buttons')">
          <el-button type="primary" size="mini" @click="handleCopy">复制模板</el-button>
        </template>
        <template v-if="$setAuthority('002009011', 'buttons')">
          <el-button :loading="downloading" type="primary" size="mini" @click="handleDownload">{{downloading ? '下载中' : '模板下载'}}</el-button>
        </template>
      </div>
      <div>
        <el-table
          ref="table"
          :data="tableData"
          class="template-table"
          height="calc(100vh - 40px - 30px - 12px - 30px - 70px - 45px - 32px)"
          style="width: 100%"
          @select="handleSelectTable"
          @row-click="handleRowClick"
          @select-all="handleSelectAll"
          @sort-change="handleSort">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="templateCode" label="模板编码" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="templateName" sortable="custom" label="模板名称" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <div style="display: flex;align-items: center;">
                <span :style="{background: scope.row.status === '0' ? 'green' : 'red'}" class="point"></span>
                <span>{{scope.row.statusText}}</span>
              </div>
            </template>
          </el-table-column>
          <!--<el-table-column prop="templateDirections" label="模板说明" min-width="250"></el-table-column>-->
          <!--<el-table-column prop="createInfo" label="创建信息" width="180"></el-table-column>-->
          <el-table-column prop="modifier" label="修改人" width="180"></el-table-column>
          <el-table-column prop="updateTime" sortable="custom" label="修改时间" width="180"></el-table-column>
          <!--<el-table-column prop="modifyInfo" label="修改信息" width="180"></el-table-column>-->
          <el-table-column
            fixed="right"
            label="操作"
            width="180">
            <template slot-scope="scope">
              <template v-if="$setAuthority('002009005', 'buttons')  || true">
                <el-button type="text" size="mini" @click.stop="handleAdd(scope.row)">编辑</el-button>
              </template>
              <template v-if="$setAuthority('002009007', 'buttons')">
                <el-button v-if="scope.row.status === '1'" type="text" size="mini" style="color: #67C23A" @click.stop="handleSetStatus(scope.row, '0')">启用</el-button>
              </template>
              <template v-if="$setAuthority('002009008', 'buttons')">
                <el-button v-if="scope.row.status === '0'" type="text" size="mini" style="color: #E6A23C" @click.stop="handleSetStatus(scope.row, '1')">禁用</el-button>
              </template>
              <template v-if="$setAuthority('002009006', 'buttons')">
                <el-button type="text" size="mini" style="color: red;" @click.stop="handleDelete(scope.row)">删除</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <template-list-edit-template-dialog
      :pvisible.sync="editTemplateDialogInfo.visible"
      :title="editTemplateDialogInfo.title"
      :template-info="editTemplateDialogInfo.templateInfo"
      @dialogConfirmEvent="getData(templateId)"
      @dialogCloseEvent="editTemplateDialogInfo.visible = false"/>
    <copy-template-dialog
      :pvisible.sync="copyTemplateDialogData.visible"
      :template-id="copyTemplateDialogData.templateId"
      :template-name="copyTemplateDialogData.templateName"
      :template-code="copyTemplateDialogData.templateCode"
      :old-template-row="copyTemplateDialogData.oldTemplateRow"
      @dialogCloseEvent="copyTemplateDialogData.visible = false"
      @dialogConfirmEvent="handleCopyDialogConfirm"
    />
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import templateListEditTemplateDialog from './templateListEditTemplateDialog'
import copyTemplateDialog from './copyTemplateDialog'
export default {
  name: 'detectionAppointment',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    templateListEditTemplateDialog,
    copyTemplateDialog
  },
  props: {
    templateId: {
      type: String | Number,
      default: null
    }
  },
  mounted () {
  },
  watch: {
    templateId: {
      handler: function (val) {
        this.tableData = []
        this.form = {
          templateName: '',
          status: ''
        }
        this.handleSearch(val)
      },
      immediate: true
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      form: {
        templateName: '',
        status: ''
      },
      downloading: false,
      formSubmit: {},
      templateNameOrder: 0, // 模板名称排序 0 降序 1 升序
      updateTimeOrder: 0, // 修改时间排序 0 降序 1 升序
      editTemplateDialogInfo: {
        title: '新建模板',
        templateInfo: null,
        visible: false
      },
      copyTemplateDialogData: {
        visible: false,
        templateCode: '',
        templateId: '',
        templateName: '',
        oldTemplateRow: null
      },
      openList: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  },
  methods: {
    getData (templateId = this.templateId) {
      this.$ajax({
        url: '/system/template/search_template',
        data: {
          templateCategoryId: templateId,
          reportName: this.formSubmit.templateName,
          state: this.formSubmit.status,
          nameOrder: this.templateNameOrder,
          updateTimeOrder: this.updateTimeOrder,
          page: this.currentPage,
          rows: this.pageSize
        },
        loadingDom: '.template-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.selectedRows.clear()
          let rows = res.data.records || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.reportTemplateId,
              templateCode: v.reportCode,
              templateName: v.reportName,
              fileType: v.fileType,
              fileName: v.reportPath,
              status: v.state,
              statusText: v.state === '0' ? '启用' : v.state === '1' ? '禁用' : '-',
              templateDirections: v.templateDetail,
              createTime: v.createTime,
              creator: v.creator,
              updateTime: v.updateTime,
              modifier: v.modifier,
              templateType: v.templateType,
              categoryArr: v.categoryArr || [], // 分类的树形数据，用于反选
              project: v.sendTestProject, // 送检项目
              NccnConfig: v.fnccnTargetTypeCode, // Nccn配置
              versionNum: v.fversionNum,
              coreMarkerGene: v.coreMarkerGene,
              chemotherapyConfig: v.fctDrugTypeCode, // 化疗配置
              ParpConfig: v.fparpConfigTypeCode, // PARP配置
              isPharmaceuticalFactoryReport: v.medicineReport, // 是否药厂报告
              evidenceLevelConfig: v.levelOut, // 证据等级配置
              detectSummary: v.testRsSummary, // 检测小结
              somaticVariationDescription: v.testRsSummaryT, // 体细胞变异说明
              germlineVariationDescription: v.testRsSummaryP, // 胚系变异说明
              detectMethod: v.testDetail, // 检测方法说明
              references: v.docList || [], // 参考文献
              prognosticTargetCode: v.fprognosticTargetCode
            }
            item.realData = {...item}
            util.setDefaultEmptyValueForObject(item)
            // item.createInfo = `${item.creator} | ${item.createTime}`
            // item.modifyInfo = `${item.modifier} | ${item.updateTime}`
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSearch (val) {
      if (!val) val = this.templateId
      this.currentPage = 1
      this.formSubmit = {...this.form}
      this.getData(val)
    },
    // 重置
    handleReset () {
      this.form = {templateName: '', status: ''}
      this.handleSearch()
    },
    // 复制弹窗确认
    handleCopyDialogConfirm (row) {
      console.log(row)
      this.editTemplateDialogInfo = {
        title: '编辑模板',
        visible: true
      }
      this.editTemplateDialogInfo.templateInfo = row
    },
    // 增加模板
    handleAdd (row) {
      console.log(row)
      if (row) {
        this.editTemplateDialogInfo = {
          title: '编辑模板',
          visible: true
        }
        this.editTemplateDialogInfo.templateInfo = row.realData
      } else {
        this.editTemplateDialogInfo = {
          title: '新建模板',
          templateInfo: null,
          visible: true
        }
      }
    },
    handleCopy () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择一行')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.error('只能选择一项数据进行操作')
        return
      }
      let row = [...this.selectedRows.values()][0].realData
      this.copyTemplateDialogData.templateCode = row.templateCode
      this.copyTemplateDialogData.templateId = row.id
      this.copyTemplateDialogData.templateName = row.templateName
      this.copyTemplateDialogData.oldTemplateRow = row
      this.copyTemplateDialogData.visible = true
    },
    // 下载模板
    handleDownload () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择一行')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.downloading = true
      this.$ajax({
        url: '/system/template/download_template',
        data: {
          reportTemplateIds: ids.join(';')
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloading = false
      })
    },
    handleToTemplateConfigPage () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择一行')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.error('只能选择一项数据进行操作')
        return
      }
      let row = [...this.selectedRows.values()][0].realData
      this.$store.commit({
        type: 'old/setValue',
        category: 'templateId',
        templateId: row.id
      })
      this.$router.push({path: '/business/sub/templateConfigPage'})
    },
    // 删除模板
    handleDelete (row) {
      this.$confirm('此操作将永久删除模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$ajax({
          url: '/system/template/delete_template',
          method: 'get',
          data: {
            reportTemplateId: row.id
          }
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
    // 启用|禁用模板
    handleSetStatus (row, status) {
      this.$ajax({
        url: '/system/template/template_setState',
        method: 'get',
        data: {
          reportTemplateId: row.id,
          state: status
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('操作成功')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 排序
    handleSort ({ column, prop, order }) {
      const orderFiled = {
        ascending: 1,
        descending: 0
      }
      console.log(column, prop, order)
      if (prop === 'templateName') {
        this.templateNameOrder = order ? orderFiled[order] : 0
      }
      if (prop === 'updateTime') {
        this.updateTimeOrder = order ? orderFiled[order] : 0
      }
      this.handleSearch()
    },
    // // 点击行
    // handleRowClick (row) {
    //   this.handleSelectTable(undefined, row)
    // },
    // // 选中行
    // handleSelectTable (selection, row) {
    //   if (!this.selectedRows.has(row.id)) {
    //     this.$refs.table.clearSelection()
    //     this.selectedRows.clear()
    //   }
    //   this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
    //   this.selectedRows.has(row.id)
    //     ? this.selectedRows.delete(row.id)
    //     : this.selectedRows.set(row.id, row)
    // },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped>
  .search{
    background-color: #ffffff;
    height: 60px;
    display: flex;
    align-items: center;
  }
  .search >>>.el-form-item{
    margin-bottom: 0;
  }
  .content{
    background-color: #ffffff;
  }
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }
  >>>.el-pagination{
    padding: 7px 2em;
  }
  /*/deep/ .el-table__header .el-checkbox {*/
    /*display: none;*/
  /*}*/
  .point{
    display: inline-block;
    width: 0.4em;
    height: 0.4em;
    background: black;
    border-radius: 50%;
    margin-right: 3px;
  }
</style>
