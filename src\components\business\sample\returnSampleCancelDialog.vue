<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose" title="撤回"
      width="40%"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item prop="reason" label="撤回原因">
          <el-input v-model.trim="form.reason" size="mini" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.removeImg" >同步删除图片</el-checkbox>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button  :loading="loading" size="mini" type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    returnId: {
      type: [Number, String],
      default: '',
      required: true
    }
  },
  data () {
    return {
      form: {
        reason: '',
        removeImg: ''
      },
      rules: {
        reason: [
          {required: true, message: '请输入撤回原因', trigger: 'blur'}
        ]
      },
      loading: false
    }
  },
  methods: {
    handleOpen () {
      this.form = {
        reason: '',
        removeImg: ''
      }
    },
    // 撤回
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/sample/return/revoke_return_result',
            data: {
              fid: this.returnId,
              reason: this.form.reason,
              removeImg: this.form.removeImg
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success('撤回成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }

  }
}
</script>

<style scoped></style>
