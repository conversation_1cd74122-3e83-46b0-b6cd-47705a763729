<!--耐药基因突变-->
<template>
  <div>
    <el-table
      ref="table"
      border
      :data="tableData"
      style="width: 100%"
      :cell-style="handleCheckCell"
      :height="'calc(100% - 32px)'"
      class="dataFilterTable"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @row-click="handleRowClick"
      :row-class-name="tableRowClassName"
    >
      <el-table-column fixed align="right" type="index" width="45"/>
      <el-table-column fixed align="center" type="selection" width="45"/>
      <el-table-column fixed :align="getColumnType['report']" prop="report" label="是否报出" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['Confirmed']" prop="Confirmed" label="报出参考" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['mutID']" prop="mutID" label="突变ID" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['Organism']" prop="Organism"
                       label="物种" width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column fixed :align="getColumnType['geneSymbol']" prop="geneSymbol"
                       label="GeneSymbol" width="160" show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['cHGVS']" prop="cHGVS"
                       label="cHGVS" width="160" show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['pHGVS']" prop="pHGVS"
                       label="pHGVS" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fhotLabel']" prop="fhotLabel"
                       label="hot_label" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fphenotypicAssociationConfidence']" prop="fphenotypicAssociationConfidence"
                       label="表型关联置信度" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fspeciesAssociationConfidence']" prop="fspeciesAssociationConfidence"
                       label="物种关联置信度" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['readsDepth']" prop="readsDepth"
                       label="Reads_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['uniqReadsDepth']" prop="uniqReadsDepth"
                       label="Uniq_Reads_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fdp']" prop="fdp"
                       label="DP" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['faf']" prop="faf"
                       label="AF" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fadRef']" prop="fadRef"
                       label="AD_REF" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fadAlt']" prop="fadAlt"
                       label="AD_ALT" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fmbqRef']" prop="fmbqRef"
                       label="MBQ_REF" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fmbqAlt']" prop="fmbqAlt"
                       label="MBQ_ALT" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fmmqRef']" prop="fmmqRef"
                       label="MMQ_REF" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fmmqAlt']" prop="fmmqAlt"
                       label="MMQ_ALT" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['drugClass']" prop="drugClass"
                       label="耐药类别" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['drugResistanceMerchanism']" prop="drugResistanceMerchanism"
                       label="耐药机制" width="160" show-overflow-tooltip/>

      <el-table-column :align="getColumnType['Deplot']" prop="Deplot" label="Reads图"
                       width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button class="underline" type="text" @click.stop="handleShowImg(scope.row.realData.fdeplot)">
            链接
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :align="getColumnType['Chr']" prop="Chr" label="Chr"
                       width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Start']" prop="Start"
                       label="Start" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Stop']" prop="Stop"
                       label="Stop" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Ref']" prop="Ref"
                       label="Ref" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Alt']" prop="Alt"
                       label="Alt" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['varType']" prop="varType"
                       label="VarType" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fvcfPos']" prop="fvcfPos"
                       label="VCFPos" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fvcfRef']" prop="fvcfRef"
                       label="VCFRef" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['fvcfAlt']" prop="fvcfAlt"
                       label="VCFAlt" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['freasonOfFilter']" prop="freasonOfFilter"
                       label="Reason_of_Filter" width="160" show-overflow-tooltip/>

    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      :total="totalPage">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh"/>
      </button>
    </el-pagination>
  </div>
</template>

<script>
import util from '@/util/util'
import mixins from '@/util/mixins'
import {getTypeArray} from '../../../../../../util/util'

export default {
  name: 'DrugResistantGeneMutation',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    refresh: {
      type: Boolean
    }
  },
  mounted () {
    this.getData()
    // 处理拉动列名时，fix列的行位置与非fix列的行位置错位
    const table = document.querySelector('.el-table')
    table.addEventListener('click', async (e) => {
      await this.$nextTick()
      this.$refs.table.doLayout()
    })
  },
  beforeDestroy () {
    const table = document.querySelector('.el-table')
    if (table) {
      table.removeEventListener('click', async (e) => {
        await this.$nextTick()
        this.$refs.table.doLayout()
      })
    }
  },
  watch: {
    refresh: {
      handler (newVal) {
        if (newVal) {
          this.getData()
        }
      }
    }
  },
  data () {
    return {
      tableData: [],
      selectedRows: new Map(),
      getColumnType: []
    }
  },
  methods: {
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/get_drug_resistance_mutation_gene',
        data: {
          analysisId: this.$route.query.oxym,
          current: this.currentPage + '',
          size: this.pageSize + ''
        },
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        let rows = data.rows || []
        this.totalPage = data.total
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            report: v.freport,
            Confirmed: v.fconfirmed,
            Organism: v.forganism,
            Deplot: v.freadDeplots,
            mutID: v.fmutId,
            Chr: v.fchr,
            Start: v.fstart,
            Stop: v.fstop,
            Ref: v.fref,
            Alt: v.falt,
            varType: v.fvarType,
            readsDepth: v.freadsDepth,
            uniqReadsDepth: v.funiqReadsDepth,
            geneSymbol: v.fgeneSymbol,
            cHGVS: v.fchGvs,
            pHGVS: v.fphGvs,
            drugClass: v.fdrugClass,
            drugResistanceMerchanism: v.fdrugResistanceMerchanism,
            fdeplot: v.fdeplot,
            fhotLabel: v.fhotLabel,
            fphenotypicAssociationConfidence: v.fphenotypicAssociationConfidence,
            fspeciesAssociationConfidence: v.fspeciesAssociationConfidence,
            fdp: v.fdp,
            faf: v.faf,
            fadRef: v.fadRef,
            fadAlt: v.fadAlt,
            fmbqRef: v.fmbqRef,
            fmbqAlt: v.fmbqAlt,
            fmmqRef: v.fmmqRef,
            fmmqAlt: v.fmmqAlt,
            fvcfPos: v.fvcfPos,
            fvcfRef: v.fvcfRef,
            fvcfAlt: v.fvcfAlt,
            freasonOfFilter: v.freasonOfFilter
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        this.getColumnType = getTypeArray(this.tableData)
      }
    },
    // 动态行样式
    handleCheckCell ({row}) {
      const id = row.id
      // 1.let rows = [...this.selectedRows.values()]
      // const isSelect = rows.some(v => v.id === id)
      // 2.this.selectedRows.has(id)
      return this.selectedRows.has(id) ? 'background: #ecf6ff' : ''
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
      this.$emit('handleRowClick', this.selectedRows)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
      this.$emit('handleSelect', this.selectedRows)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.$emit('handleSelectAll', this.selectedRows)
    },
    // 行样式
    tableRowClassName ({row, rowIndex}) {
      const obj = {
        Y: 'red',
        L: 'blue'
      }
      if (!row.report) return ''
      return obj[row.report]
    },
    handleShowImg (fdeplot) {
      if (!fdeplot) {
        return this.$message.error('未找到关联dplots图')
      }
      window.open(fdeplot, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table {
.red {
  color: #EE3838FF;
}

.blue {
  color: #539fff;
}
}
.underline {
  text-decoration: underline;
}
</style>
