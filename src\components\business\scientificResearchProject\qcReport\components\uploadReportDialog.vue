<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      title="上传报告"
      width="40%"
      @open="handleOpen"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        class="form"
        label-width="110px"
        size="mini"
        label-suffix=":">
        <el-form-item label="报告模版" prop="template">
          <el-select v-model="form.template" size="mini" clearable filterable>
            <el-option
              :key="index"
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in templates"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item  prop="template">
          <el-upload
            class="upload-demo"
            drag
            ref="upload"
            :action="uploadUrl"
            :file-list="fileList"
            :headers="headers"
            :on-success="handleOnSuccess"
            :on-error="handleOnError"
            :on-progress="handleProgress"
            :before-upload="handleBeforeUpload"
            :before-remove="handleRemove"
            multiple
            accept="pdf, doc, docx"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传word及pdf格式文件，且不超过10M</div>
          </el-upload>

        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm(2)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import Cookies from 'js-cookie'
import constants from '../../../../../util/constants'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    qcResultIdList: {
      type: Array
    },
    subOrderId: {
      type: Number
    },
    orderCode: {
      type: String
    },
    reportType: {
      type: Number
    }
  },
  data () {
    return {
      loading: false,
      form: {
        template: ''
      },
      fileList: [],
      headers: {
        token: Cookies.get('token')
      },
      files: [],
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false,
      rules: {
        template: [
          {require: true, type: 'number', message: '请选择报告模版', trigger: ['blur', 'change']}
        ]
      },
      templates: [
      ]
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          template: '',
          partTestPics: [],
          electrophoreticGelPic: [],
          electrophoreticTestPic: [],
          ailentCompleteTestPic: [],
          qsepOrLabchipPic: []
        }
        this.$refs.form.resetFields()
        this.getTemplates()
      })
    },
    getTemplates () {
      this.$ajax({
        url: '/order/report/get_qc_report_template_list',
        data: {
          fqcResultIdList: this.qcResultIdList || [],
          fsubOrderId: this.subOrderId
        }
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.templates = []
          data.forEach(v => {
            let item = {
              label: v.fdescribe,
              value: v.fvalue * 1
            }
            this.templates.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    async handleClose () {
      await this.handleConfirmMessage('是否放弃操作？“取消”后已上传的数据将不会被系统保存')
      this.visible = false
      this.fileList = []
      this.$refs.upload.clearFiles()
      this.$emit('dialogCloseEvent')
    },
    async handleConfirmMessage (message) {
      await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
    },
    async handleConfirm (type) {
      if (!this.form.template) {
        this.$message.warning('请选择报告模板')
        return
      }
      if (this.onProgress) {
        this.$message.warning('文件正在上传，请等待上传完成！')
        return
      }
      if (this.fileList.length < 1) {
        this.$message.warning('请上传报告')
        return
      }
      await this.handleConfirmOption('是否确认提交报告？')
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = {
            fqcResultIdList: this.qcResultIdList || [],
            fsubOrderId: this.subOrderId,
            ftype: this.form.template,
            forderCode: this.orderCode,
            freportType: this.reportType,
            fileList: this.fileList.map(v => {
              return {
                name: v.name,
                group: v.group,
                path: v.path
              }
            })
          }
          this.loading = true
          this.$ajax({
            url: '/order/report/upload_qc_report',
            data: params,
            loadingDom: '.form'
          }).then((res) => {
            if (res && res.code === this.SUCCESS_CODE) {
              if (type === 2) {
                this.$message.success('生成成功')
                this.visible = false
                this.$emit('dialogConfirmEvent')
              } else {
                window.open(res.data, '_blank')
              }
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      let name = file.name
      let uploadFiles = this.$refs.upload.uploadFiles || [{size: 0}]

      // 判断文件大小)
      if (uploadFiles.length > 100) {
        this.$message.error('一次性上传的文件数量超过系统限制，不允许上传')
        return
      }
      if (!/\.(pdf|doc|docx)$/.test(name)) {
        this.$message.error('文件格式不符合，不允许上传')
        return false
      }
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件大小超过10M，不允许上传')
        return false
      }
      return true
    },
    // 提交成功回调
    async handleOnSuccess (res = {}) {
      this.onProgress = false
      if (res && res.code === this.SUCCESS_CODE) {
        let fileData = {}
        fileData.url = res.data.absolutePath
        fileData.group = res.data.group
        fileData.path = res.data.path
        fileData.name = res.data.originalFileName
        // 判端this.fileList内是否存在该格式文件
        let index = this.fileList.findIndex(item => {
          let fileType = item.name.split('.')[1]
          return fileType === fileData.name.split('.')[1]
        })
        if (index !== -1) {
          try {
            await this.handleConfirmOption('只能提交一份报告的word及pdf格式。上传文件格式重复，是否覆盖？')
            this.fileList.splice(index, 1, fileData)
            this.$message.success('报告上传成功')
          } catch (e) {
            console.log(this.fileList)
            this.fileList = this.fileList
          }
        } else {
          this.fileList.push(fileData)
          this.$message.success('报告上传成功')
        }
        this.$refs.upload.uploadFiles = [...this.fileList]
      } else {
        this.$message.error(res.msg)
      }
    },
    async handleConfirmOption (message) {
      const result = await this.$confirm(message, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        closeOnClickModal: false,
        type: 'warning'
      })
      return result
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    async handleRemove (file) {
      if (file && file.status === 'success') {
        await this.handleConfirmOption('是否删除？')
        this.onProgress = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-select {
  width: calc(100% - 110px);
}
.tips {
  color: $color;
}
.label {
  display: inline-block;
  width: 110px;
  padding: 0 12px 0 0;
  text-align: right;
}
</style>
