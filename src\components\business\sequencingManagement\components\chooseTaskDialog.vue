<template>
  <el-dialog
    title="选择任务单"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    width="800px"
    @open="handleOpen">
    <div class="search-form">
      <el-form ref="form" size="mini" label-width="100px" :model="form">
        <el-form-item label="任务单编号">
          <div class="flex">
            <el-input v-model.trim="form.taskCode" class="form-width" clearable size="mini" placeholder="请输入(模糊查询)"></el-input>
            <el-input v-model.trim="form.taskCodes" class="form-width" clearable size="mini" placeholder="请输入(批量精准查询)"></el-input>
            <div style="margin-left: 10px">
              <el-button size="mini" type="primary" @click="handleSearch">查询</el-button>
              <el-button size="mini" @click="handleReset">重置</el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model.trim="form.time"
            type="daterange"
            size="mini"
            class="form-long-width"
            clearable
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            @change="handleTimeChange"
          />
        </el-form-item>
      </el-form>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      class="choose-table"
      size="mini"
      border
      style="width: 100%"
      height="200px"
      @select="handleSelectTable"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column type="selection" label="序号"></el-table-column>
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column label="任务单编号" prop="code"></el-table-column>
      <el-table-column label="任务单状态" prop="statusText"></el-table-column>
    </el-table>
    <div style="display: flex; align-items: center;font-size: 13px;">
      <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
        当前选中 {{ selectedRowsSize }} 条记录
      </span>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh">
          <icon-svg icon-class="icon-refresh"/>
        </button>
      </el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap, computeObjectValidKeyNum} from '../../../../util/util'
import {getChooseTaskList} from '../../../../api/sequencingManagement/sequencingManagementApi'

export default {
  name: 'chooseTaskDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    type: {
      type: String,
      default: 'downloadTask' //  downloadTask 下载任务  returnResult 回填结果
    },
    workId: { // 当前流程Id
      type: String,
      default: '1'
    },
    workflowStatus: { // 当前流程状态
      type: String,
      default: '1'
    },
    isSingle: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        taskCodes: '',
        taskCode: '',
        time: [
          util.dateFormatter(new Date(this.getLastMonth()), false, '00:00:00'),
          util.dateFormatter(new Date(), false, '23:59:59')
        ]
      },
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          taskCodes: '',
          taskCode: '',
          time: [
            util.dateFormatter(new Date(this.getLastMonth()), false, '00:00:00'),
            util.dateFormatter(new Date(), false, '23:59:59')
          ]
        }
        this.selectedRows.clear()
        this.handleSearch()
      })
    },
    getLastMonth () {
      let now = new Date()
      // 当前月的日期
      let nowDate = now.getDate()
      let lastMonth = new Date(now.getTime())
      // 设置上一个月（这里不需要减1）
      lastMonth.setMonth(lastMonth.getMonth())
      // 设置为0，默认为当前月的最后一天
      lastMonth.setDate(0)
      // 上一个月的天数
      let daysOflastMonth = lastMonth.getDate()
      // 设置上一个月的日期，如果当前月的日期大于上个月的总天数，则为最后一天
      lastMonth.setDate(nowDate > daysOflastMonth ? daysOflastMonth : nowDate)
      return lastMonth
    },
    handleTimeChange () {
      const time = this.form.time
      if (time.length < 1) return
      const begin = new Date(time[0]).getTime()
      const end = new Date(time[1]).getTime()
      const day = (end - begin) / (24 * 60 * 60 * 1000)
      if (day > 365) {
        this.$message.error('最大查询区间为365天')
        this.form.time = []
      }
    },
    // 查询
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    // 重置
    handleReset () {
      this.form = {
        taskCodes: '',
        taskCode: '',
        time: [
          util.dateFormatter(new Date(this.getLastMonth()), false, '00:00:00'),
          util.dateFormatter(new Date(), false, '23:59:59')
        ]
      }
      this.handleSearch()
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        ftaskCode: this.form.taskCode,
        ftaskCodeList: util.setGroupData(this.form.taskCodes).split('、').filter(v => v !== ''),
        forderDateStart: time[0],
        forderDateEnd: time[1],
        fworkflowId: this.workId,
        fworkflowStatus: this.workflowStatus,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      if (computeObjectValidKeyNum(this.form, []) < 1) {
        this.$message.error('请输入查询条件')
        return
      }
      const params = this.getParams()
      let {res} = await awaitWrap(getChooseTaskList(params, {loadingDom: '.choose-table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.rows || []
        rows.forEach(v => {
          const statusList = ['删除', '正常']
          const item = {
            id: v.ftaskId,
            code: v.ftaskCode, // 任务单号
            status: v.ftaskStatus, // 任务状态
            statusText: (v.ftaskStatus && v.ftaskStatus !== 0) && statusList[v.ftaskStatus] // 任务状态
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    handleConfirm () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择任务单！')
        return
      }
      if (this.isSingle && this.selectedRows.size > 1) {
        this.$message.error('只能选择一个任务单！')
        return
      }
      const events = {
        downloadTask: 'downloadTask',
        returnResult: 'returnResult',
        downloadScheduler: 'downloadScheduler',
        addSample: 'addSample'
      }
      this.$emit(events[this.type] + 'Event', [...this.selectedRows.keys()])
      if (this.type === 'setConfig') {
        this.$emit('setConfigEvent', [...this.selectedRows.values()])
      }
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
