<template>
  <div class="wrapper">
    <!--    操作按钮组-->
    <div class="button-group">
      <el-button v-if="!isPreview" type="primary" size="mini" :loading="loadingCompare" @click="handleDbCompare">
        数据库比对
      </el-button>
      <el-button v-if="!isPreview" type="primary" size="mini" :loading="loadingAuto" @click="handleAutoGetData">
        自动获取数据
      </el-button>
      <el-button v-if="!isPreview" type="primary" size="mini" @click="handleImportData">数据导入</el-button>
      <el-button v-if="!isPreview" type="primary" size="mini" :loading="loadingDownload" @click="handleDownload">
        Excel下载
      </el-button>
      <!--      <el-button type="primary" size="mini" :loading="loadingCreate" @click="handleCreateReport">生成在线报告</el-button>-->
    </div>
    <!--    内容区-->
    <div class="pathogens-list-wrapper">
      <el-tabs
        v-model="type"
        tab-position="left"
        style="height: 100%;width: 100px;"
        @tab-click="handleChangeTabs">
        <el-tab-pane label="细菌" name="0"></el-tab-pane>
        <el-tab-pane label="病毒" name="20"></el-tab-pane>
        <el-tab-pane label="真菌" name="10"></el-tab-pane>
        <el-tab-pane label="寄生虫" name="70"></el-tab-pane>
        <el-tab-pane label="耐药基因" name="30"></el-tab-pane>
        <el-tab-pane label="耐药基因突变" name="40"></el-tab-pane>
        <el-tab-pane label="毒力基因" name="50"></el-tab-pane>
        <el-tab-pane label="新冠分型" name="60"></el-tab-pane>
        <el-tab-pane label="QC结果" name="qc"></el-tab-pane>
      </el-tabs>
      <div class="components-wrapper">
        <div v-if="type !== 'qc' && type !== '60' && !isPreview"  style="height: 42px">
          <el-dropdown style="margin-bottom: 10px;" @command="handleCommand">
            <el-button type="primary" size="mini">
              标记为<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item, index) in reports"
                :key="index"
                :command="item.value">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <component
          v-if="isCommonComponent()"
          :is="currentComponent"
          :type="type"
          :refresh="code"
          style="height: calc(100% - 40px)"
          @handleSelect="handleSelect"
          @handleSelectAll="handleSelectAll"
          @handleRowClick="handleRowClick"
          @getData="getData"
          @showImg="showImg"
        />
        <div v-show="type === 'qc'" style="height: 100%" class="qc-table">
          <el-table
            ref="table"
            :data="qcList"
            border
            height="100%"
            style="width: 100%"
          >
            <el-table-column prop="label" label="item" show-overflow-tooltip key="item"></el-table-column>
            <el-table-column prop="value" label="value" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
        <import-dialog :pvisible.sync="visible" @importDialogConfirmEvent="getData"></import-dialog>
        <img-dialog :pvisible.sync="imgVisible" :tax-id="taxId" :img="message"></img-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import util from '../../../../../util/util'
import importDialog from './components/importDialog'
import ImgDialog from './components/imgDialog'
import Common from './components/common.vue'
import DrugResistantGene from './components/DrugResistantGene.vue'
import VirulenceGene from './components/VirulenceGene.vue'
import DrugResistantGeneMutation from './components/DrugResistantGeneMutation.vue'
import COVIDClassification from './components/COVIDClassification.vue'
// import Cookies from 'js-cookie'

export default {
  name: 'tNGSPathogensList',
  components: {
    ImgDialog,
    importDialog,
    Common,
    DrugResistantGene,
    DrugResistantGeneMutation,
    VirulenceGene,
    COVIDClassification
  },
  mounted () {
    // Cookies.set('x-lims-token', '45f78f3d935d423e97df3c64008942b7')
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId') || this.$route.query.oxym
    },
    pathogenicType () {
      return this.$store.getters.getValue('pathogenicType')
    },
    isPreview () {
      return this.$store.getters.getValue('isPreview') || this.$route.query.isPreview
    },
    currentComponent () {
      const panel = {
        '0': 'Common',
        '10': 'Common',
        '20': 'Common',
        '30': 'DrugResistantGene',
        '40': 'DrugResistantGeneMutation',
        '50': 'VirulenceGene',
        '60': 'COVIDClassification',
        '70': 'Common'
      }
      return panel[this.type]
    },
    reports () {
      const arr = ['0', '10', '20', '70']
      const hasBateria = arr.includes(this.type)
      if (hasBateria) {
        return [
          {label: 'Y：高可信度报出', value: 'Y'},
          {label: 'B：疑似背景菌', value: 'B'},
          {label: 'L：低可信度报出', value: 'L'},
          {label: 'N：不报出', value: 'N'}
        ]
      }
      return [{label: 'Y：高可信度报出', value: 'Y'},
        {label: 'L：低可信度报出', value: 'L'},
        {label: 'N：不报出', value: 'N'}]
    }
  },
  data () {
    return {
      type: '0',
      visible: false,
      imgVisible: false,
      taxId: '',
      loadingCompare: false,
      loadingAuto: false,
      loadingDownload: false,
      loadingCreate: false,
      loadingCommand: false,
      selectedRows: new Map(),
      qcList: [
        {label: 'RawReads(M)', props: 'frawReadsM', value: ''},
        {label: 'RawBases(G)', props: 'frawBasesG', value: ''},
        {label: 'RawQ20(%)', props: 'frawQTwenty', value: ''},
        {label: 'RawQ30(%)', props: 'frawQThirty', value: ''},
        {label: 'RawGC(%)', props: 'frawGc', value: ''},
        {label: 'CleanReads(M)', props: 'fcleanReads', value: ''},
        {label: 'CleanBases(G)', props: 'fcleanBaseG', value: ''},
        {label: 'CleanQ20(%)', props: 'fcleanQTwenty', value: ''},
        {label: 'CleanQ30(%)', props: 'fcleanQThirty', value: ''},
        {label: 'CleanGC(%)', props: 'fcleanGc', value: ''},
        {label: 'Adapter(%)', props: 'fadapterRatio', value: ''},
        {label: 'HostReads(%)', props: 'fhostReadsRatio', value: ''},
        {label: 'NonHostReadsClassified(%)', props: 'fnonHostReadsClassifiedRatio', value: ''},
        {label: 'NonHostReadsUnClassified(%)', props: 'fnonHostReadsUnclassifiedRatio', value: ''},
        {label: 'Bacteria', props: 'fbacteria', value: ''},
        {label: 'Fungi', props: 'ffungi', value: ''},
        {label: 'Viruses', props: 'fviruses', value: ''},
        {label: 'Parasites', props: 'fparasites', value: ''},
        {label: 'HSKReads(%)', props: 'fhskReads', value: ''},
        {label: 'HSKCov(%)', props: 'fhskCov', value: ''},
        {label: 'SampleIC', props: 'fsampleIc', value: ''},
        {label: 'OtherIC', props: 'fotherIc', value: ''},
        {label: 'AMR', props: 'famr', value: ''},
        {label: 'VF', props: 'fvf', value: ''},
        {label: 'TargetReads', props: 'ftargetReads', value: ''}],
      tableData: [],
      code: false, // 用于标记子组件（细菌、病毒等）是否需要重新调用数据接口
      message: ''
    }
  },
  methods: {
    // 动态行样式
    handleCheckCell ({row}) {
      console.log(row)
      const id = row.id
      let rows = [...this.selectedRows.values()]
      const isSelect = rows.some(v => v.id === id)
      return isSelect ? 'background: #ecf6ff' : ''
    },
    handleChangeTabs () {
      this.type === 'qc' && this.getQcResultList()
    },
    async getQcResultList () {
      let {code, data = []} = await this.$ajax({
        url: '/read/pathogen/get_pathogen_excel_qc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get',
        loadingDom: '.qc-table'
      })
      if (code === this.SUCCESS_CODE) {
        this.qcList = this.qcList || []
        this.qcList = this.qcList.map(v => {
          v.value = data[v.props]
          v.realData = JSON.parse(JSON.stringify(v))
          util.setDefaultEmptyValueForObject(v)
          return v
        })
      }
    },
    async getData (args) {
      this.code = true
    },
    async handleDbCompare () {
      this.loadingCompare = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/db_compare',
          data: {
            analysisRsId: this.analysisRsId,
            version: this.pathogenicType
          },
          method: 'get'
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('比对成功')
          await this.getData()
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingCompare = false
      }
    },
    // 自动获取数据
    async handleAutoGetData () {
      this.loadingAuto = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/auto_get_read_excel',
          data: {
            analysisRsId: this.analysisRsId,
            version: this.pathogenicType
          },
          method: 'get'
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('获取数据成功')
          await this.getData()
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingAuto = false
      }
    },
    // 数据导入
    handleImportData () {
      this.visible = true
    },
    // 下载结果文件
    async handleDownload () {
      this.loadingDownload = true
      try {
        let res = await this.$ajax({
          url: '/read/pathogen/download_excel_result',
          data: {
            analysisRsId: this.analysisRsId
          },
          responseType: 'blob',
          method: 'get'
        })
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      } finally {
        this.loadingDownload = false
      }
    },
    // 生成在线报告
    async handleCreateReport () {
      this.loadingCreate = true
      try {
        let {code, message} = await this.$ajax({
          url: '/read/pathogen/build_online_report',
          data: {
            analysisRsId: this.analysisRsId
          },
          method: 'get'
        })
        if (code === this.SUCCESS_CODE) {
          this.$message.success('生成在线报告成功')
        } else {
          this.$message.error(message)
        }
      } finally {
        this.loadingCreate = false
      }
    },
    async handleCommand (command) {
      if (this.selectedRows.size < 1) {
        this.$message.error('请至少选择一条数据标记')
        return
      }
      this.loadingCommand = true
      this.code = false
      // type用于标识动态组件
      const type = ['0', '10', '20', '30', '40', '50', '60', '70']
      if (type.includes(this.type)) {
        // 类型映射
        const typeMap = {
          '30': 4,
          '40': 5,
          '50': 6,
          '': null
        }
        try {
          let {code, message} = await this.$ajax({
            url: '/read/pathogen/sign_report',
            data: {
              ids: [...this.selectedRows.values()].map(v => v.id).join(','),
              report: command,
              type: typeMap[this.type]
            }
          })
          if (code === this.SUCCESS_CODE) {
            this.code = true
            this.selectedRows = new Map()
            this.$message.success('标记成功')
          } else {
            this.$message.error(message)
          }
        } finally {
          this.loadingCommand = false
        }
      }
    },
    isCommonComponent () {
      const type = ['0', '10', '20', '30', '40', '50', '60', '70']
      return type.includes(this.type)
    },
    handleRowClick (e) {
      this.selectedRows = e
    },
    handleSelect (e) {
      this.selectedRows = e
    },
    handleSelectAll (e) {
      this.selectedRows = e
    },
    async showImg (dbSeq) {
      console.log('dbSeq', dbSeq)
      // const arr = [30, 40, 50]
      // const is = arr.includes(this.type)
      // const myTaxId = is ? {dbSeq: taxId} : {taxId: taxId}
      let {code, data = [], message} = await this.$ajax({
        url: '/read/tngs/pathogen/get_deplot',
        data: {
          analysisRsId: this.analysisRsId,
          dbSeq
        },
        methods: 'get'
      })
      if (code === this.SUCCESS_CODE) {
        this.message = data
        this.imgVisible = true
      } else if (code === '40000') {
        this.$message.error(message)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  .button-group {
    padding: 20px 10px;
  }

  .pathogens-list-wrapper {
    display: flex;
    height: calc(100vh - 232px);
    width: 100%;
    background: #fff;

    .components-wrapper {
      padding: 10px 20px;
      width: calc(100% - 133px);
    }
  }
}

/deep/ .el-table--scrollable-x {
  .el-table__body-wrapper {
    z-index: 2;
  }
}

/deep/ #tab-40 {
  line-height: 40px;
  white-space: pre-wrap;
}

/deep/ .el-tabs {
  width: 133px !important;
}

/deep/ .el-tabs__item {
  height: auto;
}

</style>
