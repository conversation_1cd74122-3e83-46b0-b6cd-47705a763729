<template>
  <div class="page">
    <div class="buttonGroup">
      <el-button type="primary" size="mini" @click="handleContrast">数据库比对</el-button>
      <el-button size="mini" type="primary" @click="handleGetDorH">自动获取数据</el-button>
      <el-dropdown placement="bottom" style="margin-left: 10px;">
        <el-button size="mini" type="primary">
          导入<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleImportData(0)">D数据</el-dropdown-item>
          <el-dropdown-item @click.native="handleImportData(1)">H数据</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown placement="bottom" style="margin-left: 10px;">
        <el-button size="mini"  type="primary">
          下载<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleDownload('0')">D数据</el-dropdown-item>
          <el-dropdown-item @click.native="handleDownload('1')">H数据</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="content">
      <div>
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%;" @tab-click="handleClick">
          <el-tab-pane label="体系-SNV" name="1"></el-tab-pane>
          <el-tab-pane label="体系-SV" name="2"></el-tab-pane>
          <el-tab-pane label="体系-CNV" name="3"></el-tab-pane>
          <el-tab-pane label="胚系-SNV" name="4"></el-tab-pane>
          <el-tab-pane label="胚系-CNV" name="5"></el-tab-pane>
          <el-tab-pane label="hot_QC结果" name="7"></el-tab-pane>
          <el-tab-pane label="QC结果" name="6"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="padding: 5px; overflow: auto;">
        <template v-if="activeName === '1'">
          <div class="buttonGroup">
            <el-button type="primary" size="mini" @click="handleAddForecastData">新增预测数据</el-button>
            <el-button type="primary" size="mini" @click="handleSignReport">标记报出</el-button>
            <el-button type="primary" size="mini" @click="handleSignNoReport">取消报出</el-button>
            <el-button type="primary" size="mini" @click="handleJudgment">顺反式判断</el-button>
            <el-button type="primary" size="mini" @click="handleMark(1)">标记定制</el-button>
            <el-button type="primary" size="mini" @click="handleCancelMark(1)">取消定制</el-button>
            <el-button type="primary" size="mini" @click="handleMutationNote(activeName)">突变备注</el-button>
          </div>
        </template>
        <template v-else-if="activeName === '2' || activeName === '3'">
          <div class="buttonGroup">
            <el-button type="primary" size="mini" @click="handleAddForecastData">新增预测数据</el-button>
            <el-button type="primary" size="mini" @click="handleSignReport">标记报出</el-button>
            <el-button type="primary" size="mini" @click="handleSignNoReport">取消报出</el-button>
            <el-button type="primary" size="mini" @click="handleAddDXData">新增DX数据</el-button>
            <el-button v-if="activeName === '2'" type="primary" size="mini" @click="handleMark(2)">标记定制</el-button>
            <el-button v-if="activeName === '2'" type="primary" size="mini" @click="handleCancelMark(2)">取消定制</el-button>
            <el-button type="primary" size="mini" @click="handleMutationNote(activeName)">突变备注</el-button>
          </div>
        </template>
        <template v-else-if="activeName === '4'">
          <div class="buttonGroup">
            <el-button type="primary" size="mini" @click="handleAddForecastData">新增预测数据</el-button>
            <el-button type="primary" size="mini" @click="handleSignReport">标记报出</el-button>
            <el-button type="primary" size="mini" @click="handleSignNoReport">取消报出</el-button>
            <el-button type="primary" size="mini" @click="handleMutationNote(activeName)">突变备注</el-button>
          </div>
        </template>
        <template v-else-if="activeName === '5'">
          <div class="buttonGroup">
            <el-button type="primary" size="mini" @click="handleAddForecastData">新增预测数据</el-button>
            <el-button type="primary" size="mini" @click="handleSignReport">标记报出</el-button>
            <el-button type="primary" size="mini" @click="handleSignNoReport">取消报出</el-button>
            <el-button type="primary" size="mini" @click="handleAddGeneticMutation">新增遗传变异</el-button>
            <el-button type="primary" size="mini" @click="handleGeneMerge">合并</el-button>
            <el-button type="primary" size="mini" @click="handleMutationNote(activeName)">突变备注</el-button>
          </div>
        </template>
        <el-table :data="tableData" :height="activeName === '6' ? '100%' : 'calc(100% - 45px)'" ref="table"
                  class="table"
                  size="mini"
                  border
                  style="width: 100%;"
                  @select="handleSelect"
                  @select-all="handleSelectAll"
                  @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="45"></el-table-column>
          <template v-if="activeName === '1'">
            <el-table-column key="1-fcanComparison" label="比对" width="120">
              <template slot-scope="scope">
                <template v-if="scope.row.fcanComparison === '0'">
                  <i class="el-icon-success" style="color: #67C23A;"></i>
                  <template v-if="scope.row.fisRead === '0'">
                    <span>(有解读)</span>
                  </template>
                  <template v-else>
                    <span>(无解读)</span>
                  </template>
                </template>
                <template v-else>
                  <i class="el-icon-error" style="color:#909399;"></i>
                </template>
              </template>
            </el-table-column>
            <el-table-column key="1-freport" prop="freport" label="是否报出" width="100"></el-table-column>
            <el-table-column key="1-fisSelect" prop="fisSelect" label="Is Select" width="100"></el-table-column>
            <el-table-column key="1-fcount" prop="fcount" label="count" width="100"></el-table-column>
            <el-table-column key="1-freadsPlot" label="Reads图" width="100">
              <template slot-scope="scope">
                <template v-if="scope.row.freadsPlot !== '-'">
                  <el-button size="mini" type="text" @click="handleReadsPicture(scope.row.freadsPlot)">链接
                  </el-button>
                </template>
                <template v-else>
                  链接
                </template>
              </template>
            </el-table-column>
            <el-table-column key="1-finProductCheckRange" prop="finProductCheckRange" label="检测范围" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.finProductCheckRange">
                  是
                </template>
                <template v-else>
                  否
                </template>
              </template>
            </el-table-column>
            <el-table-column key="1-fdxAttr" prop="fdxAttr" label="DX属性" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fgeneSymbol" prop="fgeneSymbol" label="Gene Symbol" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fchgvs" prop="fchgvs" label="cHGVS" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fphgvs" prop="fphgvs" label="pHGVS" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-ffunction" prop="ffunction" label="Function" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-ftranscript" prop="ftranscript" label="Transcript" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fexinId" prop="fexinId" label="ExIn_ID" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fcaseAf" prop="fcaseAf" label="caseAf" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fcaseAltGoodSuptReads" prop="fcaseAltGoodSuptReads" label="caseAltGoodSuptReads" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fcaseAltDep" prop="fcaseAltDep" label="caseAltDep" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fcosmicId" prop="fcosmicId" label="Cosmic ID" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fchr" prop="fchr" label="Chr" width="80" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fstart" prop="fstart" label="Start" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fstop" prop="fstop" label="Stop" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fref" prop="fref" label="Ref" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-falt" prop="falt" label="Call" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fcaseDep" prop="fcaseDep" label="CaseDep" width="100" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fautoInterpStatus" prop="fautoInterpStatus" label="AutoInterpStatus" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fmrdSelect" prop="fmrdSelect" label="MRDSelect" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fpyCloneCluster" prop="fpyCloneCluster" label="PYVI cluster" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fprobeGc" prop="fprobeGc" label="Probe_GC" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fvarLength" prop="fvarLength" label="Varlength" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-frepeatTag" prop="frepeatTag" label="RepeatTag" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fctrlAd" prop="fctrlAd" label="ctrlDep" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fctrlAf" prop="fctrlAf" label="ctrlAltAF" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fctrlDp" prop="fctrlDp" label="ctrlAltDep" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fchDb" prop="fchDb" label="CH_db" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fisTnb" prop="fisTnb" label="isTNB" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fisTnb" prop="ftnbRank" label="TNB_Rank" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fchipCode" prop="fchipCode" label="ChipCode" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column key="1-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
              <template slot-scope="scope">
                <el-tooltip placement="top">
                  <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
                  <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="activeName === '2'">
            <el-table-column key="2-fcanComparison" label="比对" width="120">
              <template slot-scope="scope">
                <template v-if="scope.row.fcanComparison === '0'">
                  <i class="el-icon-success" style="color: #67C23A;"></i>
                  <template v-if="scope.row.fisRead === '0'">
                    <span>(有解读)</span>
                  </template>
                  <template v-else>
                    <span>(无解读)</span>
                  </template>
                </template>
                <template v-else>
                  <i class="el-icon-error" style="color:#909399;"></i>
                </template>
              </template>
            </el-table-column>
            <el-table-column key="2-freport" prop="freport" label="是否报出" width="100" ></el-table-column>
            <el-table-column key="2-fisSelect" prop="fisSelect" label="Is Select" width="100" ></el-table-column>
            <el-table-column key="2-freadsPlot" label="Reads图" width="100">
              <template slot-scope="scope">
                <template v-if="scope.row.freadsPlot !== '-'">
                  <el-button size="mini" type="text" @click="handleReadsPicture(scope.row.freadsPlot)">链接
                  </el-button>
                </template>
                <template v-else>
                  链接
                </template>
              </template>
            </el-table-column>
            <el-table-column key="2-fbelongToSvCheckRange" prop="fbelongToSvCheckRange" label="检查范围" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.fbelongToSvCheckRange">
                  是
                </template>
                <template v-else>
                  否
                </template>
              </template>
            </el-table-column>
            <el-table-column key="2-fdxAttr" prop="fdxAttr" label="DX属性" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fnumOfHot" prop="fnumOfHot" label="NumOfHot" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-ffusionType" prop="ffusionType" label="FusionType" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fAlignGene" prop="fAlignGene" label="FAlignGene" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fAlignTrancript" prop="fAlignTrancript" label="FAlignTrancript" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fAlignExon" prop="fAlignExon" label="fAlignExon" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-ffusionExon" prop="ffusionExon" label="FusionExon" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-ffusionInfo" prop="ffusionInfo" label="FusionInfo" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-ffreq" prop="ffreq" label="Freq" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fpos1" prop="fpos1" label="Pos1" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fpos2" prop="fpos2" label="Pos2" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fgene1" prop="fgene1" label="Gene1" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-ftranscript1" prop="ftranscript1" label="Transcript1" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fMaploc1" prop="fMaploc1" label="Maploc1" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fgene2" prop="fgene2" label="Gene2" width="140" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-ftranscript2" prop="ftranscript2" label="Transcript2" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fAltDepth" prop="fAltDepth" label="AltDepth" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fDepth" prop="fDepth" label="Depth" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fautoInterpStatus" prop="fautoInterpStatus" label="AutoInterpretation" sortable width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="2-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
              <template slot-scope="scope">
                <el-tooltip placement="top">
                  <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
                  <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="activeName === '3'">
            <el-table-column key="1-fcanComparison" label="比对" width="120">
              <template slot-scope="scope">
                <template v-if="scope.row.fcanComparison === '0'">
                  <i class="el-icon-success" style="color: #67C23A;"></i>
                  <template v-if="scope.row.fisRead === '0'">
                    <span>(有解读)</span>
                  </template>
                  <template v-else>
                    <span>(无解读)</span>
                  </template>
                </template>
                <template v-else>
                  <i class="el-icon-error" style="color:#909399;"></i>
                </template>
              </template>
            </el-table-column>
            <el-table-column key="3-freport" prop="freport" label="是否报出" width="100"
                             ></el-table-column>
            <el-table-column key="3-freadsPlot" label="Reads图" width="100">
              <template slot-scope="scope">
                <template v-if="scope.row.freadsPlot !== '-'">
                  <el-button size="mini" type="text" @click="handleReadsPicture(scope.row.freadsPlot)">链接
                  </el-button>
                </template>
                <template v-else>
                  链接
                </template>
              </template>
            </el-table-column>
            <el-table-column key="3-finProductCheckRange" prop="finProductCheckRange" label="检测范围" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.finProductCheckRange">
                  是
                </template>
                <template v-else>
                  否
                </template>
              </template>
            </el-table-column>
            <el-table-column key="3-fdxAttr" prop="fdxAttr" label="DX属性" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fgene" prop="fgene" label="Gene" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fstate" prop="fstate" label="status" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fratio" prop="fratio" label="StatusRatio" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-favgRatio" prop="favgRatio" label="AvgRatio" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-ftumDepthOri" prop="ftumDepthOri" label="Depth" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fqual" prop="fqual" label="Qual" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-ftranscript" prop="ftranscript" label="Transcript" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fexon" prop="fexon" label="Exon" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-flengthPercent" prop="flengthPercent" label="TotalLengthPercent" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fMaploc" prop="fMaploc" label="Maploc" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fchr" prop="fchr" label="Chr" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fstart" prop="fstart" label="Start" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fend" prop="fend" label="End" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fautoInterpStatus" prop="fautoInterpStatus" label="AutoInterpretation" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="3-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
              <template slot-scope="scope">
                <el-tooltip placement="top">
                  <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
                  <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="activeName === '4'">
            <el-table-column key="4-fcanComparison" label="比对" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.fcanComparison === '0'">
                  <i class="el-icon-success" style="color: #67C23A;"></i>
                  <template v-if="scope.row.fisRead === '0'">
                    <span>(有解读)</span>
                  </template>
                  <template v-else>
                    <span>(无解读)</span>
                  </template>
                </template>
                <template v-else>
                  <i class="el-icon-error" style="color:#909399;"></i>
                </template>
              </template>
            </el-table-column>
            <el-table-column key="4-freport" prop="freport" label="Report" width="180" show-overflow-tooltip ></el-table-column>
            <el-table-column key="4-inProductCheckRange" prop="inProductCheckRange" label="检测范围" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.inProductCheckRange">
                  是
                </template>
                <template v-else>
                  否
                </template>
              </template>
            </el-table-column>
            <el-table-column key="4-fmutationType" prop="fmutationType" label="变异意义" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-finReport" label="inReport" width="180" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.freadsPlot !== '-'">
                  <el-button size="mini" type="text" @click="handleReadsPicture(scope.row.freadsPlot)">{{scope.row.finReport}}</el-button>
                </template>
                <template v-else>
                  {{scope.row.finReport}}
                </template>
              </template>
            </el-table-column>
            <el-table-column key="4-fgeneSymbol" prop="fgeneSymbol" label="Gene Symbol" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fchgvs" prop="fchgvs" label="cHGVS" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fphgvs" prop="fphgvs" label="pHGVS" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fzygosity" prop="fzygosity" label="Zygosity" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fexinId" prop="fexinId" label="ExIn_ID" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-ffunction" prop="ffunction" label="Function" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-ftranscript" prop="ftranscript" label="Transcript" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fpadCount" prop="fpadCount" label="PAD_counts" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-f1000gAf" prop="f1000gAf" label="1000G AF" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-f1000gAsnAf" prop="f1000gAsnAf" label="1000G EAS AF" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fexacAf" prop="fexacAf" label="ExAC AF" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fexacEasAf" prop="fexacEasAf" label="ExAC EAS AF" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fgadAf" prop="fgadAf" label="GAD AF" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fgadEasAf" prop="fgadEasAf" label="GAD EAS AF" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fpanelAlleleFreq" prop="fpanelAlleleFreq" label="Panel AlleleFreq" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-frsId" prop="frsId" label="rsID" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fhgmdPmId" prop="fhgmdPmId" label="HGMD pmID" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fclinVarID" prop="fclinVarID" label="CliVar ID" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fclinVarSignificant" prop="fclinVarSignificant" label="ClinVar Significant" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-faDepth" prop="faDepth" label="A.Depth" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-faRatio" prop="faRatio" label="A.Ratio" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="4-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
              <template slot-scope="scope">
                <el-tooltip placement="top">
                  <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
                  <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="activeName === '5'">
            <el-table-column key="5-fcanComparison" label="比对" width="120">
              <template slot-scope="scope">
                <template v-if="scope.row.fcanComparison === '0'">
                  <i class="el-icon-success" style="color: #67C23A;"></i>
                  <template v-if="scope.row.isRead === '0'">
                    <span>(有解读)</span>
                  </template>
                  <template v-else>
                    <span>(无解读)</span>
                  </template>
                </template>
                <template v-else>
                  <i class="el-icon-error" style="color:#909399;"></i>
                </template>
              </template>
            </el-table-column>
            <el-table-column key="5-freport" prop="freport" label="是否报出" width="100" ></el-table-column>
            <el-table-column key="5-freadsPlot" label="Reads图" width="100">
              <template slot-scope="scope">
                <template v-if="scope.row.freadsPlot !== '-'">
                  <el-button size="mini" type="text" @click="handleReadsPicture(scope.row.freadsPlot)">链接
                  </el-button>
                </template>
                <template v-else>
                  链接
                </template>
              </template>
            </el-table-column>
            <el-table-column key="5-inProductCheckRange" label="检测范围" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <template v-if="scope.row.inProductCheckRange">
                  是
                </template>
                <template v-else>
                  否
                </template>
              </template>
            </el-table-column>
            <el-table-column key="5-fmutationType" prop="fmutationType" label="变异意义" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-gene" prop="gene" label="Gene" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-state" prop="state" label="status" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-exon" prop="exon" label="exon" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-favgRatio" prop="ratio" label="avgRatio" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-transcript" prop="transcript" label="transcript" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-fautoInterpretation" prop="fautoInterpretation" label="AutoInterpretation" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="5-fmutationRemark" prop="fmutationRemark" label="突变备注" width="200">
              <template slot-scope="scope">
                <el-tooltip placement="top">
                  <div slot="content"> <span v-html="scope.row.fmutationRemark.join('<br/>')"></span></div>
                  <span class="mutation-remark" v-html="scope.row.fmutationRemark.join(',')"></span>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>
          <template v-else-if="activeName === '7'">
            <el-table-column key="gene" prop="gene" label="gene" width="120"></el-table-column>
            <el-table-column key="transcript" prop="transcript" label="transcript" width="100" show-overflow-tooltip>
            </el-table-column>
            <el-table-column key="cHGVS" prop="chgvs" label="cHGVS" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="pHGVS" prop="phgvs" label="pHGVS" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="pos" prop="pos" label="pos" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="chr" prop="chr" label="chr" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="start" prop="start" label="start" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="end" prop="end" label="end" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="fdedup_dep" prop="dedupDep" label="dedup_dep" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="altdep" prop="altdep" label="altdep" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="fdepth_coefficient" prop="depthCoefficient" label="depth_coefficient" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="hot_classify" prop="hotClassify" label="hot_classify" width="180" show-overflow-tooltip></el-table-column>
          </template>
          <template v-else-if="activeName === '6'">
            <el-table-column key="resultTitle" prop="resultTitle" label="QC结果" width="250" show-overflow-tooltip></el-table-column>
            <el-table-column key="caseResult" prop="caseResult" label="Case" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column key="controlResult" prop="controlResult" label="Control" show-overflow-tooltip></el-table-column>
          </template>
        </el-table>
      </div>
    </div>
    <judgment-dialog :pvisible="judgmentDialogVisible" :pdata="judgmentDialogData"
                     @handleJudgmentDialogConfirmEven="handleJudgmentDialogConfirm"
                     @handleJudgmentDialogCloseEven="handleJudgmentDialogClose"
    ></judgment-dialog>
    <gene-merge-dialog :pvisible="geneMergeDialogVisible" :pdata="geneMergeDialogData"
                     @handleGeneMergeDialogConfirmEven="handleGeneMergeDialogConfirm"
                     @handleGeneMergeDialogCloseEven="handleGeneMergeDialogClose"
    ></gene-merge-dialog>
    <import-dialog
      :pvisible="importDialogVisible"
      :pdata="importDialogType"
      @importDialogCloseEvent="handleImportDialogClose"
      @importDialogConfirmEvent="handleImportDialogConfirm"
    ></import-dialog>
    <mutation-note-dialog
      :gene-type="geneType"
      :id="id"
      :pvisible.sync="mutationNoteVisible"
      @dialogConfirmEvent="getData"
    ></mutation-note-dialog>
  </div>
</template>

<script>
import obj from '../../../../util/mixins'
import util from '../../../../util/util'
import constants from '../../../../util/constants'
import judgmentDialog from './common/judgmentDialog'
import geneMergeDialog from './common/geneMergeDialog'
import importDialog from './wesReportDetectionOfGeneVariationImportDialog'
import mutationNoteDialog from './mutationNoteDialog'
export default {
  name: 'wesReportDetectionOfGeneVariation',
  mixins: [obj.tablePaginationCommonData],
  components: {
    judgmentDialog,
    geneMergeDialog,
    importDialog,
    mutationNoteDialog
  },
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    matchCancer () {
      return this.$store.getters.getValue('matchCancer')
    }
  },
  data () {
    return {
      activeName: '1',
      geneType: '1',
      id: null,
      singleSample: true,
      selectedRows: [],
      tableData: [],
      judgmentDialogVisible: false,
      mutationNoteVisible: false, // 突变备注弹窗
      judgmentDialogData: {},
      geneMergeDialogVisible: false,
      geneMergeDialogData: {},
      importDialogVisible: false,
      importDialogType: ''
    }
  },
  methods: {
    getData () {
      this.tableData = []
      switch (this.activeName) {
        case '1':this.getSnvList()
          break
        case '2':this.getSvList()
          break
        case '3':this.getCnvList()
          break
        case '4':this.getHtSnvList()
          break
        case '5':this.getHtCnvList()
          break
        case '6':this.getQcResultList()
          break
        case '7':this.getQcHotResultList()
          break
      }
    },
    // 设置突变备注
    handleMutationNote (geneType) {
      if (this.selectedRows.length !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      this.$ajax({
        url: '/read/wesUnscramble/is_mutation_remark',
        data: {
          analysisRsId: this.analysisRsId || this.$route.query.oxym
        }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.geneType = geneType
          this.id = this.selectedRows[0].fid
          this.mutationNoteVisible = true
        }
      })
    },
    // 获取snv数据
    getSnvList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_snv_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                fid: v.fid,
                fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
                fcanComparison: v.fcanComparison,
                fisRead: v.fisRead,
                freport: v.freport,
                fisSelect: v.fisSelect,
                fcount: v.fcount,
                finProductCheckRange: v.finProductCheckRange,
                fdxAttr: v.fdxAttr,
                freadsPlot: v.freadsPlot,
                fgeneSymbol: v.fgeneSymbol,
                fchgvs: v.fchgvs,
                fphgvs: v.fphgvs,
                ffunction: v.ffunction,
                ftranscript: v.ftranscript,
                fexinId: v.fexinId,
                fcaseAf: v.fcaseAf,
                fcaseAltGoodSuptReads: v.fcaseAltGoodSuptReads,
                fcaseAltDep: v.fcaseAltDep,
                fcosmicId: v.fcosmicId,
                fchr: v.fchr,
                fstart: v.fstart,
                fstop: v.fstop,
                fref: v.fref,
                falt: v.falt,
                fcaseDep: v.fcaseDep,
                fautoInterpStatus: v.fautoInterpStatus,
                fmrdSelect: v.fmrdSelect,
                fpyCloneCluster: v.fpyCloneCluster,
                fprobeGc: v.fprobeGc,
                fvarLength: v.fvarLength,
                frepeatTag: v.frepeatTag,
                fctrlAd: v.fctrlAd,
                fctrlAf: v.fctrlAf,
                fctrlDp: v.fctrlDp,
                ftnbRank: v.ftnbRank,
                fchDb: v.fchDb,
                fisTnb: v.fisTnb,
                fchipCode: v.fchipCode
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取sv数据
    getSvList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_sv_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                fid: v.fid,
                fcanComparison: v.fcanComparison,
                fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
                fisRead: v.fisRead,
                freport: v.freport,
                fbelongToSvCheckRange: v.fbelongToSvCheckRange,
                fdxAttr: v.fdxAttr,
                freadsPlot: v.freadsPlot,
                fnumOfHot: v.fnumOfHot,
                ffusionType: v.ffusionType,
                fAlignGene: v.fAlignGene,
                fAlignTrancript: v.fAlignTrancript,
                fAlignExon: v.fAlignExon,
                ffusionExon: v.ffusionExon,
                ffusionInfo: v.ffusionInfo,
                fadjFreq: v.fadjFreq,
                ffreq: v.ffreq,
                fpos1: v.fpos1,
                fpos2: v.fpos2,
                fgene1: v.fgene1,
                ftranscript1: v.ftranscript1,
                fMaploc1: v.fMaploc1,
                fgene2: v.fgene2,
                ftranscript2: v.ftranscript2,
                fMaploc2: v.fMaploc2,
                fAltDepth: v.fAltDepth,
                fDepth: v.fDepth,
                fautoInterpStatus: v.fautoInterpStatus,
                fisSelect: v.fisSelect
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getCnvList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_cnv_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                fid: v.fid,
                fcanComparison: v.fcanComparison,
                fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
                fisRead: v.fisRead,
                freport: v.freport,
                finProductCheckRange: v.finProductCheckRange,
                freadsPlot: v.freadsPlot,
                fdxAttr: v.fdxAttr,
                fgene: v.fgene,
                fstate: v.fstate,
                fratio: v.fratio,
                favgRatio: v.favgRatio,
                ftumDepthOri: v.ftumDepthOri,
                fqual: v.fqual,
                ftranscript: v.ftranscript,
                fexon: v.fexon,
                flengthPercent: v.flengthPercent,
                fMaploc: v.fMaploc,
                fchr: v.fchr,
                fstart: v.fstart,
                fend: v.fend,
                fautoInterpStatus: v.fautoInterpStatus
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getHtSnvList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_htsnv_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                fid: v.fid,
                fcanComparison: v.fcanComparison,
                fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
                fisRead: v.fisRead,
                freport: v.freport,
                inProductCheckRange: v.inProductCheckRange,
                fmutationType: v.fmutationType,
                finReport: v.finReport,
                fpadCount: v.fpadCount,
                fgeneSymbol: v.fgeneSymbol,
                fchgvs: v.fchgvs,
                fphgvs: v.fphgvs,
                fzygosity: v.fzygosity,
                fexinId: v.fexinId,
                ffunction: v.ffunction,
                ftranscript: v.ftranscript,
                f1000gAf: v.f1000gAf,
                f1000gAsnAf: v.f1000gAsnAf,
                fexacAf: v.fexacAf,
                fexacEasAf: v.fexacEasAf,
                fgadAf: v.fgadAf,
                fgadEasAf: v.fgadEasAf,
                fpanelAlleleFreq: v.fpanelAlleleFreq,
                frsId: v.frsId,
                fhgmdPmId: v.fhgmdPmId,
                fclinVarID: v.fclinVarID,
                fclinVarSignificant: v.fclinVarSignificant,
                faDepth: v.faDepth,
                faRatio: v.faRatio,
                freadsPlot: v.freadsPlot
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getHtCnvList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_htcnv_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                fid: v.fid,
                fcanComparison: v.fcanComparison,
                fmutationRemark: v.fmutationRemark ? v.fmutationRemark.split(',') : [],
                isRead: v.isRead,
                inProductCheckRange: v.inProductCheckRange,
                freport: v.freport,
                fmutationType: v.fmutationType,
                freadsPlot: v.freadsPlot,
                gene: v.gene,
                state: v.state,
                exon: v.exon,
                ratio: v.favgRatio,
                transcript: v.transcript,
                fautoInterpretation: v.fautoInterpretation
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getQcResultList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_qc_result_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                fid: v.fid,
                resultTitle: v.resultTitle,
                caseResult: v.caseResult,
                controlResult: v.controlResult
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getQcHotResultList () {
      this.$ajax({
        url: '/read/wesUnscramble/get_hot_qc_result_list',
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        },
        loadingDom: '.table',
        loadingObject: {
          text: '加载中...'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          this.tableData = []
          this.selectedRows = []
          if (data.rows.length !== 0) {
            data.rows.forEach(v => {
              item = {
                id: v.fid,
                sarId: v.fsarId,
                gene: v.fgene,
                transcript: v.ftranscript,
                chgvs: v.fchgvs,
                phgvs: v.fphgvs,
                pos: v.fpos,
                chr: v.fchr,
                start: v.fstart,
                end: v.fend,
                dedupDep: v.fdedupDep,
                altdep: v.faltdep,
                depthCoefficient: v.fdepthCoefficient,
                hotClassify: v.fhotClassify,
                creator: v.fcreator,
                createTime: v.fcreateTime,
                updator: v.fupdator,
                updateTime: v.fupdateTime,
                isDelete: v.fisDelete
              }
              item.realData = JSON.parse(JSON.stringify(item))
              util.setDefaultEmptyValueForObject(item)
              this.tableData.push(item)
            })
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleGetDorH () {
      this.$ajax({
        url: '/read/wesUnscramble/auto_get_analysis_data',
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        method: 'get',
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          if (result.message) {
            this.$message({
              showClose: true,
              duration: 0,
              type: 'error',
              message: result.message
            })
          } else {
            this.$message.success('获取数据成功')
          }
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleReadsPicture (url) {
      window.open(url)
    },
    handleClick (tab, event) {
      this.currentPage = 0
      this.getData()
    },
    // 标记定制
    handleMark (type) {
      if (this.selectedRows.length <= 0) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let ids = this.selectedRows.map(v => v.fid)
      this.$ajax({
        url: '/read/unscramble/sign_select',
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        data: {
          fids: ids.join(','),
          type: type
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('标记成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 取消标记定制
    handleCancelMark (type) {
      if (this.selectedRows.length <= 0) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let ids = this.selectedRows.map(v => v.fid)

      this.$ajax({
        url: '/read/unscramble/not_sign_select',
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        data: {
          fids: ids.join(','),
          type: type
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('取消成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSelectAll (selection) {
      let self = this
      self.selectedRows = []
      selection.forEach(function (v, i) {
        self.handleRowClick(v)
      })
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      let index = this.selectedRows.indexOf(row)
      if (index > -1) {
        this.selectedRows.splice(index, 1)
        this.$refs.table.toggleRowSelection(row, false)
      } else {
        this.selectedRows.push(row)
        this.$refs.table.toggleRowSelection(row, true)
      }
    },
    handleContrast () {
      this.$ajax({
        url: '/read/wesUnscramble/compare',
        loadingDom: 'body',
        loadingObject: {
          text: '加载中...'
        },
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('比对成功')
          this.getData()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleImportData (type) {
      this.importDialogType = type
      this.importDialogVisible = true
    },
    handleImportDialogClose () {
      this.importDialogVisible = false
    },
    handleImportDialogConfirm () {
      this.importDialogVisible = false
      this.getData()
    },
    handleDownload (type) {
      let form = document.createElement('form')
      form.action = constants.JS_CONTEXT + '/read/unscramble/down_excel'
      form.method = 'post'
      form.id = 'form'
      let submitData = {
        analysisRsId: this.analysisRsId,
        type: type
      }
      for (let key in submitData) {
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = submitData[key]
        form.appendChild(input)
      }
      document.body.appendChild(form)
      form.submit()
      form.parentNode.removeChild(form)
      // this.$ajax({
      //   method: 'get',
      //   url: '/read/unscramble/downReadExcel',
      //   data: {
      //     analysisRsId: this.analysisRsId,
      //     type: type
      //   }
      // }).then(result => {
      //   let data = result.data
      //   if (data.code) {
      //     this.$message.error(data.message)
      //     return
      //   }
      //   // 创建一个新的url，此url指向新建的Blob对象
      //   let url = window.URL.createObjectURL(new Blob([data]))
      //   // 创建a标签，并隐藏改a标签
      //   let link = document.createElement('a')
      //   link.style.display = 'none'
      //   // a标签的href属性指定下载链接
      //   link.href = url
      //   // setAttribute() 方法添加指定的属性，并为其赋指定的值。
      //   link.setAttribute('download')
      //   document.body.appendChild(link)
      //   link.click()
      // })
    },
    handleSignReport () {
      let row = this.selectedRows
      let ids = []
      if (row.length === 0) {
        this.$message.error('请选择数据')
      } else {
        ids = row.map(v => {
          return v.fid
        })
        this.$ajax({
          url: '/read/unscramble/sign_report',
          data: {
            fids: ids.join(','),
            type: Number(this.activeName)
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('标记报出成功')
            this.getData()
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleSignNoReport () {
      let row = this.selectedRows
      let ids = []
      if (row.length === 0) {
        this.$message.error('请选择数据')
      } else {
        ids = row.map(v => {
          return v.fid
        })
        this.$ajax({
          url: '/read/unscramble/not_sign_report',
          data: {
            fids: ids.join(','),
            type: Number(this.activeName)
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('取消报出成功')
            this.getData()
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },
    handleAddForecastData () {
      if (this.selectedRows.length === 1) {
        let row = this.selectedRows[0]
        let data = {}
        switch (this.activeName) {
          case '1':
            data = {
              fid: row.fid,
              gene: row.fgeneSymbol,
              gSequenceChanges: row.fchgvs,
              gAminoAcidChanges: row.fphgvs,
              gRenferenceSequence: row.ftranscript,
              cancerKind: this.matchCancer
            }
            break
          case '2':
            data = {
              fid: row.fid,
              gene: row.fAlignGene,
              gSequenceChanges: row.fAlignGene,
              gAminoAcidChanges: '融合',
              gRenferenceSequence: row.fAlignTrancript,
              cancerKind: this.matchCancer
            }
            break
          case '3':
            data = {
              fid: row.fid,
              gene: row.fgene,
              gAminoAcidChanges: row.fstate === 'gain' ? '扩增' : '缺失',
              gRenferenceSequence: row.ftranscript,
              cancerKind: this.matchCancer
            }
            break
          case '4':
            data = {
              fid: row.fid,
              gene: row.fgeneSymbol,
              gSequenceChanges: row.fchgvs,
              gAminoAcidChanges: row.fphgvs,
              gRenferenceSequence: row.ftranscript,
              cancerKind: this.matchCancer
            }
            break
          case '5':
            data = {
              fid: row.fid,
              gene: row.gene,
              gSequenceChanges: row.exon + row.state === 'loss' ? 'Del' : 'Dup',
              gAminoAcidChanges: row.exon + row.state === 'loss' ? 'Del' : 'Dup',
              gRenferenceSequence: row.transcript,
              cancerKind: this.matchCancer
            }
            break
        }
        this.$store.commit({
          type: 'old/setValue',
          category: 'forecastData',
          forecastData: data
        })
        util.openNewPage('/business/unscramble/addForecastData')
      } else {
        this.$message.error('请选择一条数据')
      }
    },
    handleAddDXData () {
      if (this.selectedRows.length === 1) {
        let row = this.selectedRows[0]
        let data = {}
        switch (this.activeName) {
          case '2':
            data = {
              fid: row.fid,
              gGene: row.fAlignGene,
              gExon: row.ffusionExon,
              gFuseType: row.ffusionType,
              gMutationType: row.ffusionType,
              gDxAttribute: 'NA',
              gene1: row.fgene1,
              pos1: row.fpos1,
              transcript1: row.ftranscript1,
              gene2: row.fgene2,
              pos2: row.fpos2,
              transcript2: row.ftranscript2,
              fusionType: row.ffusionType,
              fusionInfo: row.ffusionInfo
            }
            break
          case '3':
            data = {
              fid: row.fid,
              gGene: row.fgene,
              gAminoAcidMutation: row.fstate === 'loss' ? '缺失' : '扩增',
              gReferenceSequence: row.ftranscript,
              gExon: row.fexon,
              gMutationType: row.fstate === 'loss' ? '缺失' : '扩增',
              gDxAttribute: 'NA',
              variationChr: row.fchr,
              variationStart: row.fstart,
              variationStop: row.fend
            }
            break
        }
        this.$store.commit({
          type: 'old/setValue',
          category: 'dxData',
          dxData: data
        })
        util.openNewPage('/business/unscramble/addDxData')
      } else {
        this.$message.error('请选择一条数据')
      }
    },
    handleJudgment () {
      let ids = []
      let valid = true
      let rows = this.selectedRows
      if (rows.length === 0) {
        this.$message.error('请选择数据')
      } else {
        valid = !rows.some(v => {
          return v.fgeneSymbol !== 'EGFR' || v.ffunction !== 'missense'
        })
        if (valid) {
          ids = rows.map(v => {
            return v.fid
          })
          this.judgmentDialogData = {
            ids: ids
          }
          this.judgmentDialogVisible = true
        } else {
          this.$message.error('所选突变不符合顺反式判断条件!')
        }
      }
    },
    handleJudgmentDialogConfirm () {
      this.judgmentDialogVisible = false
      this.getData()
    },
    handleJudgmentDialogClose () {
      this.judgmentDialogVisible = false
    },
    handleGeneMergeDialogConfirm () {
      this.geneMergeDialogVisible = false
      this.getData()
    },
    handleGeneMergeDialogClose () {
      this.geneMergeDialogVisible = false
    },
    handleGeneMerge () {
      let rows = this.selectedRows
      if (rows.length === 0) {
        this.$message.error('请选择数据')
      } else {
        let data = {}
        let gene = rows[0].realData.gene
        let status = rows[0].realData.state
        let transcript = rows[0].realData.transcript
        let avgRatio = rows[0].realData.ratio
        let valid = rows.every(v => {
          return v.gene === gene && v.state === status && v.transcript === transcript
        })
        if (valid) {
          data = {
            gene: gene,
            status: status,
            transcript: transcript,
            autoInterpretation: rows[0].realData.fautoInterpretation,
            avgRatio: avgRatio,
            geneticCnvIds: rows.map(v => v.fid),
            ...rows[0].realData
          }
          this.geneMergeDialogData = data
          this.geneMergeDialogVisible = true
        } else {
          this.$message.error('所选的突变要求Gene、Transcript、Status都一致')
        }
      }
    },
    handleAddGeneticMutation () {
      let rows = this.selectedRows
      if (rows.length !== 1) {
        this.$message.error('请选择数据')
      } else {
        let row = this.selectedRows[0]
        this.$store.commit({
          type: 'old/setValue',
          category: 'mutationsData',
          mutationsData: {
            fid: row.fid,
            gene: row.gene,
            nucleotideMutation: row.exon + row.state === 'loss' ? 'Del' : 'Dup',
            aminoAcidMutation: row.exon + row.state === 'loss' ? 'Del' : 'Dup',
            referenceSeq: row.transcript,
            mutationFunction: 'CNV'
          }
        })
        util.openNewPage('/business/unscramble/addGeneticMutation')
      }
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    height: 100%;
    .buttonGroup{
      padding: 0 20px;
      background-color: $page-bg;
      height: 50px;
      line-height: 50px;
    }
    .content{
      height: calc(100% - 50px);
      display: flex;
      >>>.el-tabs__item{
        width: 140px;
        text-align: center;
      }
      .buttonGroup{
        height: 45px;
        line-height: 45px;
        background-color: #fff;
      }
    }
  }
  .mutation-remark {
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
