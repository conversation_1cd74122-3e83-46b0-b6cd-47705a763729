<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="90%"
    @open="handleOpen">
    <el-form label-width="120px">
    <div v-if="visible" class="container">
        <div class="card-wrapper item">
          <h3 style="text-align: center; margin-bottom: 10px;">疾病背景-男</h3>
          <background-form ref="man" :pdata="formOfMan"></background-form>
          <references ref="referencesMan" :rids="formOfMan.sickRids"></references>
        </div>
        <div class="card-wrapper item">
          <h3 style="text-align: center; margin-bottom: 10px;">疾病背景-女</h3>
          <background-form ref="women" :pdata="formOfWomen"></background-form>
          <references ref="referencesWomen" :rids="formOfWomen.sickRids"></references>
        </div>
    </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import backgroundForm from './backgroundForm'
import References from '../common/references'

export default {
  mixins: [mixins.dialogBaseInfo],
  components: {
    backgroundForm,
    References
  },
  props: {
    geneticDescriptionId: {
      type: Number
    },
    sickRidsMan: {
      type: String
    },
    sickRidsWoman: {
      type: String
    }
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      title: '修改疾病背景',
      loading: false,
      formOfMan: {},
      formOfWomen: {}
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    async getData () {
      this.formOfMan = {}
      this.formOfWomen = {}
      const {code, data} = await this.$ajax({
        url: '/read/unscramble/get_genetic_function_description',
        data: {
          geneticGeneDescriptionId: this.geneticDescriptionId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data.dGeneticGeneDescriptionBean || {}
        this.formOfMan = {
          gene: info.gene,
          geneticMode: info.geneticMode,
          geneDescription: info.geneDescriptionMan,
          geneDescriptionEn: info.geneDescriptionManEn,
          geneBackground: info.geneBackgroundMan,
          geneBackgroundEn: info.geneBackgroundManEn,
          cancerClass: info.cancerClassMan,
          cancerClassEn: info.cancerClassManEn,
          priority: info.priorityMan,
          relatedDisease: info.relatedDiseaseMan,
          relatedDiseaseEn: info.relatedDiseaseManEn,
          sickRids: this.sickRidsMan
        }
        this.formOfWomen = {
          gene: info.gene,
          geneticMode: info.geneticMode,
          geneDescription: info.geneSescriptionWoman,
          geneDescriptionEn: info.geneSescriptionWomanEn,
          geneBackground: info.geneBackgroundWoman,
          geneBackgroundEn: info.geneBackgroundWomanEn,
          cancerClass: info.cancerClassWoman,
          cancerClassEn: info.cancerClassWomanEn,
          priority: info.priorityWoman,
          relatedDisease: info.relatedDiseaseWoman,
          relatedDiseaseEn: info.relatedDiseaseWomanEn,
          sickRids: this.sickRidsWoman
        }
      }
    },
    handleConfirm () {
      // this.loading = true
      console.log(this.$refs.man)
      let man = this.$refs.man.form
      let woman = this.$refs.women.form
      let ridOfman = this.$refs.referencesMan.tableData || []
      let ridOfWomen = this.$refs.referencesWomen.tableData || []
      let data = {
        sickRidsMan: ridOfman.map(v => v.rId).join(','),
        sickRidsWoman: ridOfWomen.map(v => v.rId).join(','),
        geneticGeneDescriptionId: this.geneticDescriptionId,
        gene: man.gene || woman.gene,
        geneticMode: this.formOfMan.geneticMode,
        geneDescriptionMan: man.geneDescription,
        geneDescriptionManEn: man.geneDescriptionEn,
        geneBackgroundMan: man.geneBackground,
        geneBackgroundManEn: man.geneBackgroundEn,
        cancerClassMan: man.cancerClass,
        cancerClassManEn: man.cancerClassEn,
        priorityMan: man.priority,
        relatedDiseaseMan: man.relatedDisease,
        relatedDiseaseManEn: man.relatedDiseaseEn,
        geneSescriptionWoman: woman.geneDescription,
        geneSescriptionWomanEn: woman.geneDescriptionEn,
        geneBackgroundWoman: woman.geneBackground,
        geneBackgroundWomanEn: woman.geneBackgroundEn,
        cancerClassWoman: woman.cancerClass,
        cancerClassWomanEn: woman.cancerClassEn,
        priorityWoman: woman.priority,
        relatedDiseaseWoman: woman.relatedDisease,
        relatedDiseaseWomanEn: woman.relatedDiseaseEn
      }
      this.$ajax({
        url: '/read/unscramble/save_genetic_function_description',
        loadingDom: '.table',
        data: {
          analysisRsId: this.analysisRsId,
          ...data
        }
      }).then(res => {
        if (res.code && res.code === this.SUCCESS_CODE) {
          this.$message.success('修改成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  display: flex;
  height: 55vh;
  overflow: auto;
  .card-wrapper {
    min-height: 50vh;
    width: 48%;
  }
  .item {
    min-height: 140vh;
    margin: 10px;
    padding: 10px;
    border: 1px solid #f2f2f2;
  }
}
</style>
