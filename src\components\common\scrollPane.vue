<template>
  <div :style="`height: ${scrollHeight}px;`" style="display: flex; align-items: center;">
    <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.native.prevent="handleScroll">
      <slot />
    </el-scrollbar>
  </div>
</template>

<script>

export default {
  name: 'ScrollPane',
  props: {
    scrollHeight: {
      type: Number,
      default: 40
    }
  },
  computed: {
    scrollWrapper () {
      return this.$refs.scrollContainer.$refs.wrap
    }
  },
  data () {
    return {
      left: 0
    }
  },
  methods: {
    handleScroll (e) {
      // wheelDelta：获取滚轮滚动方向，向上120，向下-120，但为常量，与滚轮速率无关
      // deltaY：垂直滚动幅度，正值向下滚动。电脑鼠标滚轮垂直行数默认值是3
      // wheelDelta只有部分浏览器支持，deltaY几乎所有浏览器都支持
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const $scrollWrapper = this.scrollWrapper
      // 0到scrollLeft为滚动区域隐藏部分
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4
    }
  }
}
</script>

<style scoped lang="scss">
  .scroll-container {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
  /deep/ .scroll-container .el-scrollbar__bar {
    bottom: 0;
  }
  /deep/ .scroll-container .el-scrollbar__wrap {
    overflow-x: hidden;
  }
</style>
