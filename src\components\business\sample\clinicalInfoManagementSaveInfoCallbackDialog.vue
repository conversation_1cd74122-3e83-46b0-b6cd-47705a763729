<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      :before-close="handleClose"
      v-drag-dialog
      title="钉钉信息推送"
      width="600px"
      @open="handleOpen"
    >
      <el-form ref="form" :model="form" inline label-width="120" :rules="rules">
        <el-row>
          <!--反馈原因-->
          <el-col>
            <el-form-item prop="type" label="异常信息分类：">
              <el-select v-model.trim="form.type" size="mini" multiple collapse-tags filterable clearable>
                <el-option
                  v-for="item in typeOptions"
                  :key="item"
                  :label="item"
                  :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!--备注-->
          <el-col>
            <el-form-item prop="note" label="备注：" :rules="noteRules">
              <el-input v-model="form.note" size="mini" maxlength="500" clearable placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'clinicalInfoManagementSaveInfoCallbackDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number
    }
  },
  computed: {
    noteRules () {
      let noteRules = []
      if (this.form.type.includes('其他')) {
        noteRules = [
          {required: true, message: '请输入备注', trigger: ['blur', 'change']}
        ]
      }
      return noteRules
    }
  },
  data () {
    return {
      loading: false,
      form: {
        type: [],
        note: ''
      },
      typeOptions: [
        '受试者编码', '出生年月', '性别', '血液采集时间', '组织采集时间', '组织采集部位', '临床诊断', '癌种', '转移灶/原发灶勾选不清晰', '送检单提供不清晰', '送检单提供错误', '未提供送检单', '其他'
      ],
      rules: {
        type: [
          {required: true, message: '请选择异常信息分类', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    handleConfirm () {
      const type = this.form.type || []
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/sample/basic/dingding_info_push',
            data: {
              fexceptionType: type.join(','),
              fexceptionNote: this.form.note,
              sampleBasicId: this.id
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('推送成功')
              this.visible = false
              this.$emit('callbackDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
>>>.el-dialog{
  margin: 10vh auto 20px !important;
}
</style>
