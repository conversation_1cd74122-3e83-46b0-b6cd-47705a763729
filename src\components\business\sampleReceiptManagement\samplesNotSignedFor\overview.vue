<template>
  <div>
    <div class="form-container">
      <div class="form">
        <div class="form-item">
          <label class="el-form-item__label">快递单号/订单编号：</label>
          <el-input
            ref="expressCodeInputRef"
            v-model.trim="expressCode"
            size="mini"
            :disabled="scanLoading"
            placeholder="请输入"
            @keyup.enter.native="handleScanCode"></el-input>
          <el-button size="mini" type="primary" :loading="scanLoading" @click="handleScanCode">确定</el-button>
        </div>
      </div>
    </div>
    <div style="margin: 10px 0;display: flex;justify-content: space-between;">
      <div>
        <template>
          <el-button v-if="$setAuthority('022001001', 'buttons')" type="primary" size="mini" :loading="preArrangementLoading" @click="handlePreArrangement">编号预排</el-button>
          <el-button v-if="$setAuthority('022001002', 'buttons')" type="primary" plain size="mini" @click="handleShowCamera">拍照记录</el-button>
          <el-button v-if="$setAuthority('022001003', 'buttons')" type="primary" plain size="mini" :loading="finishing" @click="handleFinish">完成签收</el-button>
          <el-button v-if="$setAuthority('022001004', 'buttons')" type="danger" plain size="mini" :loading="removeLoading" @click="handleRemove">移除数据</el-button>
        </template>
      </div>
    </div>
    <div class="table-wrap">
      <left-table
        ref="leftTableRef"
        class="left"
        :status-list="[0]"
        @select-change="handleSelectChange"/>
      <right-table
        ref="rightTableRef"
        class="right"
        :status-list="[0]"
        :search-params="{fexpressOrOrderCodeList: orderSelectDataIds}"/>
    </div>

    <camera-dialog
      :pvisible.sync="cameraDialogVisible"
      :codes="cameraDialogData"
      @dialogConfirmEvent="handleRightRefresh"/>
  </div>
</template>

<script>

// import xx form 'xxx'
import LeftTable from './LeftTable.vue'
import RightTable from './RightTable.vue'
import CameraDialog from './CameraDialog.vue'
export default {
  name: 'overview',
  components: {
    LeftTable,
    RightTable,
    CameraDialog
  },
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      expressCode: '',
      scanLoading: false,
      orderSelectDataIds: [],
      preArrangementLoading: false, // 预排loading
      removeLoading: false, // 移除loading
      finishing: false, // 完成签收loading
      cameraDialogVisible: false,
      cameraDialogData: ''
    }
  },
  methods: {
    handleSearch () {
      this.$refs.leftTableRef.search()
    },
    // 单号扫描
    handleScanCode () {
      if (!this.expressCode) {
        this.$message.warning('请输入单号')
        return
      }
      if (this.scanLoading) return
      this.scanLoading = true
      this.$ajax({
        url: '/experiment/sign/express_code_scanning',
        data: {
          fexpressCode: this.expressCode
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('扫描成功')
          this.expressCode = ''
          this.handleSearch()
        }
      }).finally(() => {
        this.scanLoading = false
        setTimeout(() => {
          this.$refs.expressCodeInputRef.focus()
        })
      })
    },
    handleSelectChange (ids) {
      this.orderSelectDataIds = ids
      if (!ids || ids.length === 0) {
        this.$refs.rightTableRef.resetTable()
      } else {
        this.$nextTick(() => {
          this.handleRightRefresh()
        })
      }
    },
    handleRightRefresh () {
      this.$refs.rightTableRef.search()
    },
    // 预排
    handlePreArrangement () {
      if (this.orderSelectDataIds.length === 0) {
        this.$message.warning('请选择要预排的订单')
        return
      }
      this.preArrangementLoading = true
      this.$ajax({
        url: '/experiment/sign/code_walkthrough',
        data: {
          forderCodeList: this.orderSelectDataIds
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('预排成功')
          this.$refs.rightTableRef.search()
        }
      }).finally(() => {
        this.preArrangementLoading = false
      })
    },
    // 移除
    async handleRemove () {
      if (this.orderSelectDataIds.length === 0) {
        this.$message.warning('请选择要移除的数据')
        return
      }
      await this.$confirm('是否确认对选择的数据进行取消签收？（取消签收后需要重新扫描录入）', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.removeLoading = true
      this.$ajax({
        url: '/experiment/sign/cancel_sign',
        data: {
          forderCodeList: this.orderSelectDataIds
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('移除成功')
          this.$refs.leftTableRef.search()
        }
      }).finally(() => {
        this.removeLoading = false
      })
    },
    // 拍照
    handleShowCamera () {
      if (this.orderSelectDataIds.length === 0) {
        this.$message.warning('请选择数据')
        return
      }
      this.cameraDialogData = this.orderSelectDataIds.join(',')
      this.cameraDialogVisible = true
    },
    // 完成签收
    async handleFinish () {
      if (this.orderSelectDataIds.length === 0) {
        this.$message.warning('请选择要完成签收的数据')
        return
      }
      await this.$confirm('是否确认对选择的数据进行标签签收？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.finishing = true
      this.$ajax({
        url: '/experiment/sign/finish_sign',
        data: {
          forderCodeList: this.orderSelectDataIds
        }
      }).then(res => {
        if (res.success) {
          this.$message.success('签收成功')
          this.$refs.leftTableRef.search()
        }
      }).finally(() => {
        this.finishing = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-container{
  display: flex;
  justify-content: space-between;
  //margin: 10px 0 0 0;
  //padding-bottom: 10px;
  //border-bottom: 1px solid #ccc;
  .form{
    display: flex;
    .form-item{
      display: flex;
      align-items: center;
      margin-right: 20px;
      label{
        flex-shrink: 0;
      }
    }
  }
}
.table-wrap{
  display: flex;
  .left{
    width: calc(30% - 20px);
    margin-right: 20px;
  }
  .right{
    width: 70%;
  }
}
</style>
