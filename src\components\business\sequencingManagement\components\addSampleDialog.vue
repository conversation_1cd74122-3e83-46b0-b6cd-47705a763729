<template>
  <el-dialog
    append-to-body
    title="添加样本"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="98vw"
    @open="handleOpen">
    <div class="dialog-content">
      <div class="result">
        <div class="left">
          <div style="display: flex; width: 100%;" class="search-form">
            <el-form
              ref="form"
              :model="form"
              :inline="true"
              size="mini"
              @keyup.enter.native="handleSearch">
              <el-form-item>
                <el-select v-model.trim="form.selectKey" style="width: 200px" clearable placeholder="请输入">
                  <el-option v-for="(item, index) in selectOptionList" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-model.trim="form.value" class="form-width" clearable placeholder="请输入"></el-input>
              </el-form-item>
<!--              <el-form-item label="文库修饰类型" prop="libModificationType">-->
<!--                <el-select v-model.trim="form.libModificationType" class="form-width" clearable placeholder="请输入">-->
<!--                  <el-option v-for="(item, index) in typeOptionList" :key="index" :label="item" :value="item"></el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
            </el-form>
            <div class="operate-btns-group">
              <el-button size="mini" plain type="primary" @click="handleSearch">查 询</el-button>
              <el-button size="mini" plain @click="handleReset">重 置</el-button>
            </div>
          </div>
          <div class="content">
            <el-table
              ref="table"
              :data="showTableData"
              :cell-style="handleRowStyle"
              class="table search-table"
              size="mini"
              border
              style="width: 660px"
              height="calc(60vh - 41px - 42px - 14px)"
              @select="handleSelectTable"
              @row-click="handleRowClick"
              @select-all="handleSelectAll">
              <el-table-column type="selection" min-width="55"></el-table-column>
              <el-table-column type="index" label="序号" min-width="50"></el-table-column>
              <el-table-column label="实验样本 " show-overflow-tooltip prop="sampleName" min-width="120"></el-table-column>
              <el-table-column label="项目名称" show-overflow-tooltip prop="projectName" min-width="120"></el-table-column>
              <el-table-column label="核酸/吉因加编号" show-overflow-tooltip prop="nucleateCodeText" min-width="160">
                <template #default="{ row }">
                  <span v-if="row.nucleateCodeText !== '查看样本详情'">{{row.nucleateCodeText}}</span>
                  <div v-else class="link" @click="handleDetail(row)">{{row.nucleateCodeText}}</div>
                </template>
              </el-table-column>
              <el-table-column label="原始样本名称" show-overflow-tooltip prop="oldSampleName" min-width="160"></el-table-column>
              <el-table-column label="文库修饰类型" show-overflow-tooltip prop="libModificationType" min-width="180"></el-table-column>
            </el-table>
            <div style="display: flex; align-items: center;font-size: 13px;">
              <el-pagination
                :page-sizes="pageSizes"
                :page-size="pageSize"
                :current-page.sync="currentPage"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper, slot"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange">
                <button @click="handleRefresh">
                  <icon-svg icon-class="icon-refresh"/>
                </button>
              </el-pagination>
            </div>
          </div>
        </div>
        <div class="center">
          <el-button type="primary" icon="el-icon-arrow-right" size="mini" @click="handleAddSampleData"></el-button>
          <el-button type="primary" icon="el-icon-arrow-left" size="mini" @click="handleRemoveSample"></el-button>
        </div>
        <div class="right">
          <vxe-table
            ref="tableRef"
            border
            resizable
            size="mini"
            auto-resize
            height="100%"
            :keep-source="false"
            show-overflow="tooltip"
            :data="checkedTableData"
            :edit-rules="validRules"
            :row-config="{isHover: true}"
            :checkbox-config="{trigger: 'row'}"
            :row-style="rowStyle"
            :valid-config="{autoPos: true, showMessage: true, msgMode: 'full'}"
            :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
          >
            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column type="seq" title="序号" width="60"></vxe-column>
            <vxe-column field="projectName" title="项目名称" min-width="120"></vxe-column>
            <vxe-column field="oldSampleName" title="原始样本名称" min-width="120"></vxe-column>
            <vxe-column field="sampleName" title="实验样本 " min-width="120"></vxe-column>
            <vxe-column field="nucleateCodeText" title="核酸/吉因加编号" min-width="130">
              <template #default="{ row }">
                <span v-if="row.nucleateCodeText !== '查看样本详情'">{{row.nucleateCodeText}}</span>
                <div v-else class="link" @click="handleDetail(row)">{{row.nucleateCodeText}}</div>
              </template>
            </vxe-column>
            <vxe-column field="libModificationType" title="文库修饰类型" min-width="180"></vxe-column>
            <vxe-column field="scheduledAmount" title="排单数据量/G" min-width="180" :edit-render="{name: '$input', props: {clearable: true}}"></vxe-column>
            <vxe-column field="process" title="工序类别" show-overflow min-width="180" :edit-render="{name: '$select', props: {clearable: true}}">
              <template #default="{ row }">
                <span>{{ row.process }}</span>
              </template>
              <template #edit="params">
                <vxe-select v-model.trim="params.row.process" clearable size="mini" @change="handleSetProcessFlow(params)">
                  <vxe-option v-for="item in processList" :key="item.value" :value="item.value" :label="item.label"></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
            <vxe-column field="makeDNB" title="makeDNB" min-width="180" :edit-render="{}">
              <template #default="{ row }">
                <span>{{ row.makeDNB }}</span>
              </template>
              <template #edit="params">
                <vxe-select v-model.trim="params.row.makeDNB" clearable transfer @change="handleSetMakeDNB(params)">
                  <vxe-option v-for="item in makeDNBMethods" :key="item" :value="item" :label="item"></vxe-option>
                </vxe-select>
              </template>
            </vxe-column>
            <vxe-column field="processFlowText" title="工序流程" show-overflow min-width="180"></vxe-column>
            <vxe-column title="操作" min-width="50">
              <template #default="{ row, rowIndex }">
                <el-button type="text" @click="handleEdit(rowIndex, row)">修改工序</el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>

    <edit-process-dialog
      :pvisible.sync="editProcessDialogVisible"
      :index-list="indexList"
      :type="libModificationType"
      :lib-type="libType"
      is-add-sample
      :current-work-flow-id="currentWorkFlowId"
      :current-process="currentProcess"
      :make-d-n-b="makeDNB"
      :length="fixData.length"
      @dialogConfirmEvent="handleEditProcess"
    />

    <set-process-config-dialog
      :pvisible.sync="setProcessConfigVisible"
      :sample-nums="sampleNums"
      :current-process="currentProcess"
      @dialogConfirmEvent="handleAddSample"
    />

    <add-sample-confirm-dialog
      :pvisible.sync="setAddSampleConfirmVisible"
      :sample-nums="sampleNums"
      @dialogConfirmEvent="handleAddSampleInfo"/>
  </el-dialog>
</template>

<script>
import mixins from '../../../../util/mixins'
import EditProcessDialog from './editProcessDialog'
import util, {awaitWrap} from '../../../../util/util'
import {
  addSample,
  getSampleList
} from '../../../../api/sequencingManagement/sequencingManagementApi'
import SetProcessConfigDialog from './setProcessConfigDialog'
import AddSampleConfirmDialog from './addSampleConfirmDialog'

export default {
  name: 'addSampleDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {AddSampleConfirmDialog, SetProcessConfigDialog, EditProcessDialog},
  props: {
    type: {
      type: String,
      default: '1'
    },
    currentProcess: {
      type: Number,
      default: 1
    }
  },
  computed: {
    showTableData () {
      return this.tableData.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    }
  },
  data () {
    const validator = ({ cellValue, row }) => {
      console.log(row)
      const process = row.process
      if ((process === '完整工序' || this.currentProcess === 5) && !cellValue) {
        return new Error('请选择makeDNB')
      }
    }
    return {
      loading: false,
      setAddSampleConfirmVisible: false,
      selectOption: [
        {label: '吉因加编号（查询外来文库）', value: 'fgeneCodeList'},
        {label: '文库编号（查询自建库-建库文库）', value: 'flibNumList'},
        {label: '杂交pooling文库（查询自建库-杂交文库）', value: 'fcrossLibNumList'},
        {label: 'pooling产物', value: 'fpoolingSampleNameList'},
        {label: '转化产物', value: 'ftransformSampleNameList'},
        {label: '环化产物', value: 'fcyclizeSampleNameList'}
      ],
      libType: '',
      libModificationType: '',
      indexList: [],
      selectConfig: {
        1: [0, 1, 2, 4],
        2: [0, 1, 2, 3],
        4: [0, 1, 2, 3, 4],
        5: [0, 1, 2, 3, 4, 5]
      },
      processList: [
        {label: '仅当前环节', value: '仅当前环节'},
        {label: '完整工序', value: '完整工序'}
      ],
      currentWorkFlowId: null,
      makeDNB: '',
      fixData: [],
      sampleNums: null,
      process: [
        {index: 1, process: 'pooling', explain: '', isDelete: 0},
        {index: 2, process: '转化', explain: 'illumina-未磷酸化”的才会执行转化', isDelete: 0},
        {index: 1, process: 'pooling', explain: '', isDelete: 1},
        {index: 4, process: '环化', explain: '“未环化”、且设置为“两步法”才会执行环化', isDelete: 0},
        {index: 5, process: 'makeDNB', explain: '', isDelete: 0}
      ],
      processFlow: {
        'illumina-未磷酸化': [0, 1, 2, 3, 4],
        'illumina-已磷酸化': [0, 3, 4],
        'MGI-未环化-单': [0, 3, 4],
        'MGI-未环化-双': [0, 3, 4],
        'illumina-已环化': [0, 4],
        'MGI-已环化': [0, 4]
      },
      typeOption: [
        'MGI-未环化-单',
        'MGI-未环化-双',
        'MGI-已环化',
        'illumina-未磷酸化',
        'illumina-已磷酸化',
        'illumina-已环化'
      ],
      selectTypeConfig: {
        1: [0, 1, 2, 3, 4, 5],
        2: [3],
        4: [0, 1, 4],
        5: [0, 1, 2, 4, 5]
      },
      typeOptionList: [],
      checkedTableData: [], // 选中样本数据
      selectOptionList: [],
      setProcessConfigVisible: false,
      editProcessDialogVisible: false,
      form: {
        selectKey: 'fgeneCodeList', // 测序平台
        value: '', // 测序类型
        libModificationType: ''
      },
      formSubmit: {},
      makeDNBMethods: ['一步法', '两步法'],
      title: '导入任务',
      tableData: [],
      validRules: {
        scheduledAmount: [
          // 必填 ，类型数字
          { required: true, message: '排单数据量/G' },
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'change'}
        ],
        process: [
          // 必填 ，类型数字
          { required: true, message: '请选择工序类别' }
        ],
        makeDNB: [
          {
            validator: validator, trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.form = {
          fworkflowType: this.currentProcess,
          selectKey: 'fgeneCodeList', // 测序平台
          value: '', // 测序类型
          libModificationType: ''
        }
        this.tableData = []
        this.checkedTableData = []
        this.selectedRows.clear()
        const config = this.selectConfig[this.currentProcess] || []
        const typeConfig = this.selectTypeConfig[this.currentProcess] || []
        this.selectOptionList = config.map(v => this.selectOption[v])
        this.typeOptionList = typeConfig.map(v => this.typeOption[v])
      })
    },
    changeCellEvent  (params) {
      const $table = this.$refs.tableRef
      if ($table) {
        $table.updateStatus(params)
      }
    },
    handleSearch () {
      if (this.form.value && !this.form.selectKey) {
        this.$message.error('请选择样本类别')
        return
      }
      if (!this.form.value && !this.form.libModificationType) {
        this.$message.error('请选择筛选条件')
        return
      }
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    handleReset () {
      this.form = {
        selectKey: 'fgeneCodeList', // 测序平台
        value: '', // 测序类型
        libModificationType: ''
      }
      this.tableData = []
      this.selectedRows.clear()
      // this.handleSearch()
    },
    rowStyle  ({ row }) {
      if (row.isHeightLight) {
        return {
          backgroundColor: '#eee'
        }
      }
      return null
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    // 查询样本
    async getData () {
      this.clearMap()
      const params = {
        [this.formSubmit.selectKey]: util.setGroupData(this.formSubmit.value).split('、'),
        fworkflowType: this.currentProcess
      }
      let {res} = await awaitWrap(getSampleList(params, {loadingDom: '.search-table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        this.selectedRows.clear()
        this.totalPage = data.length || 0
        this.tableData = []
        data.forEach(v => {
          v = v || {}
          const item = {
            id: v.fsampleName,
            sampleName: v.fsampleName,
            projectName: v.fprojectName,
            nucleateCodeText: this.handleSetGeneCode(v), // 吉因加编号,
            geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
            geneInfo: {
              fgeneCode: v.fgeneCode,
              fnucleateCode: v.fnucleateCode
            },
            // nucleateCodeText: v.fnucleateCode || v.fgeneCode,
            nucleateCode: v.fnucleateCode,
            flibType: v.flibType,
            flibVolume: v.flibVolume,
            libModificationType: v.flibModificationType,
            currentWorkFlowId: v.fcurrentWorkFlowId,
            fgeneCode: v.fgeneCode,
            forderCode: v.forderCode,
            fdataSize: v.fdataSize,
            oldSampleName: v.foldSampleName,
            flibConcentration: v.flibConcentration,
            forderSampleName: v.forderSampleName,
            fisLibTestComplete: v.fisLibTestComplete,
            fisLibTestCompleteNum: v.fisLibTestCompleteNum,
            fsourceType: v.fsourceType,
            processFlow: ''
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        this.tableData.filter(v => {
          return this.checkedTableData.findIndex(info => info.id !== v.id) === -1
        })
      }
    },
    // 添加样本
    handleAddSampleData () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择样本！')
        return
      }
      this.setProcessConfigVisible = true
      this.sampleNums = this.selectedRows.size
    },
    // 回显样本配置
    handleAddSample (data = {}) {
      const ids = [...this.selectedRows.keys()]
      // 左侧移除样本
      this.tableData = this.tableData.filter(item => !ids.includes(item.id))
      // 更新样本总数
      this.totalPage = this.tableData.length || 0
      // 右侧添加样本
      const sampleList = [...this.selectedRows.values()].map(v => {
        v.process = data.processType
        v.makeDNB = data.makeDNB
        v.area = data.area
        v = this.handleSetFlow(v)
        return v
      })
      // 判断是否有重复
      const sampleNames = sampleList
        .filter(v => this.checkedTableData.findIndex(info => info.id === v.id) !== -1)
        .map(v => v.sampleName) || []
      if (sampleNames.length > 0) {
        this.$message.warning(`样本${sampleNames.join(',')}已选中，已自动跳过`)
      }
      this.checkedTableData = this.checkedTableData.concat(
        sampleList.filter(v => this.checkedTableData.findIndex(info => info.id === v.id) === -1))
      // 排序
      this.checkedTableData = this.handleSort(this.checkedTableData) || []
      this.selectedRows.clear()
    },
    //
    handleSort (list = []) {
      let array = []
      const map = new Map()
      list.forEach(v => {
        map.has(v.projectName + v.libModificationType)
          ? map.get(v.projectName + v.libModificationType).push({ ...v })
          : map.set(v.projectName + v.libModificationType, [{ ...v }])
      })
      let isHeightLight = true
      map.forEach((list, index) => {
        isHeightLight = !isHeightLight
        list = list.map(v => {
          return {isHeightLight: isHeightLight, ...v}
        })
        array = array.concat(list)
      })
      return array
    },
    // 设置工序流程
    handleSetFlow (item) {
      let flows = this.processFlow[item.libModificationType]
      if (item.process === '仅当前环节') {
        flows = [this.currentProcess - 1]
      }
      // 根据文库修饰类型设置默认makeDNB 两步法
      /**
       * 设置makeDNB 两步法
       */
      const notOneLibType = [
        'PCR-free文库',
        '10xATAC单细胞文库',
        '甲基化文库-WGBS',
        '甲基化文库-RRBS',
        '甲基化文库-其他',
        'C4 v2.0单细胞文库',
        'C4 ATAC单细胞文库'
      ]
      if (notOneLibType.some(lib => item.flibType.includes(lib))) {
        item.makeDNB = '两步法'
      }

      /**
       * 设置makeDNB 一步法
       */
      const notOneModificationLibType = ['illumina-已环化', 'MGI-已环化']
      if (notOneModificationLibType.includes(item.libModificationType)) {
        item.makeDNB = '两步法'
      }

      // 环化环节必须两步法
      if (this.currentProcess === 4 && item.makeDNB !== '两步法') {
        this.$message.warning('环化环节必须两步法')
        item.makeDNB = '两步法'
      }
      const result = flows.map((v, i) => {
        const flow = JSON.parse(JSON.stringify(this.process[v]))
        flow.disabled = 0
        if (item.process !== '仅当前环节') {
          if (flow.index === 4 && item.makeDNB === '一步法') {
            flow.isDelete = 1
          }
          let index = flow.index * 1
          if (i !== 0 && index === 1) {
            index = 3
          }
          // 根据当前环节设置工序
          // 原始样本 更具当前环节设置删除， 产物根据产物环节设置
          if (index < this.currentProcess) {
            flow.disabled = 1
          }
        }
        return flow
      })
      // 设置工序流程
      if (result.length >= 0) {
        item.processFlowText = result.filter(v => !v.isDelete && !v.disabled).map(v => v.process).join('-')
        item.processIndex = result.filter(v => !v.isDelete && !v.disabled).map(v => v.index)
        item.deleteIndex = result.map((v, i) => {
          let value = ''
          if (v.isDelete) {
            value = i + 1
          }
          return value
        }).filter(v => v)
        item.processFlow = result
      }
      return item
    },
    // 移除样本
    handleRemoveSample () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length < 1) {
        this.$message.error('请选择数据')
        return
      }
      const ids = selectRecords.map(v => v.id)
      // 右侧移除样本
      this.checkedTableData = this.checkedTableData.filter(item => !ids.includes(item.id))
      // 左侧添加样本
      const sampleList = selectRecords.map(v => {
        return {
          id: v.id,
          sampleName: v.sampleName,
          projectName: v.projectName,
          nucleateCode: v.nucleateCode,
          flibType: v.flibType,
          libModificationType: v.libModificationType,
          flibConcentration: v.flibConcentration,
          currentWorkFlowId: v.realData.currentWorkFlowId,
          fgeneCode: v.fgeneCode,
          forderCode: v.forderCode,
          nucleateCodeText: v.nucleateCodeText,
          geneCodeValue: v.geneCodeValue,
          oldSampleName: v.oldSampleName,
          forderSampleName: v.forderSampleName,
          fdataSize: v.fdataSize,
          flibVolume: v.flibVolume,
          fisLibTestComplete: v.realData.fisLibTestComplete,
          fisLibTestCompleteNum: v.realData.fisLibTestCompleteNum,
          fsourceType: v.realData.fsourceType,
          processFlow: '',
          realData: v.realData
        }
      })
      this.tableData = this.tableData.concat(sampleList)
      // 更新样本总数
      this.totalPage = this.tableData.length || 0
    },
    // 修改表格信息
    fixTableData (changeSamples) {
      const data = this.tableData
      // 获取选中行索引
      const indexList = changeSamples
        .map(v => this.tableData.findIndex(data => v._X_ROW_KEY === data._X_ROW_KEY))
      indexList.forEach((v, index) => {
        data[v] = changeSamples[index]
      })
      this.$set(this, 'tableData', data)
    },
    // 设置makeDNB
    handleSetMakeDNB (params) {
      // 根据文库修饰类型设置默认makeDNB 两步法
      /**
       * 设置makeDNB 两步法
       */
      const notOneLibType = [
        'PCR-free文库',
        '10xATAC单细胞文库',
        '甲基化文库-WGBS',
        '甲基化文库-RRBS',
        '甲基化文库-其他',
        'C4 v2.0单细胞文库',
        'C4 ATAC单细胞文库'
      ]
      if (notOneLibType.some(lib => params.row.flibType.includes(lib))) {
        this.$message.error(`当前实验样本所属文库类型${params.row.flibType}，不能转一步法，请检查！`)
        params.row.makeDNB = '两步法'
      }

      /**
       * 设置makeDNB 一步法
       */
      const notOneModificationLibType = ['illumina-已环化', 'MGI-已环化']
      if (notOneModificationLibType.includes(params.row.libModificationType)) {
        this.$message.error(`当前实验样本为已环化文库，不能转一步法，请检查！`)
        params.row.makeDNB = '两步法'
      }
      this.changeCellEvent(params)
      let processFlow = params.row.processFlow || []
      processFlow = processFlow.map(v => {
        if (v.index === 4) {
          v.isDelete = params.row.makeDNB !== '两步法'
        }
        return v
      })
      params.row.processFlow = processFlow
      params.row.processFlowText = processFlow.filter(v => !v.isDelete).map(v => v.process).join('-')
      params.row.processIndex = processFlow.filter(v => !v.isDelete).map(v => v.index).join(',')
      params.row.deleteIndex = processFlow.map((v, i) => {
        let value = ''
        if (v.isDelete) {
          value = i + 1
        }
        return value
      }).filter(v => v)
    },
    // 设置流程
    handleSetProcessFlow (params) {
      this.changeCellEvent(params)
      params.row = this.handleSetFlow(params.row)
    },
    // 编辑工序
    handleEdit (index, row) {
      if (row.process === '仅当前环节') {
        this.$message.error('工序类别为仅当前环节不能编辑')
        return
      }
      this.index = index
      this.editProcessDialogVisible = true
      this.libModificationType = row.libModificationType
      this.libType = row.libraryType
      this.currentWorkFlowId = row.currentWorkFlowId
      this.indexList = row.deleteIndex
      this.makeDNB = row.makeDNB
      this.fixData = this.tableData.filter(v => {
        return v.name === row.name && v.libModificationType === row.libraryType
      })
    },
    handleEditProcess (processInfo) {
      const {isSync} = processInfo
      let data = this.checkedTableData[this.index] || {}
      if (!isSync) {
        data = this.handleFixProcess(data, processInfo)
        this.tableData[this.index] = data
        return
      }
      // fixData 每条数据都需要同步
      this.checkedTableData = this.checkedTableData.map((v) => {
        if (v.name === data.name && v.libModificationType === data.libModificationType) {
          v = this.handleFixProcess(v, processInfo)
        }
        return v
      })
    },
    handleFixProcess (data, processInfo) {
      data.libModificationType = processInfo.type
      data.makeDNB = processInfo.makeDNB
      data.processFlow = processInfo.processFlow
      data.processFlowText = processInfo.processFlow.filter(v => !v.isDelete && !v.disabled).map(v => v.process).join('-')
      data.processIndex = processInfo.processFlow.filter(v => !v.isDelete && !v.disabled).map(v => v.index)
      data.deleteIndex = processInfo.processFlow.map((v, i) => {
        let value = ''
        if (v.isDelete) {
          value = i + 1
        }
        return value
      }).filter(v => v)
      return data
    },
    // // 回显工序修改
    // handleEditProcess (processInfo) {
    //
    //   const data = this.checkedTableData[this.index]
    //   data.libraryType = processInfo.type
    //   data.makeDNB = processInfo.makeDNB
    //   data.processText = processInfo.processFlow.filter(v => !v.isDelete && !v.disabled).map(v => v.process).join('-')
    //   data.processIndex = processInfo.processFlow.filter(v => !v.isDelete && !v.disabled).map(v => v.index)
    //   data.deleteIndex = processInfo.processFlow.filter(v => !v.isDelete).map(v => v.index)
    //   this.checkedTableData[this.index] = data
    // },
    // 校验表单
    handleValidForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.$message.error('表单存在错误，请检查')
          }
        })
      })
    },
    // 设置请求参数
    setParams () {
      const sampleList = []
      this.checkedTableData.forEach(v => {
        const options = {
          '1': 'PA0001',
          '2': 'PA0002',
          '3': 'PA0003',
          '4': 'PA0004'
        }
        const item = {
          fprojectName: v.projectName,
          fsampleName: v.sampleName,
          fnucleateCode: v.nucleateCode,
          fgeneCode: v.fgeneCode,
          forderCode: v.forderCode,
          forderSampleName: v.forderSampleName,
          flibModificationType: v.libModificationType,
          flibType: v.flibType,
          flibConcentration: v.flibConcentration,
          foldSampleName: v.oldSampleName,
          fdataVolumeSize: v.scheduledAmount,
          flibVolume: v.flibVolume,
          fdataSize: v.fdataSize,
          fworkflowType: v.process === '仅当前环节' ? 0 : 1,
          fmakeDnbType: v.makeDNB === '一步法' ? 1 : 2,
          fprocessFlow: v.processIndex,
          fisLibTestComplete: v.realData.fisLibTestComplete,
          fisLibTestCompleteNum: v.realData.fisLibTestCompleteNum,
          fsourceType: v.realData.fsourceType,
          fcurrentWorkFlowId: v.realData.currentWorkFlowId,
          fproductionArea: options[v.area]
        }
        sampleList.push(item)
      })
      return {
        tsAddSampleVOList: sampleList
      }
    },
    // 添加前检验
    async handleConfirm () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length < 1) {
        this.$message.error('请选择数据')
        return
      }
      const $table = this.$refs.tableRef
      const errMap = await $table.fullValidate(true)
      if (errMap) {
        this.$message.error('请检查表格内容是否有误')
        return
      }
      this.sampleNums = selectRecords.length
      this.setAddSampleConfirmVisible = true
    },
    // 添加样本
    async handleAddSampleInfo (data) {
      const options = {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      }
      const params = this.setParams()
      this.loading = true
      const {res} = await awaitWrap(addSample({
        ...params,
        ftaskCode: data.taskCode,
        fseqType: data.sequencingType,
        ftaskProductionArea: options[data.area]
      }, this.currentProcess))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('添加成功')
        this.visible = false
        this.$emit('dialogConfirmEvent')
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-upload-dragger {
  width: 100%;
  height: 100%;
  padding: 20px;
}
/deep/ .el-upload {
  width: 100%;
  height: 100%;
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 10px 0;
}
.tips {
  margin: 3px 0;
  font-size: 12px;
  font-weight: 300;
  color: #ca5353;
}
.dialog-content {
  max-height: 60vh;
  overflow: auto;
  padding: 0 10px;
}
.result {
  display: flex;
  height: 60vh;
  overflow: hidden;
  .left {
    width: 660px;
  }
  .center {
    display: flex;
    align-items: center;
    width: 120px;
  }
  .right {
    width: calc(100% - 720px);
  }
}

.center {
  display: flex;
  /deep/ .el-button {
    padding: 14px 0;
    min-width: 30px;
  }
}
</style>
