import { myAjax } from '@/util/ajax'

/**
 * 获取加测列表
 * @param data
 * @param options
 * @returns {*}
 */
export function getAddTestList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/cos_add_testing/get_add_testing_list',
    data: data,
    ...options
  })
}

export function exportAddTestInfo (data, options = {}) {
  return myAjax({
    url: '/experiment/cos_add_testing/export_add_testing_list',
    responseType: 'blob',
    data: data,
    ...options
  })
}

export function auditAddTestInfo (data, options = {}) {
  return myAjax({
    url: '/experiment/cos_add_testing/audit_or_relinquish',
    data: data,
    ...options
  })
}

export function auditCheckAudit (data, options = {}) {
  return myAjax({
    url: '/experiment/cos_add_testing/check_order',
    data: data,
    ...options
  })
}
