// 基础路由（登录、首页、错误页等）
export default [
  {
    path: '/',
    name: 'HelloWorld',
    component: () => import('@/components/HelloWorld')
  },
  {
    path: '/500',
    component: () => import('@/components/common/errorPage.vue')
  },
  {
    path: '/login',
    component: () => import('@/components/system/login.vue')
  },
  {
    path: '/test',
    meta: {
      title: '个性化MRD样本监控'
    },
    component: () => import('@/components/business/test/index.vue')
  }
]
