<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="导入物流信息"
      width="500px"
      @open="handleOpen">
      <el-form v-model.trim="form" label-width="120px" class="form">
        <el-form-item label="请上传物流信息:">
          <el-upload
            ref="upload"
            :on-success="handleOnSuccess"
            :on-error="handleOnError"
            :auto-upload="false"
            :headers="headers"
            :limit="1"
            :file-list="fileList"
            :before-upload="handleBeforeUpload"
            :action="uploadUrl">
            <el-button size="mini" plain>上传文件</el-button>
            <div slot="tip" class="el-upload__tip">支持格式：Excel文件（xlsx、xls），单个文件不能超过2MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item>
<!--          <el-button type="text" @click="handleDownloadTemplate">导入模版下载</el-button>-->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
          :loading="submitBtnLoading"
          size="mini"
          type="primary"
          @click="handleConfirm">提交导入</el-button>
      </div>
    </el-dialog>
    <error-info-dialog :pvisible.sync="errorInfoVisible" :table-data="errorInfo"></error-info-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util from '../../../util/util'
import errorInfoDialog from './errorInfoDialog'
export default {
  name: 'ImportLogisticsInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    errorInfoDialog
  },
  data () {
    let loginId = util.getSessionInfo('loginId')
    let id = loginId ? util.decryptBase64(loginId) : ''
    return {
      uploadUrl: constants.JS_CONTEXT + '/sample/return/import_sample_return',
      headers: {
        'user-id': id
      },
      form: {},
      errorInfoVisible: false,
      errorInfo: [],
      submitBtnLoading: false,
      fileList: [],
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      this.tableData = []
      this.fileList = []
    },
    handleConfirm () {
      this.$refs.upload.submit()
    },
    // 错误提示处理
    handleErrorBacInfo (data) {
      let errorData = data.errorDTOs || []
      let completedSampleNums = data.completedSampleNums || []
      if (errorData.length > 0) {
        this.errorInfoVisible = true
        this.errorInfo = errorData
      }
      if (completedSampleNums.length > 0) {
        this.$confirm('部分样例编号的物流单号已存在，导入将会覆盖原有结果，确定导入？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleImportRepetition(data)
        })
      }
    },
    // 继续导入
    handleImportRepetition (data) {
      this.$ajax({
        url: `/sample/return/backfill_courier_Numer`,
        data: data.sampleReturns
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.visible = false
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 下载模板
    handleDownloadTemplate () {
      let form = document.createElement('form')
      form.action = constants.JS_CONTEXT + '/sample/return/download_sample_return_template'
      form.method = 'get'
      form.id = 'form'
      let submitData = {
        orderType: 3
      }
      for (let key in submitData) {
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = submitData[key]
        form.appendChild(input)
      }
      document.body.appendChild(form)
      form.submit()
      form.parentNode.removeChild(form)
    },
    // 提交成功回调
    handleOnSuccess (res) {
      this.submitBtnLoading = true
      if (res && res.code === this.SUCCESS_CODE) {
        let data = res.data || {}
        this.submitBtnLoading = false
        this.visible = false
        this.$emit('dialogConfirmEvent')
        this.$confirm(`数据导入成功,已导入${data}条数据`, '提示', {
          showConfirmButton: false,
          showCancelButton: false,
          type: 'success'
        })
      } else {
        let data = res.data || {}
        this.handleErrorBacInfo(data)
      }
      this.submitBtnLoading = false
      this.$refs.upload.clearFiles()
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      this.loading = true
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > 1024 * 1024 * 2) {
          this.loading = false
          this.$message.error('文件大小超过限制，无法上传')
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传xlsx或xls文件')
        return false
      }
    },
    // 提交失败回调
    handleOnError () {
      this.loading = false
      this.$message.error('上传出现错误')
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-form-item {
  margin-bottom: 0;
}
</style>
