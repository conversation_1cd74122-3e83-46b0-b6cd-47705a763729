<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="匹配癌种"
      width="30%">
      <div>
        <el-form ref="form" :model="form" label-width="80px" size="mini">
          <el-form-item label="大众癌种" prop="ordinaryCancer">
            <el-select v-model="form.ordinaryCancer" filterable placeholder="请选择" style="width: 100%">
              <el-option
                :key="item.value"
                :label="item.label"
                :value="item.value"
                v-for="item in ordinaryCancerList">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="癌种" prop="cancer">
            <el-cascader
              :show-all-levels="false"
              :options="cancerList"
              v-model="form.cancer"
              :props ="{lazy: true,checkStrictly: true, lazyLoad: getChildrenCancer}"
              style="width: 100%"
              clearable
              placeholder="请选择"
            ></el-cascader>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'selectCancerDialog',
  components: {},
  props: ['pvisible'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.getCancerList(0, '')
        this.getCancerList(1)
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      form: {
        ordinaryCancer: '',
        cancer: []
      },
      cancerList: [],
      ordinaryCancerList: [],
      rules: {
        ordinaryCancer: [],
        cancer: []
      }
    }
  },
  methods: {
    getCancerList (type, parentId) {
      this.$ajax({
        method: 'get',
        url: '/read/unscramble/list_cancers_by_cancerclass',
        data: {
          cancerClass: type,
          parentId: parentId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          let item = {}
          if (type === 0) {
            this.ordinaryCancerList = []
          } else {
            this.cancerList = []
          }
          data.forEach(v => {
            item = {
              id: v.cancerTypeId,
              label: v.cancerTypeName,
              value: v.cancerTypeName + '-' + v.cancerTypeId,
              children: []
            }
            if (type === 0) {
              this.ordinaryCancerList.push(item)
            } else {
              this.cancerList.push(item)
            }
          })
        }
      })
    },
    getChildrenCancer (node, resolve) {
      if (node.level && !node.isLeaf) {
        this.$ajax({
          method: 'get',
          url: '/read/unscramble/list_cancers_by_cancerclass',
          data: {
            cancerClass: 1,
            parentId: node.data.id
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let data = result.data
            let item = {}
            let list = []
            data.forEach(v => {
              item = {
                id: v.cancerTypeId,
                label: v.cancerTypeName,
                value: v.cancerTypeName + '-' + v.cancerTypeId,
                leaf: !v.haveLowerElement
              }
              list.push(item)
            })
            resolve(list)
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        resolve([])
      }
    },
    handleConfirm () {
      let data = []
      if (this.form.cancer.length === 0 && !this.form.ordinaryCancer) {
        this.$message.error('请选择')
      } else {
        if (this.form.cancer.length === 0) {
          data = this.form.ordinaryCancer.split('-')
          this.$emit('selectCancerDialogConfirmEvent', {cancer: data[0], cancerId: Number(data[1])})
          this.$refs.form.resetFields()
        } else {
          data = (this.form.cancer[this.form.cancer.length - 1]).split('-')
          this.$emit('selectCancerDialogConfirmEvent', {cancer: data[0], cancerId: Number(data[1])})
          this.$refs.form.resetFields()
        }
      }
    },
    handleClose () {
      this.$emit('selectCancerDialogCloseEvent')
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style scoped>

</style>
