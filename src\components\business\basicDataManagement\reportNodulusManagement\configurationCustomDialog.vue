<template>
  <el-dialog
    title="批量配置客户信息"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1000px"
    @open="handleOpen">
    <div>
      <el-form :model="form" :rules="rules" ref="form" size="mini">
        <el-form-item prop="cancer">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate"  @change="handleCheckAllChange">全部癌种</el-checkbox>
          <el-checkbox-group v-model="form.cancer" @change="handleCheckedChange">
            <el-row>
              <el-col v-for="(item, index) in cancers" :key="index" :span="3">
                <el-checkbox :label="item.id">
                  <div style="width: 90px; line-height: 13px">
                    <tooltips :txt-info="item.cancer"></tooltips>
                  </div>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="操作">
          <el-radio-group v-model="form.type">
            <el-radio :label="1" border>新增</el-radio>
            <el-radio :label="0" border>删除</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="customer">
          <div class="precondition-left">
            <div id="info" class="title precondition-left-item">客户</div>
            <div :key="index"
                 v-for="(item, index) in customerList"
                 style="display: flex;justify-content: space-between;"
                 class="precondition-left-item">
              <div>
                {{ item.customData }}
              </div>
              <div>
                <i style="color: red" class="el-icon-delete" @click="handleDeleteCustomer(index)"></i>
              </div>
            </div>
            <div>
              <el-button
                v-if="showMore"
                icon="el-icon-plus"
                size="mini"
                class="precondition-left-item more"
                @click="handleCustomer"></el-button>
              <div v-else>
                <el-select v-model.trim="customCode"
                           v-lazyLoad="lazyOption"
                           :remote-method="handleSearchCustomer"
                           size="mini"
                           filterable
                           remote
                           clearable
                           style="width: 100%"
                           placeholder="请选择（按客户编码搜索）"
                           @visible-change="handleVisible">
                  <el-option
                    :key="index + '客户'"
                    :label="item.customData"
                    :value="item.customCode"
                    v-for="(item, index) in customeres">
                  </el-option>
                </el-select>
                <el-button
                  size="mini"
                  icon="el-icon-check"
                  class="precondition-left-item more"
                  @click="handleSave"></el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div style="display: flex; align-items: center">
            <el-upload
              ref="upload"
              :file-list="fileList"
              :action="uploadUrl"
              :on-error="handleError"
              :before-upload="handleBeforeUpload"
              :on-change="handleChange"
              :on-success="handleOnSuccess"
              :show-file-list="false"
            >
              <el-button type="primary" plain size="mini">导入客户信息</el-button>
            </el-upload>
            <el-button type="text" size="mini" @click="handleDownload">下载模版</el-button>
          </div>

        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
    </span>
  </el-dialog>
</template>
<script>
import mixins from '../../../../util/mixins'
import constants from '../../../../util/constants'
import {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import {downloadReportNodulesTemplate, saveCancerReportCustomer} from '../../../../api/system/reportNodulesApi'
/**
 * 配置自定义对话框组件
 * 使用mixins引入对话框基础信息的混合项
 * 主要包含配置信息的表单、癌种列表、客户列表的管理功能
 */
export default {
  name: 'configurationCustomDialog',
  mixins: [mixins.dialogBaseInfo],
  data () {
    return {
      // 加载状态
      loading: false,
      // 全选复选框的状态
      checkAll: true,
      // 不确定状态，用于复选框部分选中时
      isIndeterminate: false,
      // 表单数据对象
      form: {
        type: 1,
        cancer: [],
        customer: []
      },
      // 癌种列表
      cancers: [],
      // 客户列表
      customerList: [],
      // 客户信息数组
      customeres: [],
      // 上传文件列表
      fileList: [],
      // 上传接口地址
      uploadUrl: constants.JS_CONTEXT + '/system/summaryReport/import_excel',
      // 客户编码
      customCode: '',
      // 是否展示更多操作按钮
      showMore: true,
      // 懒加载选项配置
      lazyOption: {
        loadData: this.getCustomerInfo,
        distance: 20,
        scrollBody: '.el-scrollbar__wrap',
        callback: (fn) => {
          this.$once('hook:beforeDestroy', () => fn())
        }
      },
      // 表单验证规则
      rules: {
        cancer: [
          {required: true, message: '请输入癌种', trigger: 'blur'}
        ],
        customer: [
          {required: true, message: '请输入客户', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    /**
     * 打开对话框时初始化癌种列表
     */
    async handleOpen () {
      await this.getCancer()
      this.handleCheckAllChange(true)
      // 清空客户缓存
      this.customerList = []
      this.showMore = true
      this.checkAll = true
      // 操作类型缓存
      this.form.type = 1
    },
    /**
     * 处理全选复选框的变化
     * @param {boolean} val 复选框的新状态
     */
    handleCheckAllChange (val) {
      this.form.cancer = val ? this.cancers.map(v => v.id) : []
      this.isIndeterminate = false
    },
    /**
     * 处理癌种复选框选中项变化
     * @param {Array} value 当前选中的癌种id列表
     */
    handleCheckedChange (value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.cancers.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cancers.length
    },
    /**
     * 获取癌种列表
     */
    async getCancer () {
      let {code, data} = await this.$ajax({
        url: '/system/summaryReport/get_cancer_report_infos',
        data: {
          page: {
            current: 1,
            size: 10000
          }
        },
        loadingDom: '.dialog-wrapper'
      })
      if (code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.cancers = []
        rows.forEach(v => {
          this.cancers.push(v)
        })
      }
    },
    /** ****************上传下载***************************/
    // 下载模版
    async handleDownload () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadReportNodulesTemplate(this.type))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    /**
     * 处理导入失败的错误
     */
    handleError () {
      this.loading = false
      this.$message.error('导入失败')
    },
    /**
     * 处理文件上传前的验证
     * @param {Object} file 上传的文件对象
     */
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.loading = false
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.loading = false
        return false
      }
    },
    /**
     * 处理文件上传后的变化
     * @param {Object} file 上传的文件对象
     * @param {Array} fileList 上传文件列表
     */
    async handleChange (file, fileList) {
      if (fileList.length < 2) return
      const message = '一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？'
      const {err} = await awaitWrap(this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      err ? fileList.pop() : fileList.shift()
    },
    /**
     * 处理文件上传成功
     * @param {Object} res 上传结果
     */
    handleOnSuccess (res) {
      if (res && res.code === this.SUCCESS_CODE) {
        this.$refs.upload.clearFiles()
        this.customerList = [...(res.data || []), ...this.customerList]
        // 去重
        this.customerList = [...new Set(this.customerList)]
      } else {
        this.$refs.upload.clearFiles()
      }
    },
    /** ***********客户相关操作******************/
    /**
     * 新增客户
     */
    handleCustomer () {
      this.showMore = false
    },
    /**
     * 删除客户
     * @param {number} index 客户在列表中的索引
     */
    handleDeleteCustomer (index) {
      this.customerList.splice(index, 1)
    },
    /**
     * 保存客户
     */
    handleSave () {
      if (!this.customCode) {
        this.$message.error('请选择客户保存')
        return
      }
      let customer = this.customeres.find(v => v.customCode === this.customCode)
      let include = this.customerList.some(v => v.customCode === this.customCode)
      if (include) {
        this.$message.error(customer.customData + '已存在客户列表中')
        return
      }
      this.showMore = true
      this.customerList.push(customer)
      this.customCode = ''
    },
    handleVisible (visible) {
      if (visible) this.handleSearchCustomer()
    },
    /**
     * 远程搜索客户信息
     * @param {string} customCode 客户编码
     */
    handleSearchCustomer (customCode) {
      this.currentPage = 1
      this.customeres = []
      this.getCustomerInfo(customCode)
    },
    /**
     * 获取客户信息
     * @param {string} customCode 客户编码
     */
    async getCustomerInfo (customCode = '') {
      this.loadingCustom = true
      try {
        let {code, data = {}} = await this.$ajax({
          url: '/system/summaryReport/get_custom_info_by_code',
          data: {
            fcustomerCode: customCode,
            page: {
              current: this.currentPage,
              size: 10000
            }
          },
          loadingDom: '.precondition-left'
        })
        if (code === this.SUCCESS_CODE) {
          this.currentPage += 1
          let result = data.records || []
          this.customeres = [...this.customeres, ...result]
        }
      } finally {
        this.loadingCustom = false
      }
    },
    async handleConfirm () {
      if (this.customerList.length === 0) {
        this.$message.error('请选择客户')
        return
      }
      if (this.form.cancer.length === 0) {
        this.$message.error('请选择癌种')
        return
      }
      this.loading = true
      const {res} = await awaitWrap(saveCancerReportCustomer({
        fsummaryConfigCanerIdList: this.form.cancer,
        fcustomerCodeList: this.customerList.map(v => v.customCode),
        foperateType: this.form.type
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('保存成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
      this.loading = false
    }
  }
}
</script>
<style scoped lang="scss">
.precondition-left {
  width: 100%;
  height: 220px;
  border: 1px solid #eee;
  overflow: auto;

  .precondition-left-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    border-bottom: 1px solid #eee;
  }

  .more {
    cursor: pointer;
    color: $color;
    text-align: center;
    font-size: 28px;
    width: 100%;
  }
}
</style>
