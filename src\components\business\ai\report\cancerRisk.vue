<template>
  <div class="card-wrapper" style="margin-top: 20px;">
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column prop="gene" label="基因" width="180"></el-table-column>
      <el-table-column prop="tumorTypes" label="癌症类型"></el-table-column>
      <el-table-column prop="cancerRisk" label="普通人群患癌风险">
        <template slot-scope="scope">
          <div v-html="scope.row.cancerRisk"></div>
        </template>
      </el-table-column>
      <el-table-column prop="mutantCancerRisk" label="突变携带者患癌风险1">
        <template slot-scope="scope">
          <div v-html="scope.row.mutantCancerRisk"></div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_cancer_risk_list',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data || []
        this.tableData = []
        rows.forEach(v => {
          let item = {
            id: v.fid,
            gene: v.gene,
            tumorTypes: v.tumorTypes,
            cancerRisk: v.cancerRisk,
            mutantCancerRisk: v.mutantCancerRisk
          }
          if (item.cancerRisk === '增加') {
            item.cancerRisk = '↑'
          }
          if (item.mutantCancerRisk === '增加') {
            item.mutantCancerRisk = '↑'
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped></style>
