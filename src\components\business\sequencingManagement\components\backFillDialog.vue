<template>
  <el-dialog
    title="回填结果"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="98vw"
    @open="handleOpen">
    <component ref="backFillInfo" :is="activeComponents" :info="tableData" :type="type"></component>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import backFillResultInfoPooling from './backFillResultInfoPooling'
import backFillResultInfoCyclization from './backFillResultInfoCyclization'
import backFillResultInfoMakeDNB from './backFillResultInfoMakeDNB'
import backFillResultInfoTranslation from './backFillResultInfoTranslation'
import mixins from '../../../../util/mixins'
import {awaitWrap} from '../../../../util/util'
import {getSampleInfo} from '../../../../api/sequencingManagement/sequencingManagementApi'
export default {
  name: 'backFillDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    backFillResultInfoPooling,
    backFillResultInfoCyclization,
    backFillResultInfoMakeDNB,
    backFillResultInfoTranslation
  },
  props: {
    type: {
      type: Number,
      default: 1
    },
    taskIds: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      activeComponents: 'backFillResultInfoPooling',
      loading: false,
      tableData: []
    }
  },
  methods: {
    handleOpen () {
      const components = {
        1: 'backFillResultInfoPooling',
        2: 'backFillResultInfoTranslation',
        3: 'backFillResultInfoCyclization',
        4: 'backFillResultInfoMakeDNB'
      }
      this.activeComponents = components[this.type]
      this.getTableData()
    },
    async getTableData () {
      let {res} = await awaitWrap(getSampleInfo({ftaskIds: this.taskIds}, this.type))
      if (res && res.code === this.SUCCESS_CODE) {
        console.log('获取结果')
        this.tableData = res.data
      }
    },
    async handleConfirm () {
      try {
        this.loading = true
        const result = await this.$refs.backFillInfo.handleSubmit()
        if (result) {
          this.$emit('dialogConfirmEvent')
          this.visible = false
        }
      } catch (e) {
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>

</style>
