<template>
  <div>
    <el-dialog
      :title="title" :visible.sync="visible" :close-on-click-modal="false"
      :before-close="handleClose" width="30%" @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="mini">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="物料编码" prop="code">
              <el-input v-model.trim="form.code" :disabled="disabled" clearable placeholder="请输入" maxlength="30"></el-input>
            </el-form-item>
          </el-col>
         <el-col :span="24">
           <el-form-item label="品类" prop="name">
             <el-input v-model.trim="form.name" clearable placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item label="规格" prop="spec">
             <el-input v-model.trim="form.spec" clearable placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item label="单位" prop="unit">
             <el-input v-model.trim="form.unit" clearable placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="24">
           <el-form-item label="版本" prop="version">
             <el-input v-model.trim="form.version" clearable placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="24">
           <el-form-item label="库存量" prop="stock">
             <el-input v-model.number="form.stock" clearable placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item label="物料类别" prop="categoryId">
             <el-select v-model="form.categoryId" clearable placeholder="请选择" class="selectWidth">
               <el-option
                 :key="item.value"
                 :label="item.label"
                 :value="item.value"
                 v-for="item in categoryList">
               </el-option>
             </el-select>
           </el-form-item>
         </el-col>
         <el-col :span="12">
           <el-form-item label="高低值" prop="upDownSize">
             <el-select v-model="form.upDownSize" :disabled="disabled" clearable placeholder="请选择" class="selectWidth">
               <el-option
                 :key="item.value"
                 :label="item.label"
                 :value="item.value"
                 v-for="item in upDownSizeList">
               </el-option>
             </el-select>
           </el-form-item>
         </el-col>
          <el-col :span="12">
            <el-form-item label="批次号" prop="batch">
              <el-input v-model.trim="form.batch" clearable placeholder="请输入" maxlength="20"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期" prop="expirationDate">
              <el-date-picker
                v-model="form.expirationDate"
                clearable
                style="width: 100%"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价" prop="unitPrice">
              <el-input v-model.trim="form.unitPrice" clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'materialsSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    pdata: {
      type: Object,
      default () {
        return {}
      }
    }
    // pname: {
    //   type: String,
    //   default: '',
    //   required: true
    // },
    // pspec: {
    //   type: String,
    //   default: '',
    //   required: true
    // },
    // punit: {
    //   type: String,
    //   default: '',
    //   required: true
    // },
    // pversion: {
    //   type: String,
    //   default: '',
    //   required: false
    // },
    // pstock: {
    //   type: Number,
    //   default: 0,
    //   required: true
    // },
    // pcategoryId: {
    //   type: [Number, String],
    //   default: '',
    //   required: true
    // },
    // pupDownSize: {
    //   type: [Number, String],
    //   default: '',
    //   required: true
    // },
    // ptype: {
    //   type: Number,
    //   default: 0,
    //   required: true
    // },
    // pfid: {
    //   type: Number,
    //   default: null,
    //   required: false
    // }
  },
  data () {
    return {
      loading: false,
      type: '',
      title: '新增物料',
      disabled: false,
      form: {
        fid: null,
        code: '',
        name: '',
        spec: '',
        unit: '',
        version: '',
        stock: '',
        expiryDate: '',
        batch: '',
        categoryId: '',
        upDownSize: '',
        unitPrice: ''
      },
      categoryList: [],
      upDownSizeList: [
        {
          label: '低值',
          value: 0
        },
        {
          label: '高值',
          value: 1
        },
        {
          label: '易耗',
          value: 2
        }
      ],
      rules: {
        code: [
          {required: true, message: '请输入物料编码', trigger: 'blur'}
          // {pattern: /([a-zA-Z]+)-(\d+)$/, message: '格式错误', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '请输入品类', trigger: 'blur'}
        ],
        spec: [
          {required: true, message: '请输入规格', trigger: 'blur'}
        ],
        unit: [
          {required: true, message: '请输入单位', trigger: 'blur'}
        ],
        unitPrice: [
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '只能输入数字，包含两位小数', trigger: 'blur'}
        ],
        stock: [
          {required: true, message: '请输入库存数量', trigger: 'blur'},
          {pattern: /^\d+$/, message: '格式错误', trigger: 'blur'}
        ],
        categoryId: [
          {required: true, message: '请选择物料类别', trigger: ['blur', 'change']}
        ],
        upDownSize: [
          {required: true, message: '请选择高低值', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    // 获取分类列表
    getCategoryList () {
      this.$ajax({
        url: '/materials/get_category_list',
        method: 'get',
        data: {
          type: this.type
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.categoryList = []
          result.data.forEach(v => {
            this.categoryList.push({
              label: v.name,
              value: v.categoryId
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.type = this.pdata.type
        this.getCategoryList()
        // this.handleSetCodeRules()
        if (this.pdata.fid) {
          this.title = '编辑物料'
          this.disabled = true
        } else {
          this.title = '新增物料'
          this.disabled = false
        }
        this.form = {
          code: this.pdata.code,
          fid: this.pdata.fid,
          name: this.pdata.name,
          spec: this.pdata.spec,
          unit: this.pdata.unit,
          version: this.pdata.version,
          stock: this.pdata.stock,
          categoryId: this.pdata.categoryId,
          upDownSize: this.pdata.upDownSize,
          batch: this.pdata.ratificationIssue,
          unitPrice: this.pdata.unitPrice,
          expirationDate: this.pdata.expirationDate === '-' ? '' : this.pdata.expirationDate
        }
      })
    },
    // // 设置物料编码格式
    // handleSetCodeRules () {
    //   // 编辑物料且物料格式校验存在
    //   if (this.pdata.fid && this.rules.code.length > 1) {
    //     this.rules.code.pop()
    //   } else if (this.rules.code.length <= 1) { // 新增物料且物料格式校验不存在
    //     this.rules.code.push(
    //       {pattern: /([a-zA-Z]+)-(\d+)$/, message: '格式错误', trigger: 'blur'}
    //     )
    //   }
    // },

    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/materials/add_materials',
            data: {
              fcode: this.form.code,
              fid: this.form.fid,
              fname: this.form.name,
              fspec: this.form.spec,
              funit: this.form.unit,
              fversion: this.form.version,
              fstock: this.form.stock,
              fcategoryId: this.form.categoryId,
              fupDownSize: this.form.upDownSize,
              ftype: this.type,
              funitPrice: this.form.unitPrice,
              fratificationIssue: this.form.batch,
              fexpirationDate: this.form.expirationDate
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('materialsSaveDialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  >>>.el-dialog__body{
    padding: 10px;
  }
  .selectWidth{
    width: 100%
  }
</style>
