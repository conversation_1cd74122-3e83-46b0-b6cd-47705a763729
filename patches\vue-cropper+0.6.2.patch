diff --git a/node_modules/vue-cropper/dist/index.js b/node_modules/vue-cropper/dist/index.js
index a33dc4a..c7d9b3d 100644
--- a/node_modules/vue-cropper/dist/index.js
+++ b/node_modules/vue-cropper/dist/index.js
@@ -1,2 +1,2 @@
-!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("vue-cropper",[],e):"object"==typeof exports?exports["vue-cropper"]=e():t["vue-cropper"]=e()}(self,(()=>(()=>{var t={593:(t,e,r)=>{(t.exports=r(252)(!1)).push([t.id,'\n.vue-cropper[data-v-33936806] {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  user-select: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  direction: ltr;\n  touch-action: none;\n  text-align: left;\n  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");\n}\n.cropper-box[data-v-33936806],\n.cropper-box-canvas[data-v-33936806],\n.cropper-drag-box[data-v-33936806],\n.cropper-crop-box[data-v-33936806],\n.cropper-face[data-v-33936806] {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  user-select: none;\n}\n.cropper-box-canvas img[data-v-33936806] {\n  position: relative;\n  text-align: left;\n  user-select: none;\n  transform: none;\n  max-width: none;\n  max-height: none;\n}\n.cropper-box[data-v-33936806] {\n  overflow: hidden;\n}\n.cropper-move[data-v-33936806] {\n  cursor: move;\n}\n.cropper-crop[data-v-33936806] {\n  cursor: crosshair;\n}\n.cropper-modal[data-v-33936806] {\n  background: rgba(0, 0, 0, 0.5);\n}\n.cropper-crop-box[data-v-33936806] {\n  /*border: 2px solid #39f;*/\n}\n.cropper-view-box[data-v-33936806] {\n  display: block;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  user-select: none;\n}\n.cropper-view-box img[data-v-33936806] {\n  user-select: none;\n  text-align: left;\n  max-width: none;\n  max-height: none;\n}\n.cropper-face[data-v-33936806] {\n  top: 0;\n  left: 0;\n  background-color: #fff;\n  opacity: 0.1;\n}\n.crop-info[data-v-33936806] {\n  position: absolute;\n  left: 0px;\n  min-width: 65px;\n  text-align: center;\n  color: white;\n  line-height: 20px;\n  background-color: rgba(0, 0, 0, 0.8);\n  font-size: 12px;\n}\n.crop-line[data-v-33936806] {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  opacity: 0.1;\n}\n.line-w[data-v-33936806] {\n  top: -3px;\n  left: 0;\n  height: 5px;\n  cursor: n-resize;\n}\n.line-a[data-v-33936806] {\n  top: 0;\n  left: -3px;\n  width: 5px;\n  cursor: w-resize;\n}\n.line-s[data-v-33936806] {\n  bottom: -3px;\n  left: 0;\n  height: 5px;\n  cursor: s-resize;\n}\n.line-d[data-v-33936806] {\n  top: 0;\n  right: -3px;\n  width: 5px;\n  cursor: e-resize;\n}\n.crop-point[data-v-33936806] {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  opacity: 0.75;\n  background-color: #39f;\n  border-radius: 100%;\n}\n.point1[data-v-33936806] {\n  top: -4px;\n  left: -4px;\n  cursor: nw-resize;\n}\n.point2[data-v-33936806] {\n  top: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: n-resize;\n}\n.point3[data-v-33936806] {\n  top: -4px;\n  right: -4px;\n  cursor: ne-resize;\n}\n.point4[data-v-33936806] {\n  top: 50%;\n  left: -4px;\n  margin-top: -3px;\n  cursor: w-resize;\n}\n.point5[data-v-33936806] {\n  top: 50%;\n  right: -4px;\n  margin-top: -3px;\n  cursor: e-resize;\n}\n.point6[data-v-33936806] {\n  bottom: -5px;\n  left: -4px;\n  cursor: sw-resize;\n}\n.point7[data-v-33936806] {\n  bottom: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: s-resize;\n}\n.point8[data-v-33936806] {\n  bottom: -5px;\n  right: -4px;\n  cursor: se-resize;\n}\n@media screen and (max-width: 500px) {\n.crop-point[data-v-33936806] {\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    opacity: 0.45;\n    background-color: #39f;\n    border-radius: 100%;\n}\n.point1[data-v-33936806] {\n    top: -10px;\n    left: -10px;\n}\n.point2[data-v-33936806],\n  .point4[data-v-33936806],\n  .point5[data-v-33936806],\n  .point7[data-v-33936806] {\n    display: none;\n}\n.point3[data-v-33936806] {\n    top: -10px;\n    right: -10px;\n}\n.point4[data-v-33936806] {\n    top: 0;\n    left: 0;\n}\n.point6[data-v-33936806] {\n    bottom: -10px;\n    left: -10px;\n}\n.point8[data-v-33936806] {\n    bottom: -10px;\n    right: -10px;\n}\n}\n',""])},252:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=function(t,e){var r,o=t[1]||"",i=t[3];if(!i)return o;if(e&&"function"==typeof btoa){var n=(r=i,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),s=i.sources.map((function(t){return"/*# sourceURL="+i.sourceRoot+t+" */"}));return[o].concat(s).concat([n]).join("\n")}return[o].join("\n")}(e,t);return e[2]?"@media "+e[2]+"{"+r+"}":r})).join("")},e.i=function(t,r){"string"==typeof t&&(t=[[null,t,""]]);for(var o={},i=0;i<this.length;i++){var n=this[i][0];"number"==typeof n&&(o[n]=!0)}for(i=0;i<t.length;i++){var s=t[i];"number"==typeof s[0]&&o[s[0]]||(r&&!s[2]?s[2]=r:r&&(s[2]="("+s[2]+") and ("+r+")"),e.push(s))}},e}},614:(t,e,r)=>{var o=r(593);"string"==typeof o&&(o=[[t.id,o,""]]);r(723)(o,{hmr:!0,transform:void 0,insertInto:void 0}),o.locals&&(t.exports=o.locals)},723:(t,e,r)=>{var o,i,n={},s=(o=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=o.apply(this,arguments)),i}),a=function(t,e){return e?e.querySelector(t):document.querySelector(t)},c=function(t){var e={};return function(t,r){if("function"==typeof t)return t();if(void 0===e[t]){var o=a.call(this,t,r);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(t){o=null}e[t]=o}return e[t]}}(),h=null,p=0,u=[],l=r(947);function f(t,e){for(var r=0;r<t.length;r++){var o=t[r],i=n[o.id];if(i){i.refs++;for(var s=0;s<i.parts.length;s++)i.parts[s](o.parts[s]);for(;s<o.parts.length;s++)i.parts.push(x(o.parts[s],e))}else{var a=[];for(s=0;s<o.parts.length;s++)a.push(x(o.parts[s],e));n[o.id]={id:o.id,refs:1,parts:a}}}}function d(t,e){for(var r=[],o={},i=0;i<t.length;i++){var n=t[i],s=e.base?n[0]+e.base:n[0],a={css:n[1],media:n[2],sourceMap:n[3]};o[s]?o[s].parts.push(a):r.push(o[s]={id:s,parts:[a]})}return r}function g(t,e){var r=c(t.insertInto);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=u[u.length-1];if("top"===t.insertAt)o?o.nextSibling?r.insertBefore(e,o.nextSibling):r.appendChild(e):r.insertBefore(e,r.firstChild),u.push(e);else if("bottom"===t.insertAt)r.appendChild(e);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=c(t.insertAt.before,r);r.insertBefore(e,i)}}function v(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var e=u.indexOf(t);e>=0&&u.splice(e,1)}function m(t){var e=document.createElement("style");if(void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce){var o=r.nc;o&&(t.attrs.nonce=o)}return w(e,t.attrs),g(t,e),e}function w(t,e){Object.keys(e).forEach((function(r){t.setAttribute(r,e[r])}))}function x(t,e){var r,o,i,n;if(e.transform&&t.css){if(!(n="function"==typeof e.transform?e.transform(t.css):e.transform.default(t.css)))return function(){};t.css=n}if(e.singleton){var s=p++;r=h||(h=m(e)),o=y.bind(null,r,s,!1),i=y.bind(null,r,s,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(r=function(t){var e=document.createElement("link");return void 0===t.attrs.type&&(t.attrs.type="text/css"),t.attrs.rel="stylesheet",w(e,t.attrs),g(t,e),e}(e),o=A.bind(null,r,e),i=function(){v(r),r.href&&URL.revokeObjectURL(r.href)}):(r=m(e),o=O.bind(null,r),i=function(){v(r)});return o(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;o(t=e)}else i()}}t.exports=function(t,e){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(e=e||{}).attrs="object"==typeof e.attrs?e.attrs:{},e.singleton||"boolean"==typeof e.singleton||(e.singleton=s()),e.insertInto||(e.insertInto="head"),e.insertAt||(e.insertAt="bottom");var r=d(t,e);return f(r,e),function(t){for(var o=[],i=0;i<r.length;i++){var s=r[i];(a=n[s.id]).refs--,o.push(a)}for(t&&f(d(t,e),e),i=0;i<o.length;i++){var a;if(0===(a=o[i]).refs){for(var c=0;c<a.parts.length;c++)a.parts[c]();delete n[a.id]}}}};var C,b=(C=[],function(t,e){return C[t]=e,C.filter(Boolean).join("\n")});function y(t,e,r,o){var i=r?"":o.css;if(t.styleSheet)t.styleSheet.cssText=b(e,i);else{var n=document.createTextNode(i),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(n,s[e]):t.appendChild(n)}}function O(t,e){var r=e.css,o=e.media;if(o&&t.setAttribute("media",o),t.styleSheet)t.styleSheet.cssText=r;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(r))}}function A(t,e,r){var o=r.css,i=r.sourceMap,n=void 0===e.convertToAbsoluteUrls&&i;(e.convertToAbsoluteUrls||n)&&(o=l(o)),i&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var s=new Blob([o],{type:"text/css"}),a=t.href;t.href=URL.createObjectURL(s),a&&URL.revokeObjectURL(a)}},947:t=>{t.exports=function(t){var e="undefined"!=typeof window&&window.location;if(!e)throw new Error("fixUrls requires window.location");if(!t||"string"!=typeof t)return t;var r=e.protocol+"//"+e.host,o=r+e.pathname.replace(/\/[^\/]*$/,"/");return t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(t,e){var i,n=e.trim().replace(/^"(.*)"$/,(function(t,e){return e})).replace(/^'(.*)'$/,(function(t,e){return e}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(n)?t:(i=0===n.indexOf("//")?n:0===n.indexOf("/")?r+n:o+n.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")}))}}},e={};function r(o){var i=e[o];if(void 0!==i)return i.exports;var n=e[o]={id:o,exports:{}};return t[o](n,n.exports,r),n.exports}r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var o={};return(()=>{"use strict";r.r(o),r.d(o,{VueCropper:()=>h,default:()=>u});var t=function(){var t=this,e=t._self._c;return e("div",{ref:"cropper",staticClass:"vue-cropper",on:{mouseover:t.scaleImg,mouseout:t.cancelScale}},[t.imgs?e("div",{staticClass:"cropper-box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"cropper-box-canvas",style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"}},[e("img",{ref:"cropperImg",attrs:{src:t.imgs,alt:"cropper-img"}})])]):t._e(),t._v(" "),e("div",{staticClass:"cropper-drag-box",class:{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping},on:{mousedown:t.startMove,touchstart:t.startMove}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.cropping,expression:"cropping"}],staticClass:"cropper-crop-box",style:{width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"}},[e("span",{staticClass:"cropper-view-box"},[e("img",{style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"},attrs:{src:t.imgs,alt:"cropper-img"}})]),t._v(" "),e("span",{staticClass:"cropper-face cropper-move",on:{mousedown:t.cropMove,touchstart:t.cropMove}}),t._v(" "),t.info?e("span",{staticClass:"crop-info",style:{top:t.cropInfo.top}},[t._v(t._s(t.cropInfo.width)+" × "+t._s(t.cropInfo.height))]):t._e(),t._v(" "),t.fixedBox?t._e():e("span",[e("span",{staticClass:"crop-line line-w",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),e("span",{staticClass:"crop-line line-a",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),e("span",{staticClass:"crop-line line-s",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),e("span",{staticClass:"crop-line line-d",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point1",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,1,1)},touchstart:function(e){return t.changeCropSize(e,!0,!0,1,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point2",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point3",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,2,1)},touchstart:function(e){return t.changeCropSize(e,!0,!0,2,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point4",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point5",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point6",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,1,2)},touchstart:function(e){return t.changeCropSize(e,!0,!0,1,2)}}}),t._v(" "),e("span",{staticClass:"crop-point point7",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),e("span",{staticClass:"crop-point point8",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,2,2)},touchstart:function(e){return t.changeCropSize(e,!0,!0,2,2)}}})])])])};function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=new Array(e);r<e;r++)o[r]=t[r];return o}function i(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o,i,n,s,a=[],c=!0,h=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=n.call(r)).done)&&(a.push(o.value),a.length!==e);c=!0);}catch(t){h=!0,i=t}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(h)throw i}}return a}}(t,r)||function(t,r){if(t){if("string"==typeof t)return e(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?e(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t._withStripped=!0;var n={};n.getData=function(t){return new Promise((function(e,r){var o={};(function(t){var e=null;return new Promise((function(r,o){if(t.src)if(/^data\:/i.test(t.src))e=function(t){t=t.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var e=atob(t),r=e.length,o=new ArrayBuffer(r),i=new Uint8Array(o),n=0;n<r;n++)i[n]=e.charCodeAt(n);return o}(t.src),r(e);else if(/^blob\:/i.test(t.src)){var i=new FileReader;i.onload=function(t){e=t.target.result,r(e)},function(t,e){var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="blob",r.onload=function(t){var e;200!=this.status&&0!==this.status||(e=this.response,i.readAsArrayBuffer(e))},r.send()}(t.src)}else{var n=new XMLHttpRequest;n.onload=function(){if(200!=this.status&&0!==this.status)throw"Could not load image";e=n.response,r(e),n=null},n.open("GET",t.src,!0),n.responseType="arraybuffer",n.send(null)}else o("img error")}))})(t).then((function(t){o.arrayBuffer=t,o.orientation=function(t){var e,r,o,i,n,s,a,c,h,p=new DataView(t),u=p.byteLength;if(255===p.getUint8(0)&&216===p.getUint8(1))for(c=2;c<u;){if(255===p.getUint8(c)&&225===p.getUint8(c+1)){s=c;break}c++}if(s&&(r=s+10,"Exif"===function(t,e,r){var o,i="";for(o=e,r+=e;o<r;o++)i+=String.fromCharCode(t.getUint8(o));return i}(p,s+4,4)&&((i=18761===(n=p.getUint16(r)))||19789===n)&&42===p.getUint16(r+2,i)&&(o=p.getUint32(r+4,i))>=8&&(a=r+o)),a)for(u=p.getUint16(a,i),h=0;h<u;h++)if(c=a+12*h+2,274===p.getUint16(c,i)){c+=8,e=p.getUint16(c,i);break}return e}(t),e(o)})).catch((function(t){r(t)}))}))};const s=n,a={data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:function(){return[1,1]}},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:function(){return 10},validator:function(t){return Array.isArray(t)?Number(t[0])>=0&&Number(t[1])>=0:Number(t)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo:function(){var t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){var e=1;this.high&&!this.full&&(e=window.devicePixelRatio),1!==this.enlarge&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE:function(){return navigator.userAgent,!!window.ActiveXObject||"ActiveXObject"in window},passive:function(){return this.isIE?null:{passive:!1}}},watch:{img:function(){this.checkedImg()},imgs:function(t){""!==t&&this.reload()},cropW:function(){this.showPreview()},cropH:function(){this.showPreview()},cropOffsertX:function(){this.showPreview()},cropOffsertY:function(){this.showPreview()},scale:function(t,e){this.showPreview()},x:function(){this.showPreview()},y:function(){this.showPreview()},autoCrop:function(t){t&&this.goAutoCrop()},autoCropWidth:function(){this.autoCrop&&this.goAutoCrop()},autoCropHeight:function(){this.autoCrop&&this.goAutoCrop()},mode:function(){this.checkedImg()},rotate:function(){this.showPreview(),(this.autoCrop||this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion:function(t){for(var e=navigator.userAgent.split(" "),r="",o=new RegExp(t,"i"),i=0;i<e.length;i++)o.test(e[i])&&(r=e[i]);return r?r.split("/")[1].split("."):["0","0","0"]},checkOrientationImage:function(t,e,r,o){var i=this;if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){var n=this.getVersion("version");n[0]>13&&n[1]>1&&(e=-1)}else{var s=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(s){var a=s[1];((a=a.split("_"))[0]>13||a[0]>=13&&a[1]>=4)&&(e=-1)}}var c=document.createElement("canvas"),h=c.getContext("2d");switch(h.save(),e){case 2:c.width=r,c.height=o,h.translate(r,0),h.scale(-1,1);break;case 3:c.width=r,c.height=o,h.translate(r/2,o/2),h.rotate(180*Math.PI/180),h.translate(-r/2,-o/2);break;case 4:c.width=r,c.height=o,h.translate(0,o),h.scale(1,-1);break;case 5:c.height=r,c.width=o,h.rotate(.5*Math.PI),h.scale(1,-1);break;case 6:c.width=o,c.height=r,h.translate(o/2,r/2),h.rotate(90*Math.PI/180),h.translate(-r/2,-o/2);break;case 7:c.height=r,c.width=o,h.rotate(.5*Math.PI),h.translate(r,-o),h.scale(-1,1);break;case 8:c.height=r,c.width=o,h.translate(o/2,r/2),h.rotate(-90*Math.PI/180),h.translate(-r/2,-o/2);break;default:c.width=r,c.height=o}h.drawImage(t,0,0,r,o),h.restore(),c.toBlob((function(t){var e=URL.createObjectURL(t);URL.revokeObjectURL(i.imgs),i.imgs=e}),"image/"+this.outputType,1)},checkedImg:function(){var t=this;if(null===this.img||""===this.img)return this.imgs="",void this.clearCrop();this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();var e=new Image;if(e.onload=function(){if(""===t.img)return t.$emit("imgLoad","error"),t.$emit("img-load","error"),!1;var r=e.width,o=e.height;s.getData(e).then((function(i){t.orientation=i.orientation||1;var n=Number(t.maxImgSize);!t.orientation&&r<n&o<n?t.imgs=t.img:(r>n&&(o=o/r*n,r=n),o>n&&(r=r/o*n,o=n),t.checkOrientationImage(e,t.orientation,r,o))}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},"data"!==this.img.substr(0,4)&&(e.crossOrigin=""),this.isIE){var r=new XMLHttpRequest;r.onload=function(){var t=URL.createObjectURL(this.response);e.src=t},r.open("GET",this.img,!0),r.responseType="blob",r.send()}else e.src=this.img},startMove:function(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),2==t.touches.length&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("imgMoving",{moving:!0,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale:function(t){var e=this;t.preventDefault();var r=this.scale,o=this.touches[0].clientX,i=this.touches[0].clientY,n=t.touches[0].clientX,s=t.touches[0].clientY,a=this.touches[1].clientX,c=this.touches[1].clientY,h=t.touches[1].clientX,p=t.touches[1].clientY,u=Math.sqrt(Math.pow(o-a,2)+Math.pow(i-c,2)),l=Math.sqrt(Math.pow(n-h,2)+Math.pow(s-p,2))-u,f=1,d=(f=(f=f/this.trueWidth>f/this.trueHeight?f/this.trueHeight:f/this.trueWidth)>.1?.1:f)*l;if(!this.touchNow){if(this.touchNow=!0,l>0?r+=Math.abs(d):l<0&&r>Math.abs(d)&&(r-=Math.abs(d)),this.touches=t.touches,setTimeout((function(){e.touchNow=!1}),8),!this.checkoutImgAxis(this.x,this.y,r))return!1;this.scale=r}},cancelTouchScale:function(t){window.removeEventListener("touchmove",this.touchScale)},moveImg:function(t){var e=this;if(t.preventDefault(),t.touches&&2===t.touches.length)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;var r,o,i="clientX"in t?t.clientX:t.touches[0].clientX,n="clientY"in t?t.clientY:t.touches[0].clientY;r=i-this.moveX,o=n-this.moveY,this.$nextTick((function(){if(e.centerBox){var t,i,n,s,a=e.getImgAxis(r,o,e.scale),c=e.getCropAxis(),h=e.trueHeight*e.scale,p=e.trueWidth*e.scale;switch(e.rotate){case 1:case-1:case 3:case-3:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2+(h-p)/2,i=e.cropOffsertY-e.trueHeight*(1-e.scale)/2+(p-h)/2,n=t-h+e.cropW,s=i-p+e.cropH;break;default:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2,i=e.cropOffsertY-e.trueHeight*(1-e.scale)/2,n=t-p+e.cropW,s=i-h+e.cropH}a.x1>=c.x1&&(r=t),a.y1>=c.y1&&(o=i),a.x2<=c.x2&&(r=n),a.y2<=c.y2&&(o=s)}e.x=r,e.y=o,e.$emit("imgMoving",{moving:!0,axis:e.getImgAxis()}),e.$emit("img-moving",{moving:!0,axis:e.getImgAxis()})}))},leaveImg:function(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("imgMoving",{moving:!1,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg:function(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale:function(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize:function(t){var e=this;t.preventDefault();var r=this.scale,o=t.deltaY||t.wheelDelta;o=navigator.userAgent.indexOf("Firefox")>0?30*o:o,this.isIE&&(o=-o);var i=this.coe,n=(i=i/this.trueWidth>i/this.trueHeight?i/this.trueHeight:i/this.trueWidth)*o;n<0?r+=Math.abs(n):r>Math.abs(n)&&(r-=Math.abs(n));var s=n<0?"add":"reduce";if(s!==this.coeStatus&&(this.coeStatus=s,this.coe=.2),this.scaling||(this.scalingSet=setTimeout((function(){e.scaling=!1,e.coe=e.coe+=.01}),50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,r))return!1;this.scale=r},changeScale:function(t){var e=this.scale;t=t||1;var r=20;if((t*=r=r/this.trueWidth>r/this.trueHeight?r/this.trueHeight:r/this.trueWidth)>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop:function(t){var e=this;t.preventDefault();var r="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,o="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick((function(){var t=r-e.cropX,i=o-e.cropY;if(t>0?(e.cropW=t+e.cropChangeX>e.w?e.w-e.cropChangeX:t,e.cropOffsertX=e.cropChangeX):(e.cropW=e.w-e.cropChangeX+Math.abs(t)>e.w?e.cropChangeX:Math.abs(t),e.cropOffsertX=e.cropChangeX+t>0?e.cropChangeX+t:0),e.fixed){var n=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];n+e.cropOffsertY>e.h?(e.cropH=e.h-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],e.cropOffsertX=t>0?e.cropChangeX:e.cropChangeX-e.cropW):e.cropH=n,e.cropOffsertY=e.cropOffsertY}else i>0?(e.cropH=i+e.cropChangeY>e.h?e.h-e.cropChangeY:i,e.cropOffsertY=e.cropChangeY):(e.cropH=e.h-e.cropChangeY+Math.abs(i)>e.h?e.cropChangeY:Math.abs(i),e.cropOffsertY=e.cropChangeY+i>0?e.cropChangeY+i:0)}))},changeCropSize:function(t,e,r,o,i){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=r,this.changeCropTypeX=o,this.changeCropTypeY=i,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("changeCropSize",{width:this.cropW,height:this.cropH}),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow:function(t){var e=this;t.preventDefault();var r="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,o="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0,n=this.w,s=this.h,a=0,c=0;if(this.centerBox){var h=this.getImgAxis(),p=h.x2,u=h.y2;a=h.x1>0?h.x1:0,c=h.y1>0?h.y1:0,n>p&&(n=p),s>u&&(s=u)}var l=i(this.checkCropLimitSize(),2),f=l[0],d=l[1];this.$nextTick((function(){var t=r-e.cropX,i=o-e.cropY;if(e.canChangeX&&(1===e.changeCropTypeX?e.cropOldW-t<f?(e.cropW=f,e.cropOffsertX=e.cropOldW+e.cropChangeX-a-f):e.cropOldW-t>0?(e.cropW=n-e.cropChangeX-t<=n-a?e.cropOldW-t:e.cropOldW+e.cropChangeX-a,e.cropOffsertX=n-e.cropChangeX-t<=n-a?e.cropChangeX+t:a):(e.cropW=Math.abs(t)+e.cropChangeX<=n?Math.abs(t)-e.cropOldW:n-e.cropOldW-e.cropChangeX,e.cropOffsertX=e.cropChangeX+e.cropOldW):2===e.changeCropTypeX&&(e.cropOldW+t<f?e.cropW=f:e.cropOldW+t>0?(e.cropW=e.cropOldW+t+e.cropOffsertX<=n?e.cropOldW+t:n-e.cropOffsertX,e.cropOffsertX=e.cropChangeX):(e.cropW=n-e.cropChangeX+Math.abs(t+e.cropOldW)<=n-a?Math.abs(t+e.cropOldW):e.cropChangeX-a,e.cropOffsertX=n-e.cropChangeX+Math.abs(t+e.cropOldW)<=n-a?e.cropChangeX-Math.abs(t+e.cropOldW):a))),e.canChangeY&&(1===e.changeCropTypeY?e.cropOldH-i<d?(e.cropH=d,e.cropOffsertY=e.cropOldH+e.cropChangeY-c-d):e.cropOldH-i>0?(e.cropH=s-e.cropChangeY-i<=s-c?e.cropOldH-i:e.cropOldH+e.cropChangeY-c,e.cropOffsertY=s-e.cropChangeY-i<=s-c?e.cropChangeY+i:c):(e.cropH=Math.abs(i)+e.cropChangeY<=s?Math.abs(i)-e.cropOldH:s-e.cropOldH-e.cropChangeY,e.cropOffsertY=e.cropChangeY+e.cropOldH):2===e.changeCropTypeY&&(e.cropOldH+i<d?e.cropH=d:e.cropOldH+i>0?(e.cropH=e.cropOldH+i+e.cropOffsertY<=s?e.cropOldH+i:s-e.cropOffsertY,e.cropOffsertY=e.cropChangeY):(e.cropH=s-e.cropChangeY+Math.abs(i+e.cropOldH)<=s-c?Math.abs(i+e.cropOldH):e.cropChangeY-c,e.cropOffsertY=s-e.cropChangeY+Math.abs(i+e.cropOldH)<=s-c?e.cropChangeY-Math.abs(i+e.cropOldH):c))),e.canChangeX&&e.fixed){var h=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];h<d?(e.cropH=d,e.cropW=e.fixedNumber[0]*d/e.fixedNumber[1],1===e.changeCropTypeX&&(e.cropOffsertX=e.cropChangeX+(e.cropOldW-e.cropW))):h+e.cropOffsertY>s?(e.cropH=s-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],1===e.changeCropTypeX&&(e.cropOffsertX=e.cropChangeX+(e.cropOldW-e.cropW))):e.cropH=h}if(e.canChangeY&&e.fixed){var p=e.cropH/e.fixedNumber[1]*e.fixedNumber[0];p<f?(e.cropW=f,e.cropH=e.fixedNumber[1]*f/e.fixedNumber[0]):p+e.cropOffsertX>n?(e.cropW=n-e.cropOffsertX,e.cropH=e.cropW/e.fixedNumber[0]*e.fixedNumber[1]):e.cropW=p}e.$emit("cropSizing",{cropW:e.cropW,cropH:e.cropH}),e.$emit("crop-sizing",{cropW:e.cropW,cropH:e.cropH})}))},checkCropLimitSize:function(){this.cropW,this.cropH;var t=this.limitMinSize,e=new Array;return e=Array.isArray(t)?t:[t,t],[parseFloat(e[0]),parseFloat(e[1])]},changeCropEnd:function(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize:function(t,e,r,o,i,n){var s=t/e,a=i,c=n;return a<r&&(a=r,c=Math.ceil(a/s)),c<o&&(c=o,(a=Math.ceil(c*s))<r&&(a=r,c=Math.ceil(a/s))),a<i&&(a=i,c=Math.ceil(a/s)),c<n&&(c=n,a=Math.ceil(c*s)),{width:a,height:c}},endCrop:function(){0===this.cropW&&0===this.cropH&&(this.cropping=!1);var t=i(this.checkCropLimitSize(),2),e=t[0],r=t[1],o=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],e,r,this.cropW,this.cropH):{width:e,height:r},n=o.width,s=o.height;n>this.cropW&&(this.cropW=n,this.cropOffsertX+n>this.w&&(this.cropOffsertX=this.w-n)),s>this.cropH&&(this.cropH=s,this.cropOffsertY+s>this.h&&(this.cropOffsertY=this.h-s)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop:function(){this.crop=!0},stopCrop:function(){this.crop=!1},clearCrop:function(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove:function(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&2===t.touches.length)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);var e,r,o="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY;e=o-this.cropOffsertX,r=i-this.cropOffsertY,this.cropX=e,this.cropY=r,this.$emit("cropMoving",{moving:!0,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop:function(t,e){var r=this,o=0,i=0;t&&(t.preventDefault(),o="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick((function(){var t,n,s=o-r.cropX,a=i-r.cropY;if(e&&(s=r.cropOffsertX,a=r.cropOffsertY),t=s<=0?0:s+r.cropW>r.w?r.w-r.cropW:s,n=a<=0?0:a+r.cropH>r.h?r.h-r.cropH:a,r.centerBox){var c=r.getImgAxis();t<=c.x1&&(t=c.x1),t+r.cropW>c.x2&&(t=c.x2-r.cropW),n<=c.y1&&(n=c.y1),n+r.cropH>c.y2&&(n=c.y2-r.cropH)}r.cropOffsertX=t,r.cropOffsertY=n,r.$emit("cropMoving",{moving:!0,axis:r.getCropAxis()}),r.$emit("crop-moving",{moving:!0,axis:r.getCropAxis()})}))},getImgAxis:function(t,e,r){t=t||this.x,e=e||this.y,r=r||this.scale;var o={x1:0,x2:0,y1:0,y2:0},i=this.trueWidth*r,n=this.trueHeight*r;switch(this.rotate){case 0:o.x1=t+this.trueWidth*(1-r)/2,o.x2=o.x1+this.trueWidth*r,o.y1=e+this.trueHeight*(1-r)/2,o.y2=o.y1+this.trueHeight*r;break;case 1:case-1:case 3:case-3:o.x1=t+this.trueWidth*(1-r)/2+(i-n)/2,o.x2=o.x1+this.trueHeight*r,o.y1=e+this.trueHeight*(1-r)/2+(n-i)/2,o.y2=o.y1+this.trueWidth*r;break;default:o.x1=t+this.trueWidth*(1-r)/2,o.x2=o.x1+this.trueWidth*r,o.y1=e+this.trueHeight*(1-r)/2,o.y2=o.y1+this.trueHeight*r}return o},getCropAxis:function(){var t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop:function(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("cropMoving",{moving:!1,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked:function(t){var e=this,r=document.createElement("canvas"),o=new Image,i=this.rotate,n=this.trueWidth,s=this.trueHeight,a=this.cropOffsertX,c=this.cropOffsertY;function h(t,e){r.width=Math.round(t),r.height=Math.round(e)}o.onload=function(){if(0!==e.cropW){var p=r.getContext("2d"),u=1;e.high&!e.full&&(u=window.devicePixelRatio),1!==e.enlarge&!e.full&&(u=Math.abs(Number(e.enlarge)));var l=e.cropW*u,f=e.cropH*u,d=n*e.scale*u,g=s*e.scale*u,v=(e.x-a+e.trueWidth*(1-e.scale)/2)*u,m=(e.y-c+e.trueHeight*(1-e.scale)/2)*u;switch(h(l,f),p.save(),e.fillColor&&(p.fillStyle=e.fillColor,p.fillRect(0,0,r.width,r.height)),i){case 0:e.full?(h(l/e.scale,f/e.scale),p.drawImage(o,v/e.scale,m/e.scale,d/e.scale,g/e.scale)):p.drawImage(o,v,m,d,g);break;case 1:case-3:e.full?(h(l/e.scale,f/e.scale),v=v/e.scale+(d/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-d/e.scale)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,m,-v-g/e.scale,d/e.scale,g/e.scale)):(v+=(d-g)/2,m+=(g-d)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,m,-v-g,d,g));break;case 2:case-2:e.full?(h(l/e.scale,f/e.scale),p.rotate(90*i*Math.PI/180),v/=e.scale,m/=e.scale,p.drawImage(o,-v-d/e.scale,-m-g/e.scale,d/e.scale,g/e.scale)):(p.rotate(90*i*Math.PI/180),p.drawImage(o,-v-d,-m-g,d,g));break;case 3:case-1:e.full?(h(l/e.scale,f/e.scale),v=v/e.scale+(d/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-d/e.scale)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,-m-d/e.scale,v,d/e.scale,g/e.scale)):(v+=(d-g)/2,m+=(g-d)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,-m-d,v,d,g));break;default:e.full?(h(l/e.scale,f/e.scale),p.drawImage(o,v/e.scale,m/e.scale,d/e.scale,g/e.scale)):p.drawImage(o,v,m,d,g)}p.restore()}else{var w=n*e.scale,x=s*e.scale,C=r.getContext("2d");switch(C.save(),e.fillColor&&(C.fillStyle=e.fillColor,C.fillRect(0,0,r.width,r.height)),i){case 0:h(w,x),C.drawImage(o,0,0,w,x);break;case 1:case-3:h(x,w),C.rotate(90*i*Math.PI/180),C.drawImage(o,0,-x,w,x);break;case 2:case-2:h(w,x),C.rotate(90*i*Math.PI/180),C.drawImage(o,-w,-x,w,x);break;case 3:case-1:h(x,w),C.rotate(90*i*Math.PI/180),C.drawImage(o,-w,0,w,x);break;default:h(w,x),C.drawImage(o,0,0,w,x)}C.restore()}t(r)},"data"!==this.img.substr(0,4)&&(o.crossOrigin="Anonymous"),o.src=this.imgs},getCropData:function(t){var e=this;this.getCropChecked((function(r){t(r.toDataURL("image/"+e.outputType,e.outputSize))}))},getCropBlob:function(t){var e=this;this.getCropChecked((function(r){r.toBlob((function(e){return t(e)}),"image/"+e.outputType,e.outputSize)}))},showPreview:function(){var t=this;if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout((function(){t.isCanShow=!0}),16);var e=this.cropW,r=this.cropH,o=this.scale,i={};i.div={width:"".concat(e,"px"),height:"".concat(r,"px")};var n=(this.x-this.cropOffsertX)/o,s=(this.y-this.cropOffsertY)/o;i.w=e,i.h=r,i.url=this.imgs,i.img={width:"".concat(this.trueWidth,"px"),height:"".concat(this.trueHeight,"px"),transform:"scale(".concat(o,")translate3d(").concat(n,"px, ").concat(s,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,"deg)")},i.html='\n      <div class="show-preview" style="width: '.concat(i.w,"px; height: ").concat(i.h,'px; overflow: hidden">\n        <div style="width: ').concat(e,"px; height: ").concat(r,'px">\n          <img src=').concat(i.url,' style="width: ').concat(this.trueWidth,"px; height: ").concat(this.trueHeight,"px; transform:\n          scale(").concat(o,")translate3d(").concat(n,"px, ").concat(s,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,'deg)">\n        </div>\n      </div>'),this.$emit("realTime",i),this.$emit("real-time",i)},reload:function(){var t=this,e=new Image;e.onload=function(){t.w=parseFloat(window.getComputedStyle(t.$refs.cropper).width),t.h=parseFloat(window.getComputedStyle(t.$refs.cropper).height),t.trueWidth=e.width,t.trueHeight=e.height,t.original?t.scale=1:t.scale=t.checkedMode(),t.$nextTick((function(){t.x=-(t.trueWidth-t.trueWidth*t.scale)/2+(t.w-t.trueWidth*t.scale)/2,t.y=-(t.trueHeight-t.trueHeight*t.scale)/2+(t.h-t.trueHeight*t.scale)/2,t.loading=!1,t.autoCrop&&t.goAutoCrop(),t.$emit("img-load","success"),t.$emit("imgLoad","success"),setTimeout((function(){t.showPreview()}),20)}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},e.src=this.imgs},checkedMode:function(){var t=1,e=(this.trueWidth,this.trueHeight),r=this.mode.split(" ");switch(r[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":(e*=t=this.w/this.trueWidth)<this.h&&(t=(e=this.h)/this.trueHeight);break;default:try{var o=r[0];if(-1!==o.search("px")){o=o.replace("px","");var i=parseFloat(o)/this.trueWidth,n=1,s=r[1];-1!==s.search("px")&&(s=s.replace("px",""),n=(e=parseFloat(s))/this.trueHeight),t=Math.min(i,n)}if(-1!==o.search("%")&&(o=o.replace("%",""),t=parseFloat(o)/100*this.w/this.trueWidth),2===r.length&&"auto"===o){var a=r[1];-1!==a.search("px")&&(a=a.replace("px",""),t=(e=parseFloat(a))/this.trueHeight),-1!==a.search("%")&&(a=a.replace("%",""),t=(e=parseFloat(a)/100*this.h)/this.trueHeight)}}catch(e){t=1}}return t},goAutoCrop:function(t,e){if(""!==this.imgs&&null!==this.imgs){this.clearCrop(),this.cropping=!0;var r=this.w,o=this.h;if(this.centerBox){var i=Math.abs(this.rotate)%2>0,n=(i?this.trueHeight:this.trueWidth)*this.scale,s=(i?this.trueWidth:this.trueHeight)*this.scale;r=n<r?n:r,o=s<o?s:o}var a=t||parseFloat(this.autoCropWidth),c=e||parseFloat(this.autoCropHeight);0!==a&&0!==c||(a=.8*r,c=.8*o),a=a>r?r:a,c=c>o?o:c,this.fixed&&(c=a/this.fixedNumber[0]*this.fixedNumber[1]),c>this.h&&(a=(c=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(a,c)}},changeCrop:function(t,e){var r=this;if(this.centerBox){var o=this.getImgAxis();t>o.x2-o.x1&&(e=(t=o.x2-o.x1)/this.fixedNumber[0]*this.fixedNumber[1]),e>o.y2-o.y1&&(t=(e=o.y2-o.y1)/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick((function(){r.cropOffsertX=(r.w-r.cropW)/2,r.cropOffsertY=(r.h-r.cropH)/2,r.centerBox&&r.moveCrop(null,!0)}))},refresh:function(){var t=this;this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick((function(){t.checkedImg()}))},rotateLeft:function(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight:function(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear:function(){this.rotate=0},checkoutImgAxis:function(t,e,r){t=t||this.x,e=e||this.y,r=r||this.scale;var o=!0;if(this.centerBox){var i=this.getImgAxis(t,e,r),n=this.getCropAxis();i.x1>=n.x1&&(o=!1),i.x2<=n.x2&&(o=!1),i.y1>=n.y1&&(o=!1),i.y2<=n.y2&&(o=!1)}return o}},mounted:function(){this.support="onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll";var t=this,e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(e,r,o){for(var i=atob(this.toDataURL(r,o).split(",")[1]),n=i.length,s=new Uint8Array(n),a=0;a<n;a++)s[a]=i.charCodeAt(a);e(new Blob([s],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},destroyed:function(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}};r(614);var c=function(t,e,r,o,i,n,s,a){var c,h="function"==typeof t?t.options:t;if(e&&(h.render=e,h.staticRenderFns=[],h._compiled=!0),n&&(h._scopeId="data-v-"+n),c)if(h.functional){h._injectStyles=c;var p=h.render;h.render=function(t,e){return c.call(e),p(t,e)}}else{var u=h.beforeCreate;h.beforeCreate=u?[].concat(u,c):[c]}return{exports:t,options:h}}(a,t,0,0,0,"33936806");const h=c.exports;var p=function(t){t.component("VueCropper",h)};"undefined"!=typeof window&&window.Vue&&p(window.Vue);const u={version:"0.6.1",install:p,VueCropper:h,vueCropper:h}})(),o})()));
+!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("vue-cropper",[],e):"object"==typeof exports?exports["vue-cropper"]=e():t["vue-cropper"]=e()}(self,(()=>(()=>{var t={105:(t,e,r)=>{(t.exports=r(252)(!1)).push([t.id,'\n.vue-cropper[data-v-1adc23fd] {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  user-select: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  direction: ltr;\n  touch-action: none;\n  text-align: left;\n  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");\n}\n.cropper-box[data-v-1adc23fd],\n.cropper-box-canvas[data-v-1adc23fd],\n.cropper-drag-box[data-v-1adc23fd],\n.cropper-crop-box[data-v-1adc23fd],\n.cropper-face[data-v-1adc23fd] {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  user-select: none;\n}\n.cropper-box-canvas img[data-v-1adc23fd] {\n  position: relative;\n  text-align: left;\n  user-select: none;\n  transform: none;\n  max-width: none;\n  max-height: none;\n}\n.cropper-box[data-v-1adc23fd] {\n  overflow: hidden;\n}\n.cropper-move[data-v-1adc23fd] {\n  cursor: move;\n}\n.cropper-crop[data-v-1adc23fd] {\n  cursor: crosshair;\n}\n.cropper-modal[data-v-1adc23fd] {\n  background: rgba(0, 0, 0, 0.5);\n}\n.cropper-crop-box[data-v-1adc23fd] {\n  /*border: 2px solid #39f;*/\n}\n.cropper-view-box[data-v-1adc23fd] {\n  display: block;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  user-select: none;\n}\n.cropper-view-box img[data-v-1adc23fd] {\n  user-select: none;\n  text-align: left;\n  max-width: none;\n  max-height: none;\n}\n.cropper-face[data-v-1adc23fd] {\n  top: 0;\n  left: 0;\n  background-color: #fff;\n  opacity: 0.1;\n}\n.crop-info[data-v-1adc23fd] {\n  position: absolute;\n  left: 0px;\n  min-width: 65px;\n  text-align: center;\n  color: white;\n  line-height: 20px;\n  background-color: rgba(0, 0, 0, 0.8);\n  font-size: 12px;\n}\n.crop-line[data-v-1adc23fd] {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  opacity: 0.1;\n}\n.line-w[data-v-1adc23fd] {\n  top: -3px;\n  left: 0;\n  height: 5px;\n  cursor: n-resize;\n}\n.line-a[data-v-1adc23fd] {\n  top: 0;\n  left: -3px;\n  width: 5px;\n  cursor: w-resize;\n}\n.line-s[data-v-1adc23fd] {\n  bottom: -3px;\n  left: 0;\n  height: 5px;\n  cursor: s-resize;\n}\n.line-d[data-v-1adc23fd] {\n  top: 0;\n  right: -3px;\n  width: 5px;\n  cursor: e-resize;\n}\n.crop-point[data-v-1adc23fd] {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  opacity: 0.75;\n  background-color: #39f;\n  border-radius: 100%;\n}\n.point1[data-v-1adc23fd] {\n  top: -4px;\n  left: -4px;\n  cursor: nw-resize;\n}\n.point2[data-v-1adc23fd] {\n  top: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: n-resize;\n}\n.point3[data-v-1adc23fd] {\n  top: -4px;\n  right: -4px;\n  cursor: ne-resize;\n}\n.point4[data-v-1adc23fd] {\n  top: 50%;\n  left: -4px;\n  margin-top: -3px;\n  cursor: w-resize;\n}\n.point5[data-v-1adc23fd] {\n  top: 50%;\n  right: -4px;\n  margin-top: -3px;\n  cursor: e-resize;\n}\n.point6[data-v-1adc23fd] {\n  bottom: -5px;\n  left: -4px;\n  cursor: sw-resize;\n}\n.point7[data-v-1adc23fd] {\n  bottom: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: s-resize;\n}\n.point8[data-v-1adc23fd] {\n  bottom: -5px;\n  right: -4px;\n  cursor: se-resize;\n}\n@media screen and (max-width: 500px) {\n.crop-point[data-v-1adc23fd] {\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    opacity: 0.45;\n    background-color: #39f;\n    border-radius: 100%;\n}\n.point1[data-v-1adc23fd] {\n    top: -10px;\n    left: -10px;\n}\n.point2[data-v-1adc23fd],\n  .point4[data-v-1adc23fd],\n  .point5[data-v-1adc23fd],\n  .point7[data-v-1adc23fd] {\n    display: none;\n}\n.point3[data-v-1adc23fd] {\n    top: -10px;\n    right: -10px;\n}\n.point4[data-v-1adc23fd] {\n    top: 0;\n    left: 0;\n}\n.point6[data-v-1adc23fd] {\n    bottom: -10px;\n    left: -10px;\n}\n.point8[data-v-1adc23fd] {\n    bottom: -10px;\n    right: -10px;\n}\n}\n',""])},252:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=function(t,e){var r,o=t[1]||"",i=t[3];if(!i)return o;if(e&&"function"==typeof btoa){var n=(r=i,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */"),s=i.sources.map((function(t){return"/*# sourceURL="+i.sourceRoot+t+" */"}));return[o].concat(s).concat([n]).join("\n")}return[o].join("\n")}(e,t);return e[2]?"@media "+e[2]+"{"+r+"}":r})).join("")},e.i=function(t,r){"string"==typeof t&&(t=[[null,t,""]]);for(var o={},i=0;i<this.length;i++){var n=this[i][0];"number"==typeof n&&(o[n]=!0)}for(i=0;i<t.length;i++){var s=t[i];"number"==typeof s[0]&&o[s[0]]||(r&&!s[2]?s[2]=r:r&&(s[2]="("+s[2]+") and ("+r+")"),e.push(s))}},e}},881:(t,e,r)=>{var o=r(105);"string"==typeof o&&(o=[[t.id,o,""]]);r(723)(o,{hmr:!0,transform:void 0,insertInto:void 0}),o.locals&&(t.exports=o.locals)},723:(t,e,r)=>{var o,i,n={},s=(o=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=o.apply(this,arguments)),i}),a=function(t,e){return e?e.querySelector(t):document.querySelector(t)},c=function(t){var e={};return function(t,r){if("function"==typeof t)return t();if(void 0===e[t]){var o=a.call(this,t,r);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(t){o=null}e[t]=o}return e[t]}}(),h=null,p=0,u=[],l=r(947);function d(t,e){for(var r=0;r<t.length;r++){var o=t[r],i=n[o.id];if(i){i.refs++;for(var s=0;s<i.parts.length;s++)i.parts[s](o.parts[s]);for(;s<o.parts.length;s++)i.parts.push(x(o.parts[s],e))}else{var a=[];for(s=0;s<o.parts.length;s++)a.push(x(o.parts[s],e));n[o.id]={id:o.id,refs:1,parts:a}}}}function f(t,e){for(var r=[],o={},i=0;i<t.length;i++){var n=t[i],s=e.base?n[0]+e.base:n[0],a={css:n[1],media:n[2],sourceMap:n[3]};o[s]?o[s].parts.push(a):r.push(o[s]={id:s,parts:[a]})}return r}function g(t,e){var r=c(t.insertInto);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var o=u[u.length-1];if("top"===t.insertAt)o?o.nextSibling?r.insertBefore(e,o.nextSibling):r.appendChild(e):r.insertBefore(e,r.firstChild),u.push(e);else if("bottom"===t.insertAt)r.appendChild(e);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=c(t.insertAt.before,r);r.insertBefore(e,i)}}function v(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var e=u.indexOf(t);e>=0&&u.splice(e,1)}function m(t){var e=document.createElement("style");if(void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce){var o=r.nc;o&&(t.attrs.nonce=o)}return w(e,t.attrs),g(t,e),e}function w(t,e){Object.keys(e).forEach((function(r){t.setAttribute(r,e[r])}))}function x(t,e){var r,o,i,n;if(e.transform&&t.css){if(!(n="function"==typeof e.transform?e.transform(t.css):e.transform.default(t.css)))return function(){};t.css=n}if(e.singleton){var s=p++;r=h||(h=m(e)),o=y.bind(null,r,s,!1),i=y.bind(null,r,s,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(r=function(t){var e=document.createElement("link");return void 0===t.attrs.type&&(t.attrs.type="text/css"),t.attrs.rel="stylesheet",w(e,t.attrs),g(t,e),e}(e),o=A.bind(null,r,e),i=function(){v(r),r.href&&URL.revokeObjectURL(r.href)}):(r=m(e),o=O.bind(null,r),i=function(){v(r)});return o(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;o(t=e)}else i()}}t.exports=function(t,e){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(e=e||{}).attrs="object"==typeof e.attrs?e.attrs:{},e.singleton||"boolean"==typeof e.singleton||(e.singleton=s()),e.insertInto||(e.insertInto="head"),e.insertAt||(e.insertAt="bottom");var r=f(t,e);return d(r,e),function(t){for(var o=[],i=0;i<r.length;i++){var s=r[i];(a=n[s.id]).refs--,o.push(a)}for(t&&d(f(t,e),e),i=0;i<o.length;i++){var a;if(0===(a=o[i]).refs){for(var c=0;c<a.parts.length;c++)a.parts[c]();delete n[a.id]}}}};var C,b=(C=[],function(t,e){return C[t]=e,C.filter(Boolean).join("\n")});function y(t,e,r,o){var i=r?"":o.css;if(t.styleSheet)t.styleSheet.cssText=b(e,i);else{var n=document.createTextNode(i),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(n,s[e]):t.appendChild(n)}}function O(t,e){var r=e.css,o=e.media;if(o&&t.setAttribute("media",o),t.styleSheet)t.styleSheet.cssText=r;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(r))}}function A(t,e,r){var o=r.css,i=r.sourceMap,n=void 0===e.convertToAbsoluteUrls&&i;(e.convertToAbsoluteUrls||n)&&(o=l(o)),i&&(o+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var s=new Blob([o],{type:"text/css"}),a=t.href;t.href=URL.createObjectURL(s),a&&URL.revokeObjectURL(a)}},947:t=>{t.exports=function(t){var e="undefined"!=typeof window&&window.location;if(!e)throw new Error("fixUrls requires window.location");if(!t||"string"!=typeof t)return t;var r=e.protocol+"//"+e.host,o=r+e.pathname.replace(/\/[^\/]*$/,"/");return t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(t,e){var i,n=e.trim().replace(/^"(.*)"$/,(function(t,e){return e})).replace(/^'(.*)'$/,(function(t,e){return e}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(n)?t:(i=0===n.indexOf("//")?n:0===n.indexOf("/")?r+n:o+n.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")}))}}},e={};function r(o){var i=e[o];if(void 0!==i)return i.exports;var n=e[o]={id:o,exports:{}};return t[o](n,n.exports,r),n.exports}r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var o={};return(()=>{"use strict";r.r(o),r.d(o,{VueCropper:()=>h,default:()=>u});var t=function(){var t=this,e=t._self._c;return e("div",{ref:"cropper",staticClass:"vue-cropper",on:{mouseover:t.scaleImg,mouseout:t.cancelScale}},[t.imgs?e("div",{staticClass:"cropper-box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"cropper-box-canvas",style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"}},[e("img",{ref:"cropperImg",attrs:{src:t.imgs,alt:"cropper-img"}})])]):t._e(),t._v(" "),e("div",{staticClass:"cropper-drag-box",class:{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping},on:{mousedown:t.startMove,touchstart:t.startMove}}),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.cropping,expression:"cropping"}],staticClass:"cropper-crop-box",style:{width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"}},[e("span",{staticClass:"cropper-view-box"},[e("img",{style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"},attrs:{src:t.imgs,alt:"cropper-img"}})]),t._v(" "),e("span",{staticClass:"cropper-face cropper-move",on:{mousedown:t.cropMove,touchstart:t.cropMove}}),t._v(" "),t.info?e("span",{staticClass:"crop-info",style:{top:t.cropInfo.top}},[t._v(t._s(t.cropInfo.width)+" × "+t._s(t.cropInfo.height))]):t._e(),t._v(" "),t.fixedBox?t._e():e("span",[e("span",{staticClass:"crop-line line-w",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),e("span",{staticClass:"crop-line line-a",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),e("span",{staticClass:"crop-line line-s",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),e("span",{staticClass:"crop-line line-d",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point1",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,1,1)},touchstart:function(e){return t.changeCropSize(e,!0,!0,1,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point2",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point3",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,2,1)},touchstart:function(e){return t.changeCropSize(e,!0,!0,2,1)}}}),t._v(" "),e("span",{staticClass:"crop-point point4",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point5",on:{mousedown:function(e){return t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){return t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),e("span",{staticClass:"crop-point point6",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,1,2)},touchstart:function(e){return t.changeCropSize(e,!0,!0,1,2)}}}),t._v(" "),e("span",{staticClass:"crop-point point7",on:{mousedown:function(e){return t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){return t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),e("span",{staticClass:"crop-point point8",on:{mousedown:function(e){return t.changeCropSize(e,!0,!0,2,2)},touchstart:function(e){return t.changeCropSize(e,!0,!0,2,2)}}})])])])};function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=new Array(e);r<e;r++)o[r]=t[r];return o}function i(t,r){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var o,i,n,s,a=[],c=!0,h=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=n.call(r)).done)&&(a.push(o.value),a.length!==e);c=!0);}catch(t){h=!0,i=t}finally{try{if(!c&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(h)throw i}}return a}}(t,r)||function(t,r){if(t){if("string"==typeof t)return e(t,r);var o=Object.prototype.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?e(t,r):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}t._withStripped=!0;var n={};n.getData=function(t){return new Promise((function(e,r){var o={};(function(t){var e=null;return new Promise((function(r,o){if(t.src)if(/^data\:/i.test(t.src))e=function(t){t=t.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var e=atob(t),r=e.length,o=new ArrayBuffer(r),i=new Uint8Array(o),n=0;n<r;n++)i[n]=e.charCodeAt(n);return o}(t.src),r(e);else if(/^blob\:/i.test(t.src)){var i=new FileReader;i.onload=function(t){e=t.target.result,r(e)},function(t,e){var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="blob",r.onload=function(t){var e;200!=this.status&&0!==this.status||(e=this.response,i.readAsArrayBuffer(e))},r.send()}(t.src)}else{var n=new XMLHttpRequest;n.onload=function(){if(200!=this.status&&0!==this.status)throw"Could not load image";e=n.response,r(e),n=null},n.open("GET",t.src,!0),n.responseType="arraybuffer",n.send(null)}else o("img error")}))})(t).then((function(t){o.arrayBuffer=t,o.orientation=function(t){var e,r,o,i,n,s,a,c,h,p=new DataView(t),u=p.byteLength;if(255===p.getUint8(0)&&216===p.getUint8(1))for(c=2;c<u;){if(255===p.getUint8(c)&&225===p.getUint8(c+1)){s=c;break}c++}if(s&&(r=s+10,"Exif"===function(t,e,r){var o,i="";for(o=e,r+=e;o<r;o++)i+=String.fromCharCode(t.getUint8(o));return i}(p,s+4,4)&&((i=18761===(n=p.getUint16(r)))||19789===n)&&42===p.getUint16(r+2,i)&&(o=p.getUint32(r+4,i))>=8&&(a=r+o)),a)for(u=p.getUint16(a,i),h=0;h<u;h++)if(c=a+12*h+2,274===p.getUint16(c,i)){c+=8,e=p.getUint16(c,i);break}return e}(t),e(o)})).catch((function(t){r(t)}))}))};const s=n,a={data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:function(){return[1,1]}},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:function(){return 10},validator:function(t){return Array.isArray(t)?Number(t[0])>=0&&Number(t[1])>=0:Number(t)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo:function(){var t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){var e=1;this.high&&!this.full&&(e=window.devicePixelRatio),1!==this.enlarge&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE:function(){return navigator.userAgent,!!window.ActiveXObject||"ActiveXObject"in window},passive:function(){return this.isIE?null:{passive:!1}}},watch:{img:function(){this.checkedImg()},imgs:function(t){""!==t&&this.reload()},cropW:function(){this.showPreview()},cropH:function(){this.showPreview()},cropOffsertX:function(){this.showPreview()},cropOffsertY:function(){this.showPreview()},scale:function(t,e){this.showPreview()},x:function(){this.showPreview()},y:function(){this.showPreview()},autoCrop:function(t){t&&this.goAutoCrop()},autoCropWidth:function(){this.autoCrop&&this.goAutoCrop()},autoCropHeight:function(){this.autoCrop&&this.goAutoCrop()},mode:function(){this.checkedImg()},rotate:function(){this.showPreview(),(this.autoCrop||this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion:function(t){for(var e=navigator.userAgent.split(" "),r="",o=new RegExp(t,"i"),i=0;i<e.length;i++)o.test(e[i])&&(r=e[i]);return r?r.split("/")[1].split("."):["0","0","0"]},checkOrientationImage:function(t,e,r,o){var i=this;if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){var n=this.getVersion("version");n[0]>13&&n[1]>1&&(e=-1)}else{var s=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(s){var a=s[1];((a=a.split("_"))[0]>13||a[0]>=13&&a[1]>=4)&&(e=-1)}}var c=document.createElement("canvas"),h=c.getContext("2d");switch(h.save(),e){case 2:c.width=r,c.height=o,h.translate(r,0),h.scale(-1,1);break;case 3:c.width=r,c.height=o,h.translate(r/2,o/2),h.rotate(180*Math.PI/180),h.translate(-r/2,-o/2);break;case 4:c.width=r,c.height=o,h.translate(0,o),h.scale(1,-1);break;case 5:c.height=r,c.width=o,h.rotate(.5*Math.PI),h.scale(1,-1);break;case 6:c.width=o,c.height=r,h.translate(o/2,r/2),h.rotate(90*Math.PI/180),h.translate(-r/2,-o/2);break;case 7:c.height=r,c.width=o,h.rotate(.5*Math.PI),h.translate(r,-o),h.scale(-1,1);break;case 8:c.height=r,c.width=o,h.translate(o/2,r/2),h.rotate(-90*Math.PI/180),h.translate(-r/2,-o/2);break;default:c.width=r,c.height=o}h.drawImage(t,0,0,r,o),h.restore(),c.toBlob((function(t){var e=URL.createObjectURL(t);URL.revokeObjectURL(i.imgs),i.imgs=e}),"image/"+this.outputType,1)},checkedImg:function(){var t=this;if(null===this.img||""===this.img)return this.imgs="",void this.clearCrop();this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();var e=new Image;if(e.onload=function(){if(""===t.img)return t.$emit("imgLoad","error"),t.$emit("img-load","error"),!1;var r=e.width,o=e.height;s.getData(e).then((function(i){t.orientation=i.orientation||1;var n=Number(t.maxImgSize);!t.orientation&&r<n&o<n?t.imgs=t.img:(r>n&&(o=o/r*n,r=n),o>n&&(r=r/o*n,o=n),t.checkOrientationImage(e,t.orientation,r,o))})).catch((function(){t.checkOrientationImage(e,t.orientation,r,o)}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},"data"!==this.img.substr(0,4)&&(e.crossOrigin=""),this.isIE){var r=new XMLHttpRequest;r.onload=function(){var t=URL.createObjectURL(this.response);e.src=t},r.open("GET",this.img,!0),r.responseType="blob",r.send()}else e.src=this.img},startMove:function(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),2==t.touches.length&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("imgMoving",{moving:!0,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale:function(t){var e=this;t.preventDefault();var r=this.scale,o=this.touches[0].clientX,i=this.touches[0].clientY,n=t.touches[0].clientX,s=t.touches[0].clientY,a=this.touches[1].clientX,c=this.touches[1].clientY,h=t.touches[1].clientX,p=t.touches[1].clientY,u=Math.sqrt(Math.pow(o-a,2)+Math.pow(i-c,2)),l=Math.sqrt(Math.pow(n-h,2)+Math.pow(s-p,2))-u,d=1,f=(d=(d=d/this.trueWidth>d/this.trueHeight?d/this.trueHeight:d/this.trueWidth)>.1?.1:d)*l;if(!this.touchNow){if(this.touchNow=!0,l>0?r+=Math.abs(f):l<0&&r>Math.abs(f)&&(r-=Math.abs(f)),this.touches=t.touches,setTimeout((function(){e.touchNow=!1}),8),!this.checkoutImgAxis(this.x,this.y,r))return!1;this.scale=r}},cancelTouchScale:function(t){window.removeEventListener("touchmove",this.touchScale)},moveImg:function(t){var e=this;if(t.preventDefault(),t.touches&&2===t.touches.length)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;var r,o,i="clientX"in t?t.clientX:t.touches[0].clientX,n="clientY"in t?t.clientY:t.touches[0].clientY;r=i-this.moveX,o=n-this.moveY,this.$nextTick((function(){if(e.centerBox){var t,i,n,s,a=e.getImgAxis(r,o,e.scale),c=e.getCropAxis(),h=e.trueHeight*e.scale,p=e.trueWidth*e.scale;switch(e.rotate){case 1:case-1:case 3:case-3:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2+(h-p)/2,i=e.cropOffsertY-e.trueHeight*(1-e.scale)/2+(p-h)/2,n=t-h+e.cropW,s=i-p+e.cropH;break;default:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2,i=e.cropOffsertY-e.trueHeight*(1-e.scale)/2,n=t-p+e.cropW,s=i-h+e.cropH}a.x1>=c.x1&&(r=t),a.y1>=c.y1&&(o=i),a.x2<=c.x2&&(r=n),a.y2<=c.y2&&(o=s)}e.x=r,e.y=o,e.$emit("imgMoving",{moving:!0,axis:e.getImgAxis()}),e.$emit("img-moving",{moving:!0,axis:e.getImgAxis()})}))},leaveImg:function(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("imgMoving",{moving:!1,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg:function(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale:function(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize:function(t){var e=this;t.preventDefault();var r=this.scale,o=t.deltaY||t.wheelDelta;o=navigator.userAgent.indexOf("Firefox")>0?30*o:o,this.isIE&&(o=-o);var i=this.coe,n=(i=i/this.trueWidth>i/this.trueHeight?i/this.trueHeight:i/this.trueWidth)*o;n<0?r+=Math.abs(n):r>Math.abs(n)&&(r-=Math.abs(n));var s=n<0?"add":"reduce";if(s!==this.coeStatus&&(this.coeStatus=s,this.coe=.2),this.scaling||(this.scalingSet=setTimeout((function(){e.scaling=!1,e.coe=e.coe+=.01}),50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,r))return!1;this.scale=r},changeScale:function(t){var e=this.scale;t=t||1;var r=20;if((t*=r=r/this.trueWidth>r/this.trueHeight?r/this.trueHeight:r/this.trueWidth)>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop:function(t){var e=this;t.preventDefault();var r="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,o="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick((function(){var t=r-e.cropX,i=o-e.cropY;if(t>0?(e.cropW=t+e.cropChangeX>e.w?e.w-e.cropChangeX:t,e.cropOffsertX=e.cropChangeX):(e.cropW=e.w-e.cropChangeX+Math.abs(t)>e.w?e.cropChangeX:Math.abs(t),e.cropOffsertX=e.cropChangeX+t>0?e.cropChangeX+t:0),e.fixed){var n=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];n+e.cropOffsertY>e.h?(e.cropH=e.h-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],e.cropOffsertX=t>0?e.cropChangeX:e.cropChangeX-e.cropW):e.cropH=n,e.cropOffsertY=e.cropOffsertY}else i>0?(e.cropH=i+e.cropChangeY>e.h?e.h-e.cropChangeY:i,e.cropOffsertY=e.cropChangeY):(e.cropH=e.h-e.cropChangeY+Math.abs(i)>e.h?e.cropChangeY:Math.abs(i),e.cropOffsertY=e.cropChangeY+i>0?e.cropChangeY+i:0)}))},changeCropSize:function(t,e,r,o,i){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=r,this.changeCropTypeX=o,this.changeCropTypeY=i,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("changeCropSize",{width:this.cropW,height:this.cropH}),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow:function(t){var e=this;t.preventDefault();var r="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,o="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0,n=this.w,s=this.h,a=0,c=0;if(this.centerBox){var h=this.getImgAxis(),p=h.x2,u=h.y2;a=h.x1>0?h.x1:0,c=h.y1>0?h.y1:0,n>p&&(n=p),s>u&&(s=u)}var l=i(this.checkCropLimitSize(),2),d=l[0],f=l[1];this.$nextTick((function(){var t=r-e.cropX,i=o-e.cropY;if(e.canChangeX&&(1===e.changeCropTypeX?e.cropOldW-t<d?(e.cropW=d,e.cropOffsertX=e.cropOldW+e.cropChangeX-a-d):e.cropOldW-t>0?(e.cropW=n-e.cropChangeX-t<=n-a?e.cropOldW-t:e.cropOldW+e.cropChangeX-a,e.cropOffsertX=n-e.cropChangeX-t<=n-a?e.cropChangeX+t:a):(e.cropW=Math.abs(t)+e.cropChangeX<=n?Math.abs(t)-e.cropOldW:n-e.cropOldW-e.cropChangeX,e.cropOffsertX=e.cropChangeX+e.cropOldW):2===e.changeCropTypeX&&(e.cropOldW+t<d?e.cropW=d:e.cropOldW+t>0?(e.cropW=e.cropOldW+t+e.cropOffsertX<=n?e.cropOldW+t:n-e.cropOffsertX,e.cropOffsertX=e.cropChangeX):(e.cropW=n-e.cropChangeX+Math.abs(t+e.cropOldW)<=n-a?Math.abs(t+e.cropOldW):e.cropChangeX-a,e.cropOffsertX=n-e.cropChangeX+Math.abs(t+e.cropOldW)<=n-a?e.cropChangeX-Math.abs(t+e.cropOldW):a))),e.canChangeY&&(1===e.changeCropTypeY?e.cropOldH-i<f?(e.cropH=f,e.cropOffsertY=e.cropOldH+e.cropChangeY-c-f):e.cropOldH-i>0?(e.cropH=s-e.cropChangeY-i<=s-c?e.cropOldH-i:e.cropOldH+e.cropChangeY-c,e.cropOffsertY=s-e.cropChangeY-i<=s-c?e.cropChangeY+i:c):(e.cropH=Math.abs(i)+e.cropChangeY<=s?Math.abs(i)-e.cropOldH:s-e.cropOldH-e.cropChangeY,e.cropOffsertY=e.cropChangeY+e.cropOldH):2===e.changeCropTypeY&&(e.cropOldH+i<f?e.cropH=f:e.cropOldH+i>0?(e.cropH=e.cropOldH+i+e.cropOffsertY<=s?e.cropOldH+i:s-e.cropOffsertY,e.cropOffsertY=e.cropChangeY):(e.cropH=s-e.cropChangeY+Math.abs(i+e.cropOldH)<=s-c?Math.abs(i+e.cropOldH):e.cropChangeY-c,e.cropOffsertY=s-e.cropChangeY+Math.abs(i+e.cropOldH)<=s-c?e.cropChangeY-Math.abs(i+e.cropOldH):c))),e.canChangeX&&e.fixed){var h=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];h<f?(e.cropH=f,e.cropW=e.fixedNumber[0]*f/e.fixedNumber[1],1===e.changeCropTypeX&&(e.cropOffsertX=e.cropChangeX+(e.cropOldW-e.cropW))):h+e.cropOffsertY>s?(e.cropH=s-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],1===e.changeCropTypeX&&(e.cropOffsertX=e.cropChangeX+(e.cropOldW-e.cropW))):e.cropH=h}if(e.canChangeY&&e.fixed){var p=e.cropH/e.fixedNumber[1]*e.fixedNumber[0];p<d?(e.cropW=d,e.cropH=e.fixedNumber[1]*d/e.fixedNumber[0]):p+e.cropOffsertX>n?(e.cropW=n-e.cropOffsertX,e.cropH=e.cropW/e.fixedNumber[0]*e.fixedNumber[1]):e.cropW=p}e.$emit("cropSizing",{cropW:e.cropW,cropH:e.cropH}),e.$emit("crop-sizing",{cropW:e.cropW,cropH:e.cropH})}))},checkCropLimitSize:function(){this.cropW,this.cropH;var t=this.limitMinSize,e=new Array;return e=Array.isArray(t)?t:[t,t],[parseFloat(e[0]),parseFloat(e[1])]},changeCropEnd:function(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize:function(t,e,r,o,i,n){var s=t/e,a=i,c=n;return a<r&&(a=r,c=Math.ceil(a/s)),c<o&&(c=o,(a=Math.ceil(c*s))<r&&(a=r,c=Math.ceil(a/s))),a<i&&(a=i,c=Math.ceil(a/s)),c<n&&(c=n,a=Math.ceil(c*s)),{width:a,height:c}},endCrop:function(){0===this.cropW&&0===this.cropH&&(this.cropping=!1);var t=i(this.checkCropLimitSize(),2),e=t[0],r=t[1],o=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],e,r,this.cropW,this.cropH):{width:e,height:r},n=o.width,s=o.height;n>this.cropW&&(this.cropW=n,this.cropOffsertX+n>this.w&&(this.cropOffsertX=this.w-n)),s>this.cropH&&(this.cropH=s,this.cropOffsertY+s>this.h&&(this.cropOffsertY=this.h-s)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop:function(){this.crop=!0},stopCrop:function(){this.crop=!1},clearCrop:function(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove:function(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&2===t.touches.length)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);var e,r,o="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY;e=o-this.cropOffsertX,r=i-this.cropOffsertY,this.cropX=e,this.cropY=r,this.$emit("cropMoving",{moving:!0,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop:function(t,e){var r=this,o=0,i=0;t&&(t.preventDefault(),o="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick((function(){var t,n,s=o-r.cropX,a=i-r.cropY;if(e&&(s=r.cropOffsertX,a=r.cropOffsertY),t=s<=0?0:s+r.cropW>r.w?r.w-r.cropW:s,n=a<=0?0:a+r.cropH>r.h?r.h-r.cropH:a,r.centerBox){var c=r.getImgAxis();t<=c.x1&&(t=c.x1),t+r.cropW>c.x2&&(t=c.x2-r.cropW),n<=c.y1&&(n=c.y1),n+r.cropH>c.y2&&(n=c.y2-r.cropH)}r.cropOffsertX=t,r.cropOffsertY=n,r.$emit("cropMoving",{moving:!0,axis:r.getCropAxis()}),r.$emit("crop-moving",{moving:!0,axis:r.getCropAxis()})}))},getImgAxis:function(t,e,r){t=t||this.x,e=e||this.y,r=r||this.scale;var o={x1:0,x2:0,y1:0,y2:0},i=this.trueWidth*r,n=this.trueHeight*r;switch(this.rotate){case 0:o.x1=t+this.trueWidth*(1-r)/2,o.x2=o.x1+this.trueWidth*r,o.y1=e+this.trueHeight*(1-r)/2,o.y2=o.y1+this.trueHeight*r;break;case 1:case-1:case 3:case-3:o.x1=t+this.trueWidth*(1-r)/2+(i-n)/2,o.x2=o.x1+this.trueHeight*r,o.y1=e+this.trueHeight*(1-r)/2+(n-i)/2,o.y2=o.y1+this.trueWidth*r;break;default:o.x1=t+this.trueWidth*(1-r)/2,o.x2=o.x1+this.trueWidth*r,o.y1=e+this.trueHeight*(1-r)/2,o.y2=o.y1+this.trueHeight*r}return o},getCropAxis:function(){var t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop:function(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("cropMoving",{moving:!1,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked:function(t){var e=this,r=document.createElement("canvas"),o=new Image,i=this.rotate,n=this.trueWidth,s=this.trueHeight,a=this.cropOffsertX,c=this.cropOffsertY;function h(t,e){r.width=Math.round(t),r.height=Math.round(e)}o.onload=function(){if(0!==e.cropW){var p=r.getContext("2d"),u=1;e.high&!e.full&&(u=window.devicePixelRatio),1!==e.enlarge&!e.full&&(u=Math.abs(Number(e.enlarge)));var l=e.cropW*u,d=e.cropH*u,f=n*e.scale*u,g=s*e.scale*u,v=(e.x-a+e.trueWidth*(1-e.scale)/2)*u,m=(e.y-c+e.trueHeight*(1-e.scale)/2)*u;switch(h(l,d),p.save(),e.fillColor&&(p.fillStyle=e.fillColor,p.fillRect(0,0,r.width,r.height)),i){case 0:e.full?(h(l/e.scale,d/e.scale),p.drawImage(o,v/e.scale,m/e.scale,f/e.scale,g/e.scale)):p.drawImage(o,v,m,f,g);break;case 1:case-3:e.full?(h(l/e.scale,d/e.scale),v=v/e.scale+(f/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-f/e.scale)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,m,-v-g/e.scale,f/e.scale,g/e.scale)):(v+=(f-g)/2,m+=(g-f)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,m,-v-g,f,g));break;case 2:case-2:e.full?(h(l/e.scale,d/e.scale),p.rotate(90*i*Math.PI/180),v/=e.scale,m/=e.scale,p.drawImage(o,-v-f/e.scale,-m-g/e.scale,f/e.scale,g/e.scale)):(p.rotate(90*i*Math.PI/180),p.drawImage(o,-v-f,-m-g,f,g));break;case 3:case-1:e.full?(h(l/e.scale,d/e.scale),v=v/e.scale+(f/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-f/e.scale)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,-m-f/e.scale,v,f/e.scale,g/e.scale)):(v+=(f-g)/2,m+=(g-f)/2,p.rotate(90*i*Math.PI/180),p.drawImage(o,-m-f,v,f,g));break;default:e.full?(h(l/e.scale,d/e.scale),p.drawImage(o,v/e.scale,m/e.scale,f/e.scale,g/e.scale)):p.drawImage(o,v,m,f,g)}p.restore()}else{var w=n*e.scale,x=s*e.scale,C=r.getContext("2d");switch(C.save(),e.fillColor&&(C.fillStyle=e.fillColor,C.fillRect(0,0,r.width,r.height)),i){case 0:h(w,x),C.drawImage(o,0,0,w,x);break;case 1:case-3:h(x,w),C.rotate(90*i*Math.PI/180),C.drawImage(o,0,-x,w,x);break;case 2:case-2:h(w,x),C.rotate(90*i*Math.PI/180),C.drawImage(o,-w,-x,w,x);break;case 3:case-1:h(x,w),C.rotate(90*i*Math.PI/180),C.drawImage(o,-w,0,w,x);break;default:h(w,x),C.drawImage(o,0,0,w,x)}C.restore()}t(r)},"data"!==this.img.substr(0,4)&&(o.crossOrigin="Anonymous"),o.src=this.imgs},getCropData:function(t){var e=this;this.getCropChecked((function(r){t(r.toDataURL("image/"+e.outputType,e.outputSize))}))},getCropBlob:function(t){var e=this;this.getCropChecked((function(r){r.toBlob((function(e){return t(e)}),"image/"+e.outputType,e.outputSize)}))},showPreview:function(){var t=this;if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout((function(){t.isCanShow=!0}),16);var e=this.cropW,r=this.cropH,o=this.scale,i={};i.div={width:"".concat(e,"px"),height:"".concat(r,"px")};var n=(this.x-this.cropOffsertX)/o,s=(this.y-this.cropOffsertY)/o;i.w=e,i.h=r,i.url=this.imgs,i.img={width:"".concat(this.trueWidth,"px"),height:"".concat(this.trueHeight,"px"),transform:"scale(".concat(o,")translate3d(").concat(n,"px, ").concat(s,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,"deg)")},i.html='\n      <div class="show-preview" style="width: '.concat(i.w,"px; height: ").concat(i.h,'px; overflow: hidden">\n        <div style="width: ').concat(e,"px; height: ").concat(r,'px">\n          <img src=').concat(i.url,' style="width: ').concat(this.trueWidth,"px; height: ").concat(this.trueHeight,"px; transform:\n          scale(").concat(o,")translate3d(").concat(n,"px, ").concat(s,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,'deg)">\n        </div>\n      </div>'),this.$emit("realTime",i),this.$emit("real-time",i)},reload:function(){var t=this,e=new Image;e.onload=function(){t.w=parseFloat(window.getComputedStyle(t.$refs.cropper).width),t.h=parseFloat(window.getComputedStyle(t.$refs.cropper).height),t.trueWidth=e.width,t.trueHeight=e.height,t.original?t.scale=1:t.scale=t.checkedMode(),t.$nextTick((function(){t.x=-(t.trueWidth-t.trueWidth*t.scale)/2+(t.w-t.trueWidth*t.scale)/2,t.y=-(t.trueHeight-t.trueHeight*t.scale)/2+(t.h-t.trueHeight*t.scale)/2,t.loading=!1,t.autoCrop&&t.goAutoCrop(),t.$emit("img-load","success"),t.$emit("imgLoad","success"),setTimeout((function(){t.showPreview()}),20)}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},e.src=this.imgs},checkedMode:function(){var t=1,e=(this.trueWidth,this.trueHeight),r=this.mode.split(" ");switch(r[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":(e*=t=this.w/this.trueWidth)<this.h&&(t=(e=this.h)/this.trueHeight);break;default:try{var o=r[0];if(-1!==o.search("px")){o=o.replace("px","");var i=parseFloat(o)/this.trueWidth,n=1,s=r[1];-1!==s.search("px")&&(s=s.replace("px",""),n=(e=parseFloat(s))/this.trueHeight),t=Math.min(i,n)}if(-1!==o.search("%")&&(o=o.replace("%",""),t=parseFloat(o)/100*this.w/this.trueWidth),2===r.length&&"auto"===o){var a=r[1];-1!==a.search("px")&&(a=a.replace("px",""),t=(e=parseFloat(a))/this.trueHeight),-1!==a.search("%")&&(a=a.replace("%",""),t=(e=parseFloat(a)/100*this.h)/this.trueHeight)}}catch(e){t=1}}return t},goAutoCrop:function(t,e){if(""!==this.imgs&&null!==this.imgs){this.clearCrop(),this.cropping=!0;var r=this.w,o=this.h;if(this.centerBox){var i=Math.abs(this.rotate)%2>0,n=(i?this.trueHeight:this.trueWidth)*this.scale,s=(i?this.trueWidth:this.trueHeight)*this.scale;r=n<r?n:r,o=s<o?s:o}var a=t||parseFloat(this.autoCropWidth),c=e||parseFloat(this.autoCropHeight);0!==a&&0!==c||(a=.8*r,c=.8*o),a=a>r?r:a,c=c>o?o:c,this.fixed&&(c=a/this.fixedNumber[0]*this.fixedNumber[1]),c>this.h&&(a=(c=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(a,c)}},changeCrop:function(t,e){var r=this;if(this.centerBox){var o=this.getImgAxis();t>o.x2-o.x1&&(e=(t=o.x2-o.x1)/this.fixedNumber[0]*this.fixedNumber[1]),e>o.y2-o.y1&&(t=(e=o.y2-o.y1)/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick((function(){r.cropOffsertX=(r.w-r.cropW)/2,r.cropOffsertY=(r.h-r.cropH)/2,r.centerBox&&r.moveCrop(null,!0)}))},refresh:function(){var t=this;this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick((function(){t.checkedImg()}))},rotateLeft:function(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight:function(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear:function(){this.rotate=0},checkoutImgAxis:function(t,e,r){t=t||this.x,e=e||this.y,r=r||this.scale;var o=!0;if(this.centerBox){var i=this.getImgAxis(t,e,r),n=this.getCropAxis();i.x1>=n.x1&&(o=!1),i.x2<=n.x2&&(o=!1),i.y1>=n.y1&&(o=!1),i.y2<=n.y2&&(o=!1)}return o}},mounted:function(){this.support="onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll";var t=this,e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(e,r,o){for(var i=atob(this.toDataURL(r,o).split(",")[1]),n=i.length,s=new Uint8Array(n),a=0;a<n;a++)s[a]=i.charCodeAt(a);e(new Blob([s],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},destroyed:function(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}};r(881);var c=function(t,e,r,o,i,n,s,a){var c,h="function"==typeof t?t.options:t;if(e&&(h.render=e,h.staticRenderFns=[],h._compiled=!0),n&&(h._scopeId="data-v-"+n),c)if(h.functional){h._injectStyles=c;var p=h.render;h.render=function(t,e){return c.call(e),p(t,e)}}else{var u=h.beforeCreate;h.beforeCreate=u?[].concat(u,c):[c]}return{exports:t,options:h}}(a,t,0,0,0,"1adc23fd");const h=c.exports;var p=function(t){t.component("VueCropper",h)};"undefined"!=typeof window&&window.Vue&&p(window.Vue);const u={version:"0.6.1",install:p,VueCropper:h,vueCropper:h}})(),o})()));
 //# sourceMappingURL=index.js.map
\ No newline at end of file
diff --git a/node_modules/vue-cropper/src/vue-cropper.vue b/node_modules/vue-cropper/src/vue-cropper.vue
index d680549..ace6a88 100644
--- a/node_modules/vue-cropper/src/vue-cropper.vue
+++ b/node_modules/vue-cropper/src/vue-cropper.vue
@@ -576,7 +576,9 @@ export default {
             height = max;
           }
           this.checkOrientationImage(img, this.orientation, width, height);
-        });
+        }).catch(() =>{
+          this.checkOrientationImage(img, this.orientation, width, height);
+        })
       };
 
       img.onerror = () => {
