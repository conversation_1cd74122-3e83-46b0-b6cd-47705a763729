<template>
  <div style="height: 100%;width: 100%;">
    <div class="search">
      <el-form ref="form" :model="form" :inline="true" label-width="130px" label-suffix=":" size="mini" @submit.native.prevent>
        <el-form-item label="工序流程模板名称">
          <el-input v-model="form.templateName" placeholder="请输入工序模版名称" style="width: 220px;"></el-input>
        </el-form-item>
        <el-form-item>
          <div style="width: 200px; display: flex;">
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="left">
        <div class="header">工序流程模板</div>
        <div class="buttonGroup">
          <el-button v-if="$setAuthority('002005001', 'buttons')" type="primary" size="mini" icon="el-icon-plus" @click="handleEditProcess(0)">新增</el-button>
          <el-button v-if="$setAuthority('002005002', 'buttons')" type="success" size="mini" icon="el-icon-edit" @click="handleEditProcess(1)">修改</el-button>
          <el-button v-if="$setAuthority('002005003', 'buttons')" type="danger" size="mini" icon="el-icon-delete" @click="handleDeleteProcess">删除</el-button>
        </div>
        <el-table
          ref="table"
          :data="templateTableData" border
          size="mini" class="templateTable" height="calc(100% - 30px - 36px - 40px)"
          style="width: 100%"
          @row-click="handleRowClick"
          @select="handleSelect">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="fprocedureTemplateName" label="工序流程名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column label="配置" width="100">
            <template slot-scope="scope">
              <div style="cursor: pointer;" @click.stop="handleProductOrProjectDetail(scope.row)"><icon-svg icon-class="icon-detail" style="color: #409EFF;"></icon-svg><span style="color: #409EFF;margin-left: 10px;">详情</span></div>
            </template>
          </el-table-column>
        </el-table>
        <scroll-pane :scroll-height="36">
          <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :total="totalPage"
            style="background-color: #ffffff;width: 350px;"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
            <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
          </el-pagination>
        </scroll-pane>
      </div>
      <div class="right">
        <div class="header">工序流程详情</div>
        <div class="processName">
          {{procedureTemplateName}}
        </div>
        <el-table
          :data="detailTableData" border class="detailTable"
          size="mini"
          height="calc(100% - 30px - 36px)"
          style="width: 100%">
          <el-table-column type="index" label="序号" width="70" fixed="left"></el-table-column>
          <el-table-column prop="stepName" label="工序名称" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="stepTypeName" label="工序分类" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tatTime" label="TAT时间" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sendSampleTypeName" label="送样类型" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleTypeName" label="样本类型" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="useSampleTypeName" label="工序可用样本类型" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="weights" label="权重序号" width="100"></el-table-column>
          <el-table-column label="是否继承" width="100">
            <template slot-scope="scope">
              {{scope.row.isInherit === 1 ? '是': '否' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <save-dialog
      :pvisible="saveDialogVisible" :pdata="saveDialogData"
      @processFlowManagementSaveDialogConfirmEvent="handleSaveDialogConfirm"
      @processFlowManagementSaveDialogCloseEvent="handleSaveDialogClose"
    ></save-dialog>
    <project-configuration-dialog
      :pvisible="projectConfigurationDialogVisible"
      :pdata="projectConfigurationDialogData"
      @processFlowManagementProjectConfigurationDialogCloseEvent="handleProjectConfigurationDialogClose"
    ></project-configuration-dialog>
  </div>
</template>

<script>
import saveDialog from './processFlowManagementSaveDialog'
import projectConfigurationDialog from './processFlowManagementProjectConfigurationDialog'
import util from '../../../util/util'
export default {
  name: 'processFlowManagement',
  components: {
    saveDialog,
    projectConfigurationDialog
  },
  props: [],
  mounted () {
    this.getData()
  },
  watch: {},
  computed: {},
  data () {
    return {
      form: {
        templateName: ''
      },
      selectedRows: new Map(),
      pageSizes: [30, 50, 100],
      pageSize: 30,
      totalPage: 0,
      currentPage: 1,
      processCurrentRow: null,
      arrowIcon: 'el-icon-d-arrow-left',
      templateTableData: [],
      procedureTemplateName: '',
      procedureTemplateId: '',
      detailTableData: [],
      saveDialogVisible: false,
      saveDialogData: {
        fid: null,
        procedureTemplateName: '',
        fspecialMark: 0 // 特殊标记：默认不标记
      },
      projectConfigurationDialogVisible: false,
      projectConfigurationDialogData: {}
    }
  },
  methods: {
    // 工序流程模板列表数据查询
    getData () {
      this.$ajax({
        loadingDom: '.templateTable',
        url: '/system/procedure/list_template',
        data: {
          ftemplateName: this.form.templateName,
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          let data = result.data
          this.totalPage = data.total
          let rows = data.rows || []
          this.templateTableData = []
          rows.forEach(v => {
            let item = {
              fid: v.fid,
              fprocedureTemplateName: v.fprocedureTemplateName,
              fspecialMark: v.fspecialMark || 0 // 特殊标记 不传默认设为0
            }
            this.templateTableData.push(item)
          })
          this.detailTableData = []
          this.procedureTemplateName = ''
          this.procedureTemplateId = ''
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleReset () {
      this.form.templateName = ''
      this.handleSearch()
    },
    handleSearch () {
      this.currentPage = 1
      this.getData()
    },
    handleSizeChange (size) {
      this.pageSize = size
      this.handleSearch()
    },
    handleCurrentChange (page) {
      this.currentPage = page
      this.getData()
    },
    handleRefresh () {
      this.getData()
    },
    handleProcessDetail (row) {
      this.procedureTemplateName = row.fprocedureTemplateName
      this.procedureTemplateId = row.fid
      this.$ajax({
        loadingDom: '.detailTable',
        method: 'get',
        url: '/system/procedure/get_procedure_info',
        data: {
          fid: row.fid
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.detailTableData = []
          let item = {}
          result.data.forEach(v => {
            item = {
              stepName: v.stepName,
              stepType: v.stepType,
              stepTypeName: v.stepTypeName,
              tatTime: v.tatTime,
              sendSampleType: v.sendSampleType ? v.sendSampleType.split(',') : [],
              sendSampleTypeName: v.sendSampleTypeName,
              sampleType: v.sampleType ? v.sampleType.split(',') : [],
              sampleTypeName: v.sampleTypeName,
              useSampleType: v.useSampleType ? v.useSampleType.split(',') : [],
              useSampleTypeName: v.useSampleTypeName,
              weights: v.weights,
              isInherit: v.isInherit
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.detailTableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleProductOrProjectDetail (row) {
      this.projectConfigurationDialogData = {
        name: row.fprocedureTemplateName,
        id: row.fid
      }
      this.projectConfigurationDialogVisible = true
    },
    handleTransition () {
      let dom = document.getElementsByClassName('left')
      if (dom[0].style.width === '0px') {
        dom[0].style.width = '400px'
        this.arrowIcon = 'el-icon-d-arrow-left'
      } else {
        dom[0].style.width = '0px'
        this.arrowIcon = 'el-icon-d-arrow-right'
      }
    },
    handleSelectProcessTemplate (val) {
      if (val === 1) {
        let row = [...this.selectedRows.values()][0]
        this.handleProcessDetail(row)
      } else {
        this.procedureTemplateName = ''
        this.procedureTemplateId = null
        this.detailTableData = []
      }
    },
    handleEditProcess (type) {
      if (type === 0) {
        this.saveDialogData = {
          fid: null,
          procedureTemplateName: '',
          fspecialMark: 0
        }
        this.saveDialogVisible = true
      } else {
        if (this.selectedRows.size > 0) {
          let row = [...this.selectedRows.values()][0]
          this.saveDialogData = {
            fid: row.fid,
            procedureName: row.fprocedureTemplateName,
            fspecialMark: row.fspecialMark
          }
          this.saveDialogVisible = true
        } else {
          this.$message.error('请选择一条数据')
        }
      }
    },
    handleDeleteProcess () {
      if (this.selectedRows.size > 0) {
        let row = [...this.selectedRows.values()][0]
        this.$confirm(`是否删除${row.fprocedureTemplateName}?删除工序模板后，对应产品/项目配置工序流程为空，请谨慎删除！`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$ajax({
            loadingDom: '.templateTable',
            url: '/system/procedure/delete_template',
            method: 'get',
            data: {
              fid: row.fid
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('删除成功')
              this.handleSearch()
            } else {
              this.$message.error(result.message)
            }
          })
        }).catch(err => {
          console.log(err)
        })
      } else {
        this.$message.error('请选择工序流程模板')
      }
    },
    handleSaveDialogConfirm () {
      this.saveDialogVisible = false
      this.getData()
    },
    handleSaveDialogClose () {
      this.saveDialogVisible = false
    },
    handleProjectConfigurationDialogClose () {
      this.projectConfigurationDialogVisible = false
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row, event, column) {
      this.$refs.table.clearSelection()
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.fid))
      let hasThisId = this.selectedRows.has(row.fid)
      this.selectedRows.clear()
      if (!hasThisId) {
        this.selectedRows.set(row.fid, row)
        this.handleSelectProcessTemplate(1)
      } else {
        this.handleSelectProcessTemplate(0)
      }
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-dialog__body{
    padding: 10px 20px;
  }
  >>>.el-table th{
    background: #f2f2f2;
  }
  >>>.el-table, >>>.el-table thead{
    font-size: 12px;
  }
 .search{
   height: 49px;
   border-bottom: 1px solid #ebeef5;
   padding: 0 10px;
   display: flex;
   align-items: center;
   >>>.el-form-item--mini.el-form-item{
     margin-bottom: 0;
   }
 }
  .content{
    height: calc(100% - 30px - 10px);
    width: 100%;
    display: flex;
    .header{
      font-size: 14px;
      border-right: 2px solid #ffffff;
      padding-left: 10px;
      height: 30px;
      line-height: 30px;
    }
    .header::before{
      content: '';
      border-left: 3px solid #409EFF;
      margin-right: 10px;
    }
    .left {
      transition: width 0.28s;
      width: 340px;
      margin-right: 10px;
      height: 100%;
      .buttonGroup {
        overflow-x: hidden;
        display: flex;
        align-items: center;
        height: 40px;
      }
      >>>.el-table__header .el-checkbox {
        display: none;
      }
    }
    .right {
      //min-width: calc(100% - 350px);
      flex: 1;
      height: 100%;
      .processName{
        padding-left: 10px;
        display: flex;
        align-items: center;
        height: 40px;
      }
    }
  }
</style>
