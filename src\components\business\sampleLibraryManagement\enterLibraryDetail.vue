<template>
  <div style="">
    <h2 class="title">入库详情</h2>
    <div class="toolbar">
      <div>
        <el-button size="mini" type="primary" @click="handleAutoEnterLibrary">自动入库</el-button>
        <el-button size="mini" @click="handleShowArtificialEnterLibraryDialog">人工入库</el-button>
        <!--<el-button size="mini" @click="test">测试关闭下载</el-button>-->
      </div>
      <div>
        <el-button :disbaled="enterLibraryOrder" size="mini" type="primary" @click="handleSubmit">确认</el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      ref="sampleTable"
      style="width: 100%;"
      height="calc(100vh - 65px - 40px - 51px - 50px)"
      @select="handleSelectTable"
      @select-all="handleSelectAll"
      @row-click="handleRowClick">
      <el-table-column :selectable="checkedSourceTableCanSelect" type="selection" width="55"></el-table-column>
      <el-table-column label="样本状态" width="180" prop="fsampleStatus"></el-table-column>
      <el-table-column label="样本编号" width="180" prop="fsampleNumber"></el-table-column>
      <el-table-column label="样本类型" width="180" prop="fsampleType"></el-table-column>
      <el-table-column label="管型" width="180" prop="ftubeType"></el-table-column>
      <el-table-column label="样本量" width="180" prop="fsampleAmount"></el-table-column>
      <el-table-column label="所属实验室" width="180" prop="flab"></el-table-column>
      <el-table-column label="存储温度" width="180" prop="ftemperature"></el-table-column>
      <el-table-column label="存储位置" min-width="180" prop="fsamplePlace"></el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.frejectTime" type="text" @click.stop="handleShowRejectDialog(scope.row)">驳回</el-button>
        </template>
      </el-table-column>
    </el-table>
    <artificial-enter-library-dialog
      :pvisible="artificialEnterLibraryDialogVisible"
      :pdata="artificialEnterLibraryDialogTableData"
      @dialogCloseEvent="handleDialogClose"
      @dialogConfirmEvent="handleDialogConfirm"/>
    <reject-dialog
      :pvisible.sync="rejectDialogVisible"
      :sample-code="rejectDialogSampleCode"
      @dialogConfirmEvent="getData"/>
  </div>
</template>

<script>
// import num from './components/cc'
import artificialEnterLibraryDialog from './artificialEnterLibraryDialog'
import util from '../../../util/util'
import rejectDialog from './turnoverLibraryManagementRejectDialog'
export default {
  name: 'enterLibraryDetail',
  components: {
    rejectDialog,
    artificialEnterLibraryDialog // 人工入库组件
  },
  mounted () {
    this.addTableScrollEvent()
    this.getData()
  },
  computed: {
    enterLibraryOrder () {
      return this.$store.getters.getValue('enterLibraryOrder')
    },
    tableData () {
      return this.allTableData.slice(0, this.currentPage * this.pageSize)
    }
  },
  data () {
    return {
      // tableData: [],
      allTableData: [],
      currentPage: 1,
      pageSize: 30,
      selectedRows: new Map(),
      artificialEnterLibraryDialogVisible: false,
      rejectDialogVisible: false,
      rejectDialogSampleCode: '',
      artificialEnterLibraryDialogTableData: [] // 人工入库的id
    }
  },
  methods: {
    handleDialogClose () {
      this.artificialEnterLibraryDialogVisible = false
      this.getData()
    },
    // 为表格添加前端分页
    addTableScrollEvent () {
      let table = this.$refs.sampleTable.$el.querySelector('.el-table__body-wrapper')
      table.addEventListener('scroll', () => {
        let scrollTop = table.scrollTop
        let scrollHeight = table.scrollHeight
        let clientHeight = table.clientHeight
        if (scrollHeight - scrollTop - clientHeight < 1) {
          // this.handleGetMoreSample()
          this.currentPage++
          this.$nextTick(() => { this.selectRows() })
        }
      })
    },
    // 反选表格
    selectRows () {
      this.selectedRows.forEach(v => {
        this.$refs.sampleTable.toggleRowSelection(v, false)
      })
      this.tableData.forEach(v => {
        if (this.selectedRows.has(v.fid)) {
          this.$refs.sampleTable.toggleRowSelection(v, true)
        }
      })
    },
    // 驳回状态的行是不让选中的
    checkedSourceTableCanSelect (row) {
      return !row.frejectTime
    },
    getData () {
      if (this.enterLibraryOrder) {
        this.$ajax({
          url: '/sample/order/get_sample_list_by_order_number',
          method: 'get',
          data: {
            orderNumber: this.enterLibraryOrder
          },
          loadingDom: 'body'
        }).then(res => {
          if (res && res.code === this.SUCCESS_CODE) {
            this.allTableData = res.data || []
            this.$nextTick(() => {
              this.selectedRows.clear()
              this.allTableData.forEach(item => {
                if (!item.frejectTime) {
                  this.selectedRows.set(item.fid, item)
                }
                this.$refs.sampleTable.toggleRowSelection(item, !item.frejectTime)
              })
            })
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 点击行
    handleRowClick (row, c) {
      if (this.checkedSourceTableCanSelect(row)) {
        this.$refs.sampleTable.toggleRowSelection(row, !this.selectedRows.has(row.fid))
        this.handleSelectTable(undefined, row)
      }
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      console.log(selection)
      if (selection.length === this.allTableData.filter(row => this.checkedSourceTableCanSelect(row)).length) {
        this.allTableData.forEach((row) => {
          if (this.checkedSourceTableCanSelect(row)) {
            this.selectedRows.set(row.fid, row)
          }
        })
      }
    },
    // 自动入库
    handleAutoEnterLibrary () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择要入库的样本')
        return
      }
      let ids = [...this.selectedRows.keys()]
      this.$ajax({
        url: '/sample/order/auto_instore',
        method: 'get',
        data: {
          sampleIds: ids.toString()
        },
        loadingDom: 'body',
        loadingObject: {
          text: '正在自动入库',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.$message.success(res.message || '入库成功')
          this.selectedRows.clear()
          let inMap = new Map()
          this.allTableData.forEach((item) => {
            inMap.set(item.fid, item)
          })
          res.data.forEach(item => {
            inMap.set(item.fid, item)
          })
          this.allTableData = []
          this.allTableData.push(...inMap.values())
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 人工入库
    handleShowArtificialEnterLibraryDialog () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择要入库的样本')
        return
      }
      this.artificialEnterLibraryDialogTableData = util.deepCopy([...this.selectedRows.values()])
      this.artificialEnterLibraryDialogVisible = true
    },
    // 确认
    handleSubmit () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择要提交的行')
        return
      }
      const canSelectTableData = this.tableData
      // 必须把能选的全部选项
      if (!canSelectTableData.every(v => this.selectedRows.has(v.fid))) {
        this.$message.error('请全选提交')
        return
      }
      this.$ajax({
        url: '/sample/order/confirm_inner',
        data: {
          orderNumber: this.enterLibraryOrder,
          sampleInfoList: [...this.selectedRows.values()]
        },
        loadingDom: 'body'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('成功')
          this.handleDownloadOrder(this.enterLibraryOrder)
          this.$confirm('领取成功，正在为您下载入库申请单，请在确认下载成功后再关闭该页面', '提示', {
            confirmButtonText: '关闭页面',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showCancelButton: false,
            type: 'warning'
          }).then(() => {
            window.close()
          }).catch(() => {
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    test () {
      this.handleDownloadOrder(this.enterLibraryOrder)
      this.$confirm('领取成功，正在为您下载入库申请单，请在确认下载成功后再关闭该页面', '提示', {
        confirmButtonText: '关闭',
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showCancelButton: false,
        type: 'warning'
      }).then(() => {
        window.close()
      }).catch(() => {
      })
    },
    // 下载
    handleDownloadOrder (orderNum) {
      this.$ajax({
        url: `/sample/order/download_complete_order?orderNumber=${orderNum}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
          this.$notify({
            title: '提示',
            message: '下载成功',
            type: 'success'
          })
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    handleDialogConfirm (data) {
      this.selectedRows.clear()
      let inMap = new Map()
      this.allTableData.forEach((item) => {
        inMap.set(item.fid, item)
      })
      data.forEach(item => {
        inMap.set(item.fid, item)
      })
      this.allTableData = []
      this.allTableData.push(...inMap.values())
      this.artificialEnterLibraryDialogVisible = false
      this.getData()
    },
    handleShowRejectDialog (row) {
      if (row.fsamplePlace) {
        this.$message.error('该样本有位置存储信息，无法操作驳回。')
        return
      }
      this.rejectDialogSampleCode = row.fsampleNumber
      this.rejectDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
  .title{
    border-bottom: 1px solid #ccc;
    line-height: 50px;
    padding: 0 20px;
  }
  .toolbar{
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
  }
</style>
