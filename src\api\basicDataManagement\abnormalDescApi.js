import { myAjax } from '@/util/ajax'

/**
 * 获取异常描述配置列表
 * @param {Object} data 查询参数
 * @returns {Promise}
 */
export function getAbnormalDescList (data) {
  return myAjax({
    url: '/system/exception_config/get_exception_description_config_page',
    method: 'post',
    data
  })
}

/**
 * 获取异常描述配置详情
 * @param {String|Number} id 配置ID
 * @returns {Promise}
 */
export function getAbnormalDescDetail (data) {
  return myAjax({
    url: `/system/exception_config/get_exception_description_config_detail`,
    data: data
  })
}

/**
 * 修改异常描述配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function updateAbnormalDesc (data) {
  return myAjax({
    url: '/system/exception_config/save_or_update_exception_description_config_detail',
    method: 'post',
    data
  })
}

/**
 * 删除异常描述配置
 * @param {String|Number} id 配置ID
 * @returns {Promise}
 */
export function deleteAbnormalDesc (id) {
  return myAjax({
    url: `/system/exception_config/delete_exception_description_config`,
    data: {
      fexceptionDescriptionId: id
    }
  })
}

/**
 * 获取变量列表
 */
export function getVarListAoi (data) {
  return myAjax({
    url: `/system/exception_config/get_exception_description_params_text`,
    data: data
  })
}
