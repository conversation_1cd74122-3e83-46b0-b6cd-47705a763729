<template>
  <div class="gallery-wrap">
    <div class="toolbar">
      <div class="left">
        <el-button type="primary" size="mini" @click="refresh" :loading="loading">刷新</el-button>
        <el-button type="success" size="mini" :disabled="selectedIds.length===0" @click="emitSelect">选择({{selectedIds.length}})</el-button>
      </div>
      <div class="right">
        <el-upload
          :action="uploadAction"
          :http-request="handleUpload"
          :show-file-list="false"
          :multiple="false"
          :disabled="uploading"
        >
          <el-button size="mini" :loading="uploading">上传图片</el-button>
        </el-upload>
      </div>
    </div>

    <progress-bar :visible="uploading" :value="uploadProgress" :text="uploadText" />

    <div class="grid" ref="grid">
      <div
        v-for="img in images"
        :key="img.id || img.image_id || img._id"
        class="item"
        :class="{ selected: isSelected(idOf(img)) }"
        @click="toggleSelect(img)"
        @dblclick.stop="open(img)"
        :title="img.name || img.title || ('ID: ' + idOf(img))"
      >
        <div class="thumb">
          <img
            :data-src="thumbnailUrl(img)"
            :alt="img.name || 'thumbnail'"
            ref="thumbs"
          />
          <div class="badge" v-if="img.sizeText">{{ img.sizeText }}</div>
          <div class="check" v-if="isSelected(idOf(img))"><i class="el-icon-check"></i></div>
        </div>
        <div class="meta">
          <div class="name">{{ img.name || img.title || idOf(img) }}</div>
        </div>
      </div>

      <div v-if="!loading && images.length===0" class="empty">暂无图片</div>
      <div v-if="loading" class="loading">加载中...</div>
    </div>
  </div>
</template>

<script>
import api from '@/api/onlineImage'
import ProgressBar from '@/components/business/progress/ProgressBar.vue'

export default {
  name: 'GalleryGrid',
  components: { ProgressBar },
  props: {
    multiple: { type: Boolean, default: true }
  },
  mounted () {
    this.initObserver()
    this.refresh()
  },
  computed: {
    uploadAction () {
      return '#'
    },
    uploadText () {
      return `上传中 ${Math.round(this.uploadProgress * 100)}%`
    }
  },
  data () {
    return {
      loading: false,
      images: [],
      selectedIds: [],
      io: null,
      uploading: false,
      uploadProgress: 0
    }
  },
  beforeDestroy () {
    if (this.io) {
      try { this.io.disconnect() } catch (e) {}
      this.io = null
    }
  },
  methods: {
    idOf (img) {
      return img.id || img.image_id || img._id || img.uuid || img.ID
    },
    thumbnailUrl (img) {
      return api.getThumbnailUrl(this.idOf(img))
    },
    initObserver () {
      if ('IntersectionObserver' in window) {
        this.io = new IntersectionObserver(entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const el = entry.target
              const ds = el.getAttribute('data-src')
              if (ds && !el.src) {
                el.src = ds
              }
              this.io.unobserve(el)
            }
          })
        }, { root: this.$refs.grid, rootMargin: '200px' })
      }
    },
    observeThumbs () {
      if (!this.io) return
      this.$nextTick(() => {
        const imgs = this.$refs.thumbs || []
        imgs.forEach(el => {
          if (el && el.tagName === 'IMG') this.io.observe(el)
        })
      })
    },
    async refresh () {
      this.loading = true
      try {
        const res = await api.getImages()
        const rows = Array.isArray(res) ? res : (Array.isArray(res.data) ? res.data : (Array.isArray(res.rows) ? res.rows : []))
        this.images = rows.map(r => {
          if (r.width && r.height) r.sizeText = `${r.width}×${r.height}`
          return r
        })
        this.$emit('loaded', this.images)
        this.$nextTick(this.observeThumbs)
      } catch (e) {
        // 全局错误处理已存在
      } finally {
        this.loading = false
      }
    },
    isSelected (id) {
      return this.selectedIds.includes(id)
    },
    toggleSelect (img) {
      const id = this.idOf(img)
      if (!this.multiple) {
        this.selectedIds = [id]
      } else {
        const i = this.selectedIds.indexOf(id)
        if (i >= 0) this.selectedIds.splice(i, 1)
        else this.selectedIds.push(id)
      }
      this.$emit('select-change', [...this.selectedIds])
    },
    emitSelect () {
      const list = this.images.filter(v => this.selectedIds.includes(this.idOf(v)))
      this.$emit('confirm', list)
    },
    open (img) {
      this.$emit('open', img)
    },
    async handleUpload (options) {
      const file = options.file
      if (!file) return
      this.uploading = true
      this.uploadProgress = 0
      try {
        const ajax = (await import('@/util/ajax')).default
        await ajax.myAjax({
          url: '/api/upload',
          method: 'post',
          isFormData: true,
          data: { file },
          onUploadProgress: e => { if (e.total) this.uploadProgress = e.loaded / e.total }
        })
        this.$message.success('上传成功')
        this.refresh()
        this.$emit('uploaded')
      } catch (e) {
      } finally {
        this.uploading = false
        this.uploadProgress = 0
      }
    }
  }
}
</script>

<style scoped>
.gallery-wrap{
  display:flex;
  flex-direction:column;
  gap:8px;
  height:100%;
  color:#303133;
}
.toolbar{
  display:flex;
  justify-content:space-between;
  align-items:center;
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  padding:6px 8px;
}
.grid{
  position:relative;
  flex:1;
  overflow:auto;
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  padding:8px;
  display:grid;
  grid-template-columns:repeat(auto-fill, minmax(140px, 1fr));
  grid-auto-rows:auto;
  gap:8px;
}
.item{
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  overflow:hidden;
  cursor:pointer;
  transition:border-color .15s ease, transform .15s ease;
}
.item:hover{
  border-color:#dcdfe6;
}
.item.selected{
  border-color:#409EFF;
  box-shadow:0 0 0 1px rgba(64,158,255,.15) inset;
}
.thumb{
  position:relative;
  width:100%;
  padding-top:70%;
  background:#f5f7fa;
}
.thumb img{
  position:absolute;
  left:0; top:0; width:100%; height:100%;
  object-fit:cover;
}
.meta{
  padding:6px 8px;
  font-size:12px;
  color:#606266;
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
}
.badge{
  position:absolute;
  right:6px; top:6px;
  font-size:11px;
  color:#606266;
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:4px;
  padding:2px 6px;
}
.check{
  position:absolute;
  left:6px; top:6px;
  color:#67C23A;
  font-size:14px;
  background:#fff;
  border:1px solid #ebeef5;
  border-radius:50%;
  width:20px; height:20px;
  display:flex; align-items:center; justify-content:center;
}
.empty, .loading{
  grid-column:1 / -1;
  text-align:center;
  color:#909399;
  padding:40px 0;
}
</style>
