<template>
  <div>
    <el-dialog
      title="到样标准配置"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="720px"
      v-drag-dialog
      @opened="handleOpen"
      :before-close="handleClose">
      <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="120px" label-position="right" prefix=":" inline>
        <el-form-item label="样本类型">
          <el-input v-model.trim="form.sampleType" placeholder="请输入" style="width: 200px;" clearable maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="样本数量">
          <el-input v-model.trim="form.sampleNum" placeholder="请输入" style="width: 200px;" clearable maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="是否单样本">
          <el-input v-model.trim="form.isSingle" placeholder="请输入" style="width: 200px;" clearable maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="到样标准备注">
          <el-input v-model.trim="form.notes" placeholder="请输入" style="width: 200px;" clearable maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="组织采集时间要求" prop="organizationCollectionTime">
          <el-input v-model.trim="form.organizationCollectionTime" placeholder="请输入数字" style="width: 200px;" clearable maxlength="50">
            <template slot="append">年</template>
          </el-input>
        </el-form-item>
        <el-form-item label="血液采集时间要求" prop="bloodCollectionTime">
          <el-input v-model.trim="form.bloodCollectionTime" placeholder="请输入数字" style="width: 200px;" clearable maxlength="50">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="石蜡包埋组织送样量要求" prop="stoneWaxSample">
          <el-input v-model.trim="form.stoneWaxSample[0]" placeholder="请输入区间值，例如“5-10”" style="width: 200px;" clearable></el-input>
          -
          <el-input v-model.trim="form.stoneWaxSample[1]" placeholder="请输入区间值，例如“5-10”" style="width: 200px;" clearable></el-input>
          p
        </el-form-item>
        <el-form-item label="室内质控配对">
          <el-select v-model="form.indoorQcPair" placeholder="请选择" style="width: 200px;" clearable>
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="批量检测">
          <el-select v-model="form.batchDetection" placeholder="请选择" style="width: 200px;" clearable>
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'arriveSampleDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    productId: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      loading: false,
      form: {
        sampleType: '', // 样本类型
        sampleNum: '', // 样本数量
        isSingle: '', // 是否单样本
        notes: '', // 备注
        organizationCollectionTime: '', // 组织采集时间要求
        bloodCollectionTime: '', // 血液采集时间要求
        stoneWaxSample: ['', ''], // 石蜡包埋组织送样量要求
        indoorQcPair: '', // 室内质控配对
        batchDetection: '' // 批量检测
      },
      rules: {
        organizationCollectionTime: [
          {
            pattern: /^\d+$/,
            message: '请输入数字'
          }
        ],
        bloodCollectionTime: [
          {
            pattern: /^\d+$/,
            message: '请输入数字'
          }
        ],
        stoneWaxSample: [
          {
            validator (rule, value, callback) {
              let [min, max] = value
              const regex = /^\d+$/
              if (!min && !max) {
                callback()
              }
              if (!min || !max) {
                callback(new Error('请输入区间值，例如“5-10”'))
                return
              }
              if (!regex.test(min) || !regex.test(max)) {
                callback(new Error('请输入整数'))
                return
              }
              if (+min > +max) {
                callback(new Error('最小值不能大于最大值'))
              }
              callback()
            }
          }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.$ajax({
        url: '/system/product/get_product_sample_standard',
        method: 'get',
        data: {
          productId: this.productId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || {}
          this.form = {
            sampleType: data.sampleStandardSampleType, // 样本类型
            sampleNum: data.sampleStandardSampleCount, // 样本数量
            isSingle: data.sampleStandardIsSingleSample, // 是否单样本
            notes: data.sampleStandardRemarks, // 备注
            organizationCollectionTime: data.ftissueCollectTimeLimit, // 组织采集时间要求
            bloodCollectionTime: data.fbloodCollectTimeLimit, // 血液采集时间要求
            stoneWaxSample: [data.fminFpeSampleLimit, data.fmaxFpeSampleLimit], // 石蜡包埋组织送样量要求
            indoorQcPair: data.fisQcPair, // 室内质控配对
            batchDetection: data.fisBatchInspection // 批量检测
          }
        }
      })
    },
    async handleDialogConfirm () {
      await this.handleValidForm()
      this.loading = true
      this.$ajax({
        url: '/system/product/save_product_sample_standard',
        method: 'post',
        data: {
          productId: this.productId,
          sampleStandardSampleType: this.form.sampleType, // 样本类型
          sampleStandardSampleCount: this.form.sampleNum, // 样本数量
          sampleStandardIsSingleSample: this.form.isSingle, // 是否单样本
          sampleStandardRemarks: this.form.notes, // 备注
          ftissueCollectTimeLimit: this.form.organizationCollectionTime, // 组织采集时间要求
          fbloodCollectTimeLimit: this.form.bloodCollectionTime, // 血液采集时间要求
          fminFpeSampleLimit: this.form.stoneWaxSample[0], // 石蜡包埋组织送样量要求
          fmaxFpeSampleLimit: this.form.stoneWaxSample[1],
          fisQcPair: this.form.indoorQcPair, // 室内质控配对
          fisBatchInspection: this.form.batchDetection // 批量检测
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存成功')
          this.$emit('dialogConfirmEvent')
          this.visible = false
        } else {
          this.$message.error(result.message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
