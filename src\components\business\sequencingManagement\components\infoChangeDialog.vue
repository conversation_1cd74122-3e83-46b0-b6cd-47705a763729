<template>
  <el-dialog
    title="信息变更"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="600px"
    @open="handleOpen">
    <div class="tips">勾选样本数量: {{tableData.length}}</div>
    <vxe-table
      ref="tableRef"
      border
      resizable
      height="200px"
      show-overflow
      keep-source
      size="mini"
      :data="tableData"
      :edit-rules="validRules"
      :valid-config="{autoPos: true, showMessage: true, msgMode: 'full'}"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
    >
      <vxe-column type="seq" title="序号" width="60"></vxe-column>
      <template v-if="processType === 1">
        <vxe-column v-if="type === 0" field="sampleName" title="实验样本"></vxe-column>
        <vxe-column v-if="type === 1" field="sampleName" title="Pooling文库名称"></vxe-column>
        <vxe-column v-if="type === 0" field="concentration" title="浓度(ng/ul)" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
        <vxe-column v-if="type === 1" field="productConcentration" title="Pooling浓度" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
      </template>
      <template v-if="processType === 2">
        <vxe-column v-if="type === 0" field="sampleName" title="实验样本"></vxe-column>
        <vxe-column v-if="type === 1" field="sampleName" title="产物名称"></vxe-column>
        <vxe-column v-if="type === 0" field="concentration" title="浓度(ng/ul)" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
        <vxe-column v-if="type === 1" field="productConcentration" title="（转化）浓度" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
        <vxe-column v-if="type === 1" field="productVolumn" title="（转化）体积" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
      </template>
      <template v-if="processType === 4">
        <vxe-column v-if="type === 0" field="sampleName" title="实验样本"></vxe-column>
        <vxe-column v-if="type === 1" field="sampleName" title="产物名称"></vxe-column>
        <vxe-column v-if="type === 0" field="concentration" title="浓度(ng/ul)" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
        <vxe-column v-if="type === 1" field="productConcentration" title="（环化）浓度" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
        <vxe-column v-if="type === 1" field="productVolumn" title="（环化）体积" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
      </template>
      <template v-if="processType === 5">
        <vxe-column v-if="type === 0" field="sampleName" title="实验样本"></vxe-column>
        <vxe-column v-if="type === 0" field="concentration" title="浓度(ng/ul)" :edit-render="{name: '$input', type: 'number', props: {clearable: true}}"></vxe-column>
      </template>
    </vxe-table>
    <div class="tips">旧数据将弃用(可在样本管理中查询)，并形成新数据，确认继续么？</div>
    <el-form ref="form" :model="form" label-suffix=":" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="变更原因" prop="reason">
        <el-input v-model.trim="form.reason" size="mini" clearable maxlength="200" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提 交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from '@/util/mixins'
import {changeInfo} from '../../../../api/sequencingManagement/sequencingManagementApi'
import {awaitWrap} from '../../../../util/util'
// import VXETable from 'vxe-table'

export default {
  name: 'infoChangeDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    info: { // 变更信息
      type: Array,
      default: () => []
    },
    type: { // 当前环节状态
      type: Number,
      default: 0
    },
    processType: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      loading: false,
      validRules: {
        concentration: [
          // 必填 ，类型数字
          { required: true, message: '请输入浓度' },
          { type: 'number', message: '请输入数字' }
        ],
        productConcentration: [
          // 必填 ，类型数字
          { required: true, message: '请输入产物浓度' },
          { type: 'number', message: '请输入数字' }
        ],
        productVolumn: [
          // 必填 ，类型数字
          { required: true, message: '请输入产物体积' },
          { type: 'number', message: '请输入数字' }
        ]
      },
      tableData: [],
      form: {
        reason: ''
      },
      rules: {
        reason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.loading = false
        // 初始化数据 && 清空弹窗缓存
        this.tableData = JSON.parse(JSON.stringify(this.info))
        this.$refs.form.resetFields()
      })
    },
    async handleConfirm () {
      const $table = this.$refs.tableRef
      await Promise.all([$table.fullValidate(true), this.handleValidForm()])
      const updateRecords = $table.getUpdateRecords()
      if (updateRecords.length < 0) {
        this.$message.warning('当前数据未变更')
        return
      }
      // 更新行索引 => 查找具体变更的行
      const indexList = updateRecords
        .map(v => this.tableData.findIndex(data => v._X_ROW_KEY === data._X_ROW_KEY))
      if (indexList.length !== this.tableData.length) {
        // 查找未变更的数据
        const sampleNames = this.tableData.filter((v, index) => !indexList.includes(index)).map(v => v.sampleName).join(',')
        await this.$confirm(`${sampleNames}未变更数据，将自动跳过，确认继续吗？`, '信息变更', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
      // 设置请求参数
      const records = indexList.map((v, i) => {
        const newData = updateRecords[i] || {}
        const oldData = this.info[v] || {}
        return {
          fsampleId: newData.id,
          fconcentration: newData.concentration,
          fproductConcentration: newData.productConcentration,
          fproductVolumn: newData.productVolumn,
          fchangeJson: JSON.stringify({
            fsampleId: oldData.id,
            fconcentration: oldData.concentration,
            fproductConcentration: oldData.productConcentration,
            fproductVolumn: oldData.productVolumn
          })
        }
      })
      this.loading = true
      const {res} = await awaitWrap(
        changeInfo({tsInfoChangeVO: records, ftype: this.type, fchangeReson: this.form.reason}, this.processType)
      )
      if (res && res.code === this.SUCCESS_CODE) {
        let message = ''
        this.type === 0
          ? message = '修改成功！请注意检查被修改样本在其他环节的质控数据；'
          : message = '修改成功！后续工序-处理中的数据会同步更新，已完成的数据不会同步修改，请注意检查被修改样本在其他环节的质控数据；'
        this.$confirm(message, '信息变更', {
          confirmButtonText: '确定',
          type: 'warning'
        }).then(() => {
          this.$emit('dialogConfirmEvent')
          this.visible = false
        }).catch(() => {
          this.$emit('dialogConfirmEvent')
          this.visible = false
        })
      }
      this.loading = false
    }
  }
}
</script>

<style scoped>

</style>
