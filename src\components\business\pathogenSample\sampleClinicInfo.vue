<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" :inline-message="true" label-suffix=":" label-width="150px">
      <div class="flex">
        <el-form-item prop="clinicDisease" label="临床诊断疾病">
          <el-input v-model.trim="form.clinicDisease" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
          <span v-else>{{form.clinicDisease}}</span>
        </el-form-item>
        <el-form-item prop="symptomDesc" label="感染症状描述">
          <el-input v-model.trim="form.symptomDesc" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
          <span v-else>{{form.symptomDesc}}</span>
        </el-form-item>
      </div>
      <div class="flex">
      <el-form-item prop="WBC" label="白细胞计数(WBC)">
        <el-input v-model.trim="form.WBC" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
        <span v-else>{{form.WBC}}</span>
      </el-form-item>
      <el-form-item prop="LYM" label="淋巴细胞比率(LYM)">
        <el-input v-model.trim="form.LYM" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
        <span v-else>{{form.LYM}}</span>
      </el-form-item>
      </div>
      <div class="flex">
      <el-form-item prop="N" label="中性粒细胞比率(N)">
        <el-input v-model.trim="form.N" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
        <span v-else>{{form.N}}</span>
      </el-form-item>
      <el-form-item prop="CRP" label="C反应蛋白(CRP)">
        <el-input v-model.trim="form.CRP" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
        <span v-else>{{form.CRP}}</span>
      </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="PCT" label="降钙素原(PCT)">
        <el-input v-model.trim="form.PCT" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
          <span v-else>{{form.PCT}}</span>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="cultivateResult" label="培养结果">
          <el-radio-group v-model="form.cultivateResult" v-if="!isDetail">
            <el-radio :label="1">有</el-radio>
            <el-radio :label="0">无</el-radio>
          </el-radio-group>
          <el-input v-model.trim="form.cultivateResultNote"
                    :disabled="form.cultivateResult !== 1 || isDisabled"
                    v-if="!isDetail"
                    type="textarea"
                    autosize
                    size="mini">
          </el-input>
          <div v-else>
            {{form.cultivateResult ? '有' : '无'}}
            <div>{{form.cultivateResultNote}}</div>
          </div>
        </el-form-item>
        <el-form-item prop="mirrorResult" label="镜检结果">
          <el-radio-group v-model="form.mirrorResult" v-if="!isDetail">
            <el-radio :label="1">有</el-radio>
            <el-radio :label="0">无</el-radio>
          </el-radio-group>
          <el-input v-model.trim="form.mirrorResultNote" :disabled="form.mirrorResult !== 1 || isDisabled" v-if="!isDetail" type="textarea"  autosize size="mini"></el-input>
          <div v-else>
            {{form.mirrorResult ? '有' : '无'}}
            <div>{{form.mirrorResultNote}}</div>
          </div>
      </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="fix-line-height" prop="GTest" label="1,3-β-D-葡聚糖检测（G实验）">
            <el-radio-group v-model="form.GTest" v-if="!isDetail">
              <el-radio :label="1">有</el-radio>
              <el-radio :label="0">无</el-radio>
            </el-radio-group>
            <el-input v-model.trim="form.GTestNote" :disabled="form.GTest !== 1 || isDisabled" v-if="!isDetail" type="textarea"  autosize size="mini"></el-input>
          <div v-else>
            {{form.GTest ? '有' : '无'}}
            <div>{{form.GTestNote}}</div>
          </div>
        </el-form-item>
        <el-form-item class="fix-line-height" prop="GMTest" label="半乳糖甘露醇聚糖抗原检测（GM实验）">
          <el-radio-group  v-model="form.GMTest" v-if="!isDetail">
            <el-radio :label="1">有</el-radio>
            <el-radio :label="0">无</el-radio>
          </el-radio-group>
          <el-input v-model.trim="form.GMTestNote" :disabled="form.GMTest !== 1 || isDisabled" v-if="!isDetail" type="textarea"  autosize size="mini"></el-input>
          <div v-else>
            {{form.GMTestNote ? '有' : '无'}}
            <div>{{form.GMTestNote}}</div>
          </div>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="PCR" label="PCR">
            <el-radio-group v-model="form.PCR" v-if="!isDetail">
              <el-radio :label="1">有</el-radio>
              <el-radio :label="0">无</el-radio>
            </el-radio-group>
            <el-input v-model.trim="form.PCRNote" :disabled="form.PCR !== 1 || isDisabled" v-if="!isDetail" type="textarea"  autosize size="mini"></el-input>
          <div v-else>
            {{form.PCR ? '有' : '无'}}
            <div>{{form.PCRNote}}</div>
          </div>
        </el-form-item>
        <el-form-item prop="antigen" label="抗原/抗体">
          <el-radio-group v-model="form.antigen" v-if="!isDetail">
            <el-radio :label="1">有</el-radio>
            <el-radio :label="0">无</el-radio>
          </el-radio-group>
          <el-input v-model.trim="form.antigenNote" :disabled="form.antigen !== 1 || isDisabled" v-if="!isDetail" type="textarea"  autosize size="mini"></el-input>
          <div v-else>
            {{form.antigen ? '有' : '无'}}
            <div>{{form.antigenNote}}</div>
          </div>
      </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="otherResult" label="其他检测结果">
          <el-radio-group v-model="form.otherResult" v-if="!isDetail">
            <el-radio :label="1">有</el-radio>
            <el-radio :label="0">无</el-radio>
          </el-radio-group>
        <el-input v-model.trim="form.otherResultNote" :disabled="form.otherResult !== 1 || isDisabled" v-if="!isDetail" type="textarea"  autosize size="mini"></el-input>
          <div v-else>
            {{form.otherResult ? '有' : '无'}}
            <div>{{form.otherResultNote}}</div>
          </div>
      </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="focusPathogens" label="重点关注病原">
          <el-input v-model.trim="form.focusPathogens" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
          <div v-else>
            {{form.focusPathogens}}
          </div>
        </el-form-item>
        <el-form-item prop="medicalInfo" label="抗感染用药信息">
          <el-input v-model.trim="form.medicalInfo" :disabled="isDisabled" v-if="!isDetail" type="textarea" autosize size="mini"></el-input>
          <div v-else>{{form.medicalInfo}}</div>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item prop="startCue" label="开始治疗时间">
          <el-date-picker v-model.trim="form.startCue" :disabled="isDisabled" v-if="!isDetail" value-format="yyyy-MM-dd HH:mm:ss" size="mini"></el-date-picker>
          <div v-else>{{ form.startCue }}</div>
        </el-form-item>
        <el-form-item class="fix-line-height" prop="lastMedicalInfo" label="送检前最近一次用抗感染药时间">
          <el-date-picker v-model.trim="form.lastMedicalInfo" :disabled="isDisabled" v-if="!isDetail" value-format="yyyy-MM-dd HH:mm:ss" size="mini"></el-date-picker>
          <div v-else>{{ form.lastMedicalInfo }}</div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'sampleClinicInfo',
  props: {
    isDisabled: {
      type: Boolean,
      default: false
    },
    pathogenClinical: {
      type: Object,
      required: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    form: {
      handler (oldValue, newValue) {
        this.handleClearNote()
        this.$emit('formChange', this.form)
      },
      deep: true
    },
    pathogenClinical: {
      handler (oldValue, newValue) {
        if (this.pathogenClinical && JSON.stringify(this.pathogenClinical) !== '{}') {
          this.form = this.pathogenClinical
        }
      },
      deep: true
    }
  },
  data () {
    return {
      height: 2,
      form: {
        fid: '',
        clinicDisease: '', // 临床疾病
        symptomDesc: '', // 感染症状描述
        WBC: '', // 白细胞计数
        LYM: '', // 淋巴细胞
        N: '', // 中粒性细胞
        CRP: '', // c反应蛋白
        PCT: '', // 降钙素原
        cultivateResult: 0, // 培养结果
        cultivateResultNote: '',
        mirrorResult: 0, // 镜像结果
        mirrorResultNote: '',
        GTest: 0, // 1,3-β-D-葡聚糖检测（G实验）
        GTestNote: '',
        GMTest: 0, // 半乳糖甘露醇聚糖抗原检测（GM实验）
        GMTestNote: '',
        PCR: 0, // PCR
        PCRNote: '',
        antigen: 0, // 抗原/抗体
        antigenNote: '',
        otherResult: 0, // 其他检测结果
        otherResultNote: '',
        focusPathogens: '', // 重点关注病原
        medicalInfo: '', // 抗感染用药信息
        lastMedicalInfo: '', // 送检前最近一次用抗感染药时间
        startCue: '' // 开始治疗时间
      },
      clearKeys: [
        {key: 'cultivateResult', value: 'cultivateResultNote'},
        {key: 'mirrorResult', value: 'mirrorResultNote'},
        {key: 'GTest', value: 'GTestNote'},
        {key: 'GMTest', value: 'GMTestNote'},
        {key: 'PCR', value: 'PCRNote'},
        {key: 'antigen', value: 'antigenNote'},
        {key: 'otherResult', value: 'otherResultNote'}
      ],
      rules: {}
    }
  },
  methods: {
    // 根据有无清空备注
    handleClearNote () {
      this.$nextTick(() => {
        this.clearKeys.forEach(item => {
          if (this.form[item.key] === 0) {
            this.form[item.value] = ''
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  justify-content: space-between;
}
/deep/ .el-textarea__inner {
  border-radius: 0;
}
.fix-line-height {
  /deep/ .el-form-item__label {
    color: #409EFF;
    line-height: 21px;
  }
}
</style>
