// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import 'element-ui/lib/theme-chalk/index.css'
import ElementUI from 'element-ui'
import myAjax from './util/ajax'
import fileAjax from './util/fileAjax'
import Directives from './directives'
import ErrorDialog from './plugins/errorDialog/index.js'
import SampleDetailDialog from './plugins/sampleDetailDialog/index.js'
import sequencingErrorDialog from './plugins/sequencingErrorDialog/index.js'
import downLoadChooseDialog from './plugins/downLoadChooseDialog/index.js'
import store from '@/store/index'
import dayjs from 'dayjs'
import jsonp from 'vue-jsonp'
import Fragment from 'vue-fragment'
import constants from './util/constants'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import ElSelectV2 from 'el-select-v2'

Vue.config.productionTip = false
Vue.use(jsonp)
Vue.use(ElSelectV2)
// 为了防止fragment标签报错
Vue.use(Fragment.Plugin)
Vue.use(ElementUI)
Vue.use(ErrorDialog)
Vue.use(SampleDetailDialog)
Vue.use(sequencingErrorDialog)
Vue.use(downLoadChooseDialog)
Vue.use(VXETable)

Vue.prototype.$ajax = myAjax.myAjax
Vue.prototype.$fileAjax = fileAjax.fileAjax
Vue.prototype.$log = window.console.log
Vue.prototype.$dayjs = dayjs
Vue.prototype.SUCCESS_CODE = '200'

Vue.prototype.$setAuthority = function (code, type = 'modules') {
  let $myresource = Vue.prototype.$myresource || {
    modules: [],
    menus: [],
    buttons: []
  }
  return $myresource[type].indexOf(code) > -1 || constants.IS_TEST
}

// common文件夹内的全部组件全局注册
const globalCom = require.context('./components/common', false, /\.vue$/)
globalCom.keys().forEach(item => {
  let componentConfig = globalCom(item)
  let name = item.substring(2, item.indexOf('.vue'))
  Vue.component(
    name.charAt(0).toUpperCase() + name.slice(1),
    componentConfig.default
  )
})

// 全局指令注册
for (let key in Directives) {
  let { name, f } = Directives[key]
  Vue.directive(name, f)
}

// const m = detectZoom()
// Vue.prototype.zoomVh = window.innerHeight * (Number(m) / 100) + 'px'
// document.body.style.setProperty('--zoomVh', window.innerHeight + 'px')
/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
