<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      title="数据匹配"
      top="calc((100vh - 652.4px) / 2)"
      width="80%">
      <div>
        <el-form ref="form" :model="form" class="searchForm" label-width="120px" label-suffix="：" size="mini">
          <el-row :gutter="15">
           <el-col :span="6">
             <el-form-item label="基因">
            <el-input v-model="form.gene" placeholder="请输入"></el-input>
          </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="碱基改变">
               <el-input v-model="form.nucleotideMutation" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="氨基酸改变">
               <el-input v-model="form.aminoAcidMutation" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="药物名称">
               <el-input v-model="form.drugName" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="突变类型">
               <el-select v-model="form.mutationType" placeholder="请选择">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in mutationTypeList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="疗效相关性">
               <el-select v-model="form.curativePertinence" placeholder="请选择">
                 <el-option
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"
                   v-for="item in curativePertinenceList">
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label="肿瘤类型(匹配)">
               <el-input v-model="form.cancerClass" placeholder="请输入"></el-input>
             </el-form-item>
           </el-col>
           <el-col :span="6">
             <el-form-item label-width="40px">
               <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
               <el-button type="primary" size="mini" @click="reset">重置</el-button>
             </el-form-item>
           </el-col>
          </el-row>
        </el-form>
        <el-table
          ref="table"
          :data="tableData"
          size="mini"
          class="matchDataTable"
          height="400"
          style="width: 100%"
          @select="handleSelect"
          @row-click="handleRowClick">
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="gene" label="基因" width="100"></el-table-column>
          <el-table-column prop="nucleotideMutation" label="碱基改变" width="100"></el-table-column>
          <el-table-column prop="aminoAcidMutation" label="氨基酸改变" width="100"></el-table-column>
          <el-table-column prop="drugName" label="药物名称" width="180"></el-table-column>
          <el-table-column prop="mutationType" label="突变类型" width="140"></el-table-column>
          <el-table-column prop="curativePertinence" label="疗效相关性" width="140"></el-table-column>
          <el-table-column prop="cancerClass" label="肿瘤类型(匹配)" width="180"></el-table-column>
          <el-table-column prop="clinicTrialReview" label="药物解析(中文)" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateTime" label="修改时间" width="180"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import obj from '../../../../../util/mixins'
export default {
  name: 'matchDataDialog',
  mixins: [obj.tablePaginationCommonData],
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.form = this.pdata
        this.getData()
      } else {
        this.form = {}
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      form: {
        gene: '',
        nucleotideMutation: '',
        aminoAcidMutation: '',
        drugName: '',
        mutationType: '',
        curativePertinence: '',
        cancerClass: ''
      },
      selectedRow: new Map(),
      mutationTypeList: [
        {
          label: 'Gain of Function',
          value: 'Gain of Function'
        },
        {
          label: 'Loss of Function',
          value: 'Loss of Function'
        },
        {
          label: 'No Function',
          value: 'No Function'
        },
        {
          label: '未知',
          value: '未知'
        }
      ],
      curativePertinenceList: [
        {
          label: '提示敏感',
          value: '提示敏感'
        },
        {
          label: '提示耐药或无效',
          value: '提示耐药或无效'
        },
        {
          label: '研究结论不一致',
          value: '研究结论不一致'
        },
        {
          label: '没有关联',
          value: '没有关联'
        },
        {
          label: '未知',
          value: '未知'
        }
      ]
    }
  },
  methods: {
    getData () {
      this.$ajax({
        loadingDom: '.matchDataTable',
        url: '/read/unscramble/data_match_for_drug_analysis',
        data: {
          gene: this.form.gene,
          nucleotideMutation: this.form.nucleotideMutation,
          aminoAcidMutation: this.form.aminoAcidMutation,
          drugName: this.form.drugName,
          mutationType: this.form.mutationType,
          curativePertinence: this.form.curativePertinence,
          cancerClass: this.form.cancerClass
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRow.clear()
          this.totalPage = result.data.total
          this.tableData = result.data.rows
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSearch () {
      this.currentPage = 1
      this.getData()
    },
    reset () {
      this.form = {
        gene: '',
        nucleotideMutation: '',
        aminoAcidMutation: '',
        drugName: '',
        mutationType: '',
        curativePertinence: '',
        cancerClass: ''
      }
      this.handleSearch()
    },
    handleSelect (selection, row) {
      this.handleRowClick(row)
    },
    handleRowClick (row) {
      this.$refs.table.clearSelection()
      this.$refs.table.toggleRowSelection(row, !this.selectedRow.has(row.fid))
      let hasThisId = this.selectedRow.has(row.fid)
      this.selectedRow.clear()
      if (!hasThisId) {
        this.selectedRow.set(row.fid, row)
      }
    },
    handleClose () {
      this.$emit('matchDataDialogCloseEvent')
    },
    handleConfirm () {
      if (this.selectedRow.size !== 0) {
        let text = this.selectedRow.values().clinicTrialReview
        this.$emit('matchDataDialogConfirmEvent', text)
      } else {
        this.$message.error('请选择数据')
      }
    }
  }
}
</script>

<style scoped>
  .searchForm >>> .el-form-item {
    margin-bottom: 10px;
  }

  >>>.el-table__header .el-checkbox {
    display: none;
  }
  >>> .el-dialog {
    margin: 0 auto;
  }
  >>> .el-dialog__body {
    padding: 10px 20px;
  }
</style>
