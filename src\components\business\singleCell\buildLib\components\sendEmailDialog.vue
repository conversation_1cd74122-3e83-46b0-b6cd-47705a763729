<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      append-to-body
      width="800px"
      @opened="handleOpen">
      <el-form
        ref="form"
        v-if="visible"
        :model="form"
        class="form"
        :rules="rules"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template>
          <div class="email-content">
            <el-form-item label="邮件标题" prop="emailTitle">
              <el-input
                v-model.trim="form.emailTitle"
                maxlength="200"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item v-if="visible" label="邮件正文" prop="emailContent">
              <div class="form-width">
                <edit
                  :value="form.emailContent"
                  @input="handleEmailContentInput"/>
              </div>
            </el-form-item>
          </div>
        </template>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
import util from '@/util/util'
import {awaitWrap} from '../../../../../util/util'
import {setComputerPracticeInfo} from '../../../../../api/sequencingManagement/singleCell'

export default {
  name: `modificationDescriptionDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default () {
        return []
      }
    },
    computedInfo: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      title: '邮件信息确认',
      form: {
        emailTitle: '', // 邮件标题
        emailContent: `
            上机组同事好，<br>
             <div style="text-indent: 2em;">请安排上机。</div>
               祝好!
          ` // 邮件正文 // 邮件正文
      },
      loading: false,

      rules: {
        emailTitle: [
          {required: true, message: '邮件标题不能为空', trigger: 'blur'}
        ],
        emailContent: [
          {required: true, message: '邮件正文不能为空', trigger: 'blur'},
          // 不能只包含空格 换行
          {required: true,
            validator: (rule, value, callback) => {
              if (value.replace(/<\/?[^>]*>/g, '').replace(/[|]*\n/, '').replace(/&nbs[p]*;/gi, '').replace(/ /gi, '').length === 0) {
                callback(new Error('邮件正文不能为空'))
              } else {
                callback()
              }
            },
            trigger: ['change', 'blur']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.form = {
        emailTitle: '【上机申请】- ' + util.dateFormatter(new Date()), // 邮件标题
        emailContent: `
            上机组同事好，<br>
             <div style="text-indent: 2em;">请安排上机。</div>
               祝好!
          ` // 邮件正文
      }
      console.log(this.form.emailContent, 'handle Opened')
    },
    handleEmailContentInput (val) {
      console.log(val)
      this.form.emailContent = val
    },
    async handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          let {res = {}} = await awaitWrap(setComputerPracticeInfo({
            fnewLibIdList: this.ids,
            tsEmbarkationAttachmentHeads: this.computedInfo,
            fmailSubject: this.form.emailTitle,
            fmailContent: this.form.emailContent
          }))
          if (res && res.code === this.SUCCESS_CODE) {
            this.$message.success('邮件信息确认成功')
            this.visible = false
            this.$emit('dialogConfirmEvent')
          }
          this.loading = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-width {
  width: 600px;
}

.email-content {
  padding-top: 15px;
  height: 50vh;
  overflow: auto;
  border: 1px solid #efefef;
}

.img {
  width: 150px;
  height: 150px;
  margin: 5px;
}
</style>
