<template>
  <div class="wrapper">
    <div class="order-status-wrapper">
      <el-tabs v-model="status" class="tabs" size="mini" @tab-click="getData">
        <el-tab-pane label="全部" name="全部"></el-tab-pane>
        <el-tab-pane label="待审核" name="0"></el-tab-pane>
        <el-tab-pane label="检测中" name="1"></el-tab-pane>
        <el-tab-pane label="已完成" name="2"></el-tab-pane>
        <el-tab-pane label="已驳回" name="4"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="80px"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="样本名称">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneCode" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model.trim="form.subOrderCode" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="原始样本名称">
          <el-input v-model.trim="form.sampleName" type="textarea" :rows="5" class="form-long-width" clearable :placeholder="placeholder"/>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneCode" type="textarea" :rows="5" class="form-long-width" clearable :placeholder="placeholder"></el-input>
        </el-form-item>
        <el-form-item label="子订单号">
          <el-input v-model.trim="form.subOrderCode" type="textarea" :rows="5" class="form-long-width" clearable :placeholder="placeholder"></el-input>
        </el-form-item>
        <el-form-item label="是否合并加测">
          <el-radio-group v-model="form.fisMergeAddTesting" size="mini" class="form-width">
            <el-radio-button :label="1">加测合并</el-radio-button>
            <el-radio-button :label="0">加测不合并</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请加测时间">
          <el-date-picker
            v-model="form.applyTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            class="form-long-width"
            clearable
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item label="审核时间">
          <el-date-picker
            v-model="form.auditTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            class="form-long-width"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
      </el-form>
    </search-params-dialog>
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('019004001', 'buttons')" type="primary" size="mini" @click="handleAudit">审核通过</el-button>
      <el-button v-if="$setAuthority('019004003', 'buttons')" type="danger" plain size="mini" @click="handleOverRule">驳回</el-button>
      <template v-if="$setAuthority('019004002', 'buttons')">
        <el-button v-if="downloadLoading" type="primary" size="small" disabled><i class="el-icon-loading"></i>
          正在导出
        </el-button>
        <el-dropdown v-else @command="handleCommand" style="margin: 0 10px;">
          <el-button type="primary" size="mini" plain>数据导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
            <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" plain @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :height="tbHeight"
        :row-class-name="handleClassName"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column fixed="left" type="selection" width="55"></el-table-column>
        <el-table-column fixed="left" type="index" prop="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="left" prop="geneNum" label="吉因加编号" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">
            <div :style="`color: ${row.fisMergeAddTesting ? '#409EFF' : '#E6A23C'}`">
              {{row.geneNum}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="oriSampleName" label="原始样本名称" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="addTestOrderDataSize" label="加测数据量" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.addTestOrderDataSize" class="link" @click="handleDataDetail(scope.row)">{{ scope.row.addTestOrderDataSize }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="addTestExplain" label="加测说明" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="statusText" label="加测状态" width="100" show-overflow-tooltip key="statusList">
          <template slot-scope="scope">
            <div :style="`color: ${scope.row.statusColor}`">{{ scope.row.statusText }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="fisMergeAddTestingText" label="是否加测合并" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="addTestTypeText" label="加测类型" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="auditor" label="审核人" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="auditTime" label="审核时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="applyUser" label="申请人" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="applyTime" label="申请加测时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="subOrderCode" label="子订单号" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderTypeText" label="订单类型" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="detectType" label="产品名称" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="relinquishor" label="撤回人" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="relinquishTime" label="撤回时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="area" label="所属片区" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="cosDeliverAddTestId" label="加测id" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="addTestCount" label="第几次加测" width="100" show-overflow-tooltip></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
          当前选中 {{ selectedRowsSize }} 条记录
        </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <data-dialog :pvisible.sync="dataDialogVisible" :all-product-data-size="allProductDataSize"
    ></data-dialog>
    <audit-dialog :pvisible.sync="auditVisible" :order-type="orderType" :ids="ids" @dialogConfirmEvent="getData"/>
    <over-rule-dialog :pvisible.sync="overRuleVisible" :ids="ids"  @dialogConfirmEvent="getData"/>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import AuditDialog from './components/auditDialog.vue'
import OverRuleDialog from './components/overRuleDialog.vue'
import DataDialog from './components/dataDialog.vue'
import {
  auditAddTestInfo, auditCheckAudit,
  exportAddTestInfo,
  getAddTestList
} from '../../../../api/sequencingManagement/repeatClinicManagementApi'
export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {AuditDialog, OverRuleDialog, DataDialog},
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32 + 56, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      fid: null,
      ids: null,
      downloadLoading: false,
      auditVisible: false,
      overRuleVisible: false,
      dataDialogVisible: false,
      orderType: null,
      formData: {},
      allProductDataSize: [],
      placeholder: '请输入, 批量查询用逗号分隔，不区分中英文逗号， 或直接粘粘Excel中整行或整列数据',
      statusList: [
        // 待审核：蓝色字体
        //   检测中：正常字体
        // 已完成：绿色字体
        {
          value: 0,
          color: '#409EFF',
          label: '待审核'
        },
        {
          value: 1,
          color: '',
          label: '检测中'
        },
        {
          value: 2,
          color: '#67C23A',
          label: '已完成'
        },
        {
          value: 3,
          color: '',
          label: '已停止'
        },
        {
          value: 4,
          color: '#F56C6C',
          label: '已驳回'
        }
      ],
      typeOptions: {
        1: 'illumina文库订单',
        2: 'MGI文库订单',
        3: '组织核酸样本订单',
        5: '单细胞订单'
      },
      addTestTypes: {
        // 0文库加测，1杂交文库加测，2重提加测，3重建库加测
        0: '文库加测',
        1: '杂交文库加测',
        2: '重提加测',
        3: '重建库加测'
      },
      status: '0',
      formSubmit: {},
      form: {
        geneCode: '',
        sampleName: '',
        subOrderCode: '',
        fisMergeAddTesting: ''
      }
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    getParams () {
      const fgeneNumList = util.setGroupData(this.formSubmit.geneCode, '、', false)
      const foriSampleNameList = util.setGroupData(this.formSubmit.sampleName, '、', false)
      const fsubOrderCodeList = util.setGroupData(this.formSubmit.subOrderCode, '、', false)
      // const fauditStatusList = util.setGroupData(this.formSubmit.subOrderCode)
      const applyTime = this.formSubmit.applyTime || []
      const auditTime = this.formSubmit.auditTime || []
      return {
        fgeneNumList,
        foriSampleNameList,
        fsubOrderCodeList,
        fisMergeAddTesting: this.formSubmit.fisMergeAddTesting,
        fauditStatusList: this.status === '全部' ? null : [this.status],
        fapplyTimeStart: applyTime[0],
        fapplyTimeEnd: applyTime[1],
        fauditTimeStart: auditTime[0],
        fauditTimeEnd: auditTime[1]
      }
    },
    async getData () {
      this.clearMap()
      const params = this.getParams()
      let {res} = await awaitWrap(getAddTestList({...params,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }}, {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.records || []
        rows.forEach((v, i) => {
          const status = this.statusList.find(status => status.value * 1 === v.fauditStatus * 1) || {}
          const statusText = v.fauditStatus * 1 === 4 ? [status.label, v.fauditRejectionDescription].join(' : ') : status.label
          const statusColor = status.color
          const areaMap = {
            1: '北京',
            2: '深圳',
            3: '苏州',
            4: '上海'
          }
          const item = {
            id: v.fid,
            cosDeliverAddTestId: v.fcosDeliverAddTestId,
            geneNum: v.fgeneNum,
            applyUser: v.fapplyUser,
            applyTime: v.fapplyTime,
            oriSampleName: v.foriSampleName,
            addTestOrderDataSize: v.faddTestOrderDataSize,
            addTestCount: v.faddTestCount,
            subOrderCode: v.fsubOrderCode,
            auditStatus: v.fauditStatus,
            statusText: statusText,
            statusColor: statusColor || 'rgb(96, 98, 102)',
            auditRejectionDescription: v.fauditRejectionDescription,
            addTestExplain: v.faddTestExplain,
            addTestType: v.faddTestType,
            addTestTypeText: this.addTestTypes[v.faddTestType],
            isCreateDeliverOrder: v.fisCreateDeliverOrder,
            fisMergeAddTesting: v.fisMergeAddTesting,
            fisMergeAddTestingText: v.fisMergeAddTesting ? '加测合并' : '加测不合并',
            auditor: v.fauditor,
            relinquishor: v.frelinquishor,
            auditTime: v.fauditTime,
            relinquishTime: v.frelinquishTime,
            projectCode: v.fprojectCode,
            projectName: v.fprojectName,
            orderTypeText: this.typeOptions[v.forderType],
            orderType: v.forderType,
            detectType: v.fdetectType,
            fallProductDataSize: v.fallProductDataSize ? JSON.parse(v.fallProductDataSize) : [],
            creator: v.fcreator,
            createTime: v.fcreateTime,
            updator: v.fupdator,
            updateTime: v.fupdateTime,
            isDelete: v.fisDelete,
            area: areaMap[v.fproductionAreaId]
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    handleDataDetail (row) {
      this.allProductDataSize = row.realData.fallProductDataSize || []
      this.dataDialogVisible = true
    },
    // 审核通过
    async handleAudit () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('请选择要审核的记录')
        return
      }
      const rows = [...this.selectedRows.values()]
      // 系统判断所选记录是否都为“待审核”状态，否则给出提示『所选记录加测状态必须为“待审核”状态』
      const isAllAudit = rows.every(v => v.auditStatus === 0)
      if (!isAllAudit) {
        this.$message.error('所选记录加测状态必须为“待审核”状态')
        return
      }
      // 所选记录的订单类型必须为同一个订单类型，否则给出提示『所选记录的订单类型必须一样！』
      const orderType = rows[0].orderType
      const isAllSameOrderType = rows.some(v => [3, 5].includes(orderType) ? v.orderType !== rows[0].orderType : ![1, 2].includes(v.orderType))
      if (isAllSameOrderType) {
        this.$message.error('所选记录的订单类型必须一样！')
        return
      }
      // 检测是否有其他子订单未审核状态
      const ids = rows.map(v => v.id)
      await this.handleCheckOtherOrder(ids)
      // 判断是否是文库订单
      const isAllLib = rows.every(v => v.orderType === 1 || v.orderType === 2)
      if (isAllLib) {
        const message = `
          确认审核通过所选记录? <br/>
          此为文库类订单，审核通过后，样本将流入下单页面
        `
        await this.$confirm(message, '审核通过', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.handleAuditConfirm(ids)
        return
      }
      const isAllTissue = rows.every(v => v.orderType === 3)
      const isAllSingleCell = rows.every(v => v.orderType === 5)
      if (isAllTissue || isAllSingleCell) {
        this.orderType = rows[0].orderType
        this.ids = ids
        this.auditVisible = true
      }
    },
    // 检测是否有其他子订单未审核状态
    async handleCheckOtherOrder (ids) {
      const {res} = await awaitWrap(auditCheckAudit({
        fcosAddTestingIds: ids
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        if (res.data.length > 0) {
          const message = `
          订单${res.data.join(',')}，有其他样本待审核，是否要一起做审核？
        `
          await this.$confirm(message, '提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '忽略',
            cancelButtonText: '是',
            type: 'warning'
          })
        }
      }
    },
    // 审核通过确认
    async handleAuditConfirm (ids) {
      const {res} = await awaitWrap(auditAddTestInfo({
        fcosAddTestingIds: ids,
        fauditOrRelinquish: 0
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('审核成功')
        await this.getData()
      }
    },
    // 驳回
    handleOverRule () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('请选择要驳回的记录')
        return
      }
      const rows = [...this.selectedRows.values()]
      // 系统判断所选记录是否都为“待审核”状态，否则给出提示『所选记录加测状态必须为“待审核”状态』
      const isAllAudit = rows.some(v => v.auditStatus !== 0)
      if (isAllAudit) {
        this.$message.error('所选记录加测状态必须为“待审核”状态')
        return
      }
      this.ids = rows.map(v => v.id)
      this.overRuleVisible = true
    },
    // 选择导出类型
    handleCommand (command) {
      command === 1 ? this.handleExportAll() : this.handleExport()
    },
    // 下载文件
    async downloadExport (res) {
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 按条件导出
    async handleExportAll () {
      const params = this.getParams()
      await this.$confirm('是否确认导出查询数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const {res} = await awaitWrap(exportAddTestInfo(params))
      await this.downloadExport(res)
    },
    // 导出所选
    async handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      const selectRecords = [...this.selectedRows.values()]
      let rowsId = selectRecords.map(item => item.id)
      await this.$confirm('否确认导出选中数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const {res} = await awaitWrap(exportAddTestInfo({
        fcosAddTestingIds: rowsId
      }))
      await this.downloadExport(res)
    }
  }
}
</script>

<style scoped>

</style>
