<template>
  <div class="pdf-cropper">
    <el-upload
      class="upload-demo"
      action="#"
      multiple
      accept=".pdf"
      :auto-upload="false"
      :on-change="handleFileUpload"
      :file-list="fileList"
    >
      <el-button type="primary" :loading="isLoading">点击选择PDF文件（支持多选）</el-button>
      <div slot="tip" class="el-upload__tip">只能上传PDF文件，且不超过5MB</div>
    </el-upload>

    <el-divider content-position="left">PDF图片列表</el-divider>
    <el-row :gutter="20" class="pdf-images-container">
      <el-col :span="8" v-for="(pdfImage, index) in pdfImages" :key="index">
        <el-card class="pdf-image-card">
          <el-image :src="pdfImage" fit="contain" class="pdf-image"></el-image>
          <div class="download-btn">
            <el-button type="primary" icon="el-icon-download" @click="downloadPDF(pdfImage, index)"></el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-divider content-position="left">截图参数设置</el-divider>

    <el-row :gutter="20" class="coordinate-input">
      <el-col :span="6">
        <el-input v-model.number="cropX" type="number" placeholder="起始X坐标"></el-input>
      </el-col>
      <el-col :span="6">
        <el-input v-model.number="cropY" type="number" placeholder="起始Y坐标"></el-input>
      </el-col>
      <el-col :span="6">
        <el-input v-model.number="cropWidth" type="number" placeholder="截取宽度"></el-input>
      </el-col>
      <el-col :span="6">
        <el-input v-model.number="cropHeight" type="number" placeholder="截取高度"></el-input>
      </el-col>

      <el-col :span="24" class="action-btn">
        <el-button type="success" @click="cropImage" icon="el-icon-scissors">执行截图</el-button>
        <el-progress
          :percentage="progress"
          :status="progressStatus"
          :stroke-width="18"
          class="progress-bar"
        >
          <span>{{ progressLabel }}</span>
        </el-progress>
      </el-col>
    </el-row>

    <el-divider content-position="left">截图结果（共{{ croppedImages.length }}个）</el-divider>

    <el-row :gutter="20" class="results-container">
      <el-col :span="8" v-for="(result, index) in croppedImages" :key="index">
        <el-card class="result-card">
          <el-image :src="result" fit="contain" class="result-image"></el-image>
          <div class="download-btn">
            <el-button
              type="primary"
              icon="el-icon-download"
              @click="downloadImage(result, index)"
            >
              下载截图 {{ index + 1 }}
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import 'pdfjs-dist/build/pdf.worker.entry'

export default {
  computed: {
    progressStatus () {
      return this.progress === 100 ? 'success' : undefined
    },
    progressLabel () {
      return this.progress === 100
        ? `处理完成 (${this.croppedImages.length}个文件)`
        : `正在处理 ${this.completedTasks}/${this.totalTasks}`
    }
  },
  data () {
    return {
      fileList: [],
      pdfImages: [],
      isLoading: false,
      cropX: 100,
      cropY: 500,
      cropWidth: 950,
      cropHeight: 950,
      croppedImages: [],
      progress: 0,
      totalTasks: 0,
      completedTasks: 0
    }
  },
  methods: {
    async handleFileUpload (file, fileList) {
      this.fileList = fileList
      const files = fileList.map(f => f.raw)
      this.isLoading = true

      try {
        // 使用allSettled代替all，并添加独立错误处理
        const results = await Promise.all(files.map(async (file) => {
          try {
            const arrayBuffer = await file.arrayBuffer()
            const pdf = await pdfjsLib.getDocument(arrayBuffer).promise

            // 添加PDF基础校验
            if (pdf.numPages === 0) {
              throw new Error('PDF文件没有有效页面')
            }

            const page = await pdf.getPage(1)
            const viewport = page.getViewport({ scale: 2 })

            const canvas = document.createElement('canvas')
            canvas.width = viewport.width
            canvas.height = viewport.height

            await page.render({
              canvasContext: canvas.getContext('2d'),
              viewport
            }).promise

            return {
              status: 'success',
              data: canvas.toDataURL('image/png'),
              filename: file.name
            }
          } catch (error) {
            console.error(`文件处理失败: ${file.name}`, error)
            return {
              status: 'error',
              error: error.message,
              filename: file.name
            }
          }
        }))

        // 分离成功和失败结果
        const successFiles = results.filter(r => r.status === 'success')
        const errorFiles = results.filter(r => r.status === 'error')

        // 只保留成功转换的图片
        this.pdfImages = successFiles.map(r => r.data)
        console.log(this.pdfImages)

        // 显示处理结果
        if (errorFiles.length > 0) {
          const errorNames = errorFiles.map(f => f.filename).join(', ')
          this.$message.error({
            message: `${errorFiles.length}个文件处理失败: ${errorNames}`,
            duration: 5000
          })
        }

        if (successFiles.length > 0) {
          this.$message.success(`成功转换${successFiles.length}个PDF文件`)
        }
      } catch (error) {
        this.$message.error(`文件处理异常: ${error.message}`)
      } finally {
        this.isLoading = false
      }
    },

    async cropImage () {
      if (!this.pdfImages.length) return

      // 重置状态
      this.progress = 0
      this.completedTasks = 0
      this.totalTasks = this.pdfImages.length
      this.croppedImages = []

      // 创建处理队列（添加错误处理）
      const processQueue = this.pdfImages.map(async (pdfImage) => {
        try {
          const result = await new Promise((resolve, reject) => {
            const img = new Image()
            img.onload = () => {
              try {
                // 添加尺寸校验
                if (this.cropX + this.cropWidth > img.width ||
                  this.cropY + this.cropHeight > img.height) {
                  throw new Error('截图区域超出图片范围')
                }

                const canvas = document.createElement('canvas');
                [canvas.width, canvas.height] = [this.cropWidth, this.cropHeight]

                canvas.getContext('2d').drawImage(
                  img,
                  this.cropX, this.cropY,
                  this.cropWidth, this.cropHeight,
                  0, 0,
                  this.cropWidth, this.cropHeight
                )

                resolve(canvas.toDataURL('image/png'))
              } catch (error) {
                reject(error)
              }
            }

            // 先绑定事件再设置src
            img.onerror = () => reject(new Error('图片加载失败'))
            img.src = pdfImage
          })

          return { status: 'success', result }
        } catch (error) {
          console.error(`截图失败: ${error.message}`)
          return { status: 'error', error }
        } finally {
          // 使用原子操作更新进度
          this.completedTasks++
          this.progress = Math.round((this.completedTasks / this.totalTasks) * 100)
        }
      })

      // 等待所有任务完成
      const results = await Promise.all(processQueue)

      // 过滤有效结果
      this.croppedImages = results
        .filter(r => r.status === 'success')
        .map(r => r.result)

      // 显示完成提示（包含错误计数）
      const errorCount = results.filter(r => r.status === 'error').length
      if (errorCount > 0) {
        this.$message.warning(`成功截图${this.croppedImages.length}个，失败${errorCount}个`)
      }
    },

    downloadImage (imageData, index) {
      const link = document.createElement('a')
      link.href = imageData
      link.download = `screenshot_${index + 1}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style scoped>
.pdf-cropper {
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
}

.upload-demo {
  margin-bottom: 30px;
}

.coordinate-input {
  margin-bottom: 30px;
}

.action-btn {
  text-align: center;
  margin-top: 20px;
}

.results-container {
  margin-top: 20px;
}

.result-card {
  margin-bottom: 20px;
  transition: box-shadow 0.3s;
}

.result-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.result-image {
  height: 300px;
  width: 100%;
  background: #f5f7fa;
}

.download-btn {
  text-align: center;
  margin-top: 15px;
}

.el-divider {
  margin: 30px 0;
}
</style>
