<template>
  <div class="desc">
    <div  :key="i" v-for="(item, i) in tableData" class="card-wrapper card">
      <div>
        <span>{{ item.item }}:</span>
        <span>{{ item.itemResult }}</span>
        <el-table
          :data="item.result"
          border
          style="width: 100%">
          <el-table-column prop="fgene" label="基因" width="180"></el-table-column>
          <el-table-column prop="fsite" label="位点"></el-table-column>
          <el-table-column prop="fgenotype" label="基因型"></el-table-column>
          <el-table-column prop="fforecastResult" label="预测结果"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_nutrient_metabolism_result',
        loadingDom: '.desc',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        const info = data || []
        this.tableData = []
        info.forEach(v => {
          let item = {
            item: v.fitem,
            itemResult: v.rnutrientMetabolismResultStr,
            result: v.rnutrientMetabolismResults || []
          }
          this.tableData.push(item)
        })
      }
    }
  }
}
</script>

<style scoped>
.card {
  margin-top: 20px;
}
</style>
