<!--前三种：细菌、病毒、真菌-->
<template>
  <div>
    <el-table
      ref="table"
      border
      :data="tableData"
      style="width: 100%"
      :height="'calc(100% - 32px)'"
      class="dataFilterTable"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @row-click="handleRowClick"
      :row-class-name="tableRowClassName"
    >
      <el-table-column fixed align="right" type="index" width="45"/>
      <el-table-column fixed align="center" type="selection" width="45"/>
      <el-table-column fixed :align="getColumnType['report']" prop="report" label="是否报出" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['Confirmed']" prop="Confirmed" label="报出参考" width="120"
                       show-overflow-tooltip/>
      <el-table-column fixed :align="getColumnType['vfName']" prop="vfName"
                       label="毒力基因亚型" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button class="underline" type="text" @click.stop="handleShowImg(scope.row.realData.fdbSeq)">
            {{ scope.row[scope.column.property] }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column fixed :align="getColumnType['vfGene']" prop="vfGene" label="毒力基因"
                       width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['toxicMechanism']" prop="toxicMechanism" label="毒性机制"
                       width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['Organism']" prop="Organism"
                       label="物种" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['targetReads']" prop="targetReads"
                       label="Target_Reads" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['uniqTargetReads']" prop="uniqTargetReads"
                       label="Uniq_Target_Reads" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['SDSMRN']" prop="SDSMRN"
                       label="SDSMRN" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['targetRPMCR']" prop="targetRPMCR"
                       label="Target_RPMCR" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['uniqTargetRPMCR']" prop="uniqTargetRPMCR"
                       label="Uniq_Target_RPMCR" width="170" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['targetrRPMCR']" prop="targetrRPMCR"
                       label="Target_rRPMCR" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['RPKM']" prop="RPKM"
                       label="RPKM" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['uniqRPKM']" prop="uniqRPKM"
                       label="Uniq_RPKM" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['logRPKM']" prop="logRPKM"
                       label="logRPKM" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['geneReads']" prop="geneReads"
                       label="Gene_Reads" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['aveDepth']" prop="aveDepth"
                       label="Ave_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['medDepth']" prop="medDepth"
                       label="Med_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['modeDepth']" prop="modeDepth"
                       label="Mode_Depth" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['X1Coverage']" prop="X1Coverage"
                       label="1X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['X2Coverage']" prop="X2Coverage"
                       label="2X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['X5Coverage']" prop="X5Coverage"
                       label="5X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['X10Coverage']" prop="X10Coverage"
                       label="10X_Coverage" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['uniformityT02M']" prop="uniformityT02M"
                       label="Uniformity(T_0.2_M)" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['uniformityT05M']" prop="uniformityT05M"
                       label="Uniformity(T_0.5_M)" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['dispersionCV']" prop="dispersionCV"
                       label="Dispersion(CV)" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['historyNTCInfo']" prop="historyNTCInfo"
                       label="History_NTC_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['foldChangeInfo']" prop="foldChangeInfo"
                       label="FoldChange_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['historyCaseInfo']" prop="historyCaseInfo"
                       label="History_Case_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['runCaseInfo']" prop="runCaseInfo"
                       label="RUN_Case_Info" width="160" show-overflow-tooltip/>
      <el-table-column :align="getColumnType['freasonOfFilter']" prop="freasonOfFilter"
                       label="Reason_of_Filter" width="160" show-overflow-tooltip/>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      :total="totalPage">
      <button @click="handleRefresh">
        <icon-svg icon-class="icon-refresh"/>
      </button>
    </el-pagination>
  </div>
</template>

<script>
import util from '@/util/util'
import mixins from '@/util/mixins'
import {getTypeArray} from '../../../../../../util/util'

export default {
  name: 'DrugResistantGene',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    refresh: {
      type: Boolean
    }
  },
  mounted () {
    this.getData()
    // 处理拉动列名时，fix列的行位置与非fix列的行位置错位
    const table = document.querySelector('.el-table')
    table.addEventListener('click', async (e) => {
      await this.$nextTick()
      this.$refs.table.doLayout()
    })
  },
  beforeDestroy () {
    const table = document.querySelector('.el-table')
    if (table) {
      table.removeEventListener('click', async (e) => {
        await this.$nextTick()
        this.$refs.table.doLayout()
      })
    }
  },
  watch: {
    refresh: {
      handler (newVal) {
        if (newVal) {
          this.getData()
        }
      }
    }
  },
  data () {
    return {
      tableData: [],
      selectedRows: new Map(),
      getColumnType: []
    }
  },
  methods: {
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/get_virulence_gene',
        data: {
          analysisId: this.$route.query.oxym,
          current: this.currentPage + '',
          size: this.pageSize + ''
        },
        loadingDom: '.dataFilterTable'
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        let rows = data.rows || []
        this.totalPage = data.total
        this.selectedRows = new Map()
        rows.forEach(v => {
          let item = {
            id: v.fid,
            report: v.freport,
            Confirmed: v.fconfirmed,
            vfName: v.fname,
            vfGene: v.fgene,
            toxicMechanism: v.ftoxicMechanism,
            Organism: v.forganism,
            targetReads: v.ftargetReads,
            uniqTargetReads: v.funiqTargetReads,
            SDSMRN: v.fsdsmrn,
            targetRPMCR: v.ftargetRpmcr,
            uniqTargetRPMCR: v.funiqTargetRpmcr,
            targetrRPMCR: v.ftargetRrpmcr,
            RPKM: v.frpkm,
            uniqRPKM: v.funiqRpkm,
            logRPKM: v.flogRpkm,
            geneReads: v.fgeneReads,
            aveDepth: v.faveDepth,
            medDepth: v.fmedDepth,
            modeDepth: v.fmodeDepth,
            X1Coverage: v.f1xCoverage,
            X2Coverage: v.f2xCoverage,
            X5Coverage: v.f5xCoverage,
            X10Coverage: v.f10xCoverage,
            uniformityT02M: v.funiformity02tm,
            uniformityT05M: v.funiformity05tm,
            dispersionCV: v.fdispersionCv,
            historyNTCInfo: v.fhistoryNTCInfo,
            foldChangeInfo: v.ffoldChangeInfo,
            historyCaseInfo: v.fhistoryCaseInfo,
            runCaseInfo: v.frunCaseInfo,
            fdbSeq: v.fdbSeq,
            freasonOfFilter: v.freasonOfFilter
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
        this.getColumnType = getTypeArray(this.tableData)
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
      this.$emit('handleRowClick', this.selectedRows)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
      this.$emit('handleSelect', this.selectedRows)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.$emit('handleSelectAll', this.selectedRows)
    },
    tableRowClassName ({row, rowIndex}) {
      const obj = {
        Y: 'red',
        L: 'blue'
      }
      if (!row.report) return ''
      return obj[row.report]
    },
    handleShowImg (fdbSeq) {
      this.$emit('showImg', fdbSeq)
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-table {
  .red {
    color: #EE3838FF;
  }

  .blue {
    color: #539fff;
  }
}
.underline {
  text-decoration: underline;
}
</style>
