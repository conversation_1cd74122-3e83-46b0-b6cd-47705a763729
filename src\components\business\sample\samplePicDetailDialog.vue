<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="handleClose"
    v-drag-dialog
    title="图片详情"
    width="80%"
    @open="handleOpen"
  >
    <div class="right">
    <div v-if="imgType !== 'fixInfo'">
      <el-button size="mini" @click="handlePreviousPicture">上一张</el-button>
      <el-button size="mini" @click="handleNextPicture">下一张</el-button>
      <!--<el-button size="mini" @click="handleMagnify">放大</el-button>-->
      <!--<el-button size="mini" @click="handleShrink">缩小</el-button>-->
      <!--<el-button size="mini" @click="handleRotate">旋转</el-button>-->
      <el-button size="mini" @click="handleImageAction('zoomIn')">放大 <i class="el-icon-zoom-in"></i></el-button>
      <el-button size="mini" @click="handleImageAction('zoomOut')">缩小<i class="el-icon-zoom-out"></i></el-button>
      <el-button size="mini" @click="handleImageAction('anticlocelise')">旋转<i class="el-icon-refresh-left"></i></el-button>
      <el-button size="mini" @click="handleImageReset">还原<i class="el-icon-full-screen"></i></el-button>
      <el-button :loading="exportLoading" size="mini" @click="handleImageExport">导出</el-button>
      <el-button :loading="exportLoading" size="mini" @click="handleDelete">删除</el-button>
    </div>
    <div  class="picture">
      <!--<el-image :src="imgSrc" :preview-src-list="imgSrcList">-->
      <!--<div slot="placeholder" class="imageSlot">-->
      <!--加载中<span class="dot">...</span>-->
      <!--</div>-->
      <!--&lt;!&ndash;<div slot="error" class="imageSlot">&ndash;&gt;-->
      <!--&lt;!&ndash;<i class="el-icon-picture-outline"></i>&ndash;&gt;-->
      <!--&lt;!&ndash;</div>&ndash;&gt;-->
      <!--</el-image>-->
      <div class="image">
        <template>
          <div
            ref="imgDiv"
            v-loading="imgLoading && imgNames.length > 0"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)">
            <el-empty v-if="imgNames.length < 1" description="暂无图片"></el-empty>
            <img
              ref="img"
              :src="imgSrc"
              :style="imgStyle"
              v-if="imgNames.length > 0"
              alt=""
              style="display: block;object-fit: contain"
              @load="handleImgLoad"
              @error="handleImgError">
            <!--<img :src="imgSrc" alt="" ref="img" @load="handleImgLoad" :style="`transform: rotate(${deg}deg);`">-->
          </div>
        </template>
      </div>
    </div>
  </div>
  </el-dialog>
</template>

<script>

import mixins from '../../../util/mixins'
import util from '../../../util/util'

export default {
  name: 'sampleImgInfo',
  mixins: [mixins.dialogBaseInfo],
  props: {
    fsampleCode: {
      type: [Number, String],
      default: '',
      required: true
    }
  },
  watch: {
    imgSrc () {
      this.$nextTick(_ => {
        const $img = this.$refs.img
        if (!$img.complete) {
          this.imgLoading = true
        }
      })
    }
  },
  computed: {
    imgStyle () {
      const { scale, deg, offsetX, offsetY, enableTransition, maxLength } = this.transform
      return {
        transform: `rotate(${deg}deg)`,
        transition: enableTransition ? 'all .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`,
        width: scale * maxLength + 'px',
        height: scale * maxLength + 'px'
      }
    }
  },
  data () {
    return {
      picId: '',
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
        maxLength: 100 // 宽和高的两者比较长的那部分
      },
      exportLoading: false,
      imgNames: [],
      imgSrc: '',
      multiples: 100, // 放大或者缩小
      deg: 0, // 旋转的角度
      isDrag: false, // 是否开始拖拽
      startX: 0, // 鼠标的点击X轴
      startY: 0, // 鼠标的点击Y轴
      moveX: 0, // 鼠标移动的X轴
      moveY: 0, // 鼠标移动的Y轴
      endX: 0,
      endY: 0,
      imgWidth: 0,
      imgHeight: 0,
      imgLoading: true,
      pictureInfoSaveDialogVisible: false,
      pictureInfoSaveDialogData: {}
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    getData () {
      this.imgNames = []
      this.imgSrc = ''
      this.$ajax({
        url: '/sample/return/get_return_pic',
        method: 'get',
        data: {
          sampleNum: this.fsampleCode
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.imgNames = data.map(v => ({
            fileAbsolutePath: v.ffileAbsolutePath,
            fileName: v.ffileName,
            group: v.fdfsGroup,
            path: v.fremoteFileName,
            fid: v.fid
          }))
          this.imgSrc = this.imgNames[0].fileAbsolutePath
          this.picId = this.imgNames[0].fid
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 切换上一张图片
    handlePreviousPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      this.handleImageReset()
      if (index !== -1) {
        if (index === 0) {
          this.imgSrc = this.imgNames[this.imgNames.length - 1].fileAbsolutePath
          this.picId = this.imgNames[this.imgNames.length - 1].fid
        } else {
          this.imgSrc = this.imgNames[index - 1].fileAbsolutePath
          this.picId = this.imgNames[index - 1].fid
        }
      }
      index = this.imgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
      if (index !== -1) {
        if (index === 0) {
          this.imgSrc = this.imgNames[this.imgNames.length - 1].fileAbsolutePath
          this.picId = this.imgNames[this.imgNames.length - 1].fid
        } else {
          this.imgSrc = this.imgNames[index - 1].fileAbsolutePath
          this.picId = this.imgNames[index - 1].fid
        }
      }
    },
    // 删除图片
    handleDelete () {
      this.$ajax({
        url: '/sample/return/delete_return_pic',
        method: 'get',
        data: {
          fimgId: this.picId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getData()
        } else {
          this.$message.success(result.message)
        }
      })
    },

    // 切换下一张图片
    handleNextPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      this.handleImageReset()
      index = this.imgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
      if (index !== -1) {
        if (index === this.imgNames.length - 1) {
          this.imgSrc = this.imgNames[0].fileAbsolutePath
          this.picId = this.imgNames[0].fid
        } else {
          this.imgSrc = this.imgNames[index + 1].fileAbsolutePath
          this.picId = this.imgNames[index + 1].fid
        }
      }
    },
    // 图片的操作
    handleImageAction (action, options = {}) {
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.5,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      }
      const { transform } = this
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.5) {
            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3))
          }
          break
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3))
          break
        case 'clocelise':
          transform.deg += rotateDeg
          break
        case 'anticlocelise':
          transform.deg -= rotateDeg
          break
      }
      transform.enableTransition = enableTransition
    },
    // 还原图片
    handleImageReset () {
      this.transform = {
        ...this.transform,
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      }
    },
    // 获取图片索引
    getImgIndexAndLength (type) {
      let index = 0
      let length = 0
      length = this.imgNames.length
      if (this.imgType === type) {
        index = this.imgNames.findIndex(v => v.fileAbsolutePath === this.imgSrc)
      }
      return length === 0 ? '' : `(${index + 1} / ${length})`
    },
    // 图片放大
    handleMagnify () {
      if (this.multiples >= 300) {
        this.multiples = 300
      } else {
        this.multiples += 25
      }
      this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      this.$refs.img.style.maxHeight = '100%'
      this.$refs.img.style.height = ''
      this.$refs.img.style.maxWidth = '100%'
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },

    // 图片缩小
    handleShrink () {
      if (this.multiples <= 25) {
        this.multiples = 25
      } else {
        this.multiples -= 25
      }
      this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      this.$refs.img.style.maxHeight = '100%'
      this.$refs.img.style.height = ''
      this.$refs.img.style.maxWidth = ''
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },

    // 图片旋转
    handleRotate () {
      this.deg += 90
      if (this.deg >= 360) {
        this.deg = 0
      }
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },
    // 图片导出
    handleImageExport () {
      this.exportLoading = true
      this.$ajax({
        url: '/sample/return/download_return_pic',
        method: 'get',
        responseType: 'blob',
        data: {
          sampleNum: this.fsampleCode
        }
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    handleImgLoad () {
      this.imgLoading = false
      this.transform.maxLength = this.$refs.imgDiv.clientWidth / 2
    },
    handleImgError (e) {
      this.imgLoading = false
      e.target.alt = '加载失败'
    }
  }
}
</script>

<style scoped lang="scss">
.right{
  height: 60vh;
  border-left: 1px solid #DCDFE6;
  padding: 0 5px;
  flex: 1;
  overflow-y: auto;
  .picture{
    margin: 10px auto;
    width: 90%;
    height: calc(100% - 54px - 28px - 20px);
    .image{
      margin: 30px 0;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }
  .title{
    height: 30px;
    line-height: 30px;
    font-size: 13px;
  }
}
</style>
