<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      :title="title"
      width="1500px"
      @open="handleOpen">
      <!--搜索-->
      <div class="flex">
        <el-select v-model.trim="searchType" style="width: 144px!important" size="mini" placeholder="请选择" @change="handleSearchChange">
          <el-option
            :key="item.value"
            :label="item.label"
            :value="item"
            v-for="item in optionsList">
          </el-option>
        </el-select>
        <div style="margin-left: 20px">
          <el-select
            v-model.trim="searchValue"
            :disabled="!searchType.value"
            v-if="searchType.type === 'select'"
            size="mini"
            clearable
            filterable
            placeholder="请选择"
            @keyup.enter.native="handleSearch"
          >
            <el-option
              :key="index"
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in typeOptions[searchType.value]">
            </el-option>
          </el-select>
          <el-input
            v-model.trim="searchValue"
            :disabled="!searchType.value"
            v-if="searchType.type === 'input'"
            size="mini"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleSearch"></el-input>
          <el-date-picker
            v-model="searchValue"
            :disabled="!searchType.value"
            :default-time="['00:00:00', '23:59:59']"
            v-if="searchType.type === 'date'"
            type="datetimerange"
            clearable
            size="mini"
            prefix-icon="el-icon-date"
            range-separator="~"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="input-width"
            style="width: 330px"
            @keyup.enter.native="handleSearch">
          </el-date-picker>
        </div>
        <el-button type="primary" size="mini" style="margin-left: 20px" @click="handleSearch">查询</el-button>
      </div>

      <div class="line"></div>

      <el-table
        :data="tableData"
        style="width: 100%;"
        height="400px"
        class="table"
        border>
        <el-table-column type="selection" fixed="left" width="55"></el-table-column>
        <el-table-column prop="oldSampleName"  label="样本名称" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="geneSampleNum" label="吉因加编号" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleTypeName" label="样本类型" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleStatusName" label="样本状态" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="t7ProjectCode" label="项目编号" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="t7ProjectName" label="项目名称" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="storageTime" label="到样时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originalExpireTime" label="原始样本到期时间" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="extractExpireTime" label="核酸提取物到期时间" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="applySampleTypeName" label="处理类别" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="plasmaRsNum" label="外周血产物编号" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="extractDnaNum" label="提取产物编号" min-width="180" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :current-page.sync="currentPage"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper, slot"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util from '@/util/util'

export default {
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  props: {
    orderId: {
      type: Number
    },
    orderNum: {
      type: String
    },
    orderName: {
      type: String
    }
  },
  data () {
    return {
      tableData: [],
      title: '',
      optionsList: [
        // {
        //   label: '子订单编号',
        //   value: 0
        // },
        {
          label: '样本名称',
          value: 'oldSampleName',
          type: 'input'
        }, {
          label: '吉因加编号',
          value: 'geneSampleNum',
          type: 'input'
        }, {
          label: '样本类型',
          value: 'sampleType',
          type: 'input'
        }, {
          label: '样本状态',
          value: 'cosSampleStatusList',
          type: 'select'
        }, {
          label: '项目名称',
          value: 'projectName',
          type: 'input'
        }, {
          label: '项目编号',
          value: 'projectCode',
          type: 'input'
        },
        {
          label: '提取产物编号',
          value: 'extractDnaNum',
          type: 'input'
        }, {
          label: '到样时间',
          value: 'storageTime',
          type: 'date'
        }, {
          label: '原始样本到期时间',
          value: 'originalExpireTime',
          type: 'date'
        }, {
          label: '核酸提取物到期时间',
          value: 'extractExpireTime',
          type: 'date'
        }
      ],
      typeOptions: {
        sampleTypeList: [
        ],
        cosSampleStatusList: []
      },
      searchType: {
        type: 'input'
      },
      searchValue: ''
    }
  },
  methods: {
    handleOpen () {
      this.title = this.orderNum + '-' + this.orderName
      this.searchValue = ''
      this.searchType = {
        type: 'input'
      }
      this.getData()
      this.getSampleTypeList()
      this.getSampleStatusList()
    },
    handleSearchChange () {
      this.searchValue = ''
    },
    // 获取样本类型列表
    getSampleTypeList () {
      this.$ajax({
        method: 'post',
        url: '/sample/t7_return/list_handle_sample_type'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.typeOptions.sampleTypeList = []
          data.forEach(v => {
            let item = {
              label: v.name,
              value: v.code
            }
            this.typeOptions.sampleTypeList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取样本类型列表
    getSampleStatusList () {
      this.$ajax({
        method: 'post',
        url: '/sample/t7_return/list_cos_sample_status'
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.typeOptions.cosSampleStatusList = []
          data.forEach(v => {
            let item = {
              label: v.name,
              value: v.code
            }
            this.typeOptions.cosSampleStatusList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSearch () {
      this.currentPage = 1
      this.getData()
    },
    // 获取样本列表
    getData () {
      // 格式化处理参数
      let params = {}
      let searchType = this.searchType || {}
      let key = searchType.value
      if (key) {
        params = this.setParams(key)
      }
      this.$ajax({
        url: '/sample/t7_return/page_t7_return_sample',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {
            returnOrderId: this.orderId,
            ...params
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleReturnId,
              sampleConfirmId: v.sampleConfirmId,
              geneSampleNum: v.geneSampleNum,
              oldSampleName: v.oldSampleName,
              sampleTypeCode: v.sampleTypeCode,
              sampleTypeName: v.sampleTypeName,
              productCode: v.productCode,
              productName: v.productName,
              t7OrderCode: v.t7OrderCode,
              t7ProjectCode: v.t7ProjectCode,
              t7ProjectName: v.t7ProjectName,
              storageTime: v.storageTime,
              applySampleType: v.applySampleTypeCode,
              applySampleTypeName: v.applySampleTypeName,
              sampleStatus: v.sampleStatus,
              sampleStatusName: v.sampleStatusName,
              originalExpireTime: v.originalExpireTime,
              extractExpireTime: v.extractExpireTime,
              plasmaRsNum: v.plasmaRsNum,
              extractDnaNum: v.extractDnaNum
            }
            item.realData = v
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    setParams (key) {
      let searchValue = this.searchValue
      if (Array.isArray(this.searchValue) && this.searchValue.length === 0) {
        searchValue = null
        return {}
      }
      if (['cosSampleStatusList'].includes(key)) {
        searchValue || searchValue === 0 ? searchValue = [searchValue] : searchValue = []
      }
      let params = {
        [key]: searchValue
      }
      let options = {
        'storageTime': {
          start: 'storageTimeStart',
          end: 'storageTimeEnd'
        },
        'originalExpireTime': {
          start: 'originalExpireTimeStart',
          end: 'originalExpireTimeEnd'
        },
        'extractExpireTime': {
          start: 'extractExpireTimeStart',
          end: 'extractExpireTimeEnd'
        }
      }
      if (!searchValue) return {}
      let keyObj = options[key]
      if (keyObj) {
        params = {
          [keyObj.start]: this.searchValue[0],
          [keyObj.end]: this.searchValue[1]
        }
      }
      return params
    }
  }
}
</script>

<style scoped>
.flex {
  display: flex
}

.line {
  margin: 15px 0;
  border-bottom: 1px solid #eee;
}
</style>
