// 测序管理相关路由
export default [
  {
    path: '/business/view/unTestSampleManagement',
    meta: {
      title: '待测样本管理'
    },
    component: () => import('@/components/business/sequencingManagement/unTestSampleManagement/overview.vue')
  },
  {
    path: '/business/view/scheduleTaskManagement',
    meta: {
      title: '排单任务管理'
    },
    component: () => import('@/components/business/sequencingManagement/scheduleTaskManagement/overview.vue')
  },
  {
    path: '/business/view/pooling',
    meta: {
      title: 'pooling'
    },
    component: () => import('@/components/business/sequencingManagement/pooling/overview.vue')
  },
  {
    path: '/business/view/translation',
    meta: {
      title: '转化'
    },
    component: () => import('@/components/business/sequencingManagement/translation/overview.vue')
  },
  {
    path: '/business/view/cyclization',
    meta: {
      title: '环化'
    },
    component: () => import('@/components/business/sequencingManagement/cyclization/overview.vue')
  },
  {
    path: '/business/view/makeDNB',
    meta: {
      title: 'makeDNB'
    },
    component: () => import('@/components/business/sequencingManagement/makeDNB/overview.vue')
  },
  {
    path: '/business/view/errorManagement',
    meta: {
      title: '上机-异常处理'
    },
    component: () => import('@/components/business/sequencingManagement/errorManagement/overview.vue')
  },
  {
    path: '/business/view/singleCell/dissociation',
    meta: {
      title: '解离'
    },
    component: () => import('@/components/business/singleCell/dissociation/index.vue')
  },
  {
    path: '/business/view/singleCell/buildLib',
    meta: {
      title: '建库'
    },
    component: () => import('@/components/business/singleCell/buildLib/index.vue')
  },
  {
    path: '/business/view/singleCell/smapleMonitoring',
    meta: {
      title: '单细胞样本监控'
    },
    component: () => import('@/components/business/singleCell/smapleMonitoringManagement/index.vue')
  },
  {
    path: '/business/view/singleCell/dataDelivery',
    meta: {
      title: '单细胞数据交付'
    },
    component: () => import('@/components/business/singleCell/deliveryManagement/index.vue')
  },
  {
    path: '/business/subpage/sequencingManagement/sampleProcessDetail',
    meta: {
      title: '样本进度详情'
    },
    component: () => import('@/components/business/sequencingManagement/scheduleTaskManagement/sampleProcessDetail/overview.vue')
  },
  // 质控任务单导出
  {
    path: '/business/subpage/qualityControlTaskExport',
    meta: {
      title: '质控任务单导出'
    },
    component: () => import('@/components/business/experiment/qualityControlTaskExport/index.vue')
  }
]
