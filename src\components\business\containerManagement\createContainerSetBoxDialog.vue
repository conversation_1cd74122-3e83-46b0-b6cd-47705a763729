<template>
  <div>
    <el-dialog
        :title="`设置${changeText('盒')}`"
        :close-on-click-modal="false"
        :visible.sync="visible"
        :before-close="handleClose"
        width="750px"
        @open="handleOpen">
      <div>
        <div class="form">
          <div class="form-item">
            <label>容器名称：</label>
            <el-input :style="{width: formWidth}" v-model="currentOperateName" disabled size="mini"></el-input>
          </div>
          <div class="form-item">
            <label>添加{{changeText('盒')}}数：</label>
            <el-select v-model="boxTotal" :style="{width: formWidth}" size="mini" filterable @change="handleChangeBoxNum">
              <template v-for="item in maxBoxNum">
                <el-option :key="item" :label="item" :value="item"></el-option>
              </template>
            </el-select>
          </div>
          <template v-if="boxTotal">
            <template v-for="(item, index) in boxSampleTypeInput">
              <div :key="index" class="form-item">
                <label>{{changeText('盒')}}数范围：</label>
                <el-input :style="{width: formWidth}" v-model="item.boxNums" size="mini" @blur="handleChangeBoxInfo(index, 'boxNums')"></el-input>
              </div>
              <div :key="index + 'sample'" class="form-item">
                <label>样本类型：</label>
                <el-select
                  v-model="item.sampleType"
                  :disabled="item.boxNums.length === 0"
                  :style="{width: formWidth}"
                  size="mini"
                  multiple
                  filterable
                  @change="handleChangeBoxInfo(index, 'sampleType')">
                  <template v-for="item in sampleTypeOptions">
                    <el-option :key="item.value" :label="item.value" :value="item.value">
                      <span style="float: left">{{ item.value }}</span>
<!--                      <span style="float: right; color: #8492a6; font-size: 13px;padding-right: 16px;">{{ item.pipeList ? item.pipeList.join('、') : '' }}</span>-->
                    </el-option>
                  </template>
                </el-select>
              </div>
              <div :key="index + 'icon'">
                <i
                  v-if="index === boxSampleType.length - 1 && showAddIcon"
                  class="el-icon-plus icon"
                  style="font-size: 30px;font-weight: 600;cursor: pointer;"
                  @click="handleAddBoxSampleType"></i>
              </div>
            </template>
          </template>
        </div>
        <template v-if="box.length > 0">
          <div class="box">
            <template v-for="item in box">
              <div :key="item.num" class="box-item">
                <p>{{item.num}}{{changeText('盒')}}</p>
                <p>{{item.sampleTypes.toString()}} </p>
              </div>
            </template>
          </div>
        </template>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    currentOperateName: { // 当前名称
      type: String
    },
    sampleTypeOptions: { // 样本值
      type: Array,
      default () {
        return []
      }
    },
    currentSetObj: {
      type: Object
    },
    containerData: { // 全部数据
      type: Array
    },
    currentShelf: { // 当前架
      type: Array
    },
    containerType: {
      type: String
    }
  },
  watch: {
    boxSampleType (newVal) {
      let box = []
      newVal.forEach(item => {
        if (item.boxNums.length > 0) {
          item.boxNums.forEach(v => {
            let vv = {
              num: v,
              sampleTypes: item.sampleType
            }
            box.push(vv)
          })
        }
      })
      function compare (v1, v2) { // 按盒数大小重排序
        if (v1.num < v2.num) {
          return -1
        } else if (v1.num > v2.num) {
          return 1
        } else {
          return 0
        }
      }
      this.box = box.sort(compare)
    }
  },
  computed: {
    showAddIcon () {
      let allComplete = this.boxSampleType.every(item => {
        return item.boxNums.length > 0
      })
      if (!allComplete) return false // 存在没有选择添加盒数项，则无法再次添加项
      let allBoxs = []
      this.boxSampleType.forEach(item => {
        allBoxs.push(...item.boxNums)
      })
      let sets = new Set(allBoxs)
      return [...sets].length < this.boxTotal
    },
    hasSelectBox () { // 已选盒数，防止重复选择
      let allBoxs = []
      this.boxSampleType.forEach(item => {
        allBoxs.push(...item.boxNums)
      })
      let sets = new Set(allBoxs)
      return [...sets]
    }
  },
  data () {
    return {
      maxBoxNum: 99,
      boxTotal: 0,
      formWidth: '220px',
      box: [], // {num: '', sampleTypes: []}，就两个提交的时候再对比
      sampleTypePipeMap: new Map(),
      boxSampleTypeInput: [
        {boxNums: '', sampleType: []}
      ],
      boxSampleTypeInputCorrect: [
        {boxNums: '', sampleType: []}
      ],
      boxSampleType: [
        {boxNums: [], sampleType: []}
      ]
    }
  },
  methods: {
    // 打开弹窗初始化
    handleOpen () {
      this.$nextTick(() => {
        let formInput = this.currentSetObj.formInput || {}
        let content = this.currentSetObj.children || []
        this.boxTotal = content.length || 0
        this.boxSampleTypeInput = [
          {boxNums: '', sampleType: []}
        ]
        this.boxSampleTypeInputCorrect = [
          {boxNums: '', sampleType: []}
        ]
        this.boxSampleType = [
          {boxNums: [], sampleType: []}
        ]
        if (formInput.boxSampleType) {
          this.boxSampleType = formInput.boxSampleType
          this.boxSampleTypeInput = []
          this.boxSampleTypeInputCorrect = []
          this.boxSampleType.forEach(item => {
            let v = {
              boxNums: item.boxNums.toString(),
              sampleType: item.sampleType
            }
            this.boxSampleTypeInputCorrect.push(util.deepCopy(v))
            this.boxSampleTypeInput.push(v)
          })
        }
        this.box = []
        content.forEach(item => {
          let v = {
            num: +item.num,
            sampleTypes: item.sampleTypes
          }
          this.box.push(v)
        })
        this.sampleTypePipeMap = new Map()
        this.sampleTypeOptions.forEach(v => {
          this.sampleTypePipeMap.set(v.value, v.pipeList)
        })
      })
    },
    handleAddBoxSampleType () {
      this.boxSampleType.push({boxNums: [], sampleType: []})
      this.boxSampleTypeInput.push({boxNums: '', sampleType: []})
      this.boxSampleTypeInputCorrect.push({boxNums: '', sampleType: []})
    },
    // 是否有相同管型，不同的管型样本类型不能选择
    hasSamePipe (index, pipeList = []) {
      let sampleTypes = this.boxSampleTypeInput[index].sampleType
      // [ [9*9]，[7*7] ]
      let hasChoosePipeList = []
      sampleTypes.forEach(v => {
        hasChoosePipeList.push(this.sampleTypePipeMap.get(v) || [])
      })
      return !pipeList.some(v => {
        return hasChoosePipeList.every(vv => vv.includes(v))
      })
    },
    // 改变盒数
    handleChangeBoxNum (val) {
      // 在架数减少的时候进行
      if (val < this.box.length) {
        let canModify = true // 可以修改，为true时将处理过的shelfSampleType赋值过去
        let boxSampleType = JSON.parse(JSON.stringify(this.boxSampleType))
        for (let i = 0; i < boxSampleType.length; i++) {
          let v = boxSampleType[i]
          let bigNum = v.boxNums.filter(num => {
            return num > val
          })
          console.log('bigNum', bigNum)
          if (bigNum.length > 0) {
            for (let ii = 0; ii < bigNum.length; ii++) {
              let vv = bigNum[ii]
              /**
               * 这里加判断条件，判断是否可以修改
               * **/
              let index = v.boxNums.indexOf(vv)
              if (index > -1) {
                v.boxNums.splice(index, 1)
              }
            }
          }
        }
        console.log(boxSampleType)
        if (canModify) {
          // 去除没有数量的空对象
          boxSampleType = boxSampleType.filter(item => {
            return item.boxNums.length > 0
          })
          this.boxSampleTypeInput = []
          this.boxSampleTypeInputCorrect = []
          boxSampleType.forEach(item => {
            let v = {
              boxNums: item.boxNums.toString(),
              sampleType: item.sampleType
            }
            this.boxSampleTypeInputCorrect.push(util.deepCopy(v))
            this.boxSampleTypeInput.push(v)
          })
          this.boxSampleType = boxSampleType
        }
      }
    },
    // 改变盒数信息
    handleChangeBoxInfo (index, filedName) {
      let resetFormInput = (i = index) => {
        this.$set(this.boxSampleTypeInput, i, util.deepCopy(this.boxSampleTypeInputCorrect[i]))
      }
      let data = util.deepCopy(this.boxSampleType[index])
      if (filedName === 'sampleType') {
        this.boxSampleTypeInputCorrect = util.deepCopy(this.boxSampleTypeInput)
        data.sampleType = this.boxSampleTypeInputCorrect[index].sampleType
        this.$set(this.boxSampleType, index, data)
      } else if (filedName === 'boxNums') {
        if (this.boxSampleTypeInput[index].boxNums) {
          let boxNumsSet = new Set()
          let boxNumsInput = JSON.parse(JSON.stringify(this.boxSampleTypeInput[index])).boxNums.trim()
          let regx = /[1-9]([\s+,，\\-]*[0-9])*$/
          if (regx.test(boxNumsInput)) {
            let dataBoxNums = util.deepCopy(data.boxNums)
            data.boxNums = []
            this.$set(this.boxSampleType, index, data)
            this.$nextTick(() => {
              // 将所有空格、中文逗号都变为英文逗号
              let input = boxNumsInput.replace(/\s+/g, ',').replace(/，/, ',').replace(/(\s+-\s+)|(\s+-)|(-\s+)/, '-')
              // 转换字符串为数组,去除空项
              let f = input.split(',').filter(item => { return item })
              let numCorrect = true
              for (let i = 0; i < f.length; i++) {
                if (f[i].indexOf('-') > -1) {
                  let fArr = f[i].split('-').filter(item => { return item })
                  if (fArr.length !== 2) {
                    this.$message.error('输入格式不正确')
                    numCorrect = false
                    break
                  } else {
                    let correctNum = fArr.every(v => {
                      let num = +v
                      return !Number.isNaN(num) && num > 0 && num <= this.boxTotal
                    })
                    if (correctNum) {
                      let arr = fArr.map(v => { return +v })
                      let max = Math.max(...arr)
                      let min = Math.min(...arr)
                      if (max <= this.boxTotal && min > 0) {
                        let foolA = []
                        do {
                          foolA.push(min)
                          min++
                        } while (min <= max)
                        let hasSelect = foolA.filter(item => {
                          return this.hasSelectBox.indexOf(item) > -1
                        })
                        if (hasSelect.length > 0) {
                          this.$message.error(`${hasSelect.toString()}${this.changeText('盒')}已被设置`)
                          numCorrect = false
                          break
                        } else {
                          foolA.forEach(item => {
                            boxNumsSet.add(item)
                          })
                        }
                      } else {
                        this.$message.error(`请确保输入的值在1-${this.boxTotal}之间`)
                        numCorrect = false
                        break
                      }
                    } else {
                      this.$message.error('输入格式不正确')
                      numCorrect = false
                      break
                    }
                  }
                } else {
                  let num = +f[i]
                  if (!Number.isNaN(num) && num > 0 && num <= this.boxTotal) {
                    if (this.hasSelectBox.indexOf(num) > -1) {
                      this.$message.error(`${f[i]}${this.changeText('盒')}已被设置`)
                      return
                    }
                    boxNumsSet.add(num)
                  } else {
                    this.$message.error('输入格式不正确')
                  }
                }
              }
              if (numCorrect) {
                this.boxSampleTypeInputCorrect = util.deepCopy(this.boxSampleTypeInput)
                data.boxNums = [...boxNumsSet]
                this.$set(this.boxSampleType, index, data)
              } else {
                data.boxNums = dataBoxNums
                this.$set(this.shelfSampleTypeInputCorrect, index, data)
                resetFormInput(index)
              }
            })
          } else {
            resetFormInput(index)
            this.$message.error('输入格式不正确')
          }
        } else {
          resetFormInput(index)
          // this.boxSampleTypeInput[index].boxNums = this.boxSampleType[index].boxNums.toString()
        }
      }
    },
    // 判断两个数组对象是否相同
    judgeArray (arr1, arr2) {
      if (arr1.length !== arr2.length) return false
      return arr1.every(item => {
        return arr2.includes(item)
      })
    },
    handleConfirm () {
      let allSelected = this.box.every(item => {
        return item.sampleTypes && item.sampleTypes.length > 0
      })
      if (this.boxTotal > 0 && this.box.length === this.boxTotal && allSelected) {
        let hasSetboxData = this.currentSetObj.content || []
        let box = []
        // 如果盒数变化，则意味着所有数据重新重置，以新数据为主
        if (hasSetboxData.length !== this.box.length) {
          this.box.forEach(item => {
            let v = {
              tag: '盒',
              filed: 'box',
              num: item.num,
              sampleTypes: item.sampleTypes
            }
            v.children = {}
            box.push(v)
          })
        } else { // 如果盒数未变化，则对比样本类型的变化
          let hasSetBoxMap = new Map()
          hasSetboxData.forEach(item => {
            hasSetBoxMap.set(+item.num, item)
          })
          this.box.forEach(item => {
            let v = {
              tag: '盒',
              filed: 'box',
              num: +item.num,
              sampleTypes: item.sampleTypes
            }
            let isChangeSampleType = this.judgeArray(item.sampleTypes, hasSetBoxMap.get(+item.num).sampleTypes)
            v.children = isChangeSampleType ? {} : hasSetBoxMap.get(item.num).children
            box.push(v)
          })
        }
        let formInput = {
          name: this.currentOperateName,
          total: this.boxTotal,
          boxSampleTypeInput: this.boxSampleTypeInput,
          boxSampleType: this.boxSampleType
        }
        this.$emit('dialogConfirmEvent', {formInput, content: box})
      } else {
        this.$message.error(`${this.changeText('盒')}未设置完整`)
      }
    },
    // 通过判断lab改变文字显示
    changeText (text) {
      let result = text
      if (this.containerType === 'C') {
        switch (text) {
          case '层':
            result = '组'
            break
          case '架':
            result = '抽屉'
            break
          case '盒':
            result = '道'
            break
        }
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  .form{
    display: flex;
    flex-wrap: wrap;
    .form-item{
      margin-bottom: 20px;
      margin-right: 20px;
    }
  }
  .box{
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background: #f2f2f2;
    padding: 10px 0;
    .box-item{
      background: $color;
      width: 40%;
      border-radius: 4px;
      color: #fff;
      text-align: center;
      margin: 0 auto 20px auto;
      p{
        line-height: 2;
      }
    }
  }
</style>
