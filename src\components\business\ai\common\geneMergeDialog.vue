<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      title="CNV合并"
      width="40%">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" label-suffix="：" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="Gene">{{form.gene}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Status">{{form.status}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Transcript">{{form.transcript}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AutoInterpretation">{{form.autoInterpretation}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AvgRatio">{{form.avgRatio}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Exon" prop="exon">
              <el-input v-model="form.exon" placeholder="请输入Exon" maxlength="20" show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'geneMergeDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.form = Object.assign({}, this.form, this.pdata)
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      form: {
        gene: '',
        status: '',
        transcript: '',
        avgRatio: '',
        exon: ''
      },
      rules: {
        exon: [
          {required: true, message: '请输入Exon', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleClose () {
      this.$emit('handleGeneMergeDialogCloseEven')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$ajax({
            url: '/read/unscramble/save_merge_result',
            data: {
              fcanComparison: '',
              fisUnscramble: '',
              fdetectionRange: '',
              freport: '',
              link: '',
              gene: this.form.gene,
              status: this.form.status,
              transcript: this.form.transcript,
              autoInterpretation: this.form.autoInterpretation,
              favgRatio: this.form.avgRatio,
              exon: this.form.exon,
              others: ''
            }
          }).then(result => {
            if (result.success) {
              this.$message.success('保存成功')
              this.$refs.form.resetFields()
              this.$emit('handleGeneMergeDialogConfirmEven')
            } else {
              this.$message.error(result.message)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
