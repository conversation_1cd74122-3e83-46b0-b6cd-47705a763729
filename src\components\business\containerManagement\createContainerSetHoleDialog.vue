<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="设置孔"
      width="750px"
      @open="handleOpen">
      <div>
        <div class="form">
          <div class="form-item">
            <label>容器名称：</label>
            <el-input :style="{width: formWidth}" v-model="currentOperateName" disabled size="mini"></el-input>
          </div>
          <div class="form-item">
            <label>孔位数：</label>
            <el-input :style="{width: formWidth}" :value="row * column" disabled size="mini"></el-input>
          </div>
<!--          <div class="form-item">-->
<!--            <label>盒型：</label>-->
<!--            <el-select v-model="boxTypeId" size="mini" @change="handleBoxTypeChange">-->
<!--              <el-option :key="item.fid" :value="item.fid" :label="item.fboxType" v-for="item in currentSetObj.sizeList"></el-option>-->
<!--            </el-select>-->
<!--          </div>-->
          <div class="form-item hole-num-wrapper" >
            <label>盒型：</label>
            <el-input-number  v-model.trim.number="row"  size="mini" controls-position="right" clearable :min="0" :max="20"></el-input-number>
              <span style="margin: 0 5px;">x</span>
            <el-input-number  v-model.trim.number="column"  size="mini" controls-position="right" clearable :min="0" :max="20"></el-input-number>
          </div>
        </div>
        <template v-if="row > 0 && column > 0">
          <div class="box">
            <template v-if="row > 0 && column > 0">
              <div class="hole-container">
                <div class="hole-row">
                  <div class="y-text"></div>
                  <div :key="item" v-for="item in column" class="x-text">{{addZero(item)}}</div>
                </div>
                <div :key="v" v-for="v in row" class="hole-row">
                  <div class="y-text">{{numToUppercase(v)}}</div>
                  <div :key="item" v-for="item in column" class="hole"></div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    currentOperateName: { // 当前名称
      type: String
    },
    currentSetObj: {
      type: Object
    }
  },
  computed: {
    // totalHole () {
    //   if ()
    // }
  },
  data () {
    return {
      row: 0,
      column: 0,
      boxTypeId: '',
      maxHoleNum: 100, // 最大孔位数 row * column
      formWidth: '200px'
    }
  },
  methods: {
    // 打开弹窗初始化
    handleOpen () {
      this.$nextTick(() => {
        this.row = this.currentSetObj.row || 0
        this.column = this.currentSetObj.column || 0
        this.boxTypeId = this.currentSetObj.boxTypeId
      })
    },
    handleBoxTypeChange (value) {
      const item = this.currentSetObj.sizeList.find(v => v.fid === value)
      this.row = item.fxSize
      this.column = item.fySize
    },
    handleConfirm () {
      // if (!this.boxTypeId) {
      //   this.$message.error('请选择盒型')
      //   return
      // }
      let hole = {tag: '孔', row: this.row, column: this.column, total: this.row * this.column, filed: 'hole'}
      this.$emit('dialogConfirmEvent', hole)
    },
    // 不到10补0
    addZero (num) {
      if (+num > 0 && +num < 10) {
        return 0 + '' + num
      }
      return num
    },
    // 数字转大写字母
    numToUppercase (num) {
      return String.fromCharCode(64 + num)
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .el-scrollbar__wrap{
    overflow-x: hidden;
  }
  .form{
    display: flex;
    flex-wrap: wrap;
    .form-item{
      margin-bottom: 20px;
      margin-right: 20px;
    }
  }
  .box{
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    overflow-x: auto;
    background: #fff;
    padding: 10px;
    border: 1px solid #ccc;
    .hole-container{
      padding: 10px;
      .hole-row{
        display: flex;
        margin-bottom: 15px;
        & > div{
          flex-shrink: 0;
        }
        .x-text{
          width: 20px;
          margin-right: 20px;
          font-size: 14px;
          text-align: center;
        }
        .y-text{
          font-size: 14px;
          margin-right: 10px;
          width: 1em;
        }
        .hole{
          width: 20px;
          height: 20px;
          background: $color;
          border-radius: 50%;
          margin-right: 20px;
        }
      }
    }
  }

  .hole-num-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    label {
      width: 60px;
    }
    .hole-item {
      margin: 0 10px;
      width: 140px;
    }
  }
</style>
