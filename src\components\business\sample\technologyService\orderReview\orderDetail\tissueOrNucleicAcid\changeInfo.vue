<template>
  <div class="page">
    <iframe
      ref="framePage"
      :src="src"
      id="mainFrame"
      name="mainFrame"
      scrolling="no"
      frameborder="0"
      height="100%"
      width="100%"
      @load='onload'>
      您的浏览器暂不支持iframe
    </iframe>
  </div>
</template>

<script>
import constants from '../../../../../../../util/constants'
export default {
  name: `changeInfo`,
  // beforeMount () {
  //   addEventListener('message', function (evt) {
  //     if (evt.data.type !== 'childStatus') { return }
  //     this.isChildReady = evt.data.data
  //   })
  // },
  mounted () {
    this.initData()
  },
  watch: {
    // isChildReady (newVal) {
    //   if (!newVal) return
    //   this.onload()
    // }
  },
  computed: {
    libraryOperatingData () {
      return this.$store.getters.getValue('libraryOperatingData')
    },
    userInfo () {
      return this.$store.getters.getValue('userInfo')
    }
  },
  data () {
    return {
      isChildReady: false,
      src: constants.IFRAME_URL + '/entryTissueOrder?isLims=true',
      iframeUrl: constants.IFRAME_URL
    }
  },
  methods: {
    async initData () {
      window.addEventListener('message', (event) => {
        console.log(event, 'event')
        if (event.origin !== this.iframeUrl) {
          // 来自未知的源的内容，我们忽略它
          return
        }

        if (window === event.source) {
          // chrome 下, 页面初次加载后会触发一次 message 事件, event.source 是 window 对象
          // 此时 event.source.postMessage 会形成死循环
          // 因此，要跳过第一次的初始化触发的情况
          return
        }
        let msg = event.data
        if (msg === 'close') {
          this.$router.back()
        }
        if (msg === 'jump') {
          this.$router.back()
        }
        if (msg === 'up') {
          this.$router.back()
        }
      })
    },
    onload () {
      let win = this.$refs.framePage.contentWindow
      let obj = {
        orderCode: this.libraryOperatingData.code,
        orderId: this.libraryOperatingData.orderId,
        email: `${this.userInfo.emailPrefix}@geneplus.org.cn`
      }
      console.log(win, obj, 'iframe', this.iframeUrl)
      if (this.iframeUrl) {
        setTimeout(() => {
          // 等待几秒再发请求，否则会出现iframe不存在的情况
          win.postMessage(JSON.stringify(obj), this.iframeUrl)
        }, 500)
      }
    },
    // 关闭页面
    close () {
      setTimeout(() => {
        window.close()
      }, 1000)
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-scrollbar__wrap{
  overflow-x: hidden;
}
.page{
  width: 100vw;
  height: 100vh;
  margin: auto;
  padding: 10px 20px;
}
.title{
  margin-right: 30px;
  font-size: 20px;
  font-weight: 600;
}
</style>
