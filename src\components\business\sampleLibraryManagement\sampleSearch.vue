<template>
  <div style="background: #fff;">
    <div class="search-form">
      <el-form :model="form" size="mini" label-width="80px" inline style="display: flex;">
        <el-form-item label="样本编号">
          <el-input v-model.trim="form.sampleCode" clearable></el-input>
        </el-form-item>
        <el-form-item label="样本类型">
          <el-select
            v-model.trim="form.sampleType"
            v-form-loading="getSampleTypeLoading"
            placeholder="请选择"
            multiple
            collapse-tags clearable>
            <template v-for="item in sampleTypeOptions">
              <el-option :key="item.value" :label="item.value" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model.trim="form.applicationTime"
            clearable
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <template v-if="$setAuthority('011003001', 'buttons')">
        <el-button type="primary" size="mini" @click="sampleStatisticDialogVisible = true">样本统计</el-button>
      </template>
      <el-button type="primary" plain size="mini" @click="handleFixPosition">修改存放位置</el-button>
      <el-dropdown @command="handleSignSample" style="margin: 0 10px;">
        <el-button plain type="primary" size="mini">标记操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0">赋值销毁</el-dropdown-item>
          <el-dropdown-item :command="1">取消销毁</el-dropdown-item>
          <el-dropdown-item :command="2">赋值返样</el-dropdown-item>
          <el-dropdown-item :command="3">取消返样</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button size="mini" plain type="primary" @click="handleSearch">查询</el-button>
      <el-button size="mini" plain @click="handleResetForm">重置</el-button>
    </div>
    <el-table
      ref="table"
      :data="tableData"
      style="width: 100%;"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      class="sampleTable"
      @select="handleSelectTable"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column show-overflow-tooltip type="selection" width="55"></el-table-column>
      <el-table-column show-overflow-tooltip type="index" width="55" label="序号"></el-table-column>
      <el-table-column show-overflow-tooltip prop="sampleStatus" label="样本状态" width="100"></el-table-column>
      <el-table-column show-overflow-tooltip prop="sampleCode" label="样本编号" min-width="180">
        <template slot-scope="scope">
          {{scope.row.sampleCode}} <el-tag v-if="scope.row.realData.fisPreciousSample" size="mini">珍</el-tag>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="sampleType" label="样本类型" min-width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="sampleAmount" label="样本量" min-width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="position" label="存放位置" min-width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="lab" label="所属实验室" min-width="180"></el-table-column>
      <el-table-column show-overflow-tooltip prop="applicant" label="申请人" min-width="180"></el-table-column>
      <el-table-column show-overflow-tooltip label="申请单号" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="handleShowModifyDialog(scope.row.applicationOrder)">{{scope.row.applicationOrder}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="notes" label="备注" show-overflow-tooltip width="180"></el-table-column>
      <el-table-column label="数据流" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleShowSampleLogDialog(scope.row.id)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :current-page.sync="currentPage"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper, slot"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <modify-order-dialog
      :pvisible="modifyOrderDialogVisible"
      :order-id="currentApplicationOrder"
      title="订单详情"
      page="sampleSearch"
      @dialogCloseEvent="modifyOrderDialogVisible = false"></modify-order-dialog>
    <sample-log-dialog
      :pvisible="sampleLogDialogVisible"
      :sample-id="currentSampleId"
      @dialogCloseEvent="sampleLogDialogVisible = false"/>
    <sample-statistic-dialog
      :pvisible.sync="sampleStatisticDialogVisible"/>
    <fix-position-dialog
      :pvisible.sync="fixPositionVisible"
      :position="position"
      :sample-code="sampleCode"
      @dialogConfirmEvent="getData"
    ></fix-position-dialog>
    <sample-sign-dialog
      :pvisible.sync="signSampleVisible"
      :sign-type="signType"
      @dialogConfirmEvent="getData"
    ></sample-sign-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import modifyOrderDialog from './modifyOrderDialog'
import sampleLogDialog from './sampleLogDialog'
import sampleStatisticDialog from './sampleSearchSampleStatisticDialog'
import FixPositionDialog from './fixPositionDialog'
import util from '../../../util/util'
import SampleSignDialog from './sampleSignDialog'
export default {
  name: 'sampleSearch',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    SampleSignDialog,
    FixPositionDialog,
    modifyOrderDialog,
    sampleLogDialog,
    sampleStatisticDialog
  },
  mounted () {
    this.getSampleType()
    this.getData()
  },
  data () {
    return {
      fixPositionVisible: false, // 修改存储位置弹窗
      position: '', // 存储位置
      sampleCode: '', // 样本编号
      signType: null,
      signSampleVisible: false,
      form: {
        sampleCode: '',
        sampleType: [],
        applicationTime: []
      },
      sampleTypeOptions: [],
      formSubmit: { // 用于提交的数据，点击查询后从form中得到
        sampleCode: '',
        sampleType: [],
        applicationTime: []
      },
      currentApplicationOrder: '',
      modifyOrderDialogVisible: false,
      currentSampleId: '',
      sampleLogDialogVisible: false,
      getSampleTypeLoading: false,
      sampleStatisticDialogVisible: false // 样本统计弹窗
    }
  },
  methods: {
    getData () {
      this.$ajax({
        url: '/sample/get_sample_info_list',
        data: {
          fsampleNumber: this.formSubmit.sampleCode,
          fsampleType: this.formSubmit.sampleType,
          startDate: this.formSubmit.applicationTime[0],
          endDate: this.formSubmit.applicationTime[1],
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        loadingDom: '.sampleTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.clearMap()
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              sampleStatus: v.fsampleStatus,
              sampleCode: v.fsampleNumber,
              sampleType: v.fsampleType,
              sampleAmount: v.fsampleAmount,
              position: v.fsamplePlace,
              lab: v.flab,
              applicant: v.applicatUser,
              applicationOrder: v.orderNumber,
              fisPreciousSample: v.fisPreciousSample,
              notes: v.fnotes
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.formSubmit.sampleCode = this.form.sampleCode
      this.formSubmit.sampleType = this.form.sampleType
      this.formSubmit.applicationTime = this.form.applicationTime || []
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.form = {
        sampleCode: '',
        sampleType: [],
        applicationTime: []
      }
      this.handleSearch()
    },
    // 修改存储位置
    handleFixPosition () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据进行操作')
        return
      }
      // 当前样本状态为“已入库”（否则提示：只能修改样本状态为“已入库”的样本存放位置）
      const row = [...this.selectedRows.values()][0] || {}
      if (row.sampleStatus !== '已入库') {
        this.$message.error('只能修改样本状态为“已入库”的样本存放位置')
        return
      }
      this.position = row.realData.position
      this.sampleCode = row.realData.sampleCode
      this.fixPositionVisible = true
    },
    handleSignSample (command) {
      this.signType = command
      this.signSampleVisible = true
    },
    handleShowModifyDialog (orderId) {
      this.currentApplicationOrder = orderId
      this.modifyOrderDialogVisible = true
    },
    handleShowSampleLogDialog (sampleId) {
      this.currentSampleId = sampleId
      this.sampleLogDialogVisible = true
    },
    getSampleType () {
      this.getSampleTypeLoading = true
      this.$ajax({
        url: '/sample/sample_type/get_all_sample_type'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.sampleTypeOptions = []
          data.forEach(item => {
            let v = {
              value: item.sampleTypeName
            }
            this.sampleTypeOptions.push(v)
          })
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.getSampleTypeLoading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
