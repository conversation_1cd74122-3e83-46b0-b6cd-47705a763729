<template>
  <div>
    <el-dialog
        :title="title"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :before-close="handleClose"
        width="700px"
        @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" v-if="visible" label-width="100px" size="mini" label-suffix="：" inline>
        <el-form-item label="吉因加编号" prop="geneplusNum">
          <el-input v-model.trim="form.geneplusNum" disabled class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本名称" prop="sampleName">
          <el-input v-model.trim="form.sampleName" maxlength="50" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="物种" prop="species">
          <el-input v-model.trim="form.species" class="form-width" maxlength="20"  placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="片段大小" prop="fragmentSize">
          <el-input v-model.trim="form.fragmentSize" class="form-width" maxlength="20" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="数据量" prop="libraryDateNum">
          <el-input v-model.trim="form.libraryDateNum" :min="0" type="number" class="form-width" maxlength="20" placeholder="请输入"  @blur="handleNumBlur('libraryDateNum')"></el-input>
        </el-form-item>
        <el-form-item :rules="isCoreSpun ? rules.baseBalance : [{required: false}]" label="碱基平衡" prop="baseBalance">
          <el-select v-model="form.baseBalance" class="form-width" placeholder="请选择">
            <el-option label="碱基平衡" value="碱基平衡"></el-option>
            <el-option label="碱基不平衡" value="碱基不平衡"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保存介质" prop="storageMedium">
          <el-select v-model="form.storageMedium" class="form-width" placeholder="请选择">
            <el-option label="纯水" value="纯水"></el-option>
            <el-option label="TE buffer" value="TE buffer"></el-option>
            <el-option label="RNase水" value="RNase水"></el-option>
            <el-option label="DEPC水" value="DEPC水"></el-option>
            <el-option label="乙醇" value="乙醇"></el-option>
            <el-option label="干粉" value="干粉"></el-option>
            <el-option label="其他（请在备注中说明）" value="其他（请在备注中说明）"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否环化" prop="isCyclization">
          <el-select v-model="form.isCyclization" class="form-width" placeholder="请选择">
            <el-option label="未环化" value="未环化"></el-option>
            <el-option label="已环化" value="已环化"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测序平台" prop="sequencingPlatform">
          <el-select v-model="form.sequencingPlatform" class="form-width" placeholder="请选择">
            <el-option label="DNBSEQ-T7" value="DNBSEQ-T7"></el-option>
            <el-option label="Gene+2000" value="Gene+2000"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="策略" prop="tactics">
          <el-select v-model="form.tactics" class="form-width" placeholder="请选择">
            <el-option label="PE100" value="PE100"></el-option>
            <el-option label="PE150" value="PE150"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="浓度" prop="samplingConcentration">
          <el-input v-model.trim="form.samplingConcentration" maxlength="10" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="体积" prop="samplingVolume">
          <el-input v-model.trim="form.samplingVolume" maxlength="10"  class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="libraryNotes">
          <el-input v-model.trim="form.libraryNotes" maxlength="150"  class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <!--<el-button size="mini" type="primary" @click="handleAddFakeData">添加假数据</el-button>-->
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../../../util/mixins'
export default {
  name: `editSendSampleDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Object | null
    },
    rowIndex: Number | null, // 选择数据在表中的行数
    isCoreSpun: Boolean // 应用类型是否是包芯上机
  },
  data () {
    // 第一个验证，仅允许数字、字母、- _ 组成
    const valid1 = function (rule, value, callback) {
      let regx = /^[0-9A-Za-z_]{1,}$/
      if (regx.test(value)) {
        callback()
      } else {
        callback(new Error('仅允许数字和字母还有“_”组成'))
      }
    }
    return {
      title: '',
      form: {},
      rules: {
        sampleName: [
          {required: true, message: '请输入样本名称', trigger: 'blur'},
          {validator: valid1, trigger: 'blur'}
        ],
        species: [
          {required: true, message: '请输入物种', trigger: 'blur'}
        ],
        libraryDateNum: [
          {required: true, message: '请输入数据量', trigger: 'blur'}
        ],
        baseBalance: [
          {required: true, message: '请选择碱基平衡', trigger: 'change'}
        ],
        storageMedium: [
          {required: true, message: '请选择保存介质', trigger: 'change'}
        ],
        sequencingPlatform: [
          {required: true, message: '请选择测序平台', trigger: 'change'}
        ],
        tactics: [
          {required: true, message: '请选择策略', trigger: 'change'}
        ],
        isCyclization: [
          {required: true, message: '请选择是否环化', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      if (this.pdata) {
        this.form = {...this.pdata}
        this.title = this.form.sampleName ? '编辑寄送样本信息' : '新增寄送样本信息'
      } else {
        this.title = '新增寄送样本信息'
        this.form = {
          geneplusNum: '',
          sampleName: '',
          fragmentSize: '',
          species: '',
          libraryDateNum: '',
          baseBalance: '',
          storageMedium: '',
          isCyclization: '',
          sequencingPlatform: '',
          tactics: '',
          samplingConcentration: '',
          samplingVolume: '',
          libraryNotes: ''
        }
      }
    },
    // 数字类型的输入失焦后的操作
    handleNumBlur (k) {
      let v = +this.form[k]
      if (isNaN(v) || v < 0) {
        this.form[k] = ''
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let oldName = ''
          let code = ''
          if (this.pdata) oldName = this.pdata.sampleName
          if (this.pdata) code = this.pdata.geneplusNum
          this.$emit('dialogConfirmEvent', this.form, this.rowIndex, oldName, code)
        }
      })
    },
    async handleClose () {
      await this.$confirm(`是否确认放弃${this.pdata && this.pdata.sampleName ? '编辑' : '新增'}？确认后关闭弹窗`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.visible = false
    }
  }
}
</script>

<style scoped>
  .form-width{
    width: 200px;
  }
</style>>
