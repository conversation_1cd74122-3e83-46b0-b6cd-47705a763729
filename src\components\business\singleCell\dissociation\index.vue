<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form ref="form" :model="plainForm" :inline="true" label-width="100px" size="mini" label-suffix=":"
        @keyup.enter.native="handleSearch">
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="plainForm.geneNum" placeholder="请输入吉因加编号" class="form-width" size="mini">
          </el-input>
        </el-form-item>
        <el-form-item label="生产序号" prop="productionNumber">
          <el-input v-model.trim="form.productionNumber" placeholder="请输入生产序号" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="项目编号" prop="projectNo">
          <el-input v-model.trim="plainForm.projectCode" placeholder="请输入项目编号" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model.trim="plainForm.projectName" placeholder="请输入项目名称" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="样本原始名称" prop="sampleName">
          <el-input v-model.trim="plainForm.sampleName" placeholder="请输入样本原始名称" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="异常状态">
          <el-select v-model="form.exceptionType" size="mini" clearable class="form-width" placeholder="请选择">
            <el-option v-for="item in exceptionTypeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织状态">
          <el-input v-model.trim="form.ftissueSampleState" placeholder="请输入组织状态" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="到样日期" prop="time">
          <el-date-picker v-model.trim="form.time" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" size="mini" clearable :default-time="['00:00:00', '23:59:59']"
            :value-format="'yyyy-MM-dd HH:mm:ss'" class="form-long-width">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog :pvisible.sync="searchDialogVisible" @reset="handleReset" @search="handleSearch">
      <el-form ref="form" class="params-search-form" :model="form" label-width="80px" label-suffix=":" size="mini"
        label-position="top" inline>
        <el-form-item label="子订单编号" prop="cosSubCode">
          <el-input v-model.trim="form.cosSubCode" type="textarea" placeholder="请输入子订单编号" size="mini" clearable
            class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneNum" type="textarea" placeholder="请输入吉因加编号" class="form-long-width"
            size="mini">
          </el-input>
        </el-form-item>
        <el-form-item label="项目编号" prop="projectCode">
          <el-input v-model.trim="form.projectCode" type="textarea" placeholder="请输入项目编号" size="mini" clearable
            class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model.trim="form.projectName" type="textarea" placeholder="请输入项目名称" size="mini" clearable
            class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="样本原始名称" prop="sampleName">
          <el-input v-model.trim="form.sampleName" type="textarea" placeholder="请输入样本原始名称" size="mini" clearable
            class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="器官类型" prop="organType">
          <el-input v-model.trim="form.organType" placeholder="请输入器官类型" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="组织样本类型" prop="organSampleType">
          <el-input v-model.trim="form.organSampleType" placeholder="请输入组织样本类型" size="mini" clearable
            class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model.trim="form.productName" type="textarea" placeholder="请输入产品名称" size="mini" clearable
            class="form-long-width" />
        </el-form-item>
        <el-form-item label="开启状态" prop="status">
          <el-select v-model.trim="form.status" placeholder="请选择开启状态" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in statusOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否为备份" prop="isBackup">
          <el-select v-model.trim="form.isBackup" placeholder="请选择是否为备份" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in isBackupOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否上门实验" prop="isVisit">
          <el-select v-model.trim="form.isVisit" placeholder="请选择是否上门实验" size="mini" clearable class="form-width">
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样本状态" prop="buildLibStatus">
          <el-select v-model.trim="form.buildLibStatus" placeholder="请选择样本状态" multiple size="mini" clearable
            class="form-width">
            <el-option v-for="(key, value) in buildLibStatusOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="异常状态">
          <el-select v-model="form.exceptionType" size="mini" clearable class="form-width" placeholder="请选择">
            <el-option v-for="item in exceptionTypeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生产序号" prop="productionNumber">
          <el-input v-model.trim="form.productionNumber" type="textarea" placeholder="请输入生产序号" size="mini" clearable
            class="form-long-width"></el-input>
        </el-form-item>
        <el-form-item label="到样日期" prop="time">
          <el-date-picker v-model.trim="form.time" class="form-long-width" type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>
    <div class="flex-wrapper">
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('024002005', 'buttons')" type="primary" plain size="mini"
          @click="handleSubmitResult">提交结果</el-button>
        <el-button v-if="$setAuthority('024002006', 'buttons')" type="primary" size="mini" plain
          @click="handleBatchModify">异常状态编辑</el-button>
        <template v-if="$setAuthority('024002001', 'buttons')">
          <el-button v-if="downloadingExcelLoading" type="primary" size="small" disabled><i class="el-icon-loading"></i>
            正在导出
          </el-button>
          <el-dropdown v-else @command="handleCommand" style="margin: 0 10px;">
            <el-button type="primary" plain size="mini">数据导出<i
                class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <!--        <el-button v-if="$setAuthority('024002004', 'buttons')" type="primary" plain size="mini" @click="handleFixNote">到样异常编辑</el-button>-->
        <el-button v-if="$setAuthority('024002002', 'buttons')" type="primary" plain size="mini"
          @click="handleImport">导入结果</el-button>
        <el-button v-if="$setAuthority('024002003', 'buttons')" type="primary" plain size="mini"
          @click="handleBuildLib">确认建库</el-button>
        <el-button v-if="$setAuthority('024002003', 'buttons')" type="primary" plain size="mini"
          @click="handleAutoAudit">审核</el-button>
        <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
        <el-button size="mini" @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
        </el-badge>
      </div>

      <div class="setting-wrapper">
        <el-popover v-model.trim="visible" placement="bottom-start" title="自定义列" width="360" trigger="manual">
          <div>
            <el-table ref="table" :data="tableData" border size="mini" height="300px" style="width: 100%" row-key="id"
              @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll">
              <el-table-column type="index" width="50" show-overflow-tooltip>
                <template slot-scope="scope">
                  <icon-svg icon-class="icon-tuozhuai" class="handle"></icon-svg>
                </template>
              </el-table-column>
              <el-table-column type="selection" min-width="55" show-overflow-tooltip></el-table-column>
              <el-table-column prop="title" label="列名" key="title" min-width="200">
                <template slot-scope="scope">
                  <div v-html="scope.row.title"></div>
                </template>
              </el-table-column>
            </el-table>
            <div class="operate-wrapper">
              <div class="operate-item" @click="handleResetTableConfig">恢复默认</div>
              <div class="operate-item" @click="handleCancel">关闭</div>
              <div class="operate-item" @click="handleSave">保存</div>
            </div>
          </div>
          <div slot="reference" @click="handleShowSetting">
            <el-card :body-style="{ padding: '5px' }" shadow="hover">
              <icon-svg style="font-size: 20px" icon-class="icon-shezhi"></icon-svg>
            </el-card>
          </div>
        </el-popover>
      </div>
    </div>

    <div class="content">
      <vxe-table ref="tableRef" border resizable show-overflow :height="tbHeight" keep-source :key="tableKey"
        class="table" :data="tableList" size="mini" :row-style="handleVxeRowStyle" :auto-resize="true"
        :edit-rules="validRules" :valid-config="{ msgMode: 'full' }" :checkbox-config="{ trigger: 'row' }"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }" :scroll-y="{ enabled: true }"
        @checkbox-all="handleSelectChange" @checkbox-change="handleSelectChange" @edit-closed="handleSaveNote">
        <vxe-column fixed="left" type="checkbox" width="50"></vxe-column>
        <vxe-table-column fixed="left" type="seq" title="序号" width="60"></vxe-table-column>
        <template v-for="(item, index) in tableConfig">
          <vxe-table-column v-if="item.field === 'fsampleTag' && item.isShow" :key="index" :field="item.field"
            :formatter="item.formater" :title="item.title" :width="item.width" :edit-render="item.render"
            line-clamp="1">
            <template #default="{ row }">
              <el-tag v-if="row.realData.fsampleTag">多</el-tag>
              <span v-else>-</span>
            </template>
          </vxe-table-column>

          <vxe-table-column v-else-if="item.field === 'warningFlag' && item.isShow" :key="index" :field="item.field"
            :formatter="item.formater" :title="item.title" :width="item.width" :edit-render="item.render"
            line-clamp="1">
            <template #default="{ row }">

              <el-tooltip :content="row.fremark" :disabled="!row.realData.fremark" placement="top" effect="dark">
                <div :class="getWarningFlagClass(row.fexceptionRemark)">{{ row.warningFlagText }}</div>
              </el-tooltip>
            </template>
          </vxe-table-column>
          <vxe-table-column v-else-if="item.field === 'fexperimentTime' && item.isShow" :key="index" :field="item.field"
            :formatter="item.formater" :title="item.title" :width="item.width" :edit-render="{}" line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template #edit="{ row }">
              <vxe-input v-model="row.realData.fexperimentTime" type="datetime" clearable placeholder="请选择日期" transfer
                @change="() => handleChange(row, item.field)"></vxe-input>
            </template>
          </vxe-table-column>

          <vxe-table-column v-else-if="item.isShow" :key="item.field" :field="item.field" :formatter="item.formater"
            :title="item.title" :fixed="['fcosSampleName', 'fproductionNumber'].includes(item.field) ? 'left' : ''"
            :width="item.width" :edit-render="item.render" line-clamp="1">
            <template #header="{ row }">
              <span v-html="item.title"></span>
            </template>
            <template v-if="item.render" #edit="{ row }">
              <vxe-input v-if="item.render.name === '$input'" v-model="row.realData[item.field]" type="text" clearable
                placeholder="请输入内容" transfer @change="() => handleChange(row, item.field)"></vxe-input>
              <!--              下拉-->
              <vxe-select v-else-if="item.render.name === '$select'" v-model="row.realData[item.field]" clearable
                transfer @change="() => handleChange(row, item.field)">
                <vxe-option v-for="(value, index) in item.render.options" :key="index" :label="value.label"
                  :value="value.value"></vxe-option>
              </vxe-select>
            </template>
          </vxe-table-column>

        </template>
      </vxe-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh" />
          </button>
        </el-pagination>
      </div>
    </div>
    <batch-modifying-dialog :pvisible.sync="batchModifyingDialogVisible" :ids="ids"
      @dialogConfirmEvent="getData"></batch-modifying-dialog>
    <upload-result-dialog :pvisible.sync="uploadResultVisible" @dialogConfirmEvent="getData"></upload-result-dialog>
    <fix-note-dialog :pvisible.sync="fixNoteDialogVisible" :id="id" :note="note"
      @dialogConfirmEvent="getData"></fix-note-dialog>
    <auto-audit-dialog :pvisible.sync="autoAuditDialogVisible" :id="selectedId" :audit-type="auditType"
      :show-quality-info="showQualityInfo" @dialogConfirmEvent="getData" />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import { tableConfig } from './tabelConfig'
import { awaitWrap, deepCopy, downloadFile, readBlob, setGroupData } from '../../../../util/util'
import Sortable from 'sortablejs'
import {
  dataFormating,
  statusOptions,
  isBackupOptions,
  booleanOptions,
  buildLibStatusOptions,
  exceptionTypeList
} from './dataFormate'
import UploadResultDialog from './components/uploadResultDialog.vue'
import {
  confirmBuildLib,
  exportDissociationData,
  getDissociationList, submitDissociationResult, updateDissociationData
} from '../../../../api/sequencingManagement/singleCell'
import BatchModifyingDialog from './components/batchModifyingDialog.vue'
import fixNoteDialog from './components/fixNoteDialog.vue'
import AutoAuditDialog from './components/autoAuditDialog.vue'
import { beforeRequiredField, requiredField } from './constants'

export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: { BatchModifyingDialog, UploadResultDialog, fixNoteDialog, AutoAuditDialog },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      statusOptions,
      isBackupOptions,
      booleanOptions,
      buildLibStatusOptions,
      // 控制弹出框的可见性，默认为隐藏
      visible: false,
      // 控制搜索对话框的可见性，默认为隐藏
      searchDialogVisible: false,
      // 控制下载Excel时的加载状态，默认为隐藏
      downloadingExcelLoading: false,
      // 控制导入Excel时的加载状态，默认为隐藏
      uploadResultVisible: false,
      batchModifyingDialogVisible: false,
      fixNoteDialogVisible: false,
      autoAuditDialogVisible: false,
      showQualityInfo: false,
      auditType: 0,
      // 选中项的ID数组，默认为空数组
      ids: [],
      tableKey: Math.random(),
      id: null,
      exceptionTypeList: [
        { label: '无', value: '5' },
        ...exceptionTypeList
      ],
      // 交付状态，默认为空
      deliverStatus: null,
      note: null,
      // 订单编号，默认为空
      orderCode: null,
      // 订单类型，默认为空
      organType: null,
      productName: null,
      plainForm: {
        cosSubCode: '',
        projectCode: '',
        projectName: '',
        sampleName: '',
        organType: '',
        organSampleType: '',
        productName: '',
        status: '',
        isBackup: '',
        isVisit: '',
        geneNum: '',
        buildLibStatus: [],
        productionNumber: '',
        time: []
      },
      // 表单数据对象，默认为空对象
      form: {
        cosSubCode: '',
        projectCode: '',
        projectName: '',
        geneNum: '',
        sampleName: '',
        organType: '',
        organSampleType: '',
        productName: '',
        status: '',
        isBackup: '',
        isVisit: '',
        exceptionType: '',
        buildLibStatus: [],
        productionNumber: '',
        time: []
      },
      formSubmit: {},
      plainFormSubmit: {},
      // 包lane数
      laneTotal: null,
      // 实际周期
      actualCycle: null,
      // 分页大小数组，默认为100、200、500
      pageSizes: [100, 200, 500],
      pageSize: 100,
      // 表配置对象，由外部定义
      tableConfig: JSON.parse(localStorage.getItem('dissociationTableConfig')) || tableConfig,
      // 表数据数组，默认为空数组
      tableData: [],
      // 表列表数组，默认为空数组
      tableList: [],
      // 表单验证规则对象，默认为空对象
      validRules: {
      },
      selectedId: null
    }
  },
  methods: {
    /**
     * 设置查询参数。
     * 此函数用于根据表单提交的信息，生成并返回一个包含各种查询条件的对象。
     * 这些条件包括时间范围、项目信息、样本信息、订单信息等，用于精确查询项目进度和状态。
     *
     * @returns {Object} 返回一个对象，其中包含了所有的查询参数。
     */
    setParams () {
      const formSubmit = this.formSubmit || {}
      const plainFormSubmit = this.plainFormSubmit || {}
      const time = formSubmit.time || []
      const fcosSubCodeList = setGroupData(formSubmit.cosSubCode, '、', false) || []
      const fprojectCodeList = setGroupData(formSubmit.projectCode, '、', false) || []
      const fprojectNameList = setGroupData(formSubmit.projectName, '、', false) || []
      const fcosSampleNameList = setGroupData(formSubmit.sampleName, '、', false) || []
      const fproductNameList = setGroupData(formSubmit.productName, '、', false) || []
      const fproductionNumberList = setGroupData(formSubmit.productionNumber, '、', false) || []
      const fgeneNumList = setGroupData(formSubmit.geneNum, '、', false) || []
      return {
        fcosSubCodeList: [plainFormSubmit.cosSubCode, ...fcosSubCodeList].filter(v => v),
        fprojectCodeList: [plainFormSubmit.projectCode, ...fprojectCodeList].filter(v => v),
        fprojectNameList: [plainFormSubmit.projectName, ...fprojectNameList].filter(v => v),
        fcosSampleNameList: [plainFormSubmit.sampleName, ...fcosSampleNameList].filter(v => v),
        fproductNameList: [plainFormSubmit.productName, ...fproductNameList].filter(v => v),
        fproductionNumberList: [plainFormSubmit.productionNumber, ...fproductionNumberList].filter(v => v),
        fgeneNumList: [plainFormSubmit.geneNum, ...fgeneNumList].filter(v => v),
        fconfirmTimeStart: time[0],
        fconfirmTimeEnd: time[1],
        forganType: formSubmit.organType,
        ftissueSampleState: formSubmit.ftissueSampleState,
        fexceptionRemark: formSubmit.exceptionType,
        ftissueSampleType: formSubmit.organSampleType,
        fisStaging: formSubmit.status,
        fisBackUp: formSubmit.isBackup,
        fisBuildLibList: formSubmit.buildLibStatus,
        fisSiteExperiment: formSubmit.isVisit
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (this.pin) {
        this.shiftSelect(row)
      } else {
        this.startPoint = undefined// 清空多选起点
        this.endPoint = undefined// 清空多选终点
        this.selectedRows.has(row.id)
          ? this.selectedRows.delete(row.id)
          : this.selectedRows.set(row.id, row)
      }
      this.handleEchoSelect()
    },
    // 全选
    handleSelectAll (selection) {
      this.handleDelCurrentDataMap()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.selectedRowsSize = this.selectedRows.size
    },
    /**
     * 处理搜索操作。
     * 重置当前页码并调用获取数据的方法，以便加载新搜索结果。
     */
    handleSearch () {
      // 深拷贝表单提交的数据，确保不直接修改原数据
      this.formSubmit = deepCopy(this.form)
      this.plainFormSubmit = deepCopy(this.plainForm)
      this.currentPage = 1
      this.getData()
    },
    /**
     * 处理重置操作。
     * 将表单数据重置为初始状态，并调用搜索方法以刷新列表。
     */
    handleReset () {
      this.form = this.$options.data().form
      this.plainForm = this.$options.data().plainForm
      this.handleSearch()
    },
    /**
     * 异步获取样本监控列表的数据。
     *
     * 使用设置的参数进行请求，并对请求结果进行处理，将处理后的数据赋值给列表。
     * 使用awaitWrap封装请求以处理加载状态。
     */
    async getData () {
      const params = {
        ...this.setParams(), // 分页信息
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      const { res } = await awaitWrap(getDissociationList(params, {
        loadingDom: '.table'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.totalPage = res.data.total
        this.tableList = dataFormating(res.data.records)
      }
    },
    setUpdateParams (field, cellValue, row) {
      return {
        fid: row.fid,
        [field]: cellValue
      }
    },
    handleChange (row, field) {
      row[field] = row.realData[field]
      if (!row[field] && row[field] !== 0) {
        row[field] = '-'
      }
    },
    // 校验必填项是否填写，并返回对应label
    handleCheck (rows) {
      const unFilled = []
      rows.forEach(row => {
        row = row.realData
        // 细胞悬液浓度、是否去死细胞、是否去碎片、上机前-细胞悬液浓度
        const unRequiredFields = ['fexperimentRemark', 'fcellSuspensionConcentration', 'fisDieCell', 'fisDefragment', 'fbeforeBoardSuspensionConcentration']
        const requiredFields = this.tableConfig.filter(v => v.render && !unRequiredFields.includes(v.field)).map(v => v.field)
        const isEmpty = requiredFields.some(field => !(row[field] || row[field] === 0))
        if (isEmpty) {
          unFilled.push(row.fgeneNum)
        }
      })
      return unFilled
    },
    async handleSaveNote ({ row, column }) {
      let xTable = this.$refs.tableRef
      let field = column.property
      row[field] = (row[field] + '').replace(/\s+/g, '')
      let cellValue = row.realData[field]
      // 去除所有空格
      // 判断单元格值是否被修改, 判断0,1 是否改变
      console.log(row, row[field], xTable.isUpdateByRow(row, field))
      if (xTable.isUpdateByRow(row, field)) {
        // 校验格式 配置式
        if (cellValue) {
          if (field === 'fproductionNumber') {
            // 已建库的生产序号不允许编辑
            if (!/^[0-9a-zA-Z]+$/.test(cellValue)) {
              this.$message.error('生产序号仅允许输入数字、大小写字母')
              row[field] = ''
              return
            }
          }
          if (field === 'fcellSuspensionConcentration' || field === 'fbeforeBoardSuspensionConcentration') {
            if (!/^\d+(\.\d{1,2})?$/.test(cellValue)) {
              this.$message.error('请输入正确的浓度值，最多两位小数')
              await this.getData()
              return
            }
          }
          // 最多50字符
          if (cellValue.length > 50) {
            this.$message.error('最多50字符')
            return
          }
        }
        const booleanFields = ['fisDieCell', 'fisDefragment']
        if (booleanFields.includes(field) && (!cellValue && cellValue !== 0)) {
          cellValue = 3 // 后端要求空值时 传 其他任意数字
        }
        const params = this.setUpdateParams(field, cellValue, row)
        const { res = {} } = await awaitWrap(updateDissociationData(params))
        if (res.code === this.SUCCESS_CODE) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          // 局部更新单元格为已保存状态
          await this.$refs.tableRef.reloadRow(row, null, field)
          return
        }
        await this.getData()
      }
    },
    /**
     * 获取警告标记对应的样式类名
     * @param {number} type - 警告类型
     * @returns {string} 对应的CSS类名
     * 1: 暂停 - 橙色
     * 2-5: 各类终止状态 - 红色
     */
    getWarningFlagClass (type) {
      const warningClassMap = {
        0: 'warning-flag--pause', // 暂停
        1: 'warning-flag--terminate-pre', // 终止-解离前
        2: 'warning-flag--terminate-post', // 终止-解离后
        3: 'warning-flag--terminate-lib', // 终止-建库后
        4: 'warning-flag--terminate-seq' // 终止-上机后
      }
      return warningClassMap[type] || ''
    },
    // 选择导出类型
    handleCommand (command) {
      command === 1 ? this.handleExportAll() : this.handleExport()
    },
    async downloadExport (res) {
      if (res) {
        const { err } = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 按条件导出
    async handleExportAll () {
      const params = this.setParams()
      await this.$confirm('是否确认导出查询数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const { res } = await awaitWrap(exportDissociationData(params))
      await this.downloadExport(res)
    },
    // 导出所选
    async handleExport () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rowsId = selectRecords.map(item => item.fid)
      await this.$confirm('否确认导出选中数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const { res } = await awaitWrap(exportDissociationData({
        fdissociationIdList: rowsId
      }))
      await this.downloadExport(res)
    },
    async handleSubmitResult () {
      // 1、用户单选/多选数据，点击【提交结果】;
      // 2、所选数据状态应为“未提交”，否则提示：存在数据状态为“已提交/已建库”，请检查！
      // 3、系统根据导入/页面编辑的字段规则校验是否回填完整，返回校验结果：
      //  若字段未回填完整，弹窗提示：存在数据未回填完整，请检查！
      // 若回填完整，提示：提交成功！并把对应数据的”建库状态“改为”已提交”。
      const selectedRows = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRows.length === 0) {
        this.$message.error('请选择数据操作')
        return
      }
      const hasUnSubmit = selectedRows.every(row => row.fisBuildLib === 2)
      if (hasUnSubmit) {
        this.$message.error('存在数据状态为“已建库”，请检查！')
        return
      }
      const { res } = await awaitWrap(submitDissociationResult({
        fdissociationIdList: selectedRows.map(row => row.fid)
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message({
          message: '提交成功',
          type: 'success'
        })
        await this.getData()
      }
    },
    handleBatchModify () {
      // 获取选中的样本记录
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择至少一条样本')
        return
      }
      this.ids = selectedRecords.map(item => item.fid)
      this.batchModifyingDialogVisible = true
    },
    handleFixNote () {
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择需要修改备注的样本')
        return
      }
      if (selectedRecords.length > 1) {
        this.$message.error('只能选择一个样本进行备注修改')
        return
      }
      this.id = selectedRecords[0].fid
      this.note = selectedRecords[0].fcosExceptionRemark
      this.fixNoteDialogVisible = true
    },
    // 导入结果
    handleImport () {
      this.uploadResultVisible = true
    },
    // 确认建库
    async handleBuildLib () {
      const selectedRows = this.$refs.tableRef.getCheckboxRecords()
      // 点击「确认建库」时要求选择数据操作，否则提示“请选择数据操作！”
      if (selectedRows.length === 0) {
        this.$message.error('请选择数据操作')
        return
      }
      // 仅允许选择「建库状态」为「未建库」且「开启状态」为「直接开启」的数据进行操作，否则提示“当前所选数据中含有“暂存”/“已建库”的数据，请检查！”
      const rows = selectedRows
      const hasBuildLib = rows.some(row => [0].includes(+row.fisBuildLib) || +row.fisStaging !== 0)
      if (hasBuildLib) {
        this.$message.error('当前所选数据中含有“暂存”/“未提交”/“已建库”的异常状态的数据，请检查！')
        return
      }
      // 无生产序号的数据不允许流入建库环节；
      const hasProductionNumber = rows.some(row => !row.fproductionNumber)
      if (hasProductionNumber) {
        this.$message.error('所选数据含有无生产序号的数据，请检查！')
        return
      }
      await this.$confirm(`<div><b>建库确认</b></div>当前已选${rows.length}条样本数据，确定流入建库环节么？`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
      const { res } = await awaitWrap(confirmBuildLib({
        fdissociationIdList: selectedRows.map(v => v.fid)
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('操作成功')
        await this.getData()
      }
    },
    handleSelectChange () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      this.selectedRowsSize = selectRecords.length
    },
    // 恢复默认表格配置
    handleResetTableConfig () {
      this.tableConfig = tableConfig
      localStorage.setItem('dissociationTableConfig', JSON.stringify(this.tableConfig))
      this.tableKey = Math.random()
      this.visible = false
    },
    // 拖拽排序
    async initSort () {
      await this.$nextTick()
      const el = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      // 根据具体需求配置options配置项
      Sortable.create(el, {
        handle: '.handle', // handle's class
        onEnd: (evt) => { // 监听拖动结束事件
          try {
            // 交换元素的逻辑，避免直接使用splice，以提高性能
            const temp = this.tableData[evt.oldIndex]
            this.tableData[evt.oldIndex] = this.tableData[evt.newIndex]
            this.tableData[evt.newIndex] = temp
          } catch (error) {
            console.error('Error during sorting:', error)
            // 可以进一步处理异常，例如回滚操作、显示错误信息等
          }
        }
      })
    },
    // 保存表格配置
    handleSave () {
      this.tableConfig = this.tableData.map(item => {
        if (item.isCustomerField) {
          item.isShow = !!this.selectedRows.has(item.id)
        }
        return item
      })
      this.tableKey = Math.random()
      localStorage.setItem('dissociationTableConfig', JSON.stringify(this.tableConfig))
      this.visible = false
    },
    // 取消表格配置
    handleCancel () {
      this.visible = false
    },
    // 显示表格配置
    handleShowSetting () {
      this.initSort()
      this.visible = !this.visible
      // 回显选中的列
      this.tableData = this.tableConfig.filter(item => item.isCustomerField)
      this.tableData.forEach(item => {
        if (item.isShow) {
          this.selectedRows.set(item.id, item)
        }
      })
      this.handleEchoSelect()
    },
    // 自动审核
    handleAutoAudit () {
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择需要审核的样本')
        return
      }
      if (selectedRecords.length > 1) {
        this.$message.error('只能选择一个样本进行自动审核')
        return
      }
      if (selectedRecords.some(v => v.fisBuildLib !== 0)) {
        this.$message.error('只能选择样本状态为“未提交”的样本进行自动审核')
        return
      }
      const row = selectedRecords[0] || {}
      // 实验环节包含抽核的不能点审核
      if (row.fexperimentalLink.includes('抽核')) {
        this.$message.error('实验环节包含抽核的不能点审核')
        return
      }
      this.showQualityInfo = false
      this.auditType = 0
      // 是否去死细胞、是否去碎片存在为空的
      const isCellNull = !row.realData.fisDieCell && row.realData.fisDieCell !== 0
      const isFragmentNull = !row.realData.fisDefragment && row.realData.fisDefragment !== 0
      // 存在为空的情况
      if (!isCellNull && !isFragmentNull) {
        this.auditType = 1
        this.showQualityInfo = true
        const hasQualityResult = beforeRequiredField.some(v => {
          console.log(v, row.realData[v])
          return (!row.realData[v] && (row.realData[v] !== 0 || row.realData[v] !== '0'))
        })
        if (hasQualityResult) {
          this.$message.error('质控结果未回填系统，无法审核')
          return
        }
      } else {
        const hasQualityResult = requiredField.some(v => {
          return (!row.realData[v] && row.realData[v] !== 0)
        })
        if (hasQualityResult) {
          this.$message.error('质控结果未回填系统，无法审核')
          return
        }
      }
      this.selectedId = row.fid
      this.autoAuditDialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;

  .btn-group {
    margin-bottom: 10px;
  }
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
}

.setting-wrapper {
  height: 32px;
}

.operate-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin-top: 12px;

  .operate-item {
    flex: 1;
    cursor: pointer;
  }

  .operate-item:hover {
    color: #409EFF;
  }
}

.time-tips-item {
  border-bottom: 1px solid #ebeef5;
  padding: 5px 0;
}

.time-tips-item:last-child {
  border-bottom: none;
}

.table-item {
  width: 100%;
  // 单行省略
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/deep/ .vxe-body--column {
  padding: 0 !important;
}

.warning-flag {
  &--pause {
    color: #E6A23C;
  }

  // 暂停 - 橙色
  // 所有终止状态使用相同的红色
  &--terminate {

    &-pre,
    &-post,
    &-lib,
    &-seq {
      color: #F56C6C;
    }

    // 终止状态 - 红色
  }
}
</style>
