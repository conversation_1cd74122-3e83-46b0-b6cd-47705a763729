<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="100px"
        size="mini"
        @keyup.enter.native="handleSearch">
        <el-form-item label="任务单编号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="任务单编号">
          <el-input v-model.trim="form.taskCode" class="form-width" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="实验样本 ">
          <el-input v-model.trim="form.sampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="FC号">
          <el-input v-model.trim="form.fc" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.code" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model.trim="form.time"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </search-params-dialog>

    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('021005001', 'buttons')" type="primary" size="mini" @click="handleChooseTask">下载任务单</el-button>
      <el-button v-if="$setAuthority('021005007', 'buttons')" type="danger" plain class="fix-dropdown-margin" size="mini" @click="handleRegisterException">登记异常</el-button>
      <el-button v-if="$setAuthority('021005009', 'buttons')" type="primary" plain size="mini" @click="handleChooseDownloadScheduler">下载预排表</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" @click="handleReset">重置</el-button>
      <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
        <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
      </el-badge>
    </div>
    <div class="content">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        :row-class-name="handleClassName"
        :height="tbHeight"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55" show-overflow-tooltip></el-table-column>
        <el-table-column type="index" label="序号" width="50" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNB名称" prop="dnbName" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="实验样本 " width="150" prop="sampleName" show-overflow-tooltip></el-table-column>
        <el-table-column label="核酸/吉因加编号" prop="geneCode" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.geneCode !== '查看样本详情'">{{scope.row.geneCode}}</span>
            <div v-else class="link" @click="handleDetail(scope.row)">{{scope.row.geneCode}}</div>
          </template>
        </el-table-column>
        <el-table-column label="原始样本名称" prop="oldSampleName" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="项目名称" prop="projectName" show-overflow-tooltip></el-table-column>
        <el-table-column prop="samplePosition" label="样本孔位" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originSamplePosition" label="原始样本定位" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="文库修饰类型" prop="libModificationType" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="文库类型" prop="libType" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="浓度（ng/ul）" prop="concentration" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="片段长度" prop="clipLength" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="输入量（pmol）" min-width="120" prop="inputQuantity" show-overflow-tooltip></el-table-column>
        <el-table-column label="管数" prop="tubeNumber" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="总取样本量（ul）" min-width="120" prop="sampleSize" show-overflow-tooltip></el-table-column>
        <el-table-column label="TE（ul）" prop="te" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="排单数据量/G" prop="size" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNB浓度1" prop="dnbConcentration1" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNB浓度2" prop="dnbConcentration2" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNB浓度3" prop="dnbConcentration3" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNB浓度4" prop="dnbConcentration4" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNB计算浓度" prop="dnbCalculatedConcentration" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="理论相对量" prop="theoreticalTotal" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="相对总量" prop="relativeTotal" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="DNBpooling体积" prop="dnbPoolingVolume" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="targetPosition" label="目标孔位" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="targetMaterial" label="目标耗材" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="任务单编号" prop="taskCode" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="机器号" prop="machineNumber" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="边" prop="side" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="FC号" prop="fc" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="测序模式" prop="sequenceMode" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="Barcode文件" prop="barcode" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="芯片负压值(Mpa)" prop="chipPressureValue" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="自定义引物" prop="customItem" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="自动清洗" prop="automaticCleaning" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="testPerson" label="检测人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reviewPerson" label="复核人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskSubmitTime" label="提交时间" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="taskSubmitNumber" label="提交批次号" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderPerson" label="下单人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderDate" label="下单时间" width="120" show-overflow-tooltip></el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <choose-task-dialog
      :pvisible.sync="chooseTaskVisible"
      :type="type"
      work-id="5"
      workflow-status="1"
      @downloadTaskEvent="handleDownload"
      @downloadSchedulerEvent="handleDownloadScheduler"
    />

    <!--异常登记-->
    <register-exception-dialog
      :pvisible.sync="registerExceptionVisible"
      :sample-ids="ids"
      :type="5"
      @dialogConfirmEvent="getData"
    />
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap, downloadFile, readBlob} from '../../../../util/util'
import registerExceptionDialog from '../components/registerExceptionDialog' // 登记异常

import ChooseTaskDialog from '../components/chooseTaskDialog'
import {getMakeDNBList, downloadTask, downloadScheduler} from '../../../../api/sequencingManagement/makeDNBApi' // 选择任务单

export default {
  name: 'processing',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ChooseTaskDialog,
    registerExceptionDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 54 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      registerExceptionVisible: false,
      infoChangeDialogVisible: false,
      removeSampleVisible: false,
      uploadResultDialogVisible: false,
      chooseTaskVisible: false,
      isSingle: false,
      backFillVisible: false,
      form: {
        taskCode: '', // 任务单编号
        projectName: '', // 项目名称
        time: [],
        fc: '',
        sampleName: '', // 样本编号
        code: '' // 吉因加编号
      },
      taskIds: '', // 任务单ids
      type: '',
      removeInfo: [],
      formSubmit: {},
      info: [],
      ids: [{}],
      searchDialogVisible: false,
      tableData: []
    }
  },
  methods: {
    handleSearch () {
      this.formSubmit = { ...this.form }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = { ...this.$options.data().form }
      this.handleSearch()
    },
    handleDetail (row) {
      this.$showSampleDetailDialog({
        geneInfo: row.geneInfo
      })
    },
    handleSetGeneCode (v) {
      const code = (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode
      if (code.includes(',')) { return '查看样本详情' }
      return code
    },
    getParams () {
      const time = this.formSubmit.time || []
      return {
        fstatus: 1,
        ftaskCode: this.formSubmit.taskCode,
        fsubmitDateStart: time[0],
        fsubmitDateEnd: time[1],
        ffc: this.formSubmit.fc,
        fprojectName: this.formSubmit.projectName,
        fsampleNameList: util.setGroupData(this.formSubmit.sampleName, '、', false),
        fgeneCode: this.formSubmit.code,
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    async getData () {
      const params = this.getParams()
      this.clearMap()
      let {res} = await awaitWrap(getMakeDNBList(params, {loadingDom: '.table'}))
      const booleanMap = {
        '1': '是',
        '0': '否'
      }
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        this.totalPage = data.total * 1 || 0
        this.selectedRows.clear()
        this.tableData = []
        const rows = data.records || []
        let isHeightLight = false
        let index = 0
        rows.forEach((v, i) => {
          // 判断是否切换高亮 （同任务单号+同项目+同文库修饰类型）
          if (i !== 0 && (v.ftaskCode !== rows[i - 1].ftaskCode || v.fprojectName !== rows[i - 1].fprojectName || v.flibModificationType !== rows[i - 1].flibModificationType)) {
            isHeightLight = !isHeightLight
            index = 0
          }
          index++
          const item = {
            id: v.fid,
            isHeightLight: isHeightLight,
            index: index,
            projectName: v.fprojectName, // 项目名称
            sampleName: v.fsampleName, // 样本名称
            geneCode: this.handleSetGeneCode(v), // 吉因加编号,
            geneCodeValue: (v.fgeneCode || '').endsWith('cl') ? v.fgeneCode : v.fnucleateCode || v.fgeneCode, // 吉因加编号,
            geneInfo: {
              fgeneCode: v.fgeneCode,
              fnucleateCode: v.fnucleateCode
            },
            oldSampleName: v.foldSampleName, // 基因编码
            libModificationType: v.flibModificationType, // 修饰类型
            libType: v.flibType, // 测序类型
            size: v.fsize, // 体积
            clipLength: v.fclipLength, // 片段长度
            lastVolume: v.flastVolume, // 末端体积
            dnbConcentration: v.fdnbConcentration, // 末端浓度
            te: v.fte, // TE
            sampleSize: v.fsampleSize, // 样本量
            tubeNumber: v.ftubeNumber, // 管数
            dnbName: v.fdnbName, // 冻存盒名称
            inputQuantity: v.finputQuantity,
            concentration: v.fconcentration, // 浓度
            dnbCalculatedConcentration: v.fdnbCalculatedConcentration, // 计算浓度
            dnbPoolingVolume: v.fdnbPoolingVolume, // 冻存池体积
            taskCode: v.ftaskCode, // 任务单号
            testPerson: v.ftestPerson,
            reviewPerson: v.fcheckPerson,
            taskSubmitTime: v.fresultSubmitTime,
            machineNumber: v.fmachineNumber,
            side: v.fside,
            fc: v.ffc,
            sequenceMode: v.fsequenceMode,
            barcode: v.fbarcode,
            chipPressureValue: v.fchipPressureValue,
            customItem: booleanMap[v.fcustomItem],
            automaticCleaning: booleanMap[v.fautomaticCleaning], // 1
            taskSubmitNumber: v.fresultSubmitCode,
            orderPerson: v.forderPerson, // 下单人
            orderDate: v.forderDate, // 下单日期
            samplePosition: v.fsamplePosition, // 样本孔位
            originSamplePosition: v.foriginSamplePosition, // 原始样本定位
            volume: v.fvolume, // 体积
            targetPosition: v.ftargetPosition, // 目标定位
            targetMaterial: v.ftargetMaterial, // 目标耗材
            dnbConcentration1: v.fdnbConcentration1,
            dnbConcentration2: v.fdnbConcentration2,
            dnbConcentration3: v.fdnbConcentration3,
            dnbConcentration4: v.fdnbConcentration4,
            theoreticalTotal: v.ftheoreticalTotal,
            relativeTotal: v.frelativeTotal
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })
      }
    },
    // 下载任务单
    async handleChooseTask () {
      this.type = 'downloadTask'
      this.isSingle = false
      this.chooseTaskVisible = true
    },
    handleChooseDownloadScheduler  () {
      this.type = 'downloadScheduler'
      this.isSingle = true
      this.chooseTaskVisible = true
    },
    async handleDownload (ids = []) {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTask({fidList: ids, fstatus: 1}))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    async handleDownloadScheduler (ids) {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadScheduler({
        fidList: ids
      }))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 异常标记
    handleRegisterException () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择样本！')
        return
      }
      this.ids = [...this.selectedRows.keys()]
      this.registerExceptionVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;
  .btn-group {
    margin-bottom: 10px;
  }
}
</style>
