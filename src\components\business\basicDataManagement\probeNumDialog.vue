<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="编辑余量"
      width="40%"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="混合探针名称">
          <el-input v-model.trim="form.mixProbeName" size="mini" disabled clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item prop="remain" label="个性化探针余量">
          <el-input v-model.trim="form.remain" size="mini" maxlength="5" clearable placeholder="请输入">
            <template slot="append">rxn</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    id: {
      type: Number,
      required: false
    },
    mixProbeName: {
      type: String
    },
    remain: {
      type: Number
    }
  },
  data () {
    return {
      form: {
        mixProbeName: this.mixProbeName,
        remain: this.remain
      },
      loading: false,
      rules: {
        remain: [
          {required: true, message: '请输入余量', trigger: ['blur', 'change']},
          {required: true, pattern: /^\d{1,}$/, message: '请输入整数', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        this.form = {
          mixProbeName: this.mixProbeName,
          remain: this.remain
        }
      })
    },
    // 保存
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/system/probe/update_probe_config_remain',
            data: {
              fid: this.id,
              fremain: this.form.remain
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.visible = false
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
