<template>
  <div>
    <el-button type="primary" size="mini" style="margin-bottom: 10px;" @click="handleEdit">编辑生产信息</el-button>
    <el-table
      :data="tableData"
      ref="table"
      border
      class="table"
      size="mini"
      height="calc(100vh - 350px)"
    >
      <el-table-column prop="fproductionBatch" min-width="100" label="生产批次">
      </el-table-column>
      <el-table-column prop="foperationTimeText" min-width="100" label="上机时间"></el-table-column>
      <el-table-column prop="foperationPerson" min-width="100" label="上机人员"></el-table-column>
      <el-table-column prop="fproductLocation" min-width="100" label="产品位置"></el-table-column>
      <el-table-column prop="fdemand" min-width="100" label="需求量"></el-table-column>
      <el-table-column prop="festimatedDeliveryDate" min-width="120" label="预计货期"></el-table-column>
      <el-table-column prop="fqualityInspection" min-width="120" label="质检情况"></el-table-column>
      <el-table-column prop="qualityAppendixFileUrl" min-width="120" label="质检表附件">
        <template slot-scope="scope">
          <el-button type="text" @click="handleToPreview(scope.row.realData.id)">{{scope.row.fqualityAppendixFileUrl.fileName}}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <probe-production-dialog :pvisible.sync="probeProductionDataInfo.visible" :production-info="probeProductionDataInfo.productionInfo" @dialogConfirmEvent="getData"></probe-production-dialog>
  </div>
</template>

<script>
import util from '../../../util/util'
import ProbeProductionDialog from './probeProductionDialog'
export default {
  components: {ProbeProductionDialog},
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: [],
      probeProductionDataInfo: {
        visible: false,
        id: null
      }
    }
  },
  methods: {
    // 探针探针订购列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_production_detail',
        method: 'get',
        data: {
          fid: this.$route.query.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          data.forEach(v => {
            let file = v.fqualityAppendixFileUrl && (JSON.parse(v.fqualityAppendixFileUrl) || {})
            let item = {
              id: v.fid,
              fproductionBatch: v.fproductionBatch,
              foperationTimeText: v.foperationTime ? v.foperationTime.slice(0, 10) : '',
              foperationTime: v.foperationTime,
              foperationPerson: v.foperationPerson,
              fproductLocation: v.fproductLocation,
              fdemand: v.fdemand,
              festimatedDeliveryDate: v.festimatedDeliveryDate,
              fqualityInspection: v.fqualityInspection,
              fqualityAppendixFileUrl: file && {name: file.fileName, ...file},
              fqualityAppendixFileHttpUrl: v.fqualityAppendixFileHttpUrl
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 编辑生产信息
    handleEdit () {
      let productionInfo = this.tableData[0] || {}
      console.log(productionInfo.realData)
      this.probeProductionDataInfo = {
        visible: true,
        productionInfo: productionInfo.realData
      }
    },
    async handleToPreview (id) {
      if (id) {
        await this.$ajax({
          url: '/system/probe/download_quality_appendix_file',
          method: 'get',
          data: {
            id: id
          },
          responseType: 'blob'
        }).then(res => {
          util.readBlob(res.data).then(() => {
            util.downloadFile(res, true)
          }).catch(msg => {
            this.$message.error(msg)
          })
        })
      } else {
        this.$message.error('文件不存在')
      }
    }
  }
}
</script>

<style scoped></style>
