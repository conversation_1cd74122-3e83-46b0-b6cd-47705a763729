<template>
  <div>
    <el-table
      :data="tableData"
      ref="table"
      border
      class="table"
      size="mini"
      height="calc(100vh - 320px)"
    >
      <el-table-column prop="mixProbeName" min-width="100" label="混合探针名称"></el-table-column>
      <el-table-column prop="fprobeLocation1" min-width="100" label="个性化探针储位-1"></el-table-column>
      <el-table-column prop="fprobeLocation2" min-width="100" label="个性化探针储位-2"></el-table-column>
      <el-table-column prop="remain" min-width="100" label="个性化探针余量"></el-table-column>
      <el-table-column min-width="100" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEditNum(scope.row.realData)">编辑余量</el-button>
        </template>
      </el-table-column>
    </el-table>
    <probe-num-dialog
      :pvisible.sync="probeNumDataInfo.visible"
      :id="probeNumDataInfo.id"
      :mix-probe-name="probeNumDataInfo.mixProbeName"
      :remain="probeNumDataInfo.remain"
    ></probe-num-dialog>
  </div>
</template>

<script>
import util from '../../../util/util'
import ProbeNumDialog from './probeNumDialog'
export default {
  components: {ProbeNumDialog},
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: [],
      probeNumDataInfo: {
        id: '',
        mixProbeName: '',
        remain: '',
        visible: false
      }
    }
  },
  methods: {
    // 探针探针使用列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_config_detail',
        method: 'get',
        data: {
          fid: this.$route.query.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              id: v.fid,
              mixProbeName: v.fhybirdProbeName,
              probeStorage: v.fprobeLocation,
              fprobeLocation1: v.fprobeLocation1,
              fprobeLocation2: v.fprobeLocation2,
              remain: v.fremain
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 编辑余量
    handleEditNum (row) {
      this.probeNumDataInfo = {
        id: row.id,
        mixProbeName: row.mixProbeName,
        remain: row.remain,
        visible: true
      }
    }
  }
}
</script>

<style scoped></style>
