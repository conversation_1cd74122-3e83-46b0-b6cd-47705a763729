<template>
  <div>
    <el-drawer
      :visible.sync="visible"
      :show-close="false"
      @open="handleOpen">
      <template #title>
        <div class="search-custom-title-nav">
          <p class="title">查询</p>
          <div>
            <el-button size="mini" @click="handleClose">关闭</el-button>
            <el-button size="mini" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleSearch">确认</el-button>
          </div>
        </div>
      </template>
      <el-scrollbar class="search-main-wrap">
        <div>
          <div class="sub-title">查询条件</div>
          <el-form
            ref="form"
            :model="form"
            label-width="90px"
            label-suffix=":"
            size="mini"
            inline
            @keyup.enter.native="handleSearch"
          >
            <el-form-item label="样本类型">
              <el-select v-model.trim="form.sampleType" filterable clearable size="mini" class="input-width">
                <el-option
                  v-for="item in sampleTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Gene+编号">
              <el-input v-model.trim="form.geneCode" clearable placeholder="请输入Gene+编号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="样本名称">
              <el-input v-model.trim="form.sampleName" clearable placeholder="请输入样本名称" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="项目名称">
              <el-input v-model.trim="form.projectName" clearable placeholder="请输入项目名称" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="项目编号">
              <el-input v-model.trim="form.projectCode" clearable placeholder="请输入项目编号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="订单编号">
              <el-input v-model.trim="form.orderCode" clearable placeholder="请输入订单编号" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="送检单位">
              <el-input v-model.trim="form.inspectionDepartment" clearable placeholder="请输入送检单位" class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="到样状态">
              <el-select v-model.trim="form.sampleState" size="mini" class="input-width">
                <el-option :value="0" label="待确认"></el-option>
                <el-option :value="1" label="已确认"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="到样时间">
              <el-date-picker
                v-model.trim="form.arrivalSampleDate"
                :default-time="['00:00:00', '23:59:59']"
                type="datetimerange"
                clearable
                size="mini"
                prefix-icon="el-icon-date"
                range-separator="~"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-width"
                style="width: 310px">
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="false" style="margin-top: 30px;">
          <div class="sub-title">常用查询</div>
          <div style="height: calc(100vh - 50px - 400px - 100px)">
            <el-tooltip :key="item.title" v-for="item in commonSearch" placement="bottom">
              <div slot="content">
                <span>Gene+编号：{{item.geneNumber}}</span><br/>
                <span>样本名称：{{item.sampleName}}</span><br/>
                <span>项目名称：{{item.projectName}}</span><br/>
                <span>项目编号：{{item.projectCode}}</span><br/>
                <span>订单编号：{{item.orderCode}}</span><br/>
                <span>送检单位：{{item.unit}}</span><br/>
                <span>到样时间：{{item.arrivalSampleDate}}</span><br/>
                <span>样本状态：{{item.sampleState}}</span><br/>
              </div>
              <el-tag
                :disable-transitions="false"
                closable
                @close="handleTagClose(item)">
                {{item.title}}
              </el-tag>
            </el-tooltip>
            <el-tooltip v-if="!inputVisible && commonSearch.length < 10" placement="top">
              <div slot="content">
                <span>设置查询条件后，点击“+”，命名可保存常用查询条件</span>
              </div>
              <el-button
                size="mini"
                class="button-new-tag"
                @click="showInput">+</el-button>
            </el-tooltip>
            <el-input
              v-model="inputValue"
              ref="saveTagInput"
              v-else-if="commonSearch.length < 10"
              size="mini"
              class="input-new-tag"
              maxlength="8"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            >
            </el-input>
          </div>
        </div>
      </el-scrollbar>
    </el-drawer>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../util/mixins'
export default {
  name: `searchParamsPath`,
  mixins: [mixins.dialogBaseInfo],
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      form: {
        geneCode: '', // Gene+编号
        sampleName: '', // 样本名称
        projectName: '', // 项目名称
        projectCode: '', // 项目编号
        orderCode: '', // 订单编号
        inspectionDepartment: '', // 送检单位
        arrivalSampleDate: '', // 到样时间
        sampleState: '' // 样本状态
      },
      sampleTypes: [
        {label: '外周血', value: '外周血'},
        {label: '组织', value: '组织'},
        {label: '石蜡包埋组织', value: '石蜡包埋组织'},
        {label: '血浆', value: '血浆'},
        {label: '细胞', value: '细胞'},
        {label: '全基因组DNA', value: '全基因组DNA'},
        {label: '游离DNA', value: '游离DNA'},
        {label: '羊水', value: '羊水'},
        {label: '干血片', value: '干血片'},
        {label: '唾液', value: '唾液'},
        {label: '拭子', value: '拭子'},
        {label: '胸腹水', value: '胸腹水'},
        {label: '脑脊液', value: '脑脊液'},
        {label: 'RNA', value: 'RNA'},
        {label: '动物组织', value: '动物组织'},
        {label: '植物组织', value: '植物组织'},
        {label: '微生物组织', value: '微生物组织'},
        {label: '杂交文库', value: '杂交文库'},
        {label: '文库', value: '文库'}
      ],
      commonSearch: [], // 常用查询
      inputVisible: false,
      inputValue: ''
    }
  },
  methods: {
    handleOpen () {
      this.commonSearch = []
      this.$ajax({
        method: 'post',
        url: '/system/page_query/list_query',
        data: {
          fpage: 'sampleConfirm'
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          for (let i = 0; i < result.data.length; i++) {
            let val = JSON.parse(result.data[i].fparam)
            val.fid = result.data[i].fid
            this.commonSearch.push(val)
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleReset () {
      this.form = {
        geneCode: '', // Gene+编号
        sampleName: '', // 样本名称
        projectName: '', // 项目名称
        projectCode: '', // 项目编号
        orderCode: '', // 订单编号
        inspectionDepartment: '', // 送检单位
        arrivalSampleDate: '', // 到样时间
        sampleState: '' // 样本状态
      }
    },
    handleSearch () {
      this.form.geneCode = this.form.geneCode.replace(/[，、 ;]/g, ',')
      this.$emit('dialogConfirmEvent', this.form)
      this.visible = false
    },
    // 删除常用查询条件
    async handleTagClose (tag) {
      await this.$confirm(`是否删除该常用查询条件?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.$ajax({
        url: '/system/page_query/delete_query',
        method: 'get',
        data: {fids: tag.fid.toString()}
      }).then(result => {
        if (result.code === '200') {
          this.commonSearch.splice(this.commonSearch.indexOf(tag), 1)
          this.$message.success('删除成功')
        } else {
          this.$message.error(result.message)
        }
      })
    },

    showInput () {
      // 获取对象中的key值
      let keys = Object.keys(this.form)
      // 遍历data，当data里面的key都没值时，返回true
      let noCondition = keys.every((k, index) => {
        let v = this.form[k]
        return !v && !null
      })
      // 判断查询条件是否为空 为空，则提示
      if (noCondition) {
        this.$message.error('查询条件为空，请输入相关条件')
        return
      }
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    // 常用查询条件输入框失焦
    handleInputConfirm () {
      let inputValue = this.inputValue
      if (inputValue) {
        const Data = {
          title: inputValue,
          geneNumber: this.form.geneCode,
          sampleName: this.form.sampleName,
          projectName: this.form.projectName,
          projectCode: this.form.projectCode,
          orderCode: this.form.orderCode,
          unit: this.form.inspectionDepartment,
          arrivalSampleDate: this.form.arrivalSampleDate,
          sampleState: this.form.sampleState
        }
        this.$ajax({
          method: 'post',
          url: '/system/page_query/save_query',
          data: {
            fpage: 'sampleConfirm',
            fname: inputValue,
            fparam: JSON.stringify(Data)
          }
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.commonSearch.push(Data)
          } else {
            this.$message.error(result.message)
          }
        })
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>

<style scoped lang="scss">
.search-custom-title-nav {
  display: flex;
  justify-content: space-between;
  .title {
    height: 30px;
    line-height: 30px;
    padding-left: 5px;
    border-left: 4px solid #539fff;
  }
}
.search-main-wrap {
  margin: 0 30px;
  .sub-title {
    font-size: 16px;
    overflow: scroll;
    margin-bottom: 10px;
    color: #539fff;
  }
}
.el-tag {
  margin-left: 10px;
  margin-bottom: 5px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  margin-bottom: 5px;
  vertical-align: bottom;
}
.input-width{
  width: 310px;
}
</style>
