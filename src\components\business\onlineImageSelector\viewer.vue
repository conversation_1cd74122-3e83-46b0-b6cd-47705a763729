<template>
  <div class="viewer-page">
    <header class="header">
      <div class="title">在线图片查看</div>
      <div class="ops">
        <el-tag size="mini" type="info">模式：{{ mode==='edit' ? '编辑' : '查看' }}</el-tag>
        <el-button size="mini" @click="$router.back()">返回</el-button>
        <el-button size="mini" type="primary" v-if="mode==='edit'" @click="saveMarkers">保存标记</el-button>
      </div>
    </header>

    <section class="body">
      <div class="left">
        <tile-image-viewer
          ref="viewer"
          v-if="imageId"
          :image-id="imageId"
          :mode="mode"
          :um-per-px="umPerPx"
          :markers="markers"
          @selection-change="onSelectionChange"
          @markers-change="onMarkersChange"
          @request-save="saveMarkers"
        />
      </div>
      <div class="right">
        <inspector-panel
          :um-per-px="umPerPx"
          :mm-w="mmW"
          :mm-h="mmH"
          :chip="chip"
          :angle="sel.angle"
          @update:umPerPx="v => umPerPx = v"
          @applySpec="applySpec"
          @moveByMm="moveByMm"
          @setAngle="setAngle"
          @rotateBy="rotateBy"
          @saveMarkers="saveMarkers"
          @loadMarkers="loadMarkers"
          @resetView="resetView"
        />
      </div>
    </section>
  </div>
</template>

<script>
import TileImageViewer from './TileImageViewer.vue'
import InspectorPanel from './InspectorPanel.vue'
import api from '@/api/onlineImage'

export default {
  name: 'OnlineImageViewerPage',
  components: { TileImageViewer, InspectorPanel },
  created () {
    const { id, mode } = this.$route.query
    this.imageId = id
    if (mode) this.mode = mode
    this.loadMarkers()
  },
  data () {
    return {
      imageId: null,
      mode: 'view',
      umPerPx: 0.345,
      chip: 'A1',
      mmW: 6.5,
      mmH: 6.5,
      markers: [],
      sel: { cx: 0, cy: 0, sw: 0, sh: 0, angle: 0 }
    }
  },
  methods: {
    onSelectionChange (s) {
      this.sel = { ...s }
    },
    onMarkersChange (list) {
      this.markers = Array.isArray(list) ? list : []
    },
    applySpec ({ type, mmW, mmH }) {
      this.chip = type
      this.mmW = mmW
      this.mmH = mmH
    },
    moveByMm (dxMm, dyMm) {
      const v = this.umPerPx > 0 ? this.umPerPx : 0.345
      const dx = (dxMm * 1000) / v
      const dy = (dyMm * 1000) / v
      this.$refs.viewer.moveBy(dx, dy)
    },
    setAngle (v) {
      this.$refs.viewer.setAngle(v)
    },
    rotateBy (d) {
      this.$refs.viewer.rotateBy(d)
    },
    resetView () {
      this.$refs.viewer.resetView()
    },
    async loadMarkers () {
      if (!this.imageId) return
      try {
        const res = await api.getMarkers(this.imageId)
        const list = Array.isArray(res) ? res : (Array.isArray(res.data) ? res.data : (res.data && res.data.markers ? res.data.markers : []))
        this.markers = list
      } catch (e) {}
    },
    async saveMarkers () {
      if (!this.imageId) return
      try {
        const payload = this.markers && this.markers.length ? this.markers : [{
          cx: this.sel.cx,
          cy: this.sel.cy,
          sw: this.sel.sw,
          sh: this.sel.sh,
          angle: this.sel.angle,
          umPerPx: this.umPerPx,
          chip: this.chip,
          mmW: this.mmW,
          mmH: this.mmH
        }]
        await api.saveMarkers(this.imageId, payload)
        this.$message.success('已保存')
      } catch (e) {}
    }
  }
}
</script>

<style scoped>
.viewer-page{
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 8px;
  background: #fff;
  color: #303133;
}
.header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px 12px;
}
.body{
  display: grid;
  grid-template-columns: 1fr minmax(260px, 320px);
  gap: 8px;
  height: 100%;
  min-height: 0;
}
.left, .right{min-width: 0}
.left{display: grid; grid-template-rows: 1fr; min-height: 0}
.right{display: grid; grid-template-rows: 1fr; min-height: 0}
</style>
