import ExportDialogPlugin from './errorDialog.vue'

const ErrorDialog = {}

ErrorDialog.install = function (Vue) {
  let Dialogcom = Vue.extend(ExportDialogPlugin)
  let instance = new Dialogcom()
  Vue.prototype.$showSequencingErrorDialog = function ({tableData = [], isShowButton = true}) {
    instance.visible = true
    instance.tableData = tableData
    instance.totalPage = tableData.length
    instance.currentPage = 1
    instance.isShowButton = isShowButton
  }
  instance.$mount(document.createElement('div'))
  document.body.appendChild(instance.$el)
}

export default ErrorDialog
