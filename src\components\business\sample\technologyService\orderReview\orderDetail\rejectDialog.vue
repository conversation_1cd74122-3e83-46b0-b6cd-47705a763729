<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      v-drag-dialog
      :before-close="handleClose"
      width="800px"
      @open="handleOpen">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        v-if="visible"
        label-width="110px"
        size="mini"
        label-suffix="：">
        <template v-if="step === 1">
          <el-form-item label="驳回说明" prop="notes">
            <el-input
              v-model.trim="form.notes"
              :rows="3"
              type="textarea"
              maxlength="150"
              show-word-limit
              class="form-width"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="图片说明">
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :file-list="form.picList"
              :headers="headers"
              :on-success="handleOnSuccess"
              :on-error="handleOnError"
              :on-progress="handleProgress"
              :before-upload="handleBeforeUpload"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              multiple
              accept="image/jpg,image/jpeg,image/png"
              list-type="picture-card">
              <i class="el-icon-plus"></i>
            </el-upload>
            <Tips :size="10"></Tips>
            <el-dialog :visible.sync="dialogVisible" title="图片预览" append-to-body>
              <img :src="dialogImageUrl" width="100%" alt="">
            </el-dialog>
          </el-form-item>
        </template>
        <template v-else>
          <div class="email-content">
            <el-form-item label="邮件标题" prop="emailTitle">
              <el-input
                v-model.trim="form.emailTitle"
                maxlength="150"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="邮件正文" prop="emailContent">
              <div class="form-width">
                <edit
                  :value="form.emailContent"
                  @input="handleEmailContentInput"/>
              </div>
            </el-form-item>
            <el-form-item label="附件文件">
              <el-upload
                :action="uploadUrl"
                :headers="headers"
                :on-remove="handleRemove"
                :on-error="handleOnError"
                :before-upload="handleChange"
                :on-success="handleOnSuccessFile"
                class="upload-demo"
                multiple>
                <el-button size="mini" icon="el-icon-plus">添加附件</el-button>
                <Tips :size="50"></Tips>
              </el-upload>
            </el-form-item>
            <el-form-item label="收件邮箱" prop="inbox">
              <el-input
                v-model.trim="form.inbox"
                :rows="2"
                maxlength="150"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="抄送邮箱" prop="sendEmail">
              <el-input
                v-model.trim="form.sendEmail"
                maxlength="150"
                class="form-width"
                placeholder="请输入"></el-input>
            </el-form-item>
          </div>
        </template>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="onProgress" v-if="step === 1" size="mini" type="primary"
                   @click="handleNext">下一步</el-button>
        <el-button :loading="loading" v-else size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../../../../util/mixins'
import constants from '../../../../../../util/constants'
import Cookies from 'js-cookie'
import util from '../../../../../../util/util'

export default {
  name: `modificationDescriptionDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    /**
     * pagetype: 文库类型：1: illumina 2:mgi 3：组织核酸样本信息
     * projectInfo: 项目信息
     * */
    pagetype: {
      type: Number || String
    },
    orderId: {
      type: Number || String
    },
    orderCode: {
      type: String
    },
    projectInfo: {
      type: Object || {}
    }
  },
  computed: {},
  data () {
    return {
      title: '驳回',
      step: 1, // 步骤
      form: {
        notes: '', // 修改说明
        picList: [], // 图片数明
        emailTitle: '', // 邮件标题
        emailContent: '', // 邮件正文
        attachFile: [], // 附件文件
        inbox: '', // 收件邮箱
        sendEmail: '' // 寄件邮箱
      },
      loading: false,
      headers: {
        token: Cookies.get('token')
      },
      uploadUrl: constants.JS_CONTEXT + '/order/upload_file',
      onProgress: false, // 文件是否正在上传
      dialogImageUrl: '',
      dialogVisible: false,
      linkUrl: constants.IFRAME_URL + '/main/abnormalOrder?orderCode=' + (this.orderCode ? encodeURIComponent(util.encryptAES(this.orderCode)) : ''),
      rules: {
        notes: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        emailTitle: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        emailContent: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        inbox: [
          {required: true, message: '请输入', trigger: 'blur'},
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ],
        sendEmail: [
          {required: false, validator: util.validateElementEmail, trigger: ['change', 'blur']}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      const regionEmail = constants.REGION_EMAIL
      this.linkUrl = constants.IFRAME_URL + '/main/abnormalOrder?orderCode=' + (this.orderCode ? encodeURIComponent(util.encryptAES(this.orderCode)) : '')
      this.step = 1
      const projectInfo = this.projectInfo || {}
      const today = util.dateFormatter(new Date().getTime(), false, '')
      this.form = {
        notes: '', // 修改说明
        picList: [], // 图片数明
        emailTitle: `【订单信息确认】- ${projectInfo.projectCode} - ${projectInfo.projectName} - ${this.orderCode} - ${today}`, // 邮件标题
        emailContent: '', // 邮件正文
        attachFile: [], // 附件文件
        inbox: projectInfo.email, // 收件邮箱
        sendEmail: [regionEmail[projectInfo.area], projectInfo.saleEmail, projectInfo.fprojectManagerEmail].filter(Boolean).join(';')// 寄件邮箱
      }
    },
    getBase64 (file) {
      return new Promise((resolve, reject) => {
        // FileReader类就是专门用来读文件的
        const reader = new FileReader()
        // 开始读文件
        // readAsDataURL: dataurl它的本质就是图片的二进制数据， 进行base64加密后形成的一个字符串，
        reader.readAsDataURL(file)
        // 成功和失败返回对应的信息，reader.result一个base64，可以直接使用
        reader.onload = () => resolve(reader.result)
        // 失败返回失败的信息
        reader.onerror = error => reject(error)
      })
    },
    handleChange (file) {
      if (this.form.attachFile.length > 9) {
        this.$message.error('上传数量超出限制')
        return false
      }
      if (file.size > 1024 * 1024 * 50) {
        this.$message.error('文件不能大于50M，请重新上传')
        return false
      }
      return true
    },
    // 下一步
    handleNext () {
      this.$refs.form.validateField(['notes'], async (valid) => {
        if (!valid) {
          this.title = '邮件信息确认'
          this.step = 2
          let imgList = []
          let length = this.form.picList.length
          console.log(length)
          for (let i = 0; i < length; i++) {
            let item = this.form.picList[i]
            let base64 = await this.getBase64(item.raw)
            imgList.push(`<img src="${base64}" style="max-width: 80%; object-fit: contain; margin: 2px;" />`)
          }
          this.form.emailContent =
            `您好，<br/>包裹已收到，订单${this.orderCode}信息与实际样本有不符处。
            <br/>驳回原因：${this.form.notes}
            <div style="display: flex;flex-wrap: wrap;">
              ${imgList}
            </div>
            请点击链接 <a style="color: #4d4bf1; text-decoration: underline;" href="${this.linkUrl}">${this.linkUrl}</a>，
            确认问题并对订单进行修改，修改完成后我们会及时完成录入并流入检测，请知悉。
            <br/>祝好！`
        }
      })
    },
    handleEmailContentInput (val) {
      this.form.emailContent = val
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let picList = this.form.picList || []
          picList = picList.map(v => {
            return {
              group: v.group,
              path: v.path
            }
          })
          this.loading = true

          this.$ajax({
            url: '/order/send_reject_email',
            data: {
              frejectReason: this.form.notes,
              fattachment: this.form.attachFile,
              frecipientEmail: this.form.inbox,
              fcarbonCopyRecipients: this.form.sendEmail
                .replace(/，/g, ';')
                .replace(/,/g, ';')
                .split(';')
                .filter(Boolean)
                .join(';'),
              ftext: this.form.emailContent,
              fsubject: this.form.emailTitle,
              forderId: this.orderId,
              frejectPicArr: JSON.stringify(picList)
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$emit('dialogConfirmEvent', this.form)
              this.visible = false
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 提交前的函数
    handleBeforeUpload (file) {
      let name = file.name
      if (this.form.picList.length > 9) {
        this.$message.error('上传数量超出限制')
        return false
      }
      if (!/\.(jpg|png|jpeg)$/.test(name)) {
        this.$message.error('只能上传jpg、png或jpeg的图片')
        return false
      }
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件不能大于10M，请重新上传')
        return false
      }
      return true
    },
    // 提交成功回调
    handleOnSuccess (res, file, fileList) {
      this.onProgress = false
      if (res && res.code === this.SUCCESS_CODE) {
        file.onlineUrl = res.data.absolutePath
        file.group = res.data.group
        file.path = res.data.path
      }
      this.form.picList = [...fileList]
    },
    // 提交失败回调
    handleOnError () {
      this.$message.error('上传出现错误')
      this.onProgress = false
    },
    // 文件上传时
    handleProgress () {
      this.onProgress = true
    },
    handleRemove (file, fileList) {
      this.onProgress = false
      this.form.picList = [...fileList]
    },
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 附件提交成功回调
    handleOnSuccessFile (res, file, fileList) {
      this.onProgress = false
      let name = file.name
      fileList.pop()
      const index = fileList.findIndex(v => v.name === name)
      const attachFileIndex = this.form.attachFile.findIndex(v => v.name === res.name)
      if (index !== -1 && attachFileIndex !== -1) {
        this.form.attachFile[index] = res.data
        fileList[index] = file
      } else {
        this.form.attachFile.push(res.data)
        fileList.push(file)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.form-width {
  width: 600px;
}

.email-content {
  padding-top: 15px;
  border: 1px solid #efefef;
}

.img {
  width: 150px;
  height: 150px;
  margin: 5px;
}
</style>
