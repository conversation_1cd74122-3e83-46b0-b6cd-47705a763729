<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="驳回"
      width="600px"
      @open="handleOpen">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        v-if="visible"
        label-width="100px"
        size="mini">
        <el-form-item v-if="orderNum" label="申请单号">
          <span>{{orderNum}}</span>
        </el-form-item>
        <el-form-item v-if="sampleCode" label="样本编号">
          <span>{{sampleCode}}</span>
        </el-form-item>
        <el-form-item v-if="orderNum" label="驳回备注" prop="rejectReason">
          <el-select v-model.trim="form.rejectReason" clearable>
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in orderRejectOptions"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="sampleCode" label="驳回备注" prop="rejectReason">
          <el-select v-model.trim="form.rejectReason" clearable>
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in sampleRejectOptions"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.rejectReason === '其他'" label="其他原因" prop="rejectReasonOther">
          <el-input v-model.trim="form.rejectReasonOther" maxlength="20" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '../../../util/mixins'
export default {
  name: `turnoverLibraryManagementRejectDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
    // 两种状态，一种驳回订单，一种是驳回样本编号
    // 通过传过来得是订单编号还是样本编号进行判断
    // 两种状态下，参数都不同
    orderNum: String,
    sampleCode: String
  },
  data () {
    return {
      loading: false,
      orderRejectOptions: [
        {label: '样本编号填写错误', value: '样本编号填写错误'},
        {label: '样本类型填写错误', value: '样本类型填写错误'},
        {label: '样本数量不准确', value: '样本数量不准确'},
        {label: '本次入库没有此样本', value: '本次入库没有此样本'},
        {label: '样本破损', value: '样本破损'},
        {label: '其他', value: '其他'}
      ],
      sampleRejectOptions: [
        {label: '样本编号填写错误', value: '样本编号填写错误'},
        {label: '样本类型填写错误', value: '样本类型填写错误'},
        {label: '样本数量不准确', value: '样本数量不准确'},
        {label: '本次入库没有此样本', value: '本次入库没有此样本'},
        {label: '样本破损', value: '样本破损'},
        {label: '其他', value: '其他'}
      ],
      form: {
        rejectReason: '',
        rejectReasonOther: ''
      },
      rules: {
        rejectReason: [
          {required: true, message: '请选择', trigger: 'change'}
        ],
        rejectReasonOther: [
          {required: true, message: '请输入', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.form = {
        rejectReason: '',
        rejectReasonOther: ''
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let url = ''
          let data = {
            rejectMark: this.form.rejectReason === '其他' ? this.form.rejectReasonOther : this.form.rejectReason
          }
          if (this.orderNum) {
            url = '/sample/order/reject_order'
            data.orderNumber = this.orderNum
          } else if (this.sampleCode) {
            url = '/sample/order/reject_sample'
            data.sampleNumList = [this.sampleCode]
          }
          this.$ajax({
            url: url,
            data: data
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$emit('dialogConfirmEvent')
              this.visible = false
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
