<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :append-to-body="true"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="40%">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="mini" label-suffix="：">
        <el-form-item label="文献分类" prop="referenceType">
          <el-select v-model="form.referenceType" placeholder="请选择" style="width: 100%;">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in referenceTypeList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文献ID" prop="referenceId">
          <el-input v-model="form.referenceId" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="文献名称" prop="referenceName">
          <el-input v-model="form.referenceName" :autosize="{minRows: 5}" type="textarea" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'saveReferencesDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        if (this.pdata.libraryId === '') {
          this.title = '新增文献'
          this.type = 1
          this.form = {
            libraryId: null,
            referenceType: '',
            referenceId: '',
            referenceName: ''
          }
        } else {
          this.type = 2
          this.title = '修改文献'
          this.form = this.pdata
        }
      }
    }
  },
  computed: {},
  data () {
    return {
      loading: false,
      title: '新增文献',
      type: '',
      visible: this.pvisible,
      form: {
        libraryId: null,
        referenceType: '',
        referenceId: '',
        referenceName: ''
      },
      rules: {
        referenceType: [
          {required: true, message: '请选择文献分类', trigger: ['blur', 'change']}
        ],
        referenceId: [
          {required: true, message: '请填写文献ID', trigger: 'blur'}
        ],
        referenceName: [
          {required: true, message: '请填写文献名称', trigger: 'blur'}
        ]
      },
      referenceTypeList: [
        {
          label: 'PMID文献',
          value: 1
        },
        {
          label: '会议摘要',
          value: 2
        },
        {
          label: '临床试验',
          value: 3
        },
        {
          label: '其他',
          value: 4
        }
      ]
    }
  },
  methods: {
    handleClose () {
      this.$emit('saveReferencesDialogCloseEvent')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/read/unscramble/save_or_update_literature',
            data: {
              rId: this.form.referenceId.trim(),
              rName: this.form.referenceName.trim(),
              rClassified: this.form.referenceType,
              rLibraryId: this.form.libraryId
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('saveReferencesDialogConfirmEvent', this.type, result.data)
              this.$refs.form.resetFields()
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
