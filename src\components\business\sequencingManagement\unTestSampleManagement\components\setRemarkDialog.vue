<script>
import mixins from '../../../../../util/mixins'
import {awaitWrap} from '../../../../../util/util'
import {setRemark} from '../../../../../api/sequencingManagement/unTestSampleApi'

export default {
  name: 'setRemarkDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      loading: false,
      form: {
        remark: ''
      },
      rules: {
        remark: [{
          required: true,
          message: '请输入备注',
          trigger: 'blur'
        }]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
    },
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      const {res} = await awaitWrap(setRemark({
        fids: this.ids,
        fremark: this.form.remark
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('设置成功')
        this.visible = false
        this.$emit('dialogConfirmEvent')
      }
      this.loading = false
    }
  }
}
</script>

<template>
  <el-dialog
    v-drag-dialog
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="visible"
    :before-close="handleClose"
    title="添加备注"
    width="500px"
    @opened="handleOpen">
    <div>已勾选{{ids.length}}条数据，请添加备注:</div>
    <br/>
    <el-form ref="form" :model="form" label-suffix=":" label-width="80px">
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" size="mini" maxlength="500" clearable placeholder="请输入备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
