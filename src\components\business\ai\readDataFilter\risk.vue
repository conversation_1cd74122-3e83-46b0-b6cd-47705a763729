<template>
  <div>
    <div class="card-wrapper">
      <el-table
        :data="tableData"
        ref="table"
        class="table"
        size="mini"
        border
        height="calc(100vh - 40px - 154px - 20px - 20px)"
        style="width: 100%;"
        @select="handleSelect"
        @select-all="handleSelectAll"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45" fixed="left"></el-table-column>
        <el-table-column label="基因名称" prop="gene" min-width="120px"></el-table-column>
        <el-table-column label="癌症类型" prop="tumorTypes" min-width="120px"></el-table-column>
        <el-table-column label="普通人患癌风险" prop="cancerRisk" min-width="120px"></el-table-column>
        <el-table-column label="突变携带者患癌风险" prop="mutantCancerRisk" min-width="120px">
          <template slot-scope="scope">
            <div v-html="scope.row.mutantCancerRisk"></div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>

export default {
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    async getData () {
      const {code, data} = await this.$ajax({
        url: '/read/bigAi/get_report_h_risk_of_illness_list',
        loadingDom: '.table',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get'
      })
      if (code && code === this.SUCCESS_CODE) {
        let rows = data.rows || []
        this.selectedRows = new Map()
        this.tableData = []
        rows.forEach(v => {
          let item = {
            id: v.fid,
            gene: v.gene,
            tumorTypes: v.tumorTypes,
            cancerRisk: v.cancerRisk,
            mutantCancerRisk: v.mutantCancerRisk
          }
          item.realData = JSON.parse(JSON.stringify(item))
          this.tableData.push(item)
        })
      }
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.btn {
  margin: 10px;
}
</style>
