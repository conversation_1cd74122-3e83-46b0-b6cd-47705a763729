<template>
  <div style="height: 95vh">
    <div style="width: 100%; overflow: auto; padding: 10px;" v-html="tableau"></div>
  </div>
</template>

<script>
// import XLSX from 'xlsx'
// import axios from 'axios'
export default {
  name: 'HelloWorld',
  async created () {
    // let url = this.$route.query.qualityAppendixFileUrl || ''
    await this.$ajax({
      url: '/system/probe/download_quality_appendix_file',
      data: {
        id: this.$route.query.id
      },
      method: 'get',
      responseType: 'arraybuffer' // 设置响应体类型为arraybuffer
    }).then(async ({data}) => {
      console.log(data)
      // eslint-disable-next-line no-undef
      let workbook = XLSX.read(new Uint8Array(data), {type: 'array'}) // 解析数据
      let worksheet = workbook.Sheets[workbook.SheetNames[0]] // workbook.SheetNames 下存的是该文件每个工作表名字,这里取出第一个工作表
      // eslint-disable-next-line no-undef
      this.tableau = XLSX.utils.sheet_to_html(worksheet) // 渲染
    })
  },
  data () {
    return {
      tableau: ''
    }
  }
}
</script>

<style>
.handsontable {
  font-size: 13px;
  color: #222;
}
</style>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
/deep/ table{
  border-spacing: 0;/*把单元格间隙设置为0*/
  border-collapse: collapse;/*设置单元格的边框合并为1*/
}
//如果给table设置边框，中间就没有实线，因此决定给td加边框
/deep/ td{
  border: 1px solid #ACBED1;
}

</style>
