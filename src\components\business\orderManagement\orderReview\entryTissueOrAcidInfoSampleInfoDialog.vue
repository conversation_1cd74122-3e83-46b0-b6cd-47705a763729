<template>
  <div>
    <el-dialog
        :visible.sync="visible"
        :close-on-click-modal="false"
        :before-close="handleClose"
        title="编辑"
        width="700px"
        @open="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" v-if="visible" label-position="top" label-width="100px" size="mini" label-suffix="：" inline>
        <el-form-item label="吉因加编号" prop="geneplusNum">
          <el-input v-model.trim="form.geneplusNum" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本名称" prop="sampleName">
          <el-input v-model.trim="form.sampleName" maxlenght="30" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="组织来源" prop="tissueSource">
          <el-input v-model.trim="form.tissueSource" class="form-width" maxlenght="100" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="浓度（ng/ul）" prop="concentration">
          <el-input v-model.trim="form.concentration" type="number" class="form-width" maxlenght="10" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="体积" prop="volume">
          <el-input v-model.trim="form.volume" maxlenght="10" type="number"  class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="数据量（G）" prop="dataNum">
          <el-input v-model.trim="form.dataNum" maxlenght="10" type="number" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本量" prop="sampleTotal">
          <el-input v-model.trim="form.sampleTotal" maxlenght="10" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model.trim="form.notes" maxlenght="10" class="form-width" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
export default {
  name: 'entryTissueOrAcidInfoSampleInfoDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    pdata: {
      type: Object | null
    }
  },
  data () {
    return {
      title: '',
      form: {
        geneplusNum: '',
        sampleName: '',
        species: '',
        tissueSource: '',
        concentration: '',
        volume: '',
        dataNum: '',
        sampleTotal: '',
        notes: ''
      },
      rules: {
        sampleName: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        dataNum: [
          {required: true, message: '请输入', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      if (this.pdata) {
        this.form = {...this.pdata.form}
      } else {
        this.form = {
          geneplusNum: '',
          sampleName: '',
          species: '',
          tissueSource: '',
          concentration: '',
          volume: '',
          dataNum: '',
          sampleTotal: '',
          notes: ''
        }
      }
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = {
            form: this.form
          }
          if (this.pdata) data.index = this.pdata.index
          this.$emit('dialogConfirmEvent', data)
          this.visible = false
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
