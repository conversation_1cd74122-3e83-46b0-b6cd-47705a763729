<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="样本日志"
      width="900px"
      @open="handleOpen">
      <div>
        <div style="margin-bottom: 20px;">
          <p style="font-weight: 600;margin-bottom: 15px;">样本基础信息</p>
          <div style="display: flex;justify-content: space-between;align-items: center;">
            <p>样本编号：{{baseInfo.sampleCode}}</p>
            <p>样本类型：{{baseInfo.sampleType}}</p>
            <p>样本状态：{{baseInfo.sampleStatus}}</p>
          </div>
        </div>
        <el-table
          :data="tableData"
          ref="sampleLog"
          class="sampleDataTable"
          style="width: 100%;"
          height="400">
          <el-table-column show-overflow-tooltip label="操作时间" width="180" prop="operatingTime"></el-table-column>
          <el-table-column show-overflow-tooltip label="操作" width="130" prop="operating"></el-table-column>
          <el-table-column show-overflow-tooltip label="操作人" width="90" prop="operator"></el-table-column>
          <el-table-column show-overflow-tooltip label="申请单号" width="180" prop="applicationOrder">
            <template slot-scope="scope">
              <el-button type="text" @click="handleShowModifyDialog(scope.row.applicationOrder)">{{scope.row.applicationOrder}}</el-button>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="申请人" width="130" prop="applicant"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本量" width="130" prop="sampleAmount"></el-table-column>
          <el-table-column show-overflow-tooltip label="样本存放位置" min-width="130" prop="position"></el-table-column>
          <el-table-column show-overflow-tooltip label="备注" width="130" prop="notes"></el-table-column>
        </el-table>
      </div>
      <modify-order-dialog
        :pvisible="modifyOrderDialogVisible"
        :order-id="currentApplicationOrder"
        :append-to-body="true"
        title="订单详情"
        page="sampleSearch"
        @dialogCloseEvent="modifyOrderDialogVisible = false"></modify-order-dialog>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import modifyOrderDialog from './modifyOrderDialog'
import util from '../../../util/util'
export default {
  name: 'sampleLogDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {
    modifyOrderDialog
  },
  props: {
    sampleId: {
      type: String | Number
    }
  },
  data () {
    return {
      baseInfo: {
        sampleCode: '-',
        sampleType: '-',
        sampleStatus: '-'
      },
      tableData: [],
      currentApplicationOrder: '',
      modifyOrderDialogVisible: false
    }
  },
  methods: {
    handleOpen () {
      this.$ajax({
        url: '/sample/get_sample_data_stream',
        method: 'get',
        data: {
          sampleId: this.sampleId
        },
        loadingDom: '.sampleDataTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          this.baseInfo.sampleCode = data.fsampleNumber || '-'
          this.baseInfo.sampleType = data.fsampleType || '-'
          this.baseInfo.sampleStatus = data.fsampleStatus || '-'
          let rows = data.dataStreamlist || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              operatingTime: v.foperationTime,
              operating: v.foperationName,
              operator: v.foperationUser,
              applicationOrder: v.forderNumer,
              applicant: v.fapplicant,
              sampleAmount: v.fsampleAmount,
              position: v.fsamplePlace,
              notes: v.fnotes
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleShowModifyDialog (orderId) {
      this.currentApplicationOrder = orderId
      this.modifyOrderDialogVisible = true
    }
  }
}
</script>

<style scoped>

</style>
