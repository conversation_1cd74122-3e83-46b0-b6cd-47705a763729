<template>
  <div>
    <el-dialog
      title="异常通知邮件（3封）"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      append-to-body
      width="800px"
      @open="handleOpen">
      <div class="content">
        <el-form
          ref="form"
          v-if="visible"
          :model="form"
          class="form"
          :rules="rules"
          label-width="80px"
          size="mini"
          label-suffix="：">
          <template>
            <div class="project-info">
              <div style="margin-right: 30px">项目名称：xxxxxxxxxx</div>
              <div style="margin-right: 30px">订单编号：xxxxxxxxxx</div>
              <div style="margin-right: 30px">勾选样本数量：10</div>
            </div>
            <div>
              <email-info></email-info>
            </div>
          </template>
        </el-form>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button v-if="showPreEmailButton" size="mini" type="primary" @click="handlePreview">上一封邮件</el-button>
        <el-button v-if="!showConfirmButton" size="mini" type="primary" @click="handleNext">下一封邮件</el-button>
        <el-button v-if="showConfirmButton" :loading="loading" size="mini" type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import xx form 'xxx'
import mixins from '@/util/mixins'

export default {
  name: `exceptionNoticeDialog`,
  mixins: [mixins.dialogBaseInfo],
  props: {
  },
  computed: {
    showConfirmButton () {
      return this.emailForm.length - 1 === this.activeIndex
    },
    showPreEmailButton () {
      return this.activeIndex !== 0
    }
  },
  data () {
    return {
      activeIndex: 0,
      loading: false,
      emailForm: [{}]
    }
  },
  methods: {
    handleOpen () {
    },
    handleConfirm () {},
    handlePreview () {
      this.activeIndex--
    },
    handleNext () {
      this.activeIndex++
    }
  }
}
</script>

<style scoped lang="scss">
.form-width {
  width: 600px;
}

.email-content {
  padding-top: 15px;
  height: 50vh;
  overflow: auto;
  border: 1px solid #efefef;
}

.img {
  width: 150px;
  height: 150px;
  margin: 5px;
}
.content {
  height: 50vh;
  overflow: auto;
  padding: 0 10px;
}

.project-info {
  display: flex;
  margin-bottom: 20px;
}
</style>
