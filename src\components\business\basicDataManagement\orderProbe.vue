<template>
  <div>
    <el-table
      :data="tableData"
      ref="table"
      border
      class="table"
      size="mini"
      height="calc(100vh - 320px)"
    >
      <el-table-column prop="sampleNum" min-width="100" label="组织样本编号">
        <template slot-scope="scope">
          <el-button type="text" @click="handleToOrderDetail(scope.row.fsarId)">{{scope.row.sampleNum}}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="mutationCount" min-width="100" label="报出变异数"></el-table-column>
      <el-table-column prop="coreMuts" min-width="100" label="核心panel Muts"></el-table-column>
      <el-table-column prop="customMuts" min-width="100" label="设计定制Muts"></el-table-column>
      <el-table-column prop="probeSubscribeTime" min-width="100" label="探针订购时间"></el-table-column>
      <el-table-column prop="mailSender" min-width="120" label="邮件发放人"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../util/util'
export default {
  mounted () {
    this.getData()
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 探针探针订购列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_subscribe_detail',
        method: 'get',
        data: {
          fid: this.$route.query.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              fsarId: v.fsarId,
              sampleNum: v.sampleNum,
              mutationCount: v.mutationCount,
              coreMuts: v.coreMuts,
              customMuts: v.customMuts,
              probeSubscribeTime: v.probeSubscribeTime,
              mailSender: v.mailSender
            }
            item.realData = item
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleToOrderDetail (fsarId) {
      this.$router.push('/business/subpage/probeOrderDetail?fsarId=' + fsarId)
    }
  }
}
</script>

<style scoped></style>
