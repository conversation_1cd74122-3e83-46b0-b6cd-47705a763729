<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :title="title"
    width="900px"
    @opened="handleOpen">
    <el-form
      ref="form"
      :model="form"
      size="mini"
      label-position="left"
      label-suffix=":"
      label-width="150"
      inline
      style="display: flex;">
      <el-form-item label="样本编号">
        <el-input v-model.trim="form.sampleCode" placeholder="请输入样本编号" clearable @keyup.enter.native="handleSearch"></el-input>
      </el-form-item>
      <el-form-item label="导入文件" style="margin-left: 40px">
        <el-upload
          ref="upload"
          :auto-upload="false"
          :file-list="fileList"
          :action="uploadUrl"
          :data="{
            type: signType,
            samples: sampleInfos
          }"
          :before-upload="handleBeforeUpload"
          :on-change="handleChange"
          :show-file-list="false"
          :on-error="handleError"
          :on-success="handleOnSuccess"
        >
          <el-input v-model.trim="fileName" placeholder="请输入导入文件" readonly></el-input>
        </el-upload>
        <el-button type="text" :loading="downloadLoading" size="mini" @click="handleDownload">下载导入模版</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="importLoading" size="mini" @click="handleImportExcel">导入</el-button>
      </el-form-item>
    </el-form>
    <div class="sample-list-wrapper">
      <div class="title">
        {{ info }} <span style="color: #409EFF">{{sampleList.length > 0 ? sampleList.length : ''}}</span> 个样本
      </div>
      <div class="sample-list-container">
        <div v-if="sampleList.length < 1" class="empty">暂无样本</div>
        <div v-for="(item, index) in sampleList" :key="item" class="item">
          <div>{{item}} <i class="el-icon-close deleted" @click="handleDeleteSample(index)"></i></div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import mixins from '../../../util/mixins'
import util, {awaitWrap, downloadFile, readBlob} from '../../../util/util'
import constants from '../../../util/constants'
import {downloadTemplate} from '../../../api/sequencingManagement/sequencingManagementApi'
import {getSampleBySampleCode, signOperate} from '../../../api/sampleLibraryManagement/sampleSearch'

export default {
  name: 'sampleSignDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    signType: {
      type: Number,
      default: null
    }
  },
  computed: {
    sampleInfos () {
      return util.setGroupData(this.form.sampleCode, '、', true)
    },
    fileName () {
      const file = this.fileList[0] || {}
      return file.name
    }
  },
  data () {
    return {
      form: {
        sampleCode: '',
        fileName: ''
      },
      loading: false,
      importLoading: false,
      downloadLoading: false,
      uploadUrl: constants.JS_CONTEXT + '/sample/can_sign_sample',
      uploadParams: {
        type: this.signType
      },
      fileList: [],
      sampleList: [],
      info: '',
      title: ''
    }
  },
  methods: {
    handleOpen () {
      const titleInfo = {
        0: '赋值销毁',
        1: '取消销毁',
        2: '赋值返样',
        3: '取消返样'
      }
      console.log(this.$refs)
      this.info = titleInfo[this.signType]
      this.title = `${this.info}标记操作`
      this.form = this.$options.data().form
      this.fileList = []
      this.sampleList = []
      this.$refs.upload.clearFiles()
    },
    // 根据样本编号查询样本
    async handleSearch () {
      if (!this.form.sampleCode) {
        this.$message.error('请输入样本编号')
        return
      }
      const {res} = await awaitWrap(getSampleBySampleCode({
        type: this.signType,
        samples: this.sampleInfos
      }, {isFormData: true}))
      if (res && res.code === this.SUCCESS_CODE) {
        const {successData = [], failData = []} = res.data
        if (failData.length > 0) {
          const tips = {
            0: '已出库',
            1: '已销毁',
            2: '已出库',
            3: '已返样'
          }
          this.$message.error({
            showClose: true,
            message: `${failData.join(',')}样本未找到记录，或者状态不是${tips[this.signType]}，无法标记`,
            duration: 0
          })
        }
        this.sampleList = successData
      }
    },
    /** ***************导入excel***************************/
    async handleDownload () {
      this.downloadLoading = true
      const {res} = await awaitWrap(downloadTemplate(this.type))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls)$/.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 10) {
          this.$message.error('文件大小超过限制，无法上传')
          this.importLoading = false
          return false
        } else {
          return true
        }
      } else {
        this.$message.error('只能上传xlsx或xls文件')
        this.importLoading = false
        return false
      }
    },
    async handleChange (file, fileList) {
      if (fileList.length > 1) {
        const message = '一次仅支持导入一份文件，请确认是否需要重新选择文件导入替换已导入文件？'
        const {err} = await awaitWrap(this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }))
        err ? fileList.pop() : fileList.shift()
      }
      this.fileList = fileList
    },
    handleImportExcel () {
      const fileList = this.$refs.upload.uploadFiles || []
      if (fileList.length < 1) {
        this.$message.error('请上传任务文件')
        return
      }
      this.importLoading = true
      this.$refs.upload.submit()
    },
    handleError (err, file, fileList) {
      console.log(err)
      this.importLoading = false
      this.$message.error({
        showClose: true,
        message: err.message,
        duration: 0
      })
    },
    handleOnSuccess (res) {
      if (res && res.code === this.SUCCESS_CODE) {
        const tips = {
          0: '已出库',
          1: '已销毁',
          2: '已出库',
          3: '已返样'
        }
        const {successData = [], failData = []} = res.data
        if (failData.length > 0) {
          let failSampleInfo = failData.join(',')
          if (failData.length > 6) {
            failSampleInfo = `${failData.slice(0, 6).join(',')}...等${failData.length}个`
          }
          this.$message.error({
            showClose: true,
            message: `${failSampleInfo}样本未找到记录，或者状态不是${tips[this.signType]}，无法标记`,
            duration: 0
          })
        }
        this.sampleList = successData
      }
      this.importLoading = false
    },
    /** ***************导入excel***************************/

    // 删除样本
    handleDeleteSample (index) {
      this.sampleList.splice(index, 1)
    },
    async handleConfirm () {
      if (this.sampleList.length < 1) {
        this.$message.error({
          showClose: true,
          message: `请选择样本`,
          duration: 0
        })
        return
      }
      this.loading = true
      const {res} = await awaitWrap(signOperate({
        samples: this.sampleList,
        type: this.signType
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$emit('dialogConfirmEvent')
        this.$message.success({
          message: '操作成功',
          duration: 5000
        })
        this.visible = false
      }
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.sample-list-wrapper {
  max-height: 300px;
  border: 1px solid #cccccc;
  border-radius: 5px;

  .title {
    border-bottom: 1px solid #cccccc;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
  }

  .sample-list-container {
    padding: 10px 0;
    display: flex;
    flex-wrap: wrap;
    //justify-content: space-between;
    max-height: 250px;
    overflow-y: auto;

    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;
      color: #cccccc;
    }

    .item {
      margin: 10px 25px;
    }
  }
}

.deleted {
  color: red;
  cursor: pointer;
}
</style>
