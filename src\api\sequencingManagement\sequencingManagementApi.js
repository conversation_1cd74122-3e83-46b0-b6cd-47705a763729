import { myAjax } from '@/util/ajax'

/**
 * 获取任务列表
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function getTaskList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/task_list_query',
    data: data,
    ...options
  })
}

/**
 * 获取任务列表
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function getChooseTaskList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/get_current_step_task',
    data: data,
    ...options
  })
}
/**
 * 获取任务列表
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function getErrorList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/exception/get_exception_list',
    data: data,
    ...options
  })
}

/**
 * 下载任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799132
 * @param options
 * @returns {*}
 */
export function downloadTask (data, options = {}) {
  return myAjax({
    url: '/experiment/schedule/download_task',
    responseType: 'blob',
    data: data,
    ...options
  })
}
/**
 * 保存任务单
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799139
 * @param options
 * @returns {*}
 */
export function saveTaskList (data, options = {}) {
  return myAjax({
    url: '/experiment/schedule/submit_task',
    data: data,
    ...options
  })
}

/**
 * 撤回任务
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799128
 * @param options
 * @returns {*}
 */
export function returnTask (data, options = {}) {
  return myAjax({
    url: '/experiment/schedule/withdraw_task',
    data: data,
    ...options
  })
}

/**
 * 下载模板
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function downloadTemplate (data, options = {}) {
  return myAjax({
    method: 'get',
    url: '/experiment/schedule/download_task_template',
    responseType: 'blob',
    data: data,
    ...options
  })
}

/**
 * 获取邮件信息
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function getEmailInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/exception/get_exception_sample_info',
    data: data,
    ...options
  })
}

/**
 * 获取邮件信息
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function getEmailDetail (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/exception/get_email_detail',
    data: data,
    ...options
  })
}

/**
 * 发送邮件
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205695&itf=5799145
 * @param options
 * @returns {*}
 */
export function sendEmail (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/exception/send_exception_email',
    data: data,
    ...options
  })
}

/**
 * 信息变更
 * @param data /experiment/pooling/info_processing_change
 * @param options
 * @returns {*}
 */
export function changeInfo (data, type = 0, options = {}) {
  const urls = {
    1: '/experiment/pooling/info_change',
    2: '/experiment/transform/info_change',
    4: '/experiment/cyclize/information_change',
    5: '/experiment/makeDNB/information_change'
  }
  return myAjax({
    method: 'post',
    url: urls[type],
    data: data,
    ...options
  })
}

/**
 * 移除样本
 * @param data http://www.baidu.com
 * @param options
 * @returns {*}
 */
export function removeSample (data, type = 1, options = {}) {
  const urls = {
    1: '/experiment/pooling/remove_sample',
    2: '/experiment/transform/remove_sample',
    3: '/experiment/cyclize/remove_sample',
    4: '/experiment/makeDNB/remove_sample'
  }
  return myAjax({
    method: 'post',
    url: urls[type],
    data: data,
    ...options
  })
}
/**
 * 保存异常信息
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205695&itf=5799126
 * @param options
 * @returns {*}
 */
export function saveException (data, type = 1, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/exception/save_exception',
    data: data,
    ...options
  })
}

/**
 * 保存结果
 * @param data http://www.baidu.com
 * @param type 流程类型
 * @param options
 * @returns {*}
 */
export function saveResult (data, type = 1, options = {}) {
  const urls = {
    1: '/experiment/pooling/import_pooling_result',
    2: '/experiment/transform/import_transform_result',
    3: '/experiment/cyclize/save_result',
    4: '/experiment/makeDNB/save_result'
  }
  return myAjax({
    method: 'post',
    url: urls[type],
    data: data,
    showErrorMessageBox: false,
    ...options
  })
}

/**
 * 获取导入结果url
 * @param type 流程类型 1 pooling 2: 转化 3: 环化 4: makeDnb
 * @returns {*}
 */
export function uploadUrls (type = 1) {
  const urls = {
    1: '/experiment/pooling/show_result_excel',
    2: '/experiment/transform/show_result_excel',
    3: '/experiment/cyclize/import_result_excel',
    4: '/experiment/makeDNB/import_result_excel'
  }
  return urls[type]
}

/**
 * 回填结果数据
 * @param data
 * @param type
 * @param options
 * @returns {*}
 */
export function getSampleInfo (data, type = 1, options = {}) {
  const urls = {
    1: '/experiment/pooling/show_result_task',
    2: '/experiment/transform/show_result_task',
    3: '/experiment/cyclize/show_result_task',
    4: '/experiment/makeDNB/show_result_task'
  }
  return myAjax({
    method: 'post',
    url: urls[type],
    data: data,
    ...options
  })
}

/**
 * 标记处理异常
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205695&itf=5799140
 * @param type
 * @param options
 * @returns {*}
 */
export function signErrorHandler (data, type = 1, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/exception/save_handle_exception',
    data: data,
    ...options
  })
}

/**
 * 获取测序样本池
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205701
 * @param type
 * @param options
 * @returns {*}
 */
export function getSampleInfoList (data, type = 1, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/sample_list_query',
    data: data,
    ...options
  })
}

/**
 * 获取样本详情列表
 * @param data
 * @param type
 * @param options
 * @returns {*}
 */
export function getSampleFlowDetailList (data, type = 1, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/get_running_sample_list',
    data: data,
    ...options
  })
}

/**
 * 获取样本质控详情列表
 * @param data
 * @param type
 * @param options
 * @returns {*}
 */
export function getSampleQcDetailList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/get_not_complete_lib_sample_list',
    data: data,
    ...options
  })
}

/**
 * 获取测序样本池
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205701
 * @param type
 * @param options
 * @returns {*}
 */
export function getSampleList (data, type = 1, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/get_sequence_sample_pool',
    data: data,
    ...options
  })
}

/**
 * 获取各环节未完成的样本列表
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205701
 * @param type
 * @param options
 * @returns {*}
 */
export function getSampleDetailList (data, type = 1, options = {}) {
  return myAjax({
    method: 'post',
    url: '/experiment/schedule/get_sequence_sample_pool',
    data: data,
    ...options
  })
}

/**
 * 添加样本
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799132
 * @param options
 * @returns {*}
 */
export function addSample (data, type = 0, options = {}) {
  const urls = {
    1: '/experiment/pooling/add_sample',
    2: '/experiment/transform/add_sample',
    4: '/experiment/cyclize/add_sample',
    5: '/experiment/makeDNB/add_sample'
  }
  return myAjax({
    url: urls[type],
    data: data,
    ...options
  })
}

/**
 * 获取任务单样本进展详情
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799132
 * @param options
 * @returns {*}
 */
export function getTaskDetail (data, options = {}) {
  return myAjax({
    url: '/experiment/schedule/get_sample_experiment_progress',
    data: data,
    ...options
  })
}

/**
 * 获取检测人或复核人
 * @param data http://172.16.50.15:3000/repository/editor?id=82&mod=205696&itf=5799132
 * @param options
 * @returns {*}
 */
export function getTestOrCheck (data, options = {}) {
  return myAjax({
    url: '/experiment/schedule/get_test_or_check_person',
    data: data,
    ...options
  })
}
export function getSampleDetail (data, options = {}) {
  return myAjax({
    url: '/experiment/schedule/get_sample_detail',
    data: data,
    ...options
  })
}

/**
 * 确认上机
 * @param data /experiment/new_build_lib/computer_practice
 * @param options
 * @returns {*}
 */
export function setComputerPracticeInfo (data, options = {}) {
  return myAjax({
    url: '/experiment/new_build_lib/computer_practice',
    data: data,
    ...options
  })
}
