<template>
  <div class="page">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top" size="mini" label-suffix="：">
      <div class="info">
        <el-row :gutter="10">
          <el-col :span="4">
            <el-form-item label="申请人">{{form.fapplicant}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售">{{form.fsaleMan}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="申请日期">{{form.fapplyTime}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="中心编号">{{form.fcenterCode}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="中心名称">{{form.fcenterName}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="通知邮箱">{{form.finformEmail}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="期望到达日期">{{form.fexpectReveiveTime}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="物料邮寄地址">{{form.fsendAddr}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="联系人">{{form.fcontactPerson}}</el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="电话">{{form.fcontactPhone}}</el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="detailed">
        <div class="title">发货清单(合计金额: {{totalPrice}})</div>
        <el-table
          :data="form.tableData"
          :span-method="spanMethod"
          size="mini"
          border
          class="deliveryInfoTable"
          style="width: 100%">
          <el-table-column prop="categoryName" label="类别" align="center" min-width="100"></el-table-column>
          <el-table-column prop="materialsName" label="品类" align="center" min-width="100"></el-table-column>
          <el-table-column prop="specName" label="规格" align="center" min-width="100"></el-table-column>
          <el-table-column prop="unit" label="单位" align="center" min-width="100"></el-table-column>
          <el-table-column prop="materialsNum" label="数量" align="center" min-width="100"></el-table-column>
          <el-table-column label="批次号" align="center" min-width="100">
            <template slot="header">批次号</template>

            <template slot-scope="scope">
            <template v-if="deliveryInfo.type === 4">
              <el-form-item :prop="'tableData.' + scope.$index + '.ratificationIssue'" label="">
                <el-input v-model.trim="scope.row.ratificationIssue" maxlength="20" clearable placeholder="请输入"></el-input>
              </el-form-item>
            </template>
            <template v-else>
              {{scope.row.ratificationIssue}}
            </template>
          </template>
          </el-table-column>
          <el-table-column label="失效日期" align="center" min-width="140">
            <template slot="header">失效日期</template>
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 4">
                <el-form-item :prop="'tableData.' + scope.$index + '.expirationDate'" label="">
                  <el-date-picker
                    v-model.trim="scope.row.expirationDate"
                    clearable
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择">
                  </el-date-picker>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.expirationDate}}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="fapplyNum" label="申请数量" align="center" min-width="100"></el-table-column>
          <el-table-column align="center" min-width="100">
            <template slot="header"><span style="color: red;margin-right: 3px;">*</span>发货数量</template>
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 1">
                <el-form-item :prop="'tableData.' + scope.$index + '.fsendNum'" :rules="[{required: true, message: '请输入发货数量', trigger: 'blur'}, {validator: (rule, value, callback) => validateSendNum(rule, value, scope.row.fapplyNum, callback), trigger: 'blur'}]" label="">
                  <el-input v-model.trim.number="scope.row.fsendNum" :min="0" :max="scope.row.fapplyNum" type="number" placeholder="请输入"></el-input>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.fsendNum}}
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" min-width="100">
            <template slot="header">物料单价</template>
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type ===  4">
                <el-form-item
                  :prop="'tableData.' + scope.$index + '.funitPrice'"
                  :rules="[
                    {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '请输入两位小数', trigger: 'blur'}
                  ]"
                  label="">
                  <el-input
                    v-model.trim="scope.row.funitPrice" placeholder="请输入" clearable @change="handleUnitPriceChange(scope.row, scope.$index)"></el-input>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.funitPrice}}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="priceCount" label="金额" align="center" min-width="100"></el-table-column>

          <el-table-column label="发货备注" align="center" min-min-width="100">
            <template slot-scope="scope">
              <template v-if="deliveryInfo.type === 1">
                <el-form-item :prop="'tableData.' + scope.$index + '.fsendRemark'" :rules="sendRemarkRules" label="">
                  <el-input v-model.trim="scope.row.fsendRemark" placeholder="请输入"></el-input>
                </el-form-item>
              </template>
              <template v-else>
                {{scope.row.fsendRemark}}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="isbarcode & deliveryInfo.type === 1" class="record">
        <div class="title">条码记录</div>
        <el-row  :gutter="10">
          <div style="padding-left: 10px; margin: 10px 0;">
            条码使用人：{{[form.fsampleSale,form.fsampleCustomer].filter(v => v).join(';')}}
          </div>
          <el-col :span="6">
            <el-form-item label="最小条码号段" prop="fminBarcode">
              <el-input v-model.trim="form.fminBarcode"  max="9" style="width: 40%" clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="最大条码号段" prop="fmaxBarcode">
              <el-input v-model.trim="form.fmaxBarcode" max="9" style="width: 40%" clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div  class="record">
        <div class="title">发货记录</div>
        <el-row :gutter="10">
         <el-col v-if="deliveryInfo.materialType === 1" :span="6">
           <el-form-item label="邮寄方式" prop="freceiveway">
             <el-select v-model.trim="form.freceiveway" placeholder="placeholder">
               <el-option
                 :key="item.value"
                 :label="item.label"
                 :value="item.value"
                 v-for="item in receivewayList">
               </el-option>
             </el-select>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item :prop="form.freceiveway === '自提' ? '' : 'fexpressCode'" label="快递单号">
             <el-input v-model.trim="form.fexpressCode"  :disabled="deliveryInfo.type === 4" placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item label="经办人" prop="foperator">
             <el-input v-model.trim="form.foperator" :disabled="deliveryInfo.type === 4 || deliveryInfo.type === 2" clearable placeholder="请输入"></el-input>
           </el-form-item>
         </el-col>
         <el-col :span="6">
           <el-form-item label="发货时间" prop="fsendTime">
             <el-date-picker v-model.trim="form.fsendTime" :disabled="deliveryInfo.type === 2 || deliveryInfo.type === 4" clearable type="date" value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
           </el-form-item>
         </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="bottom">
      <el-button :loading="loading" type="" size="mini" @click="handleClose(false)">取消</el-button>
      <el-button v-if="deliveryInfo.type !== 4" :loading="loading" type="primary" size="mini" @click="handleConfirm">{{deliveryInfo.type === 2 ? '确认修改' : '确认发货'}}</el-button>
      <el-button v-else :loading="loading" type="primary" size="mini" @click="handleFix">确认修改</el-button>
    </div>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
export default {
  name: 'deliveryManagementSaveInfo',
  mixins: [mixins.tablePaginationCommonData],
  components: {},
  props: [],
  mounted () {
    this.getData()
  },
  watch: {
    tableData: {
      handler: (newValue = []) => {
        const length = newValue.length
        for (let i = 0; i < length; i++) {
          console.log(1111)
        }
      }
    }
  },
  computed: {
    totalPrice () {
      const tableData = this.form.tableData || []
      return tableData.reduce((pre, next) => {
        const result = util.add(pre, next.priceCount)
        if (!result) return pre
        return result
      }, 0)
    },
    isbarcode () {
      return this.form.tableData.some(v => v.materialsName.includes('条码'))
    },
    deliveryInfo () {
      return this.$store.getters.getValue('deliveryInfo')
    }
  },
  data () {
    const validateBarcode = (rule, value, callback) => {
      if (value.length !== 9) {
        callback(new Error('条码格式错误，请输入9位条码'))
      }
      // const Flag = value.charAt(2)
      // if (!['A', 'B', 'C', 'D'].includes(Flag)) {
      //   callback(new Error('条码格式错误，实验室标志位错误'))
      // }
      if (!(/^(0|\+?[1-9][0-9]*)$/.test(value))) {
        callback(new Error('条码格式错误，请输入数字'))
      }
      callback()
    }
    return {
      loading: false,
      form: {
        fcustomer: '',
        fapplicant: '',
        fsaleMan: '',
        fapplyTime: '',
        fcenterCode: '',
        fcenterName: '',
        finformEmail: '',
        fexpectReveiveTime: '',
        fsampleSale: '',
        fsampleCustomer: '',
        fsendAddr: '',
        fcontactPhone: '',
        fcontactPerson: '',
        fexpressCode: '',
        freceiveway: '',
        fbarcode: '',
        foperator: '',
        fsendTime: '',
        tableData: []
      },
      currentIndex: 0, // 当前行的申请数量，用于判断发放数量是否小于等于申请数量
      rules: {
        fminBarcode: [
          {required: true, message: '请输入最小条码号段', trigger: 'blur'},
          {validator: validateBarcode, trigger: 'blur'}
        ],
        fmaxBarcode: [
          {required: true, message: '请输入最大条码号段', trigger: 'blur'},
          {validator: validateBarcode, trigger: 'blur'}
        ],
        fexpressCode: [
          {required: true, message: '请输入快递单号', trigger: 'blur'}
        ],
        foperator: [
          {required: true, message: '请输入经办人', trigger: 'blur'}
        ],
        fsendTime: []
      },
      sendNumRules: [
        {required: true, message: '请输入发货数量', trigger: 'blur'}
      ],
      sendRemarkRules: [],
      receivewayList: [
        {
          value: '自提',
          label: '自提'
        },
        {
          value: '邮寄',
          label: '邮寄'
        }
      ]
    }
  },
  methods: {
    // 获取发货信息以及发货记录信息
    getData () {
      this.$ajax({
        method: 'get',
        url: '/materials/get_one_applyform',
        data: {
          fid: this.deliveryInfo.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form = {
            fcustomer: data.fcustomer,
            fapplyCode: data.fapplyCode,
            fapplicant: data.fapplicant,
            fsaleMan: data.fsaleMan,
            fapplyTime: data.fapplyTime,
            fcenterCode: data.fcenterCode,
            fcenterName: data.fcenterName,
            fsampleSale: data.fsampleSale,
            fsampleCustomer: data.fsampleCustomer,
            fminBarcode: data.fminBarcode,
            fmaxBarcode: data.fmaxBarcode,
            finformEmail: data.finformEmail,
            fexpectReveiveTime: data.fexpectReveiveTime,
            fsendAddr: data.fsendAddr,
            fcontactPhone: data.fcontactPhone,
            fcontactPerson: data.fcontactPerson,
            fexpressCode: data.fexpressCode,
            foperator: data.foperator,
            fsendTime: data.fsendTime,
            tableData: []
          }
          this.getDeliveryInfoTable()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取发货清单列表数据
    getDeliveryInfoTable () {
      this.$ajax({
        method: 'get',
        loadingDom: '.deliveryInfoTable',
        url: '/materials/get_materials_data',
        data: {
          fid: this.deliveryInfo.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let list = []
          let item = {}
          result.data.forEach(v => {
            item = {
              categoryName: v.categoryName,
              materialsName: v.materialsName,
              specName: v.specName,
              unit: v.unit,
              materialsNum: v.materialsNum,
              fapplyNum: v.fapplyNum,
              fsendNum: v.fsendNum || v.fapplyNum,
              materialsPackageId: v.materialsPackageId,
              materialsId: v.materialsId,
              fsendRemark: v.fremake,
              funitPrice: v.funitPrice,
              priceCount: '',
              ratificationIssue: v.fratificationIssue,
              expirationDate: v.fexpirationDate
            }
            item = this.getPriceCount(item)
            list.push(item)
          })
          this.getRealTableData(list)
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleUnitPriceChange (row, index) {
      const materialInfo = this.getPriceCount(row)
      this.$set(this.form.tableData, `form.tableData[${index}]`, materialInfo)
    },
    // 计算总额
    getPriceCount (materialInfo) {
      // 物料在申请单中是散料时：单价×申请数量
      // 物料在申请单中是物料包时：单价×数量×申请数量
      let priceCount = util.mul(materialInfo.fsendNum, materialInfo.funitPrice)
      materialInfo.categoryName = materialInfo.categoryName || ''
      if (materialInfo.materialsPackageId) {
        priceCount = util.mul(priceCount, materialInfo.materialsNum || 1)
      }
      if (!materialInfo.funitPrice && materialInfo.funitPrice !== 0) {
        priceCount = ''
      }
      materialInfo.priceCount = priceCount
      return {priceCount: priceCount, ...materialInfo}
    },
    // map 结构，key是物料包ID(materialsPackageId),value是物料包下的物料数据
    getRealTableData (list) {
      let map = new Map()
      let key = null
      list.forEach(v => {
        key = v.materialsPackageId || v.categoryName
        if (map.has(key)) {
          map.get(key).push(v)
        } else {
          map.set(key, [v])
        }
      })
      let tableData = []
      // 将map数据转换成array格式
      let data = [...map.values()]
      // 对已经分类的数据进行再次处理，获取类别合并行数据
      data.forEach((v, i) => {
        v.forEach((vv, ii) => {
          // total 表示需要合并的行数，只有每个物料包的第一个品类有数据，剩余的均为0
          let item = {...vv, total: ii === 0 ? v.length : 0}
          tableData.push(item)
        })
      })
      // 使用set方法设置表格数据，避免页面数据不渲染的问题
      this.$set(this.form, 'tableData', tableData)
    },
    // 校验发货数量是否小于申请数量以及是否大于0
    validateSendNum (rule, value, applyNum, callback) {
      if (value > applyNum) {
        callback(new Error('发货数量不能大于申请数量'))
      } else {
        callback()
      }
    },
    spanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 对类别列进行合并，行合并数为物料包里的品类数量
        return {
          rowspan: row.total,
          colspan: 1
        }
      } else if (columnIndex === 7) {
        // 对申请数量列进行行合并（只对物料包合并，散包不合并申请数量）
        // 通过物料包ID（materialsPackageId）进行判断是否为散包
        return {
          rowspan: row.materialsPackageId ? row.total : 1,
          colspan: 1
        }
      } else if (columnIndex === 8 || columnIndex === 11) {
        // 对发货数量和发货备注列进行行合并（只对物料包合并，散包不合并发货数量和发货备注）
        // 通过物料包ID（materialsPackageId）进行判断是否为散包
        return {
          rowspan: row.materialsPackageId ? row.total : 1,
          colspan: 1
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1
        }
      }
    },
    handleClose (confirm = false) {
      if (confirm) {
        this.$alert('数据保存成功，即将关闭当前页面', '保存成功', {
          confirmButtonText: '确定',
          callback: action => {
            this.$store.commit({
              type: 'old/setValue',
              category: 'deliveryInfo',
              deliveryInfo: {
                fid: null,
                type: null
              }
            })
            window.close()
          }
        })
      } else {
        this.$confirm('已填写的数据将会还原，是否放弃本次操作？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$store.commit({
            type: 'old/setValue',
            category: 'deliveryInfo',
            deliveryInfo: {
              fid: null,
              type: null
            }
          })
          window.close()
        }).catch(() => {
          console.log('取消')
        })
      }
    },
    handleFix () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            loadingDom: '.page',
            url: '/materials/modify_batch',
            data: {
              fapplyCode: this.form.fapplyCode,
              materialInfo: this.form.tableData.map(v => {
                return {
                  fid: v.materialsId,
                  fratificationIssue: v.ratificationIssue,
                  fexpirationDate: v.expirationDate,
                  funitPrice: v.funitPrice,
                  amountOfMoney: v.priceCount
                }
              })
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.handleClose(true)
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleConfirm () {
      console.log('发货')
      this.$refs.form.validate(valid => {
        console.log('发货111', valid)
        if (valid) {
          let data = {}
          if (this.deliveryInfo.type === 1 || this.deliveryInfo.type === 4) {
            let map = new Map()
            data = {
              type: this.deliveryInfo.type,
              fid: this.deliveryInfo.id,
              fexpressCode: this.form.fexpressCode,
              fminBarcode: this.form.fminBarcode,
              fmaxBarcode: this.form.fmaxBarcode,
              fbarcode: this.form.fminBarcode + '-' + this.form.fmaxBarcode,
              foperator: this.form.foperator,
              fsendTime: this.form.fsendTime,
              shippingListDTO: []
            }
            this.form.tableData.forEach(v => {
              if (map.has(v.materialsPackageId)) {
                map.get(v.materialsPackageId).rows.push(v)
              } else {
                map.set(v.materialsPackageId, {
                  rows: [v],
                  sendNum: 0,
                  sendRemark: '',
                  materialsPackageId: ''
                })
              }
              if (v.materialsPackageId) {
                // 属于物料包，需要处理发货数量
                if (v.total !== 0) {
                  map.get(v.materialsPackageId).materialsPackageId = v.materialsPackageId
                  map.get(v.materialsPackageId).sendNum = v.fsendNum
                  map.get(v.materialsPackageId).sendRemark = v.fsendRemark
                }
              }
            })
            let mapData = [...map.values()]
            mapData.forEach(v => {
              if (v.materialsPackageId) {
                data.shippingListDTO.push({
                  materialsPackageId: v.materialsPackageId,
                  fsendNum: v.sendNum,
                  fsendRemark: v.sendRemark
                })
              } else {
                v.rows.forEach(vv => {
                  data.shippingListDTO.push({
                    fsendNum: vv.fsendNum,
                    materialsPackageId: vv.materialsPackageId,
                    materialsId: vv.materialsId,
                    fsendRemark: vv.fsendRemark
                  })
                })
              }
            })
            data.shippingListDTO = JSON.stringify(data.shippingListDTO)
          } else {
            data = {
              type: 2,
              fid: this.deliveryInfo.id,
              fexpressCode: this.form.fexpressCode
            }
          }
          this.loading = true
          this.$ajax({
            loadingDom: '.page',
            url: '/materials/save_the_materials_applyform',
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.handleClose(true)
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .page{
    padding: 10px 20px;
    position: relative;
    height: calc(100vh - 40px);
    overflow: auto;
  }
  .title{
    font-size: 20px;
    /*font-weight: bold;*/
    line-height: 30px;
    height: 30px;
    border-bottom: 1px solid #DCDFE6;
    margin: 10px 0;
  }
  .detailed {
    min-height: calc(100vh - 184px - 92px - 40px - 20px - 30px - 20px - 30px - 20px);
    >>>.el-table {
      tbody tr {
        &:hover {
          td {
            background-color: transparent;
          }
        }
      }
      .el-form-item--mini.el-form-item, .el-form-item--mini.el-form-item{
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }
  .bottom{
    padding: 0 10px;
    bottom: 0;
    position: sticky;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    text-align: right;
    z-index: 101;
    border-top: 1px solid #DCDFE6;
  }
</style>
