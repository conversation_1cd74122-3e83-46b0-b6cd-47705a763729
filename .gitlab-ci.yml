image: docker:18.09.3

variables:
  DOCKER_TLS_CERTDIR: ''
  TAG: geneplus/lims-frontend:latest  # 镜像名称
stages:
- package
- deploy

cache:
  paths:
    - dist/*

job_package:
  stage: package
  image: "node:latest"
  only:
  - tags
  script:
  - npm config set registry https://registry.npm.taobao.org/
  - npm install
  - npm run build

job_site:
  stage: deploy
  only:
  - tags
  script:
  - docker build -t $TAG .
  - docker rm -f lims-frontend || true
  - docker run --network=host -d --name lims-frontend -v /geneplus/data/lims/myconf:/etc/nginx/conf.d -p 80:80 $TAG
  # - docker run -d --name cloud -v `pwd`/mynginx:/etc/nginx/conf.d -p 8080:80 $TAG
