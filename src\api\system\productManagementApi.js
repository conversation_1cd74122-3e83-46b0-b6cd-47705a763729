import { myAjax } from '@/util/ajax'

/**
 * 获取产品邮箱配置
 * @param data
 * @param options
 * @returns {*}
 */
export function getProductEmailConfigInfo (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/system/product/get_product_email',
    data: data,
    ...options
  })
}

/**
 * 获取产品 cancer 列表
 * @param data
 * @param options
 * @returns {*}
 */
export function getCancerList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/system/cancertype/list_cancertypes_by_cancer_class',
    data: data,
    ...options
  })
}

/**
 * 产品邮件-检测结果比对表-排除项
 * @param data
 * @param options
 * @returns {*}
 */
export function getCustomerList (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/system/customer/getCustomers',
    data: data,
    ...options
  })
}

/**
 * 保存产品邮件
 * @param data
 * @param options
 * @returns {*}
 */
export function saveProductEmailConfig (data, options = {}) {
  return myAjax({
    method: 'post',
    url: '/system/product/save_product_email',
    data: data,
    ...options
  })
}
