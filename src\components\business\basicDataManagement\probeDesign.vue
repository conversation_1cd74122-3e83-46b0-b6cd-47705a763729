<template>
  <div>
    <el-table
      :data="tableData"
      ref="table"
      border
      class="table"
      size="mini"
      height="calc(100vh - 320px)"
    >
      <el-table-column prop="probeName" min-width="100" label="个性化探针名称"></el-table-column>
      <el-table-column prop="coverage" min-width="100" label="实际Personal突变数"></el-table-column>
      <el-table-column prop="designFinishTime" min-width="100" label="设计完成时间"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import util from '../../../util/util'
export default {
  mounted () {
    this.getData()
    this.previewExcel()
  },
  data () {
    return {
      tableData: []
    }
  },
  methods: {
    // 探针探针订购列表
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/probe/get_probe_design_detail',
        method: 'get',
        data: {
          fid: this.$route.query.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          console.log(result)
          let data = result.data || []
          this.tableData = []
          data.forEach(v => {
            let item = {
              probeName: v.fprobeName,
              coverage: v.fpersonalSite,
              designFinishTime: v.fdesignTime
            }
            item.realData = item
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    previewExcel () {
    },
    handleToOrderDetail (sampleNum) {
      this.$router.push('/business/subpage/probeOrderDetail?sampleNum=' + sampleNum)
    }
  }
}
</script>

<style scoped></style>
