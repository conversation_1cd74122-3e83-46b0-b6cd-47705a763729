<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="500px"
      @open="handleOpen">
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        size="mini"
        label-width="120px">
        <el-form-item label="分类名称" prop="moduleType">
          <el-cascader
            :options="moduleCategoryLists"
            v-model="form.moduleType"
            v-formLoading="categoryLoading"
            :disabled="categoryLoading"
            :show-all-levels="false"
            :props="moduleCategoryProp"
            style="width: 100%;"
            collapse-tags
            filterable
            clearable></el-cascader>
        </el-form-item>
        <el-form-item label="模板编码" prop="moduleCode">
          <el-input v-model="form.moduleCode"></el-input>
        </el-form-item>
        <el-form-item label="模板名称" prop="moduleName">
          <el-input v-model="form.moduleName"></el-input>
        </el-form-item>
        <el-form-item label="模块类型" prop="fileType">
          <el-select v-model="form.fileType" placeholder="请选择模块类型">
            <template v-for="item in moduleTypeList">
              <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="模板文件" prop="name">
          <div style="display: flex;justify-content: space-between;align-items: start;">
            <el-upload
              ref="upload"
              :action="actionUrl"
              :auto-upload="false"
              :limit="1">
              <el-button size="mini" >上传</el-button>
              <div slot="tip" v-if="fileName" class="el-upload__tip">线上文件：{{fileName}}</div>
            </el-upload>
            <el-button v-if="fileName" size="mini" type="text" @click="handleDown">下载</el-button>
          </div>
        </el-form-item>
        <el-form-item  label="模板说明" prop="name">
          <el-input v-model="form.moduleDirections" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
export default {
  name: 'editModuleDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    title: String,
    moduleInfo: Object | null
  },
  data () {
    return {
      actionUrl: '',
      fileName: '',
      form: {
        id: '',
        moduleType: [], // 模板分类
        moduleCode: '',
        moduleName: '',
        fileType: '',
        moduleFile: '',
        moduleFileName: '', // 文件名称，只有在编辑时才会显示
        moduleDirections: ''
      },
      moduleTypeList: [
        {label: '文件', value: 'file'},
        {label: '图片', value: 'img'}
      ],
      moduleCategoryLists: [],
      moduleCategoryProp: {
        checkStrictly: true,
        value: 'fid',
        label: 'fmodelCategoryName'
      },
      categoryLoading: false,
      submitBtnLoading: false,
      rules: {
        moduleType: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        moduleCode: [
          { required: true, message: '请输入模块编码', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        moduleName: [
          { required: true, message: '请输入模块名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        fileType: [
          { required: true, message: '请选择模板文件', trigger: 'change' }
        ] // 模板分类
      }
    }
  },
  methods: {
    handleOpen () {
      let isNew = this.title === '新建模块'
      this.fileName = isNew ? '' : this.moduleInfo.fileName
      this.form = isNew
        ? {
          id: '',
          moduleCategory: [], // 模板分类
          moduleCode: '',
          moduleName: '',
          moduleType: '', // 模板分类
          fileType: '',
          moduleFileName: '',
          moduleDirections: ''
        }
        : {
          id: this.moduleInfo.id,
          moduleType: this.moduleInfo.moduleType ? this.moduleInfo.moduleType.split(',').map(item => { return +item }) : [], // 模板分类
          moduleCode: this.moduleInfo.moduleCode,
          moduleName: this.moduleInfo.moduleName,
          moduleFileName: '',
          fileType: this.moduleInfo.fileType,
          moduleFile: this.moduleInfo.moduleFile,
          moduleDirections: this.moduleInfo.moduleDirections
        }
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
        this.$refs.upload.clearFiles()
        this.getModuleCategoryLists()
      })
    },
    // 获取模板分类列表
    getModuleCategoryLists () {
      this.categoryLoading = true
      this.$ajax({
        url: '/system/model/get_modelCategory_tree',
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.moduleCategoryLists = res.data || []
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.categoryLoading = false
      })
    },
    handleDialogConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitBtnLoading = true
          let file = ''
          let fileName = ''
          if (this.$refs.upload && this.$refs.upload.uploadFiles.length > 0) {
            file = this.$refs.upload.uploadFiles[0].raw
            fileName = this.$refs.upload.uploadFiles[0].name
          }
          if (fileName) {
            if (this.form.fileType === 'img') {
              let i = fileName.lastIndexOf('.')
              if (i === -1) {
                this.$message.error('文件不是图片类型')
                return
              }
              let ext = fileName.substring(i + 1)
              if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'].indexOf(ext.toLowerCase()) === -1) {
                this.$message.error('文件不是图片类型')
                return
              }
            }
          }
          let templateCategory = this.form.moduleType.length > 0 ? this.form.moduleType[this.form.moduleType.length - 1] : ''
          this.$fileAjax({
            url: '/system/model/create_or_update_model',
            data: {
              fid: this.form.id,
              fileType: this.form.fileType,
              modelFile: file,
              modelType: templateCategory,
              modelCode: this.form.moduleCode,
              modelName: this.form.moduleName,
              description: this.form.moduleDirections
            }
          }).then(res => {
            if (res && res.code === this.SUCCESS_CODE) {
              this.$message.success('提交成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(res.message)
            }
          }).finally(() => {
            this.submitBtnLoading = false
          })
        }
      })
    },
    // 下载
    handleDown () {
      let form = document.createElement('form')
      form.action = constants.JS_CONTEXT + '/system/model/download_model'
      form.method = 'get'
      form.id = 'form'
      let submitData = {
        fileName: this.fileName
      }
      for (let key in submitData) {
        let input = document.createElement('input')
        input.type = 'hidden'
        input.name = key
        input.value = submitData[key]
        form.appendChild(input)
      }
      document.body.appendChild(form)
      form.submit()
      form.parentNode.removeChild(form)
    }
  }
}
</script>

<style scoped>

</style>
