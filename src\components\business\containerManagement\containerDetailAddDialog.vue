e<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="设置"
      width="1000px"
      @open="handleOpen">
      <div>
        <p class="mini-title">{{floor.floorNum}}层</p>
        <div>
          <el-select
            v-model="floor.sampleType"
            :disabled="!!currentFloorId"
            multiple size="mini" placeholder="请选择层样本类型">
            <template v-for="item in sampleTypeOptions">
              <el-option :key="item.value" :label="item.value" :value="item.value"></el-option>
            </template>
          </el-select>
          <el-select v-model="shelfNums" :disabled="!!currentShelfId" size="mini" placeholder="请选择架子数量">
            <template v-for="item in maxShelfNum">
              <el-option :key="item" :label="item" :value="item"></el-option>
            </template>
          </el-select>
          <el-button :disabled="!!currentShelfId" size="mini" type="primary" @click="handleSureShelf">确定</el-button>
        </div>
      </div>
      <div v-if="shelf.length > 0" style="margin-top: 20px;">
        <p class="mini-title">架子</p>
        <el-checkbox-group v-model="checkShelf">
          <el-checkbox
            :label="item.id"
            :key="item.id"
            v-for="item in shelf">
            {{item.shelfNum}}架（
            <span v-if="item.children && item.children.length > 0" style="color: #67C23A;">已设置</span>
            <span v-else style="color: #F56C6C;">未设置</span>
            ）
          </el-checkbox>
        </el-checkbox-group>
        <div>
          <el-button type="primary" size="mini" style="margin: 15px 0;" @click="handleSureChooseShelf">确认选中以上架</el-button>
          <div v-if="checkShelf.length > 0">
            <div>
              <p style="line-height: 2;">
                正在设置
                <span
                  :key="item"
                  v-for="(item, index) in checkShelf"
                  style="font-weight: 600; color: #000;font-size: 18px;">
                  {{item}}{{index === checkShelf.length - 1 ? '' : '，'}}
                </span>
                架
              </p>
              <el-form :inline="true" label-position="right" size="mini" label-width="120px">
                <el-form-item label="架样本类型">
                  <el-select v-model="shelfItem.sampleType" :disabled="!!currentShelfId" multiple>
                    <template v-for="item in floor.sampleType">
                      <el-option :key="item" :label="item" :value="item"></el-option>
                    </template>
                  </el-select>
                </el-form-item>
                <el-form-item label="架包含的盒数量">
                  <el-select v-model="boxNum">
                    <template v-for="item in maxBoxNum">
                      <el-option :key="item" :label="item" :value="item"></el-option>
                    </template>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="getBox">生成盒子</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div v-if="shelfItem.box.length > 0 && shelfItem.sampleType.length > 0">
              <p class="mini-title">盒</p>
              <div>
                <p>批量设置盒</p>
                <div  class="set-box-form">
                  <div>
                    <p class="label">输入盒子序号：</p>
                    <el-input v-model="batchBoxForm.numInput" style="width: 200px" size="mini"></el-input>
                  </div>
                  <div>
                    <p class="label">选择盒子样本类型：</p>
                    <el-select v-model="batchBoxForm.boxSampleType" multiple style="width: 200px" size="mini">
                      <template v-for="item in shelfItem.sampleType">
                        <el-option :key="item" :label="item" :value="item"></el-option>
                      </template>
                    </el-select>
                  </div>
                  <div>
                    <p class="label">行：</p>
                    <el-input v-model="batchBoxForm.row" style="width: 100px" size="mini"></el-input>
                  </div>
                  <div>
                    <p class="label">列：</p>
                    <el-input v-model="batchBoxForm.column" style="width: 100px" size="mini"></el-input>
                  </div>
                  <div style="align-self: flex-end;">
                    <el-button size="mini" type="primary" @click="handleBatchSetBox">确认</el-button>
                  </div>
                </div>
              </div>
              <el-table :data="shelfItem.box" style="width: 100%;" max-height="300px">
                <el-table-column prop="boxNum" label="盒序号" width="100"></el-table-column>
                <el-table-column label="样本类型" min-width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.sampleType" multiple>
                      <template v-for="item in shelfItem.sampleType">
                        <el-option :key="item" :label="item" :value="item"></el-option>
                      </template>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="行" width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.row"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="列" width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.column"></el-input>
                  </template>
                </el-table-column>
              </el-table>
              <div style="display: flex;justify-content: flex-end;align-items: center;padding: 10px 0;">
                <el-button type="primary" @click="handleSureSetBox">确认设置盒</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button size="mini" type="primary" @click="handleConfirm">确定</el-button>
        <el-button size="mini" @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
export default {
  name: 'containerDetailAddDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    type: {
      type: String,
      default: 'floor'
    },
    pdata: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  computed: {
    containerId () {
      return this.$store.getters.getValue('containerId')
    }
  },
  data () {
    return {
      maxShelfNum: 9,
      maxBoxNum: 20,
      shelfStartNum: 0, // 架子的初始数
      shelfNums: '',
      boxNum: '',
      sampleTypeOptions: [
        {value: '血液'},
        {value: '淋巴'},
        {value: '肾'},
        {value: '组织'}
      ],
      floor: {
        floorNum: '',
        sampleType: []
      },
      canEditShelf: false,
      shelf: [],
      shelfItem: {
        sampleType: [],
        box: []
      },
      batchBoxForm: { // 批量盒子form
        numInput: '',
        boxSampleType: [], // 当前设置盒子的样本类型
        row: '',
        column: ''
      },
      checkShelf: [],
      currentFloorId: '',
      currentShelfId: '',
      currentBoxId: ''
    }
  },
  methods: {
    handleOpen () {
      if (this.type === 'floor') {
        this.floor.floorNum = this.pdata.floorNum || ''
        this.floor.sampleType = this.pdata.sampleType || []
      } else if (this.type === 'shelf') {
        this.currentFloorId = this.pdata.floorId
        this.floor.floorNum = this.pdata.floorNum || ''
        this.floor.sampleType = this.pdata.sampleType || []
        this.shelfStartNum = this.pdata.shelfStartNum
      } else if (this.type === 'box') {
        this.currentFloorId = this.pdata.floorId
        this.floor.floorNum = this.pdata.floorNum || ''
        this.floor.sampleType = this.pdata.sampleType || []
        this.shelfNums = 1
        this.currentShelfId = this.pdata.shelfId
        let item = {
          sampleType: this.pdata.shelfSampleType,
          shelfNum: this.pdata.shelfNum,
          id: this.floor.floorNum + '_' + this.pdata.shelfNum,
          children: []
        }
        this.checkShelf.push(item.id)
        this.shelf.push(item)
        this.shelfItem.sampleType = item.sampleType
        this.shelfItem.box = []
      }
    },
    handleSureShelf () {
      if (this.shelfNums > 0 && this.floor.sampleType.length > 0) {
        this.shelf = []
        for (let i = 0; i < this.shelfNums; i++) {
          let item = {
            id: this.floor.floorNum + '_' + (i + 1 + this.shelfStartNum),
            shelfNum: i + 1 + this.shelfStartNum,
            sampleType: []
          }
          this.shelf.push(item)
        }
        this.resetData()
      }
    },
    handleSureChooseShelf () {
      if (this.checkShelf.length > 0) {
        this.canEditShelf = true
        this.shelf.forEach(item => {
          if (item.id === this.checkShelf[0]) {
            this.shelfItem.sampleType = item.sampleType
            if (item.children && item.children.length > 0) {
              this.shelfItem.box = []
              this.shelfItem.box.push(...item.children)
            }
          }
        })
      }
    },
    getBox () {
      if (this.boxNum && this.shelfItem.sampleType.length > 0) {
        this.shelfItem.box = []
        for (let i = 0; i < this.boxNum; i++) {
          let item = {
            id: i + 1,
            boxNum: i + 1,
            sampleType: [],
            row: '',
            column: ''
          }
          console.log(item)
          this.shelfItem.box.push(item)
        }
      }
    },
    // 批量设置盒子确认按钮
    handleBatchSetBox () {
      let rows = this.changInputToArr(this.batchBoxForm.numInput, this.shelfItem.box.length)
      console.log(rows)
      if (rows) {
        this.shelfItem.box.forEach(item => {
          if (rows.indexOf(item.boxNum) > -1) {
            item.sampleType = this.batchBoxForm.boxSampleType
            item.row = this.batchBoxForm.row
            item.column = this.batchBoxForm.column
          }
        })
      }
    },
    // 将输入值转换为数组
    changInputToArr (input, maxLength) {
      let regx = /[1-9]([\s+,，\\-]*[0-9])*$/
      if (regx.test(input)) {
        input = input.replace(/\s+/g, ',').replace(/，/, ',').replace(/(\s+-\s+)|(\s+-)|(-\s+)/, '-')
        // 转换字符串为数组,去除空项
        let f = input.split(',').filter(item => { return item })
        let floorNumsSet = new Set()
        let numCorrect = true
        for (let i = 0; i < f.length; i++) {
          if (f[i].indexOf('-') > -1) {
            let fArr = f[i].split('-').filter(item => { return item })
            if (fArr.length !== 2) {
              this.$message.error('输入格式不正确')
              numCorrect = false
              break
            } else {
              let correctNum = fArr.every(v => {
                let num = +v
                return !Number.isNaN(num) && num > 0 && num <= maxLength
              })
              if (correctNum) {
                let arr = fArr.map(v => { return +v })
                let max = Math.max(...arr)
                let min = Math.min(...arr)
                if (max <= maxLength && min > 0) {
                  let foolA = []
                  do {
                    foolA.push(min)
                    min++
                  } while (min <= max)
                  foolA.forEach(item => {
                    floorNumsSet.add(item)
                  })
                } else {
                  this.$message.error(`请确保输入的值在1-${maxLength}之间`)
                  numCorrect = false
                  break
                }
              } else {
                this.$message.error('输入格式不正确')
                numCorrect = false
                break
              }
            }
          } else {
            let num = +f[i]
            if (!Number.isNaN(num) && num > 0 && num <= maxLength) {
              floorNumsSet.add(num)
            } else {
              this.$message.error('输入格式不正确')
            }
          }
        }
        if (numCorrect) {
          return [...floorNumsSet]
        } else {
          return false
        }
      } else {
        this.$message.error('盒子序号格式不正确')
        return false
      }
    },
    // 确认生成盒子
    handleSureSetBox () {
      let allSet = this.shelfItem.box.every(item => {
        return item.column && item.row && item.sampleType && item.sampleType.length > 0
      })
      if (!allSet) {
        this.$message.error('未设置完整')
        return
      }
      this.shelf.forEach(item => {
        if (this.checkShelf.indexOf(item.id) > -1) {
          item.sampleType = this.shelfItem.sampleType
          item.children = []
          item.children.push(...this.shelfItem.box)
        }
      })
      this.resetData()
    },
    // 清空选择项
    resetData () {
      this.canEditShelf = false
      this.shelfItem = {
        sampleType: [],
        box: []
      }
      this.checkShelf = []
      this.boxNum = ''
      this.batchBoxForm = {
        numInput: '',
        boxSampleType: [],
        row: '',
        column: ''
      }
    },
    // 处理成为提交数据
    detailData () {
      let floor = {
        ffloorNumber: this.floor.floorNum,
        fsampleType: this.floor.sampleType.toString()
      }
      if (this.currentFloorId) {
        floor.fid = this.currentFloorId
      }
      let shelf = []
      this.shelf.forEach(item => {
        let v = {
          fshelfNumber: item.shelfNum,
          fsampleType: item.sampleType.toString()
        }
        if (this.currentShelfId) {
          v.fid = this.currentShelfId
        }
        let boxList = []
        let boxes = item.children || []
        boxes.forEach(vv => {
          let vvv = {
            fboxNumber: vv.boxNum,
            fsampleType: vv.sampleType.toString(),
            fxSize: vv.row,
            fySize: vv.column,
            fholeTotalCount: vv.row * vv.column
          }
          boxList.push(vvv)
        })
        v.boxList = boxList
        shelf.push(v)
      })
      floor.shelfList = shelf
      return floor
    },
    // 确认
    handleConfirm () {
      if (this.floor.sampleType.length === 0 || this.shelf.length === 0) {
        this.$message.error('层未设置')
        return
      }
      let hasSetShelf = this.shelf.every(item => {
        return item.sampleType && item.children && item.children.every(v => {
          return v.row && v.column && v.sampleType && v.sampleType.length > 0
        })
      })
      if (!hasSetShelf) {
        this.$message.error('架或者盒未设置完整')
        return
      }
      let floor = this.detailData()
      console.log(floor)
      this.$ajax({
        url: '/sample/container/new_save_container',
        data: {
          fid: this.containerId,
          floorList: floor
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('添加成功')
          this.$emit('dialogConfirmEvent')
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .mini-title{
    font-size: 15px;
    font-weight: 600;
    line-height: 2;
    color: #000;
  }
  .set-box-form{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
    & > div > p{
      color: #000;
      margin-bottom: 5px;
    }
  }
</style>
