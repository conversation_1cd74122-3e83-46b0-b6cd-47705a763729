// https://eslint.org/docs/user-guide/configuring

module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint'
  },
  globals: {
    h: true
  },
  env: {
    browser: true,
  },
  extends: [
    // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
    // consider switching to `plugin:vue/strongly-recommended` or `plugin:vue/recommended` for stricter rules.
    'plugin:vue/essential',
    // https://github.com/standard/standard/blob/master/docs/RULES-en.md
    'standard'
  ],
  // required to lint *.vue files
  plugins: [
    'vue'
  ],
  // add your custom rules here
  rules: {
    // allow async-await
    'generator-star-spacing': 'off',
    // allow debugger during development
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    // complexity: [
    //   'error',
    //   {
    //     max: 10
    //   }
    // ], // 代码圈复杂度 老项目不加
    'vue/order-in-components': [
      'error',
      {
        order: [
          'el',
          'name',
          'key',
          'mixins',
          'components',
          'props',
          'created',
          'mounted',
          'directives',
          'watch',
          'computed',
          'data',
          'methods',
          'beforeRouteEnter'
        ]
      }
    ], // vue options 顺序
    'vue/attribute-hyphenation': 'error', // 用连字符号连接
    // 'vue/attributes-order': [ // html标签顺序
    //   'error',
    //   {
    //     order: [
    //       'RENDER_MODIFIERS', // e.g. 'v-if', 'v-else-if', 'v-else', 'v-show', 'v-cloak'
    //       'LIST_RENDERING', // e.g. 'v-for item in items'
    //       'RENDER_MODIFIERS', // e.g. 'v-once', 'v-pre'
    //       'GLOBAL', // e.g. 'id'
    //       ['UNIQUE', 'SLOT'], //UNIQUE e.g. 'ref', 'key' | SLOT e.g. 'v-slot', 'slot'.
    //       'TWO_WAY_BINDING', //  e.g. 'v-model'
    //       'DEFINITION', //  e.g. 'is', 'v-is' ':'
    //       'OTHER_DIRECTIVES', // e.g. 'v-custom-directive'
    //       'OTHER_ATTR', // alias for [ATTR_DYNAMIC, ATTR_STATIC, ATTR_SHORTHAND_BOOL]:
    //                         // ATTR_DYNAMIC e.g. 'v-bind:prop="foo"', ':prop="foo"'
    //                         // ATTR_STATIC e.g. 'prop="foo"', 'custom-prop="foo"'
    //                         // ATTR_SHORTHAND_BOOL e.g. 'boolean-prop'
    //       'CONTENT', // e.g. 'v-text', 'v-html'
    //       'EVENTS' // e.g. '@click="functionCall"', 'v-on="event"'
    //     ],
    //     alphabetical: false
    //   }
    // ]
  }
}
