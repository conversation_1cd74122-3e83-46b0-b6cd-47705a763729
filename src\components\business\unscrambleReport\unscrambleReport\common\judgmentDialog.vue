<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      title="顺反式判断"
      width="30%">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px" label-suffix="：">
        <el-form-item label="顺反式判断" prop="judgment">
          <el-select v-model="form.judgment" placeholder="请选择" clearable style="width: 100%;">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in list">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'judgmentDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {},
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.ids = this.pdata.ids
      }
    }
  },
  computed: {},
  data () {
    return {
      visible: this.pvisible,
      ids: [],
      form: {
        judgment: ''
      },
      list: [
        {
          label: '-',
          value: '-'
        },
        {
          label: '与T790M呈顺式',
          value: '与T790M呈顺式'
        },
        {
          label: '与T790M呈反式',
          value: '与T790M呈反式'
        }
      ],
      rules: {
        judgment: [
          {required: true, message: '请选择顺反式判断', trigger: 'blur,change'}
        ]
      }
    }
  },
  methods: {
    handleClose () {
      this.$emit('handleJudgmentDialogCloseEven')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$ajax({
            url: '/read/unscramble/gene_cis-trans_drug',
            data: {
              object: {
                fids: JSON.stringify(this.pdata.ids),
                message: this.form.judgment
              }
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              this.$emit('handleJudgmentDialogConfirmEven')
              this.$refs.form.resetFields()
            } else {
              this.$message.error(result.message)
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
 >>>.el-dialog__body{
   padding: 10px 20px 0;
 }
</style>
