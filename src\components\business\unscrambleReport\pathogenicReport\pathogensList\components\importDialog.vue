<template>
  <div>
    <el-dialog :visible="visible" :close-on-click-modal="false" :close-on-press-escape="false"
               title="导入"
               width="700px"
               @close="handleCloseImport"
               @open="handleOpen">
      <el-upload ref="upload" :auto-upload="false"
                 :file-list="fileList" :action="uploadUrl"
                 :data="uploadParams"
                 :before-upload="handleBeforeUpload"
                 :on-change="handleChange"
                 :on-success="handleOnSuccess"
                 style="text-align: center;"
                 drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">
          <span>只支持excel文件</span>
          <!--<el-button size="mini" type="text" style="padding-left: 20px;" @click="handleClickDownload">导入模板下载</el-button>-->
        </div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleCloseImport">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirmImport">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import constants from '../../../../../../util/constants'
import mixins from '../../../../../../util/mixins'

export default {
  name: 'wesReportDetectionOfGeneVariationImportDialog',
  mixins: [mixins.dialogBaseInfo],
  props: ['importDialog'],
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    pathogenicType () {
      return this.$store.getters.getValue('pathogenicType')
    }
  },
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      fileList: [],
      uploadUrl: constants.JS_CONTEXT + '/read/pathogen/import_data',
      uploadParams: {
        analysisRsId: this.analysisRsId,
        version: this.pathogenicType
      }
    }
  },
  methods: {
    handleOpen () {
      this.loading = false
      console.log(this.analysisRsId)
      this.uploadParams = {
        analysisRsId: this.analysisRsId,
        version: this.pathogenicType
      }
    },
    handleBeforeUpload (file) {
      if (!(/\.(xlsx|xls)$/i.test(file.name))) {
        this.loading = false
        console.log(this.loading, '111111111')
        this.$message.error(file.name + '不为Excel文件，无法上传！')
        return false
      }
    },
    handleChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
    },
    handleOnSuccess (response) {
      this.loading = false
      if (response.code === this.SUCCESS_CODE) {
        if (response.message) {
          this.$message({
            showClose: true,
            duration: 0,
            type: 'error',
            message: response.message
          })
        } else {
          this.visible = false
          this.$message.success('操作成功')
        }
        this.$refs.upload.clearFiles()
        this.$emit('importDialogConfirmEvent')
      } else {
        this.loading = false
        this.fileList.splice(0, 1)
        this.$message({
          dangerouslyUseHTMLString: true,
          message: response.message,
          type: 'error'
        })
      }
    },
    handleCloseImport () {
      this.$refs.upload.clearFiles()
      this.visible = false
      this.loading = false
      this.$emit('importDialogCloseEvent')
    },
    handleConfirmImport () {
      this.loading = true
      this.$refs.upload.submit()
    }
  }
}
</script>

<style scoped>

</style>
