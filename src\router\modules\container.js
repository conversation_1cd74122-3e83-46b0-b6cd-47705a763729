// 容器管理相关路由
export default [
  {
    path: '/business/view/containerManagement',
    meta: {
      title: '容器管理'
    },
    component: () => import('@/components/business/containerManagement/overview.vue')
  },
  {
    path: '/business/sub/containerDetail',
    meta: {
      pageTitle: '容器详情'
    },
    component: () => import('@/components/business/containerManagement/containerDetail.vue')
  },
  {
    path: '/business/sub/createContainer',
    meta: {
      pageTitle: '创建容器'
    },
    component: () => import('@/components/business/containerManagement/createContainer.vue')
  },
  {
    path: '/business/sub/modifyContainer',
    meta: {
      pageTitle: '修改容器'
    },
    component: () => import('@/components/business/containerManagement/createContainer.vue')
  }
]
