<template>
  <div>
    <el-dialog :visible="visible"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               title="导入"
               width="700px"
               @open="handleOpen"
               @close="handleCloseImport">
      <el-upload ref="upload" :auto-upload="false"
                 :file-list="fileList" :action="uploadUrl"
                 :data="uploadParams"
                 :before-upload="handleBeforeUpload"
                 :on-change="handleChange"
                 :on-success="handleOnSuccess"
                 style="text-align: center;"
                 drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">
          <span>只支持excel文件</span>
          <!--<el-button size="mini" type="text" style="padding-left: 20px;" @click="handleClickDownload">导入模板下载</el-button>-->
        </div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleCloseImport">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini"  @click="handleConfirmImport">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import constants from '../../../../util/constants'
import mixins from '../../../../util/mixins'

export default {
  name: 'importDialog',
  mixins: [mixins.dialogBaseInfo],
  props: ['pvisible', 'pdata'],
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      fileList: [],
      uploadUrl: constants.JS_CONTEXT + '/read/bigAi/analysis_data_import',
      uploadParams: {
        analysisRsId: this.analysisRsId,
        currentStep: 0,
        type: ''
      }
    }
  },
  methods: {
    handleOpen () {
      this.loading = false
      this.uploadParams = {
        analysisRsId: this.analysisRsId,
        currentStep: 0,
        type: this.pdata
      }
    },
    handleBeforeUpload (file) {
      if (!(/\.(xlsx|xls)$/i.test(file.name))) {
        this.$message.error(file.name + '不为Excel文件，无法上传！')
        return false
      }
    },
    handleChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1)
      }
    },
    handleOnSuccess (response) {
      this.loading = false
      if (response.code === this.SUCCESS_CODE) {
        // if (response.message) {
        //   this.$message({
        //     showClose: true,
        //     duration: 0,
        //     type: 'error',
        //     message: response.message
        //   })
        // } else {
        this.$message.success('操作成功')
        // }
        this.$refs.upload.clearFiles()
        this.$emit('importDialogConfirmEvent')
        this.visible = false
      } else {
        // this.fileList.splice(0, 1)
        // this.$message({
        //   dangerouslyUseHTMLString: true,
        //   message: response.message,
        //   type: 'error'
        // })
        this.handleContinue(response.message)
      }
    },
    async handleContinue  (message) {
      let currentStep = 0
      if (message.indexOf('可能存在连锁突变') !== -1) {
        currentStep = 1
      } else if (message.indexOf('可能提示耐药') !== -1) {
        currentStep = 2
      } else if (message.indexOf('可能存在药物提示') !== -1) {
        currentStep = 3
      } else {
        this.uploadParams.currentStep = 0
        this.$message.error(message)
        this.loading = false
        this.$refs.upload.clearFiles()
        return
      }
      this.$confirm(message, '提示', {
        confirmButtonText: '继续解读',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.uploadParams.currentStep = currentStep
        this.handleConfirmImport()
      }).catch(() => {
        this.loading = false
        this.uploadParams.currentStep = 0
        this.fileList = []
        this.$refs.upload.clearFiles()
      })
      // type === 0 ? this.handleContrast(currentStep) : this.handleGetDorH(currentStep)
    },
    handleCloseImport () {
      this.$refs.upload.clearFiles()
      this.loading = false
      this.visible = false
      this.$emit('importDialogCloseEvent')
    },
    handleConfirmImport () {
      this.$refs.upload.uploadFiles[0].status = 'ready'
      console.log('导入开始', this.$refs.upload.uploadFiles[0])
      this.$refs.upload.submit()
      this.loading = true
    }
  }
}
</script>

<style scoped>

</style>
