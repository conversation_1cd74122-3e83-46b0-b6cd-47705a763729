/**
 * OpenSeadragon canvas Overlay plugin based on svg overlay plugin and fabric.js
 * Adapted for ES modules
 */
import OpenSeadragon from 'openseadragon'
import { fabric } from 'fabric'

/**
 * Adds fabric.js overlay capability to your OpenSeadragon Viewer
 */
export function addFabricjsOverlay (viewer, options = {}) {
  if (!viewer) {
    throw new Error('OpenSeadragon viewer is required')
  }

  const overlay = new Overlay(viewer, options.static, options.fabricCanvasOptions)

  if (options.scale) {
    overlay._scale = options.scale
  } else {
    overlay._scale = 1000 // default scale
  }

  return overlay
}

/**
 * Static counter for multiple overlays differentiation
 */
let counter = (() => {
  let i = 1
  return () => i++
})()

/**
 * Overlay class
 */
class Overlay {
  constructor (viewer, staticCanvas = false, fabricCanvasOptions = {}) {
    fabricCanvasOptions.enablePointerEvents = window.PointerEvent != null

    this._viewer = viewer
    this._containerWidth = 0
    this._containerHeight = 0

    // Create canvas container
    this._canvasdiv = document.createElement('div')
    this._canvasdiv.style.position = 'absolute'
    this._canvasdiv.style.left = '0px'
    this._canvasdiv.style.top = '0px'
    this._canvasdiv.style.width = '100%'
    this._canvasdiv.style.height = '100%'
    this._canvasdiv.style.pointerEvents = 'none'
    this._viewer.canvas.appendChild(this._canvasdiv)

    // Create canvas element
    this._canvas = document.createElement('canvas')
    this._id = 'osd-overlaycanvas-' + counter()
    this._canvas.setAttribute('id', this._id)
    this._canvas.style.pointerEvents = 'auto'
    this._canvasdiv.appendChild(this._canvas)

    this.resize()

    // Create fabric canvas
    if (staticCanvas) {
      this._fabricCanvas = new fabric.StaticCanvas(this._canvas, fabricCanvasOptions)
    } else {
      this._fabricCanvas = new fabric.Canvas(this._canvas, fabricCanvasOptions)
    }

    // Disable fabric selection by default
    this._fabricCanvas.selection = false

    // Setup event handlers
    this.setupEventHandlers()
    this.setupViewportHandlers()
  }

  setupEventHandlers () {
    // Prevent OSD mouse events on fabric objects
    this._fabricCanvas.on('mouse:down', (options) => {
      if (options.target) {
        options.e.preventDefaultAction = true
        options.e.preventDefault()
        options.e.stopPropagation()
      }
    })

    this._fabricCanvas.on('mouse:up', (options) => {
      if (options.target) {
        options.e.preventDefaultAction = true
        options.e.preventDefault()
        options.e.stopPropagation()
      }
    })

    // Handle mouse wheel events
    this._fabricCanvas.on('mouse:wheel', (options) => {
      options.e.preventDefault()
      options.e.stopPropagation()

      // Let OpenSeadragon handle the zoom
      const delta = options.e.deltaY
      const zoom = this._viewer.viewport.getZoom()
      const factor = delta > 0 ? 0.9 : 1.1

      this._viewer.viewport.zoomTo(zoom * factor)
    })
  }

  setupViewportHandlers () {
    // Update on viewport changes
    this._viewer.addHandler('update-viewport', () => {
      this.resize()
      this.resizeCanvas()
      this.render()
    })

    // Handle viewer open
    this._viewer.addHandler('open', () => {
      this.resize()
      this.resizeCanvas()
    })

    // Handle window resize
    window.addEventListener('resize', () => {
      this.resize()
      this.resizeCanvas()
    })
  }

  /**
   * Get the HTML canvas element
   */
  canvas () {
    return this._canvas
  }

  /**
   * Get the fabric.js canvas
   */
  fabricCanvas () {
    return this._fabricCanvas
  }

  /**
   * Clear all objects from the canvas
   */
  clear () {
    this._fabricCanvas.clear()
  }

  /**
   * Render the canvas
   */
  render () {
    this._fabricCanvas.renderAll()
  }

  /**
   * Resize the canvas container
   */
  resize () {
    if (this._containerWidth !== this._viewer.container.clientWidth) {
      this._containerWidth = this._viewer.container.clientWidth
      this._canvasdiv.setAttribute('width', this._containerWidth)
      this._canvas.setAttribute('width', this._containerWidth)
    }

    if (this._containerHeight !== this._viewer.container.clientHeight) {
      this._containerHeight = this._viewer.container.clientHeight
      this._canvasdiv.setAttribute('height', this._containerHeight)
      this._canvas.setAttribute('height', this._containerHeight)
    }
  }

  /**
   * Resize and transform the fabric canvas to match the viewport
   */
  resizeCanvas () {
    if (!this._viewer.viewport) return

    const origin = new OpenSeadragon.Point(0, 0)
    const viewportZoom = this._viewer.viewport.getZoom(true)

    // Set canvas dimensions
    this._fabricCanvas.setWidth(this._containerWidth)
    this._fabricCanvas.setHeight(this._containerHeight)

    // Calculate zoom
    const zoom = this._viewer.viewport._containerInnerSize.x * viewportZoom / this._scale
    this._fabricCanvas.setZoom(zoom)

    // Calculate pan
    const viewportWindowPoint = this._viewer.viewport.viewportToWindowCoordinates(origin)
    const x = Math.round(viewportWindowPoint.x)
    const y = Math.round(viewportWindowPoint.y)
    const canvasOffset = this._canvasdiv.getBoundingClientRect()

    const pageScroll = OpenSeadragon.getPageScroll()

    this._fabricCanvas.absolutePan(new fabric.Point(
      canvasOffset.left - x + pageScroll.x,
      canvasOffset.top - y + pageScroll.y
    ))
  }

  /**
   * Convert viewport coordinates to canvas coordinates
   */
  viewportToCanvas (point) {
    const canvasPoint = this._viewer.viewport.viewportToWindowCoordinates(point)
    const canvasOffset = this._canvasdiv.getBoundingClientRect()

    return {
      x: canvasPoint.x - canvasOffset.left,
      y: canvasPoint.y - canvasOffset.top
    }
  }

  /**
   * Convert canvas coordinates to viewport coordinates
   */
  canvasToViewport (point) {
    const canvasOffset = this._canvasdiv.getBoundingClientRect()
    const windowPoint = new OpenSeadragon.Point(
      point.x + canvasOffset.left,
      point.y + canvasOffset.top
    )

    return this._viewer.viewport.windowToViewportCoordinates(windowPoint)
  }

  /**
   * Destroy the overlay
   */
  destroy () {
    if (this._fabricCanvas) {
      this._fabricCanvas.dispose()
    }

    if (this._canvasdiv && this._canvasdiv.parentNode) {
      this._canvasdiv.parentNode.removeChild(this._canvasdiv)
    }
  }
}

export default Overlay
