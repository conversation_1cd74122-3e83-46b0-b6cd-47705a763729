<template>
  <div class="overview">
    <h3 class="head-title">线下检测信息查看</h3>
    <div class="container">
      <div class="container-left">
        <el-card class="card-left">
          <el-row class="container-title">
            <el-col>病理系统反馈结果展示</el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col :span="12">
              <div>病理结果生成人：<span class="font-color">{{form.pathologicalCreator}}</span></div>
            </el-col>
            <el-col :span="12">
              <div>病理结果生成时间：<span class="font-color">{{form.pathologicalCreateTime}}</span></div>
            </el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col :span="12">
              <div>病理结果审核人：<span class="font-color">{{form.pathologicalReviewer}}</span></div>
            </el-col>
            <el-col :span="12">
              <div>病理结果审核时间：<span class="font-color">{{form.pathologicalAuditTime}}</span></div>
            </el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col>
              <div>镜下所见：<span class="font-color">{{form.mirrorSee}}</span></div>
            </el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col>
              <div>大体所见：<span class="font-color">{{form.generalSee}}</span></div>
            </el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col :span="12">
              <div>TPS：<span class="font-color">{{form.pathologicalTPS}}</span></div>
            </el-col>
            <el-col :span="12">
              <div>CPS：<span class="font-color">{{form.pathologicalCPS}}</span></div>
            </el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col :span="12">
              <div>TC：<span class="font-color">{{form.pathologicalTC}}</span></div>
            </el-col>
            <el-col :span="12">
              <div>IC+：<span class="font-color">{{form.pathologicalICPlus}}</span></div>
            </el-col>
          </el-row>
          <el-row class="pathological-row">
            <el-col>
              <div>ICP：<span class="font-color">{{form.pathologicalICP}}</span></div>
            </el-col>
          </el-row>
        </el-card>
      </div>
      <div class="container-right">
        <el-card class="card-right">
          <template v-if="imgArray.length>0">
          <el-row style="margin-bottom: 20px">
            <el-button :disabled="currentImageIndex === 0" type="primary" icon="el-icon-caret-left" size="mini" @click="handlePrevious">上一张</el-button>
            <el-button :disabled="currentImageIndex === imgArray.length-1" type="primary" icon="el-icon-caret-right" size="mini" @click="handleNext">下一张</el-button>
            <el-button type="primary" icon="el-icon-refresh-right" size="mini" @click="handleRotate('right')">右转</el-button>
            <el-button type="primary" icon="el-icon-refresh-left" size="mini" @click="handleRotate('left')">左转</el-button>
          </el-row>
          <div class="preview-img">
            <vueCropper
              ref="cropper"
              :img="option.img"
              :mode="option.mode"
          ></vueCropper>
          <div class="img-name">{{ imgArray[currentImageIndex].name }}</div>
          </div>
          </template>
          <el-empty v-else :image-size="300"></el-empty>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script>
import {VueCropper} from 'vue-cropper'
export default {
  name: 'overview',
  components: {
    VueCropper
  },
  mounted () {
    this.sampleConfirmId = Number(this.$route.query.fsampleConfirmId)
    this.getData()
  },
  watch: {
    currentImageIndex (newValue) {
      this.option.img = this.imgArray[newValue].fileAbsolutePath
    }
  },
  data () {
    return {
      form: {
        pathologicalCreator: '', // 病理生成人
        pathologicalCreateTime: '', // 病理生成时间
        pathologicalReviewer: '', // 病理审核人
        pathologicalAuditTime: '',
        mirrorSee: '', // 镜下所见
        generalSee: '', // 大体所见
        pathologicalTPS: '', // TPS
        pathologicalCPS: '', // CPS
        pathologicalTC: '', // TC
        pathologicalICPlus: '', // IC+
        pathologicalICP: '', // ICP
        picName: '' // 图片名称
      },
      sampleConfirmId: '', // 病理id
      option: {
        img: '',
        mode: 'cover'
      },
      currentImageIndex: 0,
      imgArray: []
    }
  },
  methods: {
    // 获取线下数据
    getData () {
      this.$ajax({
        url: '/offline_detect/get_offline_detect_detail',
        loadingDom: '.container',
        data: {
          fsampleConfirmId: this.sampleConfirmId
        },
        method: 'post'
      }).then((res) => {
        console.log(res)
        if (res && res.code === this.SUCCESS_CODE) {
          this.form = {
            pathologicalCreator: res.data.fresultCreator,
            pathologicalCreateTime: res.data.fresultCreateTime,
            pathologicalReviewer: res.data.fresultAuditor,
            pathologicalAuditTime: res.data.fresultAuditTime,
            mirrorSee: res.data.funderMicroscope, // 镜下所见
            generalSee: res.data.fclinicalResult, // 大体所见
            pathologicalTPS: res.data.ftps,
            pathologicalCPS: res.data.fcps,
            pathologicalTC: res.data.ftc,
            pathologicalICPlus: res.data.fic, // IC+
            pathologicalICP: res.data.ficp,
            picName: res.data.fpic
          }
          this.imgArray = res.data.picList.filter((item) => {
            return item.fileAbsolutePath != null
          })
          console.log(this.imgArray)
          this.option.img = this.imgArray[0].fileAbsolutePath
          console.log('asdasd')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 上一张
    handlePrevious () {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 下一张
    handleNext () {
      if (this.currentImageIndex < this.imgArray.length - 1) {
        this.currentImageIndex++
      }
    },
    // 翻转
    handleRotate (value) {
      if (value === 'right') {
        this.$refs.cropper.rotateRight()
      } else {
        this.$refs.cropper.rotateLeft()
      }
    }
  }
}
</script>
<style scoped lang="scss" >
.overview{
  overflow: hidden;
}
.head-title{
  margin: 10px;
}
.container{
  display: flex;
  justify-content:space-around;
  margin: 20px 30px;
  .container-left{
    border-right:1px #b8d8fe solid;
    width: 50vw;
    height: 80vh;
    .card-left{
      width: 48vw;
      height: 80vh;
    }
    .container-title{
      margin-bottom: 50px;
      font-weight: bold;
    }
    .pathological-row{
      margin-bottom: 5vh;
      .font-color{
        color: #7b7b7b;
      }
    }
  }
  .container-right{
    .card-right{
      width: 40vw;
      height:80vh;
    }
    .preview-img{
      width:40vw;
      height:60vh;
      box-shadow: 0 0 10px #ccc;
    }
    .img-name{
      margin-top: 10px;
      text-align: center;
    }
  }
}
</style>
