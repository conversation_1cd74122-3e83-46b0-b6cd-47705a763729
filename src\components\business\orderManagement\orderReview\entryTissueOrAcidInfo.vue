<template>
  <div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">检测信息</p>
        </div>
      </div>
      <div class="content">
        <el-form :model="detectInfo" :rules="rules" ref="detectInfoForm" label-width="120px" size="mini">
          <el-form-item label="检测类型" prop="detectType">
            <span v-if="!editMode">{{detectInfo.detectType}}</span>
            <el-select v-model="detectInfo.detectType" v-else class="form-width" placeholder="请选择">
              <el-option
                      :key="k"
                      :label="v"
                      :value="k"
                      v-for="(v, k) in detectTypeLists">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="物种" prop="species">
            <span v-if="!editMode">{{showOtherText(detectInfo.species, detectInfo.speciesOther)}}</span>
            <template v-else>
              <el-radio-group v-model="detectInfo.species">
                <el-radio label="人"></el-radio>
                <el-radio label="动物"></el-radio>
                <el-radio label="植物"></el-radio>
                <el-radio label="微生物"></el-radio>
                <el-radio label="其他"></el-radio>
              </el-radio-group>
              <el-input
                      v-model.trim="detectInfo.speciesOther"
                      v-if="detectInfo.species === '其他'"
                      placeholder="请输入其他物种"
                      maxlength="20" class="form-width"></el-input>
            </template>
          </el-form-item>
          <el-form-item label="样本类型" prop="sampleType">
            <span v-if="!editMode">{{detectInfo.sampleType}}</span>
            <el-radio-group v-model="detectInfo.sampleType" v-else>
              <el-radio label="核酸样本"></el-radio>
              <el-radio label="组织样本"></el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="detectInfo.sampleType === '组织样本'">
            <el-form-item label="组织样本类型" prop="tissueSampleType">
              <span v-if="!editMode">{{showOtherText(detectInfo.tissueSampleType, detectInfo.tissueSampleTypeOther)}}</span>
              <template v-else>
                <el-radio-group v-model="detectInfo.tissueSampleType">
                  <el-radio label="组织"></el-radio>
                  <el-radio label="新鲜血液"></el-radio>
                  <el-radio label="冻存血液"></el-radio>
                  <el-radio label="FFPE"></el-radio>
                  <el-radio label="土壤"></el-radio>
                  <el-radio label="粪便"></el-radio>
                  <el-radio label="其他"></el-radio>
                </el-radio-group>
                <el-input
                        v-model.trim="detectInfo.tissueSampleTypeOther"
                        v-if="detectInfo.tissueSampleType === '其他'"
                        placeholder="请输入其他组织样本类型"
                        maxlength="20" class="form-width"></el-input>
              </template>
            </el-form-item>
            <el-form-item label="组织样本状态" prop="tissueSampleStatus">
              <span v-if="!editMode">{{detectInfo.tissueSampleStatus}}</span>
              <el-radio-group v-model="detectInfo.tissueSampleStatus" v-else>
                <el-radio label="液氮速冻组织"></el-radio>
                <el-radio label="已裂解"></el-radio>
                <el-radio label="福尔马林保存"></el-radio>
                <el-radio label="RNAlater保存"></el-radio>
                <el-radio label="TRlzol保存"></el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-if="detectInfo.sampleType === '核酸样本'">
            <el-form-item label="核酸样本类型" prop="">
              <span v-if="!editMode">{{detectInfo.acidSampleType}}</span>
              <el-radio-group v-model="detectInfo.acidSampleType" v-else>
                <el-radio label="基因组DNA"></el-radio>
                <el-radio label="cfDNA"></el-radio>
                <el-radio label="FFPE DNA"></el-radio>
                <el-radio label="FFPE RNA"></el-radio>
                <el-radio label="total RNA"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="核酸样本状态" prop="acidSampleStatus">
              <span v-if="!editMode">{{showOtherText(detectInfo.acidSampleStatus, detectInfo.acidSampleStatusOther)}}</span>
              <template v-else>
                <el-radio-group v-model="detectInfo.acidSampleStatus">
                  <el-radio label="溶于纯水"></el-radio>
                  <el-radio label="溶于TE buffer"></el-radio>
                  <el-radio label="溶于无RNase水（或DEPC水）"></el-radio>
                  <el-radio label="溶于乙醇"></el-radio>
                  <el-radio label="干粉"></el-radio>
                  <el-radio label="其他"></el-radio>
                </el-radio-group>
                <el-input
                        v-model.trim="detectInfo.acidSampleStatusOther"
                        v-if="detectInfo.acidSampleStatus === '其他'"
                        placeholder="请输入其他核酸样本状态"
                        maxlength="20" class="form-width"></el-input>
              </template>
            </el-form-item>
          </template>
          <el-form-item label="是否需要返样" prop="sampleType">
            <span v-if="!editMode">{{detectInfo.needBackSample}}</span>
            <el-radio-group v-model="detectInfo.needBackSample" v-else>
              <el-radio label="是"></el-radio>
              <el-radio label="否"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="返样备注" prop="backSampleNotes">
            <span v-if="!editMode">{{detectInfo.backSampleNotes}}</span>
            <el-input
                    v-model.trim="detectInfo.backSampleNotes"
                    v-else
                    placeholder="请输入返样备注"
                    maxlength="150" class="form-width"></el-input>
          </el-form-item>
          <el-form-item label="是否可以用完" prop="canRunOut">
            <span v-if="!editMode">{{showOtherText(detectInfo.canRunOut, detectInfo.canRunOutOther)}}</span>
            <template v-else>
              <el-radio-group v-model="detectInfo.canRunOut">
                <el-radio label="是"></el-radio>
                <el-radio label="否"></el-radio>
                <el-radio label="其他"></el-radio>
              </el-radio-group>
              <el-input
                      v-model.trim="detectInfo.canRunOutOther"
                      v-if="detectInfo.canRunOut === '其他'"
                      placeholder="请输入"
                      maxlength="50" class="form-width"></el-input>
            </template>
          </el-form-item>
          <el-form-item label="建库类型" prop="buildLibraryType">
            <span v-if="!editMode">{{detectInfo.buildLibraryType.join('/')}}</span>
            <el-cascader v-model="detectInfo.buildLibraryType" :options="buildLibraryType" :show-all-levels="false" v-else></el-cascader>
          </el-form-item>
          <el-form-item label="测序模式" prop="sequencingMode">
            <span v-if="!editMode">{{detectInfo.sequencingMode}}</span>
            <el-radio-group v-model="detectInfo.sequencingMode" v-else>
              <el-radio label="PE100"></el-radio>
              <el-radio label="PE150"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="仪器类型" prop="instrumentType">
            <span v-if="!editMode">{{detectInfo.instrumentType}}</span>
            <el-radio-group v-model="detectInfo.instrumentType" v-else>
              <el-radio label="DNBSEQ-T7RS"></el-radio>
              <el-radio label="Gene+Seq2000"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="拆分需求" prop="splitDemand">
            <span v-if="!editMode">{{detectInfo.splitDemand}}</span>
            <el-radio-group v-model="detectInfo.splitDemand" v-else>
              <el-radio label="拆分"></el-radio>
              <el-radio label="不拆分"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="交付方式" prop="deliveryMethod">
            <span v-if="!editMode">{{detectInfo.deliveryMethod}}</span>
            <el-radio-group v-model="detectInfo.deliveryMethod" v-else>
              <el-radio label="硬盘交付"></el-radio>
              <el-radio label="云交付"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="检测方式" label-width="auto" prop="isAutoDetect">
            <span v-if="!editMode">{{detectInfo.isAutoDetect}}</span>
            <el-radio-group v-model="detectInfo.isAutoDetect" v-else>
              <el-radio label="质控合格，样本可默认开始检测"></el-radio>
              <el-radio label="下单确认后才能开始检测"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="module">
      <div class="module-title-bar">
        <div>
          <p class="min-title">样本信息（{{totalPage}}）</p>
        </div>
      </div>
      <div class="content">
        <el-table
                :data="sampleInfoTable"
                height="300px"
                style="width: 100%">
          <el-table-column prop="geneplusNum" label="吉因加编号" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleName" label="样本名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="species" label="物种" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueSource" label="组织来源" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="concentration" label="浓度（ng/ul）" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="volume" label="体积（ul）" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dataNum" label="数据量（G）" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleTotal" label="样本量" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="notes" label="备注" min-width="180" show-overflow-tooltip></el-table-column>
          <!--<el-table-column label="操作" width="180" fixed="right" v-if="!readOnly">-->
            <!--<template slot-scope="scope">-->
              <!--<el-button type="text" @click="handleAdd(scope.row, scope.$index)">编辑</el-button>-->
              <!--<el-popconfirm title="确定删除这一条数据吗？" @confirm="handleDelete(scope.$index)">-->
                <!--<el-button type="text" slot="reference">删除</el-button>-->
              <!--</el-popconfirm>-->
            <!--</template>-->
          <!--</el-table-column>-->
        </el-table>
      </div>
    </div>
    <edit-sample-info-dialog
      :pvisible.sync="editSampleInfoDialogVisible"
      :pdata="editSampleInfoDialogData"
      @dialogConfirmEvent="handleEditSampleInfoDialogConfirm"/>
    <import-library-info-dialog
      :pvisible.sync="importLibraryInfoDialogVisible"
      :type="2"
      @dialogConfirmEvent="handleImportLibraryInfoDialogConfirm"/>
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../../util/mixins'
import editSampleInfoDialog from './entryTissueOrAcidInfoSampleInfoDialog'
import importLibraryInfoDialog from './importLibraryInfoDialog'
export default {
  name: 'entryTissueOrAcidInfo',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    editSampleInfoDialog,
    importLibraryInfoDialog
  },
  props: {
    readOnly: Boolean,
    orderId: String | Number,
    pageType: String | Number,
    editMode: Boolean, // 编辑模式
    onlineForm: Object // 线上的数据，之前前端填好了的
  },
  mounted () {
    this.getDetectType()
    if (this.orderId) {
      this.getData()
    }
  },
  watch: {
    onlineForm: {
      handler: function (newVal) {
        let keys = Object.keys(newVal)
        if (keys.length > 0) {
          this.detectInfo = {...newVal}
        }
      },
      deep: true
    }
  },
  data () {
    let self = this
    const specialVaild = function (rule, value, callback) {
      if (value === '其他' && !self.detectInfo.speciesOther) {
        callback(new Error('请输入其他物种'))
        return
      }
      callback()
    }
    const tissueSampleTypeVaild = function (rule, value, callback) {
      if (value === '其他' && !self.detectInfo.tissueSampleTypeOther) {
        callback(new Error('请输入其他组织样本类型'))
        return
      }
      callback()
    }
    const acidSampleStatusVaild = function (rule, value, callback) {
      if (value === '其他' && !self.detectInfo.acidSampleStatusOther) {
        callback(new Error('请输入其他核酸样本状态'))
        return
      }
      callback()
    }
    return {
      detectInfo: {
        detectType: '', // 检测类型
        species: '', // 物种
        speciesOther: '', // 其他物种
        sampleType: '', // 样本类型
        tissueSampleType: '', // 组织样本类型
        tissueSampleTypeOther: '', // 其他组织样本类型
        tissueSampleStatus: '', // 组织样本状态
        acidSampleType: '', // 核酸样本类型
        acidSampleStatus: '', // 核酸样本状态
        acidSampleStatusOther: '', // 其他核酸样本状态
        needBackSample: '', // 是否需要返样
        backSampleNotes: '', // 返样备注
        canRunOut: '', // 是否可以用完
        canRunOutOther: '', // 是否可以用完其他
        buildLibraryType: [], // 建库类型
        sequencingMode: '', // 测序模式
        instrumentType: '', // 仪器类型
        splitDemand: '', // 拆分需求
        deliveryMethod: '', // 交付方式
        isAutoDetect: '' // 是否允许根据质控结果自动开启检测
      },
      detectTypeLists: {},
      buildLibraryType: [
        {
          label: 'DNA文库',
          value: 'DNA文库',
          children: [
            {label: '全基因组文库-WGS', value: '全基因组文库-WGS'},
            {label: 'PCR-free文库', value: 'PCR-free文库'},
            {label: 'WES-安捷伦', value: 'WES-安捷伦'},
            {label: 'WES-IDT', value: 'WES-IDT'},
            {label: '目标区域捕获文库', value: '目标区域捕获文库'},
            {label: '宏基因组文库', value: '宏基因组文库'},
            {label: 'TAPS文库', value: 'TAPS文库'}
          ]
        },
        {
          label: 'RNA文库',
          value: 'RNA文库',
          children: [
            {label: 'mRNA普通文库', value: 'mRNA普通文库'},
            {label: 'mRNA链特异性文库', value: 'mRNA链特异性文库'},
            {label: 'LncRNA普通文库', value: 'LncRNA普通文库'},
            {label: 'LncRNA链特异性文库', value: 'LncRNA链特异性文库'}
          ]
        }
      ],
      sampleInfoTable: [],
      editSampleInfoDialogData: null, // 编辑数据，新建时传null,编辑时传正常数据
      editSampleInfoDialogVisible: false,
      importLibraryInfoDialogVisible: false,
      rules: {
        detectType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 检测类型
        species: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: specialVaild, trigger: 'change'}
        ], // 物种
        sampleType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 样本类型
        tissueSampleType: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: tissueSampleTypeVaild, trigger: 'change'}
        ], // 组织样本类型
        tissueSampleStatus: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 组织样本状态
        acidSampleType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 核酸样本类型
        acidSampleStatus: [
          {required: true, message: '请选择', trigger: 'change'},
          {validator: acidSampleStatusVaild, trigger: 'change'}
        ], // 核酸样本状态
        needBackSample: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 是否需要返样
        canRunOut: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 是否可以用完
        buildLibraryType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 建库类型
        sequencingMode: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 测序模式
        instrumentType: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 仪器类型
        splitDemand: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 拆分需求
        deliveryMethod: [
          {required: true, message: '请选择', trigger: 'change'}
        ], // 交付方式
        isAutoDetect: [
          {required: true, message: '请选择', trigger: 'change'}
        ] // 是否允许根据质控结果自动开启检测
      }
    }
  },
  methods: {
    // 展示其他文案，当一个文案为 '其他'时，展示另外一个字段，用于只读模式
    showOtherText (firstFiled, otherFiled, keyWord = '其他') {
      let text = firstFiled === keyWord ? otherFiled : firstFiled
      return (text !== 0 && !text) ? '-' : text
    },
    handleAdd (row, index) {
      console.log(2222, row, index)
      this.editSampleInfoDialogData = row ? {index, form: {...row}} : null
      this.editSampleInfoDialogVisible = true
    },
    // 获取检测类型
    getDetectType () {
      this.$ajax({
        url: '/order/get_detect_type_list',
        method: 'get',
        data: {
          type: 1
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          console.log(res.data)
          this.detectTypeLists = {}
          res.data.forEach(v => {
            this.detectTypeLists[v] = v
          })
        } else {
          this.$message.error('获取检测类型失败，错误：' + res.message)
        }
      })
    },
    // 获取文库样本表格数据
    getData () {
      let data = {
        orderId: this.orderId,
        type: 2
      }
      data.pageVO = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$ajax({
        url: '/order/get_lib_or_tissue_list',
        data: data,
        loadingDom: '.computer-table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.sampleInfoTable = []
          rows.forEach(v => {
            let item = {
              geneplusNum: v.geneCode,
              sampleName: v.name,
              tissueSource: v.tissueSource,
              species: v.species,
              dataNum: v.dataSize,
              concentration: v.concentration,
              volume: v.volume,
              sampleTotal: v.sampleSize,
              notes: v.note
            }
            this.sampleInfoTable.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleEditSampleInfoDialogConfirm (data) {
      if (!data.index && data.index !== 0) {
        delete data.index
        this.sampleInfoTable.push(data.form)
      } else {
        this.$set(this.sampleInfoTable, data.index, data.form)
        // this.libraryInfoTable[data.index] = data.form
      }
    },
    handleImportLibraryInfoDialogConfirm (res) {},
    handleDelete (index) {
      this.sampleInfoTable.splice(index, 1)
    },
    validForm () {
      return new Promise((resolve, reject) => {
        this.$refs.detectInfoForm.validate(valid => {
          let msg = '验证不通过'
          valid ? resolve() : reject(msg)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .title{
    margin-right: 30px;
    font-size: 20px;
    font-weight: 600;
  }
  .form-width{
    width: 200px;
  }
  .module{
    background: #fff;
    margin: 20px 0;
    .module-title-bar{
      @extend .operateBar;
      height: 50px;
      .min-title{
        @extend .title;
        font-size: 16px;
      }
    }
    .content{
      padding: 10px 20px;
    }
  }
</style>
