<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="30%"
      :before-close="handleClose"
      @open="handleOpen">
      <div>
        <el-form ref="form" :model="form" label-width="100px" :rules="rules" size="mini" @submit.native.prevent>
          <el-form-item label="报告模版" prop="reportCode">
            <el-select v-model="form.reportCode" clearable placeholder="请选择" style="width: 100%;">
              <el-option
                v-for="item in templateList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="mini">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading" size="mini">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../../../../util/mixins'

export default {
  name: 'wesReportDataFilterReportTemplateDialog',
  mixins: [mixins.dialogBaseInfo],
  props: ['pvisible'],
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    },
    pathogenicType () {
      return this.$store.getters.getValue('pathogenicType')
    }
  },
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      rules: {
        reportCode: [
          {required: true, message: '请选择模版', trigger: ['blur', 'change']}
        ]
      },
      form: {
        reportCode: ''
      },
      templateList: []
    }
  },
  methods: {
    handleOpen () {
      this.getTemplateList()
    },
    getTemplateList () {
      this.$ajax({
        method: 'get',
        url: '/read/pathogen/get_report_template_list',
        data: {
          analysisRsId: this.analysisRsId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.form.reportCode = result.data.defaultCode
          this.templateList = []
          result.data.list.forEach(v => {
            this.templateList.push({
              value: v.reportCode,
              label: v.reportName
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/read/pathogen/create_word_report',
            data: {
              analysisRsId: this.analysisRsId,
              reportCode: this.form.reportCode,
              version: this.pathogenicType
            },
            method: 'get'
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('生成报告成功')
              this.$emit('reportTemplateDialogConfirmEvent')
              this.$refs.form.resetFields()
              this.visible = false
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleClose () {
      this.$emit('reportTemplateDialogCloseEvent')
      this.$refs.form.resetFields()
      this.loading = false
      this.visible = false
      this.form.reportCode = ''
    }
  }
}
</script>

<style scoped>

</style>
