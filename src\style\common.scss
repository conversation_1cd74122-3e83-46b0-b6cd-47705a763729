@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

$color: #409EFF;
$success-color: #13CE66;
$fail-color: #f56c6c;
$page-bg: #f2f2f2;
$header-bg-color: #d6dbe1;
.el-popover {
  min-width: 120px;
}
.sample-popover-wrapper {
  min-width: 25px !important;
}

.operateBar{
  /*padding: 0 20px;*/
  height: 76px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #DCDFE6;
  margin-bottom: 20px;
}


label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

// 按钮内部图标和按钮文字的间距
.btn-inner-icon{
  margin-right: 8px;
}

// 弹窗输入框长度
.dialog-form{
  .form-width { // 短的form
    width: 250px;
  }
  .form-medium-width { // 短的form
    width: 350px;
  }
  .form-long-width{ // 长的form，比如日期选择器
    width: 510px;
  }
}

.search-form{
  .el-form-item--mini.el-form-item{
    margin-bottom: 12px;
  }
  .el-form-item {
    margin-bottom: 5px!important;
  }
  .form-width { // 短的form
    width: 150px;
  }
  .form-long-width{ // 长的form，比如日期选择器
    width: 350px;
  }
}
// 表格页面的基础样式
.page-wrap{
  background: #fff;
  padding: 12px;
  box-sizing: border-box;
  height: calc(100vh - 90px);
  // 更多查询内部的长度
  .params-search-form{
    .form-width { // 短的form
      width: 250px;
    }
    .form-long-width{ // 长的form，比如日期选择器
      width: 510px;
    }
  }
  // 操作按钮
  .operate-btns-group{
    //margin-bottom: -10px;
    padding-top: 2px;
    .el-button+.el-button{
      margin-left: 0;
    }
    & > button,
    & > .custom-btn,
    & > .el-dropdown{
      margin-right: 10px;
      margin-bottom: 12px;
    }
  }
}

// 详情页面固定表头
.fixed-title{
  .fixed-title-row{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .info{
      display: flex;
      margin-top: 10px;
      .info-item{
        margin-right: 30px;
        font-size: 15px;
        .info-title{
          color: #909399;
        }
      }
    }
  }
}

// 分模块化的标题
.module-title{
  font-size: 14px;
  color: $primary-color;
  line-height: 40px;
  padding-left: 10px;
  position: relative;
  &::before{
    content: '';
    display: block;
    position: absolute;
    height: 1em;
    width: 2px;
    border: 1px;
    background: $primary-color;
    left: 0;
    top: calc((100% - 1em) / 2);
  }
}

.tips {
  margin: 10px 0;
  font-size: 14px;
  font-weight: 300;
  color: #000;
}

.flex {
  display: flex;
}
