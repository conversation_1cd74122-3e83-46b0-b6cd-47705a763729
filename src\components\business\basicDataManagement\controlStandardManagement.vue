<template>
  <div>
    <div class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini" style="display: flex;justify-content: space-between" @keyup.enter.native="handleSearch">
        <el-form-item label="对照标准" prop="name">
          <el-input v-model.trim="form.name" clearable placeholder="请输入对照标准"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <!--按钮组-->
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('002015001', 'buttons')" type="primary" plain size="mini" @click="handleAddControl">新增</el-button>
      <el-button v-if="$setAuthority('002015002', 'buttons')" type="primary" plain size="mini" @click="handleEditControl">修改</el-button>
      <el-button v-if="$setAuthority('002015003', 'buttons')" type="primary" plain size="mini" @click="handleDelete">删除</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">搜索</el-button>
      <el-button type="primary" plain size="mini" @click="handleReset">重置</el-button>
    </div>
    <!--表格-->
    <el-table
      ref="table"
      :data="tableData"
      class="table"
      height="calc(100vh - 74px - 40px - 41px - 42px - 32px)"
      @select="handleSelect"
      @row-click="handleRowClick"
      @select-all="handleSelectAll">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="name" label="对照标准" min-width="140" show-overflow-tooltip></el-table-column>
      <el-table-column prop="notes" label="说明" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="creatorName" label="创建人" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="updateName" label="修改人" min-width="100" show-overflow-tooltip></el-table-column>
      <el-table-column prop="updateTime" label="修改时间" min-width="180" show-overflow-tooltip></el-table-column>
    </el-table>
    <!--分页-->
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      style="background: #ffffff;"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="refresh" /></button>
    </el-pagination>
    <control-standard-edit-dialog
      :pvisible.sync="controlStandardEditInfo.visible"
      :title="controlStandardEditInfo.title"
      :id="controlStandardEditInfo.id"
      @dialogConfirmEvent="getData"/>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import controlStandardEditDialog from './controlStandardEditDialog'
import util from '../../../util/util'
export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    controlStandardEditDialog
  },
  mounted () {
    this.getData()
  },
  data () {
    return {
      form: {
        name: ''
      },
      submitForm: {},
      tableData: [],
      selectedRows: new Map(),
      controlStandardEditInfo: {
        visible: false,
        title: '',
        id: null
      }
    }
  },
  methods: {
    getData () {
      this.$ajax({
        loadingDom: '.table',
        url: '/system/control_standard/page_control_standard',
        data: {
          params: {
            fname: this.submitForm.name
          },
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          let data = result.data
          this.totalPage = data.total
          let rows = data.rows || []
          this.tableData = []
          rows.forEach(row => {
            let item = {
              id: row.fid,
              name: row.fname,
              notes: row.fnote,
              creator: row.fcreator,
              creatorName: row.fcreatorName,
              createTime: row.fcreateTime,
              updateName: row.fupdatorName,
              updateTime: row.fupdateTime
            }
            util.setDefaultEmptyValueForObject(item)
            item.realData = util.deepCopy(row)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSearch () {
      this.submitForm = {
        name: this.form.name
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        name: ''
      }
      this.handleSearch()
    },
    // 新增对照标准
    handleAddControl () {
      this.controlStandardEditInfo.visible = true
      this.controlStandardEditInfo.title = '新增标准'
      this.controlStandardEditInfo.id = null
    },
    // 修改对照标准
    handleEditControl () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一行数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.controlStandardEditInfo.visible = true
      this.controlStandardEditInfo.title = '修改标准'
      this.controlStandardEditInfo.id = row.id
    },
    // 删除
    async handleDelete () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择要删除的数据')
        return
      }
      await this.$confirm(`已选择标准${this.selectedRows.size}项，是否确认删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const ids = [...this.selectedRows.values()].map(item => item.id)
      console.log(ids.join(','))
      this.$ajax({
        url: '/system/control_standard/delete_control_standard',
        data: {
          fids: ids.join(',')
        },
        method: 'get'
        // loadingDom: '.template-category'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击行
    handleRowClick (row) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelect(undefined, row)
    },
    // 选中行
    handleSelect (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}

</script>

<style scoped>
.btn-wrapper {
  display: flex;
}
</style>
