<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible" :close-on-click-modal="false"
      :before-close="handleClose" width="60%"
      @open="handleOpen">
      <div class="dialogContent">
        <div class="item">
          <div class="select">
            周：<el-date-picker v-model="week" :format="startWeek + '-' + endWeek" :picker-options="pickOptions" type="week" size="mini" @change="value => handleDateChange(value, 'week')"></el-date-picker>
          </div>
          <div ref="week" class="chart"></div>
        </div>
        <div class="item">
          <div class="select">
            月：<el-date-picker v-model="month" size="mini" type="month" value-format="yyyy-MM" placeholder="请选择" @change="value => handleDateChange(value, 'month')"></el-date-picker>
          </div>
          <div ref="month" class="chart"></div>
        </div>
        <div class="item">
          <div class="select">
            年：<el-date-picker v-model="year" size="mini" type="year" value-format="yyyy" placeholder="请选择" @change="value => handleDateChange(value, 'year')"></el-date-picker>
          </div>
          <div ref="year" class="chart"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import echarts from 'echarts'
import mixins from '../../../util/mixins'
export default {
  name: 'expressDeliveryManagementReportDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {},
  mounted () {},
  watch: {},
  computed: {},
  data () {
    return {
      title: '统计报表',
      startWeek: '',
      endWeek: '',
      week: '',
      month: '',
      year: '',
      weekChart: null,
      monthChart: null,
      yearChart: null,
      pickOptions: {
        firstDayOfWeek: 1
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.weekChart = null
        this.monthChart = null
        this.yearChart = null
        this.getCurrentDate()
      })
    },
    handleClose () {
      this.visible = false
    },
    getCurrentDate () {
      let newDate = this.$dayjs().$d
      this.week = newDate
      this.startWeek = this.$dayjs().startOf('week').add(1, 'day').format('YYYY-MM-DD') // 加一天是因为获取当前周的起始日为周日，比组件中起始日周一早一天，因此需要手动增加一天
      this.endWeek = this.$dayjs().endOf('week').add(1, 'day').format('YYYY-MM-DD') // 结算日同理
      this.month = this.$dayjs(newDate).format('YYYY-MM')
      this.year = this.$dayjs(newDate).format('YYYY')
      this.handleChartWeek()
      this.handleChartMonth()
      this.handleChartYear()
    },
    handleDateChange (value, type) {
      switch (type) {
        case 'week':
          this.startWeek = this.$dayjs(this.week).subtract(1, 'day').format('YYYY-MM-DD') // 获取数据的前一天，由于是周选择器的问题选择的日期为开始日的下一天，所以需要处理成前一天的时间
          this.endWeek = this.$dayjs(this.week).add(5, 'day').format('YYYY-MM-DD') // 获取数据的最后一天，加5(6 - 1)
          this.handleChartWeek()
          break
        case 'month':
          this.handleChartMonth()
          break
        case 'year':
          this.handleChartYear()
          break
      }
    },
    splitDate (date, type = 'YYYY-MM-DD') {
      return this.$dayjs(date).format(type)
    },
    handleChartWeek () {
      this.weekChart = null
      this.$ajax({
        url: '/package/get_statistical_report',
        method: 'get',
        data: {
          recordStartTime: this.startWeek,
          recordEndTime: this.endWeek
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let xValue = []
          let yValue = []
          this.weekChart = echarts.init(this.$refs.week)
          result.data.forEach(v => {
            xValue.push(v.logisticsCompany)
            yValue.push(v.num)
          })
          let option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '10%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                name: '物流公司',
                data: xValue,
                axisTick: {
                  alignWithLabel: true
                }
              }
            ],
            yAxis: [{
              type: 'value',
              name: '数量(个)'
            }],
            series: [
              {
                name: '物流公司分布',
                type: 'bar',
                barWidth: '60%',
                data: yValue,
                label: {
                  show: true,
                  color: '#ffffff'
                }
              }
            ]
          }
          this.weekChart.setOption(option)
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleChartMonth () {
      this.monthChart = null
      this.$ajax({
        url: '/package/get_statistical_report',
        method: 'get',
        data: {
          recordStartTime: this.$dayjs(this.month).startOf('month').format('YYYY-MM-DD'),
          recordEndTime: this.$dayjs(this.month).endOf('month').format('YYYY-MM-DD')
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let xValue = []
          let yValue = []
          this.monthChart = echarts.init(this.$refs.month)
          result.data.forEach(v => {
            xValue.push(v.logisticsCompany)
            yValue.push(v.num)
          })
          let option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '10%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                name: '物流公司',
                data: xValue,
                axisTick: {
                  alignWithLabel: true
                }
              }
            ],
            yAxis: [{
              type: 'value',
              name: '数量(个)'
            }],
            series: [
              {
                name: '物流公司分布',
                type: 'bar',
                barWidth: '60%',
                data: yValue,
                label: {
                  show: true,
                  color: '#ffffff'
                }
              }
            ]
          }
          this.monthChart.setOption(option)
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleChartYear () {
      this.yearChart = null
      this.$ajax({
        url: '/package/get_statistical_report',
        method: 'get',
        data: {
          recordStartTime: this.$dayjs(this.year).startOf('year').format('YYYY-MM-DD'),
          recordEndTime: this.$dayjs(this.year).endOf('year').format('YYYY-MM-DD')
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let xValue = []
          let yValue = []
          this.yearChart = echarts.init(this.$refs.year)
          result.data.forEach(v => {
            xValue.push(v.logisticsCompany)
            yValue.push(v.num)
          })
          let option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '10%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                name: '物流公司',
                data: xValue,
                axisTick: {
                  alignWithLabel: true
                }
              }
            ],
            yAxis: [{
              type: 'value',
              name: '数量(个)'
            }],
            series: [
              {
                name: '物流公司分布',
                type: 'bar',
                barWidth: '60%',
                data: yValue,
                label: {
                  show: true,
                  color: '#ffffff'
                }
              }
            ]
          }
          this.yearChart.setOption(option)
        } else {
          this.$message.error(result.message)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .dialogContent{
    height: 340px;
    overflow: auto;
  }
  .item{
    border: 1px solid #409EFF;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 10px;
  }
  .select{
    padding: 0 10px;
    height: 40px;
    line-height: 40px;
  }
  .chart{
    height: 280px;
  }
  >>>.el-dialog__body{
    padding: 10px 20px;
  }
</style>
