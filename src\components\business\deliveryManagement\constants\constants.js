export const areaOptions = [
  {
    label: '北京',
    value: 'PA0001'
  },
  {
    label: '深圳',
    value: 'PA0002'
  },
  {
    label: '苏州',
    value: 'PA0003'
  },
  {
    label: '上海',
    value: 'PA0004'
  }
]
/**
 * 个性化参数
 */
export const paramsOption = {
  'NG001': [{
    label: '新文件名',
    type: 'input',
    prop: 'fnewFileName',
    span: 24,
    show: () => true,
    placeholder: '请输入新文件名',
    rules: [
      {required: true, message: '请输入新文件名'}
    ]
  }],
  'NG004': [
    {
      label: '截取方式',
      type: 'radio',
      span: 24,
      prop: 'fcutFragmentType',
      placeholder: '',
      show: () => true,
      options: [
        {label: '片段截取', value: 1},
        {label: '过滤Q30和N截取', value: 2}
      ]
    },
    // 片段截取参数
    {
      label: 'read1截取段',
      type: 'input',
      span: 8,
      prop: 'fcutRead1',
      placeholder: '请输入read1截取范围',
      show: (form) => form.fcutFragmentType === 1,
      rules: [
        {required: true, message: '请输入read1截取范围'},
        // 只能输入数字类型，限制长度10字
        {pattern: /^[0-9]{1,10}$/, message: '只能输入数字类型，限制长度10字'}
      ]
    },
    {
      label: 'read2截取段',
      type: 'input',
      span: 8,
      prop: 'fcutRead2',
      placeholder: '请输入read2截取范围',
      show: (form) => form.fcutFragmentType === 1,
      rules: [
        {required: true, message: '请输入read2截取范围'},
        // 只能输入数字类型，限制长度10字
        {pattern: /^[0-9]{1,10}$/, message: '只能输入数字类型，限制长度10字'}
      ]
    },
    // 过滤Q30和N截取参数
    {
      label: 'Q30阈值(%)',
      type: 'input',
      span: 8,
      prop: 'q30',
      placeholder: '请输入Q30阈值',
      show: (form) => form.fcutFragmentType === 2,
      rules: [
        {required: true, message: '请输入Q30阈值'},
        // 只能输入数字类型，限制长度10字
        {pattern: /^[0-9]{1,10}$/, message: '只能输入数字类型，限制长度10字'}
      ]
    },
    {
      label: 'N碱基阈值(%)',
      type: 'input',
      span: 8,
      prop: 'fnucleateContent',
      placeholder: '请输入N碱基阈值',
      show: (form) => form.fcutFragmentType === 2,
      rules: [
        {required: true, message: '请输入N碱基阈值'},
        // 只能输入数字类型，限制长度10字
        {pattern: /^[0-9]{1,10}$/, message: '只能输入数字类型，限制长度10字'}
      ]
    },
    {
      label: 'read数量下限',
      type: 'input',
      span: 8,
      prop: 'freadCountMin',
      placeholder: '请输入read下限',
      show: (form) => form.fcutFragmentType === 2,
      rules: [
        {required: true, message: '请输入read下限'},
        // 只能输入数字类型，限制长度10字
        {pattern: /^[0-9]{1,10}$/, message: '只能输入数字类型，限制长度10字'}
      ]
    }
  ],
  'NG005': [
    {
      label: '邮件主题',
      type: 'input',
      span: 24,
      prop: 'fmailSubject',
      show: () => true,
      placeholder: '请输入邮件主题',
      rules: [
        {required: true, message: '请输入邮件主题'},
        // 限制长度100字
        {max: 100, message: '限制长度100字'}
      ]
    }
  ],
  'NG006': [
    // 调样归属片区
    {
      label: '调入片区',
      type: 'select',
      span: 24,
      prop: 'fadjustmentArea',
      show: () => true,
      placeholder: '请选择调入片区',
      options: areaOptions,
      rules: [
        {required: true, message: '请选择调入片区'}
      ]
    }
  ]
}

export const personModule = [
  // {label: '嘉检-改名重释放', value: 'NG001'},
  {label: '子文库合并', value: 'NG002'},
  {label: 'cleandata', value: 'NG003'},
  // {label: '截取模块', value: 'NG004'},
  // {label: '邮件主题', value: 'NG005'},
  {label: '调样模块', value: 'NG006'},
  {label: '重上机', value: 'NG007'},
  {label: '原始数据交付', value: 'NG008'}
]

export const personTagModule = [
  // {label: '嘉检-改名重释放', value: 'NG001'},
  {label: '子文库合并', value: 'NG002'},
  {label: 'cleandata', value: 'NG003'},
  // {label: '截取模块', value: 'NG004'},
  // {label: '邮件主题', value: 'NG005'},
  {label: '调入片区', value: 'NG006'},
  {label: '重上机', value: 'NG007'},
  {label: '原始数据交付', value: 'NG008'}
]
