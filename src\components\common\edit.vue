<template>
  <div
    ref="editor"
    class="editor-wrap"
    contenteditable="true"
    data-placeholder="请输入......"
    v-html="editorHtml"
    @focus="isLocked = true"
    @blur="isLocked = false"
    @click="saveRange"
    @input="changeText"
    @paste="changeText"
  >
  </div>
</template>

<script>
export default {
  name: 'editor',
  props: ['value'],
  mounted () {
    console.log('组件加载中...', this.value, this.editorHtml)
    this.editorHtml = this.value
    this.isLocked = false
    // this.range = ''
  },
  watch: {
    'value': {
      handler (val) {
        if (!this.isLocked) { // 解决光标错位的问题
          this.editorHtml = val
        } else {
        }
      },
      deep: true
    }
  },
  data () {
    return {
      editorHtml: this.value,
      isLocked: false,
      range: '',
      ossConfig: {
        accessKeyId: '',
        accessKeySecret: '',
        bucket: '',
        region: '',
        stsToken: ''
      },
      fileInfo: { // 正在上传文件的信息
        realName: '', // 真实名称
        uidName: '', // 唯一名
        raw: null, // 文件内容
        size: '', // 文件大小
        checkpoint: null
      }
    }
  },
  methods: {
    changeText () {
      this.$emit('input', this.$el.innerHTML)
      this.saveRange()
    },
    // 记录光标位置，本来是放在@blur里的,即失焦时记录光标位置；后面发现IOS下blur不能记录到，所以现在的做法是点击或者输入的时候记录
    saveRange () {
      const selection = window.getSelection ? window.getSelection() : document.getSelection()
      this.range = selection.getRangeAt(0)
    },
    // 设置焦点位置
    setRange () {
      const selection = window.getSelection ? window.getSelection() : document.getSelection()
      selection.removeAllRanges()
      if (this.range) {
        selection.addRange(this.range)
      } else {
        this.$refs.editor.focus()
      }
    },
    handlePaste (value) {
      let files = value.clipboardData.items || []
      console.log(this.$refs.showPartTestPic)
      const fileList = []
      for (const file of files) {
        if (file.type.includes('image')) {
          let imgFile = file.getAsFile()
          fileList.push(imgFile)
        }
      }
    },
    insert (html) {
      this.setRange()
      document.execCommand('insertHTML', false, html)
    }
  }
}
</script>
<style scoped lang="scss">
.editor-wrap {
  padding: 5px;
  border: 1px solid #DCDFE6;
  min-height: 200px;
  overflow: auto;
  border-radius: 4px;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
}

.editor-wrap:empty:before {
  color: #DCDFE6;
  content: attr(data-placeholder);
}
.editor-wrap:hover {
  border: 1px solid #C0C4CC;
}
:focus {
  outline: 1px solid $color;
}
</style>
