<template>
  <div style="height: 100%;">
    <div ref="search" class="search-form">
      <el-form ref="form" :model="form" :inline="true" label-width="80px" size="mini" @keyup.enter.native="handleSearch">
        <!--<scroll-pane :scroll-height="96">-->
        <!--</scroll-pane>-->
        <el-form-item label="申请单号">
          <el-input v-model.trim="form.fapplyCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="快递单号">
          <el-input v-model.trim="form.fexpressCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="客户">
          <el-input v-model="form.fcustomer" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="申请人">
          <el-input v-model.trim="form.fapplicant" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model.trim="form.fstatus" clearable placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in statusList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属">
          <el-select v-model.trim="form.fbelongs" collapse-tags clearable multiple placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in belongList">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker v-model.trim="form.time" clearable type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="发货时间">
          <el-date-picker v-model.trim="form.sendTime" clearable type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('013005001', 'buttons')" type="primary" size="mini" @click="handleBulkShipping" >批量发货</el-button>
        <el-button v-if="$setAuthority('013005002', 'buttons')" type="primary" size="mini" @click="handleConfirmDelivery" >确认发货</el-button>
        <el-button v-if="$setAuthority('013005003', 'buttons')" type="primary" size="mini" @click="handleEditDeliveryInfo">修改快递</el-button>
        <el-button v-if="$setAuthority('013005005', 'buttons')" type="primary" size="mini" @click="handleFixBatch">修改批次</el-button>
        <el-button v-if="$setAuthority('013005004', 'buttons')" type="primary" size="mini" @click="handlePublicityExport">导出</el-button>
        <el-button type="primary" size="mini" @click="handleSearch">查询</el-button>
        <el-button type="primary" size="mini" @click="handleReset">重置</el-button>
      </div>
      <div class="table">
        <el-table
          ref="table"
          :data="tableData"
          height="calc(100vh - 74px - 40px - 83px - 42px - 32px)"
          size="mini"
          class="deliveryTable"
          style="width: 100%"
          @select="handleSelectTable"
          @row-click="handleRowClick"
          @select-all="handleSelectAll"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="fapplyCode" label="申请单号" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="handleViewDetail(scope.row)">{{scope.row.fapplyCode}}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="fexpressCode" label="快递编号" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fcustomer" label="客户" width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.fcustomer" type="name"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="fapplicant" label="申请人" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fapplyTime" label="申请日期" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fcontactPerson" label="联系人" width="80" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.fcontactPerson" type="name"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="fstatusText" label="发货状态" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fsendRemark" label="发货备注" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fbelongText" label="所属" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fsendTime" label="发货时间" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="foperator" label="经办人" width="80" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <bulk-shipping-upload-dialog
      :pvisible.sync="bulkShippingUploadDialogVisible"
      @bulkShippingUploadDialogConfirmEvent="handleBulkShippingUploadDialogConfirm"
    ></bulk-shipping-upload-dialog>
    <export-dialog
      :pvisible.sync="exportDialogVisible" :select-number="selectedRows.size" :all-number="totalPage"
      @dialogConfirmEvent="handleExport"
    ></export-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import bulkShippingUploadDialog from './deliveryManagementBulkShippingUploadDialog'
export default {
  name: 'deliveryManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    bulkShippingUploadDialog
  },
  props: [],
  mounted () {
    this.$nextTick(() => {
      this.searchHeight = this.$refs.search.offsetHeight
      this.handleSearch()
      window.addEventListener('resize', this.$_resizeHandler)
      this.$once('hook:beforeDestroy', () => {
        window.removeEventListener('resize', this.$_resizeHandler)
      })
    })
  },
  data () {
    return {
      searchHeight: '',
      form: {
        fapplyCode: '',
        fexpressCode: '',
        fcustomer: '',
        fapplicant: '',
        fstatus: '',
        fbelongs: [],
        time: [],
        sendTime: []
      },
      submitForm: {
        fapplyCode: '',
        fexpressCode: '',
        fcustomer: '',
        fapplicant: '',
        fstatus: '',
        fbelongs: [],
        startTime: '',
        endTime: '',
        sendStartTime: '',
        sendEndTime: ''
      },
      selectedRows: new Map(),
      exportDialogVisible: false,
      statusList: [
        // {
        //   value: 0,
        //   label: '申请单通过审核'
        // },
        {
          value: 1,
          label: '待发货'
        },
        {
          value: 2,
          label: '已发货'
        },
        {
          value: 3,
          label: '已签收'
        }
      ],
      belongList: [
        {
          value: 1,
          label: '临床(物料)'
        },
        {
          value: 2,
          label: '药厂'
        },
        {
          value: 3,
          label: '渠道'
        },
        {
          value: 4,
          label: '临床(宣传品)'
        },
        {
          value: 5,
          label: '病原(物料)'
        }
      ],
      bulkShippingUploadDialogVisible: false,
      bulkShippingUploadDialogData: {},
      downloadLoading: false
    }
  },
  methods: {
    $_resizeHandler () {
      this.searchHeight = this.$refs.search.offsetHeight
    },
    getData () {
      this.$ajax({
        loadingDom: '.deliveryTable',
        url: '/materials/get_materials_list',
        data: {
          page: {
            size: this.pageSize,
            current: this.currentPage
          },
          mApplyForm: {
            ...this.submitForm
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.totalPage = data.total || 0
          this.tableData = []
          this.selectedRows.clear()
          let item = {}
          data.rows.forEach(v => {
            item = {
              fid: v.fid,
              fapplyCode: v.fapplyCode,
              fexpressCode: v.fexpressCode,
              fcustomer: v.fcustomer,
              fapplicant: v.fapplicant,
              fapplyTime: v.fapplyTime,
              fcontactPerson: v.fcontactPerson,
              fstatus: v.fstatus,
              fstatusText: this.getStatusText(v.fstatus, 'statusList'),
              fbelong: v.fbelong,
              fbelongText: this.getStatusText(v.fbelong, 'belongList'),
              fsendRemark: v.fsendRemark,
              fsendTime: v.fsendTime,
              foperator: v.foperator,
              ftype: data.ftype
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleSearch () {
      let startTime = ''
      let endTime = ''
      let form = JSON.parse(JSON.stringify(this.form))
      if (form.time && form.time.length > 0) {
        startTime = form.time[0]
        endTime = form.time[1]
      }
      let sendStartTime = ''
      let sendEndTime = ''
      if (form.sendTime && form.sendTime.length > 0) {
        sendStartTime = form.sendTime[0]
        sendEndTime = form.sendTime[1]
      }
      this.submitForm = {
        fapplyCode: form.fapplyCode.trim(),
        fexpressCode: form.fexpressCode,
        fcustomer: form.fcustomer.trim(),
        fapplicant: form.fapplicant.trim(),
        fstatus: form.fstatus,
        fbelongs: form.fbelongs,
        startTime: startTime,
        endTime: endTime,
        fsendStartTime: sendStartTime,
        fsendEndTime: sendEndTime
      }
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        fapplyCode: '',
        fexpressCode: '',
        fcustomer: '',
        fapplicant: '',
        fstatus: '',
        fbelongs: [],
        time: [],
        sendTime: []
      }
      this.handleSearch()
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.fid))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.fid) ? this.selectedRows.delete(row.fid) : this.selectedRows.set(row.fid, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.fid, row)
      })
    },
    handleBulkShipping () {
      this.bulkShippingUploadDialogVisible = true
    },
    handleConfirmDelivery () {
      if (this.selectedRows.size === 1) {
        let row = [...this.selectedRows.values()][0]
        if (row.fstatus !== 1) {
          this.$message.error('请选择待发货申请')
          return false
        }
        this.$store.commit({
          type: 'old/setValue',
          category: 'deliveryInfo',
          deliveryInfo: {
            id: row.fid,
            type: 1, // 1表示确认发货 2表示修改快递 3表示查看发货信息
            materialType: row.ftype // 0物料； 1宣传品
          }
        })
        util.openNewPage('/business/subpage/deliveryManagementSaveInfo')
      } else {
        this.$message.error('请选择一条数据')
      }
    },
    handleViewDetail (row) {
      this.$store.commit({
        type: 'old/setValue',
        category: 'deliveryInfo',
        deliveryInfo: {
          id: row.fid,
          type: 3 // 1表示确认发货 2表示修改快递 3表示查看发货信息
        }
      })
      util.openNewPage('/business/subpage/deliveryManagementDetail')
    },
    /**
     * 修改批次 2.申请单已发货。否则提示请选择已发货的申请单。
     */
    handleFixBatch () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.fstatus !== 2) {
        this.$message.error('请选择已发货的申请单')
        return
      }
      this.$store.commit({
        type: 'old/setValue',
        category: 'deliveryInfo',
        deliveryInfo: {
          id: [...this.selectedRows.values()][0].fid,
          type: 4 // 1表示确认发货 2表示修改快递 3表示查看发货信息, 4修改批次
        }
      })
      util.openNewPage('/business/subpage/deliveryManagementSaveInfo')
    },
    handleEditDeliveryInfo () {
      if (this.selectedRows.size === 1) {
        this.$store.commit({
          type: 'old/setValue',
          category: 'deliveryInfo',
          deliveryInfo: {
            id: [...this.selectedRows.values()][0].fid,
            type: 2 // 1表示确认发货 2表示修改快递 3表示查看发货信息
          }
        })
        util.openNewPage('/business/subpage/deliveryManagementSaveInfo')
      } else {
        this.$message.error('请选择一条数据')
      }
    },
    getStatusText (value, listName) {
      let text = ''
      let obj = this[listName].find(v => v.value === value)
      if (obj) {
        text = obj.label
      }
      return text
    },
    handleBulkShippingUploadDialogConfirm () {
      this.bulkShippingUploadDialogVisible = false
      this.getData()
    },
    handlePublicityExport () {
      if ([...this.selectedRows.values()].some(v => v.fstatusText === '待发货')) {
        this.$message.error('无法导出待发货的数据')
        return
      }
      this.exportDialogVisible = true
    },
    handleExport (isAll) {
      this.exportLoading = true
      let ids = [...this.selectedRows.values()].map(v => v.fid)
      if (isAll) {
        ids = []
      }
      this.$ajax({
        url: '/materials/export_propaganda_material',
        data: {
          ...this.submitForm,
          fids: ids,
          type: isAll
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
  //.search {
  //  height: 96px;
  //}

  .search >>>.el-form-item--mini {
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .content {
    //height: calc(100% - 96px);

    .buttonGroup {
      height: 40px;
      line-height: 40px;
    }

    .table {
      //height: calc(100% - 40px);
    }

    //.table >>>.el-table__header .el-checkbox {
    //  display: none;
    //}
  }
</style>
