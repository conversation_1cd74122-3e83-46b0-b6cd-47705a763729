<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      title="已有报告"
      width="70%"
      @open="handleOpen"
    >
      <el-table
        ref="table"
        :cell-style="handleRowStyle"
        :data="tableData"
        class="table"
        border
        style="width: 100%"
        height="50vh">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="type" label="报告类型" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="number" label="样本数量" min-width="40" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reportStatus" label="报告状态" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="auditTime" label="审核时间" min-width="100" show-overflow-tooltip></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '@/util/mixins'
import util from '@/util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    subOrderId: {
      type: Number
    }
  },
  data () {
    return {
      tableData: [],
      statusOptions: {
        '': {
          text: '未生成',
          class: 'not-generate'
        },
        1: {
          text: '审核通过',
          class: ''
        },
        10: {
          text: '已发送',
          class: ''
        },
        2: {
          text: '生成失败',
          class: 'fail-generate'
        },
        20: {
          text: '被驳回',
          class: 'fail-generate'
        },
        3: {
          text: '未生成',
          class: 'not-generate'
        },
        30: {
          text: '生成中',
          class: 'generate'
        },
        31: {
          text: '待审核',
          class: ''
        }
      },
      templates: {
        1: 'cfDNA 质控报告',
        2: 'GMseq cfDNA质控报告',
        3: 'GMseq FFPE DNA质控报告',
        4: 'GMseq 组织和细胞gDNA质控报告',
        5: 'FFPE DNA质控报告',
        6: '基因组DNA 质控报告（PCR-free 或 2项目）',
        7: '基因组DNA 质控报告',
        8: 'PCR产物 质控报告',
        9: 'lncRNA质控报告',
        10: 'mRNA质控报告',
        11: '宏转录组（粪便）样本RNA质控报告',
        12: '原核生物RNA质控报告',
        13: '文库报告',
        14: 'GMseq（低起始量）gDNA质控报告',
        15: 'lncRNA质控报告-新鲜组织细胞RNA',
        17: '细胞质控报告',
        18: '细胞核质控报告',
        19: '单细胞项目RNA质控报告',
        20: 'Gene+-SOP-SEQ-022-17 BS甲基化 cfDNA质控报告',
        21: 'Gene+-SOP-SEQ-022-18 GM甲基化 cfDNA质控报告',
        22: 'Gene+-SOP-SEQ-022-19 GM-panel甲基化 cfDNA质控报告'
      }
    }
  },
  methods: {
    handleOpen () {
      this.getData()
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row, rowIndex}) {
      return {padding: 0, height: '24px'}
    },
    // 获取表格数据
    getData () {
      this.$ajax({
        url: '/order/report/get_qc_report_order_statistic_list',
        data: {
          fsubOrderId: this.subOrderId
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data || []
          data.forEach((v) => {
            let reportStatus = this.statusOptions[v.fsubOrderReportStatus] || {
              text: '',
              class: ''
            }
            let item = {
              type: this.templates[v.ftype],
              number: v.fnumber,
              reportStatus: reportStatus.text,
              auditTime: v.fauditTime
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
