<template>
    <el-drawer
      :visible.sync="visible"
      :modal="false"
      :title="title"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      :show-close="true"
      size="100%"
      @open="handleOpen"
    >
      <div style="padding: 10px;">
        <h2 style="margin-bottom: 10px;">拉丁文名: {{ name }}</h2>
        <el-table
          ref="table"
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column label="FC编号" prop="fcNum" min-width="100"></el-table-column>
          <el-table-column label="样本编号" prop="fcaseSampleId" min-width="100"></el-table-column>
          <el-table-column label="样本类型" prop="fsampleType" min-width="100"></el-table-column>
          <el-table-column label="种reads数" prop="ftargetReads" min-width="100"></el-table-column>
          <el-table-column label="种RPMCR" prop="ftargetRpmcr" min-width="100"></el-table-column>
        </el-table>
      </div>

    </el-drawer>
</template>

<script>
import mixins from '../../../../../../util/mixins'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    taxId: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    }
  },
  watch: {
    'taxId': function (newVal) {
      this.getData()
    }
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId') || this.$route.query.oxym
    }
  },
  data () {
    return {
      title: '',
      tableData: []
    }
  },
  methods: {
    async getData () {
      let {code, data} = await this.$ajax({
        url: '/read/tngs/pathogen/findSameFcPathogenList',
        data: {
          taxId: this.taxId,
          analysisRsId: this.analysisRsId
        }
      })
      if (code === this.SUCCESS_CODE) {
        this.tableData = []
        let rows = data || []
        rows.forEach(v => {
          this.tableData.push({
            fcNum: v.fcNum,
            fcaseSampleId: v.dnaNum,
            fsampleType: v.sampleType,
            ftargetReads: v.targetReads,
            ftargetRpmcr: v.targetRpmcr
          })
        })
      }
    }
  }
}
</script>

<style scoped></style>
