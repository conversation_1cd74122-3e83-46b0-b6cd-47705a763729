<template>
  <div>
    <el-table
      ref="rightTable"
      :data="tableData"
      style="width: 100%;"
      :height="tbHeight"
      class="rightTable"
      border>
      <el-table-column type="index" label="序号" width="55" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="样本签收状态" prop="status">
        <template #default="{row}">
          <span :style="{color: row.statusColor}">{{ row.statusText }}</span>
        </template>
      </el-table-column>
      <el-table-column min-width="120" label="原始样本编号" prop="oldSampleName" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="吉因加编号" prop="geneCode">
        <template #default="{row}">
          <div v-if="currentEditId === row.id" style="display: flex; align-items: center">
            <el-input v-model.trim="currentEditCode" size="mini" style="width: 150px"></el-input>
            <el-button type="text" :loading="row.editLoading" icon="el-icon-check" @click="handleConfirmEdit(row)"></el-button>
            <el-button type="text" :loading="row.editLoading" icon="el-icon-close" style="color: #F56C6C;" @click="handleClearEdit"></el-button>
          </div>
          <span v-else class="code-btn" @click="handleEditCode(row)">
            {{ row.geneCode }}
            <i class="el-icon-edit"></i>
          </span>
        </template>
      </el-table-column>
      <el-table-column min-width="180" label="样本类型" prop="sampleType" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="产品名称" prop="productName" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="交付周期" prop="deliveryCycle" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="项目编码" prop="projectCode" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="项目名称" prop="projectName" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="120" label="创建人" prop="creator" show-overflow-tooltip></el-table-column>
      <el-table-column min-width="180" label="创建时间" prop="creationTime" show-overflow-tooltip></el-table-column>
      <el-table-column width="150" label="操作" fixed="right">
        <template #default="{row}">
          <el-button v-if="row.file" type="text" :loading="row.getFileUrlLoading" @click.stop="handleView(row)">查看记录文件</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>

    <image-viewer
      :pvisible.sync="imgViewerData.visible"
      :img-list="imgViewerData.imgs"
      :current-index="0"/>
  </div>
</template>

<script>

// import xx form 'xxx'
import mixins from '@/util/mixins'
import ImageViewer from '../../../common/imageViewer.vue'
import util from '../../../../util/util'
// import util from '@/util/util'
export default {
  name: 'leftTable',
  mixins: [mixins.tablePaginationCommonData],
  components: {ImageViewer},
  props: {
    statusList: Array,
    searchParams: {
      type: Object,
      default: () => {
        return {
          fexpressOrOrderCode: '',
          foldSampleName: '',
          fgeneCode: ''
        }
      }
    }
  },
  mounted () {
    this.$_setTbHeight(74 + 65 + 21 + 42 + 32)
  },
  data () {
    return {
      statusObj: {
        0: { text: '未签收', color: '#909399' },
        1: { text: '已签收', color: '#67C23A' }
      },
      imgViewerData: {
        visible: false,
        currentIndex: 0,
        imgs: []
      },
      pageSizes: [100, 200, 500],
      pageSize: 100,
      currentEditId: null,
      currentEditCode: ''
    }
  },
  methods: {
    search () {
      this.currentPage = 1
      this.getData()
    },
    getData () {
      this.$ajax({
        url: '/experiment/sign/get_sign_sample_list',
        data: {
          fstatusList: this.statusList,
          ...this.searchParams,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          }
        },
        loadingDom: '.rightTable'
      }).then(res => {
        if (res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.records || []
          this.selectedRows = {}
          this.tableData = []
          rows.forEach(v => {
            const statusItem = this.statusObj[v.fstatus] || {}
            let item = {
              id: v.fid,
              status: v.fstatus,
              statusText: statusItem.text,
              statusColor: statusItem.color,
              oldSampleName: v.foldSampleName,
              geneCode: v.fgeneCode,
              file: v.ffile,
              sampleType: v.ftissueOrNucleateSampleType,
              productName: v.fproductName,
              deliveryCycle: v.fdeliveryCycle,
              projectCode: v.fprojectCode,
              projectName: v.fprojectName,
              creator: v.forderCreator,
              creationTime: v.forderCreateTime
            }
            item.realData = JSON.parse(JSON.stringify(item))
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    async handleView (row) {
      let file
      if (row.newFileUrl) {
        file = row.newFileUrl
      } else {
        file = await this.getLastedImgUrl(row)
      }
      this.imgViewerData = {
        visible: true,
        imgs: [{ url: file }],
        currentIndex: 0
      }
    },
    getLastedImgUrl (row) {
      return new Promise(resolve => {
        this.$set(row, 'getFileUrlLoading', true)
        this.$ajax({
          url: '/experiment/common/get_file_url',
          data: {
            fflie: row.file
          }
        }).then(res => {
          if (res.code === this.SUCCESS_CODE) {
            const url = res.data || ''
            this.$set(row, 'newFileUrl', url)
            resolve(url)
          }
        }).finally(() => {
          this.$set(row, 'getFileUrlLoading', false)
        })
      })
    },
    // 清空所有数据
    resetTable () {
      this.currentPage = 1
      this.tableData = []
    },
    // 编辑
    handleEditCode (row) {
      this.currentEditId = row.id
      this.currentEditCode = row.realData.geneCode || ''
    },
    // 取消编辑
    handleClearEdit () {
      this.currentEditId = null
      this.currentEditCode = ''
    },
    handleConfirmEdit (row) {
      if (!this.currentEditCode) {
        this.$message.error('吉因加编码不能为空')
        return
      }
      const code = this.currentEditCode
      const params = {
        signSampleBeanList: [
          { fid: this.currentEditId, fgeneCode: code }
        ]
      }
      this.$set(row, 'editLoading', true)
      this.$ajax({
        url: '/experiment/sign/update_sign_sample',
        data: params
      }).then(res => {
        if (res.success) {
          this.$message.success('编辑成功')
          this.$set(row, 'geneCode', code)
          this.handleClearEdit()
        }
      }).finally(() => {
        this.$set(row, 'editLoading', false)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.code-btn{
  color: $color;
  cursor: pointer;
}
</style>
