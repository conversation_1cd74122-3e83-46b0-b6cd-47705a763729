<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="编辑"
      width="30%">
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="mini" label-suffix="：">
          <el-form-item label="基因">
            {{form.gene}}
          </el-form-item>
          <el-form-item label="解读结果" prop="result">
            <el-input v-model="form.result" :autosize="{minRows: 5}" placeholder="请输入" type="textarea"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'wesReportUnscrambleResultGeneticResultEditDialog',
  components: {},
  props: ['pvisible', 'pdata'],
  mounted () {
  },
  watch: {
    pvisible (newVal) {
      this.visible = newVal
      if (newVal) {
        this.form = Object.assign({}, this.form, this.pdata)
      } else {
        this.form = {
          fid: '',
          gene: '',
          result: ''
        }
      }
    }
  },
  computed: {},
  data () {
    return {
      loading: false,
      visible: this.pvisible,
      form: {
        fid: '',
        gene: '',
        result: ''
      },
      rules: {
        result: [
          {required: true, message: '请输入解读结果', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleClose () {
      this.$emit('geneticResultEditDialogCloseEvent')
      this.$refs.form.resetFields()
    },
    handleConfirm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          this.$ajax({
            url: '/read/wesUnscramble/edit_genetic_result_read',
            data: {
              fid: this.form.fid,
              fresultRead: this.form.result
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$emit('geneticResultEditDialogConfirmEvent')
              this.$refs.form.resetFields()
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
