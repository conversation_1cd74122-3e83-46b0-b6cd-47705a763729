import { myAjax } from '@/util/ajax'

/**
 * 分页获取样本癌种类型配置列表
 * @param {Object} params 查询参数
 * @param {Object} pageInfo 分页信息
 * @returns {Promise}
 */
export function getSampleCancerTypeList (data, options) {
  return myAjax({
    loadingDom: '.table',
    url: '/system/sample_type_config/get_sample_type_config_page',
    data: data,
    ...options
  })
}

/**
 * 获取样本癌种类型配置详情
 * @param {String|Number} id 配置ID
 * @returns {Promise}
 */
export function getSampleCancerTypeDetail (data) {
  return myAjax({
    url: '/system/sample_type_config/get_sample_type_config_detail',
    data: data
  })
}

/**
 * 根据芯片类型获取癌种分类
 * @param {*} data
 * @returns
 */
export function getCancerCategory (data) {
  return myAjax({
    url: '/system/s_chip_cancer_config/get_cancer_type_by_chip',
    data: data
  })
}

/**
 * 更新样本癌种类型配置
 * @param {Object} data 配置数据
 * @returns {Promise}
 */
export function updateSampleCancerType (data) {
  return myAjax({
    url: '/system/sample_type_config/save_or_update_sample_cancer_type_config',
    data
  })
}

/**
 * 删除样本癌种类型配置
 * @param {Array} ids 配置ID数组
 * @returns {Promise}
 */
export function deleteSampleCancerType (data) {
  return myAjax({
    url: '/system/sample_type_config/delete_sample_type_config',
    data: data
  })
}

/**
 * 获取癌种类型下拉选项（样本信息管理弹窗）
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getCancerTypeOptions (data) {
  return myAjax({
    url: '/system/api/get_sample_cancer_type',
    data: data
  })
}

/**
 * 获取产品名称下拉选项
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getProductNameOptions (data) {
  return myAjax({
    url: '/system/sample_type_config/get_product_name_options',
    data: data
  })
}

/**
 * 获取产品列表（用于产品选择弹窗）
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getProductList (data, options) {
  return myAjax({
    url: '/system/product/get_product_list',
    data: data,
    ...options
  })
}

// **************************************核心探针*******************************************************

/**
 * 获取核心探针配置列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getCoreProbeConfigList (data) {
  return myAjax({
    url: '/system/s_chip_cancer_config/get_s_chip_cancer_config_page',
    data: data
  })
}

/**
 * 新增核心探针配置
 * @param {Object} params 新增参数
 * @returns {Promise}
 */
export function editCoreProbeConfig (data) {
  return myAjax({
    url: '/system/s_chip_cancer_config/save_or_update_s_chip_cancer_config_config',
    data: data
  })
}

/**
 * 获取样本癌种类型选项（用于下拉选择）
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getSampleCancerTypeOptions (data) {
  return myAjax({
    url: '/system/sample_type_config/get_sample_cancer_type_options',
    data: data
  })
}

export function getCoreProbeList (data) {
  return myAjax({
    url: '/system/api/get_core_probe_name_list',
    data: data
  })
}
