<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="修改存储位置"
      width="500px"
      @open="handleOpen">
      <el-form ref="form" :model="form" size="mini" :rules="rules" label-position="top" label-suffix=":" label-width="240" inline style="display: flex;">
        <el-form-item label="样本编号" style="width: 160px">
          {{sampleCode}}
        </el-form-item>
        <el-form-item label="存放位置" prop="position" style="width: calc(100% - 160px)">
          <el-input v-model.trim="form.position" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="loading" size="mini" type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import {awaitWrap} from '../../../util/util'
import {fixPosition} from '../../../api/sampleLibraryManagement/sampleSearch'

export default {
  name: 'fixPositionDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    sampleCode: {
      type: String,
      default: ''
    },
    position: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      form: {
        position: ''
      },
      rules: {
        position: [
          {
            required: true,
            message: '请输入存放位置',
            trigger: 'blur'
          },
          {
            pattern: /^(SZ|BJ|SH)([ABCDEFGHI])[0-9]{1,2}-[0-9]{1,2}L-[0-9]{1,2}S-[0-9]{1,2}B-([ABCDEFGHIJKLMNOPQRST])[0-9]{1,2}$/,
            message: '请输入正确存放位置。示例：SZA4-2L-2S-3B-C03',
            trigger: ['blur', 'change']
          }
        ]
      },
      loading: false
    }
  },
  methods: {
    handleOpen () {
      this.form.position = this.position
    },
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      const { res } = await awaitWrap(fixPosition({
        fsampleNumber: this.sampleCode,
        fsamplePlace: this.form.position
      }))
      if (res.code === this.SUCCESS_CODE) {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 5000
        })
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
      this.loading = false
    }
  }
}
</script>

<style scoped>

</style>
