<template>
  <div>
    <!--操作按钮区-->
    <div class="flex search-form buttonGroup">
      <!--操作按钮区-->
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('017002001', 'buttons')" size="mini" type="primary" @click="handleAudit">批量审核</el-button>
        <el-button v-if="$setAuthority('017002002', 'buttons')" size="mini" type="primary" @click="handleSendReport">报告发放</el-button>
        <el-button v-if="$setAuthority('017002003', 'buttons')" :loading="exportLoading"  size="mini" type="primary" plain @click="handleDownLoadReport">报告下载</el-button>
      </div>
      <!--查询区-->
      <div>
        <div class="flex">
          <el-select v-model.trim="searchType" style="width: 144px!important" size="mini" clearable placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in optionsList">
            </el-option>
          </el-select>
          <el-input
            v-model.trim="searchValue"
            :disabled="!searchType"
            v-if="showInput"
            size="mini"
            placeholder="请输入"
            clearable
            class="input-width"
            @keyup.enter.native="handleSearch()"></el-input>
          <el-select
            v-model="searchValue"
            v-if="showSelect"
            filterable
            clearable
            collapse-tags
            class="input-width"
            size="mini"
          >
            <el-option
              :key="k"
              :label="k"
              :value="v * 1"
              v-for="(k, v) in templates"></el-option>
          </el-select>
          <el-date-picker
            v-if="showDatePicker"
            v-model="searchValue"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            clearable
            size="mini"
            prefix-icon="el-icon-date"
            range-separator="~"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="input-width">
          </el-date-picker>
          <div class="btn">
            <el-button size="mini" type="primary" @click="handleSearch()">查询</el-button>
            <el-button size="mini" type="primary" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleAdvancedReport">高级查询</el-button>
          </div>
        </div>
      </div>
    </div>

    <!--表格-->
    <div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="{padding: 0, height: '24px'}"
        class="table"
        border
        size="mini"
        style="width: 100%"
        height="calc(100vh - 74px - 40px - 56px - 32px)"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="reportStatusText" label="报告状态" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="scope.row.reportStatusClass">{{scope.row.reportStatusText}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="typeText" label="报告类型" min-width="280" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleNum" label="样本数量" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="生成时间" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="creator" label="操作人" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="auditTime" label="审核时间" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="auditor" label="审核人" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderCode" label="订单编号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" label="操作" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="flex">
              <span v-if="scope.row.reportStatus === 20 && $setAuthority('017002004', 'buttons')" style="margin: 5px"
                    class="link" @click="handleShowReportDetail(1, scope.row)">查看</span>
              <span v-if="scope.row.reportStatus !== 20 && $setAuthority('017002005', 'buttons')" style="margin: 5px"
                    class="link" @click="handlePreview(scope.row.id)">预览</span>
              <span v-if="scope.row.reportStatus === 31 && $setAuthority('017002006', 'buttons')" style="margin: 5px"
                    class="link" @click="handleShowReportDetail(2, scope.row)">审核</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>

    <!--查询抽屉-->
    <search-dialog
      ref="searchDialog"
      :pvisible.sync="searchVisible"
      @dialogConfirmEvent="handleSearch"/>
    <!--报告审核-->
    <audit-dialog
      :pvisible.sync="auditVisible"
      :ids="ids"
      :order-code="orderCode"
      :report-name="reportName"
      :project-infos="projectinfos"
      @dialogConfirmEvent="getData"
    ></audit-dialog>
    <!--报告发送-->
    <send-report-dialog
      :pvisible.sync="sendReportVisible"
      :ids="ids"
      :type="2"
      :report-name="reportName"
      :order-code="orderCode"
      :project-code="projectCode"
      :project-name="projectName"
      @dialogConfirmEvent="getData"
    />
    <!--报告详情弹窗-->
    <qc-report-detail-dialog
      :pvisible.sync="qcReportDetailVisible"
      :qc-report-type="qcReportType"
      :sub-order-id="subOrderId"
      :type="type"
      :order-code="orderCode"
      @dialogConfirmEvent="getData"
      @dialogCloseEvent="getData"
    />
  </div>
</template>

<script>

import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import searchDialog from './components/searchDialog' // 搜索
import AuditDialog from './components/auditDialog' // 审核弹窗
import SendReportDialog from './components/sendReportDialog' // 发送报告
import QcReportDetailDialog from './components/qcReportDetailDialog' // 报告详情弹窗

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    AuditDialog,
    searchDialog,
    SendReportDialog,
    QcReportDetailDialog
  },
  watch: {
    searchType (newValue) {
      this.searchValue = ''
      if (!newValue) {
        this.handleSearch()
      }
    }
  },
  computed: {
    showInput () {
      return ['', 'fprjectCode', 'forderCode', 'fcreator', 'fauditor'].includes(this.searchType)
    },
    showSelect () {
      return ['ftype'].includes(this.searchType)
    },
    showDatePicker () {
      return ['fcreateTime', 'fauditTime'].includes(this.searchType)
    }
  },
  data () {
    return {
      orderCode: '',
      selectedRows: new Map(),
      searchVisible: false,
      auditVisible: false,
      reportsVisible: false,
      sendReportVisible: false,
      qcReportDetailVisible: false,
      exportLoading: false,
      type: '',
      qcReportType: 1, // 报告详情弹窗类型
      reportName: null,
      subOrderId: 0,
      projectinfos: [],
      projectCode: '',
      projectName: '',
      searchType: '',
      searchValue: '',
      advanceForm: {},
      submitForm: {},
      templates: {
        1: 'cfDNA 质控报告',
        2: 'GMseq cfDNA质控报告',
        3: 'GMseq FFPE DNA质控报告',
        4: 'GMseq 组织和细胞gDNA质控报告',
        5: 'FFPE DNA质控报告',
        6: '基因组DNA 质控报告（PCR-free 或 2项目）',
        7: '基因组DNA 质控报告',
        8: 'PCR产物 质控报告',
        9: 'lncRNA质控报告',
        10: 'mRNA质控报告',
        11: '宏转录组（粪便）样本RNA质控报告',
        12: '原核生物RNA质控报告',
        13: '文库报告',
        14: 'GMseq（低起始量）gDNA质控报告',
        15: 'lncRNA质控报告-新鲜组织细胞RNA',
        17: '细胞质控报告',
        18: '细胞核质控报告',
        19: '单细胞项目RNA质控报告',
        20: 'Gene+-SOP-SEQ-022-17 BS甲基化 cfDNA质控报告',
        21: 'Gene+-SOP-SEQ-022-18 GM甲基化 cfDNA质控报告',
        22: 'Gene+-SOP-SEQ-022-19 GM-panel甲基化 cfDNA质控报告'
      },
      ids: [], // 所选样本id
      optionsList: [
        {
          label: '项目编号',
          value: 'fprjectCode'
        }, {
          label: '订单编号',
          value: 'forderCode'
        }, {
          label: '审核人',
          value: 'fauditor'
        },
        {
          label: '操作人',
          value: 'fcreator'
        },
        {
          label: '报告类型',
          value: 'ftype'
        },
        {
          label: '生成时间',
          value: 'fcreateTime'
        },
        {
          label: '审核时间',
          value: 'fauditTime'
        }
      ],
      statusOptions: {
        '': {
          text: '未生成',
          class: 'not-generate'
        },
        1: {
          text: '审核通过',
          class: ''
        },
        10: {
          text: '已发送',
          class: ''
        },
        2: {
          text: '生成失败',
          class: 'fail-generate'
        },
        20: {
          text: '被驳回',
          class: 'fail-generate'
        },
        3: {
          text: '未生成',
          class: 'not-generate'
        },
        30: {
          text: '生成中',
          class: 'generate'
        },
        31: {
          text: '待审核',
          class: ''
        }
      },
      tableData: []
    }
  },
  methods: {
    // 报告审核 1:报告状态为“待审核”,
    handleAudit () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何记录')
        return
      }
      let rows = [...this.selectedRows.values()]
      if (rows.some(v => v.reportStatus !== 31)) {
        this.$message.error('仅允许选择状态为“待审核”的记录进行操作')
        return
      }
      let orderCode = rows[0].orderCode
      if (rows.some(v => v.orderCode !== orderCode)) {
        this.$message.error('所选报告不属于同一个订单，无法一起发放')
        return
      }
      if (rows.length === 1) this.reportName = rows[0].typeText
      this.ids = rows.map(v => v.id)
      this.projectinfos = rows.map(v => {
        return {
          projectCode: v.projectCode,
          projectName: v.projectName
        }
      })
      this.orderCode = rows[0].orderCode
      this.auditVisible = true
    },
    // 报告发放
    handleSendReport () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何记录')
        return
      }
      let rows = [...this.selectedRows.values()]
      if (rows.some(v => v.reportStatus !== 1)) {
        this.$message.error('仅允许选择状态为“审核通过”的记录进行操作')
        return
      }
      this.projectCode = rows[0].projectCode
      if (rows.some(v => v.projectCode !== this.projectCode)) {
        this.$message.error('所选报告不属于同一个项目，无法一起发放')
        return
      }
      let orderCode = rows[0].orderCode
      if (rows.some(v => v.orderCode !== orderCode)) {
        this.$message.error('所选报告不属于同一个订单，无法一起发放')
        return
      }
      this.reportName = rows[0].typeText
      this.ids = rows.map(v => v.id)
      this.projectCode = rows[0].projectCode
      this.orderCode = rows[0].orderCode
      this.projectName = rows[0].projectName
      this.sendReportVisible = true
    },
    // 报告下载
    handleDownLoadReport () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何记录')
        return
      }
      this.exportLoading = true
      let fidList = [...this.selectedRows.keys()]
      this.$ajax({
        url: '/order/report/download_qc_report',
        responseType: 'blob',
        data: {
          fidList: fidList
        }
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    handlePreview (id) {
      this.$ajax({
        url: 'order/report/get_pdf_report',
        data: {
          freportId: id
        },
        loadingDom: '.form'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          window.open(res.data)
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    /**
     * 显示报告详情
     * @param type 1: 查看 2: 审核
     * @param row 列数据
     */
    handleShowReportDetail (type, row = {}) {
      if (type === 2 && row.reportStatus !== 31) {
        this.$message.error('请选择待审核的样本')
        return
      }
      this.subOrderId = row.id
      this.type = row.typeText
      this.qcReportType = type
      this.orderCode = row.orderCode
      this.qcReportDetailVisible = true
    },
    // 查询
    handleSearch (data) {
      if (data) {
        this.advanceForm = data
      } else {
        this.advanceForm = this.$refs.searchDialog.setParams()
      }
      this.submitForm = util.deepCopy(this.advanceForm)
      let createTime = []
      let auditTime = []
      this.submitForm.searchKey = this.searchType
      this.submitForm.searchValue = this.searchValue
      this.advanceForm.fcreateTime = this.advanceForm.fcreateTime || []
      this.advanceForm.fauditTime = this.advanceForm.fauditTime || []

      if (this.searchType === 'fcreateTime') {
        createTime = this.searchValue || []
        this.submitForm.searchKey = ''
        this.submitForm.searchValue = ''
      }

      if (this.searchType === 'fauditTime') {
        auditTime = this.searchValue || []
        this.submitForm.searchKey = ''
        this.submitForm.searchValue = ''
      }

      if (this.advanceForm.fcreateTime.length > 0) {
        createTime = this.advanceForm.fcreateTime || []
        auditTime = this.advanceForm.fauditTime || []
      }

      if (this.advanceForm.fauditTime.length > 0) {
        createTime = this.advanceForm.fcreateTime || []
        auditTime = this.advanceForm.fauditTime || []
      }

      this.submitForm.fcreateStartTime = createTime[0]
      this.submitForm.fcreateEndTime = createTime[1]
      this.submitForm.fauditStartTime = auditTime[0]
      this.submitForm.fauditEndTime = auditTime[1]

      this.currentPage = 1
      this.clearMap()
      this.getData()
    },
    // 重置分别重置高级和简易查询
    handleReset () {
      this.searchType = ''
      this.searchValue = ''
      this.handleSearch()
    },
    // 高级查询
    handleAdvancedReport () {
      this.searchVisible = true
    },
    // 获取表格数据
    getData () {
      let data = util.getSessionInfo('currentLab') || []
      let options = {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      }
      data = data.map(v => {
        return options[v]
      })
      this.$ajax({
        url: '/order/report/get_qc_report_list',
        data: {
          areaList: data,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          ...util.deepCopy(this.submitForm)
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.rows || []
          data.forEach((v) => {
            let reportStatus = this.statusOptions[v.fstatus] || {
              text: '',
              class: ''
            }
            let item = {
              id: v.fid,
              reportStatus: v.fstatus,
              reportStatusText: reportStatus.text,
              reportStatusClass: reportStatus.class,
              type: v.ftype,
              typeText: this.templates[v.ftype],
              sampleNum: v.fsampleNum,
              projectCode: v.fprojectCode,
              projectName: v.fprojectName,
              createTime: v.fcreateTime,
              creator: v.fcreator,
              auditTime: v.fauditTime,
              auditor: v.fauditor,
              orderCode: v.forderCode
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.handleEchoSelect()
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.buttonGroup{
  margin: 10px 0;
  height: 40px;
  line-height: 40px;
}
.btn {
  margin-left: 10px;
}

.input-width{
  width: 400px;
}
.not-generate {
  color: $color
}
.generate {
  color: $success-color
}
.fail-generate {
  color: $fail-color
}
</style>
