<template>
  <div class="wrapper">
    <test-result-list :table-data="result.bacteria" type="bacteria" title="细菌检测结果"/>
    <test-result-list :table-data="result.fungus" type="fungus" title="真菌检测结果"/>
    <test-result-list :table-data="result.dna" type="dna" title="DNA病毒检测结果"/>
    <test-result-list :table-data="result.rna" type="bacteria" title="RNA病毒检测结果"/>
    <test-result-list :table-data="result.specialPathogenDetection" type="specialPathogenDetection" title="特殊病原检测结果"/>
    <test-result-list :table-data="result.suspectedHumanMicrobiota" type="suspectedHumanMicrobiota" title="疑似人体微生态菌群检测结果"/>
    <drug-resistance-gene-result :table-data="result.drugResistanceGene" title="耐药基因检测结果"/>
    <drug-resistance-gene-mutation-result :table-data="result.drugResistanceGeneMutation" title="耐药基因突变检测结果"/>
    <virulence-gene-result-result :table-data="result.virulenceGeneResult" title="毒力基因结果"/>
  </div>
</template>

<script>
import testResultList from './componemts/testResultList'
import util from '../../../../../util/util'
import DrugResistanceGeneResult from './componemts/drugResistanceGeneResult.vue'
import DrugResistanceGeneMutationResult from './componemts/drugResistanceGeneMutationResult.vue'
import VirulenceGeneResultResult from './componemts/virulenceGeneResultResult.vue'
export default {
  components: {
    VirulenceGeneResultResult,
    DrugResistanceGeneMutationResult,
    DrugResistanceGeneResult,
    testResultList
  },
  mounted () {
    this.getData()
  },
  computed: {
    analysisRsId () {
      return this.$store.getters.getValue('analysisRsId')
    }
  },
  data () {
    return {
      result: {}
    }
  },
  methods: {
    async getData () {
      let {code, data = {}} = await this.$ajax({
        url: '/read/tngs/pathogen/get_test_results',
        data: {
          analysisRsId: this.analysisRsId
        },
        method: 'get',
        loadingDom: '.wrapper'
      })
      if (code === this.SUCCESS_CODE) {
        this.result = {
          bacteria: this.formateTableData(data.fbacteria),
          fungus: this.formateTableData(data.ffungus),
          dna: this.formateTableData(data.fdna),
          rna: this.formateTableData(data.frna),
          specialPathogenDetection: this.formateTableData(data.fspecialPathogenDetection),
          suspectedHumanMicrobiota: this.formateTableData(data.fsuspectedHumanMicrobiota)
        }
        data.fdrugResistanceGene && this.$set(this.result, 'drugResistanceGene', this.drugResistanceGeneData(data.fdrugResistanceGene))
        data.fdrugResistanceGeneMutation && this.$set(this.result, 'drugResistanceGeneMutation', this.drugResistanceGeneMutationData(data.fdrugResistanceGeneMutation))
        data.fvirulenceGeneResult && this.$set(this.result, 'virulenceGeneResult', this.virulenceGeneResultData(data.fvirulenceGeneResult))
      }
    },
    formateTableData (data) {
      if (!Array.isArray(data)) {
        return
      }
      let tableData = []
      data.forEach(v => {
        let item = {
          genusName: v.fgenusChinese,
          genusLatin: v.fgenusLatin,
          speciesName: v.fspeciesChinese,
          speciesLatin: v.fspeciesLatin,
          targetReads: v.ftargetReads,
          targetRPMCR: v.f1xCoverage,
          abundanceRe: v.fabundanceRe,
          concentration: v.fconcentration,

          report: v.freport,
          speciesReadsNumber: v.fspeciesReadsNumber,
          coverage: this.formateNum(v.fcoverage),
          speciesRelativeAbundance: v.fspeciesRelativeAbundance && this.formateNum(v.fspeciesRelativeAbundance.split('%')[0])
        }
        item.realData = JSON.parse(JSON.stringify(item))
        util.setDefaultEmptyValueForObject(item)
        tableData.push(item)
      })
      return tableData
    },
    drugResistanceGeneData (data) {
      if (!Array.isArray(data)) {
        return
      }
      let drugResistanceGeneTableData = []
      data.forEach(v => {
        let item = {
          geneList: v.fgeneList,
          reads: v.freads,
          miniResistantDrug: v.fminiResistantDrug,
          machineProcessed: v.fbigResistantDrug,
          correspondingPathogen: v.fcorrespondingPathogen,
          relevanceSpeciesDrug: v.frelevanceSpeciesDrug,
          relevanceClinic: v.frelevanceClinic
        }
        item.realData = JSON.parse(JSON.stringify(item))
        util.setDefaultEmptyValueForObject(item)
        drugResistanceGeneTableData.push(item)
      })
      return drugResistanceGeneTableData
    },
    drugResistanceGeneMutationData (data) {
      if (!Array.isArray(data)) {
        return
      }
      let drugResistanceGeneMutationTableData = []
      data.forEach(v => {
        let item = {
          geneList: v.fgeneList,
          phgvs: v.fphgvs,
          reads: v.freads,
          miniResistantDrug: v.fminiResistantDrug,
          bigResistantDrug: v.fbigResistantDrug,
          correspondingPathogen: v.fcorrespondingPathogen
        }
        item.realData = JSON.parse(JSON.stringify(item))
        util.setDefaultEmptyValueForObject(item)
        drugResistanceGeneMutationTableData.push(item)
      })
      return drugResistanceGeneMutationTableData
    },
    virulenceGeneResultData (data) {
      if (!Array.isArray(data)) {
        return
      }
      let virulenceGeneTableData = []
      data.forEach(v => {
        let item = {
          geneList: v.fgeneList,
          reads: v.freads,
          organism: v.forganism,
          mechanismToxicity: v.fmechanismToxicity
        }
        item.realData = JSON.parse(JSON.stringify(item))
        util.setDefaultEmptyValueForObject(item)
        virulenceGeneTableData.push(item)
      })
      return virulenceGeneTableData
    },
    formateNum (number) {
      if (number < 0.01) {
        return '＜0.01%'
      }
      return Math.ceil(number * 100) / 100 + '%'
    }
  }
}
</script>

<style scoped></style>
